# Caching Implementation for Admin APIs

## Overview
This document explains how caching has been implemented for multiple admin API endpoints using Redis cache:
- `/society/admin/address/list` - Address data
- `/society/admin/constitutionTypeWithClauseContent/list` - Constitution types with clause contents

## What Was Implemented

### 1. Cache Configuration
- **Redis Cache**: Already configured in `CacheConfig.java` and `LocalCacheConfig.java`
- **TTL**: 1 hour (3600 seconds)
- **Cache Manager**: RedisCacheManager with JSON serialization
- **Profiles**: 
  - `local`: Uses localhost Redis
  - `!local`: Uses AWS ElastiCache with SSL

### 2. Cache Names Constants
Created `CacheNames.java` to centralize cache name definitions:
```java
public static final String ADDRESSES = "addresses";
public static final String CONSTITUTION_TYPES = "constitution_types";
public static final String CLAUSE_CONTENTS = "clause_contents";
public static final String CONSTITUTION_CONTENTS = "constitution_contents";
```

### 3. Read Operations Caching

#### Address Operations - `AdmAddressesReadDomainService`

#### getAllAdmAddresses()
- **Cache Key**: `'all_addresses'`
- **Cache Name**: `addresses`
- **Behavior**: First call hits database, subsequent calls return cached data

#### findById(Long id)
- **Cache Key**: `'address_' + id`
- **Cache Name**: `addresses`
- **Behavior**: Each address ID is cached separately

#### getAllCountries(String nameQuery, Integer offset, Integer limit)
- **Cache Key**: `'countries_' + nameQuery + '_' + offset + '_' + limit`
- **Cache Name**: `addresses`
- **Behavior**: Different query parameters create different cache entries

#### Constitution Type Operations - `ConstitutionTypeReadDomainService`

##### getAllConstitutionType()
- **Cache Key**: `'all_constitution_types'`
- **Cache Name**: `constitution_types`
- **Behavior**: Caches all constitution types

##### findByName(String name)
- **Cache Key**: `'constitution_type_name_' + name`
- **Cache Name**: `constitution_types`
- **Behavior**: Each constitution type name is cached separately

#### Clause Content Operations - `ClauseContentReadDomainService`

##### getAllClauseContent()
- **Cache Key**: `'all_clause_contents'`
- **Cache Name**: `clause_contents`
- **Behavior**: Caches all clause contents

##### getAllByConstitutionTypeId(Long constitutionTypeId)
- **Cache Key**: `'clause_contents_type_' + constitutionTypeId`
- **Cache Name**: `clause_contents`
- **Behavior**: Caches clause contents for each constitution type

##### getByConsTypeAndClauseNo(Long constitutionType, Long clauseNo)
- **Cache Key**: `'clause_content_' + constitutionType + '_' + clauseNo`
- **Cache Name**: `clause_contents`
- **Behavior**: Caches specific clause content by type and number

#### Combined Operations - `ConstitutionTypeReadFacadeImpl`

##### getAllConstitutionTypeWithClauseContent()
- **Cache Key**: `'constitution_types_with_clause_contents'`
- **Cache Name**: `constitution_types`
- **Behavior**: Efficiently caches the complete result with populated clause contents
- **Performance**: Eliminates N+1 query problem by caching the combined result

### 4. Cache Eviction
#### Address Write Operations - `AdmAddressesWriteDomainService`
All write operations (`create`, `update`, `delete`) use:
- **Cache Name**: `addresses`
- **Strategy**: `allEntries = true` (clears entire cache)
- **Reason**: Ensures data consistency when addresses are modified

#### Constitution Content Write Operations - `ConstitutionContentWriteDomainService`
All write operations (`create`, `updateStatus`, `hardDelete`) use:
- **Cache Name**: `constitution_contents`
- **Strategy**: `allEntries = true` (clears entire cache)
- **Reason**: Ensures data consistency when constitution content is modified

#### Constitution Value Write Operations - `ConstitutionValueWriteDomainService`
All write operations (`create`, `update`, `updateStatus`, `hardDelete`) use:
- **Cache Name**: `constitution_contents`
- **Strategy**: `allEntries = true` (clears entire cache)
- **Reason**: Constitution values are related to constitution content, so cache is cleared for consistency

## How It Works

### First Request Flow
1. Client calls `GET /society/admin/address/list`
2. Controller → Facade → Service → `getAllAdmAddresses()`
3. Cache miss occurs (no data in Redis)
4. Database query executes
5. Result is stored in Redis with key `addresses::all_addresses`
6. Data returned to client

### Subsequent Requests Flow
1. Client calls `GET /society/admin/address/list`
2. Controller → Facade → Service → `getAllAdmAddresses()`
3. Cache hit occurs (data found in Redis)
4. **No database query** - data returned from cache
5. Faster response time

### Cache Invalidation Flow
1. Admin creates/updates/deletes an address
2. Write operation executes
3. `@CacheEvict` triggers and clears all `addresses` cache entries
4. Next read request will be a cache miss and refresh the cache

## Benefits

1. **Performance**: Significantly faster response times for repeated requests
2. **Database Load**: Reduces database queries for frequently accessed data
3. **Scalability**: Better handling of concurrent requests
4. **Automatic Management**: Spring handles cache operations transparently

## Monitoring Cache Performance

### Log Messages
The implementation includes log messages to track cache behavior:
- `"Fetching all addresses from database (cache miss)"` - Database hit
- `"Creating country - evicting addresses cache"` - Cache eviction

### Redis Monitoring
You can monitor cache usage through:
- Redis CLI: `redis-cli monitor`
- Application logs
- Redis metrics (if monitoring is configured)

## Testing the Implementation

### Test Cache Hit/Miss
1. Call the API endpoint twice in succession
2. First call should show database query in logs
3. Second call should be faster with no database query

### Test Cache Eviction
1. Call the API endpoint (cache populated)
2. Create/update an address via write API
3. Call the API endpoint again (cache miss, database query)

## Configuration Details

### Cache TTL
Current TTL is 1 hour. To modify:
```java
// In CacheConfig.java
.entryTtl(Duration.ofHours(1))  // Change this value
```

### Cache Keys
Cache keys are designed to be unique and descriptive:
- `all_addresses` - All addresses list
- `address_123` - Specific address with ID 123
- `countries_malaysia_0_10` - Countries query with specific parameters

## Future Enhancements

1. **Conditional Caching**: Add conditions to cache only when data exists
2. **Cache Warming**: Pre-populate cache on application startup
3. **Metrics**: Add cache hit/miss metrics
4. **Selective Eviction**: More granular cache eviction instead of clearing all entries
5. **Cache Partitioning**: Separate caches for countries, states, districts

## Troubleshooting

### Cache Not Working
1. Check Redis connection
2. Verify `@EnableCaching` is present
3. Ensure methods are called through Spring proxy (not direct calls)
4. Check cache configuration profiles

### Performance Issues
1. Monitor Redis memory usage
2. Check cache hit ratio
3. Consider adjusting TTL values
4. Review cache key strategies
