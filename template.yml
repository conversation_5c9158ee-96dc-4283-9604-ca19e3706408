AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: AWS Serverless Spring Boot 3 API - com.eroses::eroses-society
Globals:
  Api:
    EndpointConfiguration: REGIONAL

#Parameters:
#  Alias:
#    Type: String
#    Default: dev

Resources:
  ErosesSocietyFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: com.eroses.StreamLambdaHandler::handleRequest
      Runtime: java21
      CodeUri: .
      MemorySize: 2048
      Policies: AWSLambdaBasicExecutionRole
      Timeout: 30
#      SnapStart:
#        ApplyOn: "PublishedVersions"
#      AutoPublishAlias: !Ref Alias
      AutoPublishAlias: live
      ProvisionedConcurrencyConfig:
        ProvisionedConcurrentExecutions: 2
      DeploymentPreference:
        Type: AllAtOnce
      VpcConfig:
        SecurityGroupIds:
          - !If
            - IsDev
            - sg-01461118a00f07e16
            - !If
              - IsStaging
              - sg-0d2f1f703b86a6573
              - sg-009a7878bd5230050
        SubnetIds:
          - !If
            - IsDev
            - subnet-0a191db87e5f00513
            - !If
              - IsStaging
              - subnet-0b4950345a8d35498
              - subnet-0977d5be590cf08bb
      Events:
        ProxyResource:
          Type: Api
          Properties:
            Path: /{proxy+}
            Method: any

Outputs:
  ErosesSocietyApi:
    Description: URL for application
    Value: !Sub 'https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/ping'
    Export:
      Name: ErosesSocietyApi

Conditions:
  IsDev: !Equals [!Ref "AWS::AccountId", "************"]
  IsStaging: !Equals [!Ref "AWS::AccountId", "************"]
  IsProd: !Equals [!Ref "AWS::AccountId", "************"]
