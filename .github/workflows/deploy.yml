name: Deploy to AWS Lambda with SAM

on:
  push:
    branches:
      - development
      - staging
      - production

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up JDK 21
        uses: actions/setup-java@v2
        with:
          distribution: 'temurin'
          java-version: '21'

      - name: Configure <PERSON>ven for GitHub Packages
        run: |
          echo "<settings>
                  <servers>
                    <server>
                      <id>github</id>
                      <username>${{ secrets.JAR_REPO_ACCESS_ACTOR }}</username>
                      <password>${{ secrets.JAR_REPO_ACCESS_TOKEN }}</password>
                    </server>
                  </servers>
                </settings>" > ~/.m2/settings.xml

      - name: Install AWS SAM CLI
        run: |
          pip install aws-sam-cli

      - name: Set Dev AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.DEV_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEV_AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-southeast-5
        if: github.ref == 'refs/heads/development'

      - name: Set Staging AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.STAGING_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.STAGING_AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-southeast-5
        if: github.ref == 'refs/heads/staging'

      - name: Set Production AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-southeast-5
        if: github.ref == 'refs/heads/production'

      - name: Build SAM application
        run: sam build

      - name: Deploy to AWS
        env:
          SPRING_PROFILES_ACTIVE: ${{ github.ref == 'refs/heads/development' && 'dev' || github.ref == 'refs/heads/staging' && 'staging' || 'prod' }}
        run: |
          # Determine the environment and stack name based on branch name
          if [[ "${{ github.ref }}" == "refs/heads/development" ]]; then
            ENVIRONMENT="dev"
            STACK_NAME="eroses-society-dev"
            S3_BUCKET="eroses-society-microservice"
          elif [[ "${{ github.ref }}" == "refs/heads/staging" ]]; then
            ENVIRONMENT="staging"
            STACK_NAME="eroses-society-staging"
            S3_BUCKET="eroses-society-microservice-staging"
          elif [[ "${{ github.ref }}" == "refs/heads/production" ]]; then
            ENVIRONMENT="prod"
            STACK_NAME="eroses-society-prod"
            S3_BUCKET="eroses-society-microservice-prod"
          fi
          
          # Spring profile to match the environment
          # SPRING_PROFILES_ACTIVE=$ENVIRONMENT
          
          # Deploy the application using SAM
          sam deploy \
            --stack-name $STACK_NAME \
            --s3-bucket $S3_BUCKET \
            --capabilities CAPABILITY_IAM \
            --region ap-southeast-5 \
            --no-confirm-changeset \
            --parameter-overrides "Environment=$ENVIRONMENT"

          # Output deployed API URL
          aws cloudformation describe-stacks \
            --stack-name $STACK_NAME \
            --query "Stacks[0].Outputs" --output json
