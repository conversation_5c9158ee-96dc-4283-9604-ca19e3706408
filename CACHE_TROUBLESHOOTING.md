# Cache Troubleshooting Guide

## Issues Fixed

### 1. **Redis Configuration for Local Profile**
**Problem**: `application-local.yml` was pointing to AWS ElastiCache instead of localhost
**Solution**: Updated to use `localhost:6379`

### 2. **Component Scanning**
**Problem**: Services weren't being scanned due to `@Import` instead of `@ComponentScan`
**Solution**: Added `@ComponentScan(basePackages = "com.eroses")` to Application.java

### 3. **LocalDateTime Serialization Error**
**Problem**: `LocalDateTime` fields in models couldn't be serialized to JSON for Redis cache
**Error**: `Java 8 date/time type 'java.time.LocalDateTime' not supported by default`
**Solution**: Added JavaTimeModule to ObjectMapper in cache configuration

### 4. **Missing Debug Logging**
**Problem**: No visibility into cache operations
**Solution**: Added cache debug logging in `application-local.yml`

## Testing Steps

### Step 1: Verify Redis Connection
```bash
redis-cli ping
# Should return: PONG
```

### Step 2: Test Simple Caching
1. Start your application
2. Call: `GET http://localhost:8081/test/cache/simple`
3. Check logs for: `"CACHE TEST: Executing simpleTest()"`
4. Call the same endpoint again
5. The log message should NOT appear the second time (cache hit)

### Step 3: Test Address Caching
1. Call: `GET http://localhost:8081/society/admin/address/list`
2. Check logs for: `"Fetching all addresses from database (cache miss)"`
3. Call the same endpoint again
4. The log message should NOT appear the second time

### Step 4: Test Constitution Type Caching
1. Call: `GET http://localhost:8081/society/admin/constitutionTypeWithClauseContent/list`
2. Check logs for cache miss messages
3. Call again to verify cache hit

## Verification Commands

### Check Redis Keys
```bash
redis-cli keys "*"
```

### Monitor Redis Operations
```bash
redis-cli monitor
```

### Check Application Logs
Look for these log messages:
- `"Configuring Redis connection for LOCAL profile"`
- `"Redis Cache Manager configured successfully"`
- `"CACHE TEST: Executing simpleTest()"`
- `"Fetching all addresses from database (cache miss)"`

## Common Issues & Solutions

### Issue: Cache Not Working
**Symptoms**: Log messages appear every time
**Solutions**:
1. Verify Redis is running: `redis-cli ping`
2. Check active profile: Look for "LOCAL profile" in logs
3. Verify @EnableCaching is present
4. Ensure methods are called through Spring proxy

### Issue: Redis Connection Failed
**Symptoms**: Connection errors in logs
**Solutions**:
1. Start Redis: `brew services start redis` (macOS) or `sudo systemctl start redis` (Linux)
2. Check Redis port: `netstat -an | grep 6379`
3. Verify Redis config in application-local.yml

### Issue: Serialization Errors
**Symptoms**: JSON serialization exceptions
**Solutions**:
1. Ensure models implement Serializable
2. Check Jackson configuration
3. Verify Redis serializer setup

## Cache Keys to Monitor

When working correctly, you should see these keys in Redis:
- `addresses::all_addresses`
- `addresses::address_123` (for specific IDs)
- `constitution_types::all_constitution_types`
- `constitution_types::constitution_types_with_clause_contents`
- `clause_contents::all_clause_contents`
- `clause_contents::clause_contents_type_1` (for specific types)

## Performance Verification

### Before Caching
- Multiple database queries for each request
- Slower response times
- Higher database load

### After Caching
- Database query only on first request (cache miss)
- Faster subsequent requests (cache hit)
- Reduced database load
- Cache keys visible in Redis

## Next Steps

1. **Restart your application** to apply all configuration changes
2. **Test the cache endpoints** using the steps above
3. **Monitor the logs** for cache-related messages
4. **Verify Redis keys** are being created
5. **Test your original APIs** to confirm caching is working

If caching is still not working after these fixes, check:
- Application startup logs for any cache-related errors
- Redis connectivity
- Spring profile activation
- Method proxy calls (avoid self-invocation)
