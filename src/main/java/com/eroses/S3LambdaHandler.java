package com.eroses;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.S3Event;
import com.eroses.external.society.api.facade.DocumentWriteFacade;
import com.eroses.external.society.dto.request.S3EventNotificationRequest;
import com.eroses.external.society.utils.AwsS3Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.services.s3.S3Client;

public class S3LambdaHandler implements RequestHandler<S3Event, String> {

    private static final Logger logger = LoggerFactory.getLogger(S3LambdaHandler.class);
    private final AwsS3Utils awsS3Utils;
    private final DocumentWriteFacade documentWriteFacade;

    public S3LambdaHandler(AwsS3Utils awsS3Utils, DocumentWriteFacade documentWriteFacade) {
        this.awsS3Utils = awsS3Utils;
        this.documentWriteFacade = documentWriteFacade;
    }

    @Override
    public String handleRequest(S3Event s3event, Context context) {
        try {
            S3Client s3Client = awsS3Utils.createS3Client();

            // Get the first record from the event
            S3Event.S3EventNotificationRecord record = s3event.getRecords().get(0);
            String srcBucket = record.getS3().getBucket().getName();
            String srcKey = record.getS3().getObject().getKey();

            S3EventNotificationRequest s3EventNotificationRequest = new S3EventNotificationRequest();
            s3EventNotificationRequest.setBucketName(srcBucket);
            s3EventNotificationRequest.setS3Key(srcKey);

            documentWriteFacade.registerFileS3Trigger(s3EventNotificationRequest);

            return "OK, S3 Event Notification Triggered";
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
