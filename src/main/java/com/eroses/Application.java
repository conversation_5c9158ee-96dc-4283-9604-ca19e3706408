package com.eroses;

import com.eroses.admin.society.controller.PingController;
import com.eroses.config.cache.CacheConfig;
import com.eroses.config.cache.LocalCacheConfig;
import com.eroses.external.society.controller.AdmController;
import com.eroses.external.society.controller.SocietyController;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;


@SpringBootApplication
@EnableFeignClients
// We use direct @Import instead of @ComponentScan to speed up cold starts
// @ComponentScan(basePackages = "com.eroses")
@Import({PingController.class, SocietyController.class, AdmController.class, CacheConfig.class, LocalCacheConfig.class})
@MapperScan(basePackages = { "com.eroses.external.society.mappers", "com.eroses.user.mappers"}, annotationClass = Mapper.class)
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}