package com.eroses.external.society.security.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to mark methods that require the authenticated user to be a member of a society.
 * The society ID can be extracted from method parameters or path variables.
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireSocietyMembership {
    
    /**
     * The name of the parameter that contains the society ID.
     * Default is "societyId"
     */
    String societyIdParam() default "societyId";
    
    /**
     * Whether to check for active membership only.
     * Default is true (only active members allowed)
     */
    boolean activeOnly() default true;
    
    /**
     * Custom error message when membership validation fails
     */
    String message() default "User is not a member of the specified society";
}
