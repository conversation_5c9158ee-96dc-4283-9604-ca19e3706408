package com.eroses.external.society.security.util;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Utility class for extracting parameter values from AOP JoinPoints.
 * Supports extraction from @PathVariable, @RequestParam, and request body objects.
 */
@Slf4j
@Component
public class ParameterExtractionUtil {

    // Cache for reflection field lookups to improve performance
    private final Map<String, Field> fieldCache = new ConcurrentHashMap<>();

    /**
     * Extracts a parameter value and converts it to Long
     *
     * @param joinPoint the AOP join point
     * @param parameterName the name of the parameter to extract
     * @return the parameter value as Long, or null if not found
     */
    public Long extractLongParameter(JoinPoint joinPoint, String parameterName) {
        Object value = extractParameterValue(joinPoint, parameterName);
        return convertToLong(value);
    }

    /**
     * Extracts a parameter value and converts it to String
     *
     * @param joinPoint the AOP join point
     * @param parameterName the name of the parameter to extract
     * @return the parameter value as String, or null if not found
     */
    public String extractStringParameter(JoinPoint joinPoint, String parameterName) {
        Object value = extractParameterValue(joinPoint, parameterName);
        return convertToString(value);
    }

    /**
     * Generic method to extract parameter value from various sources
     *
     * @param joinPoint the AOP join point
     * @param parameterName the name of the parameter to extract
     * @return the raw parameter value, or null if not found
     */
    public Object extractParameterValue(JoinPoint joinPoint, String parameterName) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        Object[] args = joinPoint.getArgs();

        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];

            // Check for @PathVariable annotation
            Object pathVariableValue = extractFromPathVariable(parameter, args[i], parameterName);
            if (pathVariableValue != null) {
                return pathVariableValue;
            }

            // Check for @RequestParam annotation
            Object requestParamValue = extractFromRequestParam(parameter, args[i], parameterName);
            if (requestParamValue != null) {
                return requestParamValue;
            }

            // Check parameter name directly
            if (parameterName.equals(parameter.getName()) && args[i] != null) {
                return args[i];
            }

            // Check if the parameter is a request object that might contain the field
            if (args[i] != null && isRequestObject(args[i])) {
                Object fieldValue = extractFromRequestObject(args[i], parameterName);
                if (fieldValue != null) {
                    return fieldValue;
                }
            }
        }

        log.warn("Parameter '{}' not found in method parameters for method: {}",
                parameterName, signature.getName());
        return null;
    }

    /**
     * Extracts value from @PathVariable parameter
     */
    private Object extractFromPathVariable(Parameter parameter, Object arg, String parameterName) {
        PathVariable pathVariable = parameter.getAnnotation(PathVariable.class);
        if (pathVariable != null && arg != null) {
            String pathVarName = getPathVariableName(pathVariable, parameter);
            if (parameterName.equals(pathVarName)) {
                return arg;
            }
        }
        return null;
    }

    /**
     * Extracts value from @RequestParam parameter
     */
    private Object extractFromRequestParam(Parameter parameter, Object arg, String parameterName) {
        RequestParam requestParam = parameter.getAnnotation(RequestParam.class);
        if (requestParam != null && arg != null) {
            String requestParamName = getRequestParamName(requestParam, parameter);
            if (parameterName.equals(requestParamName)) {
                return arg;
            }
        }
        return null;
    }

    /**
     * Gets the effective name of a @PathVariable parameter
     */
    private String getPathVariableName(PathVariable pathVariable, Parameter parameter) {
        if (!pathVariable.value().isEmpty()) {
            return pathVariable.value();
        }
        if (!pathVariable.name().isEmpty()) {
            return pathVariable.name();
        }
        return parameter.getName();
    }

    /**
     * Gets the effective name of a @RequestParam parameter
     */
    private String getRequestParamName(RequestParam requestParam, Parameter parameter) {
        if (!requestParam.value().isEmpty()) {
            return requestParam.value();
        }
        if (!requestParam.name().isEmpty()) {
            return requestParam.name();
        }
        return parameter.getName();
    }

    /**
     * Extracts field value from request object using cached reflection
     */
    private Object extractFromRequestObject(Object obj, String fieldName) {
        String cacheKey = obj.getClass().getName() + "." + fieldName;

        try {
            Field field = fieldCache.computeIfAbsent(cacheKey,
                    k -> findField(obj.getClass(), fieldName));

            if (field != null) {
                return field.get(obj);
            }
        } catch (Exception e) {
            log.debug("Could not extract field '{}' from object: {}", fieldName, e.getMessage());
        }
        return null;
    }

    /**
     * Checks if the object is likely a request DTO/object
     */
    private boolean isRequestObject(Object obj) {
        String className = obj.getClass().getSimpleName();
        return className.contains("Request") ||
                className.contains("Dto") ||
                className.contains("DTO") ||
                className.contains("Command");
    }

    /**
     * Finds field in class hierarchy with caching
     */
    private Field findField(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null) {
            try {
                Field field = currentClass.getDeclaredField(fieldName);
                field.setAccessible(true);
                return field;
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }

    /**
     * Converts object to Long with null safety
     */
    private Long convertToLong(Object value) {
        if (value == null) return null;

        if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof Integer) {
            return ((Integer) value).longValue();
        } else if (value instanceof String) {
            try {
                return Long.parseLong(((String) value).trim());
            } catch (NumberFormatException e) {
                log.warn("Cannot convert string '{}' to Long", value);
                return null;
            }
        }

        log.warn("Cannot convert {} to Long", value.getClass().getSimpleName());
        return null;
    }

    /**
     * Converts object to String with null safety
     */
    private String convertToString(Object value) {
        return value != null ? value.toString().trim() : null;
    }
}