package com.eroses.external.society.security.annotation;

import com.eroses.external.society.model.enums.Position;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to mark methods that require the authenticated user to have specific roles in a society.
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireSocietyRole {
    
    /**
     * The name of the parameter that contains the society ID.
     * Default is "societyId"
     */
    String societyIdParam() default "societyId";
    
    /**
     * Required positions/roles in the society
     */
    Position[] roles() default {};
    
    /**
     * Required designation codes (alternative to roles)
     */
    String[] designationCodes() default {};
    
    /**
     * Whether to check for active membership only.
     * Default is true (only active members allowed)
     */
    boolean activeOnly() default true;
    
    /**
     * Custom error message when role validation fails
     */
    String message() default "User does not have required role in the specified society";
}
