package com.eroses.external.society.security.handler;

import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.security.exception.UserBlacklistedException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.time.LocalDateTime;

/**
 * Exception handler for blacklist-related exceptions
 */
@Slf4j
@RestControllerAdvice
public class BlacklistExceptionHandler {
    
    @ExceptionHandler(UserBlacklistedException.class)
    public ResponseEntity<ApiResponse<Object>> handleUserBlacklistedException(UserBlacklistedException ex) {
        log.warn("User blacklist validation failed: {}", ex.getMessage());
        
        ApiResponse<Object> response = new ApiResponse<>(
            "Bad Request", 
            HttpStatus.BAD_REQUEST.value(), 
            ex.getMessage(), 
            null, 
            LocalDateTime.now()
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }
}