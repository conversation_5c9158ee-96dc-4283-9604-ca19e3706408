package com.eroses.external.society.security.service;

import com.eroses.external.society.constants.UserGroupConstants;
import com.eroses.external.society.model.Committee;
import com.eroses.external.society.model.enums.Position;
import com.eroses.external.society.model.enums.StatusCode;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.security.exception.InsufficientSocietyPrivilegesException;
import com.eroses.external.society.security.exception.SocietyMembershipException;
import com.eroses.external.society.model.Society;
import com.eroses.external.society.service.CommitteeReadDomainService;
import com.eroses.external.society.service.NonCitizenCommitteeReadDomainService;
import com.eroses.external.society.service.SocietyReadDomainService;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Service to validate society membership and roles for authenticated users
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyMembershipValidator {

    private final CommitteeReadDomainService committeeReadDomainService;
    private final NonCitizenCommitteeReadDomainService nonCitizenCommitteeReadDomainService;
    private final SocietyReadDomainService societyReadDomainService;
    private final UserFacade userFacade;

    /**
     * Checks if the current authenticated user is an admin
     *
     * @return true if user is admin, false otherwise
     */
    private boolean isCurrentUserAdmin(User currentUser) {
        return currentUser.getUserGroup() == UserGroupConstants.INTERNAL_USER;
    }

    /**
     * Checks if the society has a secretary (SETIAUSAHA or SETIAUSAHA_AGUNG)
     *
     * @param societyId The society ID to check
     * @return true if society has a secretary, false otherwise
     */
    private boolean societyHasSecretary(Long societyId) {
        try {
            // Check for SETIAUSAHA
            Committee secretary = committeeReadDomainService.findActiveCommitteeInSocietyWithRoles(Position.getAllSecretaryPositions().stream().map(Position::getCode).map(String::valueOf).toList(), societyId);

            return secretary != null;
        } catch (Exception e) {
            log.debug("Error checking if society has secretary: {}", e.getMessage());
            return true; // Assume has secretary if error occurs to be safe
        }
    }

    /**
     * Checks if the society's identificationNo matches the current user's identificationNo
     *
     * @param societyId The society ID to check
     * @param userIdentificationNo The current user's identification number
     * @return true if they match, false otherwise
     */
    private boolean isSocietyOwner(Long societyId, String userIdentificationNo) {
        try {
            Society society = societyReadDomainService.findById(societyId);
            if (society != null && society.getIdentificationNo() != null) {
                return society.getIdentificationNo().equals(userIdentificationNo);
            }
            return false;
        } catch (Exception e) {
            log.debug("Error checking society owner: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Validates if the current authenticated user is a member of the specified society
     *
     * @param societyId The society ID to check membership for
     * @param activeOnly Whether to check only active memberships
     * @throws SocietyMembershipException if user is not a member
     */
    public void validateSocietyMembership(Long societyId, boolean activeOnly) {
        try {
            User currentUser = userFacade.me();
            if (currentUser == null) {
                throw new SocietyMembershipException("No authenticated user found");
            }

            // Check if current user is admin - admins have access to all societies
            if (isCurrentUserAdmin(currentUser)) {
                log.debug("Admin user (userGroup {}) detected, allowing access to society: {}", UserGroupConstants.INTERNAL_USER, societyId);
                return;
            }

            String identificationNo = currentUser.getIdentificationNo();
            log.debug("Validating society membership for user: {} in society: {}", identificationNo, societyId);

            // Check if society has no secretary and current user is the society owner
            if (!societyHasSecretary(societyId) && isSocietyOwner(societyId, identificationNo)) {
                log.debug("Society {} has no secretary and user {} is the society owner, allowing access", societyId, identificationNo);
                return;
            }

            boolean isMember = false;

            if (activeOnly) {
                // Check active membership in Committee
                // NOT ACTIVE application status codes
                List<Integer> notActiveApplicationStatusCodes = new ArrayList<>();
                notActiveApplicationStatusCodes.add(ApplicationStatusCode.PADAM.getCode());
                notActiveApplicationStatusCodes.add(ApplicationStatusCode.TOLAK.getCode());
                notActiveApplicationStatusCodes.add(ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode());
                notActiveApplicationStatusCodes.add(ApplicationStatusCode.INAKTIF.getCode());

                isMember = committeeReadDomainService.existsByIdentityNoAndSocietyIdAndNotEqualApplicationStatusCodesAndStatus(
                    identificationNo, societyId, notActiveApplicationStatusCodes, StatusCode.PADAM.getCode()
                );
            } else {
                // Check any membership status
                Committee committee = committeeReadDomainService.findByIdentificationNoAndSocietyId(identificationNo, societyId);
                if (committee != null) {
                    isMember = true;
                }
            }

            if (!isMember) {
                log.warn("User {} is not a member of society {}", identificationNo, societyId);
                throw new SocietyMembershipException(
                    "User is not a member of the specified society",
                    societyId,
                    identificationNo
                );
            }

            log.debug("Society membership validated successfully for user: {} in society: {}", identificationNo, societyId);

        } catch (Exception e) {
            if (e instanceof SocietyMembershipException) {
                throw (SocietyMembershipException) e;
            }
            log.error("Error validating society membership", e);
            throw new SocietyMembershipException("Error validating society membership: " + e.getMessage(), e);
        }
    }

    /**
     * Validates if the current authenticated user has the required role in the specified society
     *
     * @param societyId The society ID to check role for
     * @param requiredRoles Required positions/roles
     * @param designationCodes Required designation codes
     * @param activeOnly Whether to check only active memberships
     * @throws InsufficientSocietyPrivilegesException if user doesn't have required role
     */
    public void validateSocietyRole(Long societyId, Position[] requiredRoles, String[] designationCodes, boolean activeOnly) {
        try {
            User currentUser = userFacade.me();
            if (currentUser == null) {
                throw new InsufficientSocietyPrivilegesException("No authenticated user found");
            }

            // Check if current user is admin - admins have access to all societies
            if (isCurrentUserAdmin(currentUser)) {
                log.debug("Admin user (userGroup {}) detected, allowing access to society: {}", UserGroupConstants.INTERNAL_USER, societyId);
                return;
            }

            String identificationNo = currentUser.getIdentificationNo();
            log.debug("Validating society role for user: {} in society: {}", identificationNo, societyId);

            // Check if society has no secretary and current user is the society owner
            if (!societyHasSecretary(societyId) && isSocietyOwner(societyId, identificationNo)) {
                log.debug("Society {} has no secretary and user {} is the society owner, allowing role access", societyId, identificationNo);
                return;
            }

            // First validate membership
            validateSocietyMembership(societyId, activeOnly);

            // Get user's committee record
            Committee committee = null;
            if (activeOnly) {
                committee = committeeReadDomainService.findBySocietyIdAndIdentificationNoAndNotEqualApplicationStatusCodeAndStatus(
                    societyId, identificationNo, ApplicationStatusCode.PADAM.getCode(), StatusCode.PADAM.getCode()
                );
            } else {
                committee = committeeReadDomainService.findByIdentificationNoAndSocietyId(identificationNo, societyId);
            }

            if (committee == null) {
                log.warn("User {} committee record not found in society {}", identificationNo, societyId);
                throw new InsufficientSocietyPrivilegesException(
                    "User committee record not found in society",
                    societyId,
                    identificationNo,
                    Arrays.toString(requiredRoles)
                );
            }

            boolean hasRequiredRole = false;
            String userDesignationCode = committee.getDesignationCode();

            // Check designation codes first
            if (designationCodes != null) {
                for (String requiredCode : designationCodes) {
                    if (requiredCode.equals(userDesignationCode)) {
                        hasRequiredRole = true;
                        break;
                    }
                }
            }

            // Check position roles if designation codes don't match
            if (!hasRequiredRole && requiredRoles != null) {
                for (Position requiredRole : requiredRoles) {
                    if (String.valueOf(requiredRole.getCode()).equals(userDesignationCode)) {
                        hasRequiredRole = true;
                        break;
                    }
                }
            }

            if (!hasRequiredRole) {
                log.warn("User {} does not have required role in society {}. User role: {}, Required roles: {}",
                    identificationNo, societyId, userDesignationCode, Arrays.toString(requiredRoles));
                throw new InsufficientSocietyPrivilegesException(
                    "User does not have required role in society",
                    societyId,
                    identificationNo,
                    Arrays.toString(requiredRoles)
                );
            }

            log.debug("Society role validated successfully for user: {} in society: {}", identificationNo, societyId);

        } catch (Exception e) {
            if (e instanceof InsufficientSocietyPrivilegesException) {
                throw (InsufficientSocietyPrivilegesException) e;
            }
            if (e instanceof SocietyMembershipException) {
                throw (SocietyMembershipException) e;
            }
            log.error("Error validating society role", e);
            throw new InsufficientSocietyPrivilegesException("Error validating society role: " + e.getMessage(), e);
        }
    }

    /**
     * Checks if the current user is a member of the specified society without throwing exceptions
     *
     * @param societyId The society ID to check
     * @param activeOnly Whether to check only active memberships
     * @return true if user is a member, false otherwise
     */
    public boolean isSocietyMember(Long societyId, boolean activeOnly) {
        try {
            validateSocietyMembership(societyId, activeOnly);
            return true;
        } catch (Exception e) {
            log.debug("User is not a member of society {}: {}", societyId, e.getMessage());
            return false;
        }
    }
}
