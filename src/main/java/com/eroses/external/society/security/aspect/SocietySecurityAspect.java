package com.eroses.external.society.security.aspect;

import com.eroses.external.society.security.annotation.RequireSocietyMembership;
import com.eroses.external.society.security.annotation.RequireSocietyRole;
import com.eroses.external.society.security.service.SocietyMembershipValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * AOP Aspect to handle society security annotations
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class SocietySecurityAspect {
    
    private final SocietyMembershipValidator membershipValidator;
    
    /**
     * Intercepts methods annotated with @RequireSocietyMembership
     */
    @Before("@annotation(requireSocietyMembership)")
    public void validateSocietyMembership(JoinPoint joinPoint, RequireSocietyMembership requireSocietyMembership) {
        log.debug("Validating society membership for method: {}", joinPoint.getSignature().getName());
        
        Long societyId = extractSocietyId(joinPoint, requireSocietyMembership.societyIdParam());
        if (societyId == null || societyId == 0) {
            return;
        }
        
        membershipValidator.validateSocietyMembership(societyId, requireSocietyMembership.activeOnly());
    }
    
    /**
     * Intercepts methods annotated with @RequireSocietyRole
     */
    @Before("@annotation(requireSocietyRole)")
    public void validateSocietyRole(JoinPoint joinPoint, RequireSocietyRole requireSocietyRole) {
        log.debug("Validating society role for method: {}", joinPoint.getSignature().getName());
        
        Long societyId = extractSocietyId(joinPoint, requireSocietyRole.societyIdParam());
        if (societyId == null || societyId == 0) {
            return;
        }
        
        membershipValidator.validateSocietyRole(
            societyId, 
            requireSocietyRole.roles(), 
            requireSocietyRole.designationCodes(), 
            requireSocietyRole.activeOnly()
        );
    }
    
    /**
     * Extracts society ID from method parameters
     */
    private Long extractSocietyId(JoinPoint joinPoint, String parameterName) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        Object[] args = joinPoint.getArgs();
        
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            
            // Check for @PathVariable annotation
            PathVariable pathVariable = parameter.getAnnotation(PathVariable.class);
            if (pathVariable != null) {
                String pathVarName = pathVariable.value().isEmpty() ? pathVariable.name() : pathVariable.value();
                if (pathVarName.isEmpty()) {
                    pathVarName = parameter.getName();
                }
                if (parameterName.equals(pathVarName) && args[i] != null) {
                    return convertToLong(args[i]);
                }
            }
            
            // Check for @RequestParam annotation
            RequestParam requestParam = parameter.getAnnotation(RequestParam.class);
            if (requestParam != null) {
                String requestParamName = requestParam.value().isEmpty() ? requestParam.name() : requestParam.value();
                if (requestParamName.isEmpty()) {
                    requestParamName = parameter.getName();
                }
                if (parameterName.equals(requestParamName) && args[i] != null) {
                    return convertToLong(args[i]);
                }
            }
            
            // Check parameter name directly (for request body fields or simple parameters)
            if (parameterName.equals(parameter.getName()) && args[i] != null) {
                return convertToLong(args[i]);
            }
            
            // Check if the parameter is a request object that might contain societyId
            if (args[i] != null && isRequestObject(args[i])) {
                Long societyId = extractSocietyIdFromObject(args[i], parameterName);
                if (societyId != null) {
                    return societyId;
                }
            }
        }
        
        log.warn("Society ID parameter '{}' not found in method parameters for method: {}", 
            parameterName, signature.getName());
        return null;
    }
    
    /**
     * Converts object to Long
     */
    private Long convertToLong(Object value) {
        if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof Integer) {
            return ((Integer) value).longValue();
        } else if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                log.warn("Cannot convert string '{}' to Long", value);
                return null;
            }
        }
        return null;
    }
    
    /**
     * Checks if the object is likely a request DTO
     */
    private boolean isRequestObject(Object obj) {
        String className = obj.getClass().getSimpleName();
        return className.contains("Request") || className.contains("Dto") || className.contains("DTO");
    }
    
    /**
     * Extracts society ID from request object using reflection
     */
    private Long extractSocietyIdFromObject(Object obj, String fieldName) {
        try {
            java.lang.reflect.Field field = findField(obj.getClass(), fieldName);
            if (field != null) {
                field.setAccessible(true);
                Object value = field.get(obj);
                return convertToLong(value);
            }
        } catch (Exception e) {
            log.debug("Could not extract field '{}' from object: {}", fieldName, e.getMessage());
        }
        return null;
    }
    
    /**
     * Finds field in class hierarchy
     */
    private java.lang.reflect.Field findField(Class<?> clazz, String fieldName) {
        while (clazz != null) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        return null;
    }
}
