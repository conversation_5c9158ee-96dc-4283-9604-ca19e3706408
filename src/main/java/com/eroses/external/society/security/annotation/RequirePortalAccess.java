package com.eroses.external.society.security.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to check if the current user has access to the specified portal
 * based on their user group from JWT token
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequirePortalAccess {
    
    /**
     * The name of the header parameter that contains the portal number.
     * Default is "portal"
     */
    String portalHeader() default "portal";
    
    /**
     * Custom error message when portal access validation fails
     */
    String message() default "User does not have access to the specified portal";
}