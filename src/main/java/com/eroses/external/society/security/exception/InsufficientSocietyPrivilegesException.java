package com.eroses.external.society.security.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception thrown when a user doesn't have sufficient privileges/roles in a society
 */
@ResponseStatus(HttpStatus.FORBIDDEN)
public class InsufficientSocietyPrivilegesException extends RuntimeException {
    
    private final Long societyId;
    private final String identificationNo;
    private final String requiredRole;
    
    public InsufficientSocietyPrivilegesException(String message) {
        super(message);
        this.societyId = null;
        this.identificationNo = null;
        this.requiredRole = null;
    }
    
    public InsufficientSocietyPrivilegesException(String message, Long societyId, String identificationNo, String requiredRole) {
        super(message);
        this.societyId = societyId;
        this.identificationNo = identificationNo;
        this.requiredRole = requiredRole;
    }
    
    public InsufficientSocietyPrivilegesException(String message, Throwable cause) {
        super(message, cause);
        this.societyId = null;
        this.identificationNo = null;
        this.requiredRole = null;
    }

    public Long getSocietyId() {
        return societyId;
    }

    public String getIdentificationNo() {
        return identificationNo;
    }

    public String getRequiredRole() {
        return requiredRole;
    }
}
