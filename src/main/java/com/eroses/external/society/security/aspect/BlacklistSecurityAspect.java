package com.eroses.external.society.security.aspect;

import com.eroses.external.society.security.annotation.RequireCurrentUserNotBlacklisted;
import com.eroses.external.society.security.annotation.RequireTargetUserNotBlacklisted;
import com.eroses.external.society.security.service.BlacklistValidator;
import com.eroses.external.society.security.util.ParameterExtractionUtil;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

/**
 * AOP Aspect to handle blacklist security validation
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class BlacklistSecurityAspect {

    private final BlacklistValidator blacklistValidator;
    private final ParameterExtractionUtil parameterExtractor;
    private final UserFacade userFacade;

    /**
     * Intercepts methods annotated with @RequireCurrentUserNotBlacklisted
     */
    @Before("@annotation(requireCurrentUserNotBlacklisted)")
    public void validateCurrentUserNotBlacklisted(JoinPoint joinPoint, RequireCurrentUserNotBlacklisted requireCurrentUserNotBlacklisted) throws Exception {
        log.debug("Validating blacklist status for method: {}", joinPoint.getSignature().getName());

        User user = userFacade.me();
        if (user == null || user.getIdentificationNo() == null) {
            throw new IllegalArgumentException("Identification number not found or empty");
        }
        blacklistValidator.validateNotBlacklisted(user.getIdentificationNo());
    }

    /**
     * Intercepts methods annotated with @RequireTargetUserNotBlacklisted
     */
    @Before("@annotation(requireTargetUserNotBlacklisted)")
    public void validateTargetUserNotBlacklisted(JoinPoint joinPoint, RequireTargetUserNotBlacklisted requireTargetUserNotBlacklisted) {
        log.debug("Validating blacklist status for method: {}", joinPoint.getSignature().getName());

        String identificationNo = parameterExtractor.extractStringParameter(
                joinPoint, requireTargetUserNotBlacklisted.identificationNoParam());

        if (identificationNo == null || identificationNo.trim().isEmpty()) {
            log.warn("Identification number not found or empty for method: {}", joinPoint.getSignature().getName());
            return; // Consider throwing exception instead
        }

        blacklistValidator.validateNotBlacklisted(identificationNo);
    }
}