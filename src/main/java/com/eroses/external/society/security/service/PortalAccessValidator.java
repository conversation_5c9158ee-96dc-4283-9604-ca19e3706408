package com.eroses.external.society.security.service;

import com.eroses.innoxaf.security.utils.CustomUserDetails;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class PortalAccessValidator {
    
    private final UserFacade userFacade;
    
    /**
     * Validates if the current user has access to the specified portal
     * 
     * @param portalNumber The portal number from header
     * @param customMessage Custom error message
     * @throws ResponseStatusException if user doesn't have access
     */
    public void validatePortalAccess(Integer portalNumber, String customMessage) {
        try {
            //Get user group from JWT token
            List<Integer> userGroups = getUserGroupsFromJWT();

            User currentUser = userFacade.me();
            if (currentUser == null) {
                log.warn("No authenticated user found");
                throw new ResponseStatusException(HttpStatus.FORBIDDEN, "Authentication required");
            }

            if (userGroups == null || userGroups.isEmpty()) {
                log.warn("User {} has no user groups", currentUser.getIdentificationNo());
                throw new ResponseStatusException(HttpStatus.FORBIDDEN, customMessage);
            }

            if (!userGroups.contains(portalNumber)) {
                log.warn("User {} with groups {} does not have access to portal {}", 
                    currentUser.getIdentificationNo(), userGroups, portalNumber);
                throw new ResponseStatusException(HttpStatus.FORBIDDEN, customMessage);
            }
            
            log.debug("Portal access validated for user {} to portal {}", 
                currentUser.getIdentificationNo(), portalNumber);
                
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error validating portal access", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Portal access validation failed");
        }
    }
    
    /**
     * Extract user groups from the current user from JWT token
     */
    private List<Integer> getUserGroupsFromJWT() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if(authentication != null && authentication.isAuthenticated()){
            Object principal = authentication.getPrincipal();
            if(principal instanceof UserDetails){
                CustomUserDetails userDetails = (CustomUserDetails) principal;
                return userDetails.getUserGroup();
            }
        }

        log.warn("getUserGroups method needs to be implemented based on your User model structure");
        return Arrays.asList(); // Return empty list as fallback
    }
}