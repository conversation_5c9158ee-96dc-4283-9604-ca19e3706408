package com.eroses.external.society.security.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception thrown when a user is not a member of the required society
 */
@ResponseStatus(HttpStatus.FORBIDDEN)
public class SocietyMembershipException extends RuntimeException {
    
    private final Long societyId;
    private final String identificationNo;
    
    public SocietyMembershipException(String message) {
        super(message);
        this.societyId = null;
        this.identificationNo = null;
    }
    
    public SocietyMembershipException(String message, Long societyId, String identificationNo) {
        super(message);
        this.societyId = societyId;
        this.identificationNo = identificationNo;
    }
    
    public SocietyMembershipException(String message, Throwable cause) {
        super(message, cause);
        this.societyId = null;
        this.identificationNo = null;
    }

    public Long getSocietyId() {
        return societyId;
    }

    public String getIdentificationNo() {
        return identificationNo;
    }
}
