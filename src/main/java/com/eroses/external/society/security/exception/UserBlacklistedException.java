package com.eroses.external.society.security.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception thrown when a user is blacklisted
 */
@ResponseStatus(HttpStatus.BAD_REQUEST)
public class UserBlacklistedException extends RuntimeException {
    
    private final String identificationNo;

    public UserBlacklistedException(String message) {
        super(message);
        this.identificationNo = null;
    }
    
    public UserBlacklistedException(String message, String identificationNo) {
        super(message);
        this.identificationNo = identificationNo;
    }
    
    public String getIdentificationNo() {
        return identificationNo;
    }
    
}