package com.eroses.external.society.security.handler;

import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.security.exception.InsufficientSocietyPrivilegesException;
import com.eroses.external.society.security.exception.SocietyMembershipException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.time.LocalDateTime;

/**
 * Global exception handler for society security exceptions
 */
@Slf4j
@RestControllerAdvice
public class SocietySecurityExceptionHandler {
    
    @ExceptionHandler(SocietyMembershipException.class)
    public ResponseEntity<ApiResponse<Object>> handleSocietyMembershipException(SocietyMembershipException ex) {
        log.warn("Society membership validation failed: {}", ex.getMessage());
        
        ApiResponse<Object> response = new ApiResponse<>(
            "Forbidden", 
            HttpStatus.FORBIDDEN.value(), 
            "Access denied: " + ex.getMessage(), 
            null, 
            LocalDateTime.now()
        );
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }
    
    @ExceptionHandler(InsufficientSocietyPrivilegesException.class)
    public ResponseEntity<ApiResponse<Object>> handleInsufficientSocietyPrivilegesException(InsufficientSocietyPrivilegesException ex) {
        log.warn("Society role validation failed: {}", ex.getMessage());
        
        ApiResponse<Object> response = new ApiResponse<>(
            "Forbidden", 
            HttpStatus.FORBIDDEN.value(), 
            "Access denied: " + ex.getMessage(), 
            null, 
            LocalDateTime.now()
        );
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }
    
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Object>> handleIllegalArgumentException(IllegalArgumentException ex) {
        if (ex.getMessage() != null && ex.getMessage().contains("Society ID parameter")) {
            log.error("Society security configuration error: {}", ex.getMessage());
            
            ApiResponse<Object> response = new ApiResponse<>(
                "Internal Server Error", 
                HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                "Security configuration error: " + ex.getMessage(), 
                null, 
                LocalDateTime.now()
            );
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
        
        // Re-throw if not related to society security
        throw ex;
    }
}
