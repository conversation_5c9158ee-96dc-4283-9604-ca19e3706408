package com.eroses.external.society.security.service;

import com.eroses.external.society.model.enums.ErrorMessage;
import com.eroses.external.society.security.exception.UserBlacklistedException;
import com.eroses.external.society.service.blacklist.BlacklistUserReadDomainService;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class BlacklistValidator {
    
    private final BlacklistUserReadDomainService blacklistUserReadDomainService;
    private final UserFacade userFacade;
    
    /**
     * Validates that the current user is not blacklisted
     */
    public void validateNotBlacklisted(String identificationNo) {
        try {
            boolean isUserBlacklisted = blacklistUserReadDomainService
                .existsActiveBlacklistUserByIdentificationNo(identificationNo);
            
            if (isUserBlacklisted) {
                throw new UserBlacklistedException(
                    ErrorMessage.USER_IS_BLACKLISTED.getBmMessage(),
                    identificationNo
                );
            }
        } catch (UserBlacklistedException e) {
            throw e; // Re-throw blacklist exception
        } catch (Exception e) {
            log.error("Error checking blacklist status for current user", e);
            throw new UserBlacklistedException(
                ErrorMessage.UNEXPECTED_ERROR.getMessage(e.getMessage())
            );
        }
    }
}