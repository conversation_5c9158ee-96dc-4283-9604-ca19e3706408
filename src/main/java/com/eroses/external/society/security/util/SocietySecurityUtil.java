package com.eroses.external.society.security.util;

import com.eroses.external.society.model.enums.Position;
import com.eroses.external.society.security.service.SocietyMembershipValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Utility class for programmatic society security checks
 * Use this when annotations are not suitable (e.g., in service layers, conditional logic)
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SocietySecurityUtil {
    
    private final SocietyMembershipValidator membershipValidator;
    
    /**
     * Checks if the current user is a member of the specified society
     * 
     * @param societyId The society ID to check
     * @return true if user is a member, false otherwise
     */
    public boolean isCurrentUserMemberOf(Long societyId) {
        return membershipValidator.isSocietyMember(societyId, true);
    }
    
    /**
     * Checks if the current user is an active member of the specified society
     * 
     * @param societyId The society ID to check
     * @param activeOnly Whether to check only active memberships
     * @return true if user is a member, false otherwise
     */
    public boolean isCurrentUserMemberOf(Long societyId, boolean activeOnly) {
        return membershipValidator.isSocietyMember(societyId, activeOnly);
    }
    
    /**
     * Validates that the current user is a member of the specified society
     * Throws exception if validation fails
     * 
     * @param societyId The society ID to validate
     * @throws com.eroses.external.society.security.exception.SocietyMembershipException if user is not a member
     */
    public void requireMembershipOf(Long societyId) {
        membershipValidator.validateSocietyMembership(societyId, true);
    }
    
    /**
     * Validates that the current user is a member of the specified society
     * Throws exception if validation fails
     * 
     * @param societyId The society ID to validate
     * @param activeOnly Whether to check only active memberships
     * @throws com.eroses.external.society.security.exception.SocietyMembershipException if user is not a member
     */
    public void requireMembershipOf(Long societyId, boolean activeOnly) {
        membershipValidator.validateSocietyMembership(societyId, activeOnly);
    }
    
    /**
     * Validates that the current user has the specified role in the society
     * Throws exception if validation fails
     * 
     * @param societyId The society ID to validate
     * @param requiredRoles Required positions/roles
     * @throws com.eroses.external.society.security.exception.InsufficientSocietyPrivilegesException if user doesn't have required role
     * @throws com.eroses.external.society.security.exception.SocietyMembershipException if user is not a member
     */
    public void requireRoleInSociety(Long societyId, Position... requiredRoles) {
        membershipValidator.validateSocietyRole(societyId, requiredRoles, new String[]{}, true);
    }
    
    /**
     * Validates that the current user has the specified designation code in the society
     * Throws exception if validation fails
     * 
     * @param societyId The society ID to validate
     * @param designationCodes Required designation codes
     * @throws com.eroses.external.society.security.exception.InsufficientSocietyPrivilegesException if user doesn't have required role
     * @throws com.eroses.external.society.security.exception.SocietyMembershipException if user is not a member
     */
    public void requireDesignationInSociety(Long societyId, String... designationCodes) {
        membershipValidator.validateSocietyRole(societyId, new Position[]{}, designationCodes, true);
    }

    /**
     * Conditional security check - only validates if condition is true
     * Useful for optional security checks based on business logic
     * 
     * @param condition The condition to check
     * @param societyId The society ID to validate
     * @param requiredRoles Required positions/roles
     */
    public void requireRoleInSocietyIf(boolean condition, Long societyId, Position... requiredRoles) {
        if (condition) {
            requireRoleInSociety(societyId, requiredRoles);
        }
    }
    
    /**
     * Validates membership for multiple societies
     * User must be a member of at least one of the specified societies
     * 
     * @param societyIds Array of society IDs
     * @return true if user is a member of at least one society
     */
    public boolean isMemberOfAnySociety(Long... societyIds) {
        for (Long societyId : societyIds) {
            if (isCurrentUserMemberOf(societyId)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Validates membership for multiple societies
     * User must be a member of all specified societies
     * 
     * @param societyIds Array of society IDs
     * @return true if user is a member of all societies
     */
    public boolean isMemberOfAllSocieties(Long... societyIds) {
        for (Long societyId : societyIds) {
            if (!isCurrentUserMemberOf(societyId)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Validates that the current user is a member of at least one of the specified societies
     * Throws exception if validation fails
     * 
     * @param societyIds Array of society IDs
     * @throws com.eroses.external.society.security.exception.SocietyMembershipException if user is not a member of any society
     */
    public void requireMembershipOfAny(Long... societyIds) {
        if (!isMemberOfAnySociety(societyIds)) {
            throw new com.eroses.external.society.security.exception.SocietyMembershipException(
                "User is not a member of any of the specified societies"
            );
        }
    }
    
    /**
     * Validates that the current user is a member of all specified societies
     * Throws exception if validation fails
     * 
     * @param societyIds Array of society IDs
     * @throws com.eroses.external.society.security.exception.SocietyMembershipException if user is not a member of all societies
     */
    public void requireMembershipOfAll(Long... societyIds) {
        for (Long societyId : societyIds) {
            requireMembershipOf(societyId);
        }
    }
}
