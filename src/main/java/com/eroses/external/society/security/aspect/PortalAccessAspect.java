package com.eroses.external.society.security.aspect;

import com.eroses.external.society.security.annotation.RequirePortalAccess;
import com.eroses.external.society.security.service.PortalAccessValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class PortalAccessAspect {
    
    private final PortalAccessValidator portalAccessValidator;
    
    /**
     * Intercepts methods annotated with @RequirePortalAccess
     */
    @Before("@annotation(requirePortalAccess)")
    public void validatePortalAccess(JoinPoint joinPoint, RequirePortalAccess requirePortalAccess) {
        log.debug("Validating portal access for method: {}", joinPoint.getSignature().getName());
        
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            throw new IllegalStateException("No HTTP request found");
        }
        
        String portalHeader = request.getHeader(requirePortalAccess.portalHeader());
        if (portalHeader == null || portalHeader.trim().isEmpty()) {
            log.warn("Portal header '{}' not found or empty", requirePortalAccess.portalHeader());
            throw new IllegalArgumentException("Portal header is required");
        }
        
        try {
            Integer portalNumber = Integer.parseInt(portalHeader.trim());
            portalAccessValidator.validatePortalAccess(portalNumber, requirePortalAccess.message());
        } catch (NumberFormatException e) {
            log.warn("Invalid portal number format: {}", portalHeader);
            throw new IllegalArgumentException("Invalid portal number format");
        }
    }
    
    /**
     * Intercepts classes annotated with @RequirePortalAccess
     */
    @Before("@within(requirePortalAccess)")
    public void validatePortalAccessForClass(JoinPoint joinPoint, RequirePortalAccess requirePortalAccess) {
        validatePortalAccess(joinPoint, requirePortalAccess);
    }
    
    private HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }
}