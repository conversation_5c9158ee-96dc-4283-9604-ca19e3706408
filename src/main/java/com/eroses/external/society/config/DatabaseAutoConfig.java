package com.eroses.external.society.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

//TODO: requires Authentication instance to complete
@Configuration
@EnableJpaRepositories
//@EnableJpaAuditing(auditorAwareRef = "auditorAware")
public class DatabaseAutoConfig {
//    @Bean
//    public AuditorAware<String> auditorAware() {
//        return () -> {
//        }
//    }
}
