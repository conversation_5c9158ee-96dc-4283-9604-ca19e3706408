package com.eroses.external.society.config;


import com.eroses.external.society.exception.ApplicationException;
import com.eroses.external.society.exception.DatabaseAccessException;
import com.eroses.external.society.exception.InvalidInputException;
import com.eroses.external.society.exception.ResourceNotFoundException;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.security.exception.InsufficientSocietyPrivilegesException;
import com.eroses.external.society.security.exception.SocietyMembershipException;
import com.itextpdf.text.log.Logger;
import com.itextpdf.text.log.LoggerFactory;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.nio.file.AccessDeniedException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@ControllerAdvice
public class GlobalExceptionalHandler {


    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Object>> handleBadRequest(IllegalArgumentException ex) {
        ApiResponse<Object> response = new ApiResponse<>(
                "ERROR",
                HttpStatus.BAD_REQUEST.value(),
                ex.getMessage(),
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ApiResponse<Object>> handleForbidden(AccessDeniedException ex) {
        ApiResponse<Object> response = new ApiResponse<>(
                "ERROR",
                HttpStatus.FORBIDDEN.value(),
                ex.getMessage(),
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.FORBIDDEN);
    }


    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiResponse<Object>> handleTypeMismatch(MethodArgumentTypeMismatchException ex) {
        ApiResponse<Object> response = new ApiResponse<>(
                "ERROR",
                HttpStatus.BAD_REQUEST.value(),
                "Failed to convert value of type '" + Objects.requireNonNull(ex.getValue()).getClass().getSimpleName() + "' to required type '" + Objects.requireNonNull(ex.getRequiredType()).getSimpleName() + "'; " + ex.getMessage(),
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    // Add more specific exception handlers if needed

    @ExceptionHandler(MethodArgumentNotValidException.class)
    protected ResponseEntity<ApiResponse<Map<String, String>>> handleMethodArgumentNotValid(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        ApiResponse<Map<String, String>> response = new ApiResponse<>(
                "ERROR",
                HttpStatus.BAD_REQUEST.value(),
                "Validation failed",
                errors,
                LocalDateTime.now()
        );

        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MyBatisSystemException.class)
    protected ResponseEntity<ApiResponse<Object>> handleMyBatisSystemException(MyBatisSystemException ex) {
        ApiResponse<Object> response = new ApiResponse<>(
                "ERROR",
                HttpStatus.BAD_REQUEST.value(),
                "Ralat Sistem MyBatis : " + ex.getCause(),
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }


    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleNoHandlerFoundExceptions(NoHandlerFoundException ex) {
        ApiResponse<Object> response = new ApiResponse<>(
                "ERROR",
                HttpStatus.NOT_FOUND.value(),
                ex.getMessage(),
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleAllExceptions(Exception ex) {
        ApiResponse<Object> response = new ApiResponse<>(
                "ERROR",
                HttpStatus.BAD_REQUEST.value(),
                ex.getMessage(),
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(DuplicateKeyException.class)
    public ResponseEntity<ApiResponse<Object>> handleDuplicateKeyException(Exception ex) {
        ApiResponse<Object> response = new ApiResponse<>(
                HttpStatus.CONFLICT.name(),
                HttpStatus.CONFLICT.value(),
                ex.getMessage(),
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(DataAccessException.class)
    public ResponseEntity<ApiResponse<Object>> handleDataAccess(DataAccessException ex) {
        String rootCauseMessage = Optional.ofNullable(ex.getRootCause())
                .map(Throwable::getMessage)
                .orElse("Tiada maklumat tambahan tersedia.");
        ApiResponse<Object> response = new ApiResponse<>(
                "ERROR",
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                "Ralat pangkalan data berlaku: " + rootCauseMessage,
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(NullPointerException.class)
    public ResponseEntity<ApiResponse<Object>> handleNullPointer(NullPointerException ex) {
        // Capture the stack trace as a string for additional debugging information
        StringWriter stackTrace = new StringWriter();
        ex.printStackTrace(new PrintWriter(stackTrace));

        // Create a detailed error response
        ApiResponse<Object> response = new ApiResponse<>(
                "ERROR",
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                "Nilai null ditemui. Punca: " + (ex.getMessage() != null ? ex.getMessage() : "Tiada mesej khusus."),
                stackTrace.toString(), // Optional: Include stack trace in the response data (could be removed in production)
                LocalDateTime.now()
        );

        // Log the error for server-side debugging (production logging setup may vary)
        Logger logger = LoggerFactory.getLogger(getClass());
        logger.error("NullPointerException occurred", ex);

        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(SQLIntegrityConstraintViolationException.class)
    public ResponseEntity<ApiResponse<Object>> handleSQLIntegrityConstraintViolation(SQLIntegrityConstraintViolationException e) {
        Throwable cause = e.getCause();
        ApiResponse<Object> response = new ApiResponse<>(
                "Error",
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                cause != null ? cause.getMessage() : "Integrity constraint violation occurred.",
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(DatabaseAccessException.class)
    public ResponseEntity<ApiResponse<Object>> handleDatabaseAccessException(DatabaseAccessException ex) {
        ApiResponse<Object> response = new ApiResponse<>(
                "DATABASE_ERROR",
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                ex.getMessage(),
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(InvalidInputException.class)
    public ResponseEntity<ApiResponse<Object>> handleInvalidInputException(InvalidInputException ex) {
        ApiResponse<Object> response = new ApiResponse<>(
                "INVALID_INPUT",
                HttpStatus.BAD_REQUEST.value(),
                ex.getMessage(),
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ApplicationException.class)
    public ResponseEntity<ApiResponse<Object>> handleApplicationException(ApplicationException ex) {
        ApiResponse<Object> response = new ApiResponse<>(
                "APPLICATION_ERROR",
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                ex.getMessage(),
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleResourceNotFoundException(ResourceNotFoundException ex) {
        ApiResponse<Object> response = new ApiResponse<>(
                "RESOURCE_NOT_FOUND",
                HttpStatus.NOT_FOUND.value(),
                ex.getMessage(),
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(InsufficientSocietyPrivilegesException.class)
    public ResponseEntity<ApiResponse<Object>> handleInsufficientSocietyPrivilegesException(InsufficientSocietyPrivilegesException ex) {
        ApiResponse<Object> response = new ApiResponse<>(
                "INSUFFICIENT_SOCIETY_PRIVILEGES",
                HttpStatus.FORBIDDEN.value(),
                ex.getMessage(),
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(SocietyMembershipException.class)
    public ResponseEntity<ApiResponse<Object>> handleSocietyMembershipException(SocietyMembershipException ex) {
        ApiResponse<Object> response = new ApiResponse<>(
                "SOCIETY_MEMBERSHIP_EXCEPTION",
                HttpStatus.FORBIDDEN.value(),
                ex.getMessage(),
                null,
                LocalDateTime.now()
        );
        return new ResponseEntity<>(response, HttpStatus.FORBIDDEN);
    }
}
