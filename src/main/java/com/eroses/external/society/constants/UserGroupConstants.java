package com.eroses.external.society.constants;

/**
 * Constants for user groups used throughout the application
 */
public final class UserGroupConstants {
    
    /**
     * External users - Society members, public users
     */
    public static final int EXTERNAL_USER = 1;
    
    /**
     * Internal users - JPPM staff, administrators
     */
    public static final int INTERNAL_USER = 2;

    // Private constructor to prevent instantiation
    private UserGroupConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
    
    /**
     * Check if the given userGroup represents an external user
     * 
     * @param userGroup the user group to check
     * @return true if external user, false otherwise
     */
    public static boolean isExternalUser(int userGroup) {
        return userGroup == EXTERNAL_USER;
    }
    
    /**
     * Check if the given userGroup represents an internal user
     * 
     * @param userGroup the user group to check
     * @return true if internal user, false otherwise
     */
    public static boolean isInternalUser(int userGroup) {
        return userGroup == INTERNAL_USER;
    }

    /**
     * Get the description for a user group
     * 
     * @param userGroup the user group
     * @return description of the user group
     */
    public static String getUserGroupDescription(int userGroup) {
        switch (userGroup) {
            case EXTERNAL_USER:
                return "External User (Society Members, Public)";
            case INTERNAL_USER:
                return "Internal User (JPPM Staff, Administrators)";
            default:
                return "Unknown User Group";
        }
    }
}
