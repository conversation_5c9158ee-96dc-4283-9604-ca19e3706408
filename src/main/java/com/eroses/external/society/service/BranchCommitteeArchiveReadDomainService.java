package com.eroses.external.society.service;

import com.eroses.external.society.mappers.BranchCommitteeArchiveDao;
import com.eroses.external.society.model.BranchCommitteeArchive;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.model.enums.StatusCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class BranchCommitteeArchiveReadDomainService {

    private final BranchCommitteeArchiveDao branchCommitteeArchiveDao;

    public List<BranchCommitteeArchive> getBranchCommitteeArchive(Map<String, Object> param, Integer offset, Integer limit) {
        param.put("offset", offset);
        param.put("limit", limit);

        return branchCommitteeArchiveDao.getBranchCommitteeArchive(param);
    }

    public Long countBranchCommitteeArchive(Map<String, Object> param) {
        return branchCommitteeArchiveDao.countBranchCommitteeArchive(param);
    }

    public List<BranchCommitteeArchive> findByMeetingDate(LocalDate meetingDate) {
        return branchCommitteeArchiveDao.findByMeetingDate(meetingDate);
    }

    public BranchCommitteeArchive findOneByParams(Map<String, Object> params) {
        return branchCommitteeArchiveDao.findOneByParams(params);
    }

    public List<LocalDate> findAllAppointedDates(Long branchId) {
        return branchCommitteeArchiveDao.findAllAppointedDates(branchId);
    }

    public List<BranchCommitteeArchive> findByParams(Map<String, Object> params) {
        return branchCommitteeArchiveDao.findByParam(params);
    }

    public List<BranchCommitteeArchive> findActiveCommitteesInSocietyArchiveWithRoles(Map<String, Object> params) {

        if (!params.containsKey("applicationStatusCode")) {
            params.put("applicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
        }
        if (!params.containsKey("status")) {
            params.put("status", StatusCode.AKTIF.getCode());
        }

        return branchCommitteeArchiveDao.findActiveCommitteesInSocietyArchiveWithRoles(params);
    }
}
