package com.eroses.external.society.service.document.factory;

import com.eroses.external.society.service.document.strategy.DocumentMappingStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * Factory for creating document mapping strategies.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DocumentMappingStrategyFactory {

    private final List<DocumentMappingStrategy> strategies;

    /**
     * Gets the appropriate strategy for the given document template code.
     *
     * @param templateCode The code of the document template
     * @return The appropriate strategy
     * @throws IllegalArgumentException If no strategy is found for the template code
     */
    public DocumentMappingStrategy getStrategy(String templateCode) {
        Optional<DocumentMappingStrategy> strategy = strategies.stream()
                .filter(s -> s.canHandle(templateCode))
                .findFirst();

        return strategy.orElseThrow(() -> {
            log.error("No strategy found for template code: {}", templateCode);
            return new IllegalArgumentException("No strategy found for template code: " + templateCode);
        });
    }
}