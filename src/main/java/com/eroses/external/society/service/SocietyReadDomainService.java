package com.eroses.external.society.service;

import com.eroses.external.society.dto.response.appeal.GetUserSocietiesForAppealResponse;
import com.eroses.external.society.dto.response.society.SocietyGetOneResponse;
import com.eroses.external.society.mappers.SocietyDao;
import com.eroses.external.society.model.Society;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.model.enums.ConstitutionTypeEnum;
import com.eroses.external.society.model.enums.StatusCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyReadDomainService {
    private final SocietyDao societyDao;

    public Society findById(Long id) {
        return societyDao.findById(id);
    }

    public Society findSocietyNo(Long id) {
        return societyDao.findSocietyNo(id);
    }

    public Boolean isExists(Long id) {
        return findById(id) != null;
    }

    public List<Society> getAllSocieties() {
        return societyDao.findAll();
    }

    public Society findBySocietyNo(String societyNo) {
        return societyDao.findBySocietyNo(societyNo);
    }

    public Society findByPaymentId(Long paymentId) {
        return societyDao.findByPaymentId(paymentId);
    }

    public Society findByApplicationNo(String applicationNo) {
        return societyDao.findByApplicationNo(applicationNo);
    }

    public List<Society> searchSocieties(String societyName, String societyNo, Integer offset, Integer limit, String statusCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("searchQuery", societyName);
        params.put("societyNo", societyNo);
        params.put("offset", offset);
        params.put("limit", limit);
        params.put("statusCode", statusCode);
        return societyDao.searchSocieties(params);
    }

    public List<Society> getAllCurrentUserSocietyBranchList(List<Long> societyIds, String societyName, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        if (societyIds != null) params.put("societyIds", societyIds);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (offset != null) params.put("offset", offset);
        if (limit != null) params.put("limit", limit);
        params.put("lulusApplicationStatusCode", ApplicationStatusCode.LULUS.getCode());
        params.put("activeStatusCode", StatusCode.AKTIF.getCode());
        params.put("constitutionType", new String[]{
                ConstitutionTypeEnum.CawanganNGO.getCode(),
                ConstitutionTypeEnum.CawanganAgama.getCode(),
                ConstitutionTypeEnum.BebasCawangan.getCode()
        });
        return societyDao.getAllCurrentUserSocietyBranchList(params);
    }

    public Long countAllCurrentUserSocietyBranchList(List<Long> societyIds, String societyName) {
        Map<String, Object> params = new HashMap<>();
        if (societyIds != null) params.put("societyIds", societyIds);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        params.put("lulusApplicationStatusCode", ApplicationStatusCode.LULUS.getCode());
        params.put("activeStatusCode", StatusCode.AKTIF.getCode());
        params.put("constitutionType", new String[]{
                ConstitutionTypeEnum.CawanganNGO.getCode(),
                ConstitutionTypeEnum.CawanganAgama.getCode(),
                ConstitutionTypeEnum.BebasCawangan.getCode()
        });
        return societyDao.countAllCurrentUserSocietyBranchList(params);
    }

    public Long countSearchedSocieties(String societyName, String statusCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("searchQuery", societyName);
        params.put("statusCode", statusCode);
        return societyDao.countSearchedSocieties(params);
    }

    public List<Society> getAllPending(Integer applicationStatusCode, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "offset", offset,
                "limit", limit
        );
        return societyDao.findAllPending(params);
    }

    public long countGetAllPending(Integer applicationStatusCode) {
        return societyDao.countFindAllPending(applicationStatusCode);
    }

    public List<Society> getAllPendingByCriteria(List<Integer> applicationStatusCodes, String stateCode, Long roId, Integer isQueried, String categoryCode, String subCategoryCode, String societyName, Integer offset, Integer limit) {

        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (roId != null) params.put("ro", roId);
        if (categoryCode != null && !categoryCode.isEmpty()) params.put("categoryCode", categoryCode);
        if (subCategoryCode != null && !subCategoryCode.isEmpty()) params.put("subCategoryCode", subCategoryCode);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (isQueried != null) params.put("isQueried", isQueried);
        if (offset != null) params.put("offset", offset);
        if (limit != null) params.put("limit", limit);

        return societyDao.findAllPending(params);
    }

    public Integer countAllPendingByCriteria(List<Integer> applicationStatusCodes, String stateCode, Long roId, Integer isQueried, String categoryCode, String subCategoryCode, String societyName) {

        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (roId != null) params.put("ro", roId);
        if (categoryCode != null && !categoryCode.isEmpty()) params.put("categoryCode", categoryCode);
        if (subCategoryCode != null && !subCategoryCode.isEmpty()) params.put("subCategoryCode", subCategoryCode);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (isQueried != null) params.put("isQueried", isQueried);

        return societyDao.countAllPendingByCriteria(params);
    }

    public List<Society> getAllPendingExternalAgencyReviewByCriteria(List<Integer> applicationStatusCodes, String stateCode, Long roId, String categoryCode, String subCategoryCode, String societyName, Integer offset, Integer limit) {

        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (roId != null) params.put("ro", roId);
        if (categoryCode != null && !categoryCode.isEmpty()) params.put("categoryCode", categoryCode);
        if (subCategoryCode != null && !subCategoryCode.isEmpty()) params.put("subCategoryCode", subCategoryCode);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (offset != null) params.put("offset", offset);
        if (limit != null) params.put("limit", limit);

        return societyDao.getAllPendingExternalAgencyReviewByCriteria(params);
    }

    public Integer countAllPendingExternalAgencyReviewByCriteria(List<Integer> applicationStatusCodes, String stateCode, Long roId, String categoryCode, String subCategoryCode, String societyName) {

        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (roId != null) params.put("ro", roId);
        if (categoryCode != null && !categoryCode.isEmpty()) params.put("categoryCode", categoryCode);
        if (subCategoryCode != null && !subCategoryCode.isEmpty()) params.put("subCategoryCode", subCategoryCode);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);

        return societyDao.countAllPendingExternalAgencyReviewByCriteria(params);
    }

    public Society findPendingById(Long id, Integer applicationStatusCode) {
        Map<String, Object> params = Map.of(
                "id", id,
                "applicationStatusCode", applicationStatusCode
        );
        return societyDao.findPendingById(params);
    }

    public int countRegisteredToday() {
        return societyDao.countRegisteredToday();
    }

    public int countApprovedToday() {
        return societyDao.countApprovedToday();
    }

//    @Cacheable(value = "searchSocietiesByCriteria", key = "{#societyIdList, #searchQuery, #applicationStatusCode, #statusCode, #registeredYear, #offset, #limit}")
    public List<Society> searchSocietiesByCriteria(List<Long> societyIdList, String searchQuery, Integer applicationStatusCode, String statusCode, Integer registeredYear, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "societyIdList", societyIdList,
                "searchQuery", searchQuery,
                "applicationStatusCode", applicationStatusCode,
                "statusCode", statusCode,
                "registeredYear", registeredYear,
                "offset", offset,
                "limit", limit
        );
        return societyDao.searchSocietiesByCriteria(params);
    }

    public List<Society> findAllByParam( String searchQuery, Integer applicationStatusCode, String statusCode, Integer state, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "searchQuery", searchQuery,
                "applicationStatusCode", applicationStatusCode,
                "statusCode", statusCode,
                "state", state,
                "offset", offset,
                "limit", limit
        );
        return societyDao.findAllByParam(params);
    }

    public Long countFindAllByParam(String searchQuery, Integer applicationStatusCode, String statusCode, Integer state) {
        Map<String, Object> params = Map.of(
                "searchQuery", searchQuery,
                "applicationStatusCode", applicationStatusCode,
                "statusCode", statusCode,
                "state", state
        );
        return societyDao.countFindAllByParam(params);
    }

//    @Cacheable(value = "countSearchedSocietiesByCriteria", key = "{#societyIdList, #searchQuery, #applicationStatusCode, #statusCode, #registeredYear}")
    public Long countSearchedSocietiesByCriteria(List<Long> societyIdList, String searchQuery, Integer applicationStatusCode, String statusCode, Integer registeredYear) {
        Map<String, Object> params = Map.of(
                "societyIdList", societyIdList,
                "searchQuery", searchQuery,
                "applicationStatusCode", applicationStatusCode,
                "statusCode", statusCode,
                "registeredYear", registeredYear
        );
        return societyDao.countSearchedSocietiesByCriteria(params);
    }

    public List<Society> getAllPendingUserStateCode(int applicationStatusCode, String stateCode, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "stateCode", stateCode,
                "offset", offset,
                "limit", limit);

        return societyDao.findAllPendingUserStateCode(params);
    }

    public long countGetAllPendingUserStateCode(int applicationStatusCode, String stateCode) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "stateCode", stateCode);
        return societyDao.countFindAllPendingUserStateCode(params);
    }

    public List<Long> findSocietyIdListByIdentificationNo(String identificationNo) {
        return societyDao.findSocietyIdListByIdentificationNo(identificationNo);
    }

    /**
     * Find society ID list by identification number with optional secretary ownership rule check
     *
     * @param identificationNo The identification number to search for
     * @param checkSecretaryOwnershipRule If true, applies additional checks:
     *        - If society has no secretary and current user is the society owner, allow access
     *        - If society has secretary but current user is not a committee member, deny access
     * @return List of society IDs that match the criteria
     */
    public List<Long> findSocietyIdListByIdentificationNo(String identificationNo, boolean checkSecretaryOwnershipRule) {
        return societyDao.findSocietyIdListByIdentificationNo(identificationNo, checkSecretaryOwnershipRule);
    }

    public Optional<Society> findSocietyByIdAndStatusCode(Map<String, Object> paging) {
        return societyDao.findSocietyByIdAndStatusCode(paging);
    }

    public boolean existsByName(String societyName) {
        return societyDao.countBySocietyName(societyName) > 0;
    }

    public int countStatusCode(String statusCode) {
        return societyDao.countStatusCode(statusCode);
    }

    public int countApplicationStatusCode(int statusCode) {
        return societyDao.countApplicationStatusCode(statusCode);
    }

    public int countApplicationStatusCodeAndPaymentDate(int statusCode, int year) {
        Map<String, Object> params = Map.of(
                "statusCode", statusCode,
                "year", year
        );
        return societyDao.countApplicationStatusCodeAndPaymentDate(params);
    }

    public List<Society> searchJoinSociety(String societyName, String statusCode) {
        Map<String, Object> params = Map.of(
                "searchQuery", societyName,
                "statusCode", statusCode
        );
        return societyDao.searchSocieties(params);
    }

    public List<Society> getSocietyByNameAndStatusAndApplicationStatusCode(String searchQuery, String statusCode, Integer applicationStatusCode, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "searchQuery", searchQuery,
                "statusCode", statusCode,
                "applicationStatusCode", applicationStatusCode,
                "offset", offset,
                "limit", limit
        );
        return societyDao.getSocietyByNameAndStatusAndApplicationStatusCode(params);
    }

    public Long countSocietyByNameAndStatusAndApplicationStatusCode(String searchQuery, String statusCode, Integer applicationStatusCode) {
        Map<String, Object> params = Map.of(
                "searchQuery", searchQuery,
                "statusCode", statusCode,
                "applicationStatusCode", applicationStatusCode
        );
        return societyDao.countSocietyByNameAndStatusAndApplicationStatusCode(params);
    }

//    @Cacheable(value = "getUserSocieties", key = "{#identificationNo, #committeeApplicationStatusCode, #committeeStatusCode, #searchQuery, #applicationStatusCode, #statusCode, #registeredYear, #designationCode, #offset, #limit}")
    public List<SocietyGetOneResponse> getUserSocieties(String identificationNo, int committeeApplicationStatusCode, String committeeStatusCode, String searchQuery, Integer applicationStatusCode, String statusCode, Integer registeredYear, String designationCode, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "identificationNo", identificationNo,
                "committeeApplicationStatusCode", committeeApplicationStatusCode,
                "committeeStatusCode", committeeStatusCode,
                "searchQuery", searchQuery,
                "applicationStatusCode", applicationStatusCode,
                "statusCode", statusCode,
                "registeredYear", registeredYear,
                "designationCode", designationCode,
                "offset", offset,
                "limit", limit
        );
        return societyDao.getUserSocieties(params);
    }

//    @Cacheable(value = "countUserSocieties", key = "{#identificationNo, #committeeApplicationStatusCode, #committeeStatusCode, #searchQuery, #applicationStatusCode, #statusCode, #registeredYear, #designationCode}")
    public Long countGetUserSocieties(String identificationNo, int committeeApplicationStatusCode, String committeeStatusCode, String searchQuery, Integer applicationStatusCode, String statusCode, Integer registeredYear, String designationCode) {
        Map<String, Object> params = Map.of(
                "identificationNo", identificationNo,
                "committeeApplicationStatusCode", committeeApplicationStatusCode,
                "committeeStatusCode", committeeStatusCode,
                "searchQuery", searchQuery,
                "applicationStatusCode", applicationStatusCode,
                "statusCode", statusCode,
                "registeredYear", registeredYear,
                "designationCode", designationCode
        );
        return societyDao.countGetUserSocieties(params);
    }

    public List<Society> findByCriteria(Integer applicationStatusCode){
        //Add more criteria if needed
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode
        );
        return societyDao.findByCriteria(params);
    }

    public List<GetUserSocietiesForAppealResponse> findSocietyAmendmentAndNonCitizenForAppeal(Map<String, Object> param) {
        return societyDao.findSocietyAmendmentAndNonCitizenForAppeal(param);
    }

    public List<GetUserSocietiesForAppealResponse> findSocietyForAppeal(Map<String, Object> param) {
        return societyDao.findSocietyForAppeal(param);
    }

    public List<Society> findAllByIds(List<Long> ids){
        return societyDao.findByIds(ids);
    }

    public List<Long> getAllExpiredSocietyApplicationId(int applicationStatusCode, String statusCode, int registrationPeriodDays) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "statusCode", statusCode,
                "registrationPeriodDays", registrationPeriodDays
        );
        return societyDao.getAllExpiredSocietyApplicationId(params);
    }

    public List<Society> getAllSoonToExpireSocietyApplication(int applicationStatusCode, String statusCode, int registrationPeriodDays, int remainingDaysToExpire) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "statusCode", statusCode,
                "registrationPeriodDays", registrationPeriodDays,
                "remainingDaysToExpire", remainingDaysToExpire
        );
        return societyDao.getAllSoonToExpireSocietyApplication(params);
    }

    public List<Society> getSocietiesByCriteria(String societyName, String societyNo, String stateCode, Integer applicationStatusCode, String statusCode, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (societyNo != null && !societyNo.isEmpty()) params.put("societyNo", societyNo);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);
        if (statusCode != null && !statusCode.isEmpty()) params.put("statusCode", statusCode);
        params.put("offset", offset);
        params.put("limit", limit);
        return societyDao.getSocietiesByCriteria(params);
    }

    public Long countGetSocietiesByCriteria(String societyName, String societyNo, String stateCode, Integer applicationStatusCode, String statusCode) {
        Map<String, Object> params = new HashMap<>();
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (societyNo != null && !societyNo.isEmpty()) params.put("societyNo", societyNo);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);
        if (statusCode != null && !statusCode.isEmpty()) params.put("statusCode", statusCode);
        return societyDao.countGetSocietiesByCriteria(params);
    }

    public List<Society> getSocietiesByCriteriaAndExcludingActiveSocietyCancellationSocieties(String societyName, String societyNo, List<String> stateCodes, Integer societyCategoryCode, Integer applicationStatusCode, String statusCode, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (societyNo != null && !societyNo.isEmpty()) params.put("societyNo", societyNo);
        if (stateCodes != null && !stateCodes.isEmpty()) params.put("stateCodes", stateCodes);
        if (societyCategoryCode != null && societyCategoryCode != 0) params.put("societyCategoryCode", societyCategoryCode);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);
        if (statusCode != null && !statusCode.isEmpty()) params.put("statusCode", statusCode);
        params.put("offset", offset);
        params.put("limit", limit);
        return societyDao.getSocietiesByCriteriaAndExcludingActiveSocietyCancellationSocieties(params);
    }

    public Long countSocietiesByCriteriaAndExcludingActiveSocietyCancellationSocieties(String societyName, String societyNo, List<String> stateCodes, Integer societyCategoryCode, Integer applicationStatusCode, String statusCode) {
        Map<String, Object> params = new HashMap<>();
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (societyNo != null && !societyNo.isEmpty()) params.put("societyNo", societyNo);
        if (stateCodes != null && !stateCodes.isEmpty()) params.put("stateCodes", stateCodes);
        if (societyCategoryCode != null && societyCategoryCode != 0) params.put("societyCategoryCode", societyCategoryCode);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);
        if (statusCode != null && !statusCode.isEmpty()) params.put("statusCode", statusCode);
        return societyDao.countSocietiesByCriteriaAndExcludingActiveSocietyCancellationSocieties(params);
    }

    public List<Society> findByCategoryCodeJppm(String categoryCodeJppm) {
        return societyDao.findByCategoryCodeJppm(categoryCodeJppm);
    }

    public List<Society> findByCategoriesCodeJppm(List<Long> categoriesCodeJppm) {
        return societyDao.findByCategoriesCodeJppm(categoriesCodeJppm);
    }

    public List<Society> findBySocietyLevel(String societyLevel) {
        return societyDao.findBySocietyLevel(societyLevel);
    }

    public List<Society> findByCategoryAndLevel(String categoryCodeJppm, String societyLevel) {
        return societyDao.findByCategoryAndLevel(categoryCodeJppm, societyLevel);
    }
    public List<Society> findByCities(List<String> cities) {
        return societyDao.findByCities(cities);
    }

    public List<Society> findByDistrictCodes(List<Long> districtCodes) {
        return societyDao.findByDistrictCodes(districtCodes);
    }
    public List<Society> findByStateCodes(List<Long> stateCodes) {
        return societyDao.findByStateCodes(stateCodes);
    }

    public List<Society> getAllSocietyPendingApproval(int applicationStatusCode, String statusCode, int daysAfterSubmission) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "statusCode", statusCode,
                "daysAfterSubmission", daysAfterSubmission);
        return societyDao.getAllSocietyPendingApproval(params);
    }
}
