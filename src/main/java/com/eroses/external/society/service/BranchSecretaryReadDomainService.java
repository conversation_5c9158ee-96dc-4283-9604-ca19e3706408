package com.eroses.external.society.service;

import com.eroses.external.society.dto.response.branchSecretary.BranchSecretaryHistoryDetailsResponse;
import com.eroses.external.society.dto.response.branchSecretary.BranchSecretarySlipResponse;
import com.eroses.external.society.mappers.BranchSecretaryDao;
import com.eroses.external.society.model.BranchSecretary;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class BranchSecretaryReadDomainService {
    private final BranchSecretaryDao branchSecretaryDao;

    public List<BranchSecretary> getAll() {
        return branchSecretaryDao.getAll();
    }

    public List<BranchSecretary> getAll(Map<String, Object> params) {
        return branchSecretaryDao.search(params);
    }

    public List<BranchSecretary> getAllByCriteria(Map<String, Object> params) {
        return branchSecretaryDao.getAllByCriteria(params);
    }

    public Long countAllByCriteria(Map<String, Object> params) {
        return branchSecretaryDao.countAllByCriteria(params);
    }

    public List<BranchSecretary> findBySocietyId(Long societyId) {
        return  branchSecretaryDao.findBySocietyId(societyId);
    }

    public BranchSecretarySlipResponse getSlipData(Long newSecretaryBranchId) {
        BranchSecretarySlipResponse response = branchSecretaryDao.getSlipData(newSecretaryBranchId);

        if (response == null) {
            throw new RuntimeException("Data not found.");
        }

        return response;
    }

    public BranchSecretaryHistoryDetailsResponse getNewBranchSecretary(Long newSecretaryBranchId) throws Exception {
        return branchSecretaryDao.getNewBranchSecretary(newSecretaryBranchId);
    }
}
