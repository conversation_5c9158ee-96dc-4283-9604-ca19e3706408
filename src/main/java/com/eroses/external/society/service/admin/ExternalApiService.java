package com.eroses.external.society.service.admin;

import com.eroses.external.society.dto.response.ExternalApiResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class ExternalApiService {

    @Value("${api.jpn.base-url}")
    private String baseUrl;

    @Value("${api.jpn.key}")
    private String apiKey;

    private final RestTemplate restTemplate;

    public ExternalApiService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public ExternalApiResponse callApi(String data) {
        String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                .queryParam("n", data)
                .toUriString() + apiKey;

        return restTemplate.getForObject(url, ExternalApiResponse.class);
    }
}
