package com.eroses.external.society.service.training;

import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

/**
 * Service for retrieving and managing user context information for the training module.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrainingUserContextService {

    private final UserFacade userFacade;

    /**
     * Gets the current authenticated user.
     *
     * @return The current user or null if not authenticated
     */
    public User getCurrentUser() {
        try {
            return userFacade.me();
        } catch (Exception e) {
            log.error("Error retrieving current user", e);
            return null;
        }
    }

    /**
     * Gets the current user's ID.
     *
     * @return The current user's ID or null if not authenticated
     */
    public Long getCurrentUserId() {
        User user = getCurrentUser();
        return user != null ? user.getId() : null;
    }

    /**
     * Gets the current user's name.
     *
     * @return The current user's name or null if not authenticated
     */
    public String getCurrentUserName() {
        User user = getCurrentUser();
        return user != null ? user.getName() : null;
    }

    /**
     * Gets the current user's identification number.
     *
     * @return The current user's identification number or null if not authenticated
     */
    public String getCurrentUserIdentificationNo() {
        User user = getCurrentUser();
        return user != null ? user.getIdentificationNo() : null;
    }

    /**
     * Gets the current user's email.
     *
     * @return The current user's email or null if not authenticated
     */
    public String getCurrentUserEmail() {
        User user = getCurrentUser();
        return user != null ? user.getEmail() : null;
    }

    /**
     * Gets the user ID from an Authentication object.
     * This is useful for controller methods that receive the Authentication object as a parameter.
     *
     * @param authentication The Authentication object
     * @return The user ID as a string
     * @deprecated Use getUserIdAsLong instead to get the ID directly as a Long
     */
    @Deprecated
    public String getUserIdFromAuthentication(Authentication authentication) {
        Long userId = getUserIdAsLong(authentication);
        return userId != null ? userId.toString() : null;
    }

    /**
     * Gets the user ID from an Authentication object as a Long.
     * This is useful for controller methods that need the ID as a Long.
     *
     * @param authentication The Authentication object
     * @return The user ID as a Long or null if not authenticated
     */
    public Long getUserIdAsLong(Authentication authentication) {
        if (authentication != null && authentication.isAuthenticated()) {
            // Get the actual user from the authentication
            User user = getUserFromAuthentication(authentication);
            // Return the user ID
            return user != null ? user.getId() : null;
        }
        return null;
    }

    /**
     * Gets the user from an Authentication object.
     * This is useful for controller methods that receive the Authentication object as a parameter.
     *
     * @param authentication The Authentication object
     * @return The User object or null if not found
     */
    public User getUserFromAuthentication(Authentication authentication) {
        if (authentication != null && authentication.isAuthenticated()) {
            String userId = authentication.getName();
            try {
                return userFacade.getUserByCriteria(userId, null, 0)
                        .stream()
                        .findFirst()
                        .orElse(null);
            } catch (Exception e) {
                log.error("Error retrieving user from authentication", e);
                return null;
            }
        }
        return null;
    }
}
