package com.eroses.external.society.service;

import com.eroses.external.society.mappers.PublicOfficerDao;
import com.eroses.external.society.model.PublicOfficer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class PublicOfficerReadDomainService {
    private final PublicOfficerDao publicOfficerDao;

    public PublicOfficer findByPaymentId(Long paymentId) {
        return publicOfficerDao.findByPaymentId(paymentId);
    }

    public List<PublicOfficer> findAll(Long societyId, Integer offset, Integer limit, String applicationStatusCode) {
        return publicOfficerDao.getAll(Map.of("societyId", societyId, "offset", offset, "limit", limit, "applicationStatusCode", applicationStatusCode));
    }

    public List<PublicOfficer> findAllWithBranch(Long societyId, Long branchId, Integer offset, Integer limit, String applicationStatusCode) {
        return publicOfficerDao.getAll(Map.of("societyId", societyId, "branchId", branchId, "offset", offset, "limit", limit, "applicationStatusCode", applicationStatusCode));
    }

    public Long countAll(Long societyId, String applicationStatusCode) {
        return publicOfficerDao.countAll(Map.of("societyId", societyId, "applicationStatusCode", applicationStatusCode));
    }

    public Long countAllWithBranch(Long societyId, Long branchId, String applicationStatusCode) {
        return publicOfficerDao.countAll(Map.of("societyId", societyId, "branchId", branchId, "applicationStatusCode", applicationStatusCode));
    }

    public PublicOfficer findById(Long id) {
        return publicOfficerDao.findById(id);
    }

    public Long countAllPending(String stateCode, Long ro, String applicationStatusCode) {
        return publicOfficerDao.countAllPending(Map.of("stateCode", stateCode, "ro", ro, "applicationStatusCode", applicationStatusCode));
    }

    public Long countAllPendingWithBranch(String stateCode, Long ro, String applicationStatusCode) {
        return publicOfficerDao.countAllBranchPending(Map.of("stateCode", stateCode, "ro", ro, "applicationStatusCode", applicationStatusCode));

    }

    public List<PublicOfficer> getAllPendingBranchPublicOfficerByCriteria(Integer applicationStatusCode, String stateCode, Long roId, String stateCodeFilter, String searchQuery, Integer offset, Integer limit) {

        return publicOfficerDao.listAllBranchPending(Map.of("searchQuery", searchQuery, "stateCodeFilter", stateCodeFilter, "applicationStatusCode", applicationStatusCode, "stateCode", stateCode, "ro", roId, "offset", offset, "limit", limit));
    }

    public Long countAllBranchPendingWithCriteria(Integer applicationStatusCode, String stateCode, Long roId, String stateCodeFilter, String searchQuery) {
        return publicOfficerDao.countAllBranchPendingWithCriteria(Map.of("searchQuery", searchQuery, "stateCodeFilter", stateCodeFilter, "applicationStatusCode", applicationStatusCode, "stateCode", stateCode, "ro", roId));
    }

    public List<PublicOfficer> getAllPendingByCriteria(Integer applicationStatusCode, String stateCode, Long roId, String categoryCode, String subCategoryCode, String societyName, Integer offset, Integer limit) {
        return publicOfficerDao.listAllSocietyPending(Map.of("applicationStatusCode", applicationStatusCode, "stateCode", stateCode, "ro", roId, "categoryCode", categoryCode, "subCategoryCode", subCategoryCode, "societyName", societyName, "limit", limit, "offset", offset));
    }

    public Long countAllPendingByCriteria(Integer applicationStatusCode, String stateCode, Long roId, String categoryCode, String subCategoryCode, String societyName) {
        return publicOfficerDao.countAllSocietyPendingWithCriteria(Map.of("applicationStatusCode", applicationStatusCode, "stateCode", stateCode, "ro", roId, "categoryCode", categoryCode, "subCategoryCode", subCategoryCode, "societyName", societyName));
    }

    public List<PublicOfficer> getAllPublicOfficerPendingApproval(int applicationStatusCode, List<Integer> daysAfterSubmissionList) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "daysAfterSubmissionList", daysAfterSubmissionList);
        return publicOfficerDao.getAllPublicOfficerPendingApproval(params);
    }

    public boolean existsOnGoingSocietyPublicOfficerApplication(List<Integer> applicationStatusCodes, Long societyId) {
        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null && !applicationStatusCodes.isEmpty()) params.put("applicationStatusCodes", applicationStatusCodes);
        if (societyId != null) params.put("societyId", societyId);
        return publicOfficerDao.existsOnGoingSocietyPublicOfficerApplication(params);
    }

    public boolean existsOnGoingBranchPublicOfficerApplication(List<Integer> applicationStatusCodes, Long branchId) {
        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null && !applicationStatusCodes.isEmpty()) params.put("applicationStatusCodes", applicationStatusCodes);
        if (branchId != null) params.put("branchId", branchId);
        return publicOfficerDao.existsOnGoingBranchPublicOfficerApplication(params);
    }
}
