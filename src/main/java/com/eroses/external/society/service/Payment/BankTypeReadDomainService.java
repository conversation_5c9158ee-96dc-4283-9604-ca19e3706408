package com.eroses.external.society.service.Payment;

import com.eroses.external.society.mappers.BankTypeDao;
import com.eroses.external.society.model.payment.BankType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class BankTypeReadDomainService {
    private final BankTypeDao bankTypeDao;

    public List<BankType> getAllBankTypes() {
        return bankTypeDao.findAll();
    }

    public BankType getBankTypeById(long id) {
        return bankTypeDao.findById(id);
    }

    public BankType getBankTypeByBankCode(String bankCode) {
        return bankTypeDao.findByBankCode(bankCode);
    }
}
