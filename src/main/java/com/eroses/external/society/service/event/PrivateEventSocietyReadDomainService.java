package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.PrivateEventSocietyDao;
import com.eroses.external.society.model.PrivateEventSociety;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class PrivateEventSocietyReadDomainService {
    private final PrivateEventSocietyDao privateEventSocietyDao;

    public List<PrivateEventSociety> findAll(){
        log.info("Finding all Private Event Society ");
        return privateEventSocietyDao.findAll();
    }

    public PrivateEventSociety findById(Long id){
        log.info("Finding Private Event Society by Id");
        return privateEventSocietyDao.findById(id);
    }

    public List<PrivateEventSociety> findByEventId(Long eventId){
        log.info("Finding Private Event Society by Event Id");
        return privateEventSocietyDao.findByEventId(eventId);
    }

    public List<PrivateEventSociety> findBySocietyId(Long societyId){
        log.info("Finding Private Event Society by society id ");
        return privateEventSocietyDao.findBySocietyId(societyId);
    }
}
