package com.eroses.external.society.service.training;

import com.eroses.external.society.mappers.TrainingCertificateDao;
import com.eroses.external.society.model.TrainingCertificate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class TrainingCertificateReadDomainService {

    private final TrainingCertificateDao trainingCertificateDao;
    private final TrainingS3Service trainingS3Service;

    public TrainingCertificate getTrainingCertificateById(Long id) {
        return trainingCertificateDao.findById(id);
    }

    public TrainingCertificate getTrainingCertificateByEnrollmentId(Long trainingEnrollmentId) {
        return trainingCertificateDao.findByEnrollmentId(trainingEnrollmentId);
    }

    public TrainingCertificate getTrainingCertificateByVerificationCode(String verificationCode) {
        return trainingCertificateDao.findByVerificationCode(verificationCode);
    }

    public List<TrainingCertificate> getAllTrainingCertificatesByUserId(Long userId) {
        return trainingCertificateDao.findAllByUserId(userId);
    }

    public Resource getCertificateFileResource(TrainingCertificate certificate) {
        if (certificate == null || certificate.getCertificatePath() == null) {
            throw new RuntimeException("Certificate or certificate path is null");
        }

        try {
            // Download the certificate from S3
            ByteArrayInputStream inputStream = trainingS3Service.downloadFile(certificate.getCertificatePath());

            // Create a resource from the input stream
            return new InputStreamResource(inputStream);
        } catch (Exception e) {
            log.error("Error downloading certificate from S3: {}", certificate.getCertificatePath(), e);
            throw new RuntimeException("Failed to download certificate: " + e.getMessage(), e);
        }
    }
}
