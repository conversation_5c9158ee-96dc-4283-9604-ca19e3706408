package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmCalendarDao;
import com.eroses.external.society.mappers.AdmCalendarStateDao;
import com.eroses.external.society.model.lookup.AdmCalendar;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmCalendarReadDomainService {
    private final AdmCalendarDao admCalendarDao;
    private final AdmCalendarStateDao admCalendarStateDao;

    public AdmCalendar findById(Long id) {
        AdmCalendar calendar = admCalendarDao.findById(id);
        if (calendar != null) {
            calendar.setStateIds(admCalendarStateDao.findStateIdsByCalendarId(id));
        }
        return calendar;
    }

    public List<AdmCalendar> getAll() {
        List<AdmCalendar> calendars = admCalendarDao.getAll();
        calendars.forEach(calendar ->
            calendar.setStateIds(admCalendarStateDao.findStateIdsByCalendarId(calendar.getId())));
        return calendars;
    }

    public List<AdmCalendar> getAllActive() {
        List<AdmCalendar> calendars = admCalendarDao.getAllActive();
        calendars.forEach(calendar ->
            calendar.setStateIds(admCalendarStateDao.findStateIdsByCalendarId(calendar.getId())));
        return calendars;
    }

    public List<AdmCalendar> getAll(String nameQuery, LocalDate startDate, LocalDate endDate, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        params.put("nameQuery", nameQuery);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("offset", offset);
        params.put("limit", limit);
        
        List<AdmCalendar> calendars = admCalendarDao.getAll(params);
        calendars.forEach(calendar ->
            calendar.setStateIds(admCalendarStateDao.findStateIdsByCalendarId(calendar.getId())));
        return calendars;
    }

    public Long countAll(String nameQuery, LocalDate startDate, LocalDate endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("nameQuery", nameQuery);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        
        return admCalendarDao.countAll(params);
    }

    public boolean hasOverlappingHoliday(String holidayType, LocalDate startDate, LocalDate endDate, Long excludeId) {
        return admCalendarDao.hasOverlappingHoliday(holidayType, startDate, endDate, excludeId);
    }
}
