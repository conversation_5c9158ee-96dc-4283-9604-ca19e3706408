package com.eroses.external.society.service.grant;

import com.eroses.external.society.mappers.grant.GrantTemplateDao;
import com.eroses.external.society.mappers.grant.GrantTemplateFieldDao;
import com.eroses.external.society.mappers.grant.GrantTemplateSocietyCategoryDao;
import com.eroses.external.society.model.enums.GrantTemplateStatusEnum;
import com.eroses.external.society.model.grant.GrantTemplate;
import com.eroses.external.society.model.grant.GrantTemplateField;
import com.eroses.external.society.model.grant.GrantTemplateSocietyCategory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class GrantTemplateWriteDomainService {

    private final GrantTemplateDao grantTemplateDao;
    private final GrantTemplateFieldDao grantTemplateFieldDao;
    private final GrantTemplateSocietyCategoryDao grantTemplateSocietyCategoryDao;

    @Transactional
    public Long createGrantTemplate(GrantTemplate grantTemplate) throws Exception {
        grantTemplateDao.create(grantTemplate);
        return grantTemplate.getId();
    }

    @Transactional
    public boolean updateGrantTemplate(GrantTemplate grantTemplate) throws Exception {
        return grantTemplateDao.update(grantTemplate);
    }

    @Transactional
    public Long createGrantTemplateField(GrantTemplateField grantTemplateField) throws Exception {
        grantTemplateFieldDao.create(grantTemplateField);
        return grantTemplateField.getId();
    }

    @Transactional
    public boolean updateGrantTemplateField(GrantTemplateField grantTemplateField) throws Exception {
        return grantTemplateFieldDao.update(grantTemplateField);
    }

    @Transactional
    public void deleteGrantTemplateField(Long id) throws Exception {
        grantTemplateFieldDao.delete(id);
    }

    @Transactional
    public void deleteGrantTemplateFieldsByTemplateId(Long grantTemplateId) throws Exception {
        grantTemplateFieldDao.deleteByGrantTemplateId(grantTemplateId);
    }

    @Transactional
    public Long createGrantTemplateSocietyCategory(GrantTemplateSocietyCategory grantTemplateSocietyCategory) throws Exception {
        grantTemplateSocietyCategoryDao.create(grantTemplateSocietyCategory);
        return grantTemplateSocietyCategory.getId();
    }

    @Transactional
    public void deleteGrantTemplateSocietyCategoriesByTemplateId(Long grantTemplateId) throws Exception {
        grantTemplateSocietyCategoryDao.deleteByGrantTemplateId(grantTemplateId);
    }

    @Transactional
    public boolean publishGrantTemplate(Long id, LocalDateTime publishDate) throws Exception {
        GrantTemplate grantTemplate = grantTemplateDao.findById(id);
        if (grantTemplate == null) {
            return false;
        }

        grantTemplate.setStatus(GrantTemplateStatusEnum.PUBLISHED.name());
        grantTemplate.setPublishDate(publishDate);
        grantTemplate.setModifiedDate(LocalDateTime.now());

        return grantTemplateDao.update(grantTemplate);
    }

    @Transactional
    public boolean setPrePublishDate(Long id, LocalDateTime prePublishDate) throws Exception {
        GrantTemplate grantTemplate = grantTemplateDao.findById(id);
        if (grantTemplate == null) {
            return false;
        }

        grantTemplate.setPrePublishDate(prePublishDate);
        grantTemplate.setModifiedDate(LocalDateTime.now());

        return grantTemplateDao.update(grantTemplate);
    }

    @Transactional
    public void deleteGrantTemplate(Long id) throws Exception {
        // First delete all related fields and society categories
        deleteGrantTemplateFieldsByTemplateId(id);
        deleteGrantTemplateSocietyCategoriesByTemplateId(id);

        // Then delete the template itself
        grantTemplateDao.delete(id);
    }
}
