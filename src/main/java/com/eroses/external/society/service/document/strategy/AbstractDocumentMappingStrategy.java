package com.eroses.external.society.service.document.strategy;

import com.eroses.external.society.model.AdmAddresses;
import com.eroses.external.society.model.CustomMultipartFile;
import com.eroses.external.society.model.DocumentTemplate;
import com.eroses.external.society.model.Society;
import com.eroses.external.society.service.DocumentTemplateReadDomainService;
import com.eroses.external.society.service.S3DomainService;
import com.eroses.external.society.service.SocietyReadDomainService;
import com.eroses.external.society.service.admin.AdmAddressesReadDomainService;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.service.utils.HtmlGeneratorService;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Abstract base class for document mapping strategies.
 * Provides common functionality for all document mapping strategies.
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractDocumentMappingStrategy implements DocumentMappingStrategy {

    // Core dependencies that won't change - injected via constructor
    protected final DocumentTemplateReadDomainService documentTemplateReadDomainService;
    protected final S3DomainService s3DomainService;
    protected final PdfService pdfService;
    protected final HtmlGeneratorService htmlGeneratorService;
    protected final UserFacade userFacade;
    protected final SocietyReadDomainService societyReadDomainService;

    // New dependencies - injected via field injection
    @Autowired
    protected AdmAddressesReadDomainService admAddressesReadDomainService;

    /**
     * Generates a PDF document from HTML content and uploads it to S3.
     * This method is kept for backward compatibility.
     *
     * @param processedHtml The processed HTML content
     * @param templateName  The name of the template
     * @param s3FolderPath  The S3 folder path
     * @return The URL of the uploaded document
     * @throws Exception If an error occurs during document generation
     */
    protected String generateAndUploadPdf(String processedHtml, String templateName, String s3FolderPath) throws Exception {
        User user = userFacade.me();
        byte[] binary = convertHtmlToPdfBytes(processedHtml);
        String key = templateName + "_" + user.getName() + "_" + LocalDateTime.now();

        // Create custom MultipartFile from the byte array as PDF
        MultipartFile multipartFile = new CustomMultipartFile(
                binary,
                key + ".pdf",
                "application/pdf"
        );

        return s3DomainService.uploadFile(multipartFile, s3FolderPath, key);
    }

    /**
     * Converts HTML content to PDF bytes.
     *
     * @param processedHtml The processed HTML content
     * @return The PDF as byte array
     * @throws Exception If an error occurs during PDF generation
     */
    protected byte[] convertHtmlToPdfBytes(String processedHtml) throws Exception {
        return Base64.getDecoder().decode(pdfService.generatePdfWithOpenHtmlToPdf(processedHtml));
    }

    /**
     * Gets the document template by code.
     *
     * @param templateCode The code of the template
     * @return The document template
     * @throws Exception If the template is not found
     */
    @Override
    public DocumentTemplate getDocumentTemplate(String templateCode) throws Exception {
        DocumentTemplate template = documentTemplateReadDomainService.getDocumentTemplateByCode(templateCode);
        if (template == null) {
            throw new Exception("Document template not found for code: " + templateCode);
        }
        return template;
    }

    /**
     * Prepares the data for mapping to the document.
     *
     * @param societyId        The ID of the society
     * @param additionalParams Additional parameters
     * @return A map of data for document mapping
     * @throws Exception If an error occurs during data preparation
     */
    protected abstract Map<String, String> prepareDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception;

    /**
     * Prepares table data for mapping to the document.
     * This method should be overridden by strategies that need to prepare table data.
     *
     * @param societyId        The ID of the society
     * @param additionalParams Additional parameters
     * @return A list of maps containing table data for document mapping
     * @throws Exception If an error occurs during data preparation
     */
    protected List<Map<String, String>> prepareTableDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {
        return Collections.emptyList();
    }

    protected static String getValueOrDefault(String value) {
        return value == null ? "" : value;
    }

    protected static String getValueOrDefaultStatement(String value) {
        return value == null ? "Tiada Maklumat" : value;
    }

    protected static Integer getValueOrDefaultStatement(Integer value) {
        return value == null ? 0 : value;
    }

    protected static String formatMonetaryValuesToString(BigDecimal value) {
        if (value == null || value.equals(BigDecimal.ZERO)) {
            return "0.00";
        }

        DecimalFormat df = new DecimalFormat("#,###.00");
        return df.format(value);
    }

    /**
     * Formats a LocalDate to a dd-MM-yyyy string or returns a default value if the date is null.
     *
     * @param date The LocalDate to format
     * @param defaultValue The default value to return if date is null
     * @return The formatted date string or the default value
     */
    protected static String formatDateOrDefault(LocalDate date, String defaultValue) {
        if (date == null) {
            return defaultValue;
        }
        return date.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
    }

    /**
     * Formats a LocalDate to Malay date format using Java localization.
     *
     * @param date the LocalDate object, can be null
     * @param defaultValue the default value to return if date is null
     * @return formatted Malay date string or default value
     */
    public static String formatToMalayDate(LocalDate date, String defaultValue) {
        if (date == null) {
            return defaultValue;
        }

        return formatDateToMalay(date);
    }

    /**
     * Overloaded method with default "-" value.
     *
     * @param date the LocalDate object, can be null
     * @return formatted Malay date string or "-" if null
     */
    public static String formatToMalayDate(LocalDate date) {
        return formatToMalayDate(date, "-");
    }

    /**
     * Formats a LocalDate to Malay date format using Java localization.
     *
     * @param date the LocalDate object to format (guaranteed not null)
     * @return formatted Malay date string (e.g., "16 Mei 2025")
     */
    private static String formatDateToMalay(LocalDate date) {
        // Using Malay locale (Malaysia)
        Locale malayLocale = new Locale("ms", "MY"); // Malay language, Malaysia country
        DateTimeFormatter malayFormatter = DateTimeFormatter.ofPattern("d MMMM yyyy", malayLocale);
        return date.format(malayFormatter);
    }

    /**
     * Formats a LocalDate to return the day of the week (e.g., Mon, Tue).
     *
     * @param date the LocalDate object, can be null
     * @param defaultValue the default value to return if date is null
     * @return the short day name or the default value if null
     */
    public static String formatDateToDay(LocalDate date, String defaultValue) {
        if (date == null) {
            return defaultValue;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("E", Locale.ENGLISH);
        return date.format(formatter);
    }

    /**
     * Formats a LocalDate to return the day of the week in Malay (e.g., Isn, Sel, Rab).
     *
     * @param date the LocalDate object, can be null
     * @param defaultValue the default value to return if date is null
     * @return the short Malay day name or the default value if null
     */
    public static String formatDateToMalayDay(LocalDate date, String defaultValue) {
        if (date == null) {
            return defaultValue;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("E", new Locale("ms", "MY"));
        return date.format(formatter);
    }

    /**
     * Overloaded method with default "-" value.
     *
     * @param date the LocalDate object, can be null
     * @return the short day name or "-" if null
     */
    public static String formatDateToDay(LocalDate date) {
        return formatDateToDay(date, "-");
    }

    /**
     * Overloaded method with default "-" value.
     *
     * @param date the LocalDate object, can be null
     * @return the short day name or "-" if null
     */
    public static String formatDateToMalayDay(LocalDate date) {
        return formatDateToMalayDay(date, "-");
    }

    /**
     * Formats a LocalDateTime by removing the "T" and using spaces.
     *
     * @param dateTime the LocalDateTime object, can be null
     * @param formatter the DateTimeFormatter to use, non-null
     * @param defaultValue the default value to return if dateTime is null
     * @return formatted date-time string (e.g., "2025-07-20 22:08:12"), or defaultValue if null
     */
    public static String formatDateTime(LocalDateTime dateTime,
                                        DateTimeFormatter formatter,
                                        String defaultValue) {
        if (dateTime == null) {
            return defaultValue;
        }
        return dateTime.format(formatter);
    }

    /**
     * Overloaded method using default formatter ("yyyy-MM-dd HH:mm:ss").
     *
     * @param dateTime the LocalDateTime object, can be null
     * @return formatted string or "-" if null
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        return formatDateTime(dateTime, DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss", Locale.ENGLISH), "-");
    }

    /**
     * Overloaded method specifying default value.
     *
     * @param dateTime the LocalDateTime object, can be null
     * @param defaultValue the default value if null
     * @return formatted string or defaultValue if null
     */
    public static String formatDateTime(LocalDateTime dateTime, String defaultValue) {
        return formatDateTime(dateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH), defaultValue);
    }

    @Override
    public String getFileName(DocumentTemplate documentTemplate, Long societyId, Map<String, Object> additionalParams) throws Exception {
        // Default implementation, can be overridden by subclasses
        Society society = societyReadDomainService.findById(societyId);
        return documentTemplate.getTemplateName() + " " + society.getSocietyNo();
    }

    /**
     * Retrieves the state name based on the state code.
     *
     * @param stateCode The state code
     * @return The state name or "-" if not found
     */
    protected String getStateName(String stateCode) {
        if (stateCode == null) return "";
        AdmAddresses result = admAddressesReadDomainService.findById(Long.valueOf(stateCode));
        return (result != null && result.getName() != null) ? result.getName().toUpperCase() : "";
    }
}