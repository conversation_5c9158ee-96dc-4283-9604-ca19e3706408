package com.eroses.external.society.service;

import com.eroses.config.cache.CacheNames;
import com.eroses.external.society.mappers.ConstitutionContentDao;
import com.eroses.external.society.model.ConstitutionContent;
import com.eroses.external.society.model.ConstitutionValue;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.utils.Assert;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ConstitutionContentWriteDomainService {
    private final ConstitutionContentReadDomainService constitutionContentReadDomainService;
    private final ConstitutionValueReadDomainService constitutionValueReadDomainService;
    private final ConstitutionValueWriteDomainService constitutionValueWriteDomainService;
    private final ConstitutionContentDao constitutioncontentDao;

//    @CacheEvict(value = CacheNames.CONSTITUTION_CONTENTS, allEntries = true)
    public Long create(ConstitutionContent constitutioncontent) throws Exception {
        log.info("Creating constitution content - evicting constitution contents cache");
        constitutioncontentDao.create(constitutioncontent);
        return constitutioncontent.getId();
    }

    public int update(ConstitutionContent constitutioncontent) throws Exception {
        constitutioncontentDao.update(constitutioncontent);
        return Math.toIntExact(constitutioncontent.getId());
    }

    public int update(ConstitutionContent constitutioncontent, User user) throws Exception {
        constitutioncontent.setModifiedBy(user.getId());
        constitutioncontentDao.update(constitutioncontent);
        return Math.toIntExact(constitutioncontent.getId());
    }

//    @CacheEvict(value = CacheNames.CONSTITUTION_CONTENTS, allEntries = true)
    public void updateStatus(List<Long> idList, String status, Integer appStatusCode, User user) {
        //TODO: return proper error if data not found
        if (appStatusCode == ApplicationStatusCode.LULUS.getCode() || appStatusCode == ApplicationStatusCode.AKTIF.getCode()) {
            appStatusCode = ApplicationStatusCode.AKTIF.getCode();
        }

        try {
            constitutioncontentDao.updateStatus(idList, status, appStatusCode, user.getId());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //also update status for constitutionvalues
        List<Long> constitutionValueIdList = idList.stream()
                .flatMap(contentId -> constitutionValueReadDomainService.findByConstitutionContentId(contentId)
                        .stream()
                        .map(ConstitutionValue::getId))
                .toList();

        if (!constitutionValueIdList.isEmpty()) {
            constitutionValueWriteDomainService.updateStatus(constitutionValueIdList, status, appStatusCode, user.getId());
        }
    }

//    @CacheEvict(value = CacheNames.CONSTITUTION_CONTENTS, allEntries = true)
    public Boolean hardDelete(Long constitutionContentId) {
        Boolean isDelete = constitutioncontentDao.hardDelete(constitutionContentId);
        Assert.isTrue(isDelete, "Pembuangan tidak berjaya");

        //check for constitution value
        List<ConstitutionValue> constitutionValueList = constitutionValueReadDomainService.findByConstitutionContentId(constitutionContentId);
        List<Long> ids = constitutionValueList.stream()
                .map(ConstitutionValue::getId)
                .collect(Collectors.toList());

        if (!ids.isEmpty()) {
            constitutionValueWriteDomainService.hardDelete(ids);
        }

        return isDelete;
    }

    public void handleConstitutionTypeChange(Long id) throws Exception {
        Map<String, Object> errorDelete = new HashMap<>();

        List<Long> idList = constitutionContentReadDomainService.findBySocietyId(id).stream()
//                .filter(content -> Status.MENUNGGU_KEPUTUSAN.getCode() == content.getApplicationStatusCode())
                .map(ConstitutionContent::getId)
                .toList();

        if (!idList.isEmpty()) {
            for (Long constitutionContentId : idList) {
                Boolean isDeleted = hardDelete(constitutionContentId);
                if (!isDeleted) {
                    errorDelete.put("id", id);
                }
            }

            if (!errorDelete.isEmpty()) {
                throw new Exception("Failed to delete constitution content with id: " + errorDelete.get("id"));
            }
        }
    }
}
