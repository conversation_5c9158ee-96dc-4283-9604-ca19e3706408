package com.eroses.external.society.service;

import com.eroses.external.society.model.Branch;
import com.eroses.external.society.mappers.BranchDao;
import com.eroses.external.society.model.BranchAmendment;
import com.eroses.external.society.model.enums.BranchAmendmentTypeEnum;
import com.eroses.external.society.model.enums.PaymentStatus;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.eroses.external.society.model.enums.ExpirationPeriod.BRANCH_APPLICATION_DIBENARKAN_PERIOD;

@Slf4j
@Service
@RequiredArgsConstructor
public class BranchWriteDomainService {
    private final BranchDao branchDao;

    public Long create(Branch branch) throws Exception {
        branchDao.create(branch);
        return branch.getId();
    }

    public Long update(Branch branch) throws Exception {
        branchDao.update(branch);
        return branch.getId();
    }

    public Long update(Branch branch, User user) throws Exception {
        branch.setModifiedBy(user.getId());
        branchDao.update(branch);
        return branch.getId();
    }

    public Boolean updateApplicationStatusCodeByIds(List<Long> ids, int applicationStatusCode) {
        Map<String, Object> params = Map.of(
                "ids", ids,
                "applicationStatusCode", applicationStatusCode
        );
        return branchDao.updateApplicationStatusCodeByIds(params);
    }

    public Boolean updateStatusCodeAndSubStatusCodeByIds(List<Long> ids, String status, String subStatusCode) {
        if(ids.isEmpty()) return false;
        Map<String, Object> params = Map.of(
                "ids", ids,
                "status", status,
                "subStatusCode", subStatusCode
        );
        return branchDao.updateStatusCodeAndSubStatusCodeByIds(params);
    }

    public Long updateBranchApprovalRo(Branch branch) {
        branchDao.updateBranchApprovalRo(branch);
        return branch.getId();
    }

    public Branch updateBranchApprovalDecision(Branch branch) {
        branchDao.updateBranchApprovalDecision(branch);
        return branch;
    }

    public Branch updateBranchExtension(Branch branch) {
        branchDao.updateBranchExtension(branch);
        return branch;
    }

    public String generateBranchNo(String societyNo) {
        String lastBranchNo = branchDao.findLastBranchNo(societyNo);
        String newBranchNo;
        if (lastBranchNo == null || lastBranchNo.isEmpty()) {
            newBranchNo = societyNo + "-000001";
        } else {
            String branchNoPart = lastBranchNo.replace(societyNo + "-", "");
            try {
                int branchNo = Integer.parseInt(branchNoPart);
                newBranchNo = societyNo + "-" + String.format("%06d", branchNo + 1);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid last branch number format: " + lastBranchNo, e);
            }
        }
        return newBranchNo;
    }

    public boolean updateBranchStatus(Branch branch, int paymentStatusCode, String paymentMethod) throws Exception {
        branch.setModifiedBy(0L);

        if(paymentStatusCode == PaymentStatus.PAID.getCode()){
            branch.setApplicationStatusCode(ApplicationStatusCode.DIBENARKAN.getCode());
            branch.setPaymentDate(LocalDate.now());
            branch.setPaymentMethod(paymentMethod);
            branch.setSubmissionDate(LocalDate.now());
            branch.setApplicationExpirationDate(LocalDateTime.now().plusDays(BRANCH_APPLICATION_DIBENARKAN_PERIOD.getDays()));
        }else{
            branch.setApplicationStatusCode(ApplicationStatusCode.BAYARAN_GAGAL.getCode());
        }

        return branchDao.update(branch);
    }

    public Branch updateRoDecision(Branch branch, User user, int applicationStatusCode, String status) throws Exception {

        // Update application status code and status based on RO decision
        branch.setApplicationStatusCode(applicationStatusCode);
        branch.setStatus(status);
        branch.setModifiedBy(user.getId());
        branchDao.update(branch);
        return branch;
    }

    public Branch updateNameAndAddress(Branch branch, BranchAmendment branchAmendment, Long userId) throws Exception {

        if (Objects.equals(branchAmendment.getAmendmentType(), BranchAmendmentTypeEnum.PINDAAN_NAMA_DAN_ALAMAT_CAWANGAN.getCode())) {
            setNameField(branch, branchAmendment);
            setAddressField(branch, branchAmendment);
        } else if (Objects.equals(branchAmendment.getAmendmentType(), BranchAmendmentTypeEnum.PINDAAN_NAMA_CAWANGAN.getCode())) {
            setNameField(branch, branchAmendment);
        } else if (Objects.equals(branchAmendment.getAmendmentType(), BranchAmendmentTypeEnum.PINDAAN_ALAMAT_CAWANGAN.getCode())) {
            setAddressField(branch, branchAmendment);
        }

        branch.setModifiedBy(userId);
        branchDao.update(branch);
        return branch;
    }

    private void setNameField(Branch branch, BranchAmendment branchAmendment) {
        branch.setName(branchAmendment.getBranchName());
    }

    private void setAddressField(Branch branch, BranchAmendment branchAmendment) {
        branch.setAddress(branchAmendment.getAddress());
        branch.setCountryCode(branchAmendment.getCountryCode());
        branch.setStateCode(branchAmendment.getStateCode());
        branch.setDistrictCode(branchAmendment.getDistrictCode());
        branch.setSmallDistrictCode(branchAmendment.getSmallDistrictCode());
        branch.setCityCode(branchAmendment.getCityCode());
        branch.setCity(branchAmendment.getCity());
        branch.setPostcode(branchAmendment.getPostcode());
        branch.setMailingAddress(branchAmendment.getMailingAddress());
        branch.setMailingStateCode(branchAmendment.getMailingStateId());
        branch.setMailingDistrictCode(branchAmendment.getMailingDistrictId());
        branch.setMailingCity(branchAmendment.getMailingCity());
        branch.setMailingPostcode(branchAmendment.getMailingPostcode());
    }
}
