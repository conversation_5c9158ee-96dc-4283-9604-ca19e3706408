package com.eroses.external.society.service.societyLiquidation;

import com.eroses.external.society.api.converter.input.societyLiquidation.LiquidationFeedbackApiInputConverter;
import com.eroses.external.society.dto.request.societyLiquidation.LiquidationFeedbackCreateRequest;
import com.eroses.external.society.dto.request.societyLiquidation.LiquidationFeedbackUpdateRequest;
import com.eroses.external.society.mappers.liquidation.LiquidationFeedbackDao;
import com.eroses.external.society.model.societyLiquidation.LiquidationFeedback;
import com.eroses.user.api.facade.UserFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class LiquidationFeedbackWriteDomainService {
    private final LiquidationFeedbackDao feedbackDao;
    private final LiquidationFeedbackApiInputConverter converter;
    private final UserFacade userFacade;

    public Long create(LiquidationFeedbackCreateRequest request) throws Exception {
        LiquidationFeedback liquidationFeedback = converter.toModel(request);

        liquidationFeedback.setIcNo(userFacade.me().getIdentificationNo());
        liquidationFeedback.setCreatedBy(userFacade.me().getId());

        feedbackDao.create(liquidationFeedback);
        return liquidationFeedback.getId();
    }

    public Long update (LiquidationFeedbackUpdateRequest request) throws Exception {
        LiquidationFeedback liquidationFeedback = converter.toModel(request);

        liquidationFeedback.setModifiedBy(userFacade.me().getId());

        feedbackDao.update(liquidationFeedback);
        return liquidationFeedback.getId();
    }
}