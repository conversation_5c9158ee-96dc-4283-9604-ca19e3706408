package com.eroses.external.society.service.grant;

import com.eroses.external.society.mappers.grant.GrantApplicationDao;
import com.eroses.external.society.mappers.grant.GrantQueryDao;
import com.eroses.external.society.model.enums.GrantApplicationStatusEnum;
import com.eroses.external.society.model.enums.GrantQueryStatusEnum;
import com.eroses.external.society.model.grant.GrantApplication;
import com.eroses.external.society.model.grant.GrantQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class GrantQueryWriteDomainService {

    private final GrantQueryDao grantQueryDao;
    private final GrantApplicationDao grantApplicationDao;

    @Transactional
    public Long createGrantQuery(GrantQuery grantQuery) throws Exception {
        grantQueryDao.create(grantQuery);

        // Update the application status to QUERIED
        GrantApplication grantApplication = grantApplicationDao.findById(grantQuery.getGrantApplicationId());
        if (grantApplication != null) {
            grantApplication.setStatus(GrantApplicationStatusEnum.QUERIED.name());
            grantApplication.setModifiedBy(grantQuery.getCreatedBy());
            grantApplication.setModifiedDate(LocalDateTime.now());
            grantApplicationDao.update(grantApplication);
        }

        return grantQuery.getId();
    }

    @Transactional
    public boolean respondToQuery(Long queryId, String responseText, Long userId) throws Exception {
        GrantQuery grantQuery = grantQueryDao.findById(queryId);
        if (grantQuery == null) {
            return false;
        }

        grantQuery.setResponseText(responseText);
        grantQuery.setResponseDate(LocalDateTime.now());
        grantQuery.setStatus(GrantQueryStatusEnum.RESPONDED.name());
        grantQuery.setModifiedBy(userId);
        grantQuery.setModifiedDate(LocalDateTime.now());

        boolean updated = grantQueryDao.update(grantQuery);

        if (updated) {
            // Update the application status back to SUBMITTED
            GrantApplication grantApplication = grantApplicationDao.findById(grantQuery.getGrantApplicationId());
            if (grantApplication != null) {
                grantApplication.setStatus(GrantApplicationStatusEnum.SUBMITTED.name());
                grantApplication.setModifiedBy(userId);
                grantApplication.setModifiedDate(LocalDateTime.now());
                grantApplicationDao.update(grantApplication);
            }
        }

        return updated;
    }

    @Transactional
    public void deleteGrantQuery(Long id) throws Exception {
        grantQueryDao.delete(id);
    }
}
