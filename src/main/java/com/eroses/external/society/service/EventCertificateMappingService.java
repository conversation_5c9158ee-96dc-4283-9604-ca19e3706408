package com.eroses.external.society.service;

import com.eroses.external.society.mappers.EventCertificateDao;
import com.eroses.external.society.model.*;
import com.eroses.external.society.model.document.SijilPenglibatan;
import com.eroses.external.society.model.enums.DocumentTypeEnum;
import com.eroses.external.society.model.enums.DocumentTemplateEnum;
import com.eroses.external.society.service.event.EventAttendeesReadDomainService;
import com.eroses.external.society.service.event.EventAttendeesWriteDomainService;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.user.api.facade.UserFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.jodconverter.core.office.OfficeManager;
import org.jodconverter.local.LocalConverter;
import org.jodconverter.local.office.LocalOfficeManager;
import org.springframework.core.io.ClassPathResource;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.eroses.external.society.model.enums.EventCertificateTemplate.getCertificateTemplate;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventCertificateMappingService {
    private final EventCertificateDao eventCertificateDao;
    private static final String CERTIFICATE_FOLDER = "generated-event-certificates/";
    private final EventAttendeesWriteDomainService eventAttendeesWriteDomainService;
    private final EventAttendeesReadDomainService eventAttendeesReadDomainService;
    private final DocumentTemplateReadDomainService documentTemplateReadDomainService;
    private final DocumentWriteDomainService documentWriteDomainService;
    private final PdfService pdfService;
    private final S3DomainService s3DomainService;
    private final UserFacade authFacade;

    public String generateSijilPenglibatan(EventCertTemplateConfiguration certTemplateConfiguration, Long eventId, String identificationNo, boolean isGenerate) {
        try {
            byte[] pdfBytes = new byte[0];
            String url = "";

            if (isGenerate) {

                switch (getCertificateTemplate(certTemplateConfiguration.getTemplateCode())) {
                    case SIJIL_PENGLIBATAN -> {
                        EventCertificate eventCertificateDto = eventCertificateDao.getEventAttendeeShow(eventId, identificationNo);
                        EventAttendees eventAttendees = eventAttendeesReadDomainService.findByEventIdAndIdentificationNo(identificationNo, eventId);
                        if (Objects.isNull(eventCertificateDto)) {
                            return null;
                        }

                        String certificateName = generateCertNamePdf(certTemplateConfiguration.getOutputFileName(), eventCertificateDto.getEventName(), identificationNo);
//                        if ((Objects.nonNull(eventAttendees.getEventCertificateName()))) {
//                            return getExistingCertificate(certificateName, identificationNo);
//                        }

                        Map<String, String> mapFields = new HashMap<>();
                        mapFields = SijilPenglibatan.getMapFields(eventCertificateDto);
//                        XWPFDocument document = processDocument(mapFields, certTemplateConfiguration.getTemplateName());
//
//                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//                        document.write(outputStream);
//                        pdfBytes = convertDocxToPdf(document);
                        DocumentTemplate sijilTemplate = documentTemplateReadDomainService.getDocumentTemplateByCode(DocumentTemplateEnum.SIJIL_ACARA.getCode());
                        String htmlContent = sijilTemplate.getHtmlContent();
                        mapFields.put("{{backgroundImage}}", sijilTemplate.getByteContent());

                        for (Map.Entry<String, String> entry : mapFields.entrySet()) {
                            htmlContent = htmlContent.replace(entry.getKey(), entry.getValue());
                        }

                        byte[] binary = pdfService.generatePdfFromHtml(htmlContent);
                        String folderName = DocumentTypeEnum.EVENT_CERT.getType();

                        // Create custom MultipartFile from the byte array as PDF
                        MultipartFile multipartFile = new CustomMultipartFile(
                                binary,
                                certificateName,
                                "application/pdf"  // Set content type to PDF
                        );

                        url = s3DomainService.uploadFile(multipartFile, folderName, certificateName);
                        Document document = new Document();
                        document.setName(certificateName);
                        document.setUrl(url);
                        document.setType(DocumentTypeEnum.EVENT_CERT.getType());
                        document.setEventId(eventId);
                        document.setIcNo(identificationNo);
                        document.setStatus(1L);
                        document.setCreatedBy(authFacade.me().getId());
                        documentWriteDomainService.registerFileInDb(document);

                        try {
                            saveCertificate(binary, certificateName, identificationNo, eventAttendees);
                        } catch (Exception ex) {
                            // Log the error but continue execution since the certificate is already in S3 and DB
                            log.error("Failed to save certificate to local filesystem: {}", ex.getMessage(), ex);
                        }
                    }
                    case null -> {
                    }
                }

            }
            // 4. Return PDF as API response


//            return new ApiResponse<InputStreamResource>("Success", 200, "Certificate generated successfully", resource, LocalDateTime.now());
            return url;


        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "Tiada maklumat tambahan tersedia.";
            throw new RuntimeException("Ralat pangkalan data semasa mendapatkan sijil acara: " + getRootCauseMessage(e), e);
//            throw new ApiResponse<>("Error", 500, "Database error occurred while getting event feedback question: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (Exception e) {
            throw new RuntimeException("Ralat tidak dijangka semasa menjana sijil", e);
        }
    }


    private byte[] convertDocxToPdf(XWPFDocument document) throws Exception {
        ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();

        // Save DOCX to a temporary file
        File tempDocx = File.createTempFile("certificate", ".docx");
        FileOutputStream fos = new FileOutputStream(tempDocx);
        document.write(fos);
        fos.close();

        // Convert DOCX to PDF using LibreOffice
        File tempPdf = File.createTempFile("certificate", ".pdf");
        OfficeManager officeManager = LocalOfficeManager.builder().install().build();
        officeManager.start();

        LocalConverter.make(officeManager)
                .convert(tempDocx)
                .to(tempPdf)
                .execute();

        officeManager.stop();

        // Read the generated PDF file into a byte array
        FileInputStream fis = new FileInputStream(tempPdf);
        byte[] pdfBytes = fis.readAllBytes();
        fis.close();

        return pdfBytes;
    }

    public XWPFDocument processDocument(Map<String, String> replacements, String templateName) {
        try {
            // Load the template from classpath
            ClassPathResource resource = new ClassPathResource("template/pdf/" + templateName);
            InputStream documentStream = resource.getInputStream();

            XWPFDocument doc = new XWPFDocument(documentStream);

            // Process paragraphs
            for (XWPFParagraph paragraph : doc.getParagraphs()) {
                replaceParagraphContent(paragraph, replacements);
            }

            return doc;
        } catch (Exception e) {
            throw new RuntimeException("Failed to process document: " + e.getMessage(), e);
        }
    }

    private void replaceParagraphContent(XWPFParagraph paragraph, Map<String, String> replacements) {
        String paragraphText = paragraph.getText(); // Get full paragraph text
        if (paragraphText == null || paragraphText.isEmpty()) return;

        boolean foundPlaceholder = false;

        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            if (paragraphText.contains(entry.getKey())) {
                paragraphText = paragraphText.replace(entry.getKey(), entry.getValue());
                foundPlaceholder = true;
            }
        }

        // If we replaced something, rewrite the runs
        if (foundPlaceholder) {
            for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                paragraph.removeRun(i); // Remove old runs
            }
            XWPFRun newRun = paragraph.createRun();
            newRun.setText(paragraphText); // Set new merged text
        }
    }

    public void saveCertificate(byte[] pdfBytes, String certificateName, String identificationNo, EventAttendees attendees) throws IOException {
        // Define the directory where the PDF will be stored
        String folderPath = getFolderPath(identificationNo); // Relative path inside project
        File folder = new File(folderPath);

        // Create directory if it doesn't exist
        if (!folder.exists()) {
            folder.mkdirs(); // Creates parent directories if needed
        }

        // Define the file name with timestamp to prevent overwriting

        File outputFile = new File(folder, certificateName);

        // Write the byte array to the file
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            fos.write(pdfBytes);
            log.info("Save certificate name");
            attendees.setEventCertificateName(certificateName);
            eventAttendeesWriteDomainService.update(attendees);
        }

    }

    public byte[] getExistingCertificate(String certName, String identificationNo) throws IOException {
        String fileName = getFolderPath(identificationNo) + certName ;
        File file = new File(fileName);

        if (file.exists()) {
            log.debug("Existing certificate found: " + file.getAbsolutePath());
            return Files.readAllBytes(file.toPath());
        } else {
            log.debug("No existing certificate found.");
            return null;
        }
    }

    private String generateCertNamePdf(String outputFileName, String eventName, String identificationNo) {
        return outputFileName + "_" + eventName + "_" + identificationNo + ".pdf";
    }

    private String getFolderPath(String identificationNo) {
        return CERTIFICATE_FOLDER + identificationNo + "/";
    }


    private String getRootCauseMessage(Throwable e) {
        Throwable rootCause = e.getCause();
        return (rootCause != null) ? rootCause.getMessage() : "Tiada maklumat tambahan tersedia.";
    }
}
