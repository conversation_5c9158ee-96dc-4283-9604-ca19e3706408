package com.eroses.external.society.service;

import com.eroses.external.society.mappers.PrincipalSecretaryRoApprovalDao;
import com.eroses.external.society.model.PrincipalSecretaryRoApproval;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class PrincipalSecretaryRoApprovalReadDomainService {
    private final PrincipalSecretaryRoApprovalDao principalSecretaryRoApprovalDao;

    public List<PrincipalSecretaryRoApproval> getAll() {
        return principalSecretaryRoApprovalDao.getAll();
    }

    public List<PrincipalSecretaryRoApproval> getAll(Map<String, Object> params) {
        return principalSecretaryRoApprovalDao.search(params);
    }
}
