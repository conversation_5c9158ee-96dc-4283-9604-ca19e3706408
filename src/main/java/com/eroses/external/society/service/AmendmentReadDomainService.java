package com.eroses.external.society.service;

import com.eroses.external.society.dto.response.appeal.GetUserSocietiesForAppealResponse;
import com.eroses.external.society.dto.response.roDecision.GetAllDecisionRecordAmendmentResponse;
import com.eroses.external.society.dto.response.GetRoAmendmentResponse;
import com.eroses.external.society.dto.response.roDecision.GetAllPendingAmendmentResponse;
import com.eroses.external.society.mappers.AmendmentDao;
import com.eroses.external.society.model.Amendment;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class AmendmentReadDomainService {
    private final AmendmentDao amendmentDao;

    public Amendment findById(Long id) {
        Amendment amendment = amendmentDao.findById(id);
//        Assert.isNull(amendment, "Amendment is not found.");

        return amendment;
    }

    public Boolean isExists(Long id) {
        Amendment amendment = amendmentDao.findById(id);
        return amendment != null;
    }

    public Amendment getByPaymentId(Long paymentId) {
        return amendmentDao.getByPaymentId(paymentId);
    }

    public List<Amendment> getAllAmendments() {
        return amendmentDao.findAll();
    }

    public List<Amendment> searchAmendments(String amendmentName, int offset, int limit, int status) {
        Map<String, Object> params = new HashMap<>();
        params.put("amendmentName", amendmentName);
        params.put("offset", offset);
        params.put("limit", limit);
        params.put("statusCode", status);

        return amendmentDao.searchAmendments(params);
    }

    public long countSearchedAmendments(Integer statusCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("statusCode", statusCode);

        return amendmentDao.countSearchedAmendments(params);
    }
//commented because not used
//    public Page<Amendment> findAmendmentsInvolved(Map<String, Object> params) {
//        return amendmentDao.findByUserInvolved(params);
//    }
//
//    public Page<Amendment> findAmendmentsCreated(Map<String, Object> params) {
//        return amendmentDao.findByUserCreated(params);
//    }

    public List<Amendment> getAmendmentByParam(Map<String, Object> param) {
        return amendmentDao.findByParam(param);
    }

    public List<Amendment> getAmendmentByClauseType(Map<String, Object> param) {
        return amendmentDao.findByClauseType(param);
    }

    public Long countAmendments(Map<String, Object> param) {
        return amendmentDao.countAmendments(param);
    }

    public List<GetRoAmendmentResponse> getRoAmendmentsByUser(Map<String, Object> param) {
        return amendmentDao.getRoAmendmentsByUser(param);
    }

    public Long countRoAmendmentsByUser(Map<String, Object> param) {
        return amendmentDao.countRoAmendmentsByUser(param);
    }

    public List<GetAllPendingAmendmentResponse> getAllPendingAmendments(Integer isQuery, List<Integer> applicationStatusCodes, String stateCodeQuery, Long roIdQuery, String societyName, Long societyId, String categoryCodeJppm, String subCategoryCode, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();

        if (isQuery != null) params.put("isQuery", isQuery);
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCodeQuery != null && !stateCodeQuery.isEmpty()) params.put("stateCode", stateCodeQuery);
        if (roIdQuery != null) params.put("ro", roIdQuery);
        if (categoryCodeJppm != null && !categoryCodeJppm.isEmpty()) params.put("categoryCodeJppm", categoryCodeJppm);
        if (subCategoryCode != null && !subCategoryCode.isEmpty()) params.put("subCategoryCode", subCategoryCode);
        if (societyId != null) params.put("societyId", societyId);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        params.put("offset", offset);
        params.put("limit", limit);

        return amendmentDao.getAllPendingAmendments(params);
    }

    public Long countAllPending(Integer isQuery, List<Integer> applicationStatusCodes, String stateCodeQuery, Long roIdQuery, String societyName, Long societyId, String categoryCodeJppm, String subCategoryCode) {
        Map<String, Object> params = new HashMap<>();

        if (isQuery != null) params.put("isQuery", isQuery);
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCodeQuery != null && !stateCodeQuery.isEmpty()) params.put("stateCode", stateCodeQuery);
        if (roIdQuery != null) params.put("ro", roIdQuery);
        if (categoryCodeJppm != null && !categoryCodeJppm.isEmpty()) params.put("categoryCodeJppm", categoryCodeJppm);
        if (subCategoryCode != null && !subCategoryCode.isEmpty()) params.put("subCategoryCode", subCategoryCode);
        if (societyId != null) params.put("societyId", societyId);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);

        return amendmentDao.countAllPending(params);
    }

    public List<GetAllDecisionRecordAmendmentResponse> getAllDecisionRecordAmendment(Long stateCode, String decision, String searchQuery, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();

        if (stateCode != null) params.put("stateCode", stateCode);
        if (decision != null && !decision.isEmpty()) params.put("decision", decision);
        if (searchQuery != null && !searchQuery.isEmpty()) params.put("searchQuery", searchQuery);
        params.put("offset", offset);
        params.put("limit", limit);

        return amendmentDao.getAllDecisionRecordAmendment(params);
    }

    public Long countAllDecisionRecordAmendment(Long stateCode, String decision, String searchQuery) {
        Map<String, Object> params = new HashMap<>();

        if (stateCode != null) params.put("stateCode", stateCode);
        if (decision != null && !decision.isEmpty()) params.put("decision", decision);
        if (searchQuery != null && !searchQuery.isEmpty()) params.put("searchQuery", searchQuery);

        return amendmentDao.countAllDecisionRecordAmendment(params);
    }

    public List<GetUserSocietiesForAppealResponse> findAmendmentForAppeal(Map<String, Object> param) {
        return amendmentDao.findAmendmentForAppeal(param);
    }

    public List<Amendment> getAllAmendmentPendingApproval(int applicationStatusCode, int daysAfterSubmission) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "daysAfterSubmission", daysAfterSubmission);
        return amendmentDao.getAllAmendmentPendingApproval(params);
    }

//    public int countRegisteredToday() {
//        return amendmentDao.countRegisteredToday();
//    }
}
