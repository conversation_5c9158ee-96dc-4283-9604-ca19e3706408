package com.eroses.external.society.service.societyCancellation;

import com.eroses.external.society.mappers.SocietyCancellationRevertDao;
import com.eroses.external.society.model.societyCancellation.SocietyCancellationRevert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyCancellationRevertReadDomainService {
    private final SocietyCancellationRevertDao societyCancellationRevertDao;

    public SocietyCancellationRevert findById(Long id) {
        return societyCancellationRevertDao.findById(id);
    }

    public List<SocietyCancellationRevert> findAll() {
        return societyCancellationRevertDao.findAll();
    }

    public List<SocietyCancellationRevert> findBySocietyId(Long societyId) {
        return societyCancellationRevertDao.findBySocietyId(societyId);
    }

    public SocietyCancellationRevert findBySocietyNo(String societyNo) {
        return societyCancellationRevertDao.findBySocietyNo(societyNo);
    }
    
    public List<SocietyCancellationRevert> findBySocietyCancellationId(Long societyCancellationId) {
        return societyCancellationRevertDao.findBySocietyCancellationId(societyCancellationId);
    }

    public List<SocietyCancellationRevert> findByParam(Map<String, Object> params) {
        return societyCancellationRevertDao.findByParam(params);
    }

    public Long countByParam(Map<String, Object> params) {
        return societyCancellationRevertDao.countByParam(params);
    }

    public List<Long> getAllPendingToProcessSocietyCancellationRevertId() {
        Map<String, Object> params = new HashMap<>();
        params.put("isReverted", false);
        return societyCancellationRevertDao.getAllPendingToProcessSocietyCancellationRevertId(params);
    }
}