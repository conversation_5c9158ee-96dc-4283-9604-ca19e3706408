package com.eroses.external.society.service.document.strategy.impl;

import com.eroses.external.society.dto.response.branchSecretary.BranchSecretarySlipResponse;
import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.*;
import com.eroses.external.society.service.*;
import com.eroses.external.society.service.document.strategy.AbstractDocumentMappingStrategy;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.service.utils.HtmlGeneratorService;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;

/**
 * Strategy implementation for Slip Pengesahan Pembaharuan Setiausaha Cawangan document.
 */
@Slf4j
@Component
public class SlipPengesahanPembaharuanSetiausahaCawanganStrategy extends AbstractDocumentMappingStrategy {

    private final BranchSecretaryReadDomainService branchSecretaryReadDomainService;
    private final BranchReadDomainService branchReadDomainService;

    public SlipPengesahanPembaharuanSetiausahaCawanganStrategy(
            DocumentTemplateReadDomainService documentTemplateReadDomainService,
            S3DomainService s3DomainService,
            PdfService pdfService,
            HtmlGeneratorService htmlGeneratorService,
            UserFacade userFacade,
            SocietyReadDomainService societyReadDomainService,
            BranchSecretaryReadDomainService branchSecretaryReadDomainService,
            BranchReadDomainService branchReadDomainService) {
        super(documentTemplateReadDomainService, s3DomainService, pdfService, htmlGeneratorService, userFacade, societyReadDomainService);
        this.branchSecretaryReadDomainService = branchSecretaryReadDomainService;
        this.branchReadDomainService = branchReadDomainService;
    }

    @Override
    public String mapDataToDocument(String templateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        DocumentTemplate template = getDocumentTemplate(templateCode);
        String htmlTemplate = template.getHtmlContent();

        Map<String, String> mapFields = prepareDataForMapping(societyId, additionalParams);
        String processedHtml = htmlGeneratorService.processConditionals(htmlTemplate, mapFields);
        return htmlGeneratorService.mapToHtml(processedHtml, mapFields);
    }

    @Override
    protected Map<String, String> prepareDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {

        //Get Additional Params
        Long newSecretaryBranchId = (Long) additionalParams.get("newSecretaryBranchId");
        BranchSecretarySlipResponse secretarySlip = branchSecretaryReadDomainService.getSlipData(newSecretaryBranchId);

        Society society = societyReadDomainService.findById(societyId);
        Branch branch = branchReadDomainService.getBranchById(secretarySlip.getBranchId());
        User applicant = userFacade.getUserById(secretarySlip.getApplicantUserId());
        return getMapFields(branch, applicant, society, secretarySlip);
    }

    @Override
    public boolean canHandle(String templateCode) {
        return DocumentTemplateEnum.SLIP_PENGESAHAN_PEMBAHARUAN_SETIAUSAHA_CAWANGAN.getCode().equals(templateCode);
    }

    private static Map<String, String> getMapFields(Branch branch, User applicant,
                                                    Society society, BranchSecretarySlipResponse secretarySlip) {
        Map<String, String> mapFields = new HashMap<>();
        mapFields.put("{{APPLICANT_NAME}}", getValueOrDefault(applicant.getName()).toUpperCase());
        mapFields.put("{{APPLICANT_ID_NO}}", getValueOrDefault(applicant.getIdentificationNo()).toUpperCase());
        mapFields.put("{{SOCIETY_NAME}}", getValueOrDefault(society.getSocietyName()).toUpperCase());
        mapFields.put("{{BRANCH_NAME}}", getValueOrDefault(branch.getName()).toUpperCase());
        mapFields.put("{{BRANCH_SECRETARY_NAME}}", getValueOrDefault(secretarySlip.getBranchSecretaryName()).toUpperCase());
        mapFields.put("{{BRANCH_SECRETARY_ID_NO}}", getValueOrDefault(secretarySlip.getBranchSecretaryIdNo()).toUpperCase());
        mapFields.put("{{BRANCH_NO}}", getValueOrDefault(branch.getBranchNo()).toUpperCase());
        mapFields.put("{{APPLICATION_DAY}}", formatDateToMalayDay(LocalDate.from(secretarySlip.getApplicationDateTime())));
        mapFields.put("{{APPLICATION_DATE_TIME}}", formatDateTime(secretarySlip.getApplicationDateTime()));
        return mapFields;
    }
}