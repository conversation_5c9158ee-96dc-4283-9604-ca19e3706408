package com.eroses.external.society.service;

import com.eroses.external.society.mappers.RoApprovalDao;
import com.eroses.external.society.model.RoApproval;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class RoApprovalReadDomainService {
    private final RoApprovalDao roApprovalDao;

    public Boolean isExists(Long id) {
        RoApproval roApproval = roApprovalDao.findById(id);
        return roApproval != null;
    }

    public RoApproval findByBranchId(Long id) {
        return roApprovalDao.findByBranchId(id);
    }

    public List<RoApproval> getAllRoApprovalByCriteria(Long societyId, Long branchId, Long amendmentId,
                                                       Long liquidationId, Long appealId,
                                                       Long principalSecretaryId, Long societyNonCitizenCommitteeId,
                                                       Long extensionTimeId, Long branchAmendmentId,
                                                       Long propertyOfficerApplicationId, Long publicOfficerId,
                                                       String type, Integer decision) {
        //Add more criteria if needed
        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        if (amendmentId != null) params.put("amendmentId", amendmentId);
        if (liquidationId != null) params.put("liquidationId", liquidationId);
        if (appealId != null) params.put("appealId", appealId);
        if (principalSecretaryId != null) params.put("principalSecretaryId", principalSecretaryId);
        if (societyNonCitizenCommitteeId != null) params.put("societyNonCitizenCommitteeId", societyNonCitizenCommitteeId);
        if (extensionTimeId != null) params.put("extensionTimeId", extensionTimeId);
        if (branchAmendmentId != null) params.put("branchAmendmentId", branchAmendmentId);
        if (propertyOfficerApplicationId != null) params.put("propertyOfficerApplicationId", propertyOfficerApplicationId);
        if (publicOfficerId != null) params.put("publicOfficerId", publicOfficerId);
        if (type != null) params.put("type", type);
        if (decision != null) params.put("decision", decision);
        return roApprovalDao.findAllByCriteria(params);
    }

    public Long countGetAllRoApproval(Long societyId) {
        return roApprovalDao.countFindAll(societyId);
    }

    public RoApproval findByParamForAppeal(Map<String, Object> param) {
        return roApprovalDao.findByParamForAppeal(param);
    }

    public RoApproval findRoApprovalByModuleId(Map<String, Object> params) {
        return roApprovalDao.findRoApprovalByModuleId(params);
    }
}
