package com.eroses.external.society.service;

import com.eroses.external.society.mappers.StatementIdSeqDao;
import com.eroses.user.api.facade.UserFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatementIdSeqDomainService {
    private final StatementIdSeqDao statementIdSeqDao;
    private final UserFacade authFacade;

    public Long getNextStatementId() throws Exception {
        Long userId = authFacade.me().getId();

        // Fetch the current value
        Long nextVal = statementIdSeqDao.getNextVal();

        // Increment the value
        statementIdSeqDao.incrementNextVal(userId);

        return nextVal;
    }
}
