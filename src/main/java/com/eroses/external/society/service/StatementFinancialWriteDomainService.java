package com.eroses.external.society.service;

import com.eroses.external.society.mappers.StatementFinancialDao;
import com.eroses.external.society.model.StatementFinancial;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatementFinancialWriteDomainService {
    private final StatementFinancialDao statementFinancialDao;

    public boolean create(StatementFinancial statementFinancial) throws Exception {
        return statementFinancialDao.create(statementFinancial);
    }

    public boolean update(StatementFinancial statementFinancial) throws Exception {
        return statementFinancialDao.update(statementFinancial);
    }

    public Boolean deleteStatement(Map<String, Object> params) {
        return statementFinancialDao.deleteStatement(params);
    }
}
