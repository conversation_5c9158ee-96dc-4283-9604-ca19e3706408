package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmPositionJppmDao;
import com.eroses.external.society.model.AdmPositionJppm;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Service
@RequiredArgsConstructor
public class AdmPositionJppmReadDomainService {

    private final AdmPositionJppmDao admPositionJppmDao;

    public AdmPositionJppm findById(Long id) {
        return admPositionJppmDao.findById(id);
    }

    public List<AdmPositionJppm> getAdmPositionJppm() {
        return admPositionJppmDao.findAll();
    }

    public List<AdmPositionJppm> getAll() {
        return admPositionJppmDao.getAll();
    }

    public List<AdmPositionJppm> getAllActive() {
        return admPositionJppmDao.getAllActive();
    }

    public List<AdmPositionJppm> getAll(String nameQuery, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        params.put("nameQuery", nameQuery);
        params.put("offset", offset);
        params.put("limit", limit);

        return admPositionJppmDao.getAll(params);
    }

    public Long countAll(String nameQuery) {
        Map<String, Object> params = new HashMap<>();
        params.put("nameQuery", nameQuery);

        return admPositionJppmDao.countAll(params);
    }

    public boolean existsByGradeAndName(String grade, String name) {
        Map<String, Object> params = new HashMap<>();
        params.put("grade", grade);
        params.put("name", name);
        return admPositionJppmDao.existsByGradeAndName(params);
    }

    public boolean existsByGradeAndNameExcludingId(String grade, String name, Long id) {
        Map<String, Object> params = new HashMap<>();
        params.put("grade", grade);
        params.put("name", name);
        params.put("id", id);
        return admPositionJppmDao.existsByGradeAndNameExcludingId(params);
    }
}
