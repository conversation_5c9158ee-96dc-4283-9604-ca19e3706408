package com.eroses.external.society.service.grant;

import com.eroses.external.society.mappers.grant.GrantReportAttachmentDao;
import com.eroses.external.society.mappers.grant.GrantReportDao;
import com.eroses.external.society.model.grant.GrantReport;
import com.eroses.external.society.model.grant.GrantReportAttachment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class GrantReportReadDomainService {
    
    private final GrantReportDao grantReportDao;
    private final GrantReportAttachmentDao grantReportAttachmentDao;
    
    public List<GrantReport> findAll(Long grantApplicationId, String status, Boolean allowPublicDisplay, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "grantApplicationId", grantApplicationId,
                "status", status,
                "allowPublicDisplay", allowPublicDisplay,
                "offset", offset,
                "limit", limit
        );
        return grantReportDao.findAll(params);
    }
    
    public Long countAll(Long grantApplicationId, String status, Boolean allowPublicDisplay) {
        Map<String, Object> params = Map.of(
                "grantApplicationId", grantApplicationId,
                "status", status,
                "allowPublicDisplay", allowPublicDisplay
        );
        return grantReportDao.countAll(params);
    }
    
    public GrantReport findById(Long id) {
        return grantReportDao.findById(id);
    }
    
    public GrantReport findByGrantApplicationId(Long grantApplicationId) {
        return grantReportDao.findByGrantApplicationId(grantApplicationId);
    }
    
    public List<GrantReport> findByStatus(String status) {
        return grantReportDao.findByStatus(status);
    }
    
    public List<GrantReport> findPublicReports() {
        return grantReportDao.findPublicReports();
    }
    
    public List<GrantReportAttachment> findAttachmentsByReportId(Long grantReportId) {
        return grantReportAttachmentDao.findByGrantReportId(grantReportId);
    }
    
    public GrantReportAttachment findAttachmentById(Long id) {
        return grantReportAttachmentDao.findById(id);
    }
}
