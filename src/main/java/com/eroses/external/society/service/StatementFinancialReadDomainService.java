package com.eroses.external.society.service;

import com.eroses.external.society.mappers.StatementFinancialDao;
import com.eroses.external.society.model.StatementFinancial;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatementFinancialReadDomainService {
    private final StatementFinancialDao statementFinancialDao;

    public boolean isExists(Long id) {
        return statementFinancialDao.isExistsByStatementId(id);
    }

    public StatementFinancial findById(Long id) {
        return statementFinancialDao.findById(id);
    }

    public List<StatementFinancial> findAll(Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset);
        params.put("limit", limit);
        return statementFinancialDao.findAll(params);
    }

    public Long countFindAll() {
        return statementFinancialDao.countFindAll();
    }

    public StatementFinancial findOneBySocietyIdAndStatementId(Long societyId, Long statementId) {
        Map<String, Object> params = new HashMap<>();
        params.put("societyId", societyId);
        params.put("statementId", statementId);
        return statementFinancialDao.findOneBySocietyIdAndStatementId(params);
    }

    public StatementFinancial findByStatementId(Long statementId) {
        return statementFinancialDao.findByStatementId(statementId);
    }

    public StatementFinancial findByParam(Map<String, Object> params) {
        return statementFinancialDao.findByParam(params);
    }
}
