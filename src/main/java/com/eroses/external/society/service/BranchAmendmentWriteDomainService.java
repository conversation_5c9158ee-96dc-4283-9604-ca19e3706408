package com.eroses.external.society.service;

import com.eroses.external.society.mappers.BranchAmendmentDao;
import com.eroses.external.society.model.BranchAmendment;
import com.eroses.external.society.model.enums.PaymentStatus;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class BranchAmendmentWriteDomainService {
    private final BranchAmendmentDao branchAmendmentDao;

    public Long create(BranchAmendment branchAmendment, Long userId) throws Exception {

        branchAmendment.setCreatedBy(userId);
        branchAmendmentDao.create(branchAmendment);
        return branchAmendment.getId();
    }

    public Long update(BranchAmendment branchAmendment, Long userId) throws Exception {

        branchAmendment.setModifiedBy(userId);
        branchAmendmentDao.update(branchAmendment);
        return branchAmendment.getId();
    }

    public Long delete(Long id) throws Exception {

        branchAmendmentDao.delete(id);
        return id;
    }

    public boolean updateBranchAmendmentStatus(BranchAmendment branchAmendment, int paymentStatusCode, String paymentMethod) throws Exception {

        if(paymentStatusCode == PaymentStatus.PAID.getCode()){
            branchAmendment.setApplicationStatusCode(ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode());
            branchAmendment.setPaymentDate(LocalDateTime.now());
            branchAmendment.setPaymentMethod(paymentMethod);
        }else {
            branchAmendment.setApplicationStatusCode(ApplicationStatusCode.BAYARAN_GAGAL.getCode());
        }
        branchAmendment.setModifiedBy(0L);
        branchAmendmentDao.update(branchAmendment);
        return true;
    }

    public BranchAmendment updateRoDecision(BranchAmendment branchAmendment, User user, int applicationStatusCode, String status) throws Exception {

        // Update application status code and status based on RO decision
        branchAmendment.setApplicationStatusCode(applicationStatusCode);
        //branchAmendment.setDecision(String.valueOf(applicationStatusCode));
        branchAmendment.setModifiedBy(user.getId());
        branchAmendmentDao.update(branchAmendment);
        return branchAmendment;
    }
}
