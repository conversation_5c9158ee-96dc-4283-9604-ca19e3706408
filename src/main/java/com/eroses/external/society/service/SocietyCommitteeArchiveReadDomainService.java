package com.eroses.external.society.service;

import com.eroses.external.society.mappers.SocietyCommitteeArchiveDao;
import com.eroses.external.society.model.SocietyCommitteeArchive;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.model.enums.StatusCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyCommitteeArchiveReadDomainService {

    private final SocietyCommitteeArchiveDao societyCommitteeArchiveDao;

    public List<SocietyCommitteeArchive> getCommitteeArchive(Map<String, Object> param, Integer offset, Integer limit) {
        param.put("offset", offset);
        param.put("limit", limit);

        return societyCommitteeArchiveDao.getCommitteeArchive(param);
    }

    public Long countCommitteeArchive(Map<String, Object> param) {
        return societyCommitteeArchiveDao.countCommitteeArchive(param);
    }

    public List<SocietyCommitteeArchive> findByMeetingDate(LocalDate meetingDate) {
        return societyCommitteeArchiveDao.findByMeetingDate(meetingDate);
    }

    public SocietyCommitteeArchive findOneByParams(Map<String, Object> params) {
        return societyCommitteeArchiveDao.findOneByParams(params);
    }

    public List<LocalDate> findAllAppointedDates(Long societyId) {
        return societyCommitteeArchiveDao.findAllAppointedDates(societyId);
    }

    public List<SocietyCommitteeArchive> findByParams(Map<String, Object> params) {
        return societyCommitteeArchiveDao.findByParams(params);
    }

    public List<SocietyCommitteeArchive> findActiveCommitteesInSocietyArchiveWithRoles(Map<String, Object> params) {

        if (!params.containsKey("applicationStatusCode")) {
            params.put("applicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
        }
        if (!params.containsKey("status")) {
            params.put("status", StatusCode.AKTIF.getCode());
        }

        return societyCommitteeArchiveDao.findActiveCommitteesInSocietyArchiveWithRoles(params);
    }
}
