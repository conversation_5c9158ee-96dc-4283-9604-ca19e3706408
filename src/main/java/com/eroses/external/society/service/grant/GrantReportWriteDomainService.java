package com.eroses.external.society.service.grant;

import com.eroses.external.society.mappers.grant.GrantReportAttachmentDao;
import com.eroses.external.society.mappers.grant.GrantReportDao;
import com.eroses.external.society.model.enums.GrantReportStatusEnum;
import com.eroses.external.society.model.grant.GrantReport;
import com.eroses.external.society.model.grant.GrantReportAttachment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class GrantReportWriteDomainService {

    private final GrantReportDao grantReportDao;
    private final GrantReportAttachmentDao grantReportAttachmentDao;

    @Transactional
    public Long createGrantReport(GrantReport grantReport) throws Exception {
        grantReportDao.create(grantReport);
        return grantReport.getId();
    }

    @Transactional
    public boolean updateGrantReport(GrantReport grantReport) throws Exception {
        return grantReportDao.update(grantReport);
    }

    @Transactional
    public Long createGrantReportAttachment(GrantReportAttachment grantReportAttachment) throws Exception {
        grantReportAttachmentDao.create(grantReportAttachment);
        return grantReportAttachment.getId();
    }

    @Transactional
    public boolean submitGrantReport(Long id, Long userId) throws Exception {
        GrantReport grantReport = grantReportDao.findById(id);
        if (grantReport == null) {
            return false;
        }

        grantReport.setStatus(GrantReportStatusEnum.SUBMITTED.name());
        grantReport.setSubmissionDate(LocalDateTime.now());
        grantReport.setModifiedBy(userId);
        grantReport.setModifiedDate(LocalDateTime.now());

        return grantReportDao.update(grantReport);
    }

    @Transactional
    public boolean approveGrantReport(Long id, Long userId) throws Exception {
        GrantReport grantReport = grantReportDao.findById(id);
        if (grantReport == null) {
            return false;
        }

        grantReport.setStatus(GrantReportStatusEnum.APPROVED.name());
        grantReport.setModifiedBy(userId);
        grantReport.setModifiedDate(LocalDateTime.now());

        return grantReportDao.update(grantReport);
    }

    @Transactional
    public boolean rejectGrantReport(Long id, Long userId) throws Exception {
        GrantReport grantReport = grantReportDao.findById(id);
        if (grantReport == null) {
            return false;
        }

        grantReport.setStatus(GrantReportStatusEnum.REJECTED.name());
        grantReport.setModifiedBy(userId);
        grantReport.setModifiedDate(LocalDateTime.now());

        return grantReportDao.update(grantReport);
    }

    @Transactional
    public void deleteGrantReportAttachment(Long id) throws Exception {
        grantReportAttachmentDao.delete(id);
    }

    @Transactional
    public void deleteGrantReportAttachmentsByReportId(Long grantReportId) throws Exception {
        grantReportAttachmentDao.deleteByGrantReportId(grantReportId);
    }

    @Transactional
    public void deleteGrantReport(Long id) throws Exception {
        // First delete all attachments
        deleteGrantReportAttachmentsByReportId(id);

        // Then delete the report itself
        grantReportDao.delete(id);
    }
}
