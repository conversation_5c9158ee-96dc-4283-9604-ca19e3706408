package com.eroses.external.society.service;

import com.eroses.external.society.mappers.SocietyDocumentTemplateDao;
import com.eroses.external.society.model.SocietyDocumentTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyDocumentTemplateReadDomainService {
    private final SocietyDocumentTemplateDao societyDocumentTemplateDao;

    public List<SocietyDocumentTemplate> getAllBySocietyId(Long societyId) {
        return societyDocumentTemplateDao.getAllBySocietyId(societyId);
    }

    public SocietyDocumentTemplate getBySocietyIdAndTemplateCode(Long societyId, String templateCode) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "templateCode", templateCode,
                "status", 1
        );
        return societyDocumentTemplateDao.getBySocietyIdAndTemplateCode(params);
    }
}
