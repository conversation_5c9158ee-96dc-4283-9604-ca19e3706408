package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmInsolvencyDepartmentDao;
import com.eroses.external.society.model.lookup.AdmInsolvencyDepartment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmInsolvencyDepartmentWriteDomainService {
    private final AdmInsolvencyDepartmentDao admInsolvencyDepartmentDao;

    public Long create(AdmInsolvencyDepartment insolvencyDepartment) throws Exception {
        admInsolvencyDepartmentDao.create(insolvencyDepartment);
        return insolvencyDepartment.getId();
    }

    public boolean update(AdmInsolvencyDepartment insolvencyDepartment) throws Exception {
        return admInsolvencyDepartmentDao.update(insolvencyDepartment);
    }

    public Long delete(Long id) throws Exception {
        admInsolvencyDepartmentDao.delete(id);
        return id;
    }
}
