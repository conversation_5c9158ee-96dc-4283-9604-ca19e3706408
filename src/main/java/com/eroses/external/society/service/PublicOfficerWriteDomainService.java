package com.eroses.external.society.service;

import com.eroses.external.society.mappers.PublicOfficerDao;
import com.eroses.external.society.model.PublicOfficer;
import com.eroses.external.society.model.enums.PaymentStatus;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Service
@Slf4j
@RequiredArgsConstructor
public class PublicOfficerWriteDomainService {
    private final PublicOfficerDao publicOfficerDao;

    public boolean create(PublicOfficer publicOfficer) throws Exception {
        return publicOfficerDao.create(publicOfficer);
    }

    public boolean update(PublicOfficer publicOfficer) throws Exception {
        return publicOfficerDao.update(publicOfficer);
    }

    public boolean updatePublicOfficerStatus(PublicOfficer publicOfficer, int paymentStatusCode, String paymentMethod) throws Exception {
        if (paymentStatusCode == PaymentStatus.PAID.getCode()) {
            publicOfficer.setSubmissionDate(LocalDate.now());
            publicOfficer.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode()));
            publicOfficer.setPaymentDate(LocalDate.now());
            publicOfficer.setPaymentMethod(paymentMethod);
        } else {
            publicOfficer.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.BAYARAN_GAGAL.getCode()));
        }
        publicOfficer.setModifiedBy(0L);
        publicOfficer.setModifiedDate(LocalDateTime.now());
        return update(publicOfficer);
    }

}
