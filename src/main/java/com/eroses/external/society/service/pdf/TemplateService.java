package com.eroses.external.society.service.pdf;

import lombok.RequiredArgsConstructor;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Service
@RequiredArgsConstructor
public class TemplateService {

    public String getHtmlTemplate(String templateName) throws IOException {
        byte[] file = Files.readAllBytes(
                Paths.get(
                        new ClassPathResource("template/html/"+templateName).getURI()
                )
        );

        return new String(file);
    }
}
