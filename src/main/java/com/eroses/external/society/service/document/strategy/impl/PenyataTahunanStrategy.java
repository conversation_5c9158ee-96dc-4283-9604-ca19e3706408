package com.eroses.external.society.service.document.strategy.impl;

import com.eroses.external.society.api.converter.input.CommitteeArchiveConverter;
import com.eroses.external.society.dto.response.statement.StatementSocietyInfoGetOneResponse;
import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.*;
import com.eroses.external.society.service.*;
import com.eroses.external.society.service.admin.AdmAddressesReadDomainService;
import com.eroses.external.society.service.admin.AdmCategoryReadDomainService;
import com.eroses.external.society.service.document.strategy.AbstractDocumentMappingStrategy;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.service.utils.HtmlGeneratorService;
import com.eroses.user.api.facade.UserFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.parser.Parser;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Component
public class PenyataTahunanStrategy extends AbstractDocumentMappingStrategy {

    private final AdmCategoryReadDomainService admCategoryReadDomainService;
    private final StatementReadDomainService statementReadDomainService;
    private final StatementSocietyInfoReadDomainService statementSocietyInfoReadDomainService;
    private final StatementBankInfoReadDomainService statementBankInfoReadDomainService;
    private final StatementContributionReadDomainService statementContributionReadDomainService;
    private final StatementFinancialReadDomainService statementFinancialReadDomainService;
    private final BranchReadDomainService branchReadDomainService;
    private final CommitteeReadDomainService committeeReadDomainService;
    private final SocietyCommitteeArchiveReadDomainService societyCommitteeArchiveReadDomainService;
    private final CommitteeArchiveConverter committeeArchiveConverter;
    private final AdmAddressesReadDomainService admAddressesReadDomainService;
    private final AuditorReadDomainService auditorReadDomainService;
    private final TrusteeReadDomainService trusteeReadDomainService;

    public PenyataTahunanStrategy(
            DocumentTemplateReadDomainService documentTemplateReadDomainService,
            S3DomainService s3DomainService,
            PdfService pdfService,
            HtmlGeneratorService htmlGeneratorService,
            UserFacade userFacade,
            SocietyReadDomainService societyReadDomainService,
            AdmCategoryReadDomainService admCategoryReadDomainService,
            StatementReadDomainService statementReadDomainService,
            StatementSocietyInfoReadDomainService statementSocietyInfoReadDomainService,
            StatementBankInfoReadDomainService statementBankInfoReadDomainService,
            StatementContributionReadDomainService statementContributionReadDomainService,
            StatementFinancialReadDomainService statementFinancialReadDomainService,
            BranchReadDomainService branchReadDomainService,
            CommitteeReadDomainService committeeReadDomainService,
            SocietyCommitteeArchiveReadDomainService societyCommitteeArchiveReadDomainService,
            CommitteeArchiveConverter committeeArchiveConverter,
            AdmAddressesReadDomainService admAddressesReadDomainService,
            AuditorReadDomainService auditorReadDomainService,
            TrusteeReadDomainService trusteeReadDomainService) {
        super(documentTemplateReadDomainService, s3DomainService, pdfService, htmlGeneratorService, userFacade, societyReadDomainService);
        this.admCategoryReadDomainService = admCategoryReadDomainService;
        this.statementReadDomainService = statementReadDomainService;
        this.statementSocietyInfoReadDomainService = statementSocietyInfoReadDomainService;
        this.statementBankInfoReadDomainService = statementBankInfoReadDomainService;
        this.statementContributionReadDomainService = statementContributionReadDomainService;
        this.statementFinancialReadDomainService = statementFinancialReadDomainService;
        this.branchReadDomainService = branchReadDomainService;
        this.committeeReadDomainService = committeeReadDomainService;
        this.societyCommitteeArchiveReadDomainService = societyCommitteeArchiveReadDomainService;
        this.committeeArchiveConverter = committeeArchiveConverter;
        this.admAddressesReadDomainService = admAddressesReadDomainService;
        this.auditorReadDomainService = auditorReadDomainService;
        this.trusteeReadDomainService = trusteeReadDomainService;
    }

    @Override
    public String mapDataToDocument(String templateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        DocumentTemplate template = getDocumentTemplate(templateCode);
        String htmlTemplate = template.getHtmlContent();

        Map<String, String> mapFields = prepareDataForMapping(societyId, additionalParams);
        String processedHtml = htmlGeneratorService.processConditionals(htmlTemplate, mapFields);
        String secondProcessedHtml = htmlGeneratorService.mapToHtml(processedHtml, mapFields);

        Document doc = Jsoup.parse(secondProcessedHtml, "", Parser.xmlParser());
        //A3 - AJK Table
        Element committeeTable = doc.getElementById("MAKLUMAT-PEMEGANG-JAWATAN-UTAMA-table");
        this.processCommitteeTable(committeeTable, societyId, additionalParams);

        //A4 - JuruAudit Table
        Element juruAuditTable = doc.getElementById("MAKLUMAT-JURUAUDIT-table");
        this.processJuruAuditTable(juruAuditTable, societyId, additionalParams);

        //A5 - Pemegang Amanah?
        Element pemegangAmanahTable = doc.getElementById("MAKLUMAT-PEMEGANG-AMANAH-table");
        this.processPemegangAmanah(pemegangAmanahTable, societyId);

        //C1 - Sumbangan Dari Luar Table
        Element sumbanganDariLuarTable = doc.getElementById("SUMBANGAN-DARI-LUAR-table");
        //C2 - Sumbangan Ke Luar Table
        Element sumbanganKeLuarTable = doc.getElementById("SUMBANGAN-KE-LUAR-table");
        this.processContributionTable(sumbanganDariLuarTable, sumbanganKeLuarTable, societyId, additionalParams);

        //clean up just in case
        HtmlGeneratorService.escapeTextNodesAsXHTML(doc.body().html());
        String cleanedHtml = doc.outerHtml().replaceFirst("(?i)<!DOCTYPE[^>]*>", "");
        String xhtmlHeader = "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" " +
                "\"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">";

        String finalHtml = xhtmlHeader + cleanedHtml;

        return finalHtml;
    }

    @Override
    protected Map<String, String> prepareDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {
        //call everything for statement
        Society society = societyReadDomainService.findById(societyId);
        Statement statement = statementReadDomainService.findByParam(additionalParams);
        StatementSocietyInfoGetOneResponse statementSocietyInfo = statementSocietyInfoReadDomainService.findByParam(additionalParams);
        List<StatementBankInfo> statementBankInfo = statementBankInfoReadDomainService.listStatementBankInfo(additionalParams);
        StatementFinancial statementFinancial = statementFinancialReadDomainService.findByParam(additionalParams);
        StatementContribution statementContribution = statementContributionReadDomainService.findByParam(additionalParams);

        Map<String, String> mapFields = new HashMap<>();

        //map for Maklumat Pertubuhan - A1 Maklumat Am
        mapFields = getMaklumatPertubuhanMap(society, statement, statementSocietyInfo, statementBankInfo, additionalParams, mapFields);

        //map for Maklumat Mesyuarat Agung - A2
        mapFields = getMaklumatMesyuaratAgungMap(statementSocietyInfo, mapFields);

        /**map for Maklumat Kewangan - B1 Pendapatan dan Perbelanjaan
         * & map for Maklumat Kewangan - B2 Aset dan Liabiliti
         **/
        mapFields = getMaklumatKewanganMap(statementFinancial, mapFields);

        return mapFields;
    }

    private Map<String, String> getMaklumatPertubuhanMap(Society society, Statement statement, StatementSocietyInfoGetOneResponse statementSocietyInfo, List<StatementBankInfo> statementBankInfo, Map<String, Object> additionalParams, Map<String, String> mapFields) {

        mapFields.put("{{tahunPenyata}}", getValueOrDefaultStatement(statement.getStatementYear().toString()));
        mapFields.put("{{societyNo}}", getValueOrDefaultStatement(society.getSocietyNo().toUpperCase()));

        String categoryCodeJppm = SocietyCategoryEnum.getCategoryDescriptionById(Integer.valueOf(society.getCategoryCodeJppm()));
        mapFields.put("{{categoryCodeJppm}}", getValueOrDefaultStatement(categoryCodeJppm));

        if (society.getSubCategoryCode() != null && !society.getSubCategoryCode().isEmpty()) {
            AdmCategory admCategory = admCategoryReadDomainService.findById(Long.valueOf(society.getSubCategoryCode()));
            mapFields.put("{{subCategoryCode}}", getValueOrDefaultStatement(admCategory.getCategoryNameBm()));
        } else {
            mapFields.put("{{subCategoryCode}}", getValueOrDefaultStatement("Tiada"));
        }

        mapFields.put("{{societyName}}", getValueOrDefaultStatement(society.getSocietyName().toUpperCase()));

        String fullAddress = societyAddressProcessor(society);
        mapFields.put("{{address}}", getValueOrDefaultStatement(fullAddress));
        mapFields.put("{{phoneNumber}}", getValueOrDefaultStatement(society.getPhoneNumber()));
        mapFields.put("{{faxNumber}}", getValueOrDefaultStatement(society.getFaxNumber()));
        mapFields.put("{{financialYearStart}}", formatDateOrDefault(statement.getFinancialYearStart(), "-"));
        mapFields.put("{{financialYearEnd}}", formatDateOrDefault(statement.getFinancialYearEnd(), "-"));

        mapFields.put("{{regMemberCount}}", String.valueOf(getValueOrDefaultStatement(statementSocietyInfo.getRegMemberCount())));
        mapFields.put("{{ajkCount}}", String.valueOf(getValueOrDefaultStatement(Math.toIntExact(statementSocietyInfo.getCommitteeCount()))));

        Map<String, Object> branchCountParam = new HashMap<>();
        branchCountParam.put("societyId", society.getId());
        Long branchCount = branchReadDomainService.countAllBranch(branchCountParam);
        mapFields.put("{{branchCount}}", String.valueOf(getValueOrDefaultStatement(Math.toIntExact(branchCount))));
        mapFields.put("{{federation}}", getValueOrDefaultStatement(statementSocietyInfo.getFederation() ? "Ya" : "Tidak"));

        //statement bank info
        String bankInfo = this.getBankInfo(statementBankInfo);
        mapFields.put("{{accountNo}}", bankInfo != null ? bankInfo : "Tiada maklumat");

        return mapFields;
    }

    private Map<String, String> getMaklumatMesyuaratAgungMap(StatementSocietyInfoGetOneResponse statementSocietyInfo, Map<String, String> mapFields) {

        mapFields.put("{{meetingFrequency}}", getValueOrDefaultStatement(statementSocietyInfo.getMeetingFrequency()));
        mapFields.put("{{meetingDate}}", formatDateOrDefault(statementSocietyInfo.getMeetingDate(), "-"));
        mapFields.put("{{regMemberCountMeetingDate}}", String.valueOf(getValueOrDefaultStatement(statementSocietyInfo.getRegMemberCount())));
        mapFields.put("{{votingMemberCount}}", String.valueOf(getValueOrDefaultStatement(statementSocietyInfo.getVotingMemberCount())));

        return mapFields;
    }

    private Map<String, String> getMaklumatKewanganMap(StatementFinancial statementFinancial, Map<String, String> mapFields) {
        BigDecimal totalIncome = BigDecimal.ZERO;
        BigDecimal totalExpense = BigDecimal.ZERO;
        BigDecimal totalAsset = BigDecimal.ZERO;
        BigDecimal totalLiability = BigDecimal.ZERO;

        if (statementFinancial != null) {
            totalIncome = statementFinancial.getTotalIncome() != null ? statementFinancial.getTotalIncome() : BigDecimal.ZERO;
            totalExpense = statementFinancial.getTotalExpense() != null ? statementFinancial.getTotalExpense() : BigDecimal.ZERO;
            totalAsset = statementFinancial.getTotalAsset() != null ? statementFinancial.getTotalAsset() : BigDecimal.ZERO;
            totalLiability = statementFinancial.getTotalLiability() != null ? statementFinancial.getTotalLiability() : BigDecimal.ZERO;
        }

        //comma formatting
        mapFields.put("{{totalIncome}}", formatMonetaryValuesToString(totalIncome));
        mapFields.put("{{totalExpense}}", formatMonetaryValuesToString(totalExpense));
        mapFields.put("{{totalAsset}}", formatMonetaryValuesToString(totalAsset));
        mapFields.put("{{totalLiability}}", formatMonetaryValuesToString(totalLiability));

        return mapFields;
    }

    private String societyAddressProcessor(Society society) {

        //process state and district id first
        AdmAddresses state = admAddressesReadDomainService.findById(Long.valueOf(society.getStateCode()));
        AdmAddresses district = admAddressesReadDomainService.findById(Long.valueOf(society.getDistrictCode()));

        String fullAddress = society.getAddress() + " " + society.getCity() + " " + society.getPostcode() + " "
                + district.getName() + " " + state.getName();

        return fullAddress;
    }

    @Override
    public boolean canHandle(String templateCode) {
        return DocumentTemplateEnum.PENYATA_TAHUNAN.getCode().equals(templateCode);
    }

    @Override
    public String getFileName(DocumentTemplate documentTemplate, Long societyId, Map<String, Object> additionalParams) throws Exception {
        // Get the latest society data from database
        Society society = societyReadDomainService.findById(societyId);
        Statement statement = statementReadDomainService.findByParam(additionalParams);

        // Build filename with structure: SIJIL KELULUSAN + "_" + societyNo + "_" + societyName
        return "Penyata Tahunan - " + society.getSocietyNo() + " Tahun " + statement.getStatementYear();
    }

    private String getBankInfo(List<StatementBankInfo> statementBankInfo) {
        Integer bankCount = statementBankInfo.size();
        if (bankCount == 0) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        Integer i = 1;
        for (StatementBankInfo bankInfo : statementBankInfo) {
            sb.append("<br />");
            sb.append("<strong>Akaun Bank " + i + "</strong><br />");
            sb.append("Nama Bank: " + bankInfo.getBankName() + "<br />");
            sb.append("Nombor Akaun: " + bankInfo.getAccountNo() + "<br />");
            i++;
        }

        return sb.toString();
    }

    private void processCommitteeTable(Element committeeTable, Long societyId, Map<String, Object> additionalParams) {
        Statement statement = statementReadDomainService.findByParam(additionalParams);

        LocalDate appointedDate = statement.getAjkAppointedDate();
        List<String> positions = Position.committeePositionCodes();
        List<Committee> committeeList = new ArrayList<>();

        List<Committee> committees = committeeReadDomainService.findActiveCommitteesInSocietyWithRoles(positions, societyId);
        committees.sort(
                Comparator.comparing(Committee::getAppointedDate, Comparator.nullsLast(LocalDate::compareTo)).reversed()
        );
        if (appointedDate == null) {
            committeeList = committees;
        } else {
            if (!committees.getFirst().getAppointedDate().equals(appointedDate)) {
                Map<String, Object> params = new HashMap<>();
                params.put("societyId", societyId);
                params.put("appointedDate", appointedDate);
                params.put("positions", positions);

                List<SocietyCommitteeArchive> societyCommitteeArchives = societyCommitteeArchiveReadDomainService
                        .findActiveCommitteesInSocietyArchiveWithRoles(params);
                if (societyCommitteeArchives != null && !societyCommitteeArchives.isEmpty()) {
                    committeeList = committeeArchiveConverter.convertSocietyCommitteeArchiveListToCommitteeList(societyCommitteeArchives);
                } else {
                    committeeList = committees;
                }
            } else {
                committeeList = committees;
            }
        }

        //to sort based on rank
        committeeList.sort(Comparator
                .comparingInt((Committee c) -> {
                    String designationCodeStr = c.getDesignationCode();
                    if (designationCodeStr == null) return Integer.MAX_VALUE;
                    try {
                        int code = Integer.parseInt(designationCodeStr);
                        Position position = Position.sortByRank(code);
                        return position != null ? position.getRank() : Integer.MAX_VALUE;
                    } catch (NumberFormatException e) {
                        return Integer.MAX_VALUE;
                    }
                })
                .thenComparingInt(c -> {
                    try {
                        return Integer.parseInt(c.getDesignationCode());
                    } catch (Exception e) {
                        return Integer.MAX_VALUE;
                    }
                })
        );

        int titleIndex = 0;
        for (Committee eachCommittee : committeeList) {
            try {
                Position position = Position.getRole(Integer.parseInt(eachCommittee.getDesignationCode()));
                if (position != null && position.isHighRankCommittee()) {
                    String[] committeeData = setCommitteePositionInfo(eachCommittee);
                    String newRow = String.format(committeeTableRowHtml(), (Object[]) committeeData);

                    if (committeeTable != null) {
                        committeeTable.append(generateTitleRowHtml(++titleIndex, position.getName().replace("_", " ")));
                        committeeTable.append(newRow); // Then add the main row
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("Invalid designationCode for committee: {}", eachCommittee);
            }
        }
    }

    private String[] setJuruAuditPositionInfo(Auditor auditor) {
        Committee temp = new Committee();
        temp.setName(auditor.getName());
        temp.setNationalityStatus(auditor.getNationalityStatus());
        temp.setIdentificationType(auditor.getIdentificationType());
        temp.setIdentificationNo(auditor.getIdentificationNo());
        temp.setJobCode(auditor.getEmploymentCode());
        temp.setDateOfBirth(auditor.getDateOfBirth());
        temp.setResidentialAddress(auditor.getAddress());
        temp.setResidentialPostcode(auditor.getPostcode());
        temp.setResidentialCity(auditor.getCity());
        temp.setResidentialDistrictCode(auditor.getDistrictCode());
        temp.setResidentialStateCode(auditor.getStateCode());
        temp.setPhoneNumber(auditor.getPhoneNo());
        temp.setTelephoneNumber(auditor.getTelephoneNo());
        temp.setEmail(auditor.getEmail());

        return setCommitteePositionInfo(temp);
    }

    private String[] setPemegangAmanahInfo(TrusteeHolder trustee) {
        Committee temp = new Committee();
        temp.setName(trustee.getName());
        temp.setNationalityStatus(trustee.getCitizenshipStatus());
        temp.setIdentificationType(trustee.getIdentificationType());
        temp.setIdentificationNo(trustee.getIdentificationNo());
        temp.setJobCode(trustee.getOccupationCode());
        temp.setDateOfBirth(trustee.getDateOfBirth());
        temp.setResidentialAddress(trustee.getAddress());
        temp.setResidentialPostcode(trustee.getPostalCode());
        temp.setResidentialCity(trustee.getCity());
        temp.setResidentialDistrictCode(trustee.getDistrictCode());
        temp.setResidentialStateCode(trustee.getStateCode());
        temp.setPhoneNumber(trustee.getMobilePhoneNumber());
        temp.setTelephoneNumber(trustee.getHomePhoneNumber());
        temp.setEmail(trustee.getEmail());

        return setCommitteePositionInfo(temp);
    }

    public String[] setCommitteePositionInfo(Committee committee) {

        String nationalityStatus = "";
        if (committee.getNationalityStatus() != null) {
            if (committee.getNationalityStatus().matches("\\d+")) {
                nationalityStatus = NationalityStatusEnum.getNationalityStatusInBmById(committee.getNationalityStatus());
            } else {
                nationalityStatus = committee.getNationalityStatus();
            }
        }

        String identificationType = "";
        if (committee.getIdentificationType() != null) {
            if (committee.getIdentificationType().matches("\\d+")) {
                identificationType = IdentificationType.getNameByCode(Integer.parseInt(committee.getIdentificationType()));
            } else {
                identificationType = committee.getIdentificationType();
            }
        }

        String dateOfBirth = committee.getDateOfBirth() != null
                ? committee.getDateOfBirth().format(DateTimeFormatter.ofPattern("dd-MM-yyyy"))
                : "";

        String residentialAddress = "";
        if (committee.getResidentialDistrictCode() != null && committee.getResidentialStateCode() != null) {
            String district = admAddressesReadDomainService.findById(Long.valueOf(committee.getResidentialDistrictCode())).getName();
            String state = admAddressesReadDomainService.findById(Long.valueOf(committee.getResidentialStateCode())).getName();
            residentialAddress = getValueOrDefaultStatement(committee.getResidentialAddress()) + ", <br />"
                    + getValueOrDefaultStatement(committee.getResidentialPostcode()) + " "
                    + getValueOrDefaultStatement(committee.getResidentialCity()) + " "
                    + getValueOrDefaultStatement(district) + ", <br />"
                    + getValueOrDefaultStatement(state);
        }

        return new String[]{
                getValueOrDefaultStatement(committee.getName().toUpperCase()),                       // Name
                getValueOrDefaultStatement(!nationalityStatus.isEmpty() ? nationalityStatus : null),          // Nationality
                getValueOrDefaultStatement(!identificationType.isEmpty() ? identificationType : null),         // ID Type
                getValueOrDefaultStatement(committee.getIdentificationNo()),           // ID Number
                getValueOrDefaultStatement(committee.getJobCode() != null ? committee.getJobCode() : null),               // Occupation/Employer
                getValueOrDefaultStatement(dateOfBirth),                                          // Date of Birth
                getValueOrDefaultStatement(!residentialAddress.isEmpty() ? residentialAddress : null),         // Address
                getValueOrDefaultStatement(committee.getPhoneNumber() != null ? committee.getPhoneNumber() : null),                // Mobile Number
                getValueOrDefaultStatement(committee.getTelephoneNumber() != null ? committee.getTelephoneNumber() : null),            // Landline Number
                getValueOrDefaultStatement(committee.getEmail() != null ? committee.getEmail() : null)                       // Email
        };
    }

    public String generateTitleRowHtml(int number, String title) {
        return String.format(
                "<tr style=\"height: 15pt;\">\n" +
                        "<td style=\"width: 527pt; border-top-style: solid; border-top-width: 2pt; " +
                        "border-left-style: solid; border-left-width: 1pt; border-bottom-style: solid; " +
                        "border-bottom-width: 2pt; border-right-style: solid; border-right-width: 1pt;\" bgcolor=\"#CCCCCC\">\n" +
                        "<p style=\"padding-left: 10pt; text-indent: 0pt; line-height: 13pt; text-align: center; font-weight: bold;\">" +
                        "%d)&nbsp;&nbsp;&nbsp;%s</p>\n" +
                        "</td>\n" +
                        "</tr>",
                number, title.toUpperCase()
        );
    }

    public String committeeTableRowHtml() {
        return "<tr>\n"
                + "<td style=\"width: 527pt; border-top-style: solid; border-top-width: 2pt; border-left-style: solid; border-left-width: 1pt; border-bottom-style: solid; border-bottom-width: 2pt; border-right-style: solid; border-right-width: 1pt;\">\n"
                + "<ol>\n"
                + "<li><p style=\"padding-top: 11pt; padding-left:36pt; text-indent: -16pt; text-align: left;\">1. Nama Penuh : %s</p></li>\n"
                + "<li><p style=\"padding-top: 5pt; padding-left: 36pt; text-indent: -16pt; text-align: left;\">2. Warganegara : %s</p></li>\n"
                + "<li><p style=\"padding-top: 5pt; padding-left: 36pt; text-indent: -16pt; text-align: left;\">3. Jenis Pengenalan : %s</p></li>\n"
                + "<li><p style=\"padding-top: 5pt; padding-left: 36pt; text-indent: -16pt; text-align: left;\">4. Nombor Pengenalan : %s</p></li>\n"
                + "<li><p style=\"padding-top: 5pt; padding-left: 36pt; text-indent: -16pt; text-align: left;\">5. Pekerjaan : %s</p></li>\n"
                + "<li><p style=\"padding-top: 5pt; padding-left: 36pt; text-indent: -16pt; text-align: left;\">6. Tarikh Lahir : %s</p></li>\n"
                + "<li><p style=\"padding-top: 5pt; padding-left: 36pt; line-height: 15pt; text-indent: -16pt; text-align: left;\">7. Alamat : %s</p></li>\n"
                + "<li><p style=\"padding-top: 5pt; padding-left: 36pt; text-indent: -16pt; text-align: left;\">8. No. Tel(Bimbit) : %s</p></li>\n"
                + "<li><p style=\"padding-top: 5pt; padding-left: 36pt; text-indent: -16pt; text-align: left;\">9. No. Tel(Talian Tetap) : %s</p></li>\n"
                + "<li><p style=\"padding-top: 5pt; padding-left: 36pt; text-indent: -22pt; text-align: left; padding-bottom:15pt;\">10. Email : %s</p></li>\n"
                + "</ol>\n"
                + "</td>\n"
                + "</tr>";
    }

    private void processJuruAuditTable(Element juruAuditTable, Long societyId, Map<String, Object> additionalParams) {
        Statement statement = statementReadDomainService.findByParam(additionalParams);

        LocalDate appointedDate = statement.getJuruauditAppointedDate();

        Map<String, Object> params = new HashMap<>();
        params.put("societyId", societyId);
        params.put("appointmentDate", appointedDate);
        List<Auditor> auditors = auditorReadDomainService.listAuditor(params);
        if (auditors == null || auditors.isEmpty()) {
            params.remove("appointmentDate");
            auditors = auditorReadDomainService.listAuditor(params);
        }

        int titleIndex = 0;
        if (auditors != null && !auditors.isEmpty()) {
            for (Auditor auditor : auditors) {
                try {
                    String[] juruauditData = setJuruAuditPositionInfo(auditor);
                    String newRow = String.format(committeeTableRowHtml(), (Object[]) juruauditData);

                    String juruauditTitle;
                    if (auditor.getAuditorType().equals("L")) {
                        juruauditTitle = "JURUAUDIT LUAR";
                    } else {
                        juruauditTitle = "JURUAUDIT DALAM";
                    }

                    if (juruAuditTable != null) {
                        juruAuditTable.append(generateTitleRowHtml(++titleIndex, juruauditTitle));
                        juruAuditTable.append(newRow); // Then add the main row
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid auditor: {}", auditor);
                }
            }
        } else {
            String newRow = noRecordBox();

            if (juruAuditTable != null) {
                juruAuditTable.append(newRow);
            }
        }
    }

    private void processPemegangAmanah(Element pemegangAmanahTable, Long societyId) {
        Map<String, Object> params = new HashMap<>();
        params.put("societyId", societyId);
        params.put("status", ApplicationStatusCode.AKTIF.getCode());
        List<TrusteeHolder> trustees = trusteeReadDomainService.findActiveTrusteesByParam(params);

        int titleIndex = 0;
        if (!trustees.isEmpty()) {
            for (TrusteeHolder trustee : trustees) {
                try {
                    String[] pemegangAmanahData = setPemegangAmanahInfo(trustee);
                    String newRow = String.format(committeeTableRowHtml(), (Object[]) pemegangAmanahData);

                    if (pemegangAmanahTable != null) {
                        pemegangAmanahTable.append(generateTitleRowHtml(++titleIndex, "PEMEGANG AMANAH"));
                        pemegangAmanahTable.append(newRow); // Then add the main row
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid trustee: {}", trustee);
                }
            }
        } else {
            String newRow = noRecordBox();

            if (pemegangAmanahTable != null) {
                pemegangAmanahTable.append(newRow);
            }
        }
    }

    private void processContributionTable(Element sumbanganDariLuarTable, Element sumbanganKeLuarTable, Long societyId, Map<String, Object> additionalParams) {
        //settle sumbangan dari luar first - type 1
        additionalParams.put("contributionCode", ContributionCode.DARI_LUAR_NEGARA.getCode());
        List<StatementContribution> receivedContribution = statementContributionReadDomainService.findAllByParam(additionalParams);
        if (!receivedContribution.isEmpty()) {
            int titleIndex = 1;
            for (StatementContribution contribution : receivedContribution) {
                try {
                    String[] sumbanganDariLuarData = setContributionInfo(contribution);
                    String newRow = String.format(contributionTableRowHtml(titleIndex, ContributionCode.DARI_LUAR_NEGARA.getCode()),
                            (Object[]) sumbanganDariLuarData);
                    titleIndex++;

                    if (sumbanganDariLuarTable != null) {
                        sumbanganDariLuarTable.append(newRow); // Then add the main row
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid contribution: {}", contribution);
                }
            }
        } else {
            String newRow = noRecordBox();

            if (sumbanganDariLuarTable != null) {
                sumbanganDariLuarTable.append(newRow);
            }
        }

        //settle sumbangan ke luar next - type 2
        additionalParams.put("contributionCode", ContributionCode.KE_LUAR_NEGARA.getCode());
        List<StatementContribution> sentContribution = statementContributionReadDomainService.findAllByParam(additionalParams);
        if (!sentContribution.isEmpty()) {
            int titleIndex = 1;
            for (StatementContribution contribution : sentContribution) {
                try {
                    String[] sumbanganKeLuarData = setContributionInfo(contribution);
                    String newRow = String.format(contributionTableRowHtml(titleIndex, ContributionCode.KE_LUAR_NEGARA.getCode()),
                            (Object[]) sumbanganKeLuarData);
                    titleIndex++;

                    if (sumbanganKeLuarTable != null) {
                        sumbanganKeLuarTable.append(newRow); // Then add the main row
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid contribution: {}", contribution);
                }
            }
        } else {
            String newRow = noRecordBox();

            if (sumbanganKeLuarTable != null) {
                sumbanganKeLuarTable.append(newRow);
            }
        }
    }

    private String contributionTableRowHtml(Integer index, int type) {
        String firstLabel = (type == ContributionCode.DARI_LUAR_NEGARA.getCode())
                ? "Pemberi Sumbangan"
                : "Penerima Sumbangan";

        return String.format(
                "<tr>\n"
                        + "<td style='width: 527pt; border-top-style: solid; border-top-width: 2pt; "
                        + "border-left-style: solid; border-left-width: 1pt; border-bottom-style: solid; "
                        + "border-bottom-width: 1pt; border-right-style: solid; border-right-width: 1pt;'>\n"
                        + "<ol>\n"
                        + "<li><p style='padding-top: 11pt; padding-left:36pt; text-indent: -16pt; text-align: left;'>%d. %s : %%s</p></li>\n"
                        + "<li><p style='padding-top: 5pt; padding-left: 50pt; text-indent: -16pt; text-align: left;'>Negara Asal : %%s</p></li>\n"
                        + "<li><p style='padding-top: 5pt; padding-left: 50pt; text-indent: -16pt; text-align: left;'>Nilai (RM) : %%s</p></li>\n"
                        + "</ol>\n"
                        + "</td>\n"
                        + "</tr>",
                index, firstLabel
        );
    }

    private String[] setContributionInfo(StatementContribution contribution) {
        return new String[]{
                getValueOrDefault(contribution.getContribution()), // Pemberi Sumbangan
                getValueOrDefault(contribution.getCountryOrigin()), // Negara Asal
                getValueOrDefault(contribution.getValue().toString()) // Nilai
        };
    }

    private String noRecordBox() {
        return "<tr style=\"height:20pt;\">\n" +
                "<td style=\"width:527pt; border-top-style:solid; border-top-width:2pt; border-left-style:solid; " +
                "border-left-width:1pt; border-bottom-style:solid; border-bottom-width:1pt; " +
                "border-right-style:solid; border-right-width:1pt;\">\n" +
                "<p class=\"s\" style=\"text-indent:0pt; text-align:center;\">Tiada Rekod</p>\n" +
                "</td>\n" +
                "</tr>";
    }
}