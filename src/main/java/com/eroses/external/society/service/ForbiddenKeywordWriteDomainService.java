package com.eroses.external.society.service;

import com.eroses.external.society.mappers.ForbiddenKeywordDao;
import com.eroses.external.society.mappers.ForbiddenLogoDao;
import com.eroses.external.society.model.ForbiddenKeyword;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class ForbiddenKeywordWriteDomainService {
    private final ForbiddenKeywordDao forbiddenKeywordDao;
    private final ForbiddenKeywordReadDomainService forbiddenKeywordReadDomainService;
    private final ForbiddenLogoDao forbiddenLogoDao;

    @Transactional
    public ForbiddenKeyword createSenarai(ForbiddenKeyword forbidden) {
        log.info("creating new larangan {}", forbidden);
        forbidden.setStatus(ApplicationStatusCode.AKTIF.getCode());
        ForbiddenKeyword created = forbiddenKeywordDao.createSenarai(forbidden);
        if (Objects.isNull(created)) {
            throw new RuntimeException("Failed to create larangan");
        }
        return forbidden;
    }

    @Transactional
    public Boolean updateSenarai(ForbiddenKeyword forbidden) {
        log.info("updating larangan {}", forbidden);
        return forbiddenKeywordDao.update(forbidden);
    }

    @Transactional
    public Boolean deleteSenarai(Long id) {
        ForbiddenKeyword forbiddenKeyword = forbiddenKeywordReadDomainService.findSenaraiById(id);

        if (forbiddenKeyword == null) {
            throw new EntityNotFoundException(
                    String.format("Larangan dengan ID %d tidak ditemui.", id)
            );
        }
        forbiddenKeywordDao.delete(id);
//        forbiddenKeyword.setStatus(ApplicationStatusCode.PADAM.getCode());
        log.info("deleting larangan with id {}", id);
        return true;
    }


}