package com.eroses.external.society.service;

import com.eroses.external.society.mappers.RoAmendmentQueryDao;
import com.eroses.external.society.model.RoAmendmentQuery;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class RoAmendmentQueryReadDomainService {

    private final RoAmendmentQueryDao roAmendmentQueryDao;

    public List<RoAmendmentQuery> get(Map<String, Object> param) {
        return roAmendmentQueryDao.get(param);
    }

    public RoAmendmentQuery getById(Long id) {
        return roAmendmentQueryDao.getById(id);
    }
}
