package com.eroses.external.society.service.grant;

import com.eroses.external.society.mappers.grant.GrantTemplateDao;
import com.eroses.external.society.mappers.grant.GrantTemplateFieldDao;
import com.eroses.external.society.mappers.grant.GrantTemplateSocietyCategoryDao;
import com.eroses.external.society.model.grant.GrantTemplate;
import com.eroses.external.society.model.grant.GrantTemplateField;
import com.eroses.external.society.model.grant.GrantTemplateSocietyCategory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class GrantTemplateReadDomainService {
    
    private final GrantTemplateDao grantTemplateDao;
    private final GrantTemplateFieldDao grantTemplateFieldDao;
    private final GrantTemplateSocietyCategoryDao grantTemplateSocietyCategoryDao;
    
    public List<GrantTemplate> findAll(String title, String status, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "title", title,
                "status", status,
                "offset", offset,
                "limit", limit
        );
        return grantTemplateDao.findAll(params);
    }
    
    public Long countAll(String title, String status) {
        Map<String, Object> params = Map.of(
                "title", title,
                "status", status
        );
        return grantTemplateDao.countAll(params);
    }
    
    public List<GrantTemplate> findAllDrafts() {
        return grantTemplateDao.findAllDrafts();
    }
    
    public List<GrantTemplate> findAllPublished() {
        return grantTemplateDao.findAllPublished();
    }
    
    public GrantTemplate findById(Long id) {
        return grantTemplateDao.findById(id);
    }
    
    public List<GrantTemplateField> findFieldsByGrantTemplateId(Long grantTemplateId) {
        return grantTemplateFieldDao.findByGrantTemplateId(grantTemplateId);
    }
    
    public GrantTemplateField findFieldById(Long id) {
        return grantTemplateFieldDao.findById(id);
    }
    
    public List<GrantTemplateSocietyCategory> findSocietyCategoriesByGrantTemplateId(Long grantTemplateId) {
        return grantTemplateSocietyCategoryDao.findByGrantTemplateId(grantTemplateId);
    }
    
    public List<GrantTemplate> findBySocietyCategory(Long societyCategoryId) {
        return grantTemplateDao.findBySocietyCategory(societyCategoryId);
    }
    
    public List<GrantTemplate> findAvailableForSociety(Long societyId) {
        return grantTemplateDao.findAvailableForSociety(societyId);
    }
}
