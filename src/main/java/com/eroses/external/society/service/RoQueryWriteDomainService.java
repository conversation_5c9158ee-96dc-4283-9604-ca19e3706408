package com.eroses.external.society.service;

import com.eroses.external.society.mappers.RoQueryDao;
import com.eroses.external.society.model.RoQuery;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class RoQueryWriteDomainService {
    private final RoQueryDao roQueryDao;

    public boolean create(RoQuery roQuery) throws Exception {
        return roQueryDao.create(roQuery);
    }

    public boolean createNewSecretaryROQuery(RoQuery roQuery) throws Exception {
        return roQueryDao.create(roQuery);
    }

    public boolean update(RoQuery roQuery) throws Exception {
        return roQueryDao.update(roQuery);
    }

    public boolean updateAppealRoQuery(Long id, RoQuery roQuery) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("roQuery", roQuery);
        return roQueryDao.updateAppealRoQuery(params);
    }

    public boolean updateLatestByCriteria(int finished, Long userId, String type, Long societyId, Long branchId, Long appealId, Long amendmentId, Long liquidationId, Long principalSecretaryId, String queryReceiver) {
        Map<String, Object> params = Map.of(
                "finished", finished,
                "userId", userId,
                "roQueryType", type,
                "societyId", societyId,
                "branchId", branchId,
                "appealId", appealId,
                "amendmentId", amendmentId,
                "liquidationId", liquidationId,
                "principalSecretaryId", principalSecretaryId,
                "queryReceiver", queryReceiver);
        return roQueryDao.updateLatestByCriteria(params);
    }
}