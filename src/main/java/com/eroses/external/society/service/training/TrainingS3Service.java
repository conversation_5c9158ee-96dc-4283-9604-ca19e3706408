package com.eroses.external.society.service.training;

import com.eroses.external.society.dto.response.UploadDocumentResponse;
import com.eroses.external.society.model.Document;
import com.eroses.external.society.model.enums.DocumentTypeEnum;
import com.eroses.external.society.service.S3DomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.util.UUID;

/**
 * Service for handling S3 operations for training materials and certificates.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrainingS3Service {

    private final S3DomainService s3DomainService;

    private static final String TRAINING_MATERIAL_FOLDER = "training-materials";
    private static final String CERTIFICATE_FOLDER = "certificates";

    /**
     * Uploads a training material file to S3.
     *
     * @param file The file to upload
     * @param materialType The type of material (VIDEO, PDF, TEXT, IMAGE)
     * @return The S3 URL of the uploaded file
     */
    public String uploadTrainingMaterial(MultipartFile file, String materialType) {
        try {
            if (file == null || file.isEmpty()) {
                throw new IllegalArgumentException("File cannot be null or empty");
            }

            // Generate a unique key for the file
            String originalFilename = file.getOriginalFilename();
            String fileExtension = originalFilename != null ? originalFilename.substring(originalFilename.lastIndexOf(".")) : "";
            String key = UUID.randomUUID().toString() + fileExtension;

            // Upload the file to S3
            String s3Url = s3DomainService.uploadFile(file, TRAINING_MATERIAL_FOLDER, key);

            log.info("Training material uploaded to S3: {}", s3Url);
            return s3Url;
        } catch (Exception e) {
            log.error("Error uploading training material to S3", e);
            throw new RuntimeException("Failed to upload training material: " + e.getMessage(), e);
        }
    }

    /**
     * Uploads a certificate file to S3.
     *
     * @param certificateBytes The certificate file content as byte array
     * @param fileName The name of the certificate file
     * @return The S3 URL of the uploaded certificate
     */
    public String uploadCertificate(byte[] certificateBytes, String fileName) {
        try {
            if (certificateBytes == null || certificateBytes.length == 0) {
                throw new IllegalArgumentException("Certificate content cannot be null or empty");
            }

            // Create a Document object for the certificate
            Document document = new Document();
            document.setName(fileName);
            document.setType(DocumentTypeEnum.SUPPORTING_DOCUMENT.getType()); // Using supporting document type
            document.setDoc(certificateBytes);

            // Generate a presigned URL and upload the certificate
            UploadDocumentResponse response = s3DomainService.generatePresignedUrl(document);

            if (response != null && response.getDocument() != null) {
                log.info("Certificate uploaded to S3: {}", response.getDocument().getUrl());
                return response.getDocument().getUrl();
            } else {
                throw new RuntimeException("Failed to upload certificate to S3");
            }
        } catch (Exception e) {
            log.error("Error uploading certificate to S3", e);
            throw new RuntimeException("Failed to upload certificate: " + e.getMessage(), e);
        }
    }

    /**
     * Downloads a file from S3.
     *
     * @param s3Url The S3 URL of the file to download
     * @return The file content as a ByteArrayInputStream
     */
    public ByteArrayInputStream downloadFile(String s3Url) {
        try {
            byte[] bytes = s3DomainService.getDocumentFromS3(s3Url);
            return new ByteArrayInputStream(bytes);
        } catch (Exception e) {
            log.error("Error downloading file from S3: {}", s3Url, e);
            throw new RuntimeException("Failed to download file: " + e.getMessage(), e);
        }
    }

    /**
     * Deletes a file from S3.
     *
     * @param s3Url The S3 URL of the file to delete
     */
    public void deleteFile(String s3Url) {
        try {
            // Delete the file from S3 using the deleteDocument method
            Boolean deleteSuccess = s3DomainService.deleteDocument(s3Url);

            if (deleteSuccess) {
                log.info("File deleted from S3: {}", s3Url);
            } else {
                log.error("Failed to delete file from S3: {}", s3Url);
                throw new RuntimeException("Failed to delete file from S3");
            }
        } catch (Exception e) {
            log.error("Error deleting file from S3: {}", s3Url, e);
            throw new RuntimeException("Failed to delete file: " + e.getMessage(), e);
        }
    }
}
