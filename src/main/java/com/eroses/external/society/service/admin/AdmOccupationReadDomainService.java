package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmOccupationDao;
import com.eroses.external.society.model.lookup.AdmOccupation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmOccupationReadDomainService {
    private final AdmOccupationDao admOccupationDao;

    public AdmOccupation findById(Long id) {
        return admOccupationDao.findById(id);
    }

    public List<AdmOccupation> getAll() {
        return admOccupationDao.getAll();
    }

    public List<AdmOccupation> getAllActive() {
        return admOccupationDao.getAllActive();
    }

    public List<AdmOccupation> getAll(String nameQuery, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "nameQuery", nameQuery,
                "offset", offset,
                "limit", limit
        );
        return admOccupationDao.getAll(params);
    }

    public Long countAll(String nameQuery) {
        Map<String, Object> params = Map.of(
                "nameQuery", nameQuery
        );
        return admOccupationDao.countAll(params);
    }

    public boolean existsByCode(String code) {
        return admOccupationDao.existsByCode(code);
    }

    public boolean existsByCodeExcludingId(String code, Long id) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("id", id);
        return admOccupationDao.existsByCodeExcludingId(params);
    }
}
