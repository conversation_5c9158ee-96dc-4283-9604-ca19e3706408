package com.eroses.external.society.service.training;

import com.eroses.external.society.dto.response.training.QuizOptionResponse;
import com.eroses.external.society.dto.response.training.QuizQuestionResponse;
import com.eroses.external.society.mappers.QuizAttemptDao;
import com.eroses.external.society.mappers.QuizOptionDao;
import com.eroses.external.society.mappers.QuizQuestionDao;
import com.eroses.external.society.mappers.TrainingQuizDao;
import com.eroses.external.society.model.QuizAttempt;
import com.eroses.external.society.model.QuizOption;
import com.eroses.external.society.model.QuizQuestion;
import com.eroses.external.society.model.TrainingQuiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class TrainingQuizReadDomainService {

    private final TrainingQuizDao trainingQuizDao;
    private final QuizQuestionDao quizQuestionDao;
    private final QuizOptionDao quizOptionDao;
    private final QuizAttemptDao quizAttemptDao;

    public TrainingQuiz getTrainingQuizById(Long id) {
        return trainingQuizDao.findById(id);
    }

    public TrainingQuiz getTrainingQuizByCourseId(Long trainingCourseId) {
        return trainingQuizDao.findByCourseId(trainingCourseId);
    }

    public List<QuizQuestion> getAllQuizQuestionsByQuizId(Long trainingQuizId) {
        List<QuizQuestion> questions = quizQuestionDao.findAllByQuizId(trainingQuizId);

        // Load options for each question
        for (QuizQuestion question : questions) {
            List<QuizOption> options = quizOptionDao.findAllByQuestionId(question.getId());
            question.setOptions(options);
        }

        return questions;
    }

    public List<QuizQuestionResponse> getQuizQuestionsWithOptions(Long trainingQuizId) {
        List<QuizQuestion> questions = getAllQuizQuestionsByQuizId(trainingQuizId);

        return questions.stream().map(question -> {
            List<QuizOptionResponse> optionResponses = question.getOptions().stream()
                    .map(option -> QuizOptionResponse.builder()
                            .id(option.getId())
                            .quizQuestionId(option.getQuizQuestionId())
                            .optionText(option.getOptionText())
                            .isCorrect(option.getIsCorrect())
                            .sequenceOrder(option.getSequenceOrder())
                            .createdBy(option.getCreatedBy())
                            .createdDate(option.getCreatedDate())
                            .build())
                    .collect(Collectors.toList());

            // Randomize options order for security
            //Collections.shuffle(optionResponses);

            return QuizQuestionResponse.builder()
                    .id(question.getId())
                    .trainingQuizId(question.getTrainingQuizId())
                    .questionText(question.getQuestionText())
                    .questionType(question.getQuestionType())
                    .sequenceOrder(question.getSequenceOrder())
                    .createdBy(question.getCreatedBy())
                    .createdDate(question.getCreatedDate())
                    .options(optionResponses)
                    .build();
        }).collect(Collectors.toList());
    }

    public QuizQuestion getQuizQuestionById(Long id) {
        QuizQuestion question = quizQuestionDao.findById(id);
        if (question != null) {
            List<QuizOption> options = quizOptionDao.findAllByQuestionId(question.getId());
            question.setOptions(options);
        }
        return question;
    }

    public QuizOption getQuizOptionById(Long id) {
        return quizOptionDao.findById(id);
    }

    public boolean isCorrectAnswer(Long questionId, Long optionId) {
        QuizOption option = quizOptionDao.findById(optionId);
        return option != null && option.getQuizQuestionId().equals(questionId) && option.getIsCorrect();
    }

    public List<QuizOption> getAllQuizOptionsByQuestionId(Long quizQuestionId) {
        return quizOptionDao.findAllByQuestionId(quizQuestionId);
    }

    public QuizOption getCorrectOptionByQuestionId(Long quizQuestionId) {
        return quizOptionDao.findCorrectOptionByQuestionId(quizQuestionId);
    }

    public QuizAttempt getQuizAttemptById(Long id) {
        return quizAttemptDao.findById(id);
    }

    public int countAttemptsByEnrollmentIdAndQuizId(Long enrollmentId, Long quizId) {
        return quizAttemptDao.countAttemptsByEnrollmentIdAndQuizId(enrollmentId, quizId);
    }
}
