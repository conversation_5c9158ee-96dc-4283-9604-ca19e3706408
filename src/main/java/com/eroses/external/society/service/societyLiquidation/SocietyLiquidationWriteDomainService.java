package com.eroses.external.society.service.societyLiquidation;

import com.eroses.external.society.api.converter.input.societyLiquidation.AssetApiInputConverter;
import com.eroses.external.society.api.converter.input.societyLiquidation.SocietyLiquidationApiInputConverter;
import com.eroses.external.society.api.converter.output.societyLiquidation.AssetApiOutputConverter;
import com.eroses.external.society.api.converter.output.societyLiquidation.SocietyLiquidationOutputConverter;
import com.eroses.external.society.dto.request.societyLiquidation.*;
import com.eroses.external.society.dto.response.societyLiquidation.LiquidationAssetResponse;
import com.eroses.external.society.dto.response.societyLiquidation.SocietyLiquidationUpdateResponse;
import com.eroses.external.society.mappers.SocietyLiquidationAssetDao;
import com.eroses.external.society.mappers.SocietyLiquidationDao;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.model.societyLiquidation.SocietyLiquidation;
import com.eroses.external.society.model.societyLiquidation.SocietyLiquidationAsset;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyLiquidationWriteDomainService {
    private final SocietyLiquidationDao societyLiquidationDao;
    private final SocietyLiquidationAssetDao assetDao;
    private final SocietyLiquidationApiInputConverter converter;
    private final AssetApiInputConverter assetApiInputConverter;
    private final AssetApiOutputConverter assetApiOutputConverter;
    private final SocietyLiquidationOutputConverter societyLiquidationOutputConverter;
    private final UserFacade userFacade;
    private final SocietyLiquidationAssetWriteDomainService assetWriteDomainService;

    public Long create(SocietyLiquidation societyLiquidation) throws Exception {
        societyLiquidationDao.create(societyLiquidation);
        return societyLiquidation.getId();
    }

    public SocietyLiquidationUpdateResponse update(SocietyLiquidationUpdateRequest request) throws Exception {
        SocietyLiquidation societyLiquidation = converter.convertToModel(request);

//        if (Objects.equals(request.getApplicationStatusCode(), Status.KUIRI.getCode())) {
//            societyLiquidation.setApplicationStatusCode(Status.MENUNGGU_KEPUTUSAN.getCode());
//        } else if (Objects.equals(request.getApplicationStatusCode(), Status.BELUM_DIHANTAR.getCode())) {
//            societyLiquidation.setApplicationStatusCode(Status.MENUNGGU_KEPUTUSAN.getCode());
//        }

        societyLiquidationDao.update(societyLiquidation);

        SocietyLiquidationUpdateResponse societyLiquidationUpdateResponse = societyLiquidationOutputConverter.toModel(societyLiquidation);
        List<LiquidationAssetResponse> assetResponses = handleAssetEdit(societyLiquidation, request.getAssets(), userFacade.me());
        societyLiquidationUpdateResponse.setAssets(assetResponses);

        return societyLiquidationUpdateResponse;
    }

    public boolean update(SocietyLiquidation societyLiquidation) throws Exception {
        return societyLiquidationDao.update(societyLiquidation);
    }

    public boolean submit(SocietyLiquidation societyLiquidation) throws Exception {
        return societyLiquidationDao.submit(societyLiquidation);
    }

    public void delete(LiquidationDeleteRequest request) throws Exception {
        SocietyLiquidation societyLiquidation = converter.convertToModel(request);
        societyLiquidation.setApplicationStatusCode(ApplicationStatusCode.PADAM.getCode());
        societyLiquidationDao.update(societyLiquidation);
    }

    private List<LiquidationAssetResponse> handleAssetEdit(
            SocietyLiquidation societyLiquidation,
            List<LiquidationAssetUpdateRequest> assetUpdateRequests,
            User me
    ) throws Exception {
        if (assetUpdateRequests == null || assetUpdateRequests.isEmpty()) {
            return null;
        }

        List<LiquidationAssetResponse> assetResponses = new ArrayList<>();

        for (LiquidationAssetUpdateRequest updateRequest : assetUpdateRequests) {
            SocietyLiquidationAsset asset = assetApiInputConverter.toModel(updateRequest);

            // Add new asset
            if (asset.getId() == null) {
                asset.setIcNo(me.getIdentificationNo());
                asset.setCreatedBy(me.getId());

                asset.setLiquidationId(societyLiquidation.getId());
                asset.setSocietyId(societyLiquidation.getSocietyId());
                asset.setSocietyNo(societyLiquidation.getSocietyNo());
                asset.setBranchId(societyLiquidation.getBranchId());
                asset.setSocietyNo(societyLiquidation.getSocietyNo());

                assetDao.create(asset);
            } else {
                assetDao.update(asset);
            }
            LiquidationAssetResponse assetResponse = assetApiOutputConverter.toModel(asset);
            assetResponses.add(assetResponse);
        }

        return assetResponses;
    }

    public SocietyLiquidation updateRoDecision(SocietyLiquidation liquidation, int applicationStatusCode) throws Exception {
        // Update application status code and status based on RO decision
        liquidation.setApplicationStatusCode(applicationStatusCode);
        liquidation.setDecisionDate(LocalDate.now());
        societyLiquidationDao.update(liquidation);
        return liquidation;
    }
}
