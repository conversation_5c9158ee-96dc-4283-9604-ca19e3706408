package com.eroses.external.society.service.document.strategy.impl;

import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.*;
import com.eroses.external.society.service.*;
import com.eroses.external.society.service.document.strategy.AbstractDocumentMappingStrategy;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.service.utils.HtmlGeneratorService;
import com.eroses.user.api.facade.UserFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.util.HtmlUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PerlembagaanAktifStrategy extends AbstractDocumentMappingStrategy {

    private final AmendmentReadDomainService amendmentReadDomainService;
    private final ConstitutionContentReadDomainService constitutionContentReadDomainService;

    public PerlembagaanAktifStrategy(DocumentTemplateReadDomainService documentTemplateReadDomainService,
                                     S3DomainService s3DomainService,
                                     PdfService pdfService,
                                     HtmlGeneratorService htmlGeneratorService,
                                     UserFacade userFacade,
                                     SocietyReadDomainService societyReadDomainService,
                                     AmendmentReadDomainService amendmentReadDomainService,
                                     ConstitutionContentReadDomainService constitutionContentReadDomainService) {
        super(documentTemplateReadDomainService, s3DomainService, pdfService, htmlGeneratorService, userFacade, societyReadDomainService);
        this.amendmentReadDomainService = amendmentReadDomainService;
        this.constitutionContentReadDomainService = constitutionContentReadDomainService;
    }

    @Override
    public String mapDataToDocument(String templateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        DocumentTemplate template = getDocumentTemplate(templateCode);
        String htmlTemplate = template.getHtmlContent();

        Map<String, String> mapFields = prepareDataForMapping(societyId, additionalParams);
        String processedHtml = htmlGeneratorService.processConditionals(htmlTemplate, mapFields);
        return htmlGeneratorService.mapToHtml(processedHtml, mapFields);
    }

    @Override
    protected Map<String, String> prepareDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {
        Society society = societyReadDomainService.findById(societyId);
        String societyNo = society.getSocietyNo() != null ? society.getSocietyNo() : society.getApplicationNo();

        Map<String, String> mapFields = new HashMap<>();
        mapFields.put("{{NAMA_PERTUBUHAN}}", getValueOrDefault(society.getSocietyName().toUpperCase()));
        mapFields.put("{{NO_PERTUBUHAN}}", "(" + getValueOrDefault(societyNo.toUpperCase()) + ")");

        // Process the constitution body
        String constitutionBody = "";
        if (additionalParams.get("amendmentId") != null) {
            Amendment amendment = amendmentReadDomainService.findById((Long) additionalParams.get("amendmentId"));
            constitutionBody = processAmendedConstitutionBody(society, amendment);
        } else {
            constitutionBody = processConstitutionBody(society);
        }
        mapFields.put("{{CONSTITUTION_BODY}}", constitutionBody);

        return mapFields;
    }

    @Override
    public boolean canHandle(String templateCode) {
        return DocumentTemplateEnum.PERLEMBAGAAN_AKTIF.getCode().equals(templateCode);
    }

    @Override
    public String getFileName(DocumentTemplate documentTemplate, Long societyId, Map<String, Object> additionalParams) throws Exception {
        Society society = societyReadDomainService.findById(societyId);
        if (additionalParams.get("amendmentId") != null) {
            return documentTemplate.getTemplateName() + " " + society.getSocietyName() + " (" + LocalDate.now() + " - PINDA)";
        }
        return documentTemplate.getTemplateName() + " " + society.getSocietyName() + " (" + LocalDate.now() + ")";
    }

    private String processConstitutionBody(Society society) {
        List<ConstitutionContent> constitutionContentList;

        if (!Objects.equals(society.getConstitutionType(), ConstitutionTypeEnum.Bebas.getCode()) &&
                !Objects.equals(society.getConstitutionType(), ConstitutionTypeEnum.BebasCawangan.getCode())) {
            constitutionContentList = constitutionContentReadDomainService.getAllBySocietyId(
                            society.getId(), StatusCode.AKTIF.getCode(), ApplicationStatusCode.AKTIF.getCode())
                    .stream()
                    .filter(content -> content.getHideConstitution() == null || !content.getHideConstitution())
                    .sorted(Comparator.comparing(
                            ConstitutionContent::getClauseNo,
                            Comparator.nullsLast(Comparator.comparingInt(Integer::parseInt))
                    ))
                    .collect(Collectors.toList());
        } else {
            Map<String, Object> params = new HashMap<>();
            params.put("societyId", society.getId());
            params.put("status", StatusCode.AKTIF.getCode());
            params.put("applicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
            constitutionContentList = constitutionContentReadDomainService.findByParams(params)
                    .stream()
                    .filter(content -> content.getHideConstitution() == null || !content.getHideConstitution())
                    .sorted(Comparator.comparing(
                            ConstitutionContent::getClauseNo,
                            Comparator.nullsLast(Comparator.comparingInt(Integer::parseInt))
                    ))
                    .collect(Collectors.toList());
        }


        //might be from registration so ApplicationStatusCode = 2
        if (constitutionContentList.isEmpty()) {
            Map<String, Object> params = new HashMap<>();
            params.put("societyId", society.getId());
            params.put("status", StatusCode.AKTIF.getCode());
            params.put("applicationStatusCode", ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode());
            constitutionContentList = constitutionContentReadDomainService.findByParams(params)
                    .stream()
                    .filter(content -> content.getHideConstitution() == null || !content.getHideConstitution())
                    .sorted(Comparator.comparing(
                            ConstitutionContent::getClauseNo,
                            Comparator.nullsLast(Comparator.comparingInt(Integer::parseInt))
                    ))
                    .collect(Collectors.toList());
        }

        return formatConstitutions(constitutionContentList);
    }

    private String processAmendedConstitutionBody(Society society, Amendment amendment) {
        List<ConstitutionContent> amendedConstitutions = new ArrayList<>();

        Map<String, Object> params = new HashMap<>();
        params.put("societyId", society.getId());
        params.put("amendmentId", amendment.getId());

        //check type first
        if (!amendment.getConstitutionType().equals(society.getConstitutionType())) {
            amendedConstitutions = constitutionContentReadDomainService.findByParams(params)
                    .stream()
                    .filter(content -> content.getHideConstitution() == null || !content.getHideConstitution())
                    .filter(content -> content.getApplicationStatusCode() != null && (content.getApplicationStatusCode() != ApplicationStatusCode.PADAM.getCode()))
                    .sorted(Comparator.comparing(
                            ConstitutionContent::getClauseNo,
                            Comparator.nullsLast(Comparator.comparingInt(Integer::parseInt))
                    ))
                    .collect(Collectors.toList());
        } else {
            List<ConstitutionContent> amendmentConstitutions = constitutionContentReadDomainService.findByParams(params);

            List<ConstitutionContent> currentConstitutions = constitutionContentReadDomainService.getAllBySocietyId(
                    society.getId(), StatusCode.AKTIF.getCode(), ApplicationStatusCode.AKTIF.getCode());

            boolean isBebas = Objects.equals(amendment.getConstitutionType(), ConstitutionTypeEnum.Bebas.getCode()) ||
                    Objects.equals(amendment.getConstitutionType(), ConstitutionTypeEnum.BebasCawangan.getCode());

            Map<Long, ConstitutionContent> amendedMap = amendmentConstitutions.stream()
                    .collect(Collectors.toMap(
                            c -> isBebas ? Long.parseLong(c.getClauseNo()) : c.getClauseContentId(),
                            Function.identity()
                    ));

            amendedConstitutions = currentConstitutions.stream()
                    .map(content -> amendedMap.getOrDefault(
                            isBebas ? Long.parseLong(content.getClauseNo()) : content.getClauseContentId(),
                            content
                    ))
                    .filter(content -> content.getHideConstitution() == null || !content.getHideConstitution())
                    .filter(content -> content.getApplicationStatusCode() != null &&
                            content.getApplicationStatusCode() != ApplicationStatusCode.PADAM.getCode())
                    .sorted(isBebas
                            ? Comparator.comparingInt(c -> Integer.parseInt(c.getClauseNo()))
                            : Comparator.comparing(ConstitutionContent::getClauseContentId)
                    )
                    .toList();
        }

        return formatConstitutions(amendedConstitutions);
    }

    public String formatConstitutions(List<ConstitutionContent> constitutionContents) {
        if (constitutionContents == null || constitutionContents.isEmpty()) {
            return "<p>Tiada maklumat perlembagaan.</p>";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("<div style='margin-top:20px;'>");

        for (ConstitutionContent constitution : constitutionContents) {
            String clauseNumber = "";
            String clauseName = "";
            String constitutionDescription = "";

            if (constitution.getClauseContentId() != null) {
                ClauseEnum clauseEnum = ClauseEnum.getClause(Math.toIntExact(constitution.getClauseContentId()));
                if (clauseEnum != null) {
                    clauseNumber = String.valueOf(clauseEnum.getClauseNo());
                    clauseName = clauseEnum.getClauseName();
                }
            } else {
                clauseNumber = constitution.getClauseNo() != null ? constitution.getClauseNo() : "";
                clauseName = constitution.getClauseName() != null ? constitution.getClauseName() : "";
            }

            constitutionDescription = constitution.getDescription() != null
                    ? constitution.getDescription()
                    : "Tiada Maklumat Perihal";

            String header = "<tr>"
                    + "<td style='vertical-align: top; padding-right: 10px; white-space: nowrap;'>"
                    + "FASAL " + clauseNumber
                    + "</td>"
                    + "<td style='vertical-align: top;'>"
                    + "<strong>" + clauseName.toUpperCase() + "</strong>";

            // Clean and format content
            String formattedDescription = Arrays.stream(constitutionDescription.split("\n"))
                    .map(line -> {
                        if (line.isBlank()) {
                            return "<div style='height: 1em;'>&nbsp;</div>";
                        } else if (line.matches(".*<.*?>.*")) {
                            // contains HTML tags like <strong>, <em>, etc.
                            return "<div style='white-space: normal;'>" + line + "</div>";
                        } else {
                            return "<div style='white-space: pre-wrap;'>" + line + "</div>";
                        }
                    })
                    .collect(Collectors.joining());

            formattedDescription =
                    "<div style='font-family: Arial; font-size: 14px;'>"
                            + "<div style='margin: 15px; font-family: inherit; line-height: 1.4; padding-right: 10px; overflow-wrap: break-word; word-break: break-word;'>"
                            + formattedDescription
                            + "</div></div>";

            sb.append("<table style='border-collapse: collapse;'>")
                    .append(header)
                    .append(formattedDescription)
                    .append("</td>")
                    .append("</tr>")
                    .append("</table>")
                    .append("<br />");
        }

        sb.append("</div>");

        //sanitize first
        sb = new StringBuilder(replaceHtmlNamedEntities(sb.toString()));

        return sb.toString();
    }

    private static String replaceHtmlNamedEntities(String html) {
        return html
                .replace("&ldquo;", "&#8220;")
                .replace("&rdquo;", "&#8221;")
                .replace("&lsquo;", "&#8216;")
                .replace("&rsquo;", "&#8217;")
                .replace("&ndash;", "&#8211;")
                .replace("&mdash;", "&#8212;")
                .replace("&nbsp;", "&#160;")
                .replace("&hellip;", "&#8230;")
                .replace("&copy;", "&#169;")
                .replace("&reg;", "&#174;");
    }
}