package com.eroses.external.society.service;

import com.eroses.external.society.mappers.DocumentDao;
import com.eroses.external.society.model.Document;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentReadDomainService {
    private final S3DomainService s3DomainService;
    private final DocumentDao documentDao;

    public List<Document> getAllDocument() {
        return documentDao.findAll();
    }

    public List<Document> findByBranchId(Long branchId) { return documentDao.findByBranchId(branchId); }

    public Document findDocumentById(Long id) {
        return documentDao.findById(id);
    }

    public List<Document> findDocumentByParam(Map<String, Object> param) {
        return documentDao.findByParam(param);
    }

    public Document findDocumentByParamSingle(Map<String, Object> param) {
        return documentDao.findByParamSingle(param);
    }

    public List<Document> findByDocumentTypeAndSocietyId(String documentType, Long societyId) {
        Map<String, Object> params = Map.of(
                "type", documentType,
                "societyId", societyId
                );
        return documentDao.findByParam(params);
    }

    public List<Document> getAllByCodeAndSocietyId(String code, Long societyId) {
        Map<String, Object> params = Map.of(
                "code", code,
                "societyId", societyId
                );
        return documentDao.findByParam(params);
    }

    public Document getByCodeAndSocietyId(String code, Long societyId) {
        Map<String, Object> params = Map.of(
                "code", code,
                "societyId", societyId
        );
        return documentDao.findByParamSingle(params);
    }

    public List<Document> findDocumentByIdListAndStatus(List<Long> documentId, Long applicationStatusCode) {
        if (applicationStatusCode == null) {
            applicationStatusCode = 1L;
        }

        Map<String, Object> params = Map.of(
                "ids", documentId,
                "applicationStatusCode", applicationStatusCode
        );
        return documentDao.findDocumentByIdListAndStatus(params);
    }

    public List<Document> findDocumentsByTypeAndSocietyIdAndLiquidationId(String type, Long societyId, Long liquidationId) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "type", type,
                "liquidationId", liquidationId
        );
        return documentDao.findByParam(params);
    }

    public List<Document> findDocumentsByTypeAndSocietyIdAndLiquidationIdAndCode(String type, Long societyId, Long liquidationId, String code) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "type", type,
                "liquidationId", liquidationId,
                "code", code
        );
        return documentDao.findByParam(params);
    }

    public Long countFindDocumentByParam(Map<String, Object> param) {
        return documentDao.countFindDocumentByParam(param);
    }
}
