package com.eroses.external.society.service;

import com.eroses.external.society.mappers.RoAmendmentApprovalDao;
import com.eroses.external.society.model.RoAmendmentApproval;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class RoAmendmentApprovalReadDomainService {

    private final RoAmendmentApprovalDao roAmendmentApprovalDao;

    public List<RoAmendmentApproval> get(Map<String, Object> param) {
        return roAmendmentApprovalDao.get(param);
    }

    public RoAmendmentApproval getById(Long id) {
        return roAmendmentApprovalDao.getById(id);
    }
}
