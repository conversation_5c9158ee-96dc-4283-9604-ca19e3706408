package com.eroses.external.society.service.document.strategy.impl;

import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.*;
import com.eroses.external.society.model.lookup.AdmInsolvencyDepartment;
import com.eroses.external.society.service.*;
import com.eroses.external.society.service.admin.AdmInsolvencyDepartmentReadDomainService;
import com.eroses.external.society.service.document.strategy.AbstractDocumentMappingStrategy;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.service.utils.HtmlGeneratorService;
import com.eroses.external.society.utils.RoDecisionUtilities;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Strategy implementation for Section 13(1)(a), 13(1)(b), and 13(1)(c) document type.
 */
@Slf4j
@Component
public class Section131ABCStrategy extends AbstractDocumentMappingStrategy {

    private final CommitteeReadDomainService committeeReadDomainService;
    private final BranchCommitteeReadDomainService branchCommitteeReadDomainService;
    private final AdmInsolvencyDepartmentReadDomainService admInsolvencyDepartmentReadDomainService;
    private final RoDecisionUtilities roDecisionUtilities;

    public Section131ABCStrategy(
            DocumentTemplateReadDomainService documentTemplateReadDomainService,
            S3DomainService s3DomainService,
            PdfService pdfService,
            HtmlGeneratorService htmlGeneratorService,
            UserFacade userFacade,
            SocietyReadDomainService societyReadDomainService,
            CommitteeReadDomainService committeeReadDomainService,
            BranchCommitteeReadDomainService branchCommitteeReadDomainService,
            AdmInsolvencyDepartmentReadDomainService admInsolvencyDepartmentReadDomainService,
            RoDecisionUtilities roDecisionUtilities) {
        super(documentTemplateReadDomainService, s3DomainService, pdfService, htmlGeneratorService, userFacade,
                societyReadDomainService);
        this.committeeReadDomainService = committeeReadDomainService;
        this.branchCommitteeReadDomainService = branchCommitteeReadDomainService;
        this.admInsolvencyDepartmentReadDomainService = admInsolvencyDepartmentReadDomainService;
        this.roDecisionUtilities = roDecisionUtilities;
    }

    @Override
    public String getFileName(DocumentTemplate documentTemplate, Long societyId, Map<String, Object> additionalParams) throws Exception {
        Society society = societyReadDomainService.findById(societyId);
        SocietyCancellationDetailSectionEnum sectionEnum = (SocietyCancellationDetailSectionEnum) additionalParams.get("sectionEnum");
        return "Seksyen" + " " + sectionEnum.getDescription() + " " + society.getSocietyNo();
    }

    @Override
    public String mapDataToDocument(String templateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        DocumentTemplate template = getDocumentTemplate(templateCode);
        String htmlTemplate = template.getHtmlContent();

        Map<String, String> mapFields = prepareDataForMapping(societyId, additionalParams);
        return htmlGeneratorService.mapToHtml(htmlTemplate, mapFields);
    }

    @Override
    protected Map<String, String> prepareDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {

        Society society = societyReadDomainService.findById(societyId);
        SocietyCategoryEnum societyCategory = SocietyCategoryEnum.getCategoryById(Integer.valueOf(society.getCategoryCodeJppm()));
        SocietyCancellationDetailSectionEnum sectionEnum = (SocietyCancellationDetailSectionEnum) additionalParams.get("sectionEnum");
        LocalDate cancellationDate = (LocalDate) additionalParams.get("cancellationDate");
        boolean isBranchCancellation = (boolean) additionalParams.get("isBranchCancellation");

        String secretaryAddress = null;
        String secretaryPostcode = null;
        String secretaryCity = null;
        String secretaryStateCode = null;
        String presidentAddress = null;
        String presidentPostcode = null;
        String presidentCity = null;
        String presidentStateCode = null;
        String treasurerAddress = null;
        String treasurerPostcode = null;
        String treasurerCity = null;
        String treasurerStateCode = null;

        if (isBranchCancellation) {
            Long branchId = (Long) additionalParams.get("branchId");

            BranchCommittee secretary = branchCommitteeReadDomainService.findActiveCommitteeInBranchWithRoles(Position.getAllSecretaryPositions().stream().map(Position::getCode).map(String::valueOf).toList(), branchId);
            BranchCommittee president = branchCommitteeReadDomainService.findActiveCommitteeInBranchWithRoles(Position.getAllPresidentPositions().stream().map(Position::getCode).map(String::valueOf).toList(), branchId);
            BranchCommittee treasurer = branchCommitteeReadDomainService.findActiveCommitteeInBranchWithRoles(Position.getAllTreasurerPositions().stream().map(Position::getCode).map(String::valueOf).toList(), branchId);

            if (secretary != null) secretaryAddress = secretary.getCommitteeAddress();
            if (secretary != null) secretaryPostcode = secretary.getPostcode();
            if (secretary != null) secretaryCity = secretary.getCommitteeCity();
            if (secretary != null) secretaryStateCode = secretary.getCommitteeStateCode();
            if (president != null) presidentAddress = president.getCommitteeAddress();
            if (president != null) presidentPostcode = president.getPostcode();
            if (president != null) presidentCity = president.getCommitteeCity();
            if (president != null) presidentStateCode = president.getCommitteeStateCode();
            if (treasurer != null) treasurerAddress = treasurer.getCommitteeAddress();
            if (treasurer != null) treasurerPostcode = treasurer.getPostcode();
            if (treasurer != null) treasurerCity = treasurer.getCommitteeCity();
            if (treasurer != null) treasurerStateCode = treasurer.getCommitteeStateCode();
        }else{
            Committee secretary = committeeReadDomainService.findActiveCommitteeInSocietyWithRoles(Position.getAllSecretaryPositions().stream().map(Position::getCode).map(String::valueOf).toList(), societyId);
            Committee president = committeeReadDomainService.findActiveCommitteeInSocietyWithRoles(Position.getAllPresidentPositions().stream().map(Position::getCode).map(String::valueOf).toList(), societyId);
            Committee treasurer = committeeReadDomainService.findActiveCommitteeInSocietyWithRoles(Position.getAllTreasurerPositions().stream().map(Position::getCode).map(String::valueOf).toList(), societyId);

            if (secretary != null) secretaryAddress = secretary.getResidentialAddress();
            if (secretary != null) secretaryPostcode = secretary.getResidentialPostcode();
            if (secretary != null) secretaryCity = secretary.getResidentialCity();
            if (secretary != null) secretaryStateCode = secretary.getResidentialStateCode();
            if (president != null) presidentAddress = president.getResidentialAddress();
            if (president != null) presidentPostcode = president.getResidentialPostcode();
            if (president != null) presidentCity = president.getResidentialCity();
            if (president != null) presidentStateCode = president.getResidentialStateCode();
            if (treasurer != null) treasurerAddress = treasurer.getResidentialAddress();
            if (treasurer != null) treasurerPostcode = treasurer.getResidentialPostcode();
            if (treasurer != null) treasurerCity = treasurer.getResidentialCity();
            if (treasurer != null) treasurerStateCode = treasurer.getResidentialStateCode();
        }

        String stateCode = roDecisionUtilities.getAssignedResponsibleStateCode(society.getStateCode(), societyCategory);
        AdmInsolvencyDepartment admInsolvencyDepartment = admInsolvencyDepartmentReadDomainService.findByStateCode(stateCode);

        String societyStateName = getStateName(society.getStateCode());
        String secretaryStateName = getStateName(secretaryStateCode);
        String presidentStateName = getStateName(presidentStateCode);
        String treasurerStateName = getStateName(treasurerStateCode);
        String insolvencyDepartmentStateName = getStateName(admInsolvencyDepartment.getStateCode());

        //Get State Director of the state (Should have only 1 state director per state)
        List<User> stateDirectors = roDecisionUtilities.getAssignedApprovalOfficer(UserRoleEnum.PENOLONG_PENDAFTAR_PERTUBUHAN, society.getStateCode(), societyCategory);
        String directorName = stateDirectors.isEmpty() ? "-" : stateDirectors.getFirst().getName();

        return getMapFields(society, societyStateName, sectionEnum, cancellationDate,
                secretaryAddress, secretaryPostcode, secretaryCity, secretaryStateName,
                presidentAddress, presidentPostcode, presidentCity, presidentStateName,
                treasurerAddress, treasurerPostcode, treasurerCity, treasurerStateName,
                admInsolvencyDepartment, insolvencyDepartmentStateName, directorName);
    }

    @Override
    public boolean canHandle(String templateCode) {
        return DocumentTemplateEnum.SECTION_13_1_A_B_C.getCode().equals(templateCode);
    }

    private static Map<String, String> getMapFields(Society society, String societyStateName,
                                                    SocietyCancellationDetailSectionEnum sectionEnum,
                                                    LocalDate cancellationDate,
                                                    String secretaryAddress, String secretaryPostcode, String secretaryCity,
                                                    String secretaryStateName,
                                                    String presidentAddress, String presidentPostcode, String presidentCity,
                                                    String presidentStateName,
                                                    String treasurerAddress, String treasurerPostcode, String treasurerCity,
                                                    String treasurerStateName,
                                                    AdmInsolvencyDepartment admInsolvencyDepartment, String insolvencyDepartmentStateName,
                                                    String directorName) {
        Map<String, String> mapFields = new HashMap<>();
        mapFields.put("{{SOCIETY_NO}}", getValueOrDefault(society.getSocietyNo()).toUpperCase());
        mapFields.put("{{SECTION}}", getValueOrDefault(sectionEnum.getDescription()));

        mapFields.put("{{SOCIETY_NAME}}", getValueOrDefault(society.getSocietyName()).toUpperCase());
        mapFields.put("{{SOCIETY_ADDRESS}}", getValueOrDefault(society.getAddress()).toUpperCase());
        mapFields.put("{{SOCIETY_POSTCODE}}", getValueOrDefault(society.getPostcode()));
        mapFields.put("{{SOCIETY_CITY}}", getValueOrDefault(society.getCity()));
        mapFields.put("{{SOCIETY_STATE_NAME}}", getValueOrDefault(societyStateName));

        mapFields.put("{{CANCELLATION_DATE}}", formatDateOrDefault(cancellationDate, "-"));
        mapFields.put("{{CANCELLATION_DAY}}", getValueOrDefault(String.valueOf(cancellationDate.getDayOfMonth())));
        mapFields.put("{{CANCELLATION_MONTH}}", getValueOrDefault(cancellationDate.getMonth().getDisplayName(TextStyle.FULL, Locale.forLanguageTag("ms"))).toUpperCase());
        mapFields.put("{{CANCELLATION_YEAR}}", getValueOrDefault(String.valueOf(cancellationDate.getYear())));

        mapFields.put("{{SECRETARY_ADDRESS}}", getValueOrDefault(secretaryAddress));
        mapFields.put("{{SECRETARY_POSTCODE}}", getValueOrDefault(secretaryPostcode));
        mapFields.put("{{SECRETARY_CITY}}", getValueOrDefault(secretaryCity));
        mapFields.put("{{SECRETARY_STATE_NAME}}", getValueOrDefault(secretaryStateName));

        mapFields.put("{{PRESIDENT_ADDRESS}}", getValueOrDefault(presidentAddress));
        mapFields.put("{{PRESIDENT_POSTCODE}}", getValueOrDefault(presidentPostcode));
        mapFields.put("{{PRESIDENT_CITY}}", getValueOrDefault(presidentCity));
        mapFields.put("{{PRESIDENT_STATE_NAME}}", getValueOrDefault(presidentStateName));

        mapFields.put("{{TREASURER_ADDRESS}}", getValueOrDefault(treasurerAddress));
        mapFields.put("{{TREASURER_POSTCODE}}", getValueOrDefault(treasurerPostcode));
        mapFields.put("{{TREASURER_CITY}}", getValueOrDefault(treasurerCity));
        mapFields.put("{{TREASURER_STATE_NAME}}", getValueOrDefault(treasurerStateName));

        mapFields.put("{{INSOLVENCY_DEPARTMENT_ADDRESS}}", getValueOrDefault(admInsolvencyDepartment.getAddress()));
        mapFields.put("{{INSOLVENCY_DEPARTMENT_POSTCODE}}", getValueOrDefault(String.valueOf(admInsolvencyDepartment.getPostcode())));
        mapFields.put("{{INSOLVENCY_DEPARTMENT_CITY}}", getValueOrDefault(admInsolvencyDepartment.getCityCode()));
        mapFields.put("{{INSOLVENCY_DEPARTMENT_STATE_NAME}}", getValueOrDefault(insolvencyDepartmentStateName));

        mapFields.put("{{OFFICER_NAME}}", getValueOrDefault(directorName).toUpperCase());
        return mapFields;
    }
}