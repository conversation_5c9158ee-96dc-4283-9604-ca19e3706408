package com.eroses.external.society.service;

import com.eroses.external.society.api.converter.input.CommitteeArchiveConverter;
import com.eroses.external.society.mappers.SocietyCommitteeArchiveDao;
import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.Position;
import com.eroses.external.society.model.enums.StatusCode;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.utils.Assert;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyCommitteeArchiveWriteDomainService {

    private final SocietyReadDomainService societyReadDomainService;
    private final SocietyCommitteeArchiveDao societyCommitteeArchiveDao;
    private final MeetingReadDomainService meetingReadDomainService;
    private final CommitteeArchiveConverter committeeArchiveConverter;
    private final CommitteeDraftWriteDomainService committeeDraftWriteDomainService;

    public Boolean create(SocietyCommitteeArchive societyCommitteeArchive) throws Exception {
        Boolean isCreated = societyCommitteeArchiveDao.create(societyCommitteeArchive);
        Assert.isTrue(isCreated, "Creating archive is unsuccessful.");

        return isCreated;
    }

    public Boolean update(SocietyCommitteeArchive societyCommitteeArchive) throws Exception {
        Boolean isUpdated = societyCommitteeArchiveDao.update(societyCommitteeArchive);
        Assert.isTrue(isUpdated, "Updating archive is unsuccessful.");

        return isUpdated;
    }

    public Map<String, Object> addCommitteeListIntoArchive(List<SocietyCommitteeArchive> newSocietyCommitteeArchive, User me, Long meetingId) throws Exception {

        Meeting meeting = meetingReadDomainService.findById(meetingId);
        Map<String, Object> errorMap = new HashMap<>();

        for (SocietyCommitteeArchive eachArchive : newSocietyCommitteeArchive) {
            eachArchive.setStatus(StatusCode.TIDAK_AKTIF.getCode());
            eachArchive.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.INAKTIF.getCode()));
            eachArchive.setMarkedDate(meeting.getMeetingDate());
            eachArchive.setCreatedBy(me.getId());

            Boolean isCreated = societyCommitteeArchiveDao.create(eachArchive);
            if (!isCreated) {
                errorMap.put("Error inserting committee into archive for ID: " + eachArchive.getId(), eachArchive.getName());
            }
        }

        return errorMap;
    }

    public Boolean addSocietyCommitteeListIntoArchive(List<Committee> currentCommitteeList, List<CommitteeDraft> committeeDraftList) {
        try {
            Map<Long, CommitteeDraft> draftMap = committeeDraftList.stream()
                    .filter(d -> d.getCommitteeTableOldId() != null)
                    .collect(Collectors.toMap(CommitteeDraft::getCommitteeTableOldId, c -> c));

            Map<String, List<CommitteeDraft>> draftDesignationCodeMap = committeeDraftList.stream()
                    .collect(Collectors.groupingBy(CommitteeDraft::getDesignationCode));

            if (currentCommitteeList != null && !currentCommitteeList.isEmpty()) {
                for (Committee currentCommittee : currentCommitteeList) {
                    CommitteeDraft committeeDraft = draftMap.get(currentCommittee.getId());

                    if (committeeDraft == null) {
                        if (Position.noncommitteePositionCodes().contains(currentCommittee.getDesignationCode())) {
                            continue; // Skip irrelevant positions
                        }

                        List<CommitteeDraft> possibleDrafts = draftDesignationCodeMap.getOrDefault(currentCommittee.getDesignationCode(), new ArrayList<>());

                        Optional<CommitteeDraft> match = possibleDrafts.stream()
                                .filter(d -> d.getCommitteeTableOldId() == null)
                                .findFirst();

                        if (match.isPresent()) {
                            committeeDraft = match.get();
                            committeeDraft.setCommitteeTableOldId(currentCommittee.getId());
                            committeeDraftWriteDomainService.update(committeeDraft);
                        } else {
                            // No unlinked draft found → skip safely
                            continue;
                        }
                    }

                    SocietyCommitteeArchive societyCommitteeArchive = committeeArchiveConverter.convertCommitteeToSocietyCommitteeArchive(currentCommittee);
                    societyCommitteeArchive.setCommitteeTableOldId(currentCommittee.getId());
                    societyCommitteeArchive.setStatus(StatusCode.AKTIF.getCode());
                    societyCommitteeArchive.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                    societyCommitteeArchive.setMarkedDate(committeeDraft.getAppointedDate());
                    societyCommitteeArchive.setCreatedBy(committeeDraft.getCreatedBy());

                    //use params instead
                    Map<String, Object> params = new HashMap<>();
                    params.put("committeeTableOldId", currentCommittee.getId());
                    params.put("societyId", currentCommittee.getSocietyId());
                    if (currentCommittee.getAppointedDate() != null) params.put("appointedDate", currentCommittee.getAppointedDate());
                    if (committeeDraft.getAppointedDate() != null) params.put("markedDate", committeeDraft.getAppointedDate());
                    params.put("position", currentCommittee.getDesignationCode());
                    SocietyCommitteeArchive oldData = societyCommitteeArchiveDao.findByCommitteeOldId(currentCommittee.getId());
                    if (oldData != null) {
                        societyCommitteeArchive.setId(oldData.getId());
                        update(societyCommitteeArchive);
                    } else {
                        create(societyCommitteeArchive);
                    }
                }

                return true;
            } else {
                for (CommitteeDraft committeeDraft : committeeDraftList) {
                    SocietyCommitteeArchive societyCommitteeArchive = committeeArchiveConverter.convertCommitteeDraftToSocietyCommitteeArchive(committeeDraft);
                    societyCommitteeArchive.setStatus(StatusCode.AKTIF.getCode());
                    societyCommitteeArchive.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                    societyCommitteeArchive.setMarkedDate(committeeDraft.getAppointedDate());
                    societyCommitteeArchive.setCreatedBy(committeeDraft.getCreatedBy());

                    create(societyCommitteeArchive);

                    committeeDraft.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                    committeeDraft.setStatus(StatusCode.AKTIF.getCode());
                    committeeDraftWriteDomainService.update(committeeDraft);
                }

                return true;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
