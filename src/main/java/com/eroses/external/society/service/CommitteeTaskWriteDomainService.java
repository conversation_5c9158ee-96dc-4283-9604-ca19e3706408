package com.eroses.external.society.service;

import com.eroses.external.society.mappers.BranchCommitteeTaskConfigDao;
import com.eroses.external.society.mappers.CommitteeTaskDao;
import com.eroses.external.society.mappers.SocietyCommitteeTaskConfigDao;
import com.eroses.external.society.model.CommitteeTask;
import com.eroses.external.society.model.enums.CommitteeTaskModule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommitteeTaskWriteDomainService {
    private final CommitteeTaskDao committeeTaskDao;
    private final BranchCommitteeTaskConfigDao branchCommitteeTaskConfigDao;
    private final SocietyCommitteeTaskConfigDao societyCommitteeTaskConfigDao;

    public void create(CommitteeTask committeeTask) throws Exception {
        committeeTaskDao.create(committeeTask);
    }

    public boolean update(CommitteeTask committeeTask) throws Exception {
        return committeeTaskDao.update(committeeTask);
    }

    public boolean updateStatus(CommitteeTask committeeTask) throws Exception {
        return committeeTaskDao.updateStatus(committeeTask);
    }

    public boolean enableSocietyCommitteeTask(Long societyId, CommitteeTaskModule module) {
        return societyCommitteeTaskConfigDao.enableCommitteeTaskForModule(societyId, module) > 0;
    }

    public boolean disableSocietyCommitteeTask(Long societyId, CommitteeTaskModule module) {
        return societyCommitteeTaskConfigDao.disableCommitteeTaskForModule(societyId, module) > 0;
    }

    public boolean enableBranchCommitteeTask(Long branchId, CommitteeTaskModule module) {
        return branchCommitteeTaskConfigDao.enableCommitteeTaskForModule(branchId, module) > 0;
    }

    public boolean disableBranchCommitteeTask(Long branchId, CommitteeTaskModule module) {
        return branchCommitteeTaskConfigDao.disableCommitteeTaskForModule(branchId, module) > 0;
    }
}
