package com.eroses.external.society.service.Payment;

import com.eroses.external.society.mappers.PaymentCallbackDao;
import com.eroses.external.society.model.payment.PaymentCallback;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class PaymentCallbackWriteDomainService {
    private final PaymentCallbackDao paymentCallbackDao;

    public long savePaymentCallback(PaymentCallback paymentCallback) {

        paymentCallback.setCreatedBy(0L);
        paymentCallback.setCreatedDate(LocalDateTime.now());
        paymentCallbackDao.save(paymentCallback);
        return paymentCallback.getId();
    }
}
