package com.eroses.external.society.service;

import com.eroses.external.society.mappers.SearchInformationDocumentTemplateDao;
import com.eroses.external.society.model.SearchInformationDocumentTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class SearchInformationDocumentTemplateReadDomainService {
    private final SearchInformationDocumentTemplateDao searchInformationDocumentTemplateDao;

    public SearchInformationDocumentTemplate getByCode(String code) {
        return searchInformationDocumentTemplateDao.getByCode(code);
    }

    public List<SearchInformationDocumentTemplate> getAll() {
        return searchInformationDocumentTemplateDao.getAll();
    }
}
