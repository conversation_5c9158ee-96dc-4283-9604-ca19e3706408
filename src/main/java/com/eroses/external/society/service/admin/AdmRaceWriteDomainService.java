package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmRaceDao;
import com.eroses.external.society.model.lookup.AdmRace;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmRaceWriteDomainService {
    private final AdmRaceDao admRaceDao;

    public Long create(AdmRace race) throws Exception {
        admRaceDao.create(race);
        return race.getId();
    }

    public boolean update(AdmRace race) throws Exception {
        return admRaceDao.update(race);
    }

    public Long delete(Long id) throws Exception {
        admRaceDao.delete(id);
        return id;
    }
}