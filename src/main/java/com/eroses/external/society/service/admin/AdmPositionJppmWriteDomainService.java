package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmPositionJppmDao;
import com.eroses.external.society.model.AdmPositionJppm;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmPositionJppmWriteDomainService {
    private final AdmPositionJppmDao admPositionJppmDao;

    public Long create(AdmPositionJppm position) throws Exception {
        admPositionJppmDao.create(position);
        return position.getId();
    }

    public boolean update(AdmPositionJppm position) throws Exception {
        return admPositionJppmDao.update(position);
    }

    public boolean delete(Long id) throws Exception {
        return admPositionJppmDao.delete(id);
    }
}