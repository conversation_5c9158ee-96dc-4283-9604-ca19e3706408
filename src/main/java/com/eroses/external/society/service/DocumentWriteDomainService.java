package com.eroses.external.society.service;

import com.eroses.external.society.mappers.DocumentDao;
import com.eroses.external.society.model.Branch;
import com.eroses.external.society.model.Document;
import com.eroses.external.society.model.enums.DocumentTypeEnum;
import com.eroses.external.society.utils.Assert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentWriteDomainService {
    private final S3DomainService s3DomainService;
    private final DocumentDao documentDao;

    public String uploadFile(MultipartFile file) throws IOException {
        String folder = "test"; //to be updated after UAT
        try{
            return s3DomainService.uploadFile(file, folder, file.getOriginalFilename());
        } catch (RuntimeException e) {
            throw new RuntimeException("Upload failed: " + e);
        }
    }

    public Long registerFileInDbOld(Document document) {
        String folder = "test"; //to be updated after UAT
        String name = extractFileName(document.getUrl());
        document.setName(name);
        if (s3DomainService.checkIfFileExists(name)) {
            documentDao.registerFileInDb(document);
            return document.getId();
        } else {
            throw new RuntimeException("File not found in S3; cannot register in the database.");
        }
    }

    public Long registerFileInDb(Document document) {
        return documentDao.registerFileInDb(document);
    }

    public Long create(DocumentTypeEnum documentTypeEnum, Long societyId, String societyNo, String documentName, String documentCode, String url) throws Exception {
        Document document = new Document();
        document.setType(documentTypeEnum.getType());
        document.setSocietyId(societyId);
        document.setSocietyNo(societyNo);
        document.setName(documentName);
        document.setCode(documentCode);
        document.setUrl(url);
        document.setStatus(1L);
        documentDao.create(document);
        return document.getId();
    }

    public Long create(Document document, DocumentTypeEnum documentTypeEnum, Long societyId, String societyNo, String documentName, String documentCode, String url) throws Exception {
        document.setType(documentTypeEnum.getType());
        document.setSocietyId(societyId);
        document.setSocietyNo(societyNo);
        document.setName(documentName);
        document.setUrl(url);
        document.setStatus(1L);
        document.setCode(documentCode);
        documentDao.create(document);
        return document.getId();
    }

    public Long create(Document document) throws Exception {
        documentDao.create(document);
        return document.getId();
    }

    public boolean updateDocumentStatusToInactive(List<Long> ids) {
        return documentDao.updateDocumentStatusToInactive(ids);
    }

    //to handle S3 logics in the future
    public String updateDocument(Document document) {
        documentDao.updateDocument(document);
        return document.getName();
    }

    public String deleteDocument(Long id, String url) {
        if (s3DomainService.checkIfFileExists(url)) {
            Boolean isDelete = s3DomainService.deleteDocument(url);
            if (isDelete) {
                documentDao.deleteDocument(id);
            }
        } else {
            throw new RuntimeException("File not found in S3; delete operation cancelled");
        }
        return url;
    }

    public String extractFileName(String url) {

        String regex = ".*/([^/?]+)(?:\\?.*)?$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            String encodedFilename = matcher.group(1);
            String decodedFilename = URLDecoder.decode(encodedFilename, StandardCharsets.UTF_8);
            while (decodedFilename.contains("%")) {
                decodedFilename = URLDecoder.decode(decodedFilename, StandardCharsets.UTF_8);
            }
            return decodedFilename;
        } else {
            throw new IllegalArgumentException("No filename found in the URL");
        }
    }

    public Long registerFile(Document document) {
        documentDao.updateDocument(document);
        return 1L;
    }

    public Document updateDocumentUrl(Document document) {
        documentDao.updateDocumentUrl(document);
        return document;
    }

    public void unregisterFromDb(String url) {
        documentDao.unregisterFromDb(url);
    }

    public Boolean submitDocument(Long id) {
        Boolean isOk = documentDao.submitDocument(id);
        Assert.isTrue(isOk, "Updating document status is unsuccessful.");

        return isOk;
    }

    public Long createForBranch(Branch branch, DocumentTypeEnum documentTypeEnum, Long societyId, String societyNo, String documentName, String documentCode, String url) throws Exception {
        Document document = new Document();
        document.setType(documentTypeEnum.getType());
        document.setSocietyId(societyId);
        document.setSocietyNo(societyNo);
        document.setBranchId(branch.getId());
        if (branch.getBranchNo() != null) {
            document.setBranchNo(branch.getBranchNo());
        } else {
            document.setBranchNo(branch.getBranchApplicationNo());
        }
        document.setName(documentName);
        document.setCode(documentCode);
        document.setUrl(url);
        document.setStatus(1L);
        documentDao.create(document);
        return document.getId();
    }

    public Long createForPerlembagaan(DocumentTypeEnum documentTypeEnum, Long societyId, String societyNo, String perlembagaanFileName, String code, Map<String, Object> params) throws Exception {
        Document document = new Document();
        document.setType(documentTypeEnum.getType());
        document.setSocietyId(societyId);
        document.setSocietyNo(societyNo);
        if (params.get("amendmentId") != null) {
            document.setAmendmentId((Long) params.get("amendmentId"));
        }
        document.setName(perlembagaanFileName);
        document.setCode(code);
        document.setUrl((String) params.get("url"));
        document.setStatus(1L);
        documentDao.create(document);
        return document.getId();
    }

    public Long createForStatement(DocumentTypeEnum documentTypeEnum, Map<String, Object> params, String documentName, String documentCode, String url) throws Exception {
        Document document = new Document();
        document.setType(documentTypeEnum.getType());
        document.setSocietyId((Long) params.get("societyId"));
        if (params.get("branchId") != null) {
            document.setBranchId((Long) params.get("branchId"));
        }
        document.setStatementId((Long) params.get("statementId"));
        document.setName(documentName);
        document.setCode(documentCode);
        document.setUrl(url);
        document.setStatus(1L);
        documentDao.create(document);
        return document.getId();
    }
}
