package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.EventFeedbackDao;
import com.eroses.external.society.model.EventFeedback;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
@Slf4j
@Service
@RequiredArgsConstructor
public class EventFeedbackWriteDomainService {
    private final EventFeedbackDao eventFeedbackDao;
    public List<EventFeedback> insert(List<EventFeedback> feedback) {
        int inserted = eventFeedbackDao.insert(feedback);
        if (inserted > 0) {
            return feedback;
        } else {
            return null;
        }
    }
}
