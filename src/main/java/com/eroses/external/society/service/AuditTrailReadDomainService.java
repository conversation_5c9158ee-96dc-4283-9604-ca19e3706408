package com.eroses.external.society.service;

import com.eroses.external.society.dto.request.auditTrail.UserAuditTrailReadRequest;
import com.eroses.external.society.dto.response.auditTrail.AuditTrailPagingResponse;
import com.eroses.external.society.mappers.AuditTrailDao;
import com.eroses.external.society.model.AuditTrail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuditTrailReadDomainService {
    private final AuditTrailDao auditTrailDao;

    public List<AuditTrailPagingResponse> findAllByParams(Map<String, Object> params) {
        return auditTrailDao.findAllByParams(params);
    }

    public List<AuditTrail> findAll() {
        return auditTrailDao.findAll();
    }

    public AuditTrail findAuditTrailForUser(UserAuditTrailReadRequest request) {
        return auditTrailDao.findAuditTrailForUser(request);
    }
}