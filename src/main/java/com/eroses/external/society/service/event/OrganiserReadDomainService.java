package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.OrganiserDao;
import com.eroses.external.society.model.Organiser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrganiserReadDomainService {
    private final OrganiserDao organiserDao;

    public List<Organiser> findAll(){
        log.info("Finding all organiser");
        return organiserDao.findAll();
    }

    public Organiser findOneByIdentificationNo(String identificationNo){
        log.info("Finding organiser by identificationNo {}", identificationNo);
        return organiserDao.findByIdentificationNo(identificationNo);
    }

    public Organiser findOneById(Long id){
        log.info("Finding organiser by id {}", id);
        return organiserDao.findById(id);
    }
}
