package com.eroses.external.society.service.newSocietySecretary;

import java.util.HashMap;
import java.util.List;

import com.eroses.external.society.model.PrincipalSecretary;
import org.springframework.stereotype.Service;

import com.eroses.external.society.dto.response.principalSecretaryInternal.PrincipalSecretaryInternalResponse;
import com.eroses.external.society.mappers.newSocietySecretary.PrincipleSecretaryInternalDao;
import com.eroses.external.society.service.admin.AdmBranchReadDomainService;
import com.eroses.user.api.facade.UserFacade;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class PrincipalSecretaryInternalReadDomainService {
    private final PrincipleSecretaryInternalDao principleSecretaryInternalDao;
    private final AdmBranchReadDomainService admBranchReadDomainService;
    private final UserFacade userFacade;

    public PrincipalSecretaryInternalResponse findSecretary(Long id) {
        return principleSecretaryInternalDao.findPrincipalSecretaryInternal(id);
    }

    public List<PrincipalSecretary> getAllByCriteria(String searchQuery, Integer stateCode,
                                                     Integer applicationStatusCode, Integer offset, Integer limit) {
        HashMap<String, Object> params = new HashMap<>();
        if (searchQuery != null && !searchQuery.isEmpty()) params.put("searchQuery", searchQuery);
        if (stateCode != null) params.put("stateCode", stateCode);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);
        if (offset != null) params.put("offset", offset);
        if (limit != null) params.put("limit", limit);
        return principleSecretaryInternalDao.getAllByCriteria(params);
    }

    public Long countAllByCriteria(String searchQuery, Integer stateCode, Integer applicationStatusCode) {
        HashMap<String, Object> params = new HashMap<>();
        if (searchQuery != null && !searchQuery.isEmpty()) params.put("searchQuery", searchQuery);
        if (stateCode != null) params.put("stateCode", stateCode);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);
        return principleSecretaryInternalDao.countAllByCriteria(params);
    }
}
