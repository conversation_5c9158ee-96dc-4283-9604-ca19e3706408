package com.eroses.external.society.service;

import com.eroses.external.society.dto.response.PrincipalSecretarySearchResponse;
import com.eroses.external.society.dto.response.societySecretaryReplacement.PrincipleSecretaryResponse;
import com.eroses.external.society.mappers.PrincipalSecretaryDao;
import com.eroses.external.society.model.PrincipalSecretary;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class PrincipalSecretaryReadDomainService {
    private final PrincipalSecretaryDao principalSecretaryDao;

    public PrincipleSecretaryResponse findById(Map<String, Object> params) {
        return principalSecretaryDao.findResponseById(params);
    }

    public List<PrincipalSecretary> getAllPrincipalSecretaries() {
        return principalSecretaryDao.findAll();
    }

    public List<PrincipalSecretarySearchResponse> searchPrincipalSecretaryExternal(Map<String, Object> params) {
        return principalSecretaryDao.searchPrincipalSecretaryExternal(params);
    }

    public Long countSearchedPrincipalSecretaryExternal(Map<String, Object> params) {
        return principalSecretaryDao.countPrincipalSecretaryExternal(params);
    }

    public PrincipalSecretary getById(Long id) {
        return principalSecretaryDao.getById(id);
    }

    public boolean existsById(Long id) {
        return principalSecretaryDao.existsById(id);
    }

    public List<PrincipalSecretary> getAllPendingByCriteria(List<Integer> applicationStatusCodes, String stateCode, Long roId, Integer isQueried, String categoryCode, String subCategoryCode, String societyName, Integer offset, Integer limit) {

        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (roId != null) params.put("ro", roId);
        if (categoryCode != null && !categoryCode.isEmpty()) params.put("categoryCode", categoryCode);
        if (subCategoryCode != null && !subCategoryCode.isEmpty()) params.put("subCategoryCode", subCategoryCode);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (isQueried != null) params.put("isQueried", isQueried);
        if (offset != null) params.put("offset", offset);
        if (limit != null) params.put("limit", limit);

        return principalSecretaryDao.getAllPendingByCriteria(params);
    }

    public Long countAllPendingByCriteria(List<Integer> applicationStatusCodes, String stateCode, Long roId, Integer isQueried, String categoryCode, String subCategoryCode, String societyName) {

        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (roId != null) params.put("ro", roId);
        if (categoryCode != null && !categoryCode.isEmpty()) params.put("categoryCode", categoryCode);
        if (subCategoryCode != null && !subCategoryCode.isEmpty()) params.put("subCategoryCode", subCategoryCode);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (isQueried != null) params.put("isQueried", isQueried);

        return principalSecretaryDao.countAllPendingByCriteria(params);
    }
}
