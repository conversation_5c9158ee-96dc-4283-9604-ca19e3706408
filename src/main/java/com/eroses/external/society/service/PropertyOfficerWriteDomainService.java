package com.eroses.external.society.service;

import com.eroses.external.society.mappers.PropertyOfficerDao;
import com.eroses.external.society.model.PropertyOfficer;
import com.eroses.external.society.model.PropertyOfficerApplication;
import com.eroses.external.society.model.enums.PaymentStatus;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class PropertyOfficerWriteDomainService {
    private final PropertyOfficerDao propertyOfficerDao;

    public boolean create(PropertyOfficer propertyOfficer) throws Exception {
        return propertyOfficerDao.create(propertyOfficer);
    }

    public boolean create(PropertyOfficerApplication propertyOfficerApplication) {
        return propertyOfficerDao.createPropertyOfficerApplication(propertyOfficerApplication);
    }

    public boolean create(List<PropertyOfficer> propertyOfficers) {
        return propertyOfficerDao.createPropertyOfficers(propertyOfficers);
    }

    public boolean update(PropertyOfficer propertyOfficer) {
        return propertyOfficerDao.updatePropertyOfficer(propertyOfficer);
    }

    public boolean update(PropertyOfficerApplication propertyOfficerApplication){
        return propertyOfficerDao.updatePropertyOfficerApplication(propertyOfficerApplication);
    }

    public boolean updatePropertyOfficers(PropertyOfficerApplication propertyOfficerApplication){
        propertyOfficerDao.removePropertyOfficers(propertyOfficerApplication.getId());
        update(propertyOfficerApplication);
        return create(propertyOfficerApplication.getPropertyOfficers());
    }


    public boolean updatePropertyOfficerStatus(PropertyOfficerApplication propertyOfficerApplication, int paymentStatusCode, String paymentMethod){
        if (paymentStatusCode == PaymentStatus.PAID.getCode()) {
            propertyOfficerApplication.setSubmissionDate(LocalDate.now());
            propertyOfficerApplication.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode()));
            propertyOfficerApplication.setPaymentDate(LocalDate.now());
            propertyOfficerApplication.setPaymentMethod(paymentMethod);
        } else {
            propertyOfficerApplication.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.BAYARAN_GAGAL.getCode()));
        }
        propertyOfficerApplication.setModifiedBy(0L);
        return update(propertyOfficerApplication);
    }


}
