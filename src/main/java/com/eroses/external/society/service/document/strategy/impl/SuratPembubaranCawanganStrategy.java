package com.eroses.external.society.service.document.strategy.impl;

import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.*;
import com.eroses.external.society.model.societyLiquidation.SocietyLiquidation;
import com.eroses.external.society.service.*;
import com.eroses.external.society.service.admin.AdmAddressesReadDomainService;
import com.eroses.external.society.service.document.strategy.AbstractDocumentMappingStrategy;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.service.utils.HtmlGeneratorService;
import com.eroses.external.society.utils.RoDecisionUtilities;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Strategy implementation for Section 13(1)(a), 13(1)(b), and 13(1)(c) document type.
 */
@Slf4j
@Component
public class SuratPembubaranCawanganStrategy extends AbstractDocumentMappingStrategy {

    private final AdmAddressesReadDomainService admAddressesReadDomainService;
    private final RoDecisionUtilities roDecisionUtilities;

    public SuratPembubaranCawanganStrategy(
            DocumentTemplateReadDomainService documentTemplateReadDomainService,
            S3DomainService s3DomainService,
            PdfService pdfService,
            HtmlGeneratorService htmlGeneratorService,
            UserFacade userFacade,
            SocietyReadDomainService societyReadDomainService,
            AdmAddressesReadDomainService admAddressesReadDomainService,
            RoDecisionUtilities roDecisionUtilities) {
        super(documentTemplateReadDomainService, s3DomainService, pdfService, htmlGeneratorService, userFacade, societyReadDomainService);
        this.admAddressesReadDomainService = admAddressesReadDomainService;
        this.roDecisionUtilities = roDecisionUtilities;
    }

    @Override
    public String mapDataToDocument(String templateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        DocumentTemplate template = getDocumentTemplate(templateCode);
        String htmlTemplate = template.getHtmlContent();

        Map<String, String> mapFields = prepareDataForMapping(societyId, additionalParams);
        String processedHtml = htmlGeneratorService.processConditionals(htmlTemplate, mapFields);
        return htmlGeneratorService.mapToHtml(processedHtml, mapFields);
    }

    @Override
    protected Map<String, String> prepareDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {

        Society society = societyReadDomainService.findById(societyId);
        SocietyLiquidation societyLiquidation = (SocietyLiquidation) additionalParams.get("societyLiquidation");
        SocietyCategoryEnum societyCategory = SocietyCategoryEnum.getCategoryById(Integer.valueOf(society.getCategoryCodeJppm()));

        Branch branch;
        if (additionalParams.get("branch") instanceof Branch) {
            branch = (Branch) additionalParams.get("branch");
        } else {
            throw new IllegalArgumentException("Branch information is required for Surat Pembubaran Cawangan.");
        }

        AdmAddresses branchState = admAddressesReadDomainService.findById(Long.valueOf(branch.getStateCode()));

        //Get State Director of the state (Should have only 1 state director per state)
        List<User> stateDirectors = roDecisionUtilities.getAssignedApprovalOfficer(UserRoleEnum.PENOLONG_PENDAFTAR_PERTUBUHAN, society.getStateCode(), societyCategory);
        String directorName = stateDirectors.isEmpty() ? "-" : stateDirectors.getFirst().getName();

        Map<String, String> mapFields = getMapFields(branch, societyLiquidation, branchState, directorName);
        return mapLetterHeader(mapFields, society.getStateCode(), societyCategory);
    }

    @Override
    public boolean canHandle(String templateCode) {
        return DocumentTemplateEnum.SURAT_PEMBUBARAN_CAWANGAN.getCode().equals(templateCode);
    }

    private static Map<String, String> getMapFields(Branch branch, SocietyLiquidation societyLiquidation, AdmAddresses branchState, String stateDirectorName) {
        Map<String, String> mapFields = new HashMap<>();
        mapFields.put("{{BRANCH_NAME}}", getValueOrDefault(branch.getName()).toUpperCase());
        mapFields.put("{{BRANCH_NO}}", getValueOrDefault(branch.getBranchNo()).toUpperCase());
        mapFields.put("{{BRANCH_ADDRESS}}", getValueOrDefault(branch.getAddress()));
        mapFields.put("{{BRANCH_POSTCODE}}", getValueOrDefault(branch.getPostcode()));
        mapFields.put("{{BRANCH_CITY}}", getValueOrDefault(branch.getCity()).toUpperCase());
        mapFields.put("{{BRANCH_STATE}}", getValueOrDefault(branchState.getName()).toUpperCase());
        mapFields.put("{{LIQUIDATION_DATE}}", formatToMalayDate(LocalDate.now())); //Currently Liquidation was done immediately, hence current date
        mapFields.put("{{SUBMISSION_DATE}}", formatToMalayDate(societyLiquidation.getSubmissionDate()));
        mapFields.put("{{OFFICER_NAME}}", getValueOrDefault(stateDirectorName).toUpperCase());
        return mapFields;
    }

    private Map<String, String> mapLetterHeader(Map<String, String> mapFields, String stateCode, SocietyCategoryEnum societyCategory) throws Exception {
        // Null or empty check
        if (stateCode == null || stateCode.isEmpty()) {
            return mapFields;
        }

        // Letter Header is based on the state who will be handled the society
        String letterHeaderStateCode = roDecisionUtilities.getAssignedResponsibleStateCode(stateCode, societyCategory);
        mapFields.put("{{IS_" + letterHeaderStateCode  + "_STATE}}", "true");
        return mapFields;
    }
}