package com.eroses.external.society.service.societyCancellation;

import com.eroses.external.society.mappers.SocietyCancellationDao;
import com.eroses.external.society.model.societyCancellation.SocietyCancellation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyCancellationWriteDomainService {
    private final SocietyCancellationDao societyCancellationDao;

    public Long create(SocietyCancellation societyCancellation) throws Exception {
        societyCancellationDao.create(societyCancellation);
        return societyCancellation.getId();
    }

    public Boolean update(SocietyCancellation societyCancellation) throws Exception {
        return societyCancellationDao.update(societyCancellation);
    }

    public Boolean delete(Long id) {
        return societyCancellationDao.delete(id);
    }
}