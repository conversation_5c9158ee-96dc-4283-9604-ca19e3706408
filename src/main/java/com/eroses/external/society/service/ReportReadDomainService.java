package com.eroses.external.society.service;

import com.eroses.external.society.dto.request.report.QuickSightEmbedUrlRequest;
import com.eroses.external.society.dto.response.report.QuickSightEmbedUrlResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReportReadDomainService {
    
    private final QuickSightService quickSightService;
    
    /**
     * Generate registered user embed URL for QuickSight dashboard
     *
     * @param request The embed URL request parameters
     * @return QuickSight embed URL response
     */
    public QuickSightEmbedUrlResponse generateRegisteredUserEmbedUrl(QuickSightEmbedUrlRequest request) {
        log.info("Processing request to generate QuickSight registered user embed URL for dashboard: {}",
                request != null ? request.getDashboardId() : "null");

        // Validate request - dashboardId is required
        if (request == null || request.getDashboardId() == null || request.getDashboardId().trim().isEmpty()) {
            throw new IllegalArgumentException("Dashboard ID is required and cannot be empty");
        }

        return quickSightService.generateRegisteredUserEmbedUrl(request);
    }

    /**
     * Generate anonymous embed URL for QuickSight dashboard (deprecated - use registered user instead)
     *
     * @param request The embed URL request parameters
     * @return QuickSight embed URL response
     * @deprecated Use generateRegisteredUserEmbedUrl instead for better security and user tracking
     */
    @Deprecated
    public QuickSightEmbedUrlResponse generateAnonymousEmbedUrl(QuickSightEmbedUrlRequest request) {
        log.warn("Using deprecated anonymous embed URL generation. Consider using registered user embed URL instead.");
        return generateRegisteredUserEmbedUrl(request);
    }
}
