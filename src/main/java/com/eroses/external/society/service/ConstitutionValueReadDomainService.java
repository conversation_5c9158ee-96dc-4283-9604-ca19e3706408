package com.eroses.external.society.service;


import com.eroses.external.society.dto.request.ConstitutionValueGetAllRequest;
import com.eroses.external.society.mappers.ConstitutionValueDao;
import com.eroses.external.society.model.ConstitutionValue;
import com.eroses.external.society.utils.Assert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class ConstitutionValueReadDomainService {
    private final ConstitutionValueDao constitutionvalueDao;
    public ConstitutionValue findById(Long id) {
        ConstitutionValue constitutionvalue = constitutionvalueDao.findById(id);

        return constitutionvalue;
    }

    public Boolean isExists(Long id) {
        ConstitutionValue constitutionvalue = constitutionvalueDao.findById(id);
        return constitutionvalue != null;
    }

    public List<ConstitutionValue> getAllConstitutionValues(ConstitutionValueGetAllRequest request) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", request.getId());
        params.put("amendmentId", request.getAmendmentId());
        params.put("societyId", request.getSocietyId());
        params.put("clauseContentId", request.getClauseContentId());
        params.put("constitutionContentId", request.getConstitutionContentId());
        params.put("applicationStatusCode", request.getApplicationStatusCode());
        params.put("offset", request.getPageNo());
        params.put("limit", request.getPageSize());

        return constitutionvalueDao.findAll(params);
    }

    public List<ConstitutionValue> findByConstitutionContentId(Long constitutionContentId) {
        return constitutionvalueDao.findByConstitutionContentId(constitutionContentId);
    }

    public long countGetAllConstitutionValues(ConstitutionValueGetAllRequest request) {
        Map<String, Object> params = new HashMap<>();
        params.put("amendmentId", request.getAmendmentId());
        params.put("societyId", request.getSocietyId());
        params.put("clauseContentId", request.getClauseContentId());
        params.put("constitutionContentId", request.getConstitutionContentId());

        return constitutionvalueDao.countFindAll(params);
    }

    public ConstitutionValue findByParam(Long constitutionContentId, Map<String, Object> params) {
        params.put("constitutionContentId", constitutionContentId);
        return constitutionvalueDao.findByParam(params);
    }
}
