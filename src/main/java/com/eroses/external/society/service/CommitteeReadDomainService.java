package com.eroses.external.society.service;

import com.eroses.external.society.mappers.CommitteeDao;
import com.eroses.external.society.model.Committee;
import com.eroses.external.society.model.enums.StatusCode;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommitteeReadDomainService {
    private final CommitteeDao committeeDao;

    public Committee findById(Long id) {
        Committee committee = committeeDao.findById(id);
        return committee;
    }

    public Committee findByIdentificationNoAndSocietyId(String idNo, long societyId) {
        Map<String, Object> params = Map.of(
            "identificationNo", idNo,
            "societyId", societyId
        );
        Committee committee = committeeDao.findBySocietyIdAndIdentificationNo(params);
        return committee;
    }

    public List<Committee> findByIds(Long societyId, List<String> designationCodes, List<Long> ids) {
        return committeeDao.findByIds(Map.of("societyId", societyId, "designationCodes", designationCodes, "ids", ids, "status", StatusCode.AKTIF.getCode()));
    }

    public List<Committee> findActviveCommitteesByPositionsAndSocietyIds(List<Long> societyIds, List<String> positions) {
        Map<String, Object> params = new HashMap<>();
        params.put("designationCode", positions );
        params.put("societyIds", societyIds);
        params.put("status", StatusCode.AKTIF.getCode());
        params.put("applicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
        return committeeDao.findByPositionAndSocietyIdsAndApplicationStatusCodeAndStatus(params);
    }

    public Boolean isExists(Long id) {
        Committee committee = committeeDao.findById(id);
        return committee != null;
    }

    public List<Committee> getAllCommittees(Long societyId, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "offset", offset,
                "limit", limit
        );
        return committeeDao.findAll(params);
    }

    public List<Committee> getAllCommitteesByCriteriaAndSort(Long societyId, String sortBy, String sortDir, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "sortBy", sortBy,
                "sortDir", sortDir,
                "offset", offset,
                "limit", limit
        );
        return committeeDao.getAllCommitteesByCriteriaAndSort(params);
    }

    public List<Committee> findByParams(Map<String, Object> params) {
        return committeeDao.findByParams(params);
    }

    public Long countGetAllCommittees(Long societyId) {
        return committeeDao.countFindAll(societyId);
    }

    public List<Committee> findBySocietyId(Long societyId) {
        return committeeDao.findBySocietyId(societyId);
    }

    public List<Committee> findBySocietyIdAndApplicationStatusCodeAndStatus(Long societyId, Integer applicationStatusCode, String status) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "applicationStatusCode", applicationStatusCode,
                "status", status
        );
        return committeeDao.findBySocietyIdAndApplicationStatusCodeAndStatus(params);
    }

    public List<Long> findSocietyIdListByIdentificationNo(String identificationNo) {
        return committeeDao.findSocietyIdListByIdentificationNo(identificationNo);
    }

    public List<Long> findUniqueSocietyIdListByIdentificationNo(String identificationNo) {
        return committeeDao.findSocietyIdListByIdentificationNo(identificationNo);
    }

    public void validateExists(
        Long societyId,
        String identificationNo,
        String name,
        String designationCode,
        String status,
        Integer applicationStatusCode
    ) {
        Map<String, Object> params = new HashMap<>();
        params.put("societyId", societyId);
        params.put("identificationNo", identificationNo);
        params.put("name", name);
        params.put("designationCode", designationCode);
        params.put("status", status);
        params.put("applicationStatusCode", applicationStatusCode);
        boolean result = committeeDao.exist(params);

        if (!result) {
            throw new IllegalArgumentException("The given secretary info doesn't match.");
        }
    }

    public boolean existsByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus(String identificationNo, Long societyId, Integer applicationStatusCode, String status) {
        Map<String, Object> params = Map.of(
                "identificationNo", identificationNo,
                "societyId", societyId,
                "applicationStatusCode", applicationStatusCode,
                "status", status
        );

        return committeeDao.existsByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus(params);
    }

    public boolean existsByIdentityNoAndSocietyIdAndNotEqualApplicationStatusCodesAndStatus(String identificationNo, Long societyId, List<Integer> applicationStatusCodes, String status) {
        Map<String, Object> params = Map.of(
                "identificationNo", identificationNo,
                "societyId", societyId,
                "applicationStatusCodes", applicationStatusCodes,
                "status", status
        );

        return committeeDao.existsByIdentityNoAndSocietyIdAndNotEqualApplicationStatusCodesAndStatus(params);
    }

    public List<Committee> findBySocietyIdAndApplicationStatusCodeAndStatusPaging(
            Long societyId, Integer applicationStatusCode, String status, List<String> designationCodes, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>(Map.of(
                "societyId", societyId,
                "applicationStatusCode", applicationStatusCode,
                "status", status,
                "offset", offset,
                "limit", limit
        ));
        if(designationCodes!=null && !designationCodes.isEmpty()){
            params.put("designationCodes",designationCodes);
        }

        return committeeDao.findBySocietyIdAndApplicationStatusCodeAndStatus(params);
    }

    public Long countBySocietyIdAndApplicationStatusCodeAndStatus(
            Long societyId, Integer applicationStatusCode, String status, List<String> designationCodes) {
        Map<String, Object> params = new HashMap<>(Map.of(
                "societyId", societyId,
                "applicationStatusCode", applicationStatusCode,
                "status", status
        ));
        if(designationCodes!=null && !designationCodes.isEmpty()){
            params.put("designationCodes",designationCodes);
        }

        return committeeDao.countBySocietyIdAndApplicationStatusCodeAndStatus(params);
    }

    public Committee findBySocietyIdAndIdentificationNo(Long societyId, String identificationNo) {

        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "identificationNo", identificationNo
        );

        return committeeDao.findBySocietyIdAndIdentificationNo(params);
    }

    public Committee findNotDeletedBySocietyIdAndIdentificationNo(Long societyId, String identificationNo) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "identificationNo", identificationNo
        );
        return committeeDao.findNotDeletedBySocietyIdAndIdentificationNo(params);
    }

    /**
     * Find committee with priority logic:
     * Priority 1: status 11 (AKTIF)
     * Priority 2: status 2 (MENUNGGU_KEPUTUSAN)
     * Priority 3: other statuses except 4 (TOLAK)
     * Priority 4: status 4 (TOLAK) as last resort
     */
    public Committee findNotDeletedBySocietyIdAndIdentificationNoWithPriority(Long societyId, String identificationNo) {
        // Get all non-deleted committee records for this society and identification number
        List<Committee> committees = findAllBySocietyIdAndIdentificationNo(societyId, identificationNo)
                .stream()
                .filter(committee -> !String.valueOf(ApplicationStatusCode.PADAM.getCode()).equals(committee.getApplicationStatusCode()))
                .toList();

        if (committees.isEmpty()) {
            return null;
        }

        // Priority 1: return status 11 (AKTIF) if it exists
        Committee aktifCommittee = committees.stream()
                .filter(committee -> String.valueOf(ApplicationStatusCode.AKTIF.getCode()).equals(committee.getApplicationStatusCode()))
                .findFirst()
                .orElse(null);

        if (aktifCommittee != null) {
            return aktifCommittee;
        }

        // Priority 2: return status 2 (MENUNGGU_KEPUTUSAN) if it exists
        Committee menungguKeputusan = committees.stream()
                .filter(committee -> String.valueOf(ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode()).equals(committee.getApplicationStatusCode()))
                .findFirst()
                .orElse(null);

        if (menungguKeputusan != null) {
            return menungguKeputusan;
        }

        // Priority 3: return first non-status-4 record
        Committee nonTolakCommittee = committees.stream()
                .filter(committee -> !String.valueOf(ApplicationStatusCode.TOLAK.getCode()).equals(committee.getApplicationStatusCode()))
                .findFirst()
                .orElse(null);

        if (nonTolakCommittee != null) {
            return nonTolakCommittee;
        }

        // Priority 4: If only status 4 (TOLAK) records exist, return the first one
        return committees.get(0);
    }

    public List<Committee> findAllBySocietyIdAndIdentificationNo(Long societyId, String identificationNo) {

        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "identificationNo", identificationNo
        );

        return committeeDao.findAllBySocietyIdAndIdentificationNo(params);
    }

    public Committee findBySocietyIdAndIdentificationNoAndApplicationStatusCodeAndStatus(Long societyId, String identificationNo, Integer applicationStatusCode, String status) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "identificationNo", identificationNo,
                "applicationStatusCode", applicationStatusCode,
                "status", status
        );

        return committeeDao.findBySocietyIdAndIdentificationNoAndApplicationStatusCodeAndStatus(params);
    }

    public Committee findBySocietyIdAndIdentificationNoAndNotEqualApplicationStatusCodeAndStatus(Long societyId, String identificationNo, Integer applicationStatusCode, String status) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "identificationNo", identificationNo,
                "applicationStatusCode", applicationStatusCode,
                "status", status
        );

        return committeeDao.findBySocietyIdAndIdentificationNoAndNotEqualApplicationStatusCodeAndStatus(params);
    }

    public List<Committee> findBySocietyIdAndPositionAndApplicationStatusCodeAndStatusPaging(Long societyId, String position, int applicationStatusCode, String status, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "position", position,
                "applicationStatusCode", applicationStatusCode,
                "status", status,
                "offset", offset,
                "limit", limit
        );

        return committeeDao.findBySocietyIdAndPositionAndApplicationStatusCodeAndStatusPaging(params);
    }

    public Long countBySocietyIdAndPositionAndApplicationStatusCodeAndStatus(Long societyId, String position, int applicationStatusCode, String status) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "position", position,
                "applicationStatusCode", applicationStatusCode,
                "status", status
        );

        return committeeDao.countBySocietyIdAndPositionAndApplicationStatusCodeAndStatus(params);
    }

    public Committee findOneByIdentificationNoAndSocietyIdAndPositionAndApplicationStatusCodeAndStatus(String identificationNo, Long societyId, String position, int applicationStatusCode, String status) {
        Map<String, Object> params = Map.of(
                "identificationNo", identificationNo,
                "societyId", societyId,
                "position", position,
                "applicationStatusCode", applicationStatusCode,
                "status", status
        );

        return committeeDao.findOneByIdentificationNoAndSocietyIdAndPositionAndApplicationStatusCodeAndStatus(params);
    }

    public Committee findOneBySocietyIdAndIdentificationNoAndApplicationStatusCodeAndStatusAndPosition(Long societyId, String identificationNo, Integer applicationStatusCode, String status, String position) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "identificationNo", identificationNo,
                "applicationStatusCode", applicationStatusCode,
                "status", status,
                "position", position
        );

        return committeeDao.findOneBySocietyIdAndIdentificationNoAndApplicationStatusCodeAndStatusAndPosition(params);
    }

    public List<Map<Long, String>> findSocietyIdAndDesignationCodeByCriteria(String identificationNo, Integer applicationStatusCode, String status, String designationCode) {
        Map<String, Object> params = Map.of(
                "identificationNo", identificationNo,
                "applicationStatusCode", applicationStatusCode,
                "status", status,
                "designationCode", designationCode
        );

        return committeeDao.findSocietyIdAndDesignationCodeByCriteria(params);
    }

    public Long countActiveRoleInSociety(Long societyId, Integer status, List<String> designationCodes) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "status",status,
                "designationCodes", designationCodes
        );
        return committeeDao.countRoleNoInSociety(params);
    }

    public Long countRoleInSociety(Long societyId,String status, List<String> designationCodes) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "status",Optional.ofNullable(status).orElse(""),
                "designationCodes", designationCodes
        );
        return committeeDao.countRoleNoInSociety(params);
    }

    public List<Committee> listAllActiveMember(Long societyId,String status, String committeeName){
        return committeeDao.findByParams(
                Map.of("societyId",societyId,
                        "status",status,
                        "committeeName", committeeName
                )
        );
    }

    public List<Committee> listRoleInSociety(Long societyId, List<String> designationCodes, String status, Integer offset, Integer limit) {
        Map<String, Object> params = offset != null && limit != null ? Map.of(
                "societyId", societyId,
                "designationCodes", designationCodes,
                "status", Optional.ofNullable(status).orElse(""),
                "offset", offset,
                "limit", limit
        ) : Map.of(
                "societyId", societyId,
                "designationCodes", designationCodes,
                "status", Optional.ofNullable(status).orElse("")
        );
        return committeeDao.listRoleInSociety(params);
    }

    public List<Committee> listRoleInSocietyStatus(Long societyId, String designationCode, String status, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "designationCode", designationCode,
                "status", status,
                "offset", offset,
                "limit", limit
        );
        return committeeDao.listRoleInSocietyStatus(params);
    }

    public Long countRoleInSocietyStatus(Long societyId, String designationCode, String status) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "designationCode", designationCode,
                "status", status
        );
        return committeeDao.countRoleInSocietyStatus(params);
    }

    public Committee findByStatementId(Long statementId) {
        return committeeDao.findByStatementId(statementId);
    }

    public Committee findACommitteeInSocietyWithRole(String position, Long societyId) {
        Map<String, Object> params = new HashMap<>();
        params.put("position", position);
        params.put("societyId", societyId);

        return committeeDao.findACommitteeInSocietyWithRole(params);
    }

    public Committee findActiveCommitteeInSocietyWithRoles(List<String> positions, Long societyId) {
        Map<String, Object> params = new HashMap<>();
        params.put("positions", positions);
        params.put("societyId", societyId);
        params.put("status", StatusCode.AKTIF.getCode());
        params.put("applicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
        return committeeDao.findActiveCommitteeInSocietyWithRoles(params);
    }

    public List<Committee> findActiveCommitteesInSocietyWithRoles(List<String> positions, Long societyId) {
        Map<String, Object> params = new HashMap<>();
        params.put("positions", positions);
        params.put("societyId", societyId);
        params.put("status", StatusCode.AKTIF.getCode());
        params.put("applicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
        return committeeDao.findActiveCommitteesInSocietyWithRoles(params);
    }

    public List<Committee> findActiveByIdentificationNoAndDesignationCode(String identificationNo, List<String> positions) {
        Map<String, Object> params = new HashMap<>();
        params.put("identificationNo", identificationNo);
        params.put("positions", positions);
        params.put("status", StatusCode.AKTIF.getCode());
        params.put("applicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
        return committeeDao.findActiveByIdentificationNoAndDesignationCode(params);
    }

    public List<Committee> findByIdentificationNo(String identificationNo) {
        return committeeDao.findByIdentificationNo(identificationNo);
    }

    public List<Committee> findByIdentificationNoAndActiveUserAndActiveSociety(String identificationNo) {
        Map<String, Object> params = new HashMap<>();
        if (identificationNo != null && !identificationNo.isEmpty()) params.put("identificationNo", identificationNo);
        params.put("committeeApplicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
        params.put("societyApplicationStatusCode", ApplicationStatusCode.LULUS.getCode());
        params.put("committeeStatusCode", StatusCode.AKTIF.getCode());
        params.put("societyStatusCode", StatusCode.AKTIF.getCode());
        return committeeDao.findByIdentificationNoAndActiveUserAndActiveSociety(params);
    }

    public List<Committee> findByIdentificationNoAndApplicationStatusCodeAndStatus(String identificationNo, Integer applicationStatusCode, String status) {
        Map<String, Object> params = new HashMap<>();
        if (identificationNo != null && !identificationNo.isEmpty()) params.put("identificationNo", identificationNo);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);
        if (status != null && !status.isEmpty()) params.put("status", status);
        return committeeDao.findByParam(params);
    }

    public List<Committee> findListByIds(List<Long> currentCommitteeIds) {
        Map<String, Object> params = new HashMap<>();
        if (currentCommitteeIds != null && !currentCommitteeIds.isEmpty()) params.put("ids", currentCommitteeIds);
        return committeeDao.findByParam(params);
    }

    public List<LocalDate> findAllAppointedDates(Long societyId) {
        return committeeDao.findAllAppointedDates(societyId);
    }
}