package com.eroses.external.society.service.admin;

import com.eroses.config.cache.CacheNames;
import com.eroses.external.society.mappers.AdmCategoryDao;
import com.eroses.external.society.mappers.ClauseContentDao;
import com.eroses.external.society.model.AdmCategory;
import com.eroses.external.society.model.ClauseContent;
import com.eroses.external.society.model.MeetingMemberAttendance;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class ClauseContentReadDomainService {

    private final ClauseContentDao clauseContentDao;


//    @Cacheable(value = CacheNames.CLAUSE_CONTENTS, key = "'all_clause_contents'")
    public List<ClauseContent> getAllClauseContent() {
        log.info("Fetching all clause contents from database (cache miss)");
        return clauseContentDao.findAll();
    }

//    @Cacheable(value = CacheNames.CLAUSE_CONTENTS, key = "'clause_contents_type_' + #constitutionTypeId")
    public List<ClauseContent> getAllByConstitutionTypeId(Long constitutionTypeId) {
        log.info("Fetching clause contents for constitution type {} from database (cache miss)", constitutionTypeId);
        return clauseContentDao.findByConstitutionTypeId(constitutionTypeId);
    }

//    @Cacheable(value = CacheNames.CLAUSE_CONTENTS, key = "'clause_content_' + #constitutionType + '_' + #clauseNo")
    public ClauseContent getByConsTypeAndClauseNo(Long constitutionType, Long clauseNo) {
        log.info("Fetching clause content for constitution type {} and clause no {} from database (cache miss)", constitutionType, clauseNo);
        return clauseContentDao.getByConsTypeAndClauseNo(constitutionType, clauseNo);
    }

    public Long countAllByConstitutionTypeId(Long constitutionTypeId) {
        return clauseContentDao.countAllByConstitutionTypeId(constitutionTypeId);
    }

    public ClauseContent getByConsTypeAndClauseName(Long constitutionType, String clauseName) {
        return clauseContentDao.getByConsTypeAndClauseName(constitutionType, clauseName);
    }
}
