package com.eroses.external.society.service;

import com.eroses.external.society.mappers.DocumentTemplateDao;
import com.eroses.external.society.model.DocumentTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentTemplateWriteDomainService {
    private final DocumentTemplateDao documentTemplateDao;

    public void create(DocumentTemplate documentTemplate) throws Exception {
        documentTemplateDao.create(documentTemplate);
    }

    public void updateDocumentTemplate(DocumentTemplate laporanAktivitiTemplate) throws Exception {
        documentTemplateDao.update(laporanAktivitiTemplate);
    }
}
