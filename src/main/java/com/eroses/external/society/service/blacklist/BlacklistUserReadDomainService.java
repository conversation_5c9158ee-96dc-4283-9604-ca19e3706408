package com.eroses.external.society.service.blacklist;

import com.eroses.external.society.mappers.blacklist.BlacklistUserDao;
import com.eroses.external.society.model.blacklist.BlacklistUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class BlacklistUserReadDomainService {
    private final BlacklistUserDao blacklistUserDao;

    public BlacklistUser findById(Long id) {
        return blacklistUserDao.findById(id);
    }

    public BlacklistUser findByIdentificationNo(String identificationNo) {
        return blacklistUserDao.findByIdentificationNo(identificationNo);
    }

    public boolean existsActiveBlacklistUserByIdentificationNo(String identificationNo) {
        return blacklistUserDao.existsActiveBlacklistUserByIdentificationNo(identificationNo);
    }

    public List<BlacklistUser> search(Map<String, Object> params) {
        return blacklistUserDao.search(params);
    }

    public Long countSearch(Map<String, Object> params) {
        return blacklistUserDao.countSearch(params);
    }

    public boolean isExists(Long id) {
        return findById(id) != null;
    }

    public boolean isExistsByIdentificationNo(String identificationNo) {
        return findByIdentificationNo(identificationNo) != null;
    }

    public List<Long> getAllExpiredBlacklistUserId() {
        return blacklistUserDao.getAllExpiredBlacklistUserId();
    }
}