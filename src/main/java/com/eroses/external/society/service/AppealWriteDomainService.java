package com.eroses.external.society.service;

import com.eroses.external.society.api.facade.BranchWriteFacade;
import com.eroses.external.society.api.facade.SocietyWriteFacade;
import com.eroses.external.society.dto.request.roDecision.UpdateApprovalStatusRequest;
import com.eroses.external.society.mappers.AppealDao;
import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.*;
import com.eroses.external.society.model.lookup.AdmInsolvencyDepartment;
import com.eroses.external.society.service.admin.AdmAddressesReadDomainService;
import com.eroses.external.society.service.admin.AdmBranchReadDomainService;
import com.eroses.external.society.service.admin.AdmInsolvencyDepartmentReadDomainService;
import com.eroses.external.society.service.document.DocumentGenerationService;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.utils.Assert;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class AppealWriteDomainService {
    private final AppealDao appealDao;
    private final SocietyReadDomainService societyReadDomainService;
    private final SocietyWriteFacade societyWriteFacade;
    private final SocietyWriteDomainService societyWriteDomainService;
    private final BranchReadDomainService branchReadDomainService;
    private final BranchWriteFacade branchWriteFacade;
    private final BranchWriteDomainService branchWriteDomainService;
    private final AmendmentReadDomainService amendmentReadDomainService;
    private final AmendmentWriteDomainService amendmentWriteDomainService;
    private final NonCitizenCommitteeReadDomainService nonCitizenCommitteeReadDomainService;
    private final NonCitizenCommitteeWriteDomainService nonCitizenCommitteeWriteDomainService;
    private final CommitteeWriteDomainService committeeWriteDomainService;
    private final AdmAddressesReadDomainService admAddressesReadDomainService;
    private final DocumentTemplateReadDomainService documentTemplateReadDomainService;
    private final DocumentWriteDomainService documentWriteDomainService;
    private final DocumentReadDomainService documentReadDomainService;
    private final DocumentGenerationService documentGenerationService;
    private final S3DomainService s3DomainService;
    private final AdmBranchReadDomainService admBranchReadDomainService;
    private final AdmInsolvencyDepartmentReadDomainService admInsolvencyDepartmentReadDomainService;
    private final RoApprovalReadDomainService roApprovalReadDomainService;
    private final UserFacade authFacade;
    private final PdfService pdfService;

    public Boolean createAppeal(Appeal appeal) throws Exception {
        Boolean isCreated = appealDao.create(appeal);
        Assert.isTrue(isCreated, "Creating appeal case is unsuccessful.");

        return isCreated;
    }

    public Boolean updateAppeal(Appeal appeal) throws Exception {
        Boolean isOk = appealDao.update(appeal);
        Assert.isTrue(isOk, "Updating appeal case is unsuccessful.");

        return isOk;
    }

    public Boolean update(Appeal appeal, User user) throws Exception {

        appeal.setModifiedBy(user.getId());
        Boolean isOk = appealDao.update(appeal);
        return isOk;
    }

    public Boolean updateAppealStatus(Appeal appeal, int paymentStatusCode, String paymentMethod) throws Exception {

        if(paymentStatusCode == PaymentStatus.PAID.getCode()){
            appeal.setApplicationStatusCode((long) ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode());
            appeal.setPaymentMethod(paymentMethod);
            appeal.setPaymentDate(LocalDate.now());
            appeal.setAppealDate(String.valueOf(LocalDate.now()));
        } else {
            appeal.setApplicationStatusCode((long) ApplicationStatusCode.BAYARAN_GAGAL.getCode());
        }

        appeal.setModifiedBy(0L);
        return appealDao.update(appeal);
    }

    public void updateApprovalDecision(Appeal appeal, User currentUser, Integer applicationStatusCode, String statusCode, AppealReason appealType) throws Exception {
        appeal.setApplicationStatusCode(Long.valueOf(applicationStatusCode));
        appeal.setModifiedBy(currentUser.getId());

        if (applicationStatusCode == ApplicationStatusCode.LULUS.getCode() || applicationStatusCode == ApplicationStatusCode.LULUS_BERSYARAT.getCode()) {
            applicationStatusCode = ApplicationStatusCode.LULUS.getCode();
            statusCode = StatusCode.AKTIF.getCode();
            Integer appStatusCode = ApplicationStatusCode.AKTIF.getCode();
            //update corresponding record
            switch (appealType) {
                case PEMBATALAN_PENDAFTARAN_PERTUBUHAN_2A, PEMBATALAN_PENDAFTARAN_13, PEMBATALAN_PENDAFTARAN_16 -> {
                    //society cancelled
                    Society society = societyReadDomainService.findById(appeal.getSocietyId());

                    //Update Society and Society Entities Record
                    societyWriteDomainService.updateRoDecision(society, currentUser, applicationStatusCode, statusCode);
                    societyWriteFacade.updateSocietyEntitiesStatus(society.getId(), statusCode, applicationStatusCode, appStatusCode, currentUser, true);
                }
                case PENOLAKAN_PENDAFTARAN_7 -> {
                    //society registration
                    Society society = societyReadDomainService.findById(appeal.getSocietyId());

                    //Update Society and Society Entities Record
                    societyWriteDomainService.updateRoDecision(society, currentUser, applicationStatusCode, statusCode);
                    societyWriteFacade.updateSocietyEntitiesStatus(society.getId(), statusCode, applicationStatusCode, appStatusCode, currentUser, false);

                    //since first time approved
                    society.setSocietyNo(societyWriteFacade.generateSocietyNo(society));
                    society.setApprovedDate(LocalDate.now());
                    societyWriteDomainService.update(society);

                    //to generate document templates and save into s3
                    Map<String, Object> map = Map.of("societyId", society.getId(),
                            "societyNo", society.getSocietyNo(), "approvedDate", society.getApprovedDate());
                    societyWriteFacade.generateSocietyDocuments(map, null);
                }
                case PENOLAKAN_PENGECUALIAN_9A -> {
                }
                case PENOLAKAN_PINDAAN_UU_11 -> {
                    //amendment
                    Amendment amendment = amendmentReadDomainService.findById(appeal.getAmendmentId());

                    //Update Amendment and Amendment Entities Record
                    amendmentWriteDomainService.updateRoDecision(amendment, currentUser, applicationStatusCode, statusCode);
                    amendmentWriteDomainService.updateAmendmentEntitiesStatus(amendment, statusCode, appStatusCode, currentUser);
                }
                case PENOLAKAN_CAWANGAN_12 -> {
                    //branch
                    Branch branch = branchReadDomainService.getBranchById(appeal.getBranchId());

                    //Update Branch
                    branch.setBranchNo(branchWriteDomainService.generateBranchNo(branch.getSocietyNo()));
                    branch = branchWriteDomainService.updateRoDecision(branch, currentUser, applicationStatusCode, statusCode);
                    branchWriteFacade.updateBranchEntitiesStatus(branch.getId(), statusCode, applicationStatusCode, applicationStatusCode);
                }
                case PERINTAH_13A_1 -> {
                    //non citizen committee (society)
                    NonCitizenCommittee nonCitizenCommittee = nonCitizenCommitteeReadDomainService.findById(appeal.getSocietyNonCitizenCommitteeId());

                    //Update NonCitizen Committee Record
                    nonCitizenCommitteeWriteDomainService.updateRoDecision(nonCitizenCommittee, applicationStatusCode, statusCode);
                    //create new
                    committeeWriteDomainService.createByNonCitizenCommittee(nonCitizenCommittee, currentUser);
                }
                case PERINTAH_13A_2 -> {
                }
                case PENOLAKAN_JURUAUDIT_14_4 -> {
                }
                case PERINTAH_14_5 -> {
                }
                case PENOLAKAN_MEMEGANG_JAWATAN_49 -> {
                }
                case null, default -> {
                    throw new Exception(ExceptionMessage.DATA_NOT_FOUND.getMessage("Appeal Reason Type not found with " + appealType.getMessage()));
                }
            }
        }

        appealDao.update(appeal);
    }

    public Boolean updateAppealStatusByAppealIds(List<Long> ids, int applicationStatusCode) {
        Map<String, Object> params = Map.of(
                "ids", ids,
                "applicationStatusCode", applicationStatusCode
        );
        return appealDao.updateAppealStatusByAppealIds(params);
    }

    public void generateApprovalLetter(Appeal appeal, UpdateApprovalStatusRequest request, User currentUser) throws Exception {
        ApplicationStatusCode requestStatus = ApplicationStatusCode.getStatus(request.getApplicationStatusCode());
        String documentTemplateEnum;
        switch (requestStatus) {
            case LULUS -> documentTemplateEnum = DocumentTemplateEnum.SURAT_RAYUAN_LULUS.getCode();
            case LULUS_BERSYARAT -> documentTemplateEnum = DocumentTemplateEnum.SURAT_RAYUAN_LULUS_BERSYARAT.getCode();
            case TOLAK -> documentTemplateEnum = DocumentTemplateEnum.SURAT_RAYUAN_TOLAK.getCode();
            case null, default -> throw new RuntimeException("Approval letter template not found");
        }

        Society society = societyReadDomainService.findById(appeal.getSocietyId());
        if (society == null) {
            throw new RuntimeException("Society not found");
        }

        Map<String, Object> additionalParams = new HashMap<>();
        additionalParams.put("appealId", appeal.getId());
        additionalParams.put("decision", request.getApplicationStatusCode());
        byte[] pdfBytes = documentGenerationService.generatePdfDocumentWithItext(documentTemplateEnum, society.getId(), additionalParams);

        //name should be - Surat Kelulusan Rayuan (Society or Cawangan Name) in bracket (Rayuan No)
        String documentName = documentGenerationService.getDocumentFileName(
                documentTemplateEnum,
                society.getId(),
                additionalParams
        );

        String url = documentGenerationService.uploadFileToS3(pdfBytes, documentName, "application/pdf", S3FolderPath.APPEAL_DOCUMENT.getPath());

        additionalParams.put("name", documentName);
        Document document = documentReadDomainService.findDocumentByParamSingle(additionalParams);
        if (document == null) {
            //upload to s3
            document = new Document();
            document.setType(DocumentTypeEnum.APPEAL.getType());
            document.setSocietyId(society.getId());
            document.setSocietyNo(society.getSocietyNo() != null ? society.getSocietyNo() : society.getApplicationNo());
            document.setAppealId(appeal.getId());
            document.setName(documentName);
            document.setCode(documentTemplateEnum);
            document.setUrl(url);
            document.setStatus(1L);
            documentWriteDomainService.create(document);
        } else {
            //always update url
            document.setUrl(url);
            documentWriteDomainService.updateDocumentUrl(document);
        }
    }
}
