package com.eroses.external.society.service;

import com.eroses.config.cache.CacheNames;
import com.eroses.external.society.mappers.ConstitutionValueDao;
import com.eroses.external.society.model.ConstitutionValue;
import com.eroses.external.society.utils.Assert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ConstitutionValueWriteDomainService {
    private final ConstitutionValueDao constitutionvalueDao;


//    @CacheEvict(value = CacheNames.CONSTITUTION_CONTENTS, allEntries = true)
    public Long create(ConstitutionValue constitutionvalue) throws Exception {
        log.info("Creating constitution value - evicting constitution contents cache");
        constitutionvalueDao.create(constitutionvalue);
        return constitutionvalue.getId();
    }

//    public Boolean update(ConstitutionValue constitutionvalue) {
//        boolean isOk = constitutionvalueDao.update(constitutionvalue);
//        Assert.isTrue(isOk, "Update society is unsuccessful.");
//
//        return isOk;
//    }

    public Long update(ConstitutionValue constitutionvalue) throws Exception {
        constitutionvalueDao.update(constitutionvalue);
        return constitutionvalue.getId();
    }

    public void updateStatus(List<Long> constitutionValueIdList, String status, Integer appStatusCode, long id) {
        constitutionvalueDao.updateStatus(constitutionValueIdList, status, appStatusCode, id);
    }

    public void hardDelete(List<Long> idList) {
        constitutionvalueDao.hardDelete(idList);
    }

    public Long updateById(ConstitutionValue constitutionValue) {
        return constitutionvalueDao.updateById(constitutionValue);
    }
}
