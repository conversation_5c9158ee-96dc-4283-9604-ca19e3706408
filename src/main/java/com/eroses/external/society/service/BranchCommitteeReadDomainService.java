package com.eroses.external.society.service;

import com.eroses.external.society.dto.response.branchCommittee.ActiveBranchSecretaryResponse;
import com.eroses.external.society.dto.response.branchCommittee.BranchCommitteeBasicResponse;
import com.eroses.external.society.dto.response.branchCommittee.BranchCommitteeDetailResponse;
import com.eroses.external.society.mappers.BranchCommitteeDao;
import com.eroses.external.society.model.Branch;
import com.eroses.external.society.model.BranchCommittee;
import com.eroses.external.society.model.enums.Position;
import com.eroses.external.society.model.enums.StatusCode;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.utils.PaginationUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class BranchCommitteeReadDomainService {
    private final BranchCommitteeDao branchCommitteeDao;
    private final BranchReadDomainService branchReadDomainService;

    public ActiveBranchSecretaryResponse findActiveBranchCommittee(Long branchId) {
        Map<String, Object> params = Map.of(
                "branchId", branchId,
                "secretaryPositionCode", Position.SETIAUSAHA.getCode(),
                "activeStatus", StatusCode.AKTIF.getCode()
        );
        return branchCommitteeDao.findActiveBranchCommittee(params);
    }

    public BranchCommittee findByBranchId(Long branchId) {
        return branchCommitteeDao.findByBranchId(branchId);
    }

    public List<BranchCommittee> findAllByBranchId(Long branchId) {
        return branchCommitteeDao.findAllByBranchId(branchId);
    }

    public List<Branch> findAllByIcNo(String icNo) {
        List<Branch> branches = new ArrayList<>();
        List<BranchCommittee> branchCommitteesWithBranchIds = branchCommitteeDao.findByIcNo(icNo);
        if (branchCommitteesWithBranchIds.isEmpty()) {
            return branches;
        }
        log.info("Hosam findByIcNo service, branchComm list:{}", branchCommitteesWithBranchIds);
        for (BranchCommittee branchCommitteeWithBranchId : branchCommitteesWithBranchIds) {
            Long branchId = branchCommitteeWithBranchId.getBranchId();
            Branch branch = branchReadDomainService.getBranchById(branchId);
            log.info("Hosam findByIcNo service, branch list:{}", branch);
            List<BranchCommittee> branchCommittees = findAllByBranchId(branch.getId());
            branch.setBranchCommittees(branchCommittees);
            branches.add(branch);
        }
        return branches;
    }

    public BranchCommittee findById(Long id) {
        return branchCommitteeDao.findById(id);
    }

    public List<BranchCommittee> findByIds(Long branchId, List<Long> ids) {
        return branchCommitteeDao.findByIds(Map.of("ids", ids, "branchId", branchId));
    }

    public List<BranchCommittee> findAllByCriteria(Long branchId, List<String> designationCodes, String statusCode, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        if (branchId != null) params.put("branchId", branchId);
        if (designationCodes != null && !designationCodes.isEmpty()) params.put("designationCodes", designationCodes);
        if (statusCode != null && !statusCode.isEmpty()) params.put("statusCode", statusCode);
        params.put("deleteStatusCode", StatusCode.PADAM.getCode());
        if (offset != null) params.put("offset", offset);
        if (limit != null) params.put("limit", limit);
        return branchCommitteeDao.findAllByCriteria(params);
    }

    public Long countAllByCriteria(Long branchId, List<String> designationCodes, String statusCode) {
        Map<String, Object> params = new HashMap<>();
        if (branchId != null) params.put("branchId", branchId);
        if (designationCodes != null && !designationCodes.isEmpty()) params.put("designationCodes", designationCodes);
        if (statusCode != null && !statusCode.isEmpty()) params.put("statusCode", statusCode);
        params.put("deleteStatusCode", StatusCode.PADAM.getCode());
        return branchCommitteeDao.countAllByCriteria(params);
    }

    public List<Long> findBranchIdListByIdentificationNo(String identificationNo) {
        return branchCommitteeDao.findBranchIdListByIdentificationNo(identificationNo);
    }

    public List<BranchCommittee> findByIdentificationNo(String identificationNo) {
        return branchCommitteeDao.findByIdentificationNo(identificationNo);
    }

    public List<BranchCommittee> findByBranchIdAndApplicationStatusCodeAndStatus(Long branchId, Integer applicationStatusCode, String status) {
        Map<String, Object> params = Map.of(
                "branchId", branchId,
                "applicationStatusCode", applicationStatusCode,
                "status", status
        );
        return branchCommitteeDao.findByBranchIdAndApplicationStatusCodeAndStatus(params);
    }

    public BranchCommittee findByBranchIdAndIdentificationNoAndApplicationStatusCodeAndStatus(Long branchId, String identificationNo, Integer applicationStatusCode, String status) {
        Map<String, Object> params = Map.of(
                "branchId", branchId,
                "identificationNo", identificationNo,
                "applicationStatusCode", applicationStatusCode,
                "status", status
        );
        return branchCommitteeDao.findByBranchIdAndIdentificationNoAndApplicationStatusCodeAndStatus(params);
    }

    public BranchCommittee findByBranchIdAndIdentificationNo(Long branchId, String identificationNo) {
        Map<String, Object> params = Map.of(
                "branchId", branchId,
                "identificationNo", identificationNo
        );
        return branchCommitteeDao.findByBranchIdAndIdentificationNo(params);
    }

    public List<BranchCommitteeBasicResponse> findAll(
        Long branchId,
        Integer applicationStatusCode,
        String status,
        int pageSize,
        int pageNo
    ) {
        Map<String, Object> params = Map.of(
                "branchId", branchId,
                "applicationStatusCode", applicationStatusCode,
                "status", status,
                "limit", pageSize,
                "offset", PaginationUtil.getOffset(pageNo, pageSize)
        );
        return branchCommitteeDao.findAll(params);
    }

    public long countFindAll(
        Long branchId,
        Integer applicationStatusCode,
        String status
    ) {
        Map<String, Object> params = Map.of(
                "branchId", branchId,
                "applicationStatusCode", applicationStatusCode,
                "status", status
        );
        return branchCommitteeDao.countFindAll(params);
    }

    public BranchCommitteeDetailResponse findDetail(
        long id
    ) {
        return branchCommitteeDao.findDetail(id);
    }

    public Long countByBranchId(Long branchId) {
        return branchCommitteeDao.countByBranchId(branchId);
    }

    public List<BranchCommittee> listBranchMember(Long branchId, List<String> designationCodes, String status, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        if (branchId != null) params.put("branchId", branchId);
        if (designationCodes != null && !designationCodes.isEmpty()) params.put("designationCodes", designationCodes);
        if (status != null && !status.isEmpty()) params.put("status", status);
        if (offset != null) params.put("offset", offset);
        if (limit != null) params.put("limit", limit);

        return branchCommitteeDao.listBranchMember(params);
    }

    public List<BranchCommittee> findByIdentificationNoAndActiveUserAndActiveBranch(String identificationNo) {
        Map<String, Object> params = new HashMap<>();
        if (identificationNo != null && !identificationNo.isEmpty()) params.put("committeeIcNo", identificationNo);
        params.put("committeeApplicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
        params.put("branchApplicationStatusCode", ApplicationStatusCode.LULUS.getCode());
        params.put("committeeStatusCode", StatusCode.AKTIF.getCode());
        params.put("branchStatusCode", StatusCode.AKTIF.getCode());
        return branchCommitteeDao.findByIdentificationNoAndActiveUserAndActiveBranch(params);
    }

    public List<BranchCommittee> findByIdentificationNoAndApplicationStatusCodeAndStatus(String identificationNo, Integer applicationStatusCode, String status) {
        Map<String, Object> params = new HashMap<>();
        if (identificationNo != null && !identificationNo.isEmpty()) params.put("committeeIcNo", identificationNo);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);
        if (status != null && !status.isEmpty()) params.put("status", status);
        return branchCommitteeDao.findByParam(params);
    }

    public BranchCommittee findActiveCommitteeInBranchWithRoles(List<String> positions, Long branchId) {
        Map<String, Object> params = new HashMap<>();
        if (positions != null && !positions.isEmpty()) params.put("positions", positions);
        if (branchId != null) params.put("branchId", branchId);
        params.put("status", StatusCode.AKTIF.getCode());
        params.put("applicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
        return branchCommitteeDao.findActiveCommitteeInBranchWithRoles(params);
    }

    public List<BranchCommittee> findActiveCommitteesInBranchWithRoles(List<String> positions, Long branchId) {
        Map<String, Object> params = new HashMap<>();
        if (positions != null && !positions.isEmpty()) params.put("positions", positions);
        if (branchId != null) params.put("branchId", branchId);
        params.put("status", StatusCode.AKTIF.getCode());
        params.put("applicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
        return branchCommitteeDao.findActiveCommitteesInBranchWithRoles(params);
    }

    public List<BranchCommittee> findActiveCommitteesInBranchWithRolesByBranchIds(List<String> positions, List<Long> branchIds) {
        Map<String, Object> params = new HashMap<>();
        params.put("positions", positions);
        params.put("branchIds", branchIds);
        params.put("status", StatusCode.AKTIF.getCode());
        params.put("applicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
        return branchCommitteeDao.findActiveCommitteesInBranchWithRolesByBranchIds(params);
    }

    public List<BranchCommittee> findListByIds(List<Long> currentCommitteeIds) {
        Map<String, Object> params = new HashMap<>();
        if (currentCommitteeIds != null && !currentCommitteeIds.isEmpty()) params.put("ids", currentCommitteeIds);
        return branchCommitteeDao.findByParam(params);
    }

    public List<LocalDate> findAllAppointedDates(Long branchId) {
        return branchCommitteeDao.findAllAppointedDates(branchId);
    }

    public List<BranchCommittee> findByParams(Map<String, Object> params) {
        return branchCommitteeDao.findByParam(params);
    }

    public List<BranchCommittee> findActiveByIdentificationNo(String identificationNo) {
        Map<String, Object> params = new HashMap<>();
        params.put("identificationNo", identificationNo);
        params.put("status", StatusCode.AKTIF.getCode());
        params.put("applicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
        return branchCommitteeDao.findActiveByIdentificationNo(params);
    }
}
