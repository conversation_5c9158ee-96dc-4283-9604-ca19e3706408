package com.eroses.external.society.service;

import com.eroses.external.society.mappers.AuditorDao;
import com.eroses.external.society.model.Auditor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuditorWriteDomainService {
    private final AuditorDao auditorDao;

    public void create(Auditor auditor) throws Exception {
        auditorDao.create(auditor);
    }

    public boolean update(Auditor auditor) throws Exception {
        return auditorDao.update(auditor);
    }
}
