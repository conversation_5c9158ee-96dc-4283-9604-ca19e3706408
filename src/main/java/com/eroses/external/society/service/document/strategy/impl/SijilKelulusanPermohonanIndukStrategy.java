package com.eroses.external.society.service.document.strategy.impl;

import com.eroses.external.society.model.AdmAddresses;
import com.eroses.external.society.model.DocumentTemplate;
import com.eroses.external.society.model.Society;
import com.eroses.external.society.model.enums.DocumentTemplateEnum;
import com.eroses.external.society.service.DocumentTemplateReadDomainService;
import com.eroses.external.society.service.S3DomainService;
import com.eroses.external.society.service.SocietyReadDomainService;
import com.eroses.external.society.service.admin.AdmAddressesReadDomainService;
import com.eroses.external.society.service.document.strategy.AbstractDocumentMappingStrategy;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.service.utils.HtmlGeneratorService;
import com.eroses.external.society.utils.MonthNameConverter;
import com.eroses.user.api.facade.UserFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * Strategy implementation for Sijil Kelulusan Permohonan Induk document type.
 */
@Slf4j
@Component
public class SijilKelulusanPermohonanIndukStrategy extends AbstractDocumentMappingStrategy {

    private final AdmAddressesReadDomainService admAddressesReadDomainService;

    public SijilKelulusanPermohonanIndukStrategy(
            DocumentTemplateReadDomainService documentTemplateReadDomainService,
            S3DomainService s3DomainService,
            PdfService pdfService,
            HtmlGeneratorService htmlGeneratorService,
            UserFacade userFacade,
            SocietyReadDomainService societyReadDomainService,
            AdmAddressesReadDomainService admAddressesReadDomainService) {
        super(documentTemplateReadDomainService, s3DomainService, pdfService, htmlGeneratorService, userFacade, societyReadDomainService);
        this.admAddressesReadDomainService = admAddressesReadDomainService;
    }

    @Override
    public String mapDataToDocument(String templateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        DocumentTemplate template = getDocumentTemplate(templateCode);
        String htmlTemplate = template.getHtmlContent();

        Map<String, String> mapFields = prepareDataForMapping(societyId, additionalParams);

        // Add dynamic document title for browser title bar
        Society society = societyReadDomainService.findById(societyId);
        String documentTitle = "SIJIL KELULUSAN" + "_" +
                              getValueOrDefault(society.getSocietyNo()) + "_" +
                              getValueOrDefault(society.getSocietyName());
        mapFields.put("{{DOCUMENT_TITLE}}", documentTitle);

        return htmlGeneratorService.mapToHtml(htmlTemplate, mapFields);
    }

    @Override
    protected Map<String, String> prepareDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {
        Society society = societyReadDomainService.findById(societyId);
        AdmAddresses admAddresses = admAddressesReadDomainService.findById(Long.valueOf(society.getStateCode()));
        return getMapFields(society, admAddresses);
    }

    @Override
    public boolean canHandle(String templateCode) {
        return DocumentTemplateEnum.SIJIL_KELULUSAN_PERMOHONAN_INDUK.getCode().equals(templateCode);
    }

    @Override
    public String getFileName(DocumentTemplate documentTemplate, Long societyId, Map<String, Object> additionalParams) throws Exception {
        // Get the latest society data from database
        Society society = societyReadDomainService.findById(societyId);

        // Build filename with structure: SIJIL KELULUSAN + "_" + societyNo + "_" + societyName
        return "SIJIL KELULUSAN" + "_" +
               getValueOrDefault(society.getSocietyNo()) + "_" +
               getValueOrDefault(society.getSocietyName());
    }

    private static Map<String, String> getMapFields(Society society, AdmAddresses admAddresses) {
        Map<String, String> mapFields = new HashMap<>();
        mapFields.put("{{NAMA_PERTUBUHAN}}", getValueOrDefault(society.getSocietyName()).toUpperCase());
        mapFields.put("{{NO_PERTUBUHAN}}", getValueOrDefault(society.getSocietyNo()));
        mapFields.put("{{ALAMAT_TEMPAT_URUSAN}}", buildSocietyAddress(society, admAddresses));
        mapFields.put("{{HH_haribulan_Bulan_TTTT}}", buildSocietyApprovedDate(society.getApprovedDate()));
        return mapFields;
    }

    private static String buildSocietyAddress(Society society, AdmAddresses admAddresses) {
        StringBuilder sb = new StringBuilder();

        sb.append(getValueOrDefault(society.getAddress()).toUpperCase());
        sb.append(", ");
        sb.append(getValueOrDefault(society.getPostcode()));
        sb.append(", ");
        sb.append(getValueOrDefault(society.getCity()).toUpperCase());
        sb.append(", ");
        sb.append(getValueOrDefault(admAddresses.getName()).toUpperCase());

        // Remove the trailing comma and space, if present
        if (sb.length() > 0 && sb.charAt(sb.length() - 2) == ',') {
            sb.delete(sb.length() - 2, sb.length());
        }

        return sb.toString();
    }

    private static String buildSocietyApprovedDate(LocalDate approvedDate) {
        if (approvedDate == null) {
            return "";
        }

        // Extract the day, month, and year
        int day = approvedDate.getDayOfMonth();
        String month = MonthNameConverter.convertMonthNameToMalay(approvedDate.getMonth().name());
        int year = approvedDate.getYear();

        return day + " haribulan " + month + " " + year;
    }
}