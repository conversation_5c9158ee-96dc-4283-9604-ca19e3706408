package com.eroses.external.society.service;

import com.eroses.external.society.dto.request.appeal.AppealSearchRequest;
import com.eroses.external.society.dto.response.AppealCountByType;
import com.eroses.external.society.dto.response.appeal.*;
import com.eroses.external.society.dto.response.roDecision.GetAllPendingAppealResponse;
import com.eroses.external.society.mappers.AppealDao;
import com.eroses.external.society.model.Appeal;
import com.eroses.external.society.model.RoApproval;
import com.eroses.external.society.model.RoQuery;
import com.eroses.external.society.model.enums.AppealReason;
import com.eroses.external.society.model.enums.ExceptionMessage;
import com.eroses.external.society.utils.Assert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppealReadDomainService {
    private final AppealDao appealDao;

    public List<Appeal> getAppeal(Integer offset, Integer limit) {
        return appealDao.getAll(offset, limit);
    }

    public Long countAllBranch() { return appealDao.countAll(); }

    public Appeal getById(Long id) { return appealDao.getById(id); }

    public List<GetByParamAppealResponse> getAppealByParam(Map<String, Object> params) {
        return appealDao.getAppealByParam(params);
    }
    public List<AppealAdminRecordsResponse> getAllByParams(Map<String, Object> params) {
        return appealDao.getAllByParams(params);
    }
    public Long countAllByParams(Map<String, Object> params) { return appealDao.countAllByParams(params); }


    public Appeal getByPaymentId(Long paymentId) {
        return appealDao.getByPaymentId(paymentId); }

    public List<AppealCountByType> getCountByType() {
        return appealDao.getCountByType();
    }

    public Long countAppealByParam(Map<String, Object> params) {
        return appealDao.countAppealByParams(params);
    }

    public List<GetSearchAppealResponse> searchAppeal(AppealSearchRequest request, Integer offset, Integer limit) {
        return appealDao.searchByName(request, offset, limit);
    }

    public Long countSearchAppeal(AppealSearchRequest request) {
        return appealDao.countSearchAppeal(request);
    }

    public List<GetAllPendingAppealResponse> getAllPendingAppeal(Integer isQuery, String searchQuery, List<Integer> applicationStatusCodes, String stateCodeQuery, Long roIdQuery, String categoryCodeJppm, String subCategoryCode, String societyName, Long idSebab, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();

        if (isQuery != null) params.put("isQuery", isQuery);
        if (searchQuery != null && !searchQuery.isEmpty()) params.put("searchQuery", searchQuery);
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCodeQuery != null && !stateCodeQuery.isEmpty()) params.put("stateCode", stateCodeQuery);
        if (roIdQuery != null) params.put("ro", roIdQuery);
        if (categoryCodeJppm != null && !categoryCodeJppm.isEmpty()) params.put("categoryCodeJppm", categoryCodeJppm);
        if (subCategoryCode != null && !subCategoryCode.isEmpty()) params.put("subCategoryCode", subCategoryCode);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (idSebab != null && idSebab != 0L) params.put("idSebab", idSebab);
        params.put("offset", offset);
        params.put("limit", limit);

        return appealDao.getAllPendingAppeal(params);
    }

    public Integer countAllPending(Integer isQuery, String searchQuery, List<Integer> applicationStatusCodes, String stateCodeQuery, Long roIdQuery, String categoryCodeJppm, String subCategoryCode, String societyName, Long idSebab) {
        Map<String, Object> params = new HashMap<>();

        if (isQuery != null) params.put("isQuery", isQuery);
        if (searchQuery != null && !searchQuery.isEmpty()) params.put("searchQuery", searchQuery);
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCodeQuery != null && !stateCodeQuery.isEmpty()) params.put("stateCode", stateCodeQuery);
        if (roIdQuery != null) params.put("ro", roIdQuery);
        if (categoryCodeJppm != null && !categoryCodeJppm.isEmpty()) params.put("categoryCodeJppm", categoryCodeJppm);
        if (subCategoryCode != null && !subCategoryCode.isEmpty()) params.put("subCategoryCode", subCategoryCode);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (idSebab != null && idSebab != 0L) params.put("idSebab", idSebab);

        return appealDao.countAllPendingAppeal(params);
    }

    public List<GetUserSocietiesForAppealResponse> getAppealsByUser(Map<String, Object> param) {
        return appealDao.getAppealsByUser(param);
    }

    public Long countAppealsByUser(Map<String, Object> param) {
        return appealDao.countAppealsByUser(param);
    }

    public Boolean removeAppealApplication(Long id) {
        Boolean isDeleted = appealDao.removeAppealApplication(id);
        Assert.isTrue(isDeleted, "Removing appeal record is unsuccessful.");

        return isDeleted;
    }

    public RoApproval findAppealTypeForRoApproval(RoApproval roApproval, AppealReason appealType, Appeal appeal) throws Exception {
        switch (appealType) {
            case PEMBATALAN_PENDAFTARAN_PERTUBUHAN_2A, PEMBATALAN_PENDAFTARAN_13, PEMBATALAN_PENDAFTARAN_16 -> {
                return roApproval;
            }
            case PENOLAKAN_PENDAFTARAN_7 -> {
                return roApproval;
            }
            case PENOLAKAN_PENGECUALIAN_9A -> {
                return roApproval;
            }
            case PENOLAKAN_PINDAAN_UU_11 -> {
                roApproval.setAmendmentId(appeal.getAmendmentId());
                return roApproval;
            }
            case PENOLAKAN_CAWANGAN_12 -> {
                roApproval.setBranchId(appeal.getBranchId());
                roApproval.setBranchNo(appeal.getBranchNo());
                return roApproval;
            }
            case PERINTAH_13A_1 -> {
                return roApproval;
            }
            case PERINTAH_13A_2 -> {
                return roApproval;
            }
            case PENOLAKAN_JURUAUDIT_14_4 -> {
                return roApproval;
            }
            case PERINTAH_14_5 -> {
                return roApproval;
            }
            case PENOLAKAN_MEMEGANG_JAWATAN_49 -> {
                return roApproval;
            }
            case null, default -> {
                throw new Exception(ExceptionMessage.DATA_NOT_FOUND.getMessage("Appeal Reason Type not found with " + appealType.getMessage()));
            }
        }
    }

    public RoQuery findAppealTypeForRoQuery(RoQuery roQuery, AppealReason appealType, Appeal appeal) throws Exception {
        switch (appealType) {
            case PEMBATALAN_PENDAFTARAN_PERTUBUHAN_2A, PEMBATALAN_PENDAFTARAN_13, PEMBATALAN_PENDAFTARAN_16 -> {
                return roQuery;
            }
            case PENOLAKAN_PENDAFTARAN_7 -> {
                return roQuery;
            }
            case PENOLAKAN_PENGECUALIAN_9A -> {
                return roQuery;
            }
            case PENOLAKAN_PINDAAN_UU_11 -> {
                roQuery.setAmendmentId(appeal.getAmendmentId());
                return roQuery;
            }
            case PENOLAKAN_CAWANGAN_12 -> {
                roQuery.setBranchId(appeal.getBranchId());
                roQuery.setBranchNo(appeal.getBranchNo());
                return roQuery;
            }
            case PERINTAH_13A_1 -> {
                return roQuery;
            }
            case PERINTAH_13A_2 -> {
                return roQuery;
            }
            case PENOLAKAN_JURUAUDIT_14_4 -> {
                return roQuery;
            }
            case PERINTAH_14_5 -> {
                return roQuery;
            }
            case PENOLAKAN_MEMEGANG_JAWATAN_49 -> {
                return roQuery;
            }
            case null, default -> {
                throw new Exception(ExceptionMessage.DATA_NOT_FOUND.getMessage("Appeal Reason Type not found with " + appealType.getMessage()));
            }
        }
    }

    public List<Appeal> findByCriteria(int applicationStatusCode){
        //Add more criteria if needed
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode
        );
        return appealDao.findByCriteria(params);
    }

    public List<Long> getAllExpiredUnpaidSocietyAppealId(int pendingCounterPaymentStatusCode, int pendingOnlinePaymentStatusCode, int paymentPeriodDays) {
        Map<String, Object> params = Map.of(
                "pendingCounterPaymentStatusCode", pendingCounterPaymentStatusCode,
                "pendingOnlinePaymentStatusCode", pendingOnlinePaymentStatusCode,
                "paymentPeriodDays", paymentPeriodDays
        );
        return appealDao.getAllExpiredUnpaidSocietyAppealId(params);
    }

    public List<GetAllDecisionRecordAppealResponse> getAllDecisionRecordAppeal(Long stateCode, String decision, String searchQuery, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();

        if (stateCode != null) params.put("stateCode", stateCode);
        if (decision != null && !decision.isEmpty()) params.put("decision", decision);
        if (searchQuery != null && !searchQuery.isEmpty()) params.put("searchQuery", searchQuery);
        params.put("offset", offset);
        params.put("limit", limit);

        return appealDao.getAllDecisionRecordAppeal(params);
    }

    public Long countAllDecisionRecordAppeal(Long stateCode, String decision, String searchQuery) {
        Map<String, Object> params = new HashMap<>();

        if (stateCode != null) params.put("stateCode", stateCode);
        if (decision != null && !decision.isEmpty()) params.put("decision", decision);
        if (searchQuery != null && !searchQuery.isEmpty()) params.put("searchQuery", searchQuery);

        return appealDao.countAllDecisionRecordAppeal(params);
    }

    public AppealAdminRecordByIdResponse getAdminRecord(Long appealId) {
        return appealDao.getAdminRecord(appealId);
    }

    public List<GetByParamAppealResponse> getAppealsByParamEnhanced(Map<String, Object> params) {
        return appealDao.getAppealsByParamEnhanced(params);
    }

    public Long countAppealsByParamEnhanced(Map<String, Object> params) {
        return appealDao.countAppealsByParamEnhanced(params);
    }
}
