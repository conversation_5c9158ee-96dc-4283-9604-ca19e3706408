package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.EventFeedbackScaleDao;
import com.eroses.external.society.model.EventFeedbackScale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventFeedbackScaleWriteDomainService {
    private final EventFeedbackScaleDao eventFeedbackScaleDao;

    public Boolean create(EventFeedbackScale eventFeedbackScale) {
        log.info("Creating Event Feedback Scale: {}", eventFeedbackScale);
        return eventFeedbackScaleDao.create(eventFeedbackScale);
    }

    public boolean update(EventFeedbackScale eventFeedbackScale) {
        log.info("Updating Event Feedback Scale with ID: {}", eventFeedbackScale.getId());
        return eventFeedbackScaleDao.update(eventFeedbackScale) > 0;
    }

    public boolean delete(Long id) {
        log.info("Deleting Event Feedback Scale with ID: {}", id);
        return eventFeedbackScaleDao.delete(id) > 0;
    }
}