package com.eroses.external.society.service.training;

import com.eroses.external.society.mappers.TrainingCertificateDao;
import com.eroses.external.society.mappers.TrainingCourseDao;
import com.eroses.external.society.mappers.TrainingEnrollmentDao;
import com.eroses.external.society.model.TrainingCertificate;
import com.eroses.external.society.model.TrainingCourse;
import com.eroses.external.society.model.TrainingEnrollment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Service for creating and managing training certificates.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrainingCertificateWriteDomainService {

    private final TrainingCertificateDao trainingCertificateDao;
    private final TrainingEnrollmentDao trainingEnrollmentDao;
    private final TrainingCourseDao trainingCourseDao;
    private final CertificateGenerationService certificateGenerationService;
    private final TrainingUserContextService userContextService;

    // No longer need local storage path as we're using S3

    /**
     * Creates a new training certificate for a completed enrollment.
     *
     * @param enrollment The completed training enrollment
     * @param userName The name of the user who completed the training
     * @return The ID of the created certificate
     * @throws IllegalArgumentException If the enrollment is null or not completed
     * @throws IllegalStateException If the course is not found
     * @throws RuntimeException If there is an error creating the certificate
     */
    @Transactional
    public Long createTrainingCertificate(TrainingEnrollment enrollment) {
        // Get user name from context
        String userName = userContextService.getCurrentUserName();
        if (userName == null) {
            userName = "Unknown User"; // Fallback if user name is not available
        }
        // Validate enrollment
        if (enrollment == null) {
            throw new IllegalArgumentException("Enrollment cannot be null");
        }

        if (!"COMPLETED".equals(enrollment.getCompletionStatus())) {
            throw new IllegalArgumentException("Cannot generate certificate for incomplete training. Status: " +
                    enrollment.getCompletionStatus());
        }

        // Check if certificate already exists
        TrainingCertificate existingCertificate = trainingCertificateDao.findByEnrollmentId(enrollment.getId());
        if (existingCertificate != null) {
            log.info("Certificate already exists for enrollment ID: {}", enrollment.getId());
            return existingCertificate.getId();
        }

        try {
            // Get course details
            TrainingCourse course = trainingCourseDao.findById(enrollment.getTrainingCourseId());
            if (course == null) {
                throw new IllegalStateException("Course not found for enrollment: " + enrollment.getId());
            }

            // Generate verification code
            String verificationCode = generateVerificationCode();

            // Generate certificate file path
            String certificatePath = generateCertificateFilePath(enrollment.getUserId(), course.getTitle(), verificationCode);

            // Create certificate record
            TrainingCertificate certificate = TrainingCertificate.builder()
                    .trainingEnrollmentId(enrollment.getId())
                    .certificatePath(certificatePath)
                    .issueDate(LocalDateTime.now())
                    .verificationCode(verificationCode)
                    .build();

            boolean isCreated = trainingCertificateDao.create(certificate);
            if (!isCreated) {
                throw new RuntimeException("Failed to create training certificate record in database");
            }

            // Generate certificate PDF and upload to S3
            try {
                // Generate certificate and get S3 URL
                String s3Url = certificateGenerationService.generateCertificate(
                        certificate,
                        course.getTitle(),
                        userName,
                        enrollment.getCompletionDate() != null ? enrollment.getCompletionDate() : LocalDateTime.now()
                );

                // Update certificate with actual S3 URL
                certificate.setCertificatePath(s3Url);
                boolean isUpdated = trainingCertificateDao.update(certificate);
                if (!isUpdated) {
                    log.warn("Failed to update certificate with S3 URL. Certificate ID: {}", certificate.getId());
                }
            } catch (Exception e) {
                log.error("Error generating certificate PDF", e);
                throw new RuntimeException("Failed to generate certificate PDF: " + e.getMessage());
            }

            // Update enrollment with certificate ID
            try {
                boolean isUpdated = trainingEnrollmentDao.updateCertificateId(enrollment.getId(), certificate.getId().toString());
                if (!isUpdated) {
                    log.warn("Failed to update enrollment with certificate ID. Enrollment ID: {}, Certificate ID: {}",
                            enrollment.getId(), certificate.getId());
                }
            } catch (Exception e) {
                log.error("Error updating enrollment with certificate ID", e);
                // Continue execution - this is not a critical error
            }

            log.info("Certificate created successfully. ID: {}, Path: {}", certificate.getId(), certificatePath);
            return certificate.getId();
        } catch (DataAccessException e) {
            log.error("Database error while creating certificate for enrollment ID: {}", enrollment.getId(), e);
            throw new RuntimeException("Database error while creating certificate: " + e.getMessage(), e);
        } catch (IllegalArgumentException | IllegalStateException e) {
            // Re-throw these exceptions as they are already properly formatted
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error while creating certificate for enrollment ID: {}", enrollment.getId(), e);
            throw new RuntimeException("Unexpected error while creating certificate: " + e.getMessage(), e);
        }
    }

    /**
     * Generates a unique verification code for a certificate.
     *
     * @return A UUID string to use as verification code
     */
    private String generateVerificationCode() {
        return UUID.randomUUID().toString();
    }

    /**
     * Generates a placeholder URL for a certificate PDF.
     * This will be replaced with the actual S3 URL after the certificate is generated and uploaded.
     *
     * @param userId The ID of the user who earned the certificate
     * @param courseTitle The title of the completed course
     * @param verificationCode The verification code for the certificate
     * @return A placeholder URL for the certificate
     */
    private String generateCertificateFilePath(Long userId, String courseTitle, String verificationCode) {
        // Sanitize course title for filename
        String sanitizedTitle = courseTitle.replaceAll("[^a-zA-Z0-9]", "_");

        // Generate a placeholder URL - this will be replaced with the actual S3 URL
        return "placeholder_" + userId.toString() + "_" + sanitizedTitle + "_" + verificationCode;
    }
}
