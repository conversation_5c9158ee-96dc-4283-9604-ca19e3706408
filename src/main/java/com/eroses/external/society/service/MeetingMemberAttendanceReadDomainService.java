package com.eroses.external.society.service;

import com.eroses.external.society.mappers.MeetingMemberAttendanceDao;
import com.eroses.external.society.model.MeetingMemberAttendance;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingMemberAttendanceReadDomainService {
    private final MeetingMemberAttendanceDao meetingMemberAttendanceDao;

    public List<MeetingMemberAttendance> getAllByMeetingId(Long meetingId) {
        return meetingMemberAttendanceDao.getAllByMeetingId(meetingId);
    }
}
