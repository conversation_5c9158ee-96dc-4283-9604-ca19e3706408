package com.eroses.external.society.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.eroses.external.society.model.enums.UserRoleEnum;
import com.eroses.external.society.utils.InternalUserUtil;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.dto.response.GetAllUserRoleByUserIdResponse;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class InternalUserService {
    private final UserFacade userFacade;

    public boolean handleUserROCheck(Long userId) throws Exception {
        List<GetAllUserRoleByUserIdResponse> roles;

        try {
            roles = userFacade.getAllUserRoleByUserId(userId);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }

        Boolean userRO = InternalUserUtil.checkUserRO(roles);

        if (userRO == null) {
            throw new RuntimeException("Unauthorized test");
        }

        return userRO;
    }

    public String getHighestRole(Long userId) throws Exception {
        List<GetAllUserRoleByUserIdResponse> roles = userFacade.getAllUserRoleByUserId(userId);
        List<String> roleString = roles
                .stream()
                .map(r -> r.getRoleId())
                .collect(Collectors.toList());

        if (roleString.contains(UserRoleEnum.SUPER_ADMIN.getRole())) {
            return UserRoleEnum.SUPER_ADMIN.getRole();
        } else if (roleString.contains(UserRoleEnum.PENOLONG_PEGAWAI_PENDAFTAR.getRole())) {
            return UserRoleEnum.PENOLONG_PEGAWAI_PENDAFTAR.getRole();
        } else if (roleString.contains(UserRoleEnum.PEGAWAI_PENDAFTARAN_PERTUBUHAN.getRole())) {
            return UserRoleEnum.PEGAWAI_PENDAFTARAN_PERTUBUHAN.getRole();
        }

        throw new RuntimeException("Unauthorized");
    }
}
