package com.eroses.external.society.service;

import com.eroses.external.society.dto.response.UploadDocumentResponse;
import com.eroses.external.society.mappers.DocumentDao;
import java.io.*;
import java.net.URI;
import com.eroses.external.society.model.CustomMultipartFile;
import com.eroses.external.society.model.Document;
import com.eroses.external.society.model.enums.DocumentTypeEnum;
import com.eroses.external.society.utils.AwsS3Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.core.sync.RequestBody;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class S3DomainService {

    private final AwsS3Utils awsS3Utils;
    private final DocumentUploadMetadataDomainService documentUploadMetadataDomainService;
    private final DocumentDao documentDao;

    @Value("${aws.s3.bucket-name}")
    private String bucketName;

    private static final List<String> allowedContentType = Arrays.asList(
            "application/pdf", "image/jpeg", "image/png", "image/webp",
            "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    );

    public String uploadFile(MultipartFile file, String folderName, String key) {
        validateFileType(file);

        S3Client s3Client = awsS3Utils.createS3Client();
        try (InputStream fileInputStream = file.getInputStream()) {
            String s3Key = folderName + "/" + key;
            String versionId = null;

            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(s3Key)
                    .contentType(file.getContentType())
                    .build();

            byte[] bytes = file.getBytes();
            RequestBody requestBody = RequestBody.fromBytes(bytes);

            s3Client.putObject(putObjectRequest, requestBody);

            String fileUrl = s3Client.utilities().getUrl(GetUrlRequest.builder()
                    .bucket(bucketName)
                    .key(s3Key)
                    .build()).toExternalForm();

            ListObjectVersionsRequest listVersionsRequest = ListObjectVersionsRequest.builder()
                    .bucket(bucketName)
                    .prefix(s3Key)
                    .build();

            ListObjectVersionsResponse response = s3Client.listObjectVersions(listVersionsRequest);
            for (ObjectVersion versionSummary : response.versions()) {
                if (versionSummary.key().equals(s3Key)) {
                    versionId = versionSummary.versionId();
                    break;
                }
            }

            return fileUrl + "?versionId=" + versionId;
        } catch (S3Exception e) {
            e.printStackTrace(); // Use logging framework in production
            throw new RuntimeException("Failed to upload file to S3: " + e.awsErrorDetails().errorMessage(), e);
        } catch (IOException e) {
            e.printStackTrace(); // Use logging framework in production
            throw new RuntimeException("Failed to process the file", e);
        }
    }

    public Boolean checkIfFileExists(String url) {
        S3Client s3Client = awsS3Utils.createS3Client();
        try {
            URI uri = new URI(url);
            String host = uri.getHost();
//            String bucketName = host.split("\\.")[0];
            String s3Key = uri.getPath().substring(1);

            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(s3Key)
                    .build();

            // If the object exists, headObject will succeed without an exception
            s3Client.headObject(headObjectRequest);
             return true;
        } catch (S3Exception e) {
            if (e.statusCode() == 404) {
                return false;
            }
            throw new RuntimeException("Error checking object existence: " + e.awsErrorDetails().errorMessage(), e);
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
    }

    public Boolean deleteDocument(String fileUrl) {
        Boolean deleteSuccess = false;
        S3Client s3Client = awsS3Utils.createS3Client();
        try {
            // Extract bucket name and key from URL
            URI uri = new URI(fileUrl);
            String host = uri.getHost();
//            String bucketName = host.split("\\.")[0];
            String s3Key = uri.getPath().substring(1);

            // Build delete request
            DeleteObjectRequest deleteRequest = DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(s3Key)
                    .build();

            s3Client.deleteObject(deleteRequest);
            deleteSuccess = true;
            log.info("Successfully deleted document from S3: {}", fileUrl);
        } catch (Exception e) {
            log.error("Failed to delete document from S3: {}. Error: {}", fileUrl, e.getMessage());
        }
        return deleteSuccess;
    }

    private void validateFileType(MultipartFile file) {
        String contentType = file.getContentType();
        if (!allowedContentType.contains(contentType)) {
            throw new IllegalArgumentException("File type not allowed: " + contentType);
        }
    }

    //generate presigned url
    public UploadDocumentResponse generatePresignedUrl(Document document) {
        S3Client s3Client = awsS3Utils.createS3Client();
        S3Presigner s3Presigner = awsS3Utils.createS3Presigner();

        String uuid = UUID.randomUUID().toString();
        String fileName = document.getName();

        String folder = document.getType() + "/";
        String s3Key = fileName + "_" + uuid;

        // Store metadata temporarily
        documentUploadMetadataDomainService.storeMetadata(uuid, document);

        try {
            Duration expiration = Duration.ofMinutes(5);

            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(folder + s3Key)
                    .build();

            PutObjectPresignRequest presignRequest = PutObjectPresignRequest.builder()
                    .putObjectRequest(putObjectRequest)
                    .signatureDuration(expiration)
                    .build();

            String presignedUrl = s3Presigner.presignPutObject(presignRequest).url().toString();

            //setup document response for FE
            if (presignedUrl != null) {
                UploadDocumentResponse response = new UploadDocumentResponse();
                response.setPresignedUrl(presignedUrl);

                document.setUrl(s3Key);
                document.setType(String.valueOf(DocumentTypeEnum.getDocumentCode(document.getType().toUpperCase())));
                Long documentRegister = documentDao.registerFileInDb(document);

                response.setDocument(document);
                return response;
            }
            return null;
        } catch (Exception e) {
            log.error("Error generating presigned URL: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to generate presigned URL", e);
        } finally {
            // Close the presigner to release resources
            s3Presigner.close();
        }
    }

    //function to validate file
    public String checkForUpload(String key, String folder) {
        S3Client s3Client = awsS3Utils.createS3Client();
        try {
            String s3Key = folder + "/" + key;
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(s3Key)
                    .build();

            // If the object exists, headObject will succeed without an exception
            s3Client.headObject(headObjectRequest);

            String fileUrl = s3Client.utilities().getUrl(GetUrlRequest.builder()
                    .bucket(bucketName)
                    .key(s3Key)
                    .build()).toExternalForm();

            ListObjectVersionsRequest listVersionsRequest = ListObjectVersionsRequest.builder()
                    .bucket(bucketName)
                    .prefix(s3Key)
                    .build();

            String versionId = null;
            ListObjectVersionsResponse response = s3Client.listObjectVersions(listVersionsRequest);
            for (ObjectVersion versionSummary : response.versions()) {
                if (versionSummary.key().equals(s3Key)) {
                    versionId = versionSummary.versionId();
                    break;
                }
            }

            return fileUrl + "?versionId=" + versionId;
        } catch (S3Exception e) {
            e.printStackTrace();
            // Use logging framework in production
            documentDao.unregisterFromDb(key);
            throw new RuntimeException("Failed to find file in S3: " + e.awsErrorDetails().errorMessage(), e);
        }
    }

    public byte[] getDocumentFromS3(String url) {
        try {
            // Parse the URL directly without decoding first
            URI uri = new URI(url);

            // Now decode the path component
            String decodedPath = URLDecoder.decode(uri.getPath(), StandardCharsets.UTF_8.toString());
            // Remove the leading '/' if present
            String s3Key = decodedPath.startsWith("/") ? decodedPath.substring(1) : decodedPath;

            // Extract versionId if present
            String versionId = null;
            String query = uri.getQuery();
            if (query != null && query.contains("versionId=")) {
                versionId = query.split("versionId=")[1];
            }

            log.debug("Extracting document with key: {} and versionId: {}", s3Key, versionId);

            S3Client s3Client = awsS3Utils.createS3Client();

            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(s3Key)
                    .versionId(versionId)
                    .build();

            ResponseInputStream<GetObjectResponse> response = s3Client.getObject(getObjectRequest);

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            response.transferTo(baos);

            return baos.toByteArray();
        } catch (S3Exception s3e) {
            log.error("Failed to retrieve document from S3: {}", s3e.getMessage(), s3e);
            throw new RuntimeException("Failed to retrieve document from S3: " + s3e.getMessage(), s3e);
        } catch (Exception e) {
            log.error("Failed to process document: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to process document: " + e.getMessage(), e);
        }
    }

    public String uploadXWPFDocument(XWPFDocument doc, String folderName, String key) throws IOException {
        // Convert XWPFDocument to byte array
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        doc.write(byteArrayOutputStream);
        byte[] byteArray = byteArrayOutputStream.toByteArray();

        // Create custom MultipartFile from byte array
        MultipartFile multipartFile = new CustomMultipartFile(
                byteArray,
                key,
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        );

        // Call your existing upload method
        return uploadFile(multipartFile, folderName, key);
    }
}
