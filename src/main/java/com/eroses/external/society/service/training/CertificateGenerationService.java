package com.eroses.external.society.service.training;

import com.eroses.external.society.model.TrainingCertificate;
import com.eroses.external.society.model.TrainingCourse;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Service for generating training certificates using iText PDF library.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CertificateGenerationService {

    private final TrainingS3Service trainingS3Service;

    /**
     * Generate a certificate for a completed training course and upload it to S3.
     *
     * @param certificate The certificate entity with metadata
     * @param courseTitle The title of the completed course
     * @param userName The name of the user who completed the course
     * @param completionDate The date when the course was completed
     * @return The S3 URL of the generated certificate
     */
    public String generateCertificate(TrainingCertificate certificate, String courseTitle,
                                     String userName, LocalDateTime completionDate) {
        try {
            // Generate PDF in memory
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            // Create PDF document
            PdfWriter writer = new PdfWriter(outputStream);
            PdfDocument pdf = new PdfDocument(writer);
            Document document = new Document(pdf, PageSize.A4.rotate());

            // Set margins
            document.setMargins(50, 50, 50, 50);

            // Create fonts
            PdfFont titleFont = PdfFontFactory.createFont();
            PdfFont normalFont = PdfFontFactory.createFont();
            PdfFont boldFont = PdfFontFactory.createFont();

            // Create certificate border
            Table borderTable = new Table(1);
            borderTable.setWidth(UnitValue.createPercentValue(100));

            Cell borderCell = new Cell();
            borderCell.setBorder(Border.NO_BORDER);
            borderCell.setPadding(10);

            // Create content table
            Table contentTable = new Table(1);
            contentTable.setWidth(UnitValue.createPercentValue(100));
            contentTable.setHorizontalAlignment(HorizontalAlignment.CENTER);

            // Add certificate title
            Cell titleCell = new Cell();
            titleCell.setBorder(Border.NO_BORDER);
            titleCell.setPadding(10);

            Paragraph title = new Paragraph("CERTIFICATE OF COMPLETION")
                    .setFont(titleFont)
                    .setFontSize(24)
                    .setFontColor(new DeviceRgb(0, 102, 204))
                    .setTextAlignment(TextAlignment.CENTER);

            titleCell.add(title);
            contentTable.addCell(titleCell);

            // Add certificate content
            Cell contentCell = new Cell();
            contentCell.setBorder(Border.NO_BORDER);
            contentCell.setPadding(10);

            Paragraph content = new Paragraph("This is to certify that")
                    .setFont(normalFont)
                    .setFontSize(14)
                    .setTextAlignment(TextAlignment.CENTER);

            Paragraph userName_p = new Paragraph(userName)
                    .setFont(boldFont)
                    .setFontSize(20)
                    .setTextAlignment(TextAlignment.CENTER)
                    .setFontColor(new DeviceRgb(0, 102, 204));

            Paragraph completion = new Paragraph("has successfully completed the course")
                    .setFont(normalFont)
                    .setFontSize(14)
                    .setTextAlignment(TextAlignment.CENTER);

            Paragraph courseTitle_p = new Paragraph(courseTitle)
                    .setFont(boldFont)
                    .setFontSize(18)
                    .setTextAlignment(TextAlignment.CENTER)
                    .setFontColor(new DeviceRgb(0, 102, 204));

            Paragraph date = new Paragraph("on " + completionDate.format(DateTimeFormatter.ofPattern("dd MMMM yyyy")))
                    .setFont(normalFont)
                    .setFontSize(14)
                    .setTextAlignment(TextAlignment.CENTER);

            contentCell.add(content);
            contentCell.add(userName_p);
            contentCell.add(completion);
            contentCell.add(courseTitle_p);
            contentCell.add(date);
            contentTable.addCell(contentCell);

            // Add verification code
            Cell verificationCell = new Cell();
            verificationCell.setBorder(Border.NO_BORDER);
            verificationCell.setPadding(10);

            Paragraph verification = new Paragraph("Verification Code: " + certificate.getVerificationCode())
                    .setFont(normalFont)
                    .setFontSize(10)
                    .setTextAlignment(TextAlignment.CENTER);

            verificationCell.add(verification);
            contentTable.addCell(verificationCell);

            // Add signature
            Cell signatureCell = new Cell();
            signatureCell.setBorder(Border.NO_BORDER);
            signatureCell.setPadding(10);

            Paragraph signature = new Paragraph("_______________________")
                    .setFont(normalFont)
                    .setFontSize(12)
                    .setTextAlignment(TextAlignment.CENTER);

            Paragraph signatureTitle = new Paragraph("Authorized Signature")
                    .setFont(normalFont)
                    .setFontSize(10)
                    .setTextAlignment(TextAlignment.CENTER);

            signatureCell.add(signature);
            signatureCell.add(signatureTitle);
            contentTable.addCell(signatureCell);

            // Add content table to border cell
            borderCell.add(contentTable);
            borderTable.addCell(borderCell);

            // Add border table to document
            document.add(borderTable);

            // Close document
            document.close();

            // Get the PDF as byte array
            byte[] pdfBytes = outputStream.toByteArray();

            // Generate filename
            String sanitizedTitle = courseTitle.replaceAll("[^a-zA-Z0-9]", "_");
            String filename = String.format("%s_%s_%s.pdf", userName.replaceAll("\\s+", "_"), sanitizedTitle, certificate.getVerificationCode());

            // Upload to S3
            String s3Url = trainingS3Service.uploadCertificate(pdfBytes, filename);

            log.info("Certificate generated and uploaded to S3: {}", s3Url);
            return s3Url;
        } catch (IOException e) {
            log.error("Error generating certificate", e);
            throw new RuntimeException("Failed to generate certificate: " + e.getMessage());
        }
    }
}
