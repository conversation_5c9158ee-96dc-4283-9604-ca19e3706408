package com.eroses.external.society.service.societyLiquidation;

import com.eroses.external.society.api.converter.input.societyLiquidation.RoLiquidationApprovalApiInputConverter;
import com.eroses.external.society.dto.request.societyLiquidation.RoLiquidationApprovalCreateRequest;
import com.eroses.external.society.dto.request.societyLiquidation.RoLiquidationApprovalUpdateRequest;
import com.eroses.external.society.mappers.liquidation.RoLiquidationApprovalDao;
import com.eroses.external.society.model.societyLiquidation.RoLiquidationApproval;
import com.eroses.user.api.facade.UserFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class RoLiquidationApprovalWriteDomainService {
    private final UserFacade userFacade;
    private final RoLiquidationApprovalDao roLiquidationApprovalDao;
    private final RoLiquidationApprovalApiInputConverter converter;

    public Long create(RoLiquidationApproval roLiquidationApproval) throws Exception {
        roLiquidationApprovalDao.create(roLiquidationApproval);
        return roLiquidationApproval.getId();
    }

    public Long update(RoLiquidationApprovalUpdateRequest request) throws Exception {
        RoLiquidationApproval roLiquidationApproval = converter.toModel(request);
        roLiquidationApprovalDao.update(roLiquidationApproval);

        return roLiquidationApproval.getId();
    }
}
