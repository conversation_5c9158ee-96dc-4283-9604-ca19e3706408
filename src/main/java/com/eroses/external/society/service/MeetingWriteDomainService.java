package com.eroses.external.society.service;

import com.eroses.external.society.model.Meeting;
import com.eroses.external.society.mappers.MeetingDao;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingWriteDomainService {
    private final MeetingDao meetingDao;

    public void create(Meeting meeting) throws Exception {
        meetingDao.create(meeting);
    }

    public Boolean update(Meeting meeting) throws Exception {
        return meetingDao.update(meeting);
    }
}
