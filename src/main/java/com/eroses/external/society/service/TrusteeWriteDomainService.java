package com.eroses.external.society.service;

import com.eroses.external.society.mappers.TrusteeHolderDao;
import com.eroses.external.society.model.TrusteeHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class TrusteeWriteDomainService {

    private final TrusteeHolderDao trusteeHolderDao;

    public Long create(TrusteeHolder trusteeHolder) throws Exception {
        trusteeHolderDao.create(trusteeHolder);
        return trusteeHolder.getId();
    }

    public boolean update(TrusteeHolder trusteeHolder) throws Exception {
        return trusteeHolderDao.update(trusteeHolder);
    }

}
