package com.eroses.external.society.service.blacklist;

import com.eroses.external.society.mappers.blacklist.BlacklistUserDao;
import com.eroses.external.society.model.blacklist.BlacklistUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class BlacklistUserWriteDomainService {
    private final BlacklistUserDao blacklistUserDao;

    @Transactional
    public Long create(BlacklistUser blacklistUser) throws Exception {
        blacklistUserDao.create(blacklistUser);
        return blacklistUser.getId();
    }

    @Transactional
    public Long update(BlacklistUser blacklistUser) throws Exception {
        blacklistUserDao.update(blacklistUser);
        return blacklistUser.getId();
    }

    @Transactional
    public Boolean delete(Long id) throws Exception {
        return blacklistUserDao.delete(id);
    }
}