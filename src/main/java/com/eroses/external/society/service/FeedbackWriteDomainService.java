package com.eroses.external.society.service;


import com.eroses.external.society.mappers.FeedbackDao;
import com.eroses.external.society.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class FeedbackWriteDomainService {
    private final FeedbackDao feedbackDao;

    public Long create(Feedback feedback) throws Exception {
        feedbackDao.create(feedback);
        return feedback.getId();
    }

    public Long update(Feedback feedback) throws Exception {
        feedbackDao.update(feedback);
        return feedback.getId();
    }
}
