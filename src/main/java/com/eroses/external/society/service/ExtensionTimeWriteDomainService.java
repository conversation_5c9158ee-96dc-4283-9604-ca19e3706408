package com.eroses.external.society.service;

import com.eroses.external.society.mappers.ExtensionTimeDao;
import com.eroses.external.society.model.Branch;
import com.eroses.external.society.model.ExtensionTime;
import com.eroses.external.society.model.NonCitizenCommittee;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExtensionTimeWriteDomainService {
    private final ExtensionTimeDao extensionTimeDao;

    public Long create(ExtensionTime extensionTime) throws Exception {
        extensionTime.setApplicationDate(LocalDateTime.now());
        extensionTimeDao.create(extensionTime);
        return extensionTime.getId();
    }

    public Long update(ExtensionTime extensionTime) throws Exception {
        extensionTimeDao.update(extensionTime);
        return extensionTime.getId();
    }

    public ExtensionTime updateRoDecision(ExtensionTime extensionTime, int applicationStatusCode) throws Exception {

        // Update application status code and status based on RO decision
        extensionTime.setApplicationStatusCode(applicationStatusCode);
        extensionTime.setDecision(String.valueOf(applicationStatusCode));
        extensionTimeDao.update(extensionTime);
        return extensionTime;
    }
}
