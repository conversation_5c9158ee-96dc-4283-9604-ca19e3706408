package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmReligionDao;
import com.eroses.external.society.model.lookup.AdmReligion;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmReligionWriteDomainService {
    private final AdmReligionDao admReligionDao;

    public Long create(AdmReligion religion) throws Exception {
        admReligionDao.create(religion);
        return religion.getId();
    }

    public boolean update(AdmReligion religion) throws Exception {
        return admReligionDao.update(religion);
    }

    public Long delete(Long id) throws Exception {
        admReligionDao.delete(id);
        return id;
    }
}