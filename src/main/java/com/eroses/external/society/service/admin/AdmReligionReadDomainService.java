package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmReligionDao;
import com.eroses.external.society.model.lookup.AdmReligion;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmReligionReadDomainService {
    private final AdmReligionDao admReligionDao;

    public AdmReligion findById(Long id) {
        return admReligionDao.findById(id);
    }

    public List<AdmReligion> getAll(String nameQuery, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "nameQuery", nameQuery,
                "offset", offset,
                "limit", limit
        );
        return admReligionDao.getAll(params);
    }

    public Long countAll(String nameQuery) {
        Map<String, Object> params = Map.of(
                "nameQuery", nameQuery
        );
        return admReligionDao.countAll(params);
    }

    public List<AdmReligion> getAllActive() {
        return admReligionDao.getAllActive();
    }

    public boolean existsByCode(String code) {
        return admReligionDao.existsByCode(code);
    }

    public boolean existsByCodeExcludingId(String code, Long id) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("id", id);
        return admReligionDao.existsByCodeExcludingId(params);
    }
}
