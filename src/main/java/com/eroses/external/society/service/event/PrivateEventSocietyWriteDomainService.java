package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.PrivateEventSocietyDao;
import com.eroses.external.society.model.PrivateEventSociety;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class PrivateEventSocietyWriteDomainService {
    private final PrivateEventSocietyDao privateEventSocietyDao;

    public PrivateEventSociety create(PrivateEventSociety privateEventSociety){
        log.info("Creating Private Event Society");
        int creating = privateEventSocietyDao.create(privateEventSociety);
        if(creating > 0)
            return privateEventSociety;
        else
            return null;
    }
    public List<PrivateEventSociety> creates(List<PrivateEventSociety> privateEventSocieties){
        log.info("Creating Private Event Society");
        int creating = privateEventSocietyDao.createMultiples(privateEventSocieties);
        if(creating > 0)
            return privateEventSocieties;
        else
            return null;
    }

    public PrivateEventSociety update(PrivateEventSociety privateEventSociety){
        log.info("Updating Private Event Society");
        Boolean updating = privateEventSocietyDao.update(privateEventSociety);
        if(updating)
            return privateEventSociety;
        else
            return null;
    }

    public void deleteBySocietyIdAndEventId(List<PrivateEventSociety> societies) {
        log.info("Deleting Private Event Society by {}", societies);
        privateEventSocietyDao.deleteBySocietyIdAndEventId(societies);
    }
    public void deleteByEventIdAndManualSelection(Long eventId, Boolean manualSelection) {
        log.info("Deleting Private Event Society by Event id {}", eventId);
        privateEventSocietyDao.deleteByEventIdAndManualSelection(eventId, manualSelection);
    }

    public void deleteByEventId(Long eventId) {
        log.info("Deleting Private Event Society by Event id {}", eventId);
        privateEventSocietyDao.deleteByEventId(eventId);
    }
}
