package com.eroses.external.society.service;

import com.eroses.external.society.mappers.SearchInformationDocumentDao;
import com.eroses.external.society.model.SearchInformationDocument;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class SearchInformationDocumentReadDomainService {
    private final SearchInformationDocumentDao searchInformationDocumentDao;

    public List<SearchInformationDocument> getAllBySearchInformationId(Long searchInformationId) {
        return searchInformationDocumentDao.getAllBySearchInformationId(searchInformationId);
    }

    public SearchInformationDocument findById(Long id) {
        return searchInformationDocumentDao.findById(id);
    }

    public List<Long> getAllExpiredSearchInformationDocumentId(int applicationStatusCode) {
        return searchInformationDocumentDao.getAllExpiredSearchInformationDocumentId(applicationStatusCode);
    }
}
