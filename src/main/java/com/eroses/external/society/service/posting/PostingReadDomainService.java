package com.eroses.external.society.service.posting;

import com.eroses.external.society.dto.response.posting.PostingReviewResponse;
import com.eroses.external.society.dto.response.posting.PostingPagingResponse;
import com.eroses.external.society.dto.response.posting.PostingReportResponse;
import com.eroses.external.society.mappers.*;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.model.posting.Posting;
import com.eroses.external.society.model.posting.PostingMedia;
import com.eroses.external.society.model.posting.PostingReport;
import com.eroses.external.society.model.posting.PostingReview;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class PostingReadDomainService {

    private final PostingDao postingDao;
    private final PostingMediaDao postingMediaDao;
    private final PostingEngagementDao postingEngagementDao;
    private final PostingReportDao postingReportDao;
    private final PostingReviewDao postingReviewDao;

    public Posting findById(Long id) {
        Posting posting = postingDao.findById(id);
        if (posting != null) {
            List<PostingMedia> media = postingMediaDao.findByPostingId(posting.getId());
            posting.setMedia(media);
        }
        return posting;
    }

    public List<Posting> findAll() {
        List<Posting> postings = postingDao.getAll();
        for (Posting posting : postings) {
            List<PostingMedia> media = postingMediaDao.findByPostingId(posting.getId());
            posting.setMedia(media);
        }
        return postings;
    }

    public Paging<PostingPagingResponse> search(String title, String category, String subCategory,
                                               String dateFrom, String dateTo, String status,
                                               String sortBy, String sortDir, Integer page, Integer size) {

        Map<String, Object> params = new HashMap<>();
        params.put("title", title);
        params.put("category", category);
        params.put("subCategory", subCategory);
        params.put("dateFrom", dateFrom);
        params.put("dateTo", dateTo);
        params.put("status", status);
        params.put("sortBy", sortBy);
        params.put("sortDir", sortDir);

        int offset = (page - 1) * size;
        params.put("offset", offset);
        params.put("pageSize", size);

        List<PostingPagingResponse> postings = postingDao.search(params);
        Long total = postingDao.findPostingsTotalCount(params);

        return new Paging<>(total, postings,size);
    }

    public List<Posting> findRandomPostings(Integer count, List<Long> excludeIds, boolean prioritizeRecent, boolean urgentFirst) {
        Map<String, Object> params = new HashMap<>();
        params.put("count", count);
        params.put("excludeIds", excludeIds);
        params.put("prioritizeRecent", prioritizeRecent);
        params.put("urgentFirst", urgentFirst);

        List<Posting> postings = postingDao.findRandomPostings(params);
        for (Posting posting : postings) {
            List<PostingMedia> media = postingMediaDao.findByPostingId(posting.getId());
            posting.setMedia(media);
        }

        return postings;
    }

    public List<Posting> findRecommendedPostings(Long postingId, Integer count) {
        Posting posting = findById(postingId);
        if (posting == null) {
            return List.of();
        }

        Map<String, Object> params = new HashMap<>();
        params.put("postingId", postingId);
        params.put("category", posting.getCategory());
        params.put("subCategory", posting.getSubCategory());
        params.put("count", count);

        List<Posting> postings = postingDao.findRecommendedPostings(params);
        for (Posting recommendedPosting : postings) {
            List<PostingMedia> media = postingMediaDao.findByPostingId(recommendedPosting.getId());
            recommendedPosting.setMedia(media);
        }

        return postings;
    }

    public List<Posting> findByCategory(String category) {
        List<Posting> postings = postingDao.findByCategory(category);
        for (Posting posting : postings) {
            List<PostingMedia> media = postingMediaDao.findByPostingId(posting.getId());
            posting.setMedia(media);
        }
        return postings;
    }

    public List<Posting> findBySubCategory(String subCategory) {
        List<Posting> postings = postingDao.findBySubCategory(subCategory);
        for (Posting posting : postings) {
            List<PostingMedia> media = postingMediaDao.findByPostingId(posting.getId());
            posting.setMedia(media);
        }
        return postings;
    }

    public List<Posting> findByStatus(String status) {
        List<Posting> postings = postingDao.findByStatus(status);
        for (Posting posting : postings) {
            List<PostingMedia> media = postingMediaDao.findByPostingId(posting.getId());
            posting.setMedia(media);
        }
        return postings;
    }

    public List<Posting> findExpiredPostings() {
        return postingDao.findExpiredPostings();
    }

    public List<Posting> findPostingsToPublish() {
        return postingDao.findPostingsToPublish();
    }

    public Paging<PostingReportResponse> getReportedPostings(Integer page, Integer size) {
        Map<String, Object> params = new HashMap<>();
        int offset = (page - 1) * size;
        params.put("offset", offset);
        params.put("pageSize", size);

        List<PostingReportResponse> reports = postingReportDao.findReportedPostings(params);
        Long total = postingReportDao.countReportedPostings(params);

        return new Paging<>(total, reports);
    }

    public List<PostingReviewResponse> getPostingReviews(Long postingId) {
        return postingReviewDao.findByPostingId(postingId);
    }

    public Map<String, Object> getEngagementStats(String dateFrom, String dateTo) {
        Map<String, Object> stats = new HashMap<>();

        // Get total counts
        Long totalViews = postingEngagementDao.countByPostingIdAndEngagementType(null, "VIEW");
        Long totalLikes = postingEngagementDao.countByPostingIdAndEngagementType(null, "LIKE");
        Long totalShares = postingEngagementDao.countByPostingIdAndEngagementType(null, "SHARE");

        stats.put("totalViews", totalViews);
        stats.put("totalLikes", totalLikes);
        stats.put("totalShares", totalShares);

        // Get views by state
        Map<String, Long> viewsByState = postingEngagementDao.getViewsByState();
        stats.put("viewsByState", viewsByState);

        // Get views by date
        Map<String, Object> dateParams = new HashMap<>();
        dateParams.put("dateFrom", dateFrom);
        dateParams.put("dateTo", dateTo);
        Map<String, Long> viewsByDate = postingEngagementDao.getViewsByDate(dateParams);
        stats.put("viewsByDate", viewsByDate);

        return stats;
    }

    public PostingReview findByReviewerIdAndPostingId(Long reviewerId, Long postingId) {
        return postingReviewDao.findByReviewerIdAndPostingId(reviewerId, postingId);
    }

    public PostingReport findByUserIdAndPostingIdAndDate(Long userId, Long postingId) {
        return postingReportDao.findByUserIdAndPostingIdAndDate(userId, postingId);
    }
}
