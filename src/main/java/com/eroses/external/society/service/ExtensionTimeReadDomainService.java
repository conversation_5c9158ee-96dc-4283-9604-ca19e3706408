package com.eroses.external.society.service;

import com.eroses.external.society.mappers.ExtensionTimeDao;
import com.eroses.external.society.model.ExtensionTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Slf4j
@Service
@RequiredArgsConstructor
public class ExtensionTimeReadDomainService {
    private final ExtensionTimeDao extensionTimeDao;

    public ExtensionTime findById(Long id) throws Exception {
        return extensionTimeDao.findById(id);
    }

    public ExtensionTime findByIdJoin(Long id) throws Exception {
        return extensionTimeDao.findByIdJoin(id);
    }

    public List<ExtensionTime> findByBranchId(Long branchId) throws Exception {
        return extensionTimeDao.findByBranchId(branchId);
    }

    public List<ExtensionTime> getAllPendingBranchExtensionTimeByCriteria(Integer applicationStatusCode, String stateCode, Long roId, String searchQuery, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "stateCode", stateCode,
                "ro", roId,
                "searchQuery", searchQuery,
                "offset", offset,
                "limit", limit);

        return extensionTimeDao.getAllPendingBranchExtensionTimeByCriteria(params);
    }

    public Integer countAllPendingBranchExtensionTimeByCriteria(Integer applicationStatusCode, String stateCode, Long roId, String searchQuery) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "stateCode", stateCode,
                "ro", roId,
                "searchQuery", searchQuery);

        return extensionTimeDao.countAllPendingBranchExtensionTimeByCriteria(params);
    }

    public ExtensionTime findExisting(Long branchId, Integer applicationStatusCode) {
        Map<String, Object> params = Map.of(
                "branchId", branchId,
                "applicationStatusCode", applicationStatusCode);

        return extensionTimeDao.findExisting(params);
    }
}
