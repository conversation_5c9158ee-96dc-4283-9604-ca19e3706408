package com.eroses.external.society.service;

import com.eroses.external.society.dto.response.committee.CommitteeListAjkResponse;
import com.eroses.external.society.mappers.CommitteeDraftDao;
import com.eroses.external.society.model.CommitteeDraft;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommitteeDraftReadDomainService {

    private final CommitteeDraftDao committeeDraftDao;

    public List<CommitteeListAjkResponse> findAllByParams(Map<String, Object> params) {
        return committeeDraftDao.findAllByParams(params);
    }

    public Long countAllByParams(Map<String, Object> params) {
        return committeeDraftDao.countAllByParams(params);
    }

    public CommitteeDraft findById(Long id) {
        return committeeDraftDao.findById(id);
    }

    public List<CommitteeDraft> findByParam(Map<String, Object> params) {
        return committeeDraftDao.findByParam(params);
    }

    public List<CommitteeDraft> findListByIds(List<Long> committeeDraftId) {
        return committeeDraftDao.findByParam(Map.of("ids", committeeDraftId));
    }

    public CommitteeDraft findOneByParams(Map<String, Object> params) {
        return committeeDraftDao.findOneByParams(params);
    }

    public Long countByParam(Map<String, Object> params) {
        return committeeDraftDao.countByParam(params);
    }
}
