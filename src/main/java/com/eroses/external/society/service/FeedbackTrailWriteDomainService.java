package com.eroses.external.society.service;


import com.eroses.external.society.mappers.FeedbackDao;
import com.eroses.external.society.mappers.FeedbackTrailDao;
import com.eroses.external.society.model.Feedback;
import com.eroses.external.society.model.FeedbackTrail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class FeedbackTrailWriteDomainService {
    private final FeedbackTrailDao feedbackTrailDao;

    public Long create(FeedbackTrail feedbackTrail) throws Exception {
        feedbackTrailDao.create(feedbackTrail);
        return feedbackTrail.getId();
    }

    public Long update(FeedbackTrail feedbackTrail) throws Exception {
        feedbackTrailDao.update(feedbackTrail);
        return feedbackTrail.getId();
    }
}
