package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.EventAttendeesDao;
import com.eroses.external.society.model.EventAttendees;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventAttendeesReadDomainService {
    private final EventAttendeesDao eventAttendeesDao;

    public List<EventAttendees> findAll() {
        log.info("Finding all Event Attendees");
        return eventAttendeesDao.findAll();
    }

    public EventAttendees findById(Long id) {
        log.info("Find all Event Attendees by id [{}]", id);
        return eventAttendeesDao.findById(id);
    }

    public List<EventAttendees> findByIdentificationNo(String idNo) {
        log.info("Find all Event Attendees by identification no [{}]", idNo);
        return eventAttendeesDao.findByIdentificationNo(idNo);
    }

    public List<EventAttendees> findByEventId(Long eventId) {
        log.info("Find all Event Attendees by event id [{}]", eventId);
        return eventAttendeesDao.findByEventId(eventId);
    }

    public List<EventAttendees> findAttendeesSubmittedFeedbackByEventId(Long eventId) {
        log.info("Find all Event Attendees submitted feedback by event id [{}]", eventId);
        return eventAttendeesDao.findAttendeesSubmittedFeedbackByEventId(eventId);
    }
    public List<EventAttendees> findByCanceledEventId(Long eventId) {
        log.info("Find all Event Attendees by event id [{}]", eventId);
        return eventAttendeesDao.findCanceledByEventId(eventId);
    }

    public EventAttendees findByEventIdAndIdentificationNo(String identificationNo, Long eventId){
        log.info("Find all Event Attendees by identification no: {} & event id: {}",identificationNo, eventId );
        return eventAttendeesDao.findByEventIdAndIdentificationNo(identificationNo, eventId);

    }

    public EventAttendees findByEventIdAndIdentificationNoIncludeCancelled(String identificationNo, Long eventId){
        log.info("Find all Event Attendees by identification no: {} & event id: {}",identificationNo, eventId );
        return eventAttendeesDao.findByEventIdAndIdentificationNoIncludeCancelled(identificationNo, eventId);

    }

    public EventAttendees findByAttendanceNo(String attendanceNo){
        log.info("Find all Event Attendees by attendance no: {}", attendanceNo);
        return eventAttendeesDao.findByAttendanceNo(attendanceNo);

    }
    public int countByEventId(Long eventId) {
        log.info("Counting all Event Attendees by event id: {}", eventId);
        return eventAttendeesDao.findByEventId(eventId).size();
    }


}
