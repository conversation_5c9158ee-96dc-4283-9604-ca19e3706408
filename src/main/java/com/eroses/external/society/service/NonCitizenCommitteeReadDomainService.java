package com.eroses.external.society.service;

import com.eroses.external.society.dto.response.appeal.GetUserSocietiesForAppealResponse;
import com.eroses.external.society.mappers.NonCitizenCommitteeDao;
import com.eroses.external.society.model.Branch;
import com.eroses.external.society.model.BranchCommittee;
import com.eroses.external.society.model.NonCitizenCommittee;
import com.eroses.external.society.model.Society;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class NonCitizenCommitteeReadDomainService {
    private final NonCitizenCommitteeDao nonCitizenCommitteeDao;

    public NonCitizenCommittee findById(Long id) {
        NonCitizenCommittee nonCitizenCommittee = nonCitizenCommitteeDao.findById(id);
        return nonCitizenCommittee;
    }

    public Boolean isExists(Long id) {
        NonCitizenCommittee nonCitizenCommittee = nonCitizenCommitteeDao.findById(id);
        return nonCitizenCommittee != null;
    }

    public List<NonCitizenCommittee> getAllNonCitizenCommittees(Long societyId, Long branchId, Long id, Long meetingId, Boolean documentType, LocalDate appointedDate, String excludedApplicationStatusCode, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        params.put("applicationStatusCode", excludedApplicationStatusCode);
        if (offset != null) params.put("offset", offset);
        if (limit != null && limit > 0) params.put("limit", limit);

        if (branchId != null) {
            params.put("branchId", branchId);
        }
        if (id != null) {
            params.put("id", id);
        }
        if (societyId != null) {  // Ensure we only add it when not null
            params.put("societyId", societyId);
        }
        if (meetingId != null) {
            params.put("meetingId", meetingId);
        }
        if (documentType != null) {
            params.put("documentType", documentType);
        }
        if (appointedDate != null) {
            params.put("appointedDate", appointedDate);
        }

        return nonCitizenCommitteeDao.findAll(params);
    }

    public List<NonCitizenCommittee> findAllByParams(Map<String, Object> params) {
        return nonCitizenCommitteeDao.findAllByParams(params);
    }

    public Long countAllByParams(Map<String, Object> params) {
        return nonCitizenCommitteeDao.countAllByParams(params);
    }

    public Long countGetAllNonCitizenCommittees(Long societyId, Long branchId, Long id, Long meetingId, Boolean documentType, LocalDate appointedDate, String excludedApplicationStatusCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("applicationStatusCode", excludedApplicationStatusCode);

        if (branchId != null) {
            params.put("branchId", branchId);
        }
        if (id != null) {
            params.put("id", id);
        }
        if (societyId != null) {  // Ensure we only add it when not null
            params.put("societyId", societyId);
        }
        if (meetingId != null) {
            params.put("meetingId", meetingId);
        }
        if (documentType != null) {
            params.put("documentType", documentType);
        }
        if (appointedDate != null) {
            params.put("appointedDate", appointedDate);
        }

        return nonCitizenCommitteeDao.countFindAll(params);
    }

    public List<NonCitizenCommittee> findBySocietyId(Long societyId) {
        return nonCitizenCommitteeDao.findBySocietyId(societyId);
    }

    public List<NonCitizenCommittee> findByBranchId(Long branchId) {
        return nonCitizenCommitteeDao.findByBranchId(branchId);
    }

    public List<Map<Long, String>> findSocietyIdAndDesignationCodeByIdentificationNo(String identificationNo) {
        return nonCitizenCommitteeDao.findSocietyIdAndDesignationCodeByIdentificationNo(identificationNo);
    }

    public boolean existsByIdentityNoAndSocietyId(String identificationNo, Long societyId, Integer applicationStatusCode, String status) {
        Map<String, Object> params = Map.of(
                "identificationNo", identificationNo,
                "societyId", societyId,
                "applicationStatusCode", applicationStatusCode,
                "status", status
        );

        return nonCitizenCommitteeDao.existsByIdentityNoAndSocietyId(params);
    }

    public List<NonCitizenCommittee> findBySocietyIdAndApplicationStatusCodeAndStatus(
            Long societyId, Integer applicationStatusCode, String status, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "applicationStatusCode", applicationStatusCode,
                "status", status,
                "offset", offset,
                "limit", limit
        );

        return nonCitizenCommitteeDao.findBySocietyIdAndApplicationStatusCodeAndStatus(params);
    }

    // Count total non-citizen committees with the given filters
    public Long countBySocietyIdAndApplicationStatusCodeAndStatus(
            Long societyId, Integer applicationStatusCode, String status) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "applicationStatusCode", applicationStatusCode,
                "status", status
        );

        return nonCitizenCommitteeDao.countBySocietyIdAndApplicationStatusCodeAndStatus(params);
    }

    public NonCitizenCommittee findBySocietyIdAndIdentificationNo(Long societyId, String identificationNo) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "identificationNo", identificationNo
        );

        return nonCitizenCommitteeDao.findBySocietyIdAndIdentificationNo(params);
    }

    public List<NonCitizenCommittee> findAllBySocietyIdAndIdentificationNo(Long societyId, String identificationNo) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "identificationNo", identificationNo
        );

        return nonCitizenCommitteeDao.findAllBySocietyIdAndIdentificationNo(params);
    }

    public List<NonCitizenCommittee> findAllBySocietyIdAndApplicationStatusCodeAndStatus(Long societyId, Integer applicationStatusCode, String status) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "applicationStatusCode", applicationStatusCode,
                "status", status
        );

        return nonCitizenCommitteeDao.findAllBySocietyIdAndApplicationStatusCodeAndStatus(params);
    }

    public List<NonCitizenCommittee> getAllPendingSocietyNonCitizenByCriteria(Integer applicationStatusCode, String stateCode, Long roId, String categoryCode, String subCategoryCode, String societyName, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "stateCode", stateCode,
                "ro", roId,
                "categoryCode", categoryCode,
                "subCategoryCode", subCategoryCode,
                "societyName", societyName,
                "offset", offset,
                "limit", limit);

        return nonCitizenCommitteeDao.getAllPendingSocietyNonCitizenByCriteria(params);
    }

    public List<NonCitizenCommittee> getAllPendingBranchNonCitizenByCriteria(Integer applicationStatusCode, String stateCode, Long roId, String categoryCode, String subCategoryCode, String societyName, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "stateCode", stateCode,
                "ro", roId,
                "categoryCode", categoryCode,
                "subCategoryCode", subCategoryCode,
                "societyName", societyName,
                "offset", offset,
                "limit", limit);

        return nonCitizenCommitteeDao.getAllPendingBranchNonCitizenByCriteria(params);
    }

    public Integer countAllPendingSocietyNonCitizenByCriteria(Integer applicationStatusCode, String stateCode, Long roId, String categoryCode, String subCategoryCode, String societyName) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "stateCode", stateCode,
                "ro", roId,
                "categoryCode", categoryCode,
                "subCategoryCode", subCategoryCode,
                "societyName", societyName);

        return nonCitizenCommitteeDao.countAllPendingSocietyNonCitizenByCriteria(params);
    }

    public Integer countAllPendingBranchNonCitizenByCriteria(Integer applicationStatusCode, String stateCode, Long roId, String categoryCode, String subCategoryCode, String societyName) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "stateCode", stateCode,
                "ro", roId,
                "categoryCode", categoryCode,
                "subCategoryCode", subCategoryCode,
                "societyName", societyName);

        return nonCitizenCommitteeDao.countAllPendingBranchNonCitizenByCriteria(params);
    }

    public NonCitizenCommittee findByPaymentId(Long paymentId){
        return nonCitizenCommitteeDao.findByPaymentId(paymentId);
    }


    public List<GetUserSocietiesForAppealResponse> findNonCitizenCommitteeForAppeal(Map<String, Object> param) {
        return nonCitizenCommitteeDao.findNonCitizenCommitteeForAppeal(param);
    }
}
