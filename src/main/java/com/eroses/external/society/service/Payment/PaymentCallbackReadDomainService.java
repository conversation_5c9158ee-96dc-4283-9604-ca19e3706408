package com.eroses.external.society.service.Payment;

import com.eroses.external.society.mappers.PaymentCallbackDao;
import com.eroses.external.society.model.payment.PaymentCallback;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class PaymentCallbackReadDomainService {
    private final PaymentCallbackDao paymentCallbackDao;

    public List<PaymentCallback> getPaymentCallbacks() {
        return paymentCallbackDao.select();
    }

    public PaymentCallback findPaymentCallbackByCriteria(PaymentCallback paymentCallback) {
        return paymentCallbackDao.findByCriteria(paymentCallback);
    }
}
