package com.eroses.external.society.service;

import com.eroses.external.society.api.facade.MeetingMemberAttendanceReadFacade;
import com.eroses.external.society.dto.response.meeting.BranchMeetingResponse;
import com.eroses.external.society.dto.response.meeting.MeetingBasicListResponse;
import com.eroses.external.society.dto.response.meeting.MeetingPagingResponse;
import com.eroses.external.society.dto.response.meeting.MeetingResponse;
import com.eroses.external.society.mappers.MeetingDao;
import com.eroses.external.society.model.Meeting;
import com.eroses.external.society.model.MeetingMemberAttendance;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.List.of;

import java.time.LocalDate;

@Service
@RequiredArgsConstructor
public class MeetingReadDomainService {
    private final MeetingDao meetingDao;
    private final MeetingMemberAttendanceReadFacade meetingMemberAttendanceReadFacade;

    public List<Meeting> getAll() {
        return meetingDao.getAll();
    }

    public List<MeetingPagingResponse> getAll(Map<String, Object> params) {
        return meetingDao.search(params);
    }

    public Long findMeetingsTotalCount(Map<String, Object> params) {
        return meetingDao.findMeetingsTotalCount(params);
    }

    public Meeting findByBranchNo(String branchNo) {
        return meetingDao.findByBranchNo(branchNo);
    }

    public List<Meeting> findBySocietyId(Long societyId) {
        return  meetingDao.findBySocietyId(societyId);
    }

    public Boolean isExistBySocietyId(Long societyId) {
        Map<String, Object> params = Map.of(
                "societyId", societyId
        );
        return meetingDao.isExistByCriteria(params);
    }

    public Boolean isExistBySocietyIdAndBranchId(Long societyId, Long branchId) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "branchId", branchId
        );
        return meetingDao.isExistByCriteria(params);
    }

    public List<Meeting> findByBranchId(Long branchId) {
        return  meetingDao.findByBranchId(branchId);
    }

    public List<BranchMeetingResponse> findByBranchIdAndMeetingTypes(Long branchId, List<Integer> meetingTypes) {
        HashMap<String, Object> params = new HashMap<>();
        if (branchId != null) params.put("branchId", branchId);
        if (meetingTypes != null) params.put("meetingTypes", meetingTypes);
        return meetingDao.findByBranchIdAndMeetingTypes(params);
    }

    public List<Meeting> findBySocietyIdAndMeetingTypes(Long societyId, List<Integer> meetingTypes) {
        HashMap<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (meetingTypes != null) params.put("meetingTypes", meetingTypes);
        return meetingDao.findBySocietyIdAndMeetingTypes(params);
    }

    public List<MeetingBasicListResponse> findBySocietyIdAndBranchId(
        Long societyId,
        Long branchId,
        Integer meetingType,
        LocalDate meetingDate
    ) {
        return meetingDao.findBySocietyIdAndBranchId(
            societyId,
            branchId,
            meetingType,
            meetingDate
        );
    }

    public MeetingResponse findMeetingAndMembers(Long meetingId) {
        return meetingDao.findMeetingAndMembers(meetingId);
    }

    public Meeting findById(Long meetingId) {
        Meeting meeting = meetingDao.findById(meetingId);
        List<MeetingMemberAttendance> meetingMemberAttendances = meetingMemberAttendanceReadFacade.getAllByMeetingId(meeting.getId());
        meeting.setMeetingMemberAttendances(meetingMemberAttendances);
        return meeting;
    }

    public Meeting findStatementMeeting(Long societyId, Long branchId, Long statementId) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "branchId", branchId,
                "statementId", statementId
        );
        return meetingDao.findStatementMeeting(params);
    }

    public Meeting findByStatementId(Long statementId) {
        return meetingDao.findByStatementId(statementId);
    }

    public Meeting findBySocietyIdAndMeetingType(Long societyId, Integer code) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "meetingType", code
        );

        return meetingDao.findBySocietyIdAndMeetingType(params);
    }

    public List<MeetingResponse> getMeetingInfoForStatement(Map<String, Object> params) {
        return meetingDao.getMeetingInfoForStatement(params);
    }

    public boolean existsByDateAndTypeAndSocietyId(LocalDate meetingDate, Integer meetingType, Long societyId, Long branchId) {
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("meetingDate", meetingDate);
        params.put("meetingType", meetingType);
        params.put("societyId", societyId);
        if (branchId != null) {
            params.put("branchId", branchId);
        }
        return !meetingDao.findByDateAndTypeAndSocietyId(params).isEmpty();
    }

    public Meeting findByBranchIdAndMeetingType(Long id, Integer code) {
        return meetingDao.findByBranchIdAndMeetingType(id, code);
    }
}
