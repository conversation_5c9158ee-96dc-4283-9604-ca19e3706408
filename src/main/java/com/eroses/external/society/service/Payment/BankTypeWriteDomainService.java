package com.eroses.external.society.service.Payment;

import com.eroses.external.society.mappers.BankTypeDao;
import com.eroses.external.society.model.payment.BankType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BankTypeWriteDomainService {
    private final BankTypeDao bankTypeDao;

    public void save(BankType bankType) {
        bankTypeDao.save(bankType);
    }

    public void update(BankType bankType) throws Exception {
        bankTypeDao.update(bankType);
    }
}
