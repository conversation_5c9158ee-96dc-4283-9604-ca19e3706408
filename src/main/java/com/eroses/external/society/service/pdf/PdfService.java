package com.eroses.external.society.service.pdf;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.styledxmlparser.jsoup.Jsoup;
import com.itextpdf.styledxmlparser.jsoup.nodes.Document;
import com.itextpdf.styledxmlparser.jsoup.nodes.Element;
import com.openhtmltopdf.outputdevice.helper.BaseRendererBuilder;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;

@Service
@RequiredArgsConstructor
public class PdfService {
    public String generatePdf(String content) {
        try(ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            HtmlConverter.convertToPdf(content, outputStream);

            return Base64.getEncoder().encodeToString(outputStream.toByteArray());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public String generatePdfLandscape(String content) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            String cleanHtmlContent = fixHtml(content);

            PdfRendererBuilder builder = new PdfRendererBuilder();
            builder.useFastMode();
            builder.withHtmlContent(cleanHtmlContent, null);
            builder.toStream(outputStream);
            builder.useDefaultPageSize(11.69f, 8.27f, BaseRendererBuilder.PageSizeUnits.INCHES);

            builder.run();// A4 Landscape

            return Base64.getEncoder().encodeToString(outputStream.toByteArray());
        } catch (Exception e) {
            // Print full stack trace for debugging
            e.printStackTrace();
            throw new RuntimeException("Failed to generate landscape PDF", e);
        }
    }

    public String generatePdfWithOpenHtmlToPdf(String content) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            PdfRendererBuilder builder = new PdfRendererBuilder();
            builder.withHtmlContent(content, null);
            builder.toStream(outputStream);
            builder.run();

            return Base64.getEncoder().encodeToString(outputStream.toByteArray());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static String fixHtml(String htmlContent) {
        // Parse the HTML content into a jsoup Document
        Document document = Jsoup.parse(htmlContent);

        // Ensure that the document is well-formed XML (XHTML)
        document.outputSettings().syntax(Document.OutputSettings.Syntax.xml);

        // Fix all void elements (self-closing tags) like <meta>
        for (Element meta : document.select("meta")) {
            // Ensure the meta tag is self-closing
            meta.tag().isSelfClosing();
        }

        // Convert the document back to a string and return the fixed HTML
        return document.html();
    }

    public byte[] generatePdfFromHtml(String htmlContent) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            PdfWriter writer = new PdfWriter(baos);
            PdfDocument pdfDocument = new PdfDocument(writer);

            HtmlConverter.convertToPdf(htmlContent, pdfDocument.getWriter()); // No ConverterProperties needed
            return baos.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
