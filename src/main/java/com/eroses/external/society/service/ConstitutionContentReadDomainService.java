package com.eroses.external.society.service;

import com.eroses.external.society.dto.request.ConstitutionContentGetAllSocietiesRequest;
import com.eroses.external.society.mappers.ConstitutionContentDao;
import com.eroses.external.society.model.ConstitutionContent;
import com.eroses.external.society.model.Society;
import com.eroses.external.society.utils.Assert;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class ConstitutionContentReadDomainService {
    private final ConstitutionContentDao constitutioncontentDao;

    public ConstitutionContent findById(Long id) throws Exception{
        return constitutioncontentDao.findById(id);
    }

    public Boolean isExists(Long id) {
        ConstitutionContent constitutioncontent = constitutioncontentDao.findById(id);
        return constitutioncontent != null;
    }

//    public List<ConstitutionContent> getAllConstitutionContents() {
//        return constitutioncontentDao.findAll();
//    }

    public List<ConstitutionContent> getAllConstitutionContents(ConstitutionContentGetAllSocietiesRequest request) {
        Map<String, Object> params = new HashMap<>();
        if (request.getId() != null) params.put("id", request.getId());
        if (request.getOldConstitutionContentId() != null) params.put("oldConstitutionContentId", request.getOldConstitutionContentId());
        if (request.getAmendmentId() != null) params.put("amendmentId", request.getAmendmentId());
        if (request.getSocietyId() != null) params.put("societyId", request.getSocietyId());
        if (request.getClauseContentId() != null) params.put("clauseContentId", request.getClauseContentId());
        if (request.getClauseNo() != null) params.put("clauseNo", request.getClauseNo());
        if (request.getClauseName() != null) params.put("clauseName", request.getClauseName());
        if (request.getApplicationStatusCode() != null) params.put("applicationStatusCode", request.getApplicationStatusCode());
        if (request.getPageNo() != null) params.put("offset", request.getPageNo());
        if (request.getPageSize() != null) params.put("limit", request.getPageSize());

        return constitutioncontentDao.findAll(params);
    }

    public long countGetAllConstitutionContents(ConstitutionContentGetAllSocietiesRequest request) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", request.getId());
        params.put("amendmentId", request.getAmendmentId());
        params.put("societyId", request.getSocietyId());
        params.put("clauseContentId", request.getClauseContentId());

        return constitutioncontentDao.countFindAll(params);
    }

    public List<ConstitutionContent> findBySocietyId(Long societyId) {
        return constitutioncontentDao.findBySocietyId(societyId);
    }

    //Mapped to ClauseContent Model
    public List<ConstitutionContent> getAllBySocietyId(Long societyId, String status, int applicationStatusCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("societyId", societyId);
        if (status != null) params.put("status", status);
        if (applicationStatusCode != 0) params.put("applicationStatusCode", applicationStatusCode);
        return constitutioncontentDao.getAllBySocietyId(params);
    }

    public ConstitutionContent findForAmendment(Long societyId, Long clauseContentId, String clauseNo, Integer applicationStatusCode) {
        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (clauseContentId != null) params.put("clauseContentId", clauseContentId);
        if (clauseNo != null && !clauseNo.isEmpty()) params.put("clauseNo", clauseNo);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);

        return constitutioncontentDao.findForAmendment(params);
    }

    public long countBySocietyIdAndConstitutionTypeId(Long societyId, Long constitutionTypeId) {
        Map<String, Object> params = new HashMap<>();
        params.put("societyId", societyId);
        params.put("constitutionTypeId", constitutionTypeId);
        return constitutioncontentDao.countBySocietyIdAndConstitutionTypeId(params);
    }

    public ConstitutionContent findBySocietyIdAndClauseContentId(Long societyId, Long clauseContentId) {
        Map<String, Object> params = new HashMap<>();
        params.put("societyId", societyId);
        params.put("clauseContentId", clauseContentId);
        return constitutioncontentDao.findBySocietyIdAndClauseContentId(params);
    }

    public ConstitutionContent findByAmendmentIdAndClauseContentId(Long societyId, Long amendmentId, Long clauseContentId) {
        Map<String, Object> params = new HashMap<>();
        params.put("societyId", societyId);
        params.put("amendmentId", amendmentId);
        params.put("clauseContentId", clauseContentId);
        return constitutioncontentDao.findBySocietyIdAndClauseContentId(params);
    }

    public List<ConstitutionContent> getAllConstitutionByListOfId(List<Long> oldConstitutionContentIds) {
        return constitutioncontentDao.findAllByIdList(oldConstitutionContentIds);
    }

    public List<ConstitutionContent> findByParams(Map<String, Object> params) {
        return constitutioncontentDao.findAll(params);
    }

    public ConstitutionContent findBySocietyIdAndClauseContentIdAndApplicationStatusCode(Long societyId, Long clauseContentId, Integer applicationStatusCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("societyId", societyId);
        params.put("clauseContentId", clauseContentId);
        params.put("applicationStatusCode", applicationStatusCode);
        return constitutioncontentDao.findBySocietyIdAndClauseContentIdAndApplicationStatusCode(params);
    }
}
