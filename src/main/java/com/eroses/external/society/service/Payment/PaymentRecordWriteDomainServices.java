package com.eroses.external.society.service.Payment;

import com.eroses.external.society.mappers.PaymentRecordDao;
import com.eroses.external.society.model.payment.PaymentRecord;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
@RequiredArgsConstructor
public class PaymentRecordWriteDomainServices {
    private final PaymentRecordDao paymentRecordDao;

    public long savePaymentRecord(PaymentRecord paymentRecord, User user) {
        paymentRecord.setCreatedBy(user.getId());
        paymentRecordDao.save(paymentRecord);
        return paymentRecord.getId();
    }

    public void updatePaymentRecord(PaymentRecord paymentRecord) throws Exception {
        paymentRecordDao.update(paymentRecord);
    }

    public void updatePaymentRecordBySystem(PaymentRecord paymentRecord) throws Exception {
        paymentRecordDao.updateBySystem(paymentRecord);
    }
}
