package com.eroses.external.society.service;

import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
import com.amazonaws.services.rekognition.AmazonRekognition;
import com.amazonaws.services.rekognition.model.*;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.eroses.external.society.utils.AwsS3Utils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.github.cdimascio.dotenv.Dotenv;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
//import software.amazon.awssdk.services.s;

import software.amazon.awssdk.services.s3.model.GetUrlRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse;

import java.io.InputStream;
import java.time.Duration;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class ForbiddenLogoService {

//    @Autowired
//    private AmazonRekognition rekognition;

//    @Autowired
//    private S3DomainService s3DomainService;

//    private LogoRepository logoRepository;

    @Value("${aws.s3.bucket-name}")
    private String BUCKET_NAME;
    @Value("${aws.s3.region}")
    private String REGION;
    @Value("${spring.profiles.active}")
    private String env;
    @Value("${aws.s3.secret-name}")
    private String secretName;

    private final List<String> RESTRICTED_WORDS = Arrays.asList("God", "Jesus", "Allah", "Buddha");
    private final List<String> RESTRICTED_OBJECTS = Arrays.asList("Dog", "Cat", "Elephant", "Cow", "Monkey");
    private final S3DomainService s3DomainService;
    private final AmazonRekognition rekognition;
    private S3Client s3Client;
    private final AwsS3Utils awsS3Utils;
//    private final amazonS3 amazonS3;
//


    public Map<String, String> validateLogo(String s3Key) throws Exception {

        Map<String, String> result = new HashMap<>();
        // Step 1: Call Rekognition for text detection
        DetectTextRequest textRequest = new DetectTextRequest()
                .withImage(new Image().withS3Object(new S3Object().withBucket(BUCKET_NAME).withName(s3Key)));

        DetectTextResult textResult = rekognition.detectText(textRequest);
        for (TextDetection detection : textResult.getTextDetections()) {
            String detectedText = detection.getDetectedText();
            for (String restrictedWord : RESTRICTED_WORDS) {
                if (detectedText.toLowerCase().contains(restrictedWord.toLowerCase())) {
                    result.put("status", "INVALID");
                    result.put("msg", "Logo mengandungi teks terlarang.");
                    return result;
                }
            }
        }

        // Step 2: Call Rekognition for object detection
        DetectLabelsRequest labelRequest = new DetectLabelsRequest()
                .withImage(new Image().withS3Object(new S3Object().withBucket(BUCKET_NAME).withName(s3Key)))
                .withMaxLabels(10)
                .withMinConfidence(75F); // Set minimum confidence threshold

        DetectLabelsResult labelResult = rekognition.detectLabels(labelRequest);
        for (Label label : labelResult.getLabels()) {
            String labelName = label.getName();
            for (String restrictedObject : RESTRICTED_OBJECTS) {
                if (labelName.toLowerCase().contains(restrictedObject.toLowerCase())) {
                    result.put("status", "INVALID");
                    result.put("msg", "Logo mengandungi objek terlarang.");
                    return result;
                }
            }
        }

        result.put("status", "VALID");
        result.put("msg", "Logo dibenarkan.");
        return result;
    }

//    public String uploadAndValidate(MultipartFile file) throws Exception {
//        // Step 1: Generate hash
//        String hash = DigestUtils.sha256Hex(file.getBytes());
//
//        // Step 2: Check for duplicates
//        if (logoRepository.findByHash(hash).isPresent()) {
//            throw new RuntimeException("Duplicate logo detected.");
//        }
//
//        // Step 3: Upload to S3
//        String s3Path = uploadToS3(file);
//
//        // Step 4: Save to RDS
//        Logo logo = new Logo();
//        logo.setLogoName(file.getOriginalFilename());
//        logo.setS3Path(s3Path);
//        logo.setHash(hash);
//        logoRepository.save(logo);
//
//        // Step 5: Validate logo
//        String validationStatus = validateLogo(s3Path);
//        logo.setValidationStatus(validationStatus);
//        logoRepository.save(logo);
//
//        return validationStatus;
//    }

    public Map<String, String> validateMultipartFile(MultipartFile file) throws Exception {
        byte[] fileBytes = file.getBytes();
        Map<String, String> result = new HashMap<>();

        // Step 1: Call Rekognition for text detection
        DetectTextRequest textRequest = new DetectTextRequest()
                .withImage(new Image().withBytes(java.nio.ByteBuffer.wrap(fileBytes)));

        DetectTextResult textResult = rekognition.detectText(textRequest);
        for (TextDetection detection : textResult.getTextDetections()) {
            String detectedText = detection.getDetectedText();
            for (String restrictedWord : RESTRICTED_WORDS) {
                if (detectedText.toLowerCase().contains(restrictedWord.toLowerCase())) {
                    result.put("status", "INVALID");
                    result.put("msg", detectedText);
                    return result;
                }
            }
        }

        // Step 2: Call Rekognition for object detection
        DetectLabelsRequest labelRequest = new DetectLabelsRequest()
                .withImage(new Image().withBytes(java.nio.ByteBuffer.wrap(fileBytes)))
                .withMaxLabels(10)
                .withMinConfidence(75F);

        DetectLabelsResult labelResult = rekognition.detectLabels(labelRequest);
        for (Label label : labelResult.getLabels()) {
            String labelName = label.getName();
            for (String restrictedObject : RESTRICTED_OBJECTS) {
                if (labelName.toLowerCase().contains(restrictedObject.toLowerCase())) {
                    result.put("status", "INVALID");
                    result.put("msg", labelName);
                    return result;
                }
            }
        }

        result.put("status", "VALID");
        result.put("msg", "Logo dibenarkan.");
        return result;
    }

    public String getUrlPath(String key) {
        S3Presigner s3Presigner = awsS3Utils.createS3Presigner();
        String fileUrl;
        try {
            Duration expiration = Duration.ofMinutes(5);

            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(BUCKET_NAME)
                    .key("forbidden-logo/" + key)
                    .build();

            PutObjectPresignRequest presignRequest = PutObjectPresignRequest.builder()
                    .putObjectRequest(putObjectRequest)
                    .signatureDuration(expiration)
                    .build();

            String presignedUrl = s3Presigner.presignPutObject(presignRequest).url().toString();
            return presignedUrl;
        } catch (Exception e) {
//            log.error("Error generating presigned URL: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to generate presigned URL: " + e.getMessage(), e);
        } finally {
            // Close the presigner to release resources
            s3Presigner.close();
        }
    }

    public String uploadToS3(MultipartFile file) throws Exception {
        String key = UUID.randomUUID().toString() + "-" + file.getOriginalFilename();

        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(file.getSize());
        metadata.setContentType(file.getContentType());

        amazonS3().putObject(BUCKET_NAME, key, file.getInputStream(), metadata);

      return s3DomainService.uploadFile(file, "forbidden-logo", key); // Return just the key, not the full path
    }

    public float compareImagesWithRekognitionS3(MultipartFile sourceFile, String targetS3Key) throws Exception {
        try {
            // For face comparison (if logos contain faces)
            CompareFacesRequest request = new CompareFacesRequest()
                    .withSourceImage(new Image().withBytes(java.nio.ByteBuffer.wrap(sourceFile.getBytes())))
                    .withTargetImage(new Image().withS3Object(new S3Object().withBucket(BUCKET_NAME).withName(targetS3Key)))
                    .withSimilarityThreshold(80F);

            CompareFacesResult result = rekognition.compareFaces(request);

            if (!result.getFaceMatches().isEmpty()) {
                return result.getFaceMatches().get(0).getSimilarity();
            }

            return 0.0f;

        } catch (Exception e) {
            // If comparison fails (no faces detected), return 0
            return 0.0f;
        }
    }

    public String checkForSimilarImagesInS3(MultipartFile file) throws Exception {
        String folderPrefix = "forbidden-logo";

        ListObjectsV2Request listRequest = new ListObjectsV2Request()
                .withBucketName(BUCKET_NAME)
                .withPrefix(folderPrefix)
                .withMaxKeys(100); // Limit for performance


        if (!amazonS3().doesBucketExistV2(BUCKET_NAME)) {
            throw new RuntimeException("Bucket does not exist: " + BUCKET_NAME);
        }
        //CHECK WHY BUCKET NOT FOUND
        ListObjectsV2Result result = amazonS3().listObjectsV2(listRequest);

        for (S3ObjectSummary objectSummary : result.getObjectSummaries()) {
            float similarity = compareImagesWithRekognitionS3(file, objectSummary.getKey());
            System.out.println("Similarity to " + objectSummary.getKey() + ": " + similarity);

            if (similarity > 95.0f) { // 95% similarity threshold
                return objectSummary.getKey(); // Found very similar image
            }
        }

        return "No matched"; // No similar images found
    }

    public Map<String, String> getBasicCredentials() {
        String accessKey;
        String secretKey;

        if (env.equals("local")) {
            Dotenv dotenv = Dotenv.configure().load();
            accessKey = dotenv.get("AWS_ACCESS_KEY_ID_DEV");
            secretKey = dotenv.get("AWS_SECRET_ACCESS_KEY_DEV");
        } else {
            SecretsManagerClient secretsManagerClient = SecretsManagerClient.builder()
                    .region(Region.of(REGION))
                    .build();

            GetSecretValueRequest getSecretValueRequest = GetSecretValueRequest.builder()
                    .secretId(secretName)
                    .build();

            GetSecretValueResponse getSecretValueResponse = secretsManagerClient.getSecretValue(getSecretValueRequest);

            String secret = getSecretValueResponse.secretString();

            ObjectMapper objectMapper = new ObjectMapper();
            try {
                JsonNode secretJson = objectMapper.readTree(secret);
                accessKey = secretJson.get("accessKey").asText();
                secretKey = secretJson.get("secretKey").asText();
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }

        Map<String, String> credentialsMap = new HashMap<>();
        credentialsMap.put("accessKey", accessKey);
        credentialsMap.put("secretKey", secretKey);
        return credentialsMap;
    }

    private String getS3ObjectHash(String s3Key) throws Exception {
        // Download object and calculate hash
        BasicAWSCredentials creds = new BasicAWSCredentials(this.getBasicCredentials().get("accessKey"), this.getBasicCredentials().get("secretKey"));
        AWSCredentialsProvider provider = new AWSStaticCredentialsProvider(creds);
        AmazonS3 amazonS3 = AmazonS3ClientBuilder.standard()
                .withRegion(REGION)
                .withCredentials(provider)
                .build();

        com.amazonaws.services.s3.model.S3Object s3Object = amazonS3.getObject(BUCKET_NAME, s3Key);
        InputStream objectContent = s3Object.getObjectContent();

        try {
            byte[] objectBytes = objectContent.readAllBytes();
            return DigestUtils.sha256Hex(objectBytes);
        } finally {
            objectContent.close();
        }
    }

    public String compareWithReferenceLogo(MultipartFile uploadedFile, String referenceUrl) throws Exception {
        try {
            // Method 1: Hash Comparison (for exact matches)
            String uploadedHash = DigestUtils.sha256Hex(uploadedFile.getBytes());
            String referenceHash = getHashFromUrl(referenceUrl);

            if (uploadedHash.equals(referenceHash)) {
                return "EXACT_MATCH";
            }

            // Method 2: AWS Rekognition Face Comparison (if logos contain faces)
            float similarity = compareImagesWithRekognition(uploadedFile, referenceUrl);

            if (similarity > 90.0f) {
                return "HIGH_SIMILARITY (" + String.format("%.2f", similarity) + "%)";
            } else if (similarity > 70.0f) {
                return "MODERATE_SIMILARITY (" + String.format("%.2f", similarity) + "%)";
            } else {
                return "LOW_SIMILARITY (" + String.format("%.2f", similarity) + "%)";
            }

        } catch (Exception e) {
            return "COMPARISON_ERROR: " + e.getMessage();
        }
    }

    private String getHashFromUrl(String imageUrl) throws Exception {
        // Download image from URL and calculate hash
        java.net.URL url = new java.net.URL(imageUrl);
        java.io.InputStream inputStream = url.openStream();
        byte[] imageBytes = inputStream.readAllBytes();
        inputStream.close();

        return DigestUtils.sha256Hex(imageBytes);
    }

    public float compareImagesWithRekognition(MultipartFile sourceFile, String targetUrl) throws Exception {
        // Upload source image to temporary S3 location
        String tempS3Key = "temp-" + UUID.randomUUID().toString();
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(sourceFile.getSize());
        amazonS3().putObject(BUCKET_NAME, tempS3Key, sourceFile.getInputStream(), metadata);

        try {
            // Note: This uses CompareFaces which is designed for faces
            // For general image comparison, you'd need custom ML models
            CompareFacesRequest request = new CompareFacesRequest()
                    .withSourceImage(new Image().withS3Object(new S3Object().withBucket(BUCKET_NAME).withName(tempS3Key)))
                    .withTargetImage(new Image().withBytes(java.nio.ByteBuffer.wrap(downloadImageFromUrl(targetUrl))))
                    .withSimilarityThreshold(70F);

            CompareFacesResult result = rekognition.compareFaces(request);

            if (!result.getFaceMatches().isEmpty()) {
                return result.getFaceMatches().get(0).getSimilarity();
            }

            return 0.0f; // No faces found to compare

        } finally {
            // Clean up temporary S3 object
            amazonS3().deleteObject(BUCKET_NAME, tempS3Key);
        }
    }

    private byte[] downloadImageFromUrl(String imageUrl) throws Exception {
        java.net.URL url = new java.net.URL(imageUrl);
        java.io.InputStream inputStream = url.openStream();
        byte[] imageBytes = inputStream.readAllBytes();
        inputStream.close();
        return imageBytes;
    }

    // Method 1: Check duplicates by comparing hashes of all S3 objects
    public String checkForDuplicatesInS3(MultipartFile file, String fileHash) throws Exception {
        String folderPrefix = "forbidden-logo/"; // Specify folder path in S3

        ListObjectsV2Request listRequest = new ListObjectsV2Request()
                .withBucketName(BUCKET_NAME)
                .withPrefix(folderPrefix)
                .withMaxKeys(1000); // Adjust based on your needs

        ListObjectsV2Result result;
        do {
            result = amazonS3().listObjectsV2(listRequest);

            for (S3ObjectSummary objectSummary : result.getObjectSummaries()) {
                String existingObjectHash = getS3ObjectHash(objectSummary.getKey());

                if (fileHash.equals(existingObjectHash)) {
                    System.out.println("Duplicate found: " + objectSummary.getKey());
                    return objectSummary.getKey(); // Return the duplicate file path
                }
            }

            listRequest.setContinuationToken(result.getNextContinuationToken());
        } while (result.isTruncated());

        return "No Duplicate found"; // No duplicates found
    }

    public String getHashFromMultipartFile(MultipartFile file) throws Exception {
        // Get bytes directly from MultipartFile
        byte[] imageBytes = file.getBytes();
        return DigestUtils.sha256Hex(imageBytes);
    }


    public AmazonS3 amazonS3() {
        Map<String, String> credentials = this.getBasicCredentials(); // must return accessKey & secretKey
//        log.info("Credentials: {}", credentials);
        System.out.println("Credentials: " + credentials);
        BasicAWSCredentials creds = new BasicAWSCredentials(
                credentials.get("accessKey"),
                credentials.get("secretKey")
        );

        return AmazonS3ClientBuilder.standard()
                .withRegion(REGION)
                .withCredentials(new AWSStaticCredentialsProvider(creds))
                .build();
    }

}
