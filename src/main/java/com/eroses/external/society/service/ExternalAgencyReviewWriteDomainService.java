package com.eroses.external.society.service;

import com.eroses.external.society.mappers.ExternalAgencyReviewDao;
import com.eroses.external.society.model.ExternalAgencyReview;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExternalAgencyReviewWriteDomainService {
    private final ExternalAgencyReviewDao externalAgencyReviewDao;

    public Long create(ExternalAgencyReview externalAgencyReview) throws Exception {
        externalAgencyReview.setStatus(0);
        externalAgencyReviewDao.create(externalAgencyReview);
        return externalAgencyReview.getId();
    }

    public Long update(ExternalAgencyReview externalAgencyReview) throws Exception {
        externalAgencyReviewDao.update(externalAgencyReview);
        return externalAgencyReview.getId();
    }
}
