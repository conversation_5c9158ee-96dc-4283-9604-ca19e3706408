package com.eroses.external.society.service.document.strategy.impl;

import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.DocumentTemplateEnum;
import com.eroses.external.society.model.lookup.AdmInsolvencyDepartment;
import com.eroses.external.society.service.*;
import com.eroses.external.society.service.admin.AdmBranchReadDomainService;
import com.eroses.external.society.service.admin.AdmInsolvencyDepartmentReadDomainService;
import com.eroses.external.society.service.document.strategy.AbstractDocumentMappingStrategy;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.service.utils.HtmlGeneratorService;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * Strategy implementation for Surat <PERSON> type - <PERSON>, Tolak, Lulu<PERSON>
 */
@Slf4j
@Component
public class SuratKelulusanRayuanStrategy extends AbstractDocumentMappingStrategy {

    private final AppealReadDomainService appealReadDomainService;
    private final BranchReadDomainService branchReadDomainService;
    private final RoApprovalReadDomainService roApprovalReadDomainService;
    private final AdmBranchReadDomainService admBranchReadDomainService;
    private final AdmInsolvencyDepartmentReadDomainService admInsolvencyDepartmentReadDomainService;

    public SuratKelulusanRayuanStrategy(
            DocumentTemplateReadDomainService documentTemplateReadDomainService,
            S3DomainService s3DomainService,
            PdfService pdfService,
            HtmlGeneratorService htmlGeneratorService,
            UserFacade userFacade,
            SocietyReadDomainService societyReadDomainService,
            AppealReadDomainService appealReadDomainService,
            BranchReadDomainService branchReadDomainService,
            RoApprovalReadDomainService roApprovalReadDomainService,
            AdmBranchReadDomainService admBranchReadDomainService,
            AdmInsolvencyDepartmentReadDomainService admInsolvencyDepartmentReadDomainService) {
        super(documentTemplateReadDomainService, s3DomainService, pdfService, htmlGeneratorService, userFacade, societyReadDomainService);
        this.appealReadDomainService = appealReadDomainService;
        this.branchReadDomainService = branchReadDomainService;
        this.roApprovalReadDomainService = roApprovalReadDomainService;
        this.admBranchReadDomainService = admBranchReadDomainService;
        this.admInsolvencyDepartmentReadDomainService = admInsolvencyDepartmentReadDomainService;
    }

    @Override
    public String mapDataToDocument(String templateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        DocumentTemplate template = getDocumentTemplate(templateCode);
        String htmlTemplate = template.getHtmlContent();

        Map<String, String> mapFields = prepareDataForMapping(societyId, additionalParams);
        String processedHtml = htmlGeneratorService.processConditionals(htmlTemplate, mapFields);
        return htmlGeneratorService.mapToHtml(processedHtml, mapFields);
    }

    @Override
    protected Map<String, String> prepareDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {

        Society society = societyReadDomainService.findById(societyId);
        Appeal appeal = appealReadDomainService.getById((Long) additionalParams.get("appealId"));

        Map<String, Object> approvalParam = new HashMap<>();
        approvalParam.put("appealId", appeal.getId());
        if (additionalParams.containsKey("decision")) {
            approvalParam.put("decision", additionalParams.get("decision"));
        }
        RoApproval roApproval = roApprovalReadDomainService.findRoApprovalByModuleId(approvalParam);

        Map<String, String> mapFields = new HashMap<>();
        mapFields.put("{{NO_SURAT_RUJUKAN}}", getValueOrDefault(appeal.getLetterReferenceNo()));
        mapFields.put("{{TARIKH_SURAT}}", formatToMalayDate(roApproval.getDecisionDate()));
        mapFields.put("{{NAMA_PERTUBUHAN}}", getValueOrDefault(society.getSocietyName()).toUpperCase());

       String processedAddress = societyProcessedAddress(society.getAddress());
        mapFields.put("{{ALAMAT_PERTUBUHAN}}", getValueOrDefault(processedAddress));
        mapFields.put("{{POSKOD_PERTUBUHAN", getValueOrDefault(society.getPostcode()));
        mapFields.put("{{BANDAR_PERTUBUHAN}}", getValueOrDefault(society.getCity()));
        mapFields.put("{{NO_PERTUBUHAN}}", getValueOrDefault(society.getSocietyNo() != null ? society.getSocietyNo() : society.getApplicationNo()));

        //ppkdn approver
        String ppkdnApprover = ppkdnApproverProcessor(roApproval.getApprovedBy());
        mapFields.put("{{PELULUS_PPKDN}}", getValueOrDefault(ppkdnApprover));

        //jppm state processor
        AdmBranch jppmState = admBranchReadDomainService.getByStateCode(society.getStateCode());
        mapFields.put("{{CAWANGAN_JPPM}}", getValueOrDefault(jppmState.getDescription() != null ? jppmState.getDescription() : ""));
        mapFields.put("{{ALAMAT_CAWANGAN_JPPM}}", getValueOrDefault(addressBeautifier(jppmState.getAddress() != null ? jppmState.getAddress() : "")));

        //alamat jppm and insolvensi
        String insolvensiStateAddress = "";
        if (jppmState != null) {
            insolvensiStateAddress = insolvensiStateAddressProcessor(jppmState);
        }
        mapFields.put("{{ALAMAT_CAWANGAN_INSOLVENSI}}", getValueOrDefault(insolvensiStateAddress));
        return mapFields;
    }

    @Override
    public boolean canHandle(String templateCode) {
        return DocumentTemplateEnum.SURAT_RAYUAN_LULUS.getCode().equals(templateCode) ||
                DocumentTemplateEnum.SURAT_RAYUAN_TOLAK.getCode().equals(templateCode) ||
                DocumentTemplateEnum.SURAT_RAYUAN_LULUS_BERSYARAT.getCode().equals(templateCode);
    }

    @Override
    public String getFileName(DocumentTemplate documentTemplate, Long societyId, Map<String, Object> additionalParams) throws Exception {
        Appeal appeal = appealReadDomainService.getById((Long) additionalParams.get("appealId"));
        Map<String, Object> approvalParam = new HashMap<>();
        approvalParam.put("appealId", appeal.getId());
        if (additionalParams.containsKey("decision")) {
            approvalParam.put("decision", additionalParams.get("decision"));
        }
        RoApproval roApproval = roApprovalReadDomainService.findRoApprovalByModuleId(approvalParam);

        String index = "Surat Kelulusan Rayuan";
        String appealNo = appeal.getLetterReferenceNo() != null ? "(" + appeal.getLetterReferenceNo() + ")" : "";
        String registrationNo;
        if (appeal.getBranchId() != null) {
            Branch branch = branchReadDomainService.getBranchById(appeal.getBranchId());
            registrationNo = branch.getBranchNo() != null ? branch.getBranchNo() : branch.getBranchApplicationNo();
        } else {
            Society society = societyReadDomainService.findById(appeal.getSocietyId());
            registrationNo = society.getSocietyNo() != null ? society.getSocietyNo() : society.getApplicationNo();
        }
        return (index + " " + registrationNo + " " + appealNo);
    }

    private String societyProcessedAddress(String address) {
        if (address == null || address.isEmpty()) {
            return "";
        }

        // Split the address by commas
        String[] addressParts = address.split(",");

        // Join the parts with a comma and new line
        StringBuilder processedAddress = new StringBuilder();
        for (int i = 0; i < addressParts.length; i++) {
            processedAddress.append(addressParts[i].trim());
            if (i < addressParts.length - 1) {
                processedAddress.append(",\n");
            }
        }

        return processedAddress.toString();
    }

    private String ppkdnApproverProcessor(Long id) {
        User user = userFacade.getUserById(id);

        return user.getName() != null ? user.getName() : "";
    }

    private String addressBeautifier(String address) {
        if (address == null || address.isEmpty()) return address;

        String[] parts = address.split(",");
        StringBuilder result = new StringBuilder();
        boolean foundPostcode = false;

        for (int i = 0; i < parts.length; i++) {
            String trimmed = parts[i].trim();

            // If postcode is found or already passed, append rest in one line
            if (!foundPostcode && trimmed.matches(".*\\b\\d{5}\\b.*")) {
                foundPostcode = true;
                result.append(trimmed);
            } else if (foundPostcode) {
                result.append(", ").append(trimmed);
            } else {
                result.append(trimmed).append("\n");
            }
        }

        return result.toString();
    }

    private String insolvensiStateAddressProcessor(AdmBranch jppmState) {
        AdmInsolvencyDepartment admInsolvencyDepartment = admInsolvencyDepartmentReadDomainService.findByStateCode(jppmState.getStateCode());
        return admInsolvencyDepartment.getAddress();
    }
}
