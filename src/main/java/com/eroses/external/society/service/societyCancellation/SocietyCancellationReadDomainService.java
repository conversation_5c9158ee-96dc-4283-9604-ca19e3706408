package com.eroses.external.society.service.societyCancellation;

import com.eroses.external.society.mappers.SocietyCancellationDao;
import com.eroses.external.society.model.societyCancellation.SocietyCancellation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyCancellationReadDomainService {
    private final SocietyCancellationDao societyCancellationDao;

    public SocietyCancellation findById(Long id) {
        return societyCancellationDao.findById(id);
    }

    public List<SocietyCancellation> findAll() {
        return societyCancellationDao.findAll();
    }

    public List<SocietyCancellation> findBySocietyId(Long societyId) {
        return societyCancellationDao.findBySocietyId(societyId);
    }

    public SocietyCancellation findBySocietyNo(String societyNo) {
        return societyCancellationDao.findBySocietyNo(societyNo);
    }

    public List<SocietyCancellation> findByParam(Map<String, Object> params) {
        return societyCancellationDao.findByParam(params);
    }

    public Long countByParam(Map<String, Object> params) {
        return societyCancellationDao.countByParam(params);
    }

    public List<SocietyCancellation> findActiveSocietyCancellationsByParam(String societyName, String societyNo, List<String> stateCodes, Integer societyCategoryCode, String section, LocalDate cancelledDateFrom, LocalDate cancelledDateTo, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (societyNo != null && !societyNo.isEmpty()) params.put("societyNo", societyNo);
        if (stateCodes != null && !stateCodes.isEmpty()) params.put("stateCodes", stateCodes);
        if (societyCategoryCode != null && societyCategoryCode != 0) params.put("societyCategoryCode", societyCategoryCode);
        if (section != null && !section.isEmpty()) params.put("section", section);
        if (cancelledDateFrom != null) params.put("cancelledDateFrom", cancelledDateFrom);
        if (cancelledDateTo != null) params.put("cancelledDateTo", cancelledDateTo);
        params.put("offset", offset);
        params.put("limit", limit);
        params.put("isReverted", false);
        return societyCancellationDao.findActiveSocietyCancellationsByParam(params);
    }

    public Long countActiveSocietyCancellationsByParam(String societyName, String societyNo, List<String> stateCodes, Integer societyCategoryCode, String section, LocalDate cancelledDateFrom, LocalDate cancelledDateTo) {
        Map<String, Object> params = new HashMap<>();
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (societyNo != null && !societyNo.isEmpty()) params.put("societyNo", societyNo);
        if (stateCodes != null && !stateCodes.isEmpty()) params.put("stateCodes", stateCodes);
        if (societyCategoryCode != null && societyCategoryCode != 0) params.put("societyCategoryCode", societyCategoryCode);
        if (section != null && !section.isEmpty()) params.put("section", section);
        if (cancelledDateFrom != null) params.put("cancelledDateFrom", cancelledDateFrom);
        if (cancelledDateTo != null) params.put("cancelledDateTo", cancelledDateTo);
        params.put("isReverted", false);
        return societyCancellationDao.countActiveSocietyCancellationsByParam(params);
    }

    public List<Long> getAllPendingToProcessSocietyCancellationId() {
        Map<String, Object> params = new HashMap<>();
        params.put("isReverted", false);
        params.put("isCancelled", false);
        return societyCancellationDao.getAllPendingToProcessSocietyCancellationId(params);
    }
}