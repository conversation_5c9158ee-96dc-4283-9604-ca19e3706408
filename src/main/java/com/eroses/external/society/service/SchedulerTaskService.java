package com.eroses.external.society.service;

import com.eroses.external.society.api.facade.RoQueryWriteFacade;
import com.eroses.external.society.api.facade.SocietyWriteFacade;
import com.eroses.external.society.model.RoQuery;
import com.eroses.external.society.model.Society;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SchedulerTaskService {
    private final SocietyWriteFacade societyWriteFacade;
    private final RoQueryWriteFacade roQueryWriteFacade;

//    private List<RoQuery> updateOverdueQuery() {
//        List<RoQuery> roQueries = roQueryWriteFacade.updateOverdueQuery();
//
//
//        return society;
//    }

}
