package com.eroses.external.society.service;

import com.eroses.external.society.api.converter.input.CommitteeArchiveConverter;
import com.eroses.external.society.mappers.BranchCommitteeArchiveDao;
import com.eroses.external.society.model.BranchCommittee;
import com.eroses.external.society.model.BranchCommitteeArchive;
import com.eroses.external.society.model.CommitteeDraft;
import com.eroses.external.society.model.Meeting;
import com.eroses.external.society.model.enums.Position;
import com.eroses.external.society.model.enums.StatusCode;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.utils.Assert;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BranchCommitteeArchiveWriteDomainService {

    private final BranchReadDomainService branchReadDomainService;
    private final BranchCommitteeArchiveDao branchCommitteeArchiveDao;
    private final MeetingReadDomainService meetingReadDomainService;
    private final CommitteeDraftWriteDomainService committeeDraftWriteDomainService;
    private final CommitteeArchiveConverter committeeArchiveConverter;

    public Boolean create(BranchCommitteeArchive branchCommitteeArchive) throws Exception {
        Boolean isCreated = branchCommitteeArchiveDao.create(branchCommitteeArchive);
        Assert.isTrue(isCreated, "Creating branch committee archive is unsuccessful.");

        return isCreated;
    }

    public Boolean update(BranchCommitteeArchive branchCommitteeArchive) throws Exception {
        Boolean isUpdated = branchCommitteeArchiveDao.update(branchCommitteeArchive);
        Assert.isTrue(isUpdated, "Updating branch committee archive is unsuccessful.");

        return isUpdated;
    }

    public Map<String, Object> addBranchCommitteeListIntoArchive(List<BranchCommitteeArchive> newBranchCommitteeArchive, User me, Map<String, Object> param) throws Exception {

        Meeting meeting = meetingReadDomainService.findById((Long) param.get("meetingId"));
        Map<String, Object> errorMap = new HashMap<>();

        for (BranchCommitteeArchive eachArchive : newBranchCommitteeArchive) {
            eachArchive.setStatus(StatusCode.TIDAK_AKTIF.getCode());
            eachArchive.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.INAKTIF.getCode()));
            eachArchive.setMarkedDate(meeting.getMeetingDate());
            eachArchive.setCreatedBy(me.getId());

            Boolean isCreated = branchCommitteeArchiveDao.create(eachArchive);
            if (!isCreated) {
                errorMap.put("Error inserting branch committee into archive for ID: " + eachArchive.getId(), eachArchive.getCommitteeName());
            }
        }

        return errorMap;
    }

    public Boolean addCommitteeListIntoArchive(List<BranchCommittee> currentCommitteeList, List<CommitteeDraft> committeeDraftList) {
        try {
            Map<Long, CommitteeDraft> currentMap = committeeDraftList.stream()
                    .filter(d -> d.getCommitteeTableOldId() != null)
                    .collect(Collectors.toMap(CommitteeDraft::getCommitteeTableOldId, c -> c));

            Map<String, List<CommitteeDraft>> draftDesignationCodeMap = committeeDraftList.stream()
                    .collect(Collectors.groupingBy(CommitteeDraft::getDesignationCode));

            if (currentCommitteeList != null && !currentCommitteeList.isEmpty()) {
                for (BranchCommittee currentCommittee : currentCommitteeList) {
                    CommitteeDraft committeeDraft = currentMap.get(currentCommittee.getId());

                    if (committeeDraft == null) {
                        if (Position.noncommitteePositionCodes().contains(String.valueOf(currentCommittee.getDesignationCode()))) {
                            continue;
                        }
                        String designationCode = String.valueOf(currentCommittee.getDesignationCode());
                        List<CommitteeDraft> possibleDrafts = draftDesignationCodeMap.getOrDefault(designationCode, new ArrayList<>());

                        // Try to find the first unlinked draft (i.e., committeeTableOldId == null)
                        Optional<CommitteeDraft> match = possibleDrafts.stream()
                                .filter(d -> d.getCommitteeTableOldId() == null)
                                .findFirst();

                        if (match.isPresent()) {
                            committeeDraft = match.get();
                            committeeDraft.setCommitteeTableOldId(currentCommittee.getId());
                            committeeDraftWriteDomainService.update(committeeDraft);
                        } else {
                            // Skip this committee if no matching draft found
                            continue;
                        }
                    }

                    BranchCommitteeArchive branchCommitteeArchive = committeeArchiveConverter.convertCommitteeToBranchCommitteeArchive(currentCommittee);
                    branchCommitteeArchive.setCommitteeTableOldId(currentCommittee.getId());
                    branchCommitteeArchive.setStatus(StatusCode.AKTIF.getCode());
                    branchCommitteeArchive.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                    branchCommitteeArchive.setMarkedDate(committeeDraft.getAppointedDate());
                    branchCommitteeArchive.setCreatedBy(committeeDraft.getCreatedBy());

                    //use params instead
                    Map<String, Object> params = new HashMap<>();
                    params.put("committeeTableOldId", currentCommittee.getId());
                    params.put("branchId", currentCommittee.getBranchId());
                    if (currentCommittee.getAppointedDate() != null) params.put("appointedDate", currentCommittee.getAppointedDate());
                    if (committeeDraft.getAppointedDate() != null) params.put("markedDate", committeeDraft.getAppointedDate());
                    params.put("position", currentCommittee.getDesignationCode());
                    BranchCommitteeArchive oldData = branchCommitteeArchiveDao.findOneByParams(params);
                    if (oldData != null) {
                        branchCommitteeArchive.setId(oldData.getId());
                        update(branchCommitteeArchive);
                    } else {
                        create(branchCommitteeArchive);
                    }
                }

                return true;
            } else {
                for (CommitteeDraft committeeDraft : committeeDraftList) {
                    BranchCommitteeArchive branchCommitteeArchive = committeeArchiveConverter.convertCommitteeDraftToBranchCommitteeArchive(committeeDraft);
                    branchCommitteeArchive.setStatus(StatusCode.AKTIF.getCode());
                    branchCommitteeArchive.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                    branchCommitteeArchive.setMarkedDate(committeeDraft.getAppointedDate());
                    branchCommitteeArchive.setCreatedBy(committeeDraft.getCreatedBy());

                    create(branchCommitteeArchive);

                    committeeDraft.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.SELESAI.getCode()));
                    committeeDraft.setStatus(StatusCode.AKTIF.getCode());
                    committeeDraftWriteDomainService.update(committeeDraft);
                }

                return true;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
