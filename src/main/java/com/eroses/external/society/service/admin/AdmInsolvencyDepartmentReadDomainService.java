package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmInsolvencyDepartmentDao;
import com.eroses.external.society.model.lookup.AdmInsolvencyDepartment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmInsolvencyDepartmentReadDomainService {
    private final AdmInsolvencyDepartmentDao admInsolvencyDepartmentDao;

    public AdmInsolvencyDepartment findById(Long id) {
        return admInsolvencyDepartmentDao.findById(id);
    }

    public List<AdmInsolvencyDepartment> getAll() {
        return admInsolvencyDepartmentDao.getAll();
    }

    public List<AdmInsolvencyDepartment> getAllActive() {
        return admInsolvencyDepartmentDao.getAllActive();
    }

    public List<AdmInsolvencyDepartment> getAll(String nameQuery, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "nameQuery", nameQuery,
                "offset", offset,
                "limit", limit
        );
        return admInsolvencyDepartmentDao.getAll(params);
    }

    public Long countAll(String nameQuery) {
        Map<String, Object> params = Map.of(
                "nameQuery", nameQuery
        );
        return admInsolvencyDepartmentDao.countAll(params);
    }

    public boolean existsByCode(String code) {
        return admInsolvencyDepartmentDao.existsByCode(code);
    }

    public boolean existsByCodeExcludingId(String code, Long id) {
        return admInsolvencyDepartmentDao.existsByCodeExcludingId(code, id);
    }

    public AdmInsolvencyDepartment findByStateCode(String stateCode) {
        return admInsolvencyDepartmentDao.findByStateCode(stateCode);
    }
}