package com.eroses.external.society.service.grant;

import com.eroses.external.society.mappers.grant.GrantApplicationDao;
import com.eroses.external.society.mappers.grant.GrantApplicationFieldValueDao;
import com.eroses.external.society.model.grant.GrantApplication;
import com.eroses.external.society.model.grant.GrantApplicationFieldValue;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class GrantApplicationReadDomainService {
    
    private final GrantApplicationDao grantApplicationDao;
    private final GrantApplicationFieldValueDao grantApplicationFieldValueDao;
    
    public List<GrantApplication> findAll(Long societyId, Long grantTemplateId, String status, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "grantTemplateId", grantTemplateId,
                "status", status,
                "offset", offset,
                "limit", limit
        );
        return grantApplicationDao.findAll(params);
    }
    
    public Long countAll(Long societyId, Long grantTemplateId, String status) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "grantTemplateId", grantTemplateId,
                "status", status
        );
        return grantApplicationDao.countAll(params);
    }
    
    public GrantApplication findById(Long id) {
        return grantApplicationDao.findById(id);
    }
    
    public List<GrantApplication> findBySocietyId(Long societyId) {
        return grantApplicationDao.findBySocietyId(societyId);
    }
    
    public List<GrantApplication> findByGrantTemplateId(Long grantTemplateId) {
        return grantApplicationDao.findByGrantTemplateId(grantTemplateId);
    }
    
    public List<GrantApplication> findByStatus(String status) {
        return grantApplicationDao.findByStatus(status);
    }
    
    public List<GrantApplication> findPendingStateApproval() {
        return grantApplicationDao.findPendingStateApproval();
    }
    
    public List<GrantApplication> findPendingHQApproval() {
        return grantApplicationDao.findPendingHQApproval();
    }
    
    public List<GrantApplicationFieldValue> findFieldValuesByApplicationId(Long grantApplicationId) {
        return grantApplicationFieldValueDao.findByGrantApplicationId(grantApplicationId);
    }
    
    public GrantApplicationFieldValue findFieldValueByApplicationIdAndFieldId(Long grantApplicationId, Long grantTemplateFieldId) {
        return grantApplicationFieldValueDao.findByGrantApplicationIdAndFieldId(grantApplicationId, grantTemplateFieldId);
    }
}
