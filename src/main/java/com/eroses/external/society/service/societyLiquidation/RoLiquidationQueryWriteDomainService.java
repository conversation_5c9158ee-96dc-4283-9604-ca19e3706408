package com.eroses.external.society.service.societyLiquidation;

import com.eroses.external.society.mappers.liquidation.RoLiquidationQueryDao;
import com.eroses.external.society.model.societyLiquidation.RoLiquidationQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class RoLiquidationQueryWriteDomainService {
    private final RoLiquidationQueryDao roLiquidationQueryDao;

    public void create(RoLiquidationQuery roLiquidationQuery) throws Exception {
        roLiquidationQueryDao.create(roLiquidationQuery);
    }
}
