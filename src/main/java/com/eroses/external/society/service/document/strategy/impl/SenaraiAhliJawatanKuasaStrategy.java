package com.eroses.external.society.service.document.strategy.impl;

import com.eroses.external.society.model.Committee;
import com.eroses.external.society.model.DocumentTemplate;
import com.eroses.external.society.model.Society;
import com.eroses.external.society.model.enums.*;
import com.eroses.external.society.service.*;
import com.eroses.external.society.service.document.strategy.AbstractDocumentMappingStrategy;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.service.utils.HtmlGeneratorService;
import com.eroses.user.api.facade.UserFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Strategy implementation for Senarai Ahli J<PERSON> Kuasa document type.
 */
@Slf4j
@Component
public class SenaraiAhliJawatanKuasaStrategy extends AbstractDocumentMappingStrategy {

    private final CommitteeReadDomainService committeeReadDomainService;

    public SenaraiAhliJawatanKuasaStrategy(
            DocumentTemplateReadDomainService documentTemplateReadDomainService,
            S3DomainService s3DomainService,
            PdfService pdfService,
            HtmlGeneratorService htmlGeneratorService,
            UserFacade userFacade,
            SocietyReadDomainService societyReadDomainService,
            CommitteeReadDomainService committeeReadDomainService) {
        super(documentTemplateReadDomainService, s3DomainService, pdfService, htmlGeneratorService, userFacade, societyReadDomainService);
        this.committeeReadDomainService = committeeReadDomainService;
    }

    @Override
    public String mapDataToDocument(String templateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        DocumentTemplate template = getDocumentTemplate(templateCode);
        String htmlTemplate = template.getHtmlContent();

        Map<String, String> mapFields = prepareDataForMapping(societyId, additionalParams);
        List<Map<String, String>> tableData = prepareTableDataForMapping(societyId, additionalParams);
        return htmlGeneratorService.processTemplate(
                htmlTemplate,
                mapFields,                 // Your common placeholder values like <<NO_PERTUBUHAN>>
                tableData,                 // Your committee data from getCommitteeListMapFields()
                "tbody id=\"committee-table-body\""  // Target the specific tbody
        );
    }

    @Override
    protected Map<String, String> prepareDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {
        Society society = societyReadDomainService.findById(societyId);
        List<String> committeeDesignationCodes = Position.committeePositionCodes();
        List<Committee> committees = committeeReadDomainService.findActiveCommitteesInSocietyWithRoles(
                committeeDesignationCodes,
                societyId);
        
        return getMapFields(society, committees);
    }

    @Override
    protected List<Map<String, String>> prepareTableDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {
        List<String> committeeDesignationCodes = Position.committeePositionCodes();
        List<Committee> committees = committeeReadDomainService.findActiveCommitteesInSocietyWithRoles(
                committeeDesignationCodes,
                societyId);

        return getCommitteeListMapFields(committees);
    }


    @Override
    public boolean canHandle(String templateCode) {
        return DocumentTemplateEnum.SENARAI_AHLI_JAWATAN_KUASA.getCode().equals(templateCode);
    }

    private static Map<String, String> getMapFields(Society society, List<Committee> committeeList){

        Map<String, String> mapFields = new HashMap<>();

        mapFields.put("{{NO_PERTUBUHAN}}", getValueOrDefault(society.getSocietyNo()));
        mapFields.put("{{NAMA_PERTUBUHAN}}", getValueOrDefault(society.getSocietyName()));
        mapFields.put("{{DD/MM/YYYY}}", getLatestCommitteeUpdateDate(committeeList));
        return mapFields;
    }

    private static List<Map<String, String>> getCommitteeListMapFields(List<Committee> committeeList) {
        // Filter and sort the committee list
        List<Committee> filteredAndSortedCommittees = committeeList.stream()
                .filter(committee -> {
                    Position position = Position.getRole(Integer.parseInt(committee.getDesignationCode()));
                    return position != null;
                })
                .sorted(Comparator.comparingInt(committee -> {
                    Position position = Position.getRole(Integer.parseInt(committee.getDesignationCode()));
                    return position != null ? position.getRank() : Integer.MAX_VALUE;
                }))
                .collect(Collectors.toList());

        // Map to the required structure
        List<Map<String, String>> committeeMembers = new ArrayList<>();
        int runningNumber = 1; // Counter for running number

        for (Committee committee : filteredAndSortedCommittees) {
            Map<String, String> member = new LinkedHashMap<>(); // Maintain insertion order
            Position position = Position.getRole(Integer.parseInt(committee.getDesignationCode())); // Get the Position object

            // Ensure the position is not null before accessing its name
            String positionName = (position != null) ? position.getName() : "UNKNOWN";

            member.put("{{Bil}}", String.valueOf(runningNumber)); // Use the running number
            member.put("{{Jawatan}}", positionName); // Use the position's name
            member.put("{{Nama AJK}}", getValueOrDefault(committee.getName()).toUpperCase());
            committeeMembers.add(member);
            runningNumber++; // Increment the running number
        }

        return committeeMembers;
    }

    private static String getLatestCommitteeUpdateDate(List<Committee> committees) {
        Optional<LocalDateTime> latestModifiedDate = committees.stream()
                .filter(committee -> {
                    Position position = Position.getRole(Integer.parseInt(committee.getDesignationCode()));
                    return position != null;
                })
                .map(Committee::getModifiedDate) // Extract the LocalDateTime
                .max(LocalDateTime::compareTo); // Find the maximum (latest) date

        return latestModifiedDate.map(localDateTime -> localDateTime.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))).orElse("");
    }
}