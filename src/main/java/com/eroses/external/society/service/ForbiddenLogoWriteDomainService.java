package com.eroses.external.society.service;

import com.eroses.external.society.mappers.ForbiddenLogoDao;
import com.eroses.external.society.model.ForbiddenLogo;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ForbiddenLogoWriteDomainService {
    private final ForbiddenLogoDao forbiddenLogoDao;
    private final ForbiddenKeywordReadDomainService forbiddenReadDomainService;
    private final ForbiddenLogoReadDomainService forbiddenLogoReadDomainService;

    @Transactional
    public ForbiddenLogo createLaranganLogo(ForbiddenLogo forbiddenLogo) {
        log.info("creating new larangan logo {}", forbiddenLogo);
        return forbiddenLogoDao.createLaranganLogo(forbiddenLogo);
    }

    @Transactional
    public ForbiddenLogo updateLaranganLogo(ForbiddenLogo forbiddenLogo) {
        log.info("updating larangan logo {}", forbiddenLogo);
        Boolean isUpdated = forbiddenLogoDao.updateLaranganLogo(forbiddenLogo);
        if (!isUpdated) {
            throw new RuntimeException("Failed to update larangan logo");
        }
        return forbiddenLogo;
    }

    @Transactional
    public Boolean deleteLaranganLogo(Long id) {
        ForbiddenLogo forbiddenLogo = forbiddenLogoReadDomainService.findLaranganLogoById(id);
        if (forbiddenLogo == null) {
            throw new EntityNotFoundException(
                    String.format("Logo larangan dengan ID %d tidak ditemui.", id)
            );
        }
        forbiddenLogo.setStatus(ApplicationStatusCode.PADAM.getCode());

        log.info("deleting larangan logo with id {}", forbiddenLogo.getId());
        this.updateLaranganLogo(forbiddenLogo);
        return true;
    }
}
