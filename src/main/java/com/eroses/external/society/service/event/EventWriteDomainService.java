package com.eroses.external.society.service.event;


import com.eroses.external.society.api.converter.output.EventApiOutputConverter;
import com.eroses.external.society.dto.response.event.EventResponse;
import com.eroses.external.society.mappers.EventDao;
import com.eroses.external.society.model.Event;
import com.eroses.external.society.model.EventOrganiser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventWriteDomainService {
    public final EventDao eventDao;
    public final EventOrganiserWriteDomainService eventOrganiserWriteService;
    public final PrivateEventSocietyWriteDomainService privateEventSocietyWriteService;
    public final EventApiOutputConverter eventApiOutputConverter;
    private static final String ALPHANUMERIC = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int UNIQUE_PART_LENGTH = 5;
    private static final SecureRandom random = new SecureRandom();

    public EventResponse createEvent(Event event) throws Exception {
        try {
//            String yearMonth = event.getEventStartDate().format(DateTimeFormatter.ofPattern("yyMM"));
            String eventNo = generateEventNo();
            event.setEventNo(eventNo);
            log.info("Creating new Event");
            Boolean eventCreated = eventDao.create(event);
            EventResponse eventResponse = eventApiOutputConverter.convertToDto(event);
            return eventResponse;


        } catch ( RuntimeException ex) {
            log.warn("Error" + ex.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new RuntimeException(ex.getMessage());
        }
    }

//    private String generateEventNo(String yearAndMonth) {
//        String eventPrefix = "EVT";
//        int nextNumber = 1;
//        Optional<Integer> existingSequence = eventDao.getNextSequence(yearAndMonth);
//        if (existingSequence.isPresent()) {
//            nextNumber = existingSequence.get();
//        } else {
//            eventDao.insertSequence(yearAndMonth);
//        }
//
//        return String.format("%s%s%03d", yearAndMonth, eventPrefix, nextNumber);
//    }

    private String generateEventNo() {
        // Get current year (last 2 digits)
        String yearPart = String.format("%02d", LocalDate.now().getYear() % 100);

        // Generate random alphanumeric string for uniqueness
        StringBuilder uniquePart = new StringBuilder();
        for (int i = 0; i < UNIQUE_PART_LENGTH; i++) {
            uniquePart.append(ALPHANUMERIC.charAt(random.nextInt(ALPHANUMERIC.length())));
        }

        return yearPart + uniquePart.toString();
    }


    public Event updateEvent(Long id, Event event) {
        event.setId(id);
        int eventUpdated = eventDao.update(event);
        if (eventUpdated > 0) {
            return event;
        } else {
            return null;

        }
    }

    public boolean deleteEvent(Long id) {
        log.info("Deleting event with id: {}", id);
        try {
            return eventDao.delete(id);
        } catch (Exception e) {
            log.error("Error deleting event: {}", e.getMessage(), e);
            return false;
        }
    }

    public static <T> boolean isNotEmpty(List<T> array) {
        return array != null && !array.isEmpty();
    }

    public static List<EventOrganiser> createEO(List<Long> organisersId, Long eventId, Long userId) {
        List<EventOrganiser> eoListWithEvent = new ArrayList<>();
        if (organisersId.isEmpty())
            return null;

        for (Long org : organisersId) {
            EventOrganiser newEventOrganiser = new EventOrganiser();
            newEventOrganiser.setCreatedBy(userId);
            newEventOrganiser.setEventId(eventId);
            newEventOrganiser.setOrganiserId(org);
            newEventOrganiser.setActive(true);
            eoListWithEvent.add(newEventOrganiser);
        }
        return eoListWithEvent;
    }
}
