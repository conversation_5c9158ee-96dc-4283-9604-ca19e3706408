package com.eroses.external.society.service;

import com.eroses.external.society.mappers.TrusteeHolderDao;
import com.eroses.external.society.model.TrusteeHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class TrusteeReadDomainService {

    private final TrusteeHolderDao trusteeHolderDao;

    public List<TrusteeHolder> findAll(Long societyId, Long branchId, Integer offset, Integer limit) {
        Map<String, Object> params = Optional.ofNullable(branchId).isPresent() ? Map.of("societyId", societyId, "branchId", branchId, "limit", limit, "offset", offset) : Map.of("societyId", societyId, "limit", limit, "offset", offset);
        return trusteeHolderDao.findBySocietyId(params);
    }

    public Long countAll(Long societyId, Long branchId) {
        Map<String, Object> params = Optional.ofNullable(branchId).isPresent() ? Map.of("societyId", societyId, "branchId", branchId) : Map.of("societyId", societyId);

        return trusteeHolderDao.countBySocietyId(params);
    }

    public TrusteeHolder findById(Long id) {
        return trusteeHolderDao.findById(id);
    }

    public boolean existsBySocietyIdAndIdentification(Long societyId, String identificationType, String identificationNo, Integer status) {
        return trusteeHolderDao.existsBySocietyIdAndIdentificationNumber(Map.of("societyId", societyId, "identificationType", identificationType, "identificationNo", identificationNo, "status", status));
    }

    public List<TrusteeHolder> findActiveTrusteesByParam(Map<String, Object> params) {
        return trusteeHolderDao.findActiveTrusteesByParam(params);
    }
}
