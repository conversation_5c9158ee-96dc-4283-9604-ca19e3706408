package com.eroses.external.society.service.Payment;

import com.eroses.external.society.mappers.PaymentRecordDao;
import com.eroses.external.society.model.payment.PaymentRecord;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class PaymentRecordReadDomainService {
    private final PaymentRecordDao paymentRecordDao;

    public List<PaymentRecord> getAllPaymentRecordsByCriteria(String searchQuery, String paymentType, String stateCode, String paymentMethod, String paymentStatus , int limit, int offset) {
        Map<String, Object> params = Map.of(
                "searchQuery", searchQuery,
                "paymentType", paymentType,
                "stateCode", stateCode,
                "paymentMethod", paymentMethod,
                "paymentStatus", paymentStatus,
                "limit", limit,
                "offset", offset
        );

        return paymentRecordDao.getAllPaymentRecordsByCriteria(params);
    }

    public Long countGetAllPaymentRecordsByCriteria(String searchQuery, String paymentType, String stateCode, String paymentMethod, String paymentStatus) {
        Map<String, Object> params = Map.of(
                "searchQuery", searchQuery,
                "paymentType", paymentType,
                "stateCode", stateCode,
                "paymentMethod", paymentMethod,
                "paymentStatus", paymentStatus
        );
        return paymentRecordDao.countGetAllPaymentRecordsByCriteria(params);
    }


    public PaymentRecord getPaymentRecordById(Long id) {
        return paymentRecordDao.selectById(id);
    }

    public PaymentRecord getLatestPaymentRecordByDocCodeAndReferenceNo(String docCode, String referenceNo) {
        Map<String, Object> params = Map.of(
                "docCode", docCode,
                "referenceNo", referenceNo
        );
        return paymentRecordDao.getLatestPaymentRecordByDocCodeAndReferenceNo(params);
    }


}
