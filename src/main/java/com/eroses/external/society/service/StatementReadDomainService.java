package com.eroses.external.society.service;

import com.eroses.external.society.dto.response.statement.StatementGetOneResponse;
import com.eroses.external.society.dto.response.statement.StatementInfoForInternalResponse;
import com.eroses.external.society.mappers.StatementDao;
import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.StatusCode;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatementReadDomainService {
    private final StatementDao statementDao;
    private final CommitteeReadDomainService committeeReadDomainService;
    private final BranchCommitteeReadDomainService branchCommitteeReadDomainService;
    private final SocietyCommitteeArchiveReadDomainService societyCommitteeArchiveReadDomainService;
    private final BranchCommitteeArchiveReadDomainService branchCommitteeArchiveReadDomainService;

    public boolean isExists(Long id) {
        return findById(id) != null;
    }

    public Statement findById(Long id) {
        return statementDao.findById(id);
    }

    public List<Statement> findAll(Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset);
        params.put("limit", limit);
        return statementDao.findAll(params);
    }

    public Long countFindAll() {
        return statementDao.countFindAll();
    }

    public List<StatementGetOneResponse> listStatements(Map<String, Object> params) {
        return statementDao.listStatements(params);
    }

    public long countListStatements(Map<String, Object> params) {
        return statementDao.countListStatements(params);
    }

    public boolean existsBySocietyIdAndStatementYear(Map<String, Object> params) {
        return statementDao.existsBySocietyIdAndStatementYear(params);
    }

    public Statement findByStatementId(Long statementId) {
        return statementDao.findByStatementId(statementId);
    }

    public List<Statement> admSearchStatement(String stateCode, Integer statementYear, String applicationStatusCode, String searchQuery, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        params.put("stateCode", stateCode);
        params.put("statementYear", statementYear);
        params.put("applicationStatusCode", applicationStatusCode);
        params.put("searchQuery", searchQuery);
        params.put("offset", offset);
        params.put("limit", limit);
        return statementDao.admSearchStatement(params);
    }

    public List<Statement> searchStatement(String searchQuery) {
        return statementDao.searchStatement(searchQuery);
    }

    public long countAdmSearchStatement(String stateCode, Integer statementYear, String applicationStatusCode, String searchQuery) {
        Map<String, Object> params = new HashMap<>();
        params.put("stateCode", stateCode);
        params.put("statementYear", statementYear);
        params.put("applicationStatusCode", applicationStatusCode);
        params.put("searchQuery", searchQuery);
        return statementDao.countAdmSearchStatement(params);
    }

    public StatementGetOneResponse getGeneralStatementInfo(Map<String, Object> params) {
        return statementDao.getGeneralStatementInfo(params);
    }

    public Statement findBySocietyIdOrBranchIdAndStatementYear(Long societyId, Long branchId, Integer year) {
        Map<String, Object> params = new HashMap<>();
        params.put("societyId", societyId);
        params.put("branchId", branchId);
        params.put("year", year);
        params.put("applicationStatusCode", String.valueOf(ApplicationStatusCode.SELESAI.getCode()));

        return statementDao.findBySocietyIdOrBranchIdAndStatementYear(params);
    }

    public Statement findByParam(Map<String, Object> param) {
        return statementDao.findByParam(param);
    }


    public List<Object> processCommitteeList(Map<String, Object> committeeParams, Statement statement) {
        //enhancement, loop through the committee
        List<Object> committeeList = new ArrayList<>();
        if (statement.getBranchId() != null) {
            if (statement.getAjkAppointedDate() != null) {
                committeeParams.put("branchId", statement.getBranchId());
                List<BranchCommittee> branchCommittees = branchCommitteeReadDomainService.findByParams(committeeParams);
                if (branchCommittees != null && !branchCommittees.isEmpty()) {
                    committeeList.addAll(branchCommittees);
                } else {
                    List<BranchCommitteeArchive> branchCommitteeArchives = branchCommitteeArchiveReadDomainService.findByParams(committeeParams);
                    if (branchCommitteeArchives != null && !branchCommitteeArchives.isEmpty()) {
                        committeeList.addAll(branchCommitteeArchives);
                    } else {
                        List<BranchCommittee> defaultCommittees = branchCommitteeReadDomainService.findByBranchIdAndApplicationStatusCodeAndStatus(
                                statement.getBranchId(),
                                ApplicationStatusCode.AKTIF.getCode(),
                                StatusCode.AKTIF.getCode());
                        if (defaultCommittees != null && !defaultCommittees.isEmpty()) {
                            committeeList.addAll(defaultCommittees);
                        }
                    }
                }
            }
        } else {
            committeeParams.put("societyId", statement.getSocietyId());
            List<Committee> societyCommittees = committeeReadDomainService.findByParams(committeeParams);
            if (societyCommittees != null && !societyCommittees.isEmpty()) {
                committeeList.addAll(societyCommittees);
            } else {
                List<SocietyCommitteeArchive> societyCommitteeArchives = societyCommitteeArchiveReadDomainService.findByParams(committeeParams);
                if (societyCommitteeArchives != null && !societyCommitteeArchives.isEmpty()) {
                    committeeList.addAll(societyCommitteeArchives);
                } else {
                    List<Committee> defaultCommittees = committeeReadDomainService.findBySocietyIdAndApplicationStatusCodeAndStatus(
                            statement.getSocietyId(),
                            ApplicationStatusCode.AKTIF.getCode(),
                            StatusCode.AKTIF.getCode());
                    if (defaultCommittees != null && !defaultCommittees.isEmpty()) {
                        committeeList.addAll(defaultCommittees);
                    }
                }
            }
        }

        return committeeList;
    }

    public List<StatementInfoForInternalResponse> getStatementListForInternal(Map<String, Object> params) {
        return statementDao.getStatementListForInternal(params);
    }

    public Long countGetStatementListForInternal(Map<String, Object> params) {
        return statementDao.countGetStatementListForInternal(params);
    }
}