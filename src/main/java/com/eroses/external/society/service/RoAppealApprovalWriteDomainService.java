package com.eroses.external.society.service;

import com.eroses.external.society.mappers.RoAppealApprovalDao;
import com.eroses.external.society.model.RoAppealApproval;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class RoAppealApprovalWriteDomainService {
    private final RoAppealApprovalDao roAppealApprovalDao;

    public Long create(RoAppealApproval roAppealApproval) throws Exception {
        roAppealApprovalDao.create(roAppealApproval);
        return roAppealApproval.getId();
    }

    public Long update(RoAppealApproval roAppealApproval) throws Exception {
        roAppealApprovalDao.update(roAppealApproval);
        return roAppealApproval.getId();
    }
}
