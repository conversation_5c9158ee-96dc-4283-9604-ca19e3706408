package com.eroses.external.society.service.document.strategy.impl;

import com.eroses.external.society.model.ConstitutionContent;
import com.eroses.external.society.model.ConstitutionType;
import com.eroses.external.society.model.DocumentTemplate;
import com.eroses.external.society.model.Society;
import com.eroses.external.society.model.enums.DocumentTemplateEnum;
import com.eroses.external.society.model.enums.StatusCode;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.service.*;
import com.eroses.external.society.service.admin.ConstitutionTypeReadDomainService;
import com.eroses.external.society.service.document.strategy.AbstractDocumentMappingStrategy;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.service.utils.HtmlGeneratorService;
import com.eroses.user.api.facade.UserFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Strategy implementation for Perlembagaan document type.
 */
@Slf4j
@Component
public class PerlembagaanStrategy extends AbstractDocumentMappingStrategy {

    private final ConstitutionContentReadDomainService constitutionContentReadDomainService;
    private final ConstitutionTypeReadDomainService constitutionTypeReadDomainService;

    public PerlembagaanStrategy(
            DocumentTemplateReadDomainService documentTemplateReadDomainService,
            S3DomainService s3DomainService,
            PdfService pdfService,
            HtmlGeneratorService htmlGeneratorService,
            UserFacade userFacade,
            SocietyReadDomainService societyReadDomainService,
            ConstitutionContentReadDomainService constitutionContentReadDomainService,
            ConstitutionTypeReadDomainService constitutionTypeReadDomainService) {
        super(documentTemplateReadDomainService, s3DomainService, pdfService, htmlGeneratorService, userFacade, societyReadDomainService);
        this.constitutionContentReadDomainService = constitutionContentReadDomainService;
        this.constitutionTypeReadDomainService = constitutionTypeReadDomainService;
    }

    @Override
    public String mapDataToDocument(String templateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        DocumentTemplate template = getDocumentTemplate(templateCode);
        String htmlTemplate = template.getHtmlContent();

        Map<String, String> mapFields = prepareDataForMapping(societyId, additionalParams);

        // Process conditionals first, then map the fields
        String processedHtml = htmlGeneratorService.processConditionals(htmlTemplate, mapFields);
        return htmlGeneratorService.mapToHtml(processedHtml, mapFields);
    }

    @Override
    protected Map<String, String> prepareDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {
        Society society = societyReadDomainService.findById(societyId);
        List<ConstitutionContent> constitutionContentList = constitutionContentReadDomainService.getAllBySocietyId(
                societyId, StatusCode.AKTIF.getCode(), ApplicationStatusCode.AKTIF.getCode());
        ConstitutionType constitutionType = constitutionTypeReadDomainService.findByName(society.getConstitutionType());

        return getMapFields(society, constitutionContentList, constitutionType);
    }

    @Override
    public boolean canHandle(String templateCode) {
        return DocumentTemplateEnum.PERLEMBAGAAN.getCode().equals(templateCode);
    }

    private static Map<String, String> getMapFields(Society society, List<ConstitutionContent> constitutionContentList, ConstitutionType constitutionType){

        Map<String, String> mapFields = new HashMap<>();
        mapFields.put("{{JENIS_PERLEMBAGAAN}}", getValueOrDefault(society.getConstitutionType()));
        mapFields.put("{{NAMA_PERTUBUHAN}}", getValueOrDefault(society.getSocietyName()));
        mapFields.put("{{(NO_PERTUBUHAN)}}", "(" + getValueOrDefault(society.getSocietyNo()) + ")");

        // Map all fasal headers and contents
        mapFasalHeadersAndContents(mapFields, constitutionContentList);

        return mapFields;
    }

    private static void mapFasalHeadersAndContents(Map<String, String> mapFields,
                                                   List<ConstitutionContent> constitutionContentList) {
        // Null or empty check
        if (constitutionContentList == null || constitutionContentList.isEmpty()) {
            return;
        }

        // Iterate through the list and map descriptions and headers to FASAL keys
        for (ConstitutionContent content : constitutionContentList) {
            if (content != null && content.getClauseContent() != null) {
                String clauseNo = content.getClauseContent().getClauseNo();
                if (clauseNo != null && !clauseNo.isEmpty()) {
                    try {
                        int clauseNumber = Integer.parseInt(clauseNo);

                        // Map header to FASAL_X_HEADER key
                        String headerKey = "{{FASAL_" + clauseNumber + "_HEADER}}";
                        String headerValue = content.getClauseContent().getName();
                        if (headerValue != null && !headerValue.trim().isEmpty()) {
                            mapFields.put(headerKey, headerValue.toUpperCase());
                        }

                        // Map content to FASAL_X_CONTENT key
                        String contentKey = "{{FASAL_" + clauseNumber + "_CONTENT}}";
                        String contentValue = content.getDescription();
                        if (contentValue != null && !contentValue.trim().isEmpty()) {
                            mapFields.put(contentKey, contentValue);
                        }

                        // Add a flag indicating this fasal exists
                        mapFields.put("{{FASAL_" + clauseNumber + "_EXISTS}}", "true");

                    } catch (NumberFormatException e) {
                        System.err.println("Invalid clause number: " + clauseNo);
                    }
                }
            }
        }
    }
}