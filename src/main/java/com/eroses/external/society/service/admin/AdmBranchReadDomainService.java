package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmBranchDao;
import com.eroses.external.society.model.AdmBranch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmBranchReadDomainService {
    private final AdmBranchDao admBranchDao;

    public List<AdmBranch> getAllAdmBranches() {
        return admBranchDao.findAll();
    }

    public List<AdmBranch> getAllByCodes(List<String> codes) {
        Map<String, Object> params = Map.of(
                "codes", codes
        );
        return admBranchDao.findAllByCodes(params);
    }

    public AdmBranch getByCode(String code) {
        Map<String, Object> params = Map.of(
                "code", code
        );
        return admBranchDao.findByCode(params);
    }

    public List<AdmBranch> getAdmBranchesByJppmBranchId(Long jppmBranchId) {
        return admBranchDao.findAdmBranchByJppmBranchId(jppmBranchId);
    }

    public String getStateCode(Long jppmBranchId) {
        return admBranchDao.findStateCodeByJppmBranchId(jppmBranchId);
    }

    public AdmBranch findById(Long id) {
        return admBranchDao.findById(id);
    }

    public AdmBranch getByStateCode(String stateCode) {
        return admBranchDao.findByStateCode(stateCode);
    }

    public List<AdmBranch> getAllByStateCode(String stateCode) {
        return admBranchDao.getAllByStateCode(stateCode);
    }

    public List<AdmBranch> getAllActive() {
        return admBranchDao.getAllActive();
    }

    public List<AdmBranch> list() {
        return admBranchDao.list();
    }

    public List<AdmBranch> getAll(String nameQuery, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "nameQuery", nameQuery,
                "offset", offset,
                "limit", limit
        );
        return admBranchDao.getAll(params);
    }

    public Long countAll(String nameQuery) {
        Map<String, Object> params = Map.of(
                "nameQuery", nameQuery
        );
        return admBranchDao.countAll(params);
    }

    public boolean existsByCode(String code) {
        return admBranchDao.existsByCode(code);
    }

    public boolean existsByCodeExcludingId(String code, Long id) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("id", id);
        return admBranchDao.existsByCodeExcludingId(params);
    }
}
