package com.eroses.external.society.service;

import com.eroses.external.society.dto.request.GetRoAppealRequest;
import com.eroses.external.society.mappers.RoAppealApprovalDao;
import com.eroses.external.society.model.RoAppealApproval;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class RoAppealReadDomainService {
    private final RoAppealApprovalDao roAppealApprovalDao;

    public List<RoAppealApproval> getQueryHistory(Long appealId) {
        String decision = String.valueOf(ApplicationStatusCode.KUIRI.getCode());
        Map<String, Object> params = Map.of(
                "appealId", appealId,
                "decision", decision
        );
        return roAppealApprovalDao.getQueryHistory(params);
    }

    public List<RoAppealApproval> getRoAppeal(GetRoAppealRequest getRoAppealRequest) {
        return roAppealApprovalDao.getRoAppeal(getRoAppealRequest);
    }
}
