package com.eroses.external.society.service;

import com.eroses.external.society.mappers.SearchInformationDocumentTemplateDao;
import com.eroses.external.society.model.SearchInformationDocumentTemplate;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;


@Slf4j
@Service
@RequiredArgsConstructor
public class SearchInformationDocumentTemplateWriteDomainService {
    private final SearchInformationDocumentTemplateDao searchInformationDocumentTemplateDao;

    public Long create(SearchInformationDocumentTemplate searchInformationDocumentTemplate, User currentUser) throws Exception {

        searchInformationDocumentTemplate.setCreatedBy(currentUser.getId());
        searchInformationDocumentTemplate.setCreatedDate(LocalDateTime.now());
        searchInformationDocumentTemplateDao.create(searchInformationDocumentTemplate);
        return searchInformationDocumentTemplate.getId();
    }
}
