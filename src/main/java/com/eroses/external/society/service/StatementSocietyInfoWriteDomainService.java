package com.eroses.external.society.service;

import com.eroses.external.society.mappers.StatementSocietyInfoDao;
import com.eroses.external.society.model.StatementSocietyInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatementSocietyInfoWriteDomainService {
    private final StatementSocietyInfoDao statementSocietyInfoDao;

    public boolean create(StatementSocietyInfo statementSocietyInfo) throws Exception {
        return statementSocietyInfoDao.create(statementSocietyInfo);
    }

    public boolean update(StatementSocietyInfo statementSocietyInfo) throws Exception {
        return statementSocietyInfoDao.update(statementSocietyInfo);
    }

    public boolean updateByStatementId(StatementSocietyInfo statementSocietyInfo) {
        return statementSocietyInfoDao.updateByStatementId(statementSocietyInfo);
    }

    public Boolean deleteStatement(Long statementId, Long societyId, Long branchId) {
        return statementSocietyInfoDao.deleteStatement(statementId, societyId, branchId);
    }
}
