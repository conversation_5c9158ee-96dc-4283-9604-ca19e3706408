package com.eroses.external.society.service;

import com.eroses.external.society.mappers.StatementContributionDao;
import com.eroses.external.society.model.StatementContribution;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatementContributionReadDomainService {

    private final StatementContributionDao statementContributionDao;

    public boolean isExists(Long id) {
        return findById(id) != null;
    }

    public StatementContribution findById(Long id) {
        return statementContributionDao.findById(id);
    }

    public List<StatementContribution> listStatementContributions(Long societyId, Long branchId, Long statementId, String contributionCode, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        if (branchId != null) params.put("branchId", branchId);
        if (societyId != null) params.put("societyId", societyId);
        if (statementId != null) params.put("statementId", statementId);
        if (contributionCode != null) params.put("contributionCode", contributionCode);
        params.put("offset", offset);
        params.put("limit", limit);
        return statementContributionDao.listStatementContributions(params);
    }

    public Long countListStatementContributions(Long societyId, Long branchId, Long statementId, String contributionCode) {
        Map<String, Object> params = new HashMap<>();
        if (branchId != null) params.put("branchId", branchId);
        if (societyId != null) params.put("societyId", societyId);
        params.put("statementId", statementId);
        params.put("contributionCode", contributionCode);
        return statementContributionDao.countListStatementContributions(params);
    }

    public StatementContribution findByStatementId(Long statementId) {
        return statementContributionDao.findByStatementId(statementId);
    }

    public StatementContribution findByParam(Map<String, Object> params) {
        return statementContributionDao.findByParam(params);
    }

    public List<StatementContribution> findAllByParam(Map<String, Object> params) {
        return statementContributionDao.findAllByParam(params);
    }
}
