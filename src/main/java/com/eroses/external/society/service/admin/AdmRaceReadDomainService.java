package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmRaceDao;
import com.eroses.external.society.model.lookup.AdmRace;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmRaceReadDomainService {
    private final AdmRaceDao admRaceDao;

    public AdmRace findById(Long id) {
        return admRaceDao.findById(id);
    }

    public List<AdmRace> getAll(String nameQuery, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "nameQuery", nameQuery,
                "offset", offset,
                "limit", limit
        );
        return admRaceDao.getAll(params);
    }

    public Long countAll(String nameQuery) {
        Map<String, Object> params = Map.of(
                "nameQuery", nameQuery
        );
        return admRaceDao.countAll(params);
    }

    public List<AdmRace> getAllActive() {
        return admRaceDao.getAllActive();
    }

    public boolean existsByCode(String code) {
        return admRaceDao.existsByCode(code);
    }

    public boolean existsByCodeExcludingId(String code, Long id) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("id", id);
        return admRaceDao.existsByCodeExcludingId(params);
    }
}
