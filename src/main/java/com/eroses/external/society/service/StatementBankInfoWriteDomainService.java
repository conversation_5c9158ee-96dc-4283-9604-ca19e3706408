package com.eroses.external.society.service;

import com.eroses.external.society.mappers.StatementBankInfoDao;
import com.eroses.external.society.model.StatementBankInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatementBankInfoWriteDomainService {

    private final StatementBankInfoDao statementBankInfoDao;

    // Create a new StatementBankInfo record
    public Boolean create(StatementBankInfo statementBankInfo) throws Exception {
        return statementBankInfoDao.create(statementBankInfo);
    }

    // Update an existing StatementBankInfo record
    public Boolean update(StatementBankInfo statementBankInfo) throws Exception {
        return statementBankInfoDao.update(statementBankInfo);
    }

    public Boolean delete(Long id) {
        return statementBankInfoDao.delete(id);
    }

    public Boolean deleteStatement(Map<String, Object> params) {
        return statementBankInfoDao.deleteStatement(params);
    }

    public Boolean deleteBankInfo(Long id) {
        return statementBankInfoDao.deleteBankInfo(id);
    }
}
