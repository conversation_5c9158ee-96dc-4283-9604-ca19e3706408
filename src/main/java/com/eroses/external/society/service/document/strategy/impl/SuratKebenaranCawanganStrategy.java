package com.eroses.external.society.service.document.strategy.impl;

import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.DocumentTemplateEnum;
import com.eroses.external.society.model.enums.SocietyCategoryEnum;
import com.eroses.external.society.model.enums.UserRoleEnum;
import com.eroses.external.society.service.BranchReadDomainService;
import com.eroses.external.society.service.DocumentTemplateReadDomainService;
import com.eroses.external.society.service.S3DomainService;
import com.eroses.external.society.service.SocietyReadDomainService;
import com.eroses.external.society.service.admin.AdmAddressesReadDomainService;
import com.eroses.external.society.service.admin.AdmBranchReadDomainService;
import com.eroses.external.society.service.document.strategy.AbstractDocumentMappingStrategy;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.service.utils.HtmlGeneratorService;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Strategy implementation for Surat Kebenaran Cawangan document type.
 */
@Slf4j
@Component
public class SuratKebenaranCawanganStrategy extends AbstractDocumentMappingStrategy {

    private final AdmAddressesReadDomainService admAddressesReadDomainService;
    private final BranchReadDomainService branchReadDomainService;
    private final AdmBranchReadDomainService admBranchReadDomainService;

    public SuratKebenaranCawanganStrategy(
            DocumentTemplateReadDomainService documentTemplateReadDomainService,
            S3DomainService s3DomainService,
            PdfService pdfService,
            HtmlGeneratorService htmlGeneratorService,
            UserFacade userFacade,
            SocietyReadDomainService societyReadDomainService,
            BranchReadDomainService branchReadDomainService,
            AdmAddressesReadDomainService admAddressesReadDomainService,
            AdmBranchReadDomainService admBranchReadDomainService) {
        super(documentTemplateReadDomainService, s3DomainService, pdfService, htmlGeneratorService, userFacade, societyReadDomainService);
        this.admAddressesReadDomainService = admAddressesReadDomainService;
        this.branchReadDomainService = branchReadDomainService;
        this.admBranchReadDomainService = admBranchReadDomainService;
    }

    @Override
    public String mapDataToDocument(String templateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        DocumentTemplate template = getDocumentTemplate(templateCode);
        String htmlTemplate = template.getHtmlContent();

        Map<String, String> mapFields = prepareDataForMapping(societyId, additionalParams);
        String processedHtml = htmlGeneratorService.processConditionals(htmlTemplate, mapFields);
        return htmlGeneratorService.mapToHtml(processedHtml, mapFields);
    }

    @Override
    public boolean canHandle(String templateCode) {
        return DocumentTemplateEnum.SURAT_KEBENARAN_CAWANGAN.getCode().equals(templateCode);
    }

    @Override
    protected Map<String, String> prepareDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {
        Society society = societyReadDomainService.findById(societyId);
        Branch branch = branchReadDomainService.getBranchById((Long) additionalParams.get("branchId"));

        Long branchStateCode = Long.valueOf(branch.getStateCode());
        Long societyStateCode = Long.valueOf(society.getStateCode());

        AdmAddresses branchState = admAddressesReadDomainService.findById(branchStateCode);
        AdmAddresses societyState = admAddressesReadDomainService.findById(societyStateCode);
        AdmBranch admBranch = admBranchReadDomainService.getByStateCode(branch.getStateCode());

        Map<String, String> mapFields = getMapFields(society, branch, branchState, societyState, admBranch);

        return mapFields;
    }

    private static Map<String, String> getMapFields(Society society, Branch branch, AdmAddresses branchState, AdmAddresses societyState, AdmBranch admBranch) {

        String stateAddress = addressProcessor(branchState, admBranch, null, null);
        String societyAddress = addressProcessor(societyState, null, society, null);
        String branchAddress = addressProcessor(branchState, null, null, branch);

        Map<String, String> mapFields = new HashMap<>();
        mapFields.put("{{branchState}}", getValueOrDefault(admBranch.getDescription()).toUpperCase());
        mapFields.put("{{stateAddress}}", getValueOrDefault(stateAddress));
        mapFields.put("{{stateNo}}", getValueOrDefault(admBranch.getPhoneNumber()));
        mapFields.put("{{stateFaxNumber}}", getValueOrDefault(admBranch.getFaxNumber()));
        mapFields.put("{{branchNo}}", getValueOrDefault(society.getSocietyNo()));
        mapFields.put("{{dibenarkanDate}}", formatToMalayDate(branch.getSubmissionDate()));
        mapFields.put("{{societyAddress}}", getValueOrDefault(societyAddress));
        mapFields.put("{{branchName}}", getValueOrDefault(branch.getName()));
        mapFields.put("{{societyName}}", getValueOrDefault(society.getSocietyName().toUpperCase()));
        mapFields.put("{{branchAddress}}", getValueOrDefault(branchAddress));

        return mapFields;
    }

    private static String addressProcessor(AdmAddresses admAddresses, AdmBranch admBranch, Society society, Branch branch) {
        String address = "";

        if (admBranch != null) {
            String[] parts = admBranch.getAddress().split(",");
            StringBuilder formatted = new StringBuilder();

            for (int i = 0; i < parts.length; i++) {
                formatted.append(parts[i].trim());
                if (i < parts.length - 1) {
                    formatted.append(", ").append("\n");
                }
            }

            address = formatted.toString();

        } else if (society != null) {
            StringBuilder sb = new StringBuilder();

            sb.append(getValueOrDefault(society.getAddress()).toUpperCase()).append(", ").append("\n");
            sb.append(getValueOrDefault(society.getPostcode())).append(", ").append("\n");
            sb.append(getValueOrDefault(society.getCity()).toUpperCase()).append(", ").append("\n");
            sb.append(getValueOrDefault(admAddresses.getName()).toUpperCase());

            address = sb.toString();

        } else if (branch != null) {
            StringBuilder sb = new StringBuilder();

            sb.append(getValueOrDefault(branch.getAddress()).toUpperCase()).append(", ").append("\n");
            sb.append(getValueOrDefault(branch.getPostcode())).append(", ").append("\n");
            sb.append(getValueOrDefault(branch.getCity()).toUpperCase()).append(", ").append("\n");
            sb.append(getValueOrDefault(admAddresses.getName()).toUpperCase());

            address = sb.toString();
        }

        return address;
    }

    @Override
    public String getFileName(DocumentTemplate documentTemplate, Long societyId, Map<String, Object> additionalParams) throws Exception {
        Society society = societyReadDomainService.findById(societyId);
        Branch branch = branchReadDomainService.getBranchById((Long) additionalParams.get("branchId"));
        return documentTemplate.getTemplateName() + " " + society.getSocietyNo() + " " + branch.getName();
    }
}
