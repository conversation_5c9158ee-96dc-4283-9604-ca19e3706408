package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmCalendarDao;
import com.eroses.external.society.mappers.AdmCalendarStateDao;
import com.eroses.external.society.model.lookup.AdmCalendar;
import com.eroses.external.society.model.lookup.AdmCalendarState;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmCalendarWriteDomainService {
    private final AdmCalendarDao admCalendarDao;
    private final AdmCalendarStateDao admCalendarStateDao;

    @Transactional
    public Long create(AdmCalendar calendar) throws Exception {
        // Create the calendar entry
        admCalendarDao.create(calendar);
        Long calendarId = calendar.getId();

        // Create the calendar-state relationships
        if (calendar.getStateIds() != null && !calendar.getStateIds().isEmpty()) {
            for (Long stateId : calendar.getStateIds()) {
                AdmCalendarState calendarState = new AdmCalendarState();
                calendarState.setCalendarId(calendarId);
                calendarState.setStateId(stateId);
                calendarState.setCreatedBy(calendar.getCreatedBy());
                calendarState.setModifiedBy(calendar.getModifiedBy());
                admCalendarStateDao.create(calendarState);
            }
        }

        return calendarId;
    }

    @Transactional
    public boolean update(AdmCalendar calendar) throws Exception {
        // Update the calendar entry
        boolean updated = admCalendarDao.update(calendar);

        if (updated && calendar.getStateIds() != null) {
            // Delete existing state relationships
            admCalendarStateDao.deleteByCalendarId(calendar.getId());

            // Create new state relationships
            for (Long stateId : calendar.getStateIds()) {
                AdmCalendarState calendarState = new AdmCalendarState();
                calendarState.setCalendarId(calendar.getId());
                calendarState.setStateId(stateId);
                calendarState.setCreatedBy(calendar.getModifiedBy());
                calendarState.setModifiedBy(calendar.getModifiedBy());
                admCalendarStateDao.create(calendarState);
            }
        }

        return updated;
    }

    @Transactional
    public Long delete(Long id) throws Exception {
        // Delete state relationships first
        admCalendarStateDao.deleteByCalendarId(id);

        // Then delete the calendar entry
        admCalendarDao.delete(id);
        return id;
    }
}