package com.eroses.external.society.service;

import com.eroses.external.society.mappers.SearchInformationDao;
import com.eroses.external.society.model.SearchInformation;
import com.eroses.external.society.model.SearchInformationDocument;
import com.eroses.external.society.model.Society;
import com.eroses.external.society.model.enums.PaymentMethod;
import com.eroses.external.society.model.enums.PaymentStatus;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service
@RequiredArgsConstructor
public class SearchInformationWriteDomainService {
    private final SearchInformationDao searchInformationDao;
    private final SearchInformationDocumentReadDomainService searchInformationDocumentReadDomainService;
    private final SearchInformationDocumentWriteDomainService searchInformationDocumentWriteDomainService;

    public Long create(SearchInformation searchInformation, Society society, User currentUser, String paymentMethod, BigDecimal amount, String bankName) throws Exception {

        searchInformation.setSocietyNo(society.getSocietyNo());
        searchInformation.setViewCertificate(null); //Pending clarification
        searchInformation.setViewConstitution(null); //Pending clarification
        searchInformation.setViewCommittee(null); //Pending clarification
        searchInformation.setPaymentTotal(amount);
        searchInformation.setAcknowledgementDate(LocalDateTime.now());
        if(Objects.equals(paymentMethod, PaymentMethod.KAUNTER.getIdentifier())) {
            searchInformation.setPaymentMethod(PaymentMethod.KAUNTER.getCode());
        } else if (Objects.equals(paymentMethod, PaymentMethod.ONLINE.getIdentifier())){
            searchInformation.setPaymentMethod(PaymentMethod.ONLINE.getCode());
        }
        searchInformation.setBankName(bankName);
        searchInformation.setBankReferenceNo(null); //Pending clarification
        searchInformation.setReceiptStatus(0); //Pending clarification
        searchInformation.setRo(null); //Pending clarification
        searchInformation.setEPaymentId(null); //Pending clarification
        searchInformation.setReconciliationDate(null); //Pending clarification
        searchInformation.setStatus(0);
        searchInformation.setCreatedBy(currentUser.getId());
        searchInformation.setCreatedDate(LocalDateTime.now());
        searchInformationDao.create(searchInformation);
        return searchInformation.getId();
    }

    public Long update(SearchInformation searchInformation) throws Exception {
        searchInformationDao.update(searchInformation);
        return searchInformation.getId();
    }

    public Long delete(SearchInformation searchInformation) {

        searchInformation.setApplicationStatusCode(ApplicationStatusCode.PADAM.getCode());
        searchInformationDao.delete(searchInformation);
        return searchInformation.getId();
    }

    public boolean updateSearchInformationStatus(SearchInformation searchInformation, int paymentStatusCode, String paymentMethod) throws Exception {

        List<SearchInformationDocument> searchInformationDocumentList =
                searchInformationDocumentReadDomainService.getAllBySearchInformationId(searchInformation.getId());
        if (searchInformationDocumentList == null) {
            return false;
        }

        Integer applicationStatusCode;
        LocalDateTime paymentDate = null;

        if(paymentStatusCode == PaymentStatus.PAID.getCode()){
            applicationStatusCode = ApplicationStatusCode.LULUS.getCode();
            paymentDate = LocalDateTime.now();
            searchInformation.setPaymentMethod(paymentMethod);
        }else {
            applicationStatusCode = ApplicationStatusCode.BAYARAN_GAGAL.getCode();
        }

        // Update search information and payment record
        searchInformation.setApplicationStatusCode(applicationStatusCode);
        searchInformation.setPaymentDate(paymentDate);

        // Update each document in the list
        for (SearchInformationDocument document : searchInformationDocumentList) {
            document.setApplicationStatusCode(applicationStatusCode);
            searchInformationDocumentWriteDomainService.update(document);
        }

        searchInformation.setModifiedBy(0L);
        searchInformation.setModifiedDate(LocalDateTime.now());
        searchInformationDao.update(searchInformation);

        return true;
    }
}
