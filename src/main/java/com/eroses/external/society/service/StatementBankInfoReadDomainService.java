package com.eroses.external.society.service;

import com.eroses.external.society.mappers.StatementBankInfoDao;
import com.eroses.external.society.model.StatementBankInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatementBankInfoReadDomainService {

    private final StatementBankInfoDao statementBankInfoDao;

    // Method to check if a StatementBankInfo exists by ID
    public boolean isExists(Long id) {
        StatementBankInfo statementBankInfo = findById(id);
        if(statementBankInfo == null) {
            return false;
        } else {
            return true;
        }
//        return statementBankInfo;
    }

    // Method to find a StatementBankInfo by its ID
    public StatementBankInfo findById(Long id) {
        return statementBankInfoDao.findById(id);
    }

    // Method to find all StatementBankInfo records with pagination
    public List<StatementBankInfo> listStatementBankInfo(Map<String, Object> params) {
        return statementBankInfoDao.listStatementBankInfo(params);
    }

    // Method to count the total number of StatementBankInfo records
    public Long countListStatementBankInfo(Map<String, Object> params) {
        return statementBankInfoDao.countListStatementBankInfo(params);
    }

    public StatementBankInfo findByStatementId(Map<String, Object> params) {
        return statementBankInfoDao.findByStatementId(params);
    }

    public StatementBankInfo getByParam(Map<String, Object> params) {
        return statementBankInfoDao.getByParam(params);
    }
}
