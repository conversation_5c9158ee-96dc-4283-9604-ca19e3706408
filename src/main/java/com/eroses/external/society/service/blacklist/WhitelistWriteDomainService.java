package com.eroses.external.society.service.blacklist;

import com.eroses.external.society.mappers.blacklist.WhitelistDao;
import com.eroses.external.society.model.blacklist.Whitelist;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class WhitelistWriteDomainService {
    private final WhitelistDao whitelistDao;

    @Transactional
    public Long create(Whitelist whitelist) throws Exception {
        whitelistDao.create(whitelist);
        return whitelist.getId();
    }

    @Transactional
    public Boolean update(Whitelist whitelist) throws Exception {
        return whitelistDao.update(whitelist);
    }

    @Transactional
    public Boolean delete(Long id) throws Exception {
        return whitelistDao.delete(id);
    }
}