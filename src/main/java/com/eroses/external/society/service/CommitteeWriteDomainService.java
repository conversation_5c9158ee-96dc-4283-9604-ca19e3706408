package com.eroses.external.society.service;

import com.eroses.external.society.mappers.CommitteeDao;
import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.*;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class CommitteeWriteDomainService {
    private final CommitteeDao committeeDao;
    private final DocumentReadDomainService documentReadDomainService;
    private final DocumentWriteDomainService documentWriteDomainService;
    private final CommitteeDraftWriteDomainService committeeDraftWriteDomainService;
    private final CommitteeDraftReadDomainService committeeDraftReadDomainService;
    private final SocietyCommitteeArchiveReadDomainService societyCommitteeArchiveReadDomainService;
    private final SocietyCommitteeArchiveWriteDomainService societyCommitteeArchiveWriteDomainService;
    private final S3DomainService s3DomainService;
    private final UserFacade authFacade;

    public Long create(Committee committee) throws Exception {
        committeeDao.create(committee);
        return committee.getId();
    }

    public void createByNonCitizenCommittee(NonCitizenCommittee nonCitizenCommittee, User user) throws Exception {
//        //need to check if active or archived
//        if (nonCitizenCommittee.getAppointedDate() != null) {
//            LocalDate appointedDate = nonCitizenCommittee.getAppointedDate();
//        }

        Map<String, Object> params = new HashMap<>();
        params.put("id", nonCitizenCommittee.getActiveCommitteeId());
        params.put("societyId", nonCitizenCommittee.getSocietyId());
        params.put("position", nonCitizenCommittee.getDesignationCode());
        params.put("appointedDate", nonCitizenCommittee.getAppointedDate());
        params.put("applicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
        params.put("status", StatusCode.AKTIF.getCode());

        //check first if from registration
        if (nonCitizenCommittee.getIsFromRegistration() != null && nonCitizenCommittee.getIsFromRegistration()) {
            params.remove("applicationStatusCode");
            params.remove("status");
        }

        Committee activeCommittee = committeeDao.findOneByParams(params);
        if (activeCommittee != null) {
            if (nonCitizenCommittee.getIsFromRegistration() != null && nonCitizenCommittee.getIsFromRegistration()) {
                activeCommittee.setStatus(StatusCode.PADAM.getCode());
                activeCommittee.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.PADAM.getCode()));
            } else {
                activeCommittee.setStatus(StatusCode.TIDAK_AKTIF.getCode());
                activeCommittee.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.INAKTIF.getCode()));
            }
            activeCommittee.setModifiedBy(user.getId());
            committeeDao.update(activeCommittee);

            Committee committee = new Committee();
            BeanUtils.copyProperties(nonCitizenCommittee, committee); //Double check on this later

            committee.setCommitteeTableOldId(nonCitizenCommittee.getActiveCommitteeId());
            committee.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
            committee.setStatus(StatusCode.AKTIF.getCode());
            committee.setNationalityStatus(NationalityStatusEnum.NON_CITIZEN.getCode());
            committee.setCreatedBy(user.getId());
            committeeDao.create(committee);

            //need to update committeedraft as well
            params.remove("id");
            CommitteeDraft committeeDraft = committeeDraftReadDomainService.findOneByParams(params);
            if (committeeDraft != null) {
                committeeDraft.setStatus(StatusCode.TIDAK_AKTIF.getCode());
                committeeDraft.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.INAKTIF.getCode()));
                committeeDraft.setModifiedBy(user.getId());
                committeeDraftWriteDomainService.update(committeeDraft);

                CommitteeDraft updateDraft = new CommitteeDraft();
                BeanUtils.copyProperties(nonCitizenCommittee, updateDraft);

                updateDraft.setCommitteeTableOldId(nonCitizenCommittee.getActiveCommitteeId());
                updateDraft.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                updateDraft.setStatus(StatusCode.AKTIF.getCode());
                updateDraft.setNationalityStatus(NationalityStatusEnum.NON_CITIZEN.getDescriptionBm());
                updateDraft.setCreatedBy(user.getId());
                committeeDraftWriteDomainService.create(updateDraft);
            }
        } else {
            CommitteeDraft committeeDraft = committeeDraftReadDomainService.findOneByParams(params);
            if (committeeDraft != null) {
                committeeDraft.setStatus(StatusCode.TIDAK_AKTIF.getCode());
                committeeDraft.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.INAKTIF.getCode()));
                committeeDraft.setModifiedBy(user.getId());
                committeeDraftWriteDomainService.update(committeeDraft);

                CommitteeDraft updateDraft = new CommitteeDraft();
                BeanUtils.copyProperties(nonCitizenCommittee, updateDraft);

                updateDraft.setCommitteeTableOldId(nonCitizenCommittee.getActiveCommitteeId());
                updateDraft.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                updateDraft.setStatus(StatusCode.AKTIF.getCode());
                updateDraft.setNationalityStatus(NationalityStatusEnum.NON_CITIZEN.getDescriptionBm());
                updateDraft.setCreatedBy(user.getId());
                committeeDraftWriteDomainService.create(updateDraft);
            }

            params.remove("id");
            SocietyCommitteeArchive activeSocietyCommitteeArchive = societyCommitteeArchiveReadDomainService.findOneByParams(params);
            if (activeSocietyCommitteeArchive != null) {
                activeSocietyCommitteeArchive.setStatus(StatusCode.TIDAK_AKTIF.getCode());
                activeSocietyCommitteeArchive.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.INAKTIF.getCode()));
                activeSocietyCommitteeArchive.setModifiedBy(user.getId());
                societyCommitteeArchiveWriteDomainService.update(activeSocietyCommitteeArchive);

                SocietyCommitteeArchive societyCommitteeArchive = new SocietyCommitteeArchive();
                BeanUtils.copyProperties(nonCitizenCommittee, societyCommitteeArchive); //Double check on this later

                societyCommitteeArchive.setStatus(StatusCode.AKTIF.getCode());
                societyCommitteeArchive.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                societyCommitteeArchive.setNationalityStatus(NationalityStatusEnum.NON_CITIZEN.getDescriptionBm());
                societyCommitteeArchive.setCreatedBy(user.getId());
                societyCommitteeArchiveWriteDomainService.create(societyCommitteeArchive);
            }
        }
    }

    public boolean update(Committee committee) throws Exception {
        return committeeDao.update(committee);
    }

    public boolean update(Committee committee, User user) throws Exception {
        committee.setModifiedBy(user.getId());
        return committeeDao.update(committee);
    }

    public void removeSecretary(String idNo, long societyId, long modifiedBy) throws Exception {
        Committee committee = new Committee();
        committee.setIdentificationNo(idNo);
        committee.setSocietyId(societyId);
        committee.setStatus(StatusCode.TIDAK_AKTIF.getCode());
        committee.setApplicationStatusCode( String.valueOf(ApplicationStatusCode.INAKTIF.getCode()));
        committee.setModifiedBy(modifiedBy);
        Map<String, Object> map = Map.of(
            "identificationNo", idNo,
            "societyId", societyId
        );
        committeeDao.removeSecretary(map);
    }

    public Long deleteByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus(String identificationNo, Long societyId, int applicationStatusCode, String status) {
        Map<String, Object> params = Map.of(
                "identificationNo", identificationNo,
                "societyId", societyId,
                "applicationStatusCode", applicationStatusCode,
                "status", status
        );
        return committeeDao.deleteByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus(params);
    }

    public Long deleteAllBySocietyId(Long societyId) {
        try {
            Long deletedRecords = committeeDao.deleteAllBySocietyId(societyId);
            return deletedRecords;
        } catch (Exception e) {
            log.error("Error occurred while deleting Committee records: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to delete Committee records.", e);
        }
    }

    public String uploadAJK(byte[] pdfBytes, String documentName, Society society, Long branchId) throws Exception {
        Map<String, Object> param = new HashMap<>();
        param.put("name", documentName);
        param.put("societyId", society.getId());
        if (branchId != null) param.put("branchId", branchId);

        MultipartFile multipartFile = new CustomMultipartFile(
                pdfBytes,
                documentName + ".pdf",
                "application/pdf"  // Set content type to PDF
        );

        String documentUrl = s3DomainService.uploadFile(multipartFile, S3FolderPath.SOCIETY_DOCUMENT.getPath(), documentName);

        List<Document> documents = documentReadDomainService.findDocumentByParam(param);
        Document document = new Document();
        if (branchId != null) {
            document.setBranchId(branchId);
        }
        if (!documents.isEmpty()) {
            Long id = documents.get(0).getId();

            //update document
            document.setUrl(documentUrl);
            documentWriteDomainService.updateDocumentUrl(document);
        } else {
            //Save document in DB
            document.setName(documentName);
            document.setType(DocumentTypeEnum.SOCIETY.getType());
            document.setUrl(documentUrl);
            document.setSocietyId(society.getId());
            document.setStatus(1L);
            document.setCreatedBy(authFacade.me().getId());
            documentWriteDomainService.registerFileInDb(document);
        }

        return documentUrl;
    }

    public void updateBlacklistStatusByIdentificationNo(String identificationNo, boolean isBlacklist) throws Exception {
        User user = authFacade.me();

        Map<String, Object> params = new HashMap<>();
        if (identificationNo != null && !identificationNo.isEmpty()) params.put("identificationNo", identificationNo);
        params.put("isBlacklist", isBlacklist);
        if (user != null) params.put("modifiedBy", user.getId());

        committeeDao.updateBlacklistStatusByIdentificationNo(params);
    }

    public Boolean updateCommitteeToInactive(List<Committee> currentCommitteeList) {
        try {
            List<Long> committeeIds = currentCommitteeList.stream()
                    .map(Committee::getId)
                    .toList();
            committeeDao.updateStatus(committeeIds, StatusCode.TIDAK_AKTIF.getCode(), ApplicationStatusCode.INAKTIF.getCode());

            return true;
        } catch (Exception e) {
            log.error("Error occurred while updating committee to inactive: {}", e.getMessage(), e);
            return false;
        }
    }

    public Boolean createCommitteeFromDraft(List<CommitteeDraft> committeeDraftList) {
        try {
            for (CommitteeDraft committeeDraft : committeeDraftList) {
                Committee committee = new Committee();
                BeanUtils.copyProperties(committeeDraft, committee);
                committee.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                committee.setStatus(StatusCode.AKTIF.getCode());
                committee.setCreatedBy(authFacade.me().getId());
                committeeDao.create(committee);

                committeeDraft.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                committeeDraft.setStatus(StatusCode.AKTIF.getCode());
                committeeDraftWriteDomainService.update(committeeDraft);
            }

            return true;
        } catch (Exception e) {
            log.error("Error occurred while creating committee from draft: {}", e.getMessage(), e);
            return false;
        }
    }

    public Boolean updateByOldIdAndAppointedDate(Committee updateCommittee) {
        try {
            committeeDao.updateByOldIdAndAppointedDate(updateCommittee);
            return true;
        } catch (Exception e) {
            log.error("Error occurred while updating committee: {}", e.getMessage(), e);
            return false;
        }
    }

    public void saveAll(List<Committee> currentCommitteeList) {
        try {
            committeeDao.saveAll(currentCommitteeList);
        } catch (Exception e) {
            log.error("Error occurred while saving committee: {}", e.getMessage(), e);
        }
    }
}
