package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.EventCertTemplateConfigurationDao;
import com.eroses.external.society.model.EventCertTemplateConfiguration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventCertTemplateConfigurationService {
        private final EventCertTemplateConfigurationDao certTemplateConfigurationDao;

    public EventCertTemplateConfiguration findByTemplateCode(String templateCode) {
        log.info("finding template by code: {}", templateCode);
        return certTemplateConfigurationDao.findByTemplateCode(templateCode);
    }

}
