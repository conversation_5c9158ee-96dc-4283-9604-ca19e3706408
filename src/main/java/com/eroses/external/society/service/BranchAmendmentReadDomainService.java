package com.eroses.external.society.service;

import com.eroses.external.society.mappers.BranchAmendmentDao;
import com.eroses.external.society.model.BranchAmendment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Slf4j
@Service
@RequiredArgsConstructor
public class BranchAmendmentReadDomainService {
    private final BranchAmendmentDao branchAmendmentDao;

    public BranchAmendment findById(Long id) throws Exception {
        return branchAmendmentDao.findById(id);
    }

    public BranchAmendment getByPaymentId(Long paymentId) throws Exception {
        return branchAmendmentDao.getByPaymentId(paymentId);
    }

    public List<BranchAmendment> getAllByCriteria(Map<String, Object> params) {
        return branchAmendmentDao.getAllByCriteria(params);
    }

    public List<BranchAmendment> getAllPendingBranchAmendmentByCriteria(Integer applicationStatusCode, String stateCode, Long roId, String searchQuery, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "stateCode", stateCode,
                "ro", roId,
                "searchQuery", searchQuery,
                "offset", offset,
                "limit", limit);

        return branchAmendmentDao.getAllPendingBranchAmendmentByCriteria(params);
    }

    public Integer countAllPendingBranchAmendmentByCriteria(Integer applicationStatusCode, String stateCode, Long roId, String searchQuery) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "stateCode", stateCode,
                "ro", roId,
                "searchQuery", searchQuery);

        return branchAmendmentDao.countAllPendingBranchAmendmentByCriteria(params);
    }
}
