package com.eroses.external.society.service.posting;

import com.eroses.external.society.mappers.*;
import com.eroses.external.society.model.enums.PostingStatusEnum;
import com.eroses.external.society.model.posting.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class PostingWriteDomainService {

    private final PostingDao postingDao;
    private final PostingMediaDao postingMediaDao;
    private final PostingEngagementDao postingEngagementDao;
    private final PostingReportDao postingReportDao;
    private final PostingNotificationDao postingNotificationDao;
    private final PostingReviewDao postingReviewDao;

    @Transactional
    public Long createPosting(Posting posting) throws Exception {
        postingDao.create(posting);
        return posting.getId();
    }

    @Transactional
    public Boolean updatePosting(Posting posting) throws Exception {
        return postingDao.update(posting);
    }

    @Transactional
    public Boolean deletePosting(Long id) {
        try {
            // First delete all related records in child tables
            postingMediaDao.deleteByPostingId(id);

            // Then delete the posting itself
            return postingDao.delete(id);
        } catch (Exception e) {
            log.error("Error deleting posting with ID: {}", id, e);
            return false;
        }
    }

    @Transactional
    public Boolean publishPosting(Long id, Long userId) throws Exception {
        Posting posting = postingDao.findById(id);
        if (posting == null) {
            return false;
        }

        posting.setStatus(PostingStatusEnum.PUBLISHED.name());
        posting.setPostingDate(LocalDateTime.now());
        posting.setModifiedBy(userId);
        posting.setModifiedDate(LocalDateTime.now());

        return postingDao.update(posting);
    }

    @Transactional
    public Boolean unpublishPosting(Long id, Long userId) throws Exception {
        Posting posting = postingDao.findById(id);
        if (posting == null) {
            return false;
        }

        posting.setStatus(PostingStatusEnum.UNPUBLISHED.name());
        posting.setModifiedBy(userId);
        posting.setModifiedDate(LocalDateTime.now());

        return postingDao.update(posting);
    }

    @Transactional
    public Boolean updateStatus(Long id, String status) {
        return postingDao.updateStatus(id, status);
    }

    @Transactional
    public Long createPostingMedia(PostingMedia postingMedia) throws Exception {
        postingMediaDao.create(postingMedia);
        return postingMedia.getId();
    }

    @Transactional
    public Boolean deletePostingMedia(Long id) {
        return postingMediaDao.delete(id);
    }

    @Transactional
    public Boolean deletePostingMediaByPostingId(Long postingId) {
        return postingMediaDao.deleteByPostingId(postingId);
    }

    @Transactional
    public Long createPostingEngagement(PostingEngagement postingEngagement) throws Exception {
        postingEngagementDao.create(postingEngagement);
        return postingEngagement.getId();
    }

    @Transactional
    public Long createPostingReport(PostingReport postingReport) throws Exception {
        postingReportDao.create(postingReport);
        return postingReport.getId();
    }

    @Transactional
    public Boolean updatePostingReportStatus(Long id, String status) {
        return postingReportDao.updateStatus(id, status);
    }

    @Transactional
    public Long createPostingNotification(PostingNotification postingNotification) throws Exception {
        // TODO: Implement notification creation functionality
        // postingNotificationDao.create(postingNotification);
        // return postingNotification.getId();
        return 1L; // Mock ID for now
    }

    @Transactional
    public Boolean markNotificationAsRead(Long id) {
        // TODO: Implement mark notification as read functionality
        // return postingNotificationDao.markAsRead(id);
        return true; // Mock success for now
    }

    @Transactional
    public Boolean markAllNotificationsAsRead(Long recipientId) {
        // TODO: Implement mark all notifications as read functionality
        // return postingNotificationDao.markAllAsRead(recipientId);
        return true; // Mock success for now
    }

    @Transactional
    public Long createPostingReview(PostingReview postingReview) throws Exception {
        postingReviewDao.create(postingReview);
        return postingReview.getId();
    }

    @Transactional
    public void processExpiredPostings() throws Exception {
        List<Posting> expiredPostings = postingDao.findExpiredPostings();
        for (Posting posting : expiredPostings) {
            posting.setStatus(PostingStatusEnum.UNPUBLISHED.name());
            posting.setModifiedDate(LocalDateTime.now());
            postingDao.updateBySystem(posting);
        }
    }

    @Transactional
    public void processPostingsToPublish() throws Exception {
        List<Posting> postingsToPublish = postingDao.findPostingsToPublish();
        for (Posting posting : postingsToPublish) {
            posting.setStatus(PostingStatusEnum.PUBLISHED.name());
            posting.setPostingDate(LocalDateTime.now());
            posting.setModifiedDate(LocalDateTime.now());
            postingDao.updateBySystem(posting);
        }
    }
}
