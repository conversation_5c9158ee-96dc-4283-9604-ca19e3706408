package com.eroses.external.society.service;

import com.eroses.external.society.mappers.RoQueryDao;
import com.eroses.external.society.model.RoQuery;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class RoQueryReadDomainService {
    private final RoQueryDao roQueryDao;

    public Boolean isExists(Long id) {
        RoQuery roQuery = roQueryDao.findById(id);
        return roQuery != null;
    }

    public RoQuery findByBranchId(Long id) {
        return roQueryDao.findByBranchId(id);
    }

    public List<RoQuery> getAllRoQuery(Long societyId, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "offset", offset,
                "limit", limit
        );
        return roQueryDao.findAll(params);
    }

    public List<RoQuery> findByAppealId(Long id) { return roQueryDao.findByAppealId(id); }

    public Long countGetAllRoQuery(Long societyId) {
        return roQueryDao.countFindAll(societyId);
    }

    public List<RoQuery> getByApprovalTypeAndCriteriaOrderByCreatedDateDesc(String roQueryType, Long societyId, Long branchId, Long appealId, Long amendmentId, Long liquidationId, Long principalSecretaryId, String queryReceiver) {
        Map<String, Object> params = Map.of(
                "roQueryType", roQueryType,
                "societyId", societyId,
                "branchId", branchId,
                "appealId", appealId,
                "amendmentId", amendmentId,
                "liquidationId", liquidationId,
                "principalSecretaryId", principalSecretaryId,
                "queryReceiver", queryReceiver);
        return roQueryDao.getByApprovalTypeAndCriteriaOrderByCreatedDateDesc(params);
    }
}