package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.EventFeedbackScaleDao;
import com.eroses.external.society.model.EventFeedbackScale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventFeedbackScaleReadDomainService {
    private final EventFeedbackScaleDao eventFeedbackScaleDao;

    public List<EventFeedbackScale> findAll() {
        log.info("Finding all Event Feedback Scales");
        return eventFeedbackScaleDao.findAll();
    }

    public EventFeedbackScale findById(Long id) {
        log.info("Finding Event Feedback Scale by Id: {}", id);
        return eventFeedbackScaleDao.findById(id);
    }

    public EventFeedbackScale findByCode(String code) {
        log.info("Finding Event Feedback Scale by Code: {}", code);
        return eventFeedbackScaleDao.findByCode(code);
    }

    public boolean existsByCode(String code) {
        log.info("Checking if Event Feedback Scale exists with Code: {}", code);
        EventFeedbackScale scale = eventFeedbackScaleDao.findByCode(code);
        return scale != null;
    }
}