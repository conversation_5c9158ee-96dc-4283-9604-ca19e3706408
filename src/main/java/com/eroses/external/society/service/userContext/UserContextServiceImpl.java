package com.eroses.external.society.service.userContext;

import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class UserContextServiceImpl implements UserContextService {
    private final UserFacade userFacade;

    @Autowired
    public UserContextServiceImpl(UserFacade userFacade) {
        this.userFacade = userFacade;
    }

    @Override
    public Long getCurrentUserId() {
        try {
            User user = userFacade.me();
            return (user != null) ? user.getId() : 0L;
        } catch (Exception e) {
            return 0L;
        }
    }
}
