package com.eroses.external.society.service;

import ai.djl.modality.cv.Image;
import ai.djl.ndarray.NDArray;
import ai.djl.ndarray.NDManager;
import ai.djl.ndarray.index.NDIndex;
import ai.djl.ndarray.types.DataType;
import ai.djl.ndarray.types.Shape;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@RequiredArgsConstructor
public class ForbiddenLogoService2 {
    private static final double LOGO_MATCH_THRESHOLD = 0.65;
    private static final ObjectMapper mapper = new ObjectMapper();
    private final ForbiddenLogoReadDomainService forbiddenLogoReadDomainService;

    public boolean checkContainForbiddenLogo(MultipartFile file, List<String> logoVectorList) throws Exception {

        String base64 = convertToBase64(file);
        List<double[]> vectorList = logoVectorList.stream()
                .map(this::stringToDouble)
                .filter(Objects::nonNull)   // ignore null results
                .toList();


        String json = buildPayload("img", base64);
        double[] result = getVectorFromAPI(json);

        return checkContainForbiddenLogic(result, vectorList);

    }

    public String checkExistingToUpload(String url) throws Exception {
        String json = buildPayload("url", url);
        double[] vector = getVectorFromAPI(json);
        List<String> logoVectorList = forbiddenLogoReadDomainService.findAllLaranganLogoVectorOnly();
        List<double[]> vectorList = logoVectorList.stream()
                .map(this::stringToDouble)
                .filter(Objects::nonNull)   // ignore null results
                .toList();
        List<Double> results = checkExistingLogic(vector, vectorList);

        for (Double result : results) {
            if (result >= LOGO_MATCH_THRESHOLD) {
                return null;
            }
        }
        return vectorToString(vector);


    }

    public static String vectorToString(double[] vector) {
        if (vector == null) {
            return null;
        }
        return Arrays.stream(vector)
                .mapToObj(Double::toString)
                .collect(Collectors.joining(","));
    }

    private static boolean checkContainForbiddenLogic(double[] uploadedVector, List<double[]> vectorList) {
        List<Double> suspicious = new ArrayList<>();

//        Layer 1
        for (double[] vec : vectorList) {
            double similarity = cosineSimilarity(uploadedVector, vec);
            if (similarity >= 0.9) {
                suspicious.add(similarity);
                return true;
            }

            if (similarity >= 0.5) {
                suspicious.add(similarity);
            }
        }

//        for (Double result : results) {
//            if (result >= )
//        }

        long strongMatches = suspicious.stream()
                .filter(sim -> sim >= 0.7)
                .count();

        return strongMatches >= 3;
    }

    public static List<Double> checkExistingLogic(double[] vector1, List<double[]> vector2List) {
        List<Double> results = new ArrayList<>();
        int countAbove07 = 0;


        for (double[] v2 : vector2List) {
            double similarity = cosineSimilarity(vector1, v2);

            if (similarity >= 0.9) {
                // Perfect match, stop immediately
                results.add(similarity);
                return results;
//                break;
            }

            if (similarity >= 0.65) {
                countAbove07++;
                results.add(similarity);
                if (countAbove07 == 3) {
                    // Found 3 matches >= 0.7, stop
                    break;
                }
            }
        }

//        for (Double result : results) {
//            if (result >= 0.65) {
//                resultsLayer2.add(result);
//                return results;
//            }
//        }

        return results;
    }

    public class SimilarityResult {
        private final double similarity;
        private final double[] vector;

        public SimilarityResult(double similarity, double[] vector) {
            this.similarity = similarity;
            this.vector = vector;
        }

        public double getSimilarity() {
            return similarity;
        }

        public double[] getVector() {
            return vector;
        }
    }

    public static double cosineSimilarity(double[] vector1, double[] vector2) {
        double dot = IntStream.range(0, vector1.length)
                .mapToDouble(i -> vector1[i] * vector2[i])
                .sum();

        double normA = Math.sqrt(IntStream.range(0, vector1.length)
                .mapToDouble(i -> vector1[i] * vector1[i])
                .sum());

        double normB = Math.sqrt(IntStream.range(0, vector2.length)
                .mapToDouble(i -> vector2[i] * vector2[i])
                .sum());

        return dot / (normA * normB);
    }

    public String convertToBase64(MultipartFile file) throws IOException {
        byte[] bytes = file.getBytes();
        return Base64.getEncoder().encodeToString(bytes);
    }

    public static String buildPayload(String key, String value) throws Exception {
        Map<String, String> payload = new HashMap<>();
        payload.put(key, value);
        return mapper.writeValueAsString(payload);
    }

    double[] stringToDouble(String vectorString) {
        if (vectorString == null || vectorString.isBlank()) {
            return null; // nothing to parse
        }

        try {
            String[] parts = vectorString.split(",");
            double[] vector = new double[parts.length];

            for (int i = 0; i < parts.length; i++) {
                vector[i] = Double.parseDouble(parts[i].trim());
            }
            return vector;
        } catch (NumberFormatException e) {
            // log error or handle invalid data
            System.err.println("Invalid vector string: " + vectorString);
            return null;
        }
    }

    double[] getVectorFromAPI(String json) throws Exception {
        HttpClient client = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("https://p7c5uyg59d.execute-api.ap-southeast-1.amazonaws.com/prod/image-similarity"))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(json))
                .build();

        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(response.body());
        JsonNode resultNode = root.get("result");
        double[] vector = new double[resultNode.size()];
        for (int i = 0; i < resultNode.size(); i++) {
            vector[i] = resultNode.get(i).asDouble();
        }
        return vector;
    }


    // MOST IMPORTANT: Extract dominant colors (this is the key for logo matching)
    private int[] extractDominantColors(Image image) {
        NDManager manager = NDManager.newBaseManager();

        try {
            // Small size for speed - logos don't need high resolution for color analysis
            NDArray array = image.toNDArray(manager).toType(DataType.FLOAT32, false);
            NDArray resized = fastResize(array, 32, 32, manager); // Very small for speed

            // Create color buckets (reduce color space)
            int[] colorBuckets = new int[64]; // 4x4x4 RGB buckets

            long[] shape = resized.getShape().getShape();
            long height = shape[0];
            long width = shape[1];
            long channels = shape.length > 2 ? shape[2] : 1;

            // Count colors in buckets
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int r, g, b;

                    if (channels >= 3) {
                        r = (int) (resized.getFloat(y, x, 0) / 64); // 0-3 range
                        g = (int) (resized.getFloat(y, x, 1) / 64);
                        b = (int) (resized.getFloat(y, x, 2) / 64);
                    } else {
                        // Grayscale
                        int gray = (int) (resized.getFloat(y, x) / 64);
                        r = g = b = gray;
                    }

                    // Clamp values
                    r = Math.max(0, Math.min(3, r));
                    g = Math.max(0, Math.min(3, g));
                    b = Math.max(0, Math.min(3, b));

                    int bucketIndex = r * 16 + g * 4 + b; // Convert to single index
                    colorBuckets[bucketIndex]++;
                }
            }

            return colorBuckets;

        } finally {
            manager.close();
        }
    }

    // Compare two images by their dominant colors
    private double compareByDominantColors(int[] uploadedColors, Image dbImage) {
        int[] dbColors = extractDominantColors(dbImage);

        // Calculate similarity using histogram intersection
        int intersection = 0;
        int union = 0;

        for (int i = 0; i < uploadedColors.length; i++) {
            intersection += Math.min(uploadedColors[i], dbColors[i]);
            union += Math.max(uploadedColors[i], dbColors[i]);
        }

        if (union == 0) return 0.0;

        double similarity = (double) intersection / union;

        // Boost similarity for logos with similar color distributions
        similarity = enhanceSimilarityForLogos(uploadedColors, dbColors, similarity);

        return similarity;
    }

    // Enhance similarity calculation for logo characteristics
    private double enhanceSimilarityForLogos(int[] colors1, int[] colors2, double baseSimilarity) {
        // Find top 3 dominant colors in each image
        int[] top1 = findTopColors(colors1, 3);
        int[] top2 = findTopColors(colors2, 3);

        // Check if dominant colors match
        int dominantMatches = 0;
        for (int c1 : top1) {
            for (int c2 : top2) {
                if (c1 == c2) {
                    dominantMatches++;
                    break;
                }
            }
        }

        // Boost score if dominant colors match
        double boost = dominantMatches > 0 ? 0.2 * dominantMatches : 0.0;

        return Math.min(1.0, baseSimilarity + boost);
    }

    // Find top N color buckets
    private int[] findTopColors(int[] colorBuckets, int n) {
        int[] topIndices = new int[n];
        int[] bucketsCopy = colorBuckets.clone();

        for (int i = 0; i < n; i++) {
            int maxIndex = 0;
            for (int j = 1; j < bucketsCopy.length; j++) {
                if (bucketsCopy[j] > bucketsCopy[maxIndex]) {
                    maxIndex = j;
                }
            }
            topIndices[i] = maxIndex;
            bucketsCopy[maxIndex] = -1; // Mark as used
        }

        return topIndices;
    }

    // Super fast resize - just sampling, not interpolation
    private NDArray fastResize(NDArray image, int targetWidth, int targetHeight, NDManager manager) {
        long[] shape = image.getShape().getShape();

        if (shape.length < 2) return image;

        long originalHeight = shape[0];
        long originalWidth = shape[1];
        long channels = shape.length > 2 ? shape[2] : 1;

        NDArray resized = manager.zeros(new Shape(targetHeight, targetWidth, channels), DataType.FLOAT32);

        // Simple sampling - much faster than interpolation
        for (int y = 0; y < targetHeight; y++) {
            for (int x = 0; x < targetWidth; x++) {
                int srcX = (int) ((long) x * originalWidth / targetWidth);
                int srcY = (int) ((long) y * originalHeight / targetHeight);

                srcX = Math.min(srcX, (int) originalWidth - 1);
                srcY = Math.min(srcY, (int) originalHeight - 1);

                if (channels > 1) {
                    for (int c = 0; c < channels; c++) {
                        float value = image.getFloat(srcY, srcX, c);
                        resized.setScalar(new NDIndex(y, x, c), value);
                    }
                } else {
                    float value = image.getFloat(srcY, srcX);
                    resized.setScalar(new NDIndex(y, x), value);
                }
            }
        }

        return resized;
    }
}

