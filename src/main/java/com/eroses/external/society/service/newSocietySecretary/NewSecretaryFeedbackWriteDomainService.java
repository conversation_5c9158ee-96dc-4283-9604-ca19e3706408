package com.eroses.external.society.service.newSocietySecretary;

import com.eroses.external.society.mappers.newSocietySecretary.NewSecretaryFeedbackDao;
import com.eroses.external.society.model.newSocietySecretary.NewSecretaryFeedback;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class NewSecretaryFeedbackWriteDomainService {
    private final NewSecretaryFeedbackDao newSecretaryFeedbackDao;

    public void create(NewSecretaryFeedback newSecretaryFeedback) throws Exception {
        newSecretaryFeedbackDao.create(newSecretaryFeedback);
    }
}
