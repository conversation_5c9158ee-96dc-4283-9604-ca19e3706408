package com.eroses.external.society.service.societyLiquidation;

import com.eroses.external.society.dto.response.societyLiquidation.LiquidationApprovalResponse;
import com.eroses.external.society.dto.response.societyLiquidation.LiquidationFeedbackResponse;
import com.eroses.external.society.mappers.liquidation.LiquidationFeedbackDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class LiquidationFeedbackReadDomainService {
    private final LiquidationFeedbackDao liquidationFeedbackDao;

    public List<LiquidationFeedbackResponse> getFeedbacks(Long liquidationId) {
        return liquidationFeedbackDao.findByLiquidationId(liquidationId);
    }
}
