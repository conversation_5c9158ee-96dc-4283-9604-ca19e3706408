package com.eroses.external.society.service;

import com.eroses.external.society.mappers.PrincipalSecretaryDao;
import com.eroses.external.society.model.PrincipalSecretary;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PrincipalSecretaryWriteDomainService {
    private final PrincipalSecretaryDao principalSecretaryDao;

    public boolean create(PrincipalSecretary principalSecretary) throws Exception {
        return principalSecretaryDao.create(principalSecretary);
    }

    public boolean update(PrincipalSecretary principalSecretary) throws Exception {
        return principalSecretaryDao.update(principalSecretary);
    }

    public void delete(PrincipalSecretary principalSecretary) {
        principalSecretaryDao.delete(principalSecretary);
    }

    public PrincipalSecretary updateRoDecision(PrincipalSecretary principalSecretary, int applicationStatusCode) throws Exception {

        // Update application status code and status based on RO decision
        principalSecretary.setApplicationStatusCode(applicationStatusCode);
        principalSecretaryDao.update(principalSecretary);
        return principalSecretary;
    }
}
