package com.eroses.external.society.service;

import com.eroses.external.society.mappers.RoAmendmentQueryDao;
import com.eroses.external.society.model.RoAmendmentQuery;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class RoAmendmentQueryWriteDomainService {

    private final RoAmendmentQueryDao roAmendmentQueryDao;

    public Long create(RoAmendmentQuery roAmendmentQuery) throws Exception {
        roAmendmentQueryDao.create(roAmendmentQuery);
        return roAmendmentQuery.getId();
    }

    public RoAmendmentQuery update(RoAmendmentQuery roAmendmentQuery) throws Exception {
        roAmendmentQueryDao.update(roAmendmentQuery);
        return roAmendmentQuery;
    }
}
