package com.eroses.external.society.service;

import com.eroses.external.society.api.converter.input.CommitteeDraftConverter;
import com.eroses.external.society.mappers.BranchCommitteeDao;
import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.NationalityStatusEnum;
import com.eroses.external.society.model.enums.StatusCode;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class BranchCommitteeWriteDomainService {
    private final BranchCommitteeDao branchCommitteeDao;
    private final BranchCommitteeArchiveWriteDomainService branchCommitteeArchiveWriteDomainService;
    private final BranchCommitteeArchiveReadDomainService branchCommitteeArchiveReadDomainService;
    private final CommitteeDraftConverter committeeDraftConverter;
    private final UserFacade authFacade;
    private final CommitteeDraftWriteDomainService committeeDraftWriteDomainService;
    private final CommitteeDraftReadDomainService committeeDraftReadDomainService;

    public Long create(BranchCommittee branchCommittee) throws Exception {
        branchCommitteeDao.create(branchCommittee);
        return branchCommittee.getId();
    }

    public void createByNonCitizenCommittee(NonCitizenCommittee nonCitizenCommittee, User user) throws Exception {
//        //need to check if active or archived
//        if (nonCitizenCommittee.getAppointedDate() == null) {
//            LocalDate appointedDate = nonCitizenCommittee.getAppointedDate();
//        }

        //expected to find one only
        Map<String, Object> params = new HashMap<>();
        params.put("id", nonCitizenCommittee.getActiveCommitteeId());
        params.put("branchId", nonCitizenCommittee.getBranchId());
        params.put("position", nonCitizenCommittee.getDesignationCode());
        params.put("appointedDate", nonCitizenCommittee.getAppointedDate());
        params.put("applicationStatusCode", ApplicationStatusCode.AKTIF.getCode());
        params.put("status", StatusCode.AKTIF.getCode());

        if (nonCitizenCommittee.getIsFromRegistration() != null && nonCitizenCommittee.getIsFromRegistration()) {
            params.remove("applicationStatusCode");
            params.remove("status");
        }

        //check if match with current, if not then check in draft
        BranchCommittee activeBranchCommittee = branchCommitteeDao.findOneByParams(params);
        if (activeBranchCommittee != null) {
            if (nonCitizenCommittee.getIsFromRegistration() != null && nonCitizenCommittee.getIsFromRegistration()) {
                activeBranchCommittee.setStatus(StatusCode.PADAM.getCode());
                activeBranchCommittee.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.PADAM.getCode()));
            } else {
                activeBranchCommittee.setStatus(StatusCode.TIDAK_AKTIF.getCode());
                activeBranchCommittee.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.INAKTIF.getCode()));
            }
            activeBranchCommittee.setModifiedBy(user.getId());
            branchCommitteeDao.update(activeBranchCommittee);

            BranchCommittee branchCommittee = new BranchCommittee();
            BeanUtils.copyProperties(nonCitizenCommittee, branchCommittee); //TODO: Check on the fields to populate

            branchCommittee.setCommitteeTableOldId(nonCitizenCommittee.getActiveCommitteeId());
            branchCommittee.setDesignationCode(Integer.valueOf(nonCitizenCommittee.getDesignationCode()));
            branchCommittee.setCommitteeName(nonCitizenCommittee.getName());
            branchCommittee.setIdentityType(nonCitizenCommittee.getIdentificationType());
            branchCommittee.setCommitteeIcNo(nonCitizenCommittee.getIdentificationNo());
            branchCommittee.setCommitteeCountryCode(nonCitizenCommittee.getApplicantCountryCode());
            branchCommittee.setOtherPosition(nonCitizenCommittee.getOtherDesignationCode());
            branchCommittee.setCitizenshipStatus(NationalityStatusEnum.NON_CITIZEN.getCode());

            branchCommittee.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
            branchCommittee.setStatus(StatusCode.AKTIF.getCode());
            branchCommittee.setCreatedBy(user.getId());
            branchCommitteeDao.create(branchCommittee);

            //update committeedraft as well
            params.remove("id");
            CommitteeDraft committeeDraft = committeeDraftReadDomainService.findOneByParams(params);
            if (committeeDraft != null) {
                committeeDraft.setStatus(StatusCode.TIDAK_AKTIF.getCode());
                committeeDraft.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.INAKTIF.getCode()));
                committeeDraft.setModifiedBy(user.getId());
                committeeDraftWriteDomainService.update(committeeDraft);

                CommitteeDraft updateDraft = new CommitteeDraft();
                BeanUtils.copyProperties(nonCitizenCommittee, updateDraft);

                updateDraft.setCommitteeTableOldId(nonCitizenCommittee.getActiveCommitteeId());
                updateDraft.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                updateDraft.setStatus(StatusCode.AKTIF.getCode());
                updateDraft.setNationalityStatus(NationalityStatusEnum.NON_CITIZEN.getDescriptionBm());
                updateDraft.setCreatedBy(user.getId());
                committeeDraftWriteDomainService.create(updateDraft);
            }
        } else {
            CommitteeDraft committeeDraft = committeeDraftReadDomainService.findOneByParams(params);
            if (committeeDraft != null) {
                committeeDraft.setStatus(StatusCode.TIDAK_AKTIF.getCode());
                committeeDraft.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.INAKTIF.getCode()));
                committeeDraft.setModifiedBy(user.getId());
                committeeDraftWriteDomainService.update(committeeDraft);

                CommitteeDraft updateDraft = new CommitteeDraft();
                BeanUtils.copyProperties(nonCitizenCommittee, updateDraft);

                updateDraft.setCommitteeTableOldId(nonCitizenCommittee.getActiveCommitteeId());
                updateDraft.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                updateDraft.setStatus(StatusCode.AKTIF.getCode());
                updateDraft.setNationalityStatus(NationalityStatusEnum.NON_CITIZEN.getDescriptionBm());
                updateDraft.setCreatedBy(user.getId());
                committeeDraftWriteDomainService.create(updateDraft);
            }

            params.remove("id");
            BranchCommitteeArchive activeBranchCommitteeArchive = branchCommitteeArchiveReadDomainService.findOneByParams(params);
            if (activeBranchCommitteeArchive != null) {
                activeBranchCommitteeArchive.setStatus(StatusCode.TIDAK_AKTIF.getCode());
                activeBranchCommitteeArchive.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.INAKTIF.getCode()));
                activeBranchCommitteeArchive.setModifiedBy(user.getId());
                branchCommitteeArchiveWriteDomainService.update(activeBranchCommitteeArchive);

                BranchCommitteeArchive branchCommitteeArchive = new BranchCommitteeArchive();
                BeanUtils.copyProperties(nonCitizenCommittee, branchCommitteeArchive); //Double check on this later

                branchCommitteeArchive.setCommitteeTableOldId(activeBranchCommitteeArchive.getCommitteeTableOldId());
                branchCommitteeArchive.setDesignationCode(Integer.valueOf(nonCitizenCommittee.getDesignationCode()));
                branchCommitteeArchive.setCommitteeName(nonCitizenCommittee.getName());
                branchCommitteeArchive.setIdentityType(nonCitizenCommittee.getIdentificationType());
                branchCommitteeArchive.setCommitteeIcNo(nonCitizenCommittee.getIdentificationNo());
                branchCommitteeArchive.setCommitteeCountryCode(nonCitizenCommittee.getApplicantCountryCode());
                branchCommitteeArchive.setOtherPosition(nonCitizenCommittee.getOtherDesignationCode());
                branchCommitteeArchive.setCitizenshipStatus(NationalityStatusEnum.NON_CITIZEN.getDescriptionBm());

                branchCommitteeArchive.setStatus(StatusCode.AKTIF.getCode());
                branchCommitteeArchive.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                branchCommitteeArchive.setCreatedBy(user.getId());
                branchCommitteeArchiveWriteDomainService.create(branchCommitteeArchive);
            }
        }

    }

    public void insert(List<BranchCommittee> list) {
        branchCommitteeDao.insert(list);
    }

    public Long update(BranchCommittee branchCommittee) throws Exception {
        branchCommitteeDao.update(branchCommittee);
        return branchCommittee.getId();
    }

    public void updateBlacklistStatusByIdentificationNo(String identificationNo, boolean isBlacklist) throws Exception {
        User user = authFacade.me();

        Map<String, Object> params = new HashMap<>();
        if (identificationNo != null && !identificationNo.isEmpty()) params.put("identificationNo", identificationNo);
        params.put("isBlacklist", isBlacklist);
        if (user != null) params.put("modifiedBy", user.getId());

        branchCommitteeDao.updateBlacklistStatusByIdentificationNo(params);
    }

    public Boolean updateBranchCommitteeToInactive(List<BranchCommittee> currentCommitteeList) {
        try {
            List<Long> committeeIds = currentCommitteeList.stream()
                    .map(BranchCommittee::getId)
                    .toList();
            branchCommitteeDao.updateStatus(committeeIds, StatusCode.TIDAK_AKTIF.getCode(), ApplicationStatusCode.INAKTIF.getCode());

            return true;
        } catch (Exception e) {
            log.error("Error occurred while updating branch committee to inactive: {}", e.getMessage(), e);
            return false;
        }
    }

    public Boolean createBranchCommitteeFromDraft(List<CommitteeDraft> committeeDraftList) {
        try {
            for (CommitteeDraft committeeDraft : committeeDraftList) {
                BranchCommittee committee = committeeDraftConverter.toBranchCommittee(committeeDraft);
                committee.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                committee.setStatus(StatusCode.AKTIF.getCode());
                committee.setCreatedBy(authFacade.me().getId());
                branchCommitteeDao.create(committee);

                committeeDraft.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.AKTIF.getCode()));
                committeeDraft.setStatus(StatusCode.AKTIF.getCode());
                committeeDraftWriteDomainService.update(committeeDraft);
            }

            return true;
        } catch (Exception e) {
            log.error("Error occurred while creating committee from draft: {}", e.getMessage(), e);
            return false;
        }
    }

    public Boolean updateByOldIdAndAppointedDate(BranchCommittee updateCommittee) {
        try {
            branchCommitteeDao.updateOldIdAndAppointedDate(updateCommittee);
            return true;
        } catch (Exception e) {
            log.error("Error occurred while updating committee: {}", e.getMessage(), e);
            return false;
        }
    }

    public void saveAll(List<BranchCommittee> currentCommitteeList) {
        try {
            branchCommitteeDao.saveAll(currentCommitteeList);
        } catch (Exception e) {
            log.error("Error occurred while saving committee: {}", e.getMessage(), e);
        }
    }
}
