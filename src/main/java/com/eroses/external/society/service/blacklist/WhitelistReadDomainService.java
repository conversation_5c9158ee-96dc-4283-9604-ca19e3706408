package com.eroses.external.society.service.blacklist;

import com.eroses.external.society.mappers.blacklist.WhitelistDao;
import com.eroses.external.society.model.blacklist.Whitelist;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class WhitelistReadDomainService {
    private final WhitelistDao whitelistDao;

    public Whitelist findById(Long id) {
        return whitelistDao.findById(id);
    }

    public List<Whitelist> findByBlacklistId(Long blacklistId) {
        return whitelistDao.findByBlacklistId(blacklistId);
    }

    public Whitelist findByReferenceNo(String referenceNo) {
        return whitelistDao.findByReferenceNo(referenceNo);
    }

    public List<Whitelist> search(Map<String, Object> params) {
        return whitelistDao.search(params);
    }

    public List<Whitelist> search(Long blacklistId) {
        Map<String, Object> params = new HashMap<>();
        if (blacklistId != null) params.put("blacklistId", blacklistId);
        return whitelistDao.search(params);
    }

    public Long countSearch(Map<String, Object> params) {
        return whitelistDao.countSearch(params);
    }

    public boolean isExists(Long id) {
        return findById(id) != null;
    }

    public boolean isExistsByReferenceNo(String referenceNo) {
        return findByReferenceNo(referenceNo) != null;
    }

    public List<Long> getAllPendingToProcessWhitelistId() {
        Map<String, Object> params = new HashMap<>();
        params.put("isCompleted", false);
        return whitelistDao.getAllPendingToProcessWhitelistId(params);
    }
}