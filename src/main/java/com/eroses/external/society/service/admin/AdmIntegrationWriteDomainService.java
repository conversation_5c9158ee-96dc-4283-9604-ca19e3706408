package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmIntegrationDao;
import com.eroses.external.society.model.AuditTrail;
import com.eroses.external.society.model.Integration;
import com.eroses.external.society.model.Society;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmIntegrationWriteDomainService {
    private final AdmIntegrationDao admIntegrationDao;

    public Long update(Integration integration) throws Exception {
        admIntegrationDao.update(integration);
        return integration.getId();
    }

//    public Long update(Integration integration) {
//        return admIntegrationDao.update(integration);
//    }

}
