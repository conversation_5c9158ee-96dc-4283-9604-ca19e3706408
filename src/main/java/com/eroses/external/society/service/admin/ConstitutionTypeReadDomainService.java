package com.eroses.external.society.service.admin;

import com.eroses.config.cache.CacheNames;
import com.eroses.external.society.mappers.ConstitutionTypeDao;
import com.eroses.external.society.model.ConstitutionType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class ConstitutionTypeReadDomainService {

    private final ConstitutionTypeDao constitutionTypeDao;


//    @Cacheable(value = CacheNames.CONSTITUTION_TYPES, key = "'all_constitution_types'")
    public List<ConstitutionType> getAllConstitutionType() {
        log.info("Fetching all constitution types from database (cache miss)");
        return constitutionTypeDao.findAll();
    }

//    @Cacheable(value = CacheNames.CONSTITUTION_TYPES, key = "'constitution_type_name_' + #name")
    public ConstitutionType findByName(String name) {
        log.info("Fetching constitution type by name '{}' from database (cache miss)", name);
        return constitutionTypeDao.findByName(name);
    }

//    @Cacheable(value = CacheNames.CONSTITUTION_TYPES, key = "'constitution_types_with_clause_contents'")
    public List<ConstitutionType> getAllConstitutionTypeWithClauseContent() {
        log.info("Fetching all constitution types with clause contents from database (cache miss)");
        List<ConstitutionType> constitutionTypes = constitutionTypeDao.findAll();

        // This method should be called from a service that has access to ClauseContentReadDomainService
        // For now, we'll return the basic list and let the facade handle the clause content population
        return constitutionTypes;
    }
}
