package com.eroses.external.society.service;

import com.eroses.external.society.mappers.PrincipalSecretaryDao;
import com.eroses.external.society.mappers.PrincipalSecretaryRoApprovalDao;
import com.eroses.external.society.model.Branch;
import com.eroses.external.society.model.PrincipalSecretaryRoApproval;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.SQLIntegrityConstraintViolationException;

@Slf4j
@Service
@RequiredArgsConstructor
public class PrincipalSecretaryRoApprovalWriteDomainService {
    private final PrincipalSecretaryRoApprovalDao principalSecretaryRoApprovalDao;
    private final PrincipalSecretaryDao principalSecretaryDao;

    public Long updatePrincipalSecretaryRoApproval(PrincipalSecretaryRoApproval principalSecretaryRoApproval) {

        // Validate that the PrincipalSecretary exists and the committeeId is valid
//        if (principalSecretaryRoApproval.getPrincipalSecretary() == null
//                || principalSecretaryRoApproval.getPrincipalSecretary().getCommittee() == null
//                || principalSecretaryDao.findById(
//                principalSecretaryRoApproval.getPrincipalSecretary().getCommittee().getId()) == null) {
//            throw new EntityNotFoundException(
//                    "PrincipalSecretary with committeeId " +
//                            (principalSecretaryRoApproval.getPrincipalSecretary().getCommittee() != null
//                                    ? principalSecretaryRoApproval.getPrincipalSecretary().getCommittee().getId()
//                                    : "null") + " not found."
//            );
//        }

        // Ensure the PrincipalSecretaryRoApproval entity exists before updating
        if (!principalSecretaryRoApprovalDao.existsById(principalSecretaryRoApproval.getId())) {
            throw new EntityNotFoundException(
                    "PrincipalSecretaryRoApproval with id " + principalSecretaryRoApproval.getId() + " not found."
            );
        }

        principalSecretaryRoApprovalDao.updatePrincipalSecretaryRoApproval(principalSecretaryRoApproval);
        return principalSecretaryRoApproval.getId();
    }

    public PrincipalSecretaryRoApproval updatePrincipalSecretaryApprovalDecision(PrincipalSecretaryRoApproval principalSecretaryRoApproval) {
        principalSecretaryRoApprovalDao.updatePrincipalSecretaryApprovalDecision(principalSecretaryRoApproval);
        return principalSecretaryRoApproval;
    }
}
