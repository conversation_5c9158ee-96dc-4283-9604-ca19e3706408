package com.eroses.external.society.service;

import com.eroses.external.society.mappers.EmailTemplateDao;
import com.eroses.external.society.model.EmailTemplate;
import com.eroses.external.society.model.Society;
import com.eroses.external.society.utils.Assert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmailTemplateWriteDomainService {
    private final EmailTemplateDao emailTemplateDao;

    public Long create(EmailTemplate emailTemplate) throws Exception {
        emailTemplateDao.create(emailTemplate);
        return emailTemplate.getId();
    }

    public Boolean edit(EmailTemplate emailTemplate) {
        boolean isOk = emailTemplateDao.edit(emailTemplate);
        Assert.isTrue(isOk, "Update emailTemplate is unsuccessful.");

        return isOk;
    }
}
