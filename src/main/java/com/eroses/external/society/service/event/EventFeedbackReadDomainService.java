package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.EventFeedbackDao;
import com.eroses.external.society.model.EventFeedback;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventFeedbackReadDomainService {
    private final EventFeedbackDao eventFeedbackDao;

    public List<EventFeedback> findByEventAttendeesId(Long attendeesId) {
        log.info("Finding all Event Feedback");
        return eventFeedbackDao.findByAttendeesId(attendeesId);
    }
}