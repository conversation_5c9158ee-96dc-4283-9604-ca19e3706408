package com.eroses.external.society.service.event;


import com.eroses.external.society.api.converter.output.EventApiOutputConverter;
import com.eroses.external.society.dto.response.event.EventResponse;
import com.eroses.external.society.mappers.EventDao;
import com.eroses.external.society.model.Event;
import com.eroses.external.society.model.EventAttendees;
import com.eroses.external.society.model.EventOrganiser;
import com.eroses.external.society.model.PrivateEventSociety;
import com.eroses.external.society.model.enums.EventEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.eroses.external.society.model.enums.EventEnum.EVENT_PRIVATE;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventReadDomainService {
    private final EventDao eventDao;
    private final PrivateEventSocietyReadDomainService privateEventSocietyReadDomainService;
    private final EventOrganiserReadDomainService eventOrganiserReadDomainService;
    private final EventApiOutputConverter eventApiOutputConverter;
    private final EventAttendeesReadDomainService eventAttendeesReadDomainService;

    public List<EventResponse> findAllEvent(Integer year) {
        log.info("Finding all Events");
        List<Event> events = eventDao.findAll(year);
        List<EventResponse> eventResponses = new ArrayList<>();

        for (Event event : events) {

            if (Objects.equals(event.getVisibility(), EVENT_PRIVATE)) {
                List<PrivateEventSociety> privateEventSocieties = privateEventSocietyReadDomainService.findByEventId(event.getId());
                event.setPrivateEventSocieties(privateEventSocieties);
            }
            EventResponse eventResponse = eventApiOutputConverter.convertToDto(event);
            eventResponse.setTotalRegisteredParticipants(eventAttendeesReadDomainService.countByEventId(event.getId()));
            eventResponses.add(eventResponse);

        }
        return eventResponses;
    }

    public List<EventResponse> findAllPublishedEvent(Integer year) {
        log.info("Finding all Published Events by Year {}" , year);
        List<Event> events = eventDao.findAllPublished(year);
        List<EventResponse> eventResponses = new ArrayList<>();
        for (Event event : events) {
            List<PrivateEventSociety> privateEventSocieties = privateEventSocietyReadDomainService.findByEventId(event.getId());
            event.setPrivateEventSocieties(privateEventSocieties);
            EventResponse eventResponse = eventApiOutputConverter.convertToDto(event);
            eventResponse.setTotalRegisteredParticipants(eventAttendeesReadDomainService.countByEventId(event.getId()));
            eventResponses.add(eventResponse);

        }
        return eventResponses;
    }

    public Event findOneById(Long id) {
        log.info("Finding Event by Event Id");
        Event event = eventDao.findById(id);

        List<PrivateEventSociety> privateEventSocieties = privateEventSocietyReadDomainService.findByEventId(id);
        if (privateEventSocieties != null && !privateEventSocieties.isEmpty())
            event.setPrivateEventSocieties(privateEventSocieties);


        return event;
    }

    public Event findOneByEventNo(String eventNo) {
        log.info("Finding Event by Event No");
        Event event = eventDao.findByEventNo(eventNo);
        List<EventOrganiser> eventOrganisers = eventOrganiserReadDomainService.findByEventId(event.getId());
        if (eventOrganisers != null && !eventOrganisers.isEmpty())
            event.setEventOrganisers(eventOrganisers);

        EventResponse eventResponse = new EventResponse();


        if (Objects.equals(event.getVisibility(), EVENT_PRIVATE.getDescription())) {
            log.info("Event is PRIVATE");
            List<PrivateEventSociety> privateEventSocieties = privateEventSocietyReadDomainService.findByEventId(event.getId());
            if (privateEventSocieties != null && !privateEventSocieties.isEmpty())
                event.setPrivateEventSocieties(privateEventSocieties);
        }
        return event;
    }

    public EventResponse findOneByEventNoAsResponse(String eventNo) {
        log.info("Finding Event by Event No and converting to EventResponse");
        Event event = eventDao.findByEventNo(eventNo);
        List<EventOrganiser> eventOrganisers = eventOrganiserReadDomainService.findByEventId(event.getId());
        if (eventOrganisers != null && !eventOrganisers.isEmpty())
            event.setEventOrganisers(eventOrganisers);

        if (Objects.equals(event.getVisibility(), EVENT_PRIVATE.getDescription())) {
            log.info("Event is PRIVATE");
            List<PrivateEventSociety> privateEventSocieties = privateEventSocietyReadDomainService.findByEventId(event.getId());
            if (privateEventSocieties != null && !privateEventSocieties.isEmpty())
                event.setPrivateEventSocieties(privateEventSocieties);
        }

        // Convert the Event to EventResponse using the converter
        EventResponse eventResponse = eventApiOutputConverter.convertToDto(event);
        eventResponse.setTotalRegisteredParticipants(eventAttendeesReadDomainService.countByEventId(event.getId()));
        return eventResponse;
    }

    public Long findIdByEventNo(String eventNo) {
        log.info("Finding Event Id..");
        return eventDao.findIdByEventNo(eventNo);
    }

    public List<EventResponse> findPastEvents(Integer year) {
        log.info("Finding past events");
        List<Event> events = eventDao.findPastEvents(year);
        List<EventResponse> eventResponses = new ArrayList<>();
        for (Event event : events) {
            List<PrivateEventSociety> privateEventSocieties = privateEventSocietyReadDomainService.findByEventId(event.getId());
            event.setPrivateEventSocieties(privateEventSocieties);
            EventResponse eventResponse = eventApiOutputConverter.convertToDto(event);
            eventResponse.setTotalRegisteredParticipants(eventAttendeesReadDomainService.countByEventId(event.getId()));
            eventResponses.add(eventResponse);
        }
        return eventResponses;
    }


}
