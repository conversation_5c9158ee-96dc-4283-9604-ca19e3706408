package com.eroses.external.society.service;

import com.eroses.external.society.dto.response.statement.StatementSocietyInfoGetOneResponse;
import com.eroses.external.society.mappers.StatementSocietyInfoDao;
import com.eroses.external.society.model.StatementSocietyInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatementSocietyInfoReadDomainService {

    private final StatementSocietyInfoDao statementSocietyInfoDao;

    public boolean isExists(Long id) {
        return findById(id) != null;
    }

    public StatementSocietyInfo findById(Long id) {
        return statementSocietyInfoDao.findById(id);
    }

    public List<StatementSocietyInfo> findAll(Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        params.put("offset", offset);
        params.put("limit", limit);
        return statementSocietyInfoDao.findAll(params);
    }

    public Long countFindAll() {
        return statementSocietyInfoDao.countFindAll();
    }

    public StatementSocietyInfo getStatementSocietyInfo(Long societyId, Long branchId, Long statementId) {
        Map<String, Object> params = new HashMap<>();
        if (branchId != null) {
            params.put("branchId", branchId);
        } else {
            if (societyId != null) params.put("societyId", societyId);
        }
        params.put("statementId", statementId);
        return statementSocietyInfoDao.getStatementSocietyInfo(params);
    }

    public StatementSocietyInfo findByStatementId(Map<String, Object> params) {
        return statementSocietyInfoDao.findByStatementId(params);
    }

    public StatementSocietyInfoGetOneResponse findByParam(Map<String, Object> param) {
        return statementSocietyInfoDao.findByParam(param);
    }
}