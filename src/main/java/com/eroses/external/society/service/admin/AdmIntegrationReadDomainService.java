package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmIntegrationDao;
import com.eroses.external.society.model.Integration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmIntegrationReadDomainService {
    private final AdmIntegrationDao admIntegrationDao;

    public List<Integration> getAllAdmIntegration() {
        return admIntegrationDao.findAll();
    }

}
