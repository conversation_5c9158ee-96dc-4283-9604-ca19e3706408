package com.eroses.external.society.service.societyLiquidation;

import com.eroses.external.society.dto.response.societyLiquidation.LiquidationAssetResponse;
import com.eroses.external.society.mappers.SocietyLiquidationAssetDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyLiquidationAssetReadDomainService {
    private final SocietyLiquidationAssetDao societyLiquidationAssetDao;

    public List<LiquidationAssetResponse> findByLiquidationId(Long liquidationId) {
        return societyLiquidationAssetDao.findByLiquidationId(liquidationId);
    }
}
