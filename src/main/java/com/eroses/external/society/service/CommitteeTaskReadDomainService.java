package com.eroses.external.society.service;

import com.eroses.external.society.mappers.BranchCommitteeTaskConfigDao;
import com.eroses.external.society.mappers.CommitteeTaskDao;
import com.eroses.external.society.mappers.SocietyCommitteeTaskConfigDao;
import com.eroses.external.society.model.CommitteeTask;
import com.eroses.external.society.model.enums.CommitteeTaskModule;
import com.eroses.external.society.model.enums.StatusCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommitteeTaskReadDomainService {
    private final CommitteeTaskDao committeeTaskDao;
    private final SocietyCommitteeTaskConfigDao societyCommitteeTaskConfigDao;
    private final BranchCommitteeTaskConfigDao branchCommitteeTaskConfigDao;

    public List<CommitteeTask> listCommitteeTask(Long societyId, Long branchId, String status,String module) {
        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) {
            params.put("branchId", branchId);
        }
        if (status != null) {
            params.put("status", status);
        }
        if(module!=null){
            params.put("module",module);
        }
        return committeeTaskDao.listCommitteeTask(params);
    }

    public Long countListCommitteeTask(Long societyId, Long branchId, String status) {
        Map<String, Object> params = new HashMap<>();
        params.put("societyId", societyId);
        if (branchId != null) {
            params.put("branchId", branchId);
        }
        if (status != null) {
            params.put("status", status);
        }
        return committeeTaskDao.countListCommitteeTask(params);
    }

    public CommitteeTask findById(Long id) {
        return committeeTaskDao.findById(id);
    }

    public boolean isExists(Long id) {
        return findById(id) != null;
    }

    public CommitteeTask findByStatementId(Long statementId) {
        return committeeTaskDao.findByStatementId(statementId);
    }

    public boolean isSocietyCommitteeTaskEnableForModule(Long societyId, CommitteeTaskModule module) {
        return societyCommitteeTaskConfigDao.isCommitteeTaskEnabled(societyId, module);
    }

    public boolean isBranchCommitteeTaskEnableForModule(Long branchId, CommitteeTaskModule committeeTaskModule) {
        return branchCommitteeTaskConfigDao.isCommitteeTaskEnabled(branchId, committeeTaskModule);
    }


    public CommitteeTask findByIdNoModuleAndStatus(Long societyId, Long branchId, String idNo, String module, String status) {
        Map<String, Object> params = new HashMap<>(Map.of("societyId", societyId, "status", StatusCode.AKTIF.getCode(), "identificationNo", idNo, "module", module));
        if (branchId != null) {
            params.put("branchId", branchId);
        }
        if (status != null) {
            params.put("status", status);
        }

        return committeeTaskDao.findOneByCriteria(params);
    }
}
