package com.eroses.external.society.service;

import com.eroses.external.society.mappers.SearchInformationDao;
import com.eroses.external.society.model.SearchInformation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Slf4j
@Service
@RequiredArgsConstructor
public class SearchInformationReadDomainService {
    private final SearchInformationDao searchInformationDao;

    public SearchInformation findById (Long id) {
        return searchInformationDao.findById(id);
    }

    public List<SearchInformation> getAll(Long createdBy, Integer applicationStatusCode, String searchQuery, Integer paymentYear, Integer applicationStatusCodeFilter, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of(
                "createdBy", createdBy,
                "applicationStatusCode", applicationStatusCode,
                "searchQuery", searchQuery,
                "paymentYear", paymentYear,
                "applicationStatusCodeFilter", applicationStatusCodeFilter,
                "offset", offset,
                "limit", limit
        );
        return searchInformationDao.getAll(params);
    }

    public Long countGetAll(Long createdBy, Integer applicationStatusCode, String searchQuery, Integer paymentYear, Integer applicationStatusCodeFilter) {
        Map<String, Object> params = Map.of(
                "createdBy", createdBy,
                "applicationStatusCode", applicationStatusCode,
                "searchQuery", searchQuery,
                "paymentYear", paymentYear,
                "applicationStatusCodeFilter", applicationStatusCodeFilter
        );
        return searchInformationDao.countGetAll(params);
    }

    public SearchInformation getByPaymentId(Long paymentId) {
        return searchInformationDao.getByPaymentId(paymentId);
    }
}
