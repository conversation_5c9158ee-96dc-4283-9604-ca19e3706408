package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.EventFeedbackQuestionDao;
import com.eroses.external.society.model.EventFeedbackQuestion;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventFeedbackQuestionReadDomainService {
    private final EventFeedbackQuestionDao eFeedbackQuestionDao;

    public List<EventFeedbackQuestion> findAll(){
        log.info("Finding all event feedback question");
        return eFeedbackQuestionDao.findAll();
    }

    public List<EventFeedbackQuestion> findByEventId(Long eventId) {
        log.info("Finding all event feedback by event id: {}", eventId);
        return eFeedbackQuestionDao.findByEventId(eventId);
    }

    public EventFeedbackQuestion findById(Long id){
        log.info("Finding all event feedback question by id:{}", id);
        return eFeedbackQuestionDao.findById(id);
    }
}
