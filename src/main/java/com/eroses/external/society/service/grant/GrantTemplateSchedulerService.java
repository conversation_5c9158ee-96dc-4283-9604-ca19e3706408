package com.eroses.external.society.service.grant;

import com.eroses.external.society.model.enums.GrantTemplateStatusEnum;
import com.eroses.external.society.model.grant.GrantTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for handling scheduled tasks related to grant templates.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GrantTemplateSchedulerService {
    
    private final GrantTemplateReadDomainService grantTemplateReadDomainService;
    private final GrantTemplateWriteDomainService grantTemplateWriteDomainService;
    
    /**
     * Scheduled task that runs every hour to check for grant templates that need to be published.
     * It looks for templates with a pre-publish date that has passed and publishes them.
     */
    @Scheduled(cron = "0 0 * * * *") // Run every hour
    @Transactional
    public void publishScheduledGrantTemplates() {
        log.info("Running scheduled task to publish grant templates");
        
        try {
            // Find all draft templates
            List<GrantTemplate> draftTemplates = grantTemplateReadDomainService.findAllDrafts();
            
            // Filter templates that have a pre-publish date in the past
            List<GrantTemplate> templatesToPublish = draftTemplates.stream()
                    .filter(template -> template.getPrePublishDate() != null 
                            && template.getPrePublishDate().isBefore(LocalDateTime.now()))
                    .collect(Collectors.toList());
            
            log.info("Found {} templates to publish", templatesToPublish.size());
            
            // Publish each template
            for (GrantTemplate template : templatesToPublish) {
                try {
                    boolean published = grantTemplateWriteDomainService.publishGrantTemplate(
                            template.getId(), LocalDateTime.now());
                    
                    if (published) {
                        log.info("Successfully published template with ID: {}", template.getId());
                        
                        // TODO: Send notification to relevant users about the published grant
                        // This would typically involve calling a notification service
                    } else {
                        log.error("Failed to publish template with ID: {}", template.getId());
                    }
                } catch (Exception e) {
                    log.error("Error publishing template with ID: {}", template.getId(), e);
                }
            }
        } catch (Exception e) {
            log.error("Error in scheduled task to publish grant templates", e);
        }
    }
    
    /**
     * Scheduled task that runs daily to check for grant templates that are about to be published.
     * It sends reminders for templates that will be published in the next 24 hours.
     */
    @Scheduled(cron = "0 0 12 * * *") // Run at noon every day
    public void sendPrePublishReminders() {
        log.info("Running scheduled task to send pre-publish reminders");
        
        try {
            // Find all draft templates
            List<GrantTemplate> draftTemplates = grantTemplateReadDomainService.findAllDrafts();
            
            // Filter templates that have a pre-publish date in the next 24 hours
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime tomorrow = now.plusHours(24);
            
            List<GrantTemplate> templatesForReminder = draftTemplates.stream()
                    .filter(template -> template.getPrePublishDate() != null 
                            && template.getPrePublishDate().isAfter(now)
                            && template.getPrePublishDate().isBefore(tomorrow))
                    .collect(Collectors.toList());
            
            log.info("Found {} templates for pre-publish reminders", templatesForReminder.size());
            
            // Send reminder for each template
            for (GrantTemplate template : templatesForReminder) {
                try {
                    // TODO: Send notification reminder to the template creator
                    // This would typically involve calling a notification service
                    log.info("Sending reminder for template with ID: {}", template.getId());
                } catch (Exception e) {
                    log.error("Error sending reminder for template with ID: {}", template.getId(), e);
                }
            }
        } catch (Exception e) {
            log.error("Error in scheduled task to send pre-publish reminders", e);
        }
    }
    
    /**
     * Scheduled task that runs daily to check for grant templates that have reached their end date.
     * It automatically closes these templates to prevent new applications.
     */
    @Scheduled(cron = "0 0 0 * * *") // Run at midnight every day
    @Transactional
    public void closeExpiredGrantTemplates() {
        log.info("Running scheduled task to close expired grant templates");
        
        try {
            // Find all published templates
            List<GrantTemplate> publishedTemplates = grantTemplateReadDomainService.findAllPublished();
            
            // Filter templates that have an end date in the past
            List<GrantTemplate> templatesToClose = publishedTemplates.stream()
                    .filter(template -> template.getEndDate() != null 
                            && template.getEndDate().isBefore(LocalDateTime.now()))
                    .collect(Collectors.toList());
            
            log.info("Found {} templates to close", templatesToClose.size());
            
            // Close each template (could implement a specific "CLOSED" status if needed)
            for (GrantTemplate template : templatesToClose) {
                try {
                    // TODO: Implement logic to close the template or mark it as expired
                    // This might involve setting a new status or updating the template in some way
                    log.info("Closing template with ID: {}", template.getId());
                    
                    // TODO: Send notification to relevant users about the closed grant
                } catch (Exception e) {
                    log.error("Error closing template with ID: {}", template.getId(), e);
                }
            }
        } catch (Exception e) {
            log.error("Error in scheduled task to close expired grant templates", e);
        }
    }
}
