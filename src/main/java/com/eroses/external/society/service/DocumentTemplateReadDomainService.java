package com.eroses.external.society.service;

import com.eroses.external.society.mappers.DocumentTemplateDao;
import com.eroses.external.society.model.DocumentTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentTemplateReadDomainService {
    private final DocumentTemplateDao documentTemplateDao;

    public DocumentTemplate getDocumentTemplate(Long id) {
        return documentTemplateDao.findById(id);
    }

    public DocumentTemplate getDocumentTemplateByCode(String templateCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", templateCode);
        return documentTemplateDao.findByCode(params);
    }
}
