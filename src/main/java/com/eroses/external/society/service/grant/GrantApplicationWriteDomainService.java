package com.eroses.external.society.service.grant;

import com.eroses.external.society.mappers.grant.GrantApplicationDao;
import com.eroses.external.society.mappers.grant.GrantApplicationFieldValueDao;
import com.eroses.external.society.model.enums.GrantApplicationStatusEnum;
import com.eroses.external.society.model.grant.GrantApplication;
import com.eroses.external.society.model.grant.GrantApplicationFieldValue;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class GrantApplicationWriteDomainService {

    private final GrantApplicationDao grantApplicationDao;
    private final GrantApplicationFieldValueDao grantApplicationFieldValueDao;

    @Transactional
    public Long createGrantApplication(GrantApplication grantApplication) throws Exception {
        grantApplicationDao.create(grantApplication);
        return grantApplication.getId();
    }

    @Transactional
    public boolean updateGrantApplication(GrantApplication grantApplication) throws Exception {
        return grantApplicationDao.update(grantApplication);
    }

    @Transactional
    public Long createGrantApplicationFieldValue(GrantApplicationFieldValue grantApplicationFieldValue) throws Exception {
        grantApplicationFieldValueDao.create(grantApplicationFieldValue);
        return grantApplicationFieldValue.getId();
    }

    @Transactional
    public boolean updateGrantApplicationFieldValue(GrantApplicationFieldValue grantApplicationFieldValue) throws Exception {
        return grantApplicationFieldValueDao.update(grantApplicationFieldValue);
    }

    @Transactional
    public void deleteGrantApplicationFieldValuesByApplicationId(Long grantApplicationId) throws Exception {
        grantApplicationFieldValueDao.deleteByGrantApplicationId(grantApplicationId);
    }

    @Transactional
    public boolean submitGrantApplication(Long id, Long userId) throws Exception {
        GrantApplication grantApplication = grantApplicationDao.findById(id);
        if (grantApplication == null) {
            return false;
        }

        grantApplication.setStatus(GrantApplicationStatusEnum.SUBMITTED.name());
        grantApplication.setSubmissionDate(LocalDateTime.now());
        grantApplication.setModifiedBy(userId);
        grantApplication.setModifiedDate(LocalDateTime.now());

        return grantApplicationDao.update(grantApplication);
    }

    @Transactional
    public boolean updateGrantApplicationStatus(Long id, GrantApplicationStatusEnum status, Long userId) throws Exception {
        GrantApplication grantApplication = grantApplicationDao.findById(id);
        if (grantApplication == null) {
            return false;
        }

        grantApplication.setStatus(status.name());
        grantApplication.setModifiedBy(userId);
        grantApplication.setModifiedDate(LocalDateTime.now());

        return grantApplicationDao.update(grantApplication);
    }

    @Transactional
    public void deleteGrantApplication(Long id) throws Exception {
        // First delete all field values
        deleteGrantApplicationFieldValuesByApplicationId(id);

        // Then delete the application itself
        grantApplicationDao.delete(id);
    }
}
