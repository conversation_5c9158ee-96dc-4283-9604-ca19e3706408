package com.eroses.external.society.service;

import com.eroses.external.society.model.Example;
import com.eroses.external.society.model.ExampleBO;
import com.eroses.external.society.mappers.ExampleDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
//@RequiredArgsConstructor
public class ExampleWriteDomainService {
    @Autowired
    private ExampleDao exampleDao;

//    public ExampleWriteDomainService(ExampleDao exampleDao) {
//        this.exampleDao = exampleDao;
//    }

    public Long create(ExampleBO exampleBO) {
        Example example = exampleBO.getExample();
        exampleDao.create(example);
        return example.getId();
    }
}

