package com.eroses.external.society.service.document.strategy.impl;

import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.*;
import com.eroses.external.society.model.lookup.AdmInsolvencyDepartment;
import com.eroses.external.society.service.*;
import com.eroses.external.society.service.admin.AdmAddressesReadDomainService;
import com.eroses.external.society.service.admin.AdmInsolvencyDepartmentReadDomainService;
import com.eroses.external.society.service.document.strategy.AbstractDocumentMappingStrategy;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.service.utils.HtmlGeneratorService;
import com.eroses.external.society.utils.RoDecisionUtilities;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Strategy implementation for Surat Insolvensi document type.
 */
@Slf4j
@Component
public class SuratInsolvensiCawanganStrategy extends AbstractDocumentMappingStrategy {

    private final AdmInsolvencyDepartmentReadDomainService admInsolvencyDepartmentReadDomainService;
    private final AdmAddressesReadDomainService admAddressesReadDomainService;
    private final DocumentReadDomainService documentReadDomainService;
    private final RoDecisionUtilities roDecisionUtilities;
    private final CommitteeReadDomainService committeeReadDomainService;

    public SuratInsolvensiCawanganStrategy(
            DocumentTemplateReadDomainService documentTemplateReadDomainService,
            S3DomainService s3DomainService,
            PdfService pdfService,
            HtmlGeneratorService htmlGeneratorService,
            UserFacade userFacade,
            SocietyReadDomainService societyReadDomainService,
            AdmInsolvencyDepartmentReadDomainService admInsolvencyDepartmentReadDomainService,
            AdmAddressesReadDomainService admAddressesReadDomainService,
            DocumentReadDomainService documentReadDomainService,
            RoDecisionUtilities roDecisionUtilities,
            CommitteeReadDomainService committeeReadDomainService) {
        super(documentTemplateReadDomainService, s3DomainService, pdfService, htmlGeneratorService, userFacade, societyReadDomainService);
        this.admAddressesReadDomainService = admAddressesReadDomainService;
        this.admInsolvencyDepartmentReadDomainService = admInsolvencyDepartmentReadDomainService;
        this.documentReadDomainService = documentReadDomainService;
        this.roDecisionUtilities = roDecisionUtilities;
        this.committeeReadDomainService = committeeReadDomainService;
    }

    @Override
    public String mapDataToDocument(String templateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        DocumentTemplate template = getDocumentTemplate(templateCode);
        String htmlTemplate = template.getHtmlContent();

        Map<String, String> mapFields = prepareDataForMapping(societyId, additionalParams);
        String processedHtml = htmlGeneratorService.processConditionals(htmlTemplate, mapFields);
        return htmlGeneratorService.mapToHtml(processedHtml, mapFields);
    }

    @Override
    protected Map<String, String> prepareDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {

        Society society = societyReadDomainService.findById(societyId);
        SocietyCategoryEnum societyCategory = SocietyCategoryEnum.getCategoryById(Integer.valueOf(society.getCategoryCodeJppm()));
        LocalDate cancellationDate = (LocalDate) additionalParams.get("cancellationDate");
        SocietyCancellationDetailSectionEnum sectionEnum = (SocietyCancellationDetailSectionEnum) additionalParams.get("sectionEnum");

        Branch branch;
        if (additionalParams.get("branch") instanceof Branch) {
            branch = (Branch) additionalParams.get("branch");
        } else {
            throw new IllegalArgumentException("Branch information is required for Surat Insolvensi Cawangan.");
        }

        String stateCode = roDecisionUtilities.getAssignedResponsibleStateCode(society.getStateCode(), societyCategory);
        AdmInsolvencyDepartment admInsolvencyDepartment = admInsolvencyDepartmentReadDomainService.findByStateCode(stateCode);
        AdmAddresses branchState = admAddressesReadDomainService.findById(Long.valueOf(branch.getStateCode()));
        AdmAddresses admInsolvencyDepartmentState = admAddressesReadDomainService.findById(Long.valueOf(admInsolvencyDepartment.getStateCode()));

        //Get latest committee update year
        List<String> committeeDesignationCodes = Position.committeePositionCodes();
        List<Committee> committees = committeeReadDomainService.findActiveCommitteesInSocietyWithRoles(
                committeeDesignationCodes,
                societyId);
        LocalDate latestCommitteeUpdateDate = getLatestCommitteeUpdateDate(committees);

        //Get latest statement date
        LocalDate latestStatementDate = null;
        List<Document> statementDocuments = documentReadDomainService.findByDocumentTypeAndSocietyId(DocumentTypeEnum.STATEMENT.getType(), societyId);
        if (statementDocuments != null && !statementDocuments.isEmpty()) {
            //Stream to find the latest created date
            latestStatementDate = LocalDate.from(statementDocuments.stream()
                    .map(Document::getCreatedDate)
                    .max(Comparator.naturalOrder())
                    .orElse(null));
        }

        //Get State Director of the state (Should have only 1 state director per state)
        List<User> stateDirectors = roDecisionUtilities.getAssignedApprovalOfficer(UserRoleEnum.PENOLONG_PENDAFTAR_PERTUBUHAN, society.getStateCode(), societyCategory);
        String directorName = stateDirectors.isEmpty() ? "-" : stateDirectors.getFirst().getName();

        Map<String, String> mapFields;
        mapFields = getMapFields(branch, cancellationDate, sectionEnum, admInsolvencyDepartment, branchState,
                admInsolvencyDepartmentState, latestStatementDate, latestCommitteeUpdateDate, directorName);
        return mapLetterHeader(mapFields, society.getStateCode(), societyCategory);
    }

    @Override
    public boolean canHandle(String templateCode) {
        return DocumentTemplateEnum.SURAT_INSOLVENSI_CAWANGAN.getCode().equals(templateCode);
    }

    private static Map<String, String> getMapFields(Branch branch, LocalDate cancellationDate,
                                                    SocietyCancellationDetailSectionEnum sectionEnum,
                                                    AdmInsolvencyDepartment admInsolvencyDepartment,
                                                    AdmAddresses branchState,
                                                    AdmAddresses admInsolvencyDepartmentState,
                                                    LocalDate latestStatementDate,
                                                    LocalDate latestCommitteeUpdateDate,
                                                    String directorName) {
        Map<String, String> mapFields = new HashMap<>();
        mapFields.put("{{BRANCH_NO}}", getValueOrDefault(branch.getBranchNo()).toUpperCase());
        mapFields.put("{{CANCELLATION_DATE}}", formatToMalayDate(cancellationDate));
        mapFields.put("{{INSOLVENCY_DEPARTMENT_NAME}}", getValueOrDefault(admInsolvencyDepartment.getName()));
        mapFields.put("{{INSOLVENCY_DEPARTMENT_ADDRESS}}", getValueOrDefault(admInsolvencyDepartment.getAddress()));
        mapFields.put("{{INSOLVENCY_DEPARTMENT_POSTCODE}}", getValueOrDefault(String.valueOf(admInsolvencyDepartment.getPostcode())));
        mapFields.put("{{INSOLVENCY_DEPARTMENT_CITY}}", getValueOrDefault(admInsolvencyDepartment.getCityCode()));
        mapFields.put("{{INSOLVENCY_DEPARTMENT_STATE_NAME}}", getValueOrDefault(admInsolvencyDepartmentState.getName().toUpperCase()));
        mapFields.put("{{BRANCH_NAME}}", getValueOrDefault(branch.getName()).toUpperCase());
        mapFields.put("{{BRANCH_ADDRESS}}", getValueOrDefault(branch.getAddress()).toUpperCase());
        mapFields.put("{{BRANCH_POSTCODE}}", getValueOrDefault(branch.getPostcode()));
        mapFields.put("{{BRANCH_CITY}}", getValueOrDefault(branch.getCity()));
        mapFields.put("{{BRANCH_STATE_NAME}}", getValueOrDefault(branchState.getName().toUpperCase()));
        mapFields.put("{{SECTION}}", getValueOrDefault(sectionEnum.getDescription()));
        mapFields.put("{{LATEST_COMMITTEE_YEAR}}", latestCommitteeUpdateDate.getYear() + "");
        mapFields.put("{{LATEST_STATEMENT_DATE}}", formatDateOrDefault(latestStatementDate, "-"));
        mapFields.put("{{OFFICER_NAME}}", getValueOrDefault(directorName));
        return mapFields;
    }

    private static LocalDate getLatestCommitteeUpdateDate(List<Committee> committees) {
        Optional<LocalDateTime> latestModifiedDate = committees.stream()
                .filter(committee -> {
                    Position position = Position.getRole(Integer.parseInt(committee.getDesignationCode()));
                    return position != null;
                })
                .map(Committee::getModifiedDate) // Extract the LocalDateTime
                .max(LocalDateTime::compareTo); // Find the maximum (latest) date

        return latestModifiedDate.map(LocalDateTime::toLocalDate).orElse(null);
    }

    private Map<String, String> mapLetterHeader(Map<String, String> mapFields, String stateCode, SocietyCategoryEnum societyCategory) throws Exception {
        // Null or empty check
        if (stateCode == null || stateCode.isEmpty()) {
            return mapFields;
        }

        // Letter Header is based on the state who will be handled the society
        String letterHeaderStateCode = roDecisionUtilities.getAssignedResponsibleStateCode(stateCode, societyCategory);
        mapFields.put("{{IS_" + letterHeaderStateCode  + "_STATE}}", "true");
        return mapFields;
    }
}