package com.eroses.external.society.service.admin;

import com.eroses.config.cache.CacheNames;
import com.eroses.external.society.model.AdmAddresses;
import com.eroses.external.society.mappers.AdmAddressesDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmAddressesWriteDomainService {
    private final AdmAddressesDao admAddressesDao;

    @CacheEvict(value = CacheNames.ADDRESSES, allEntries = true)
    public Long createCountry(AdmAddresses address) throws Exception {
        log.info("Creating country - evicting addresses cache");
        address.setPid(0L);
        address.setLevel(0L);
        admAddressesDao.create(address);
        return address.getId();
    }

    @CacheEvict(value = CacheNames.ADDRESSES, allEntries = true)
    public Long createMalaysiaState(AdmAddresses address) throws Exception {
        log.info("Creating Malaysia state - evicting addresses cache");
        address.setPid(152L); // Set parent ID to 152 (Malaysia)
        address.setLevel(1L); // Set level to 1 for state
        admAddressesDao.create(address);
        return address.getId();
    }

    @CacheEvict(value = CacheNames.ADDRESSES, allEntries = true)
    public Long createDistrict(AdmAddresses address) throws Exception {
        log.info("Creating district - evicting addresses cache");
        address.setLevel(2L); // Set level to 2 for district
        admAddressesDao.create(address);
        return address.getId();
    }

    @CacheEvict(value = CacheNames.ADDRESSES, allEntries = true)
    public void update(AdmAddresses address) throws Exception {
        log.info("Updating address with id {} - evicting addresses cache", address.getId());
        admAddressesDao.update(address);
    }

    @CacheEvict(value = CacheNames.ADDRESSES, allEntries = true)
    public boolean delete(Long id) {
        log.info("Deleting address with id {} - evicting addresses cache", id);
        return admAddressesDao.delete(id);
    }
}
