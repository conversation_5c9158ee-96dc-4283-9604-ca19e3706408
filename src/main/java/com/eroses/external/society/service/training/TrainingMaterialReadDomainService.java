package com.eroses.external.society.service.training;

import com.eroses.external.society.mappers.TrainingMaterialDao;
import com.eroses.external.society.model.TrainingMaterial;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class TrainingMaterialReadDomainService {
    
    private final TrainingMaterialDao trainingMaterialDao;
    
    public TrainingMaterial getTrainingMaterialById(Long id) {
        return trainingMaterialDao.findById(id);
    }
    
    public List<TrainingMaterial> getAllTrainingMaterialsByCourseId(Long trainingCourseId) {
        return trainingMaterialDao.findAllByCourseId(trainingCourseId);
    }
    
    public List<TrainingMaterial> getAllTrainingMaterialsByCourseIdAndType(Long trainingCourseId, String materialType) {
        return trainingMaterialDao.findAllByCourseIdAndType(trainingCourseId, materialType);
    }
}
