package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.EventOrganiserDao;
import com.eroses.external.society.model.EventOrganiser;
import com.eroses.external.society.model.Organiser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventOrganiserReadDomainService {
    private final EventOrganiserDao eoDao;

    public List<EventOrganiser> findAll(){
        log.info("Finding all Event Organiser");
        return eoDao.findAll();
    }

    public List<EventOrganiser> findByEventId(Long eventId){
        log.info("Finding Event Organiser by Event id: {}", eventId);
        return  eoDao.findByEventId(eventId);
    }
    public List<Organiser> findByEventIdJoinTable(Long eventId){
        log.info("Finding Event Organiser by Event id: {}", eventId);
        return  eoDao.findByEventIdJointTable(eventId);
    }
    public List<EventOrganiser> findByOrganiserId(Long orgId){
        log.info("Finding all by Organiser id: {}", orgId);
        return eoDao.findByOrganiserId(orgId);
    }
    public EventOrganiser create(EventOrganiser eventOrganiser){
        log.info("Creating Event Organise {}", eventOrganiser);
        int eo = eoDao.create(eventOrganiser);
        if(eo > 0){
            return eventOrganiser;
        }else{
            return null;
        }
    }

    public EventOrganiser findOneByOrgIdAndEventId(Long orgId, Long eventId){
        log.info("finding Event Organiser by Organiser id [{}] and Event id [{}]", orgId, eventId);
        return eoDao.findOneByOrgIdAndEventId(orgId, eventId);
    }

}
