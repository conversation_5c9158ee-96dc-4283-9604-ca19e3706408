package com.eroses.external.society.service;

import com.eroses.external.society.mappers.CommitteeDraftDao;
import com.eroses.external.society.model.CommitteeDraft;
import com.eroses.user.api.facade.UserFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommitteeDraftWriteDomainService {
    private final CommitteeDraftDao committeeDraftDao;
    private final UserFacade userFacade;

    public Boolean create(CommitteeDraft committeeDraft) throws Exception {
        committeeDraft.setCreatedBy(userFacade.me().getId());
        committeeDraft.setCreatedDate(LocalDateTime.now());

        return committeeDraftDao.create(committeeDraft);
    }

    public Boolean update(CommitteeDraft committeeDraft) throws Exception {
        committeeDraft.setModifiedBy(userFacade.me().getId());
        committeeDraft.setModifiedDate(LocalDateTime.now());

        return committeeDraftDao.update(committeeDraft);
    }
}
