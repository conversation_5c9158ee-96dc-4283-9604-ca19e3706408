package com.eroses.external.society.service;

import com.eroses.external.society.mappers.EmailTemplateDao;
import com.eroses.external.society.model.EmailTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class EmailTemplateReadDomainService {
    private final EmailTemplateDao emailTemplateDao;

    public List<EmailTemplate> getAll() {
        return emailTemplateDao.getAll();
    }

    public Boolean isExists(Long id) {
        EmailTemplate emailTemplate = emailTemplateDao.findById(id);
        return emailTemplate != null;
    }

    public EmailTemplate findByTemplateCode(String templateCode) {
        return emailTemplateDao.findByTemplateCode(templateCode);
    }
}
