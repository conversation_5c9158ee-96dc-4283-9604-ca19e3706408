package com.eroses.external.society.service;

import com.eroses.external.society.mappers.ForbiddenKeywordDao;
import com.eroses.external.society.mappers.ForbiddenLogoDao;
import com.eroses.external.society.model.ForbiddenKeyword;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class ForbiddenKeywordReadDomainService {
    private final ForbiddenKeywordDao forbiddenDao;
    private final ForbiddenLogoDao forbiddenLogoDao;

    public List<ForbiddenKeyword> findAll() {
        log.info("Finding all forbidden keywords");
        return forbiddenDao.findAll();
    }

    public List<ForbiddenKeyword> findByForbiddenType(String type) {
        log.info("Finding forbidden keywords by forbidden type: {}", type);
        return forbiddenDao.findByType(type);
    }
    public List<ForbiddenKeyword> findByForbiddenTypePagination(String type, Integer offset, Integer limit) {
        log.info("Finding forbidden keywords by forbidden type: {}", type);
        return forbiddenDao.findByTypePagination(type, offset, limit);
    }

    public Long countByForbiddenType(String type) {
        log.info("Counting forbidden keywords by forbidden type: {}", type);
        return forbiddenDao.countByForbiddenType(type);
    }

    public List<String> findOnlyKeywordByForbiddenType(String type) {
        log.info("Finding forbidden keywords by forbidden type: {}", type);
        return forbiddenDao.findOnlyKeywordByType(type);
    }

    public ForbiddenKeyword findSenaraiById(Long id) {
        log.info("Finding forbidden keyword by id: {}", id);
        return forbiddenDao.findById(id);
    }

    public List<ForbiddenKeyword> checkLarangan(String keyword, String laranganType) {
        log.info("Checking {} for keyword: {}", laranganType, keyword);
        return forbiddenDao.checkKeyLarangan(keyword, laranganType);
    }

    public List<ForbiddenKeyword> searchLaranganOrLaranganKelabu(String keyword, String forbiddenType, Integer offset, Integer limit, String statusCode, Boolean activeStatus) {
        log.info("Searching type {} for keyword: {}", forbiddenType, keyword);
        Map<String, Object> params = new HashMap<>();
        params.put("searchQuery", keyword);
        params.put("forbiddenType", forbiddenType);
        params.put("offset", offset);
        params.put("limit", limit);
        params.put("statusCode", statusCode);
        params.put("activeStatus", activeStatus);
        return forbiddenDao.searchLaranganOrKelabu(params);
    }

    public Long countSearchedLarangans(String keyword, String statusCode, Boolean activeStatus, String forbiddenType) {
        log.info("Counting searched larangans for keyword: {}", keyword);
        Map<String, Object> params = new HashMap<>();
        params.put("searchQuery", keyword);
        params.put("statusCode", statusCode);
        params.put("activeStatus", activeStatus);
        params.put("forbiddenType", forbiddenType);
        return forbiddenDao.countSearchedForbiddens(params);
    }


}