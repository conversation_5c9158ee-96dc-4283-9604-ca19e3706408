package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.EventAdminDao;
import com.eroses.external.society.model.EventAdmin;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventAdminReadDomainService {
    private final EventAdminDao adminDao;

    public EventAdmin findById(Long id) {
        return adminDao.findById(id);
    }
    public List<EventAdmin> findAllEventAdmin() {
        return adminDao.findAll();
    }
    public EventAdmin findEventAdminByIdentificationNo(String identificationNo ){
        log.info("Finding Event Admin by Id No : {}", identificationNo);
        EventAdmin eventAmdin = adminDao.findByIdentificationNo(identificationNo);
        return eventAmdin;
    }

}
