package com.eroses.external.society.service.societyLiquidation;

import com.eroses.external.society.mappers.SocietyLiquidationDao;
import com.eroses.external.society.model.societyLiquidation.SocietyLiquidation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyLiquidationReadDomainService {
    private final SocietyLiquidationDao societyLiquidationDao;

    public List<SocietyLiquidation> findAllByCriteria(
            Long societyId, Long branchId, Integer createdYear, Integer submissionYear, Integer decisionYear,
            Integer applicationStatusCode, String applicantName, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null && branchId != 0) params.put("branchId", branchId);
        if (createdYear != null) params.put("createdYear", createdYear);
        if (submissionYear != null) params.put("submissionYear", submissionYear);
        if (decisionYear != null) params.put("decisionYear", decisionYear);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);
        if (applicantName != null && !applicantName.isEmpty()) params.put("applicantName", applicantName);
        if (offset != null) params.put("offset", offset);
        if (limit != null) params.put("limit", limit);
        return societyLiquidationDao.findAllByCriteria(params);
    }

    public Long countAllByCriteria( Long societyId, Long branchId, Integer createdYear, Integer submissionYear,
                                    Integer decisionYear, Integer applicationStatusCode, String applicantName) {
        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        if (createdYear != null) params.put("createdYear", createdYear);
        if (submissionYear != null) params.put("submissionYear", submissionYear);
        if (decisionYear != null) params.put("decisionYear", decisionYear);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);
        if (applicantName != null && !applicantName.isEmpty()) params.put("applicantName", applicantName);
        return societyLiquidationDao.countAllByCriteria(params);
    }

    public SocietyLiquidation findById(Long id) {
        return societyLiquidationDao.findById(id);
    }

    public SocietyLiquidation getById(Long id) {
        return societyLiquidationDao.getById(id);
    }

    public List<SocietyLiquidation> getAllPendingSocietyLiquidationByCriteria(
            List<Integer> applicationStatusCodes, String stateCode, Long roId, String categoryCode,
            String subCategoryCode, String societyName, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (roId != null) params.put("ro", roId);
        if (categoryCode != null && !categoryCode.isEmpty()) params.put("categoryCode", categoryCode);
        if (subCategoryCode != null && !subCategoryCode.isEmpty()) params.put("subCategoryCode", subCategoryCode);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (offset != null) params.put("offset", offset);
        if (limit != null) params.put("limit", limit);
        return societyLiquidationDao.getAllPendingSocietyLiquidationByCriteria(params);
    }

    public Long countAllPendingSocietyLiquidationByCriteria(
            List<Integer> applicationStatusCodes, String stateCode, Long roId, String categoryCode,
            String subCategoryCode, String societyName) {
        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (roId != null) params.put("ro", roId);
        if (categoryCode != null && !categoryCode.isEmpty()) params.put("categoryCode", categoryCode);
        if (subCategoryCode != null && !subCategoryCode.isEmpty()) params.put("subCategoryCode", subCategoryCode);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        return societyLiquidationDao.countAllPendingSocietyLiquidationByCriteria(params);
    }

    public List<SocietyLiquidation> getAllPendingBranchLiquidationByCriteria(
            List<Integer> applicationStatusCodes, String stateCode, Long roId, String categoryCode,
            String subCategoryCode, String societyName, String branchSearchQuery, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (roId != null) params.put("ro", roId);
        if (categoryCode != null && !categoryCode.isEmpty()) params.put("categoryCode", categoryCode);
        if (subCategoryCode != null && !subCategoryCode.isEmpty()) params.put("subCategoryCode", subCategoryCode);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (branchSearchQuery != null && !branchSearchQuery.isEmpty()) params.put("branchSearchQuery", branchSearchQuery);
        if (offset != null) params.put("offset", offset);
        if (limit != null) params.put("limit", limit);
        return societyLiquidationDao.getAllPendingBranchLiquidationByCriteria(params);
    }

    public Long countAllPendingBranchLiquidationByCriteria(
            List<Integer> applicationStatusCodes, String stateCode, Long roId, String categoryCode,
            String subCategoryCode, String societyName, String branchSearchQuery) {
        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (roId != null) params.put("ro", roId);
        if (categoryCode != null && !categoryCode.isEmpty()) params.put("categoryCode", categoryCode);
        if (subCategoryCode != null && !subCategoryCode.isEmpty()) params.put("subCategoryCode", subCategoryCode);
        if (societyName != null && !societyName.isEmpty()) params.put("societyName", societyName);
        if (branchSearchQuery != null && !branchSearchQuery.isEmpty()) params.put("branchSearchQuery", branchSearchQuery);
        return societyLiquidationDao.countAllPendingBranchLiquidationByCriteria(params);
    }

    public List<String> findDistinctCreatedYears(Long societyId, Long branchId) {
        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        return societyLiquidationDao.findDistinctCreatedYears(params);
    }

    public List<String> findDistinctSubmissionYears(Long societyId, Long branchId) {
        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        return societyLiquidationDao.findDistinctSubmissionYears(params);
    }

    public List<String> findDistinctDecisionYears(Long societyId, Long branchId) {
        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        return societyLiquidationDao.findDistinctDecisionYears(params);
    }
}
