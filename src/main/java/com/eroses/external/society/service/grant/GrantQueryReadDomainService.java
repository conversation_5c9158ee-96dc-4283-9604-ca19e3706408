package com.eroses.external.society.service.grant;

import com.eroses.external.society.mappers.grant.GrantQueryDao;
import com.eroses.external.society.model.grant.GrantQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class GrantQueryReadDomainService {
    
    private final GrantQueryDao grantQueryDao;
    
    public List<GrantQuery> findByGrantApplicationId(Long grantApplicationId) {
        return grantQueryDao.findByGrantApplicationId(grantApplicationId);
    }
    
    public GrantQuery findById(Long id) {
        return grantQueryDao.findById(id);
    }
    
    public List<GrantQuery> findPendingQueries() {
        return grantQueryDao.findPendingQueries();
    }
}
