package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmBranchDao;
import com.eroses.external.society.model.AdmBranch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmBranchWriteDomainService {
    private final AdmBranchDao admBranchDao;

    public Long create(AdmBranch branch) throws Exception {
        admBranchDao.create(branch);
        return branch.getId();
    }

    public boolean update(AdmBranch branch) throws Exception {
        return admBranchDao.update(branch);
    }

    public boolean delete(Long id) throws Exception {
        return admBranchDao.delete(id);
    }
}