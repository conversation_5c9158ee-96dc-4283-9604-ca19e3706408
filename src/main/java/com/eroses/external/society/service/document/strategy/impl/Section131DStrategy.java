package com.eroses.external.society.service.document.strategy.impl;

import com.eroses.external.society.model.BranchCommittee;
import com.eroses.external.society.model.Committee;
import com.eroses.external.society.model.DocumentTemplate;
import com.eroses.external.society.model.Society;
import com.eroses.external.society.model.enums.DocumentTemplateEnum;
import com.eroses.external.society.model.enums.Position;
import com.eroses.external.society.model.enums.SocietyCancellationDetailSectionEnum;
import com.eroses.external.society.model.lookup.AdmInsolvencyDepartment;
import com.eroses.external.society.model.societyCancellation.SocietyCancellation;
import com.eroses.external.society.service.*;
import com.eroses.external.society.service.admin.AdmInsolvencyDepartmentReadDomainService;
import com.eroses.external.society.service.document.strategy.AbstractDocumentMappingStrategy;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.service.utils.HtmlGeneratorService;
import com.eroses.user.api.facade.UserFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.format.TextStyle;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Strategy implementation for Section 13(1)(d) document type.
 */
@Slf4j
@Component
public class Section131DStrategy extends AbstractDocumentMappingStrategy {

    private final CommitteeReadDomainService committeeReadDomainService;
    private final BranchCommitteeReadDomainService branchCommitteeReadDomainService;
    private final AdmInsolvencyDepartmentReadDomainService admInsolvencyDepartmentReadDomainService;

    public Section131DStrategy(
            DocumentTemplateReadDomainService documentTemplateReadDomainService,
            S3DomainService s3DomainService,
            PdfService pdfService,
            HtmlGeneratorService htmlGeneratorService,
            UserFacade userFacade,
            SocietyReadDomainService societyReadDomainService,
            CommitteeReadDomainService committeeReadDomainService,
            BranchCommitteeReadDomainService branchCommitteeReadDomainService,
            AdmInsolvencyDepartmentReadDomainService admInsolvencyDepartmentReadDomainService) {
        super(documentTemplateReadDomainService, s3DomainService, pdfService, htmlGeneratorService, userFacade, societyReadDomainService);
        this.committeeReadDomainService = committeeReadDomainService;
        this.branchCommitteeReadDomainService = branchCommitteeReadDomainService;
        this.admInsolvencyDepartmentReadDomainService = admInsolvencyDepartmentReadDomainService;
    }

    @Override
    public String mapDataToDocument(String templateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        DocumentTemplate template = getDocumentTemplate(templateCode);
        String htmlTemplate = template.getHtmlContent();

        Map<String, String> mapFields = prepareDataForMapping(societyId, additionalParams);
        return htmlGeneratorService.mapToHtml(htmlTemplate, mapFields);
    }

    @Override
    protected Map<String, String> prepareDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {

        Society society = societyReadDomainService.findById(societyId);
        SocietyCancellation societyCancellation = (SocietyCancellation) additionalParams.get("societyCancellation");
        boolean isBranchCancellation = (boolean) additionalParams.get("isBranchCancellation");

        String secretaryAddress = null;
        String presidentAddress = null;
        String treasurerAddress = null;
        if (isBranchCancellation) {
            BranchCommittee secretary = branchCommitteeReadDomainService.findActiveCommitteeInBranchWithRoles(Position.getAllSecretaryPositions().stream().map(Position::getCode).map(String::valueOf).toList(), societyCancellation.getBranchId());
            BranchCommittee president = branchCommitteeReadDomainService.findActiveCommitteeInBranchWithRoles(Position.getAllPresidentPositions().stream().map(Position::getCode).map(String::valueOf).toList(), societyCancellation.getBranchId());
            BranchCommittee treasurer = branchCommitteeReadDomainService.findActiveCommitteeInBranchWithRoles(Position.getAllTreasurerPositions().stream().map(Position::getCode).map(String::valueOf).toList(), societyCancellation.getBranchId());

            if (secretary != null) secretaryAddress = secretary.getCommitteeAddress();
            if (president != null) presidentAddress = president.getCommitteeAddress();
            if (treasurer != null) treasurerAddress = treasurer.getCommitteeAddress();
        }else{
            Committee secretary = committeeReadDomainService.findActiveCommitteeInSocietyWithRoles(Position.getAllSecretaryPositions().stream().map(Position::getCode).map(String::valueOf).toList(), societyId);
            Committee president = committeeReadDomainService.findActiveCommitteeInSocietyWithRoles(Position.getAllPresidentPositions().stream().map(Position::getCode).map(String::valueOf).toList(), societyId);
            Committee treasurer = committeeReadDomainService.findActiveCommitteeInSocietyWithRoles(Position.getAllTreasurerPositions().stream().map(Position::getCode).map(String::valueOf).toList(), societyId);

            if (secretary != null) secretaryAddress = secretary.getResidentialAddress();
            if (president != null) presidentAddress = president.getResidentialAddress();
            if (treasurer != null) treasurerAddress = treasurer.getResidentialAddress();
        }
        AdmInsolvencyDepartment admInsolvencyDepartment = admInsolvencyDepartmentReadDomainService.findByStateCode(society.getStateCode());

        return getMapFields(society, societyCancellation, secretaryAddress, presidentAddress, treasurerAddress, admInsolvencyDepartment);
    }

    @Override
    public boolean canHandle(String templateCode) {
        return DocumentTemplateEnum.SECTION_13_1_D.getCode().equals(templateCode);
    }

    private static Map<String, String> getMapFields(Society society, SocietyCancellation societyCancellation, String secretaryAddress, String presidentAddress, String treasurerAddress, AdmInsolvencyDepartment admInsolvencyDepartment) {
        Map<String, String> mapFields = new HashMap<>();
        mapFields.put("{{SOCIETY_NO}}", getValueOrDefault(society.getSocietyNo()).toUpperCase());
        mapFields.put("{{SECTION}}", getValueOrDefault(SocietyCancellationDetailSectionEnum.findByCode(societyCancellation.getSection()).getDescription()));
        mapFields.put("{{SOCIETY_NAME}}", getValueOrDefault(society.getSocietyName()).toUpperCase());
        mapFields.put("{{SOCIETY_ADDRESS}}", getValueOrDefault(society.getAddress()).toUpperCase());
        mapFields.put("{{CANCELLATION_DATE}}", formatDateOrDefault(societyCancellation.getCancelledDate(), "-"));
        mapFields.put("{{DEADLINE_DATE}}", getValueOrDefault(null));
        mapFields.put("{{LIQUIDATION_DATE}}", getValueOrDefault(null));
        mapFields.put("{{CANCELLATION_DAY}}", getValueOrDefault(String.valueOf(societyCancellation.getCancelledDate().getDayOfMonth())));
        mapFields.put("{{CANCELLATION_MONTH}}", getValueOrDefault(societyCancellation.getCancelledDate().getMonth().getDisplayName(TextStyle.FULL, Locale.forLanguageTag("ms"))).toUpperCase());
        mapFields.put("{{CANCELLATION_YEAR}}", getValueOrDefault(String.valueOf(societyCancellation.getCancelledDate().getYear())));
        mapFields.put("{{SECRETARY_ADDRESS}}", getValueOrDefault(secretaryAddress));
        mapFields.put("{{PRESIDENT_ADDRESS}}", getValueOrDefault(presidentAddress));
        mapFields.put("{{TREASURER_ADDRESS}}", getValueOrDefault(treasurerAddress));
        mapFields.put("{{INSOLVENCY_DEPARTMENT_ADDRESS}}", getValueOrDefault(admInsolvencyDepartment.getAddress()));
        return mapFields;
    }
}