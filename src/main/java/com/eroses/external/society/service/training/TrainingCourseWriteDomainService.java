package com.eroses.external.society.service.training;

import com.eroses.external.society.mappers.TrainingCourseDao;
import com.eroses.external.society.model.TrainingCourse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class TrainingCourseWriteDomainService {

    private final TrainingCourseDao trainingCourseDao;

    public Long createTrainingCourse(TrainingCourse trainingCourse) throws Exception {
        boolean isCreated = trainingCourseDao.create(trainingCourse);
        if (!isCreated) {
            throw new RuntimeException("Failed to create training course");
        }
        return trainingCourse.getId();
    }

    public Long updateTrainingCourse(TrainingCourse trainingCourse) throws Exception {
        boolean isUpdated = trainingCourseDao.update(trainingCourse);
        if (!isUpdated) {
            throw new RuntimeException("Failed to update training course");
        }
        return trainingCourse.getId();
    }

    public Long updateTrainingCourseStatus(Long id, Integer status, Long modifiedBy) {
        boolean isUpdated = trainingCourseDao.updateStatus(id, status, modifiedBy);
        if (!isUpdated) {
            throw new RuntimeException("Failed to update training course status");
        }
        return id;
    }

    public void delete(Long id) {
        trainingCourseDao.delete(id);
    }
}
