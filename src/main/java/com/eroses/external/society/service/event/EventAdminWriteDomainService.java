package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.EventAdminDao;
import com.eroses.external.society.model.EventAdmin;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventAdminWriteDomainService {
    public final EventAdminDao adminDao;

    public EventAdmin createEventAdmin(EventAdmin eventAdminData) {
        try {
            adminDao.create(eventAdminData);
        } catch (Exception e) {
            return null;
        }
        return eventAdminData;
    }

    public int updateEventAdmin(String identificationNo, EventAdmin eventAdmin) {
        int updatedAdmin =  adminDao.update(identificationNo, eventAdmin);
        return updatedAdmin;
    }
}
