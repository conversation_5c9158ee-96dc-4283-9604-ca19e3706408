package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.FeedbackQuestionDao;
import com.eroses.external.society.model.FeedbackQuestion;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class FeedbackQuestionWriteDomainService {
    private  final FeedbackQuestionDao feedbackQuestionDao;

    public FeedbackQuestion create(FeedbackQuestion question) {
        int creatingQuestion = feedbackQuestionDao.create(question);
        if (creatingQuestion > 0)
            return question;
        else
            return null;
    }

    public FeedbackQuestion update(FeedbackQuestion question){
        Boolean updatingQuestion = feedbackQuestionDao.update(question);
        if(updatingQuestion)
            return question;
        else
            return null;
    }
}
