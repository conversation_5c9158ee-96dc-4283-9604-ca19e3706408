package com.eroses.external.society.service;

import com.eroses.external.society.dto.response.auditTrail.AuditTrailPagingResponse;
import com.eroses.external.society.mappers.AuditTrailDao;
import com.eroses.external.society.mappers.FeedbackDao;
import com.eroses.external.society.model.AuditTrail;
import com.eroses.external.society.model.Document;
import com.eroses.external.society.model.Feedback;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class FeedbackReadDomainService {
    private final FeedbackDao feedbackDao;

    public List<Feedback> findAllByParams(Map<String, Object> params) {
        return feedbackDao.findAllByParams(params);
    }

    public List<Feedback> findAllExcludeSatisfactory(Map<String, Object> params) {
        return feedbackDao.findAllExcludeSatisfactory(params);
    }

    public List<Feedback> findOnlySatisfactory(Map<String, Object> params) {
        return feedbackDao.findOnlySatisfactory(params);
    }

    public Feedback findFeedbackById(Long id) {
        return feedbackDao.findById(id);
    }

    public Long countFindOnlySatisfactory(Map<String, Object> params) {
        return feedbackDao.countFindOnlySatisfactory(params);
    }

    public Integer countMemuaskan(String satisfaction) {
        return feedbackDao.countMemuaskan(satisfaction);
    }

    public Integer countTidakMemuaskan(String satisfaction) {
        return feedbackDao.countTidakMemuaskan(satisfaction);
    }

    public Integer countSangatMemuaskan(String satisfaction) {
        return feedbackDao.countSangatMemuaskan(satisfaction);
    }

    public Long countAllExcludeSatisfactory(Map<String, Object> params) {
        return feedbackDao.countAllExcludeSatisfactory(params);
    }
}