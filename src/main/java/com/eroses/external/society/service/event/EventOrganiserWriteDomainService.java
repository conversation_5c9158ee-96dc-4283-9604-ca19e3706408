package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.EventOrganiserDao;
import com.eroses.external.society.model.EventOrganiser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventOrganiserWriteDomainService {
    private final EventOrganiserDao eoDao;

    public EventOrganiser create(EventOrganiser eventOrganiser){
        log.info("Creating Event Organiser [{}]", eventOrganiser);
        int eo = eoDao.create(eventOrganiser);
        if(eo > 0){
            return eventOrganiser;
        }else{
            return null;
        }
    }

    public EventOrganiser update(EventOrganiser eventOrganiser){
        log.info("Updating Event Organiser with data [{}]", eventOrganiser);
        Boolean updateEo = eoDao.update(eventOrganiser);
        if(updateEo){
            return  eventOrganiser;
        }else{
            return null;
        }
    }
}
