package com.eroses.external.society.service.societyCancellation;

import com.eroses.external.society.mappers.SocietyCancellationRevertDao;
import com.eroses.external.society.model.societyCancellation.SocietyCancellationRevert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyCancellationRevertWriteDomainService {
    private final SocietyCancellationRevertDao societyCancellationRevertDao;

    public Long create(SocietyCancellationRevert societyCancellationRevert) throws Exception {
        societyCancellationRevertDao.create(societyCancellationRevert);
        return societyCancellationRevert.getId();
    }
    
    public Boolean update(SocietyCancellationRevert societyCancellationRevert) throws Exception {
        return societyCancellationRevertDao.update(societyCancellationRevert);
    }
    
    public Boolean delete(Long id) {
        return societyCancellationRevertDao.delete(id);
    }
}