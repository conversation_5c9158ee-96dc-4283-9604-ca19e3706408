package com.eroses.external.society.service.blacklist;

import com.eroses.external.society.mappers.blacklist.BlacklistDao;
import com.eroses.external.society.model.blacklist.Blacklist;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class BlacklistWriteDomainService {
    private final BlacklistDao blacklistDao;

    @Transactional
    public Long create(Blacklist blacklist) throws Exception {
        blacklistDao.create(blacklist);
        return blacklist.getId();
    }

    @Transactional
    public Boolean update(Blacklist blacklist) throws Exception {
        return blacklistDao.update(blacklist);
    }

    @Transactional
    public Boolean delete(Long id) throws Exception {
        return blacklistDao.delete(id);
    }

    @Transactional
    public Boolean updateRemovalStatus(Long id, Boolean removalStatus) {
        return blacklistDao.updateRemovalStatus(id, removalStatus);
    }

    @Transactional
    public Boolean updateRemovalStatusByIds(List<Long> ids, Boolean removalStatus) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        params.put("removalStatus", removalStatus);
        return blacklistDao.updateRemovalStatusByIds(params);
    }
}