package com.eroses.external.society.service;

import com.eroses.external.society.mappers.SearchInformationDocumentDao;
import com.eroses.external.society.model.SearchInformationDocument;
import com.eroses.external.society.model.SearchInformationDocumentTemplate;
import com.eroses.external.society.model.enums.PaymentMethod;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Slf4j
@Service
@RequiredArgsConstructor
public class SearchInformationDocumentWriteDomainService {
    private final SearchInformationDocumentDao searchInformationDocumentDao;

    public Long create(SearchInformationDocumentTemplate searchInformationDocumentTemplate, Long searchInformationId, User currentUser, String s3Url, String paymentMethod) throws Exception {

        SearchInformationDocument searchInformationDocument = new SearchInformationDocument();

        searchInformationDocument.setSearchInformationId(searchInformationId);
        searchInformationDocument.setName(searchInformationDocumentTemplate.getName());
        searchInformationDocument.setTemplateCode(searchInformationDocumentTemplate.getCode());
        searchInformationDocument.setS3Url(s3Url);
        searchInformationDocument.setIsGenerated(searchInformationDocumentTemplate.getIsGenerated());
        searchInformationDocument.setType(searchInformationDocumentTemplate.getType());
        searchInformationDocument.setDownloadPeriod(searchInformationDocumentTemplate.getDownloadPeriod());
        searchInformationDocument.setAmount(searchInformationDocumentTemplate.getAmount());
        searchInformationDocument.setStatus(1);
        if(Objects.equals(paymentMethod, PaymentMethod.KAUNTER.getIdentifier())) {
            searchInformationDocument.setApplicationStatusCode(ApplicationStatusCode.MENUNGGU_BAYARAN_KAUNTER.getCode());
        } else if (Objects.equals(paymentMethod, PaymentMethod.ONLINE.getIdentifier())){
            searchInformationDocument.setApplicationStatusCode(ApplicationStatusCode.MENUNGGU_BAYARAN_ONLINE.getCode());
        }
        searchInformationDocument.setCreatedBy(currentUser.getId());
        searchInformationDocument.setCreatedDate(LocalDateTime.now());
        searchInformationDocumentDao.create(searchInformationDocument);
        return searchInformationDocument.getId();
    }

    public Long update(SearchInformationDocument searchInformationDocument, User currentUser) throws Exception {
        searchInformationDocument.setModifiedBy(currentUser.getId());
        searchInformationDocument.setModifiedDate(LocalDateTime.now());
        searchInformationDocumentDao.update(searchInformationDocument);
        return searchInformationDocument.getId();
    }

    public Long update(SearchInformationDocument searchInformationDocument) throws Exception {
        searchInformationDocument.setModifiedBy(0L);
        searchInformationDocument.setModifiedDate(LocalDateTime.now());
        searchInformationDocumentDao.update(searchInformationDocument);
        return searchInformationDocument.getId();
    }

    public Boolean updateExpiredSearchInformationDocument(List<Long> ids, int applicationStatusCode) {
        Map<String, Object> params = Map.of(
                "ids", ids,
                "applicationStatusCode", applicationStatusCode
        );
        return searchInformationDocumentDao.updateExpiredSearchInformationDocument(params);
    }
}
