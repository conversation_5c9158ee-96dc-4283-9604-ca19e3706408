package com.eroses.external.society.service;

import com.eroses.external.society.dto.request.report.QuickSightEmbedUrlRequest;
import com.eroses.external.society.dto.response.report.QuickSightEmbedUrlResponse;
import com.eroses.external.society.exception.ApplicationException;
import com.eroses.external.society.utils.SecretsManagerUtil;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.github.cdimascio.dotenv.Dotenv;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.quicksight.QuickSightClient;
import software.amazon.awssdk.services.quicksight.model.*;

import java.time.LocalDateTime;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class QuickSightService {

    private final UserFacade userFacade;

    @Value("${aws.region}")
    private String awsRegion;

    @Value("${aws.quicksight.region}")
    private String quicksightRegion;

    @Value("${aws.quicksight.secret-name}")
    private String secretName;

    @Value("${aws.quicksight.account-id}")
    private String accountId;

    @Value("${aws.quicksight.user-arn}")
    private String userArn;

    @Value("${aws.quicksight.session-timeout-minutes}")
    private Long defaultSessionTimeoutMinutes;

    @Value("${aws.quicksight.undo-redo-disabled}")
    private Boolean defaultUndoRedoDisabled;

    @Value("${aws.quicksight.reset-disabled}")
    private Boolean defaultResetDisabled;

    @Value("${aws.quicksight.access-key}")
    private String defaultAccessKey;

    @Value("${aws.quicksight.secret-key}")
    private String defaultSecretKey;

    @Value("${spring.profiles.active}")
    private String env;

    /**
     * Generate registered user embed URL for QuickSight dashboard
     */
    public QuickSightEmbedUrlResponse generateRegisteredUserEmbedUrl(QuickSightEmbedUrlRequest request) {
        try {
            // Use dashboardId from request instead of configuration
            String requestDashboardId = request.getDashboardId();
            log.info("Generating QuickSight registered user embed URL for dashboard: {}", requestDashboardId);

            // Get current user information
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                throw new RuntimeException("User not authenticated or not found");
            }

            QuickSightClient quickSightClient = createQuickSightClient();

            log.info("Using QuickSight user ARN from config: {}", userArn);

            // Build the registered user embed configuration using dashboardId from request
            RegisteredUserDashboardEmbeddingConfiguration dashboardEmbeddingConfiguration =
                RegisteredUserDashboardEmbeddingConfiguration.builder()
                    .initialDashboardId(requestDashboardId)
                    .build();

            // Build the embed URL generation request using configuration values
            GenerateEmbedUrlForRegisteredUserRequest embedRequest =
                GenerateEmbedUrlForRegisteredUserRequest.builder()
                    .awsAccountId(accountId)
                    .userArn(userArn)
                    .sessionLifetimeInMinutes(defaultSessionTimeoutMinutes)
                    .experienceConfiguration(RegisteredUserEmbeddingExperienceConfiguration.builder()
                        .dashboard(dashboardEmbeddingConfiguration)
                        .build())
                    .build();

            // Generate the embed URL
            GenerateEmbedUrlForRegisteredUserResponse response = quickSightClient.generateEmbedUrlForRegisteredUser(embedRequest);

            log.info("Successfully generated QuickSight embed URL. Status: {}, RequestId: {}",
                response.status(), response.requestId());

            return QuickSightEmbedUrlResponse.builder()
                .embedUrl(response.embedUrl())
                .requestId(response.requestId())
                .sessionTimeoutInMinutes(defaultSessionTimeoutMinutes)
                .dashboardId(requestDashboardId)
                .generatedAt(LocalDateTime.now())
                .status(response.status())
                .build();

        } catch (ApplicationException e) {
            // Re-throw ApplicationException as-is (these are credential/configuration issues)
            log.error("QuickSight configuration error: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Error generating QuickSight registered user embed URL: {}", e.getMessage(), e);
            throw new ApplicationException("Failed to generate QuickSight embed URL: " + e.getMessage(), e);
        }
    }

    /**
     * Get current authenticated user
     */
    private User getCurrentUser() {
        try {
            User user = userFacade.me();
            if (user == null) {
                log.error("No authenticated user found");
                return null;
            }
            log.info("Current user: ID={}, IdentificationNo={}, Name={}",
                user.getId(), user.getIdentificationNo(), user.getName());
            return user;
        } catch (Exception e) {
            log.error("Error retrieving current user: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Create QuickSight client with appropriate credentials
     */
    private QuickSightClient createQuickSightClient() {
        try {
            AwsBasicCredentials awsCredentials = getAwsCredentials();

            if (quicksightRegion == null || quicksightRegion.trim().isEmpty()) {
                log.error("AWS region is null or empty");
                throw new ApplicationException("AWS region is not configured. Please check aws.quicksight.region property.");
            }

            return QuickSightClient.builder()
                .region(Region.of(quicksightRegion))
                .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                .build();

        } catch (ApplicationException e) {
            // Re-throw ApplicationException as-is
            throw e;
        } catch (Exception e) {
            log.error("Error creating QuickSight client: {}", e.getMessage(), e);
            throw new ApplicationException("Failed to create QuickSight client: " + e.getMessage(), e);
        }
    }

    /**
     * Get AWS credentials from Secrets Manager or environment variables (for local)
     */
    private AwsBasicCredentials getAwsCredentials() {
        String accessKey;
        String secretKey;

        if ("local".equals(env)) {
            return AwsBasicCredentials.create(defaultAccessKey, defaultSecretKey);
        }

        // For non-local environments, use Secrets Manager
        log.info("Retrieving AWS credentials from Secrets Manager: {}", secretName);
        try {
            if (secretName == null || secretName.trim().isEmpty()) {
                log.error("Secret name is null or empty");
                throw new ApplicationException("AWS secret name is not configured. Please check aws.quicksight.secret-name property.");
            }

            Map<String, String> secrets = SecretsManagerUtil.getSecret(secretName, awsRegion);

            if (secrets == null || secrets.isEmpty()) {
                log.error("No secrets retrieved from Secrets Manager");
                throw new ApplicationException("No secrets found in AWS Secrets Manager for secret: " + secretName);
            }

            accessKey = secrets.get("accessKey");
            secretKey = secrets.get("secretKey");

            if (accessKey == null || accessKey.trim().isEmpty()) {
                log.error("Access key not found in secrets manager");
                throw new ApplicationException("AWS access key not found in secrets manager. Please ensure 'accessKey' field exists in secret: " + secretName);
            }

            if (secretKey == null || secretKey.trim().isEmpty()) {
                log.error("Secret key not found in secrets manager");
                throw new ApplicationException("AWS secret key not found in secrets manager. Please ensure 'secretKey' field exists in secret: " + secretName);
            }

            log.info("Successfully retrieved AWS credentials from Secrets Manager");
            return AwsBasicCredentials.create(accessKey, secretKey);

        } catch (ApplicationException e) {
            // Re-throw ApplicationException as-is
            throw e;
        } catch (Exception e) {
            log.error("Error retrieving AWS credentials from Secrets Manager: {}", e.getMessage(), e);
            throw new ApplicationException("Failed to retrieve AWS credentials from Secrets Manager: " + e.getMessage(), e);
        }
    }
}
