package com.eroses.external.society.service;

import com.eroses.external.society.mappers.AuditorDao;
import com.eroses.external.society.model.Auditor;
import com.eroses.external.society.model.enums.StatusCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuditorReadDomainService {
    private final AuditorDao auditorDao;

    public List<Auditor> listAuditor(Map<String, Object> params) {

        if (!params.containsKey("status")) {
            params.put("status", StatusCode.AKTIF.getCode());
        }

        return auditorDao.listAuditor(params);
    }

    public List<Auditor> getAllByCriteria(Long societyId, Long branchId) {
        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);

        return auditorDao.listAuditor(params);
    }

    public Long countListAuditor(Map<String, Object> params) {
        return auditorDao.countListAuditor(params);
    }

    public Auditor findById(Long id) {
        return auditorDao.findById(id);
    }

    public boolean isExists(Long id) {
        return findById(id) != null;
    }

    public Auditor findByStatementId(Long statementId) {
        return auditorDao.findByStatementId(statementId);
    }
}
