package com.eroses.external.society.service;

import com.eroses.external.society.model.Document;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentUploadMetadataDomainService {
    private final Map<String, Document> metadataCache = new ConcurrentHashMap<>();
    private final Duration metadataTtl = Duration.ofMinutes(20); // Optional TTL

    public void storeMetadata(String uuid, Document metadata) {
        metadataCache.put(uuid, metadata);
        log.info("Stored metadata for UUID: {}", uuid);
    }

    public Document getMetadata(String uuid) {
        Document metadata = metadataCache.get(uuid);
        if (metadata == null) {
            log.warn("Metadata not found for UUID: {}", uuid);
        }
        return metadata;
    }

    public void cleanupExpiredMetadata() {
        // Implement TTL-based cleanup if needed
        // Example: Remove entries older than metadataTtl
    }
}
