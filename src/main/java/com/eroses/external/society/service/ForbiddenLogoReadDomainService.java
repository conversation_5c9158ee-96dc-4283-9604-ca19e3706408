package com.eroses.external.society.service;

import com.eroses.external.society.mappers.ForbiddenLogoDao;
import com.eroses.external.society.model.ForbiddenLogo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class ForbiddenLogoReadDomainService {
    private final ForbiddenLogoDao forbiddenLogoDao;

    public List<ForbiddenLogo> findAllLaranganLogo() {
        log.info("Finding all larangan logo");
        return forbiddenLogoDao.findAllLaranganLogo();
    }

    public ForbiddenLogo findLaranganLogoById(Long id) {
        log.info("Finding larangan logo by id: {}", id);
        return forbiddenLogoDao.findLaranganLogoById(id);
    }

    public List<String> findAllLaranganLogoImageOnly() {
        log.info("Finding all larangan logo image only");
        return forbiddenLogoDao.findAllLaranganLogoImageOnly();
    }

    public List<String> findAllLaranganLogoVectorOnly() {
        log.info("Finding all larangan logo vector only");
        return forbiddenLogoDao.findAllLaranganLogoVectorOnly();
    }
    public List<ForbiddenLogo> searchLaranganLogo(String catatan, Integer offset, Integer limit, Boolean activeStatus) {
        log.info("Searching larangan logo for catatan: {}", catatan);
        Map<String, Object> params = new HashMap<>();
        params.put("catatan", catatan);
        params.put("offset", offset);
        params.put("limit", limit);
        params.put("activeStatus", activeStatus);
        return forbiddenLogoDao.searchLaranganLogo(params);
    }
    public Long countSearchLaranganLogo(String catatan, Boolean activeStatus) {
        log.info("Counting search larangan logo for catatan: {}", catatan);
        Map<String, Object> params = new HashMap<>();
        params.put("catatan", catatan);
        params.put("activeStatus", activeStatus);
        return forbiddenLogoDao.countSearchLaranganLogo(params);

    }
}
