package com.eroses.external.society.service.utils;

import com.itextpdf.styledxmlparser.jsoup.Jsoup;
import com.itextpdf.styledxmlparser.jsoup.nodes.*;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.text.StringEscapeUtils;

/**
 * Service for dynamically generating HTML tables from templates
 */
@Service
public class HtmlGeneratorService {

    // Define HTML tags that should not be escaped
    private static final Set<String> HTML_SAFE_TAGS = new HashSet<>(Arrays.asList(
            "img", "div", "span", "p", "a", "h1", "h2", "h3", "h4", "h5", "h6",
            "ul", "ol", "li", "table", "tr", "td", "th", "thead", "tbody",
            "input", "textarea", "select", "option", "form", "button",
            "strong", "em", "b", "i", "u", "br", "hr", "iframe", "video", "audio"
    ));

    // Your existing HTML_SAFE_KEYS collection
    /**
     * Used for keys or placeholders that are already set as html so not to be escaped
     */
    private static final Set<String> HTML_SAFE_KEYS = new HashSet<>(Arrays.asList(
            "{{CONSTITUTION_BODY}}"
    ));

    /**
     * Processes an HTML template by replacing placeholders and generating dynamic tables
     *
     * @param templateHtml The HTML template with placeholders
     * @param placeholders Map of placeholder keys to their values
     * @param tableData List of maps, each representing a row of data (key is column name, value is cell content)
     * @param tableIdentifier Unique identifier to find the table in the template (default: "tbody")
     * @return Processed HTML with placeholders replaced and tables populated
     */
    public String processTemplate(String templateHtml,
                                  Map<String, String> placeholders,
                                  List<Map<String, String>> tableData,
                                  String tableIdentifier) {

        // First replace all regular placeholders
        String html = replacePlaceholders(templateHtml, placeholders);

        // Then replace the table content if table data is provided
        if (tableData != null && !tableData.isEmpty()) {
            html = generateDynamicTable(html, tableData, tableIdentifier);
        }

        return html;
    }

    /**
     * Overloaded method with default table identifier
     */
    public String processTemplate(String templateHtml,
                                  Map<String, String> placeholders,
                                  List<Map<String, String>> tableData) {
        return processTemplate(templateHtml, placeholders, tableData, "tbody");
    }

    /**
     * Replaces placeholders in HTML with values from the provided map
     *
     * @param html HTML content with placeholders
     * @param placeholders Map of placeholder keys to their values
     * @return HTML with placeholders replaced
     */
    public String replacePlaceholders(String html, Map<String, String> placeholders) {
        if (html == null || placeholders == null) {
            return html;
        }

        String processedHtml = html;

        // Replace placeholders
        for (Map.Entry<String, String> entry : placeholders.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue() != null ? entry.getValue() : "";
            // Escape XML special characters to prevent parsing issues
            String escapedValue = escapeXmlSpecialChars(value);
            processedHtml = processedHtml.replace(key, escapedValue);
        }

        return processedHtml;
    }

    /**
     * Generates a dynamic table by replacing table content in HTML template
     *
     * @param html HTML content with table placeholders
     * @param tableData List of maps representing table rows
     * @param tableIdentifier Identifier to locate the table (default: "tbody")
     * @return HTML with dynamic table content
     */
    public String generateDynamicTable(String html,
                                       List<Map<String, String>> tableData,
                                       String tableIdentifier) {

        if (html == null || tableData == null || tableData.isEmpty()) {
            return html;
        }

        // Pattern to find the table section
        String tablePattern = "<" + tableIdentifier + ">[\\s\\S]*?</tbody>";
        Pattern pattern = Pattern.compile(tablePattern);
        Matcher matcher = pattern.matcher(html);

        if (matcher.find()) {
            // Get the column headers from the first row data
            Map<String, String> firstRow = tableData.get(0);
            List<String> columnKeys = new java.util.ArrayList<>(firstRow.keySet());

            // Generate new table content
            StringBuilder tableContent = new StringBuilder();
            tableContent.append("<").append(tableIdentifier).append(">\n");

            // Generate a row for each item in tableData
            for (Map<String, String> rowData : tableData) {
                tableContent.append("                <tr>\n");

                // Add cells for each column
                for (String columnKey : columnKeys) {
                    String value = rowData.getOrDefault(columnKey, "");

                    // Check if this column should be centered
                    // (customize this logic for different styling requirements)
                    boolean centerAlign = columnKey.contains("Bil") ||
                            columnKey.contains("No") ||
                            columnKey.contains("Number");

                    if (centerAlign) {
                        tableContent.append("                    <td style=\"text-align: center;\">")
                                .append(escapeXmlSpecialChars(value))
                                .append("</td>\n");
                    } else {
                        tableContent.append("                    <td>")
                                .append(escapeXmlSpecialChars(value))
                                .append("</td>\n");
                    }
                }

                tableContent.append("                </tr>\n");
            }

            tableContent.append("            </tbody>");

            // Replace the original table body with our dynamic content
            return matcher.replaceFirst(tableContent.toString());
        }

        return html;
    }

    /**
     * Overloaded method with default table identifier
     */
    public String generateDynamicTable(String html, List<Map<String, String>> tableData) {
        return generateDynamicTable(html, tableData, "tbody");
    }

    /**
     * Advanced table generation method that supports multiple tables in a single document
     *
     * @param html HTML content with table placeholders
     * @param tablesData Map of table identifiers to their data
     * @return HTML with all dynamic tables populated
     */
    public String generateMultipleTables(String html, Map<String, List<Map<String, String>>> tablesData) {
        if (html == null || tablesData == null || tablesData.isEmpty()) {
            return html;
        }

        String processedHtml = html;

        // Process each table
        for (Map.Entry<String, List<Map<String, String>>> entry : tablesData.entrySet()) {
            String tableId = entry.getKey();
            List<Map<String, String>> tableData = entry.getValue();

            // Generate this specific table
            processedHtml = generateDynamicTable(processedHtml, tableData, tableId);
        }

        return processedHtml;
    }

    /**
     * Process conditionals in the format {{#KEY}}...{{/KEY}}
     */
    public String processConditionals(String htmlTemplate, Map<String, String> mapFields) {
        StringBuffer result = new StringBuffer();

        // Pattern to match conditional blocks {{#KEY}}...{{/KEY}}
        Pattern pattern = Pattern.compile("\\{\\{#(.*?)\\}\\}([\\s\\S]*?)\\{\\{/(.*?)\\}\\}");
        Matcher matcher = pattern.matcher(htmlTemplate);

        while (matcher.find()) {
            String key = matcher.group(1);
            String content = matcher.group(2);
            String endKey = matcher.group(3);

            // Ensure the keys match
            if (!key.equals(endKey)) {
                System.err.println("Mismatched conditional tags: " + key + " and " + endKey);
                continue;
            }

            // Check if the condition is met (key exists in map with value "true")
            String conditionValue = mapFields.get("{{" + key + "}}");
            if ("true".equals(conditionValue)) {
                // Condition is met, keep the content
                matcher.appendReplacement(result, Matcher.quoteReplacement(content));
            } else {
                // Condition is not met, remove the content
                matcher.appendReplacement(result, "");
            }
        }

        matcher.appendTail(result);
        return result.toString();
    }

    public String mapToHtml(String htmlContent, Map<String, String> mapFields) {
        if (htmlContent == null || mapFields == null) {
            return htmlContent;
        }

        // Regular expression to find placeholders in the format {{PLACEHOLDER}}
        Pattern pattern = Pattern.compile("\\{\\{(.*?)\\}\\}");
        Matcher matcher = pattern.matcher(htmlContent);

        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String key = matcher.group(0); // Extract the full match {{KEY}}
            String replacement = mapFields.getOrDefault(key, ""); // Get value from map or empty string
            // Check if content should be treated as HTML-safe
            String escapedReplacement = "";

            //for constitution body to be safe
            if (HTML_SAFE_KEYS.contains(key)) {
                escapedReplacement = escapeTextNodesAsXHTML(replacement);
            } else {
                Boolean isHtml = containsAnyHtmlTags(replacement);
                escapedReplacement = isHtml ? replacement : escapeXmlSpecialChars(replacement);
            }

            matcher.appendReplacement(result, Matcher.quoteReplacement(escapedReplacement)); // Escape $ to prevent regex issues
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * Escapes XML special characters in a string to prevent XML parsing issues
     *
     * @param input The string to escape
     * @return The escaped string safe for XML
     */
    private String escapeXmlSpecialChars(String input) {
        if (input == null) {
            return "";
        }

        // Use Apache Commons Text for XML escaping
        return StringEscapeUtils.escapeXml10(input);
    }

    /**
     * More comprehensive check - finds HTML tags anywhere in the content
     * @param content The content to check
     * @return true if content contains any HTML tags that should be preserved
     */
    public static boolean containsAnyHtmlTags(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }

        // Regex pattern to match opening HTML tags
        Pattern htmlTagPattern = Pattern.compile("<\\s*([a-zA-Z][a-zA-Z0-9]*)\\s*[^>]*>", Pattern.CASE_INSENSITIVE);
        Matcher matcher = htmlTagPattern.matcher(content);

        while (matcher.find()) {
            String tagName = matcher.group(1).toLowerCase();
            if (HTML_SAFE_TAGS.contains(tagName)) {
                return true;
            }
        }

        return false;
    }

    public static String escapeTextNodesAsXHTML(String html) {
        Document doc = Jsoup.parseBodyFragment(html);

        for (Element element : doc.getAllElements()) {
            for (int i = 0; i < element.childNodeSize(); i++) {
                Node child = element.childNode(i);
                if (child instanceof TextNode) {
                    TextNode textNode = (TextNode) child;
                    String originalText = textNode.getWholeText();
//                    String escapedText = StringEscapeUtils.escapeHtml4(originalText);
                    textNode.text(originalText);
                }
            }
        }

        // Set output settings to XHTML mode
        doc.outputSettings()
                .syntax(Document.OutputSettings.Syntax.xml) // force XHTML
                .escapeMode(Entities.EscapeMode.xhtml)
                .prettyPrint(false); // optional: avoid unwanted whitespace

        return doc.body().html(); // returns well-formed XHTML fragment
    }

    /**
     * For a complex document with multiple tables.
     *
     * Example Usage:
     *
     * HtmlTableGeneratorService service = new HtmlTableGeneratorService();
     *
     * // First, replace regular placeholders
     * String htmlWithPlaceholders = service.replacePlaceholders(htmlTemplate, commonPlaceholders);
     *
     * // Create a map for multiple tables
     * Map<String, List<Map<String, String>>> tablesData = new HashMap<>();
     * tablesData.put("tbody id=\"committee-table-body\"", committeeTableData);
     * tablesData.put("tbody id=\"finance-table-body\"", financesTableData);
     * tablesData.put("tbody id=\"event-table-body\"", eventsTableData);
     *
     * // Generate all tables at once
     * String finalHtml = service.generateMultipleTables(htmlWithPlaceholders, tablesData);
     *
     * // Generate PDF
     * byte[] binary = Base64.getDecoder().decode(pdfService.generatePdfWithOpenHtmlToPdf(finalHtml));
     */

}
