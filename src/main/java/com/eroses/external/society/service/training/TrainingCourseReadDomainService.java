package com.eroses.external.society.service.training;

import com.eroses.external.society.mappers.TrainingCourseDao;
import com.eroses.external.society.model.TrainingCourse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class TrainingCourseReadDomainService {

    private final TrainingCourseDao trainingCourseDao;

    public TrainingCourse getTrainingCourseById(Long id) {
        return trainingCourseDao.findById(id);
    }

    public TrainingCourse getTrainingCourseWithMaterialsById(Long id) {
        return trainingCourseDao.findByIdWithMaterials(id);
    }

    public List<TrainingCourse> getAllTrainingCourses() {
        return trainingCourseDao.findAll();
    }

    public List<TrainingCourse> getAllPublishedTrainingCourses() {
        return trainingCourseDao.findAllPublished();
    }

    public List<TrainingCourse> getAllTrainingCoursesByStatus(Integer status) {
        return trainingCourseDao.findAllByStatus(status);
    }

    public List<TrainingCourse> getAllTrainingCoursesByCreator(Long createdBy) {
        return trainingCourseDao.findAllByCreator(createdBy);
    }
}
