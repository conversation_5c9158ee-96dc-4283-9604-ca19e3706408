package com.eroses.external.society.service;

import com.eroses.external.society.mappers.StatementContributionDao;
import com.eroses.external.society.model.StatementContribution;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatementContributionWriteDomainService {
    private final StatementContributionDao statementContributionDao;

    public void create(StatementContribution statementContribution) throws Exception {
        statementContributionDao.create(statementContribution);
    }

    public boolean update(StatementContribution statementContribution) throws Exception {
        return statementContributionDao.update(statementContribution);
    }

    public Boolean deleteStatement(Long statementId, Long societyId, Long branchId) {
        Map<String, Object> params = new HashMap<>();
        params.put("statementId", statementId);
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        return statementContributionDao.deleteStatement(params);
    }
}
