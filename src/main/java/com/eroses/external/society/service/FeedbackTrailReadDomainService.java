package com.eroses.external.society.service;

import com.eroses.external.society.mappers.FeedbackTrailDao;
import com.eroses.external.society.model.FeedbackTrail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class FeedbackTrailReadDomainService {
    private final FeedbackTrailDao feedbackTrailDao;

    public List<FeedbackTrail> getAllByCriteria(Long feedbackId) {
        Map<String, Object> params = new HashMap<>();
        params.put("feedbackId", feedbackId);
        return feedbackTrailDao.getAllByCriteria(params);
    }

    public FeedbackTrail findFeedbackTrailById(Long id) {
        return feedbackTrailDao.findById(id);
    }
}