package com.eroses.external.society.service;

import com.eroses.external.society.mappers.CommitteeDao;
import com.eroses.external.society.mappers.PrincipalSecretaryDao;
import com.eroses.external.society.mappers.SecretaryCommitteeFeedbackDao;
import com.eroses.external.society.model.Committee;
import com.eroses.external.society.model.PrincipalSecretary;
import com.eroses.external.society.model.SecretaryCommitteeFeedback;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.SQLIntegrityConstraintViolationException;

@Slf4j
@Service
@RequiredArgsConstructor
public class SecretaryCommitteeFeedbackWriteDomainService {
    private final SecretaryCommitteeFeedbackDao secretaryCommitteeFeedbackDao;

    public Long create(SecretaryCommitteeFeedback secretaryCommitteeFeedback) throws Exception {
        secretaryCommitteeFeedbackDao.create(secretaryCommitteeFeedback);
        return secretaryCommitteeFeedback.getId();
    }
}

