package com.eroses.external.society.service;

import com.eroses.external.society.mappers.RoAmendmentApprovalDao;
import com.eroses.external.society.model.RoAmendmentApproval;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class RoAmendmentApprovalWriteDomainService {

    private final RoAmendmentApprovalDao roAmendmentApprovalDao;

    public Long create(RoAmendmentApproval roAmendmentApproval) throws Exception {
        roAmendmentApprovalDao.create(roAmendmentApproval);
        return roAmendmentApproval.getId();
    }

    public RoAmendmentApproval update(RoAmendmentApproval roAmendmentApproval) throws Exception {
        roAmendmentApprovalDao.update(roAmendmentApproval);
        return roAmendmentApproval;
    }
}
