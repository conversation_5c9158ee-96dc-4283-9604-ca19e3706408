package com.eroses.external.society.service;

import com.eroses.external.society.mappers.SocietyDocumentTemplateDao;
import com.eroses.external.society.model.SocietyDocumentTemplate;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyDocumentTemplateWriteDomainService {
    private final SocietyDocumentTemplateDao societyDocumentTemplateDao;

    public Long create(Long societyId, Long documentTemplateId, User currentUser) throws Exception {

        SocietyDocumentTemplate societyDocumentTemplate = new SocietyDocumentTemplate();
        societyDocumentTemplate.setSocietyId(societyId);
        societyDocumentTemplate.setDocumentTemplateId(documentTemplateId);
        societyDocumentTemplate.setAssignedAt(LocalDateTime.now());
        societyDocumentTemplate.setStatus(1);
        societyDocumentTemplateDao.create(societyDocumentTemplate);
        return societyDocumentTemplate.getId();
    }
}
