package com.eroses.external.society.service;

import com.eroses.external.society.model.SearchInformationDocumentTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class SearchInformationUtilitiesService {
    private final SearchInformationDocumentTemplateReadDomainService searchInformationDocumentTemplateReadDomainService;

    public boolean validateDocumentTemplateCode(String documentTemplateCode) {
        if (documentTemplateCode == null || documentTemplateCode.isEmpty()) {
            return false;
        }

        SearchInformationDocumentTemplate documentTemplate =
                searchInformationDocumentTemplateReadDomainService.getByCode(documentTemplateCode);

        return documentTemplate != null && documentTemplate.getCode() != null;
    }

    public boolean validateDocumentTemplateCode(List<String> documentTemplateCodes) {
        if (documentTemplateCodes == null || documentTemplateCodes.isEmpty()) {
            return false;
        }

        for (String documentTemplateCode : documentTemplateCodes) {
            if (!validateDocumentTemplateCode(documentTemplateCode)) {
                return false;
            }
        }
        return true;
    }
}
