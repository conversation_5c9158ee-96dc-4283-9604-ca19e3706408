package com.eroses.external.society.service;

import com.eroses.external.society.model.ClauseContent;
import com.eroses.external.society.model.ConstitutionContent;
import com.eroses.external.society.model.ConstitutionValue;
import com.eroses.external.society.model.Society;
import com.eroses.external.society.model.enums.ClauseEnum;
import com.eroses.external.society.model.enums.ConstitutionTypeEnum;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.model.enums.StatusCode;
import com.eroses.external.society.service.admin.ClauseContentReadDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClauseGeneratorWriteDomainService {
    private final ConstitutionContentReadDomainService constitutionContentReadDomainService;
    private final ConstitutionContentWriteDomainService constitutionContentWriteDomainService;
    private final ConstitutionValueReadDomainService constitutionValueReadDomainService;
    private final ConstitutionValueWriteDomainService constitutionValueWriteDomainService;
    private final ClauseContentReadDomainService clauseContentReadDomainService;
    private final SocietyWriteDomainService societyWriteDomainService;

    public void generalReceiver(ConstitutionContent content, String method) throws Exception {
        //get source clause details
        ClauseEnum clauseEnum = ClauseEnum.getClause(Math.toIntExact(content.getClauseContentId()));

        //for bebas
        final boolean bebasType = clauseEnum.getConstitutionType() == ConstitutionTypeEnum.Bebas.getId() ||
                clauseEnum.getConstitutionType() == ConstitutionTypeEnum.BebasCawangan.getId();
        if (Objects.equals(content.getConstitutionTypeId(), Long.valueOf(ConstitutionTypeEnum.Bebas.getId())) ||
                Objects.equals(content.getConstitutionTypeId(), Long.valueOf(ConstitutionTypeEnum.BebasCawangan.getId()))) {
            if (content.getClauseContentId() == null) {
                return;
            }
        }

        final boolean nonBranchType = clauseEnum.getConstitutionType() == ConstitutionTypeEnum.IndukNGO.getId() ||
                clauseEnum.getConstitutionType() == ConstitutionTypeEnum.IndukAgama.getId();

        final boolean branchType = clauseEnum.getConstitutionType() == ConstitutionTypeEnum.CawanganNGO.getId() ||
                clauseEnum.getConstitutionType() == ConstitutionTypeEnum.CawanganAgama.getId();

        final boolean faedahBersamaType = clauseEnum.getConstitutionType() == ConstitutionTypeEnum.FaedahBersama.getId();

        List<Integer> clauseNoToGenerate = new ArrayList<>();
        switch (clauseEnum.getClauseNo()) {
            case 1 -> {
                if (content.getAmendmentId() == null) {
                    //update society infos
                    societyWriteDomainService.updateSocietyInfoFromFasal1(content);
                }
            }
            case 2 -> {
                //society address, change directly only if amendmentId == null
                if (content.getAmendmentId() == null) {
                    //update society address
                    societyWriteDomainService.updateSocietyAddressFromFasal2(content);
                }
            }
            case 4 -> {
                if (!bebasType) {
                    sumberKewanganTextTemplateConstructor(content, method);
                }
            }
            case 5 -> {
                if (faedahBersamaType) {

                    clauseNoToGenerate.add(11);
                    clauseNoToGenerate.add(14);
                    clauseNoToGenerate.add(15);

                    tugasanJawatanKuasaFaedahReset(content, method);
                    ajkTextTemplateConstructor(content, method);
                }
            }
            case 6 -> {
                if (nonBranchType) {
                    //generate 10 & 12 - Jenis Mesyuarat Agung
                    clauseNoToGenerate.add(10);
                    clauseNoToGenerate.add(12);

                    tugasanJawatanKuasaReset(content, method);
                    ajkTextTemplateConstructor(content, method);
                }
                if (branchType) {

                    clauseNoToGenerate.add(10);
                    clauseNoToGenerate.add(12);
                    clauseNoToGenerate.add(15);

                    //fasal 16 - Jenis Mesyuarat Agung, Tempoh Pelantikan Jawatankuasa, Kekerapan Mesyuarat Jawatankuasa
                    clauseNoToGenerate.add(16);
                    clauseNoToGenerate.add(18);

                    tugasanJawatanKuasaReset(content, method);
                    ajkTextTemplateConstructor(content, method);
                }
            }
            case 9 -> {
                if (branchType) {
                    //generate Fasal 18 - Tahun Kewangan Bermula
                    clauseNoToGenerate.add(18);
                }
            }
            case 10 -> {
                if (nonBranchType) {
                    //generate 6 & 12 - Jenis Mesyuarat Agung

                    clauseNoToGenerate.add(6);
                    clauseNoToGenerate.add(12);
                }
                if (branchType) {
                    //generate Fasal 15
                    clauseNoToGenerate.add(6);
                    clauseNoToGenerate.add(12);
                    clauseNoToGenerate.add(15);
                    clauseNoToGenerate.add(16);
                    clauseNoToGenerate.add(18);
                }
            }
            case 11 -> {
                if (faedahBersamaType) {

                    clauseNoToGenerate.add(5);
                    clauseNoToGenerate.add(14);
                    clauseNoToGenerate.add(15);
                }
            }
            case 12 -> {
                if (nonBranchType) {
                    //generate 6 & 10 - Jenis Mesyuarat Agung

                    clauseNoToGenerate.add(6);
                    clauseNoToGenerate.add(10);
                }
                if (branchType) {

                    clauseNoToGenerate.add(6);
                    clauseNoToGenerate.add(10);
                    clauseNoToGenerate.add(15);
                    clauseNoToGenerate.add(16);
                    clauseNoToGenerate.add(18);
                }
            }
            case 14 -> {
                if (faedahBersamaType) {

                    clauseNoToGenerate.add(5);
                    clauseNoToGenerate.add(11);
                    clauseNoToGenerate.add(15);
                }
            }
            case 15 -> {
                if (branchType) {

                    clauseNoToGenerate.add(6);
                    clauseNoToGenerate.add(10);
                    clauseNoToGenerate.add(12);
                    clauseNoToGenerate.add(16);
                    clauseNoToGenerate.add(18);
                }
            }
            case 16 -> {
                if (branchType) {

                    //fasal 6 - Jenis Mesyuarat Agung, Tempoh Pelantikan Jawatankuasa, Kekerapan Mesyuarat Jawatankuasa
                    clauseNoToGenerate.add(6);
                    clauseNoToGenerate.add(10);
                    clauseNoToGenerate.add(12);
                    clauseNoToGenerate.add(15);
                    clauseNoToGenerate.add(18);

                    tugasanJawatanKuasaCawanganReset(content, method);
                    ajkTextTemplateConstructor(content, method);
                }
            }
            case 18 -> {
                if (branchType) {
                    //generate Fasal 9 - Tahun Kewangan Bermula
                    clauseNoToGenerate.add(9);
                    //generate Fasals - Jenis Mesyuarat Agung
                    clauseNoToGenerate.add(6);
                    clauseNoToGenerate.add(10);
                    clauseNoToGenerate.add(12);
                    clauseNoToGenerate.add(15);
                    clauseNoToGenerate.add(16);
                }
            }
        }

        generalConstructor(content, method, clauseNoToGenerate);
    }

    private void tugasanJawatanKuasaReset(ConstitutionContent content, String method) throws Exception {

        //To clear checkUpdate for Tugasan Jawatan Kuasa when Jawatan Kuasa is updated
        if (method.equals("update")) {
            ClauseContent tugasanJawatanKuasaClause = clauseContentReadDomainService.getByConsTypeAndClauseNo(content.getConstitutionTypeId(), 7L);

            ConstitutionContent tugasanJawatanKuasaConstitution;
            if (content.getAmendmentId() != null) {
                tugasanJawatanKuasaConstitution = constitutionContentReadDomainService.findByAmendmentIdAndClauseContentId(content.getSocietyId(), content.getAmendmentId(), tugasanJawatanKuasaClause.getId());
            } else {
                tugasanJawatanKuasaConstitution = constitutionContentReadDomainService.findBySocietyIdAndClauseContentId(content.getSocietyId(), tugasanJawatanKuasaClause.getId());
            }

            if (tugasanJawatanKuasaConstitution != null) {
                tugasanJawatanKuasaConstitution.setDescription(tugasanJawatanKuasaClause.getContent());
                tugasanJawatanKuasaConstitution.setCheckUpdate(0L);
                constitutionContentWriteDomainService.update(tugasanJawatanKuasaConstitution);
            }
        }
    }

    private void tugasanJawatanKuasaCawanganReset(ConstitutionContent content, String method) throws Exception {

        //To clear checkUpdate for Tugasan Jawatan Kuasa when Jawatan Kuasa is updated
        if (method.equals("update")) {
            ClauseContent tugasanJawatanKuasaClause = clauseContentReadDomainService.getByConsTypeAndClauseNo(content.getConstitutionTypeId(), 17L);

            ConstitutionContent tugasanJawatanKuasaConstitution;
            if (content.getAmendmentId() != null) {
                tugasanJawatanKuasaConstitution = constitutionContentReadDomainService.findByAmendmentIdAndClauseContentId(content.getSocietyId(), content.getAmendmentId(), tugasanJawatanKuasaClause.getId());
            } else {
                tugasanJawatanKuasaConstitution = constitutionContentReadDomainService.findBySocietyIdAndClauseContentId(content.getSocietyId(), tugasanJawatanKuasaClause.getId());
            }

            if (tugasanJawatanKuasaConstitution != null) {
                tugasanJawatanKuasaConstitution.setDescription(tugasanJawatanKuasaClause.getContent());
                tugasanJawatanKuasaConstitution.setCheckUpdate(0L);
                constitutionContentWriteDomainService.update(tugasanJawatanKuasaConstitution);
            }
        }
    }

    private void tugasanJawatanKuasaFaedahReset(ConstitutionContent content, String method) throws Exception {

        //To clear checkUpdate for Tugasan Jawatan Kuasa when Jawatan Kuasa is updated
        if (method.equals("update")) {
            ClauseContent tugasanJawatanKuasaClause = clauseContentReadDomainService.getByConsTypeAndClauseNo(content.getConstitutionTypeId(), 6L);

            ConstitutionContent tugasanJawatanKuasaConstitution;
            if (content.getAmendmentId() != null) {
                tugasanJawatanKuasaConstitution = constitutionContentReadDomainService.findByAmendmentIdAndClauseContentId(content.getSocietyId(), content.getAmendmentId(), tugasanJawatanKuasaClause.getId());
            } else {
                tugasanJawatanKuasaConstitution = constitutionContentReadDomainService.findBySocietyIdAndClauseContentId(content.getSocietyId(), tugasanJawatanKuasaClause.getId());
            }

            if (tugasanJawatanKuasaConstitution != null) {
                tugasanJawatanKuasaConstitution.setDescription(tugasanJawatanKuasaClause.getContent());
                tugasanJawatanKuasaConstitution.setCheckUpdate(0L);
                constitutionContentWriteDomainService.update(tugasanJawatanKuasaConstitution);
            }
        }
    }

    private ConstitutionContent constitutionContentBuilder(ConstitutionContent content, ClauseEnum clause) {
        ConstitutionContent newContent = new ConstitutionContent();
        if (content.getAmendmentId() != null) {
            newContent.setAmendmentId(content.getAmendmentId());
        }
        newContent.setSocietyId(content.getSocietyId());
        newContent.setConstitutionTypeId(content.getConstitutionTypeId());
        newContent.setClauseContentId((long) clause.getId());
        newContent.setClauseNo(String.valueOf(clause.getClauseNo()));
        newContent.setClauseName(clause.getClauseName());
        newContent.setApplicationStatusCode(content.getApplicationStatusCode());
        newContent.setStatus(content.getStatus());

//        newContent.setCreatedBy(content.getCreatedBy());
        newContent.setCreatedBy(0L); //to show system auto generated this Fasal
        return newContent;
    }

    private ConstitutionValue constitutionValueBuilder(ConstitutionContent content, ClauseEnum clause) {
        ConstitutionValue newValue = new ConstitutionValue();
        newValue.setConstitutionTypeId(content.getConstitutionTypeId());
        newValue.setConstitutionContentId(content.getId());
        if (content.getAmendmentId() != null) {
            newValue.setAmendmentId(content.getAmendmentId());
        }
        newValue.setSocietyId(content.getSocietyId());
        newValue.setSocietyNo(content.getSocietyNo());
        if (content.getClauseContentId() != null) {
            newValue.setClauseContentId(content.getClauseContentId());
        }
        if (content.getClauseNo() != null) {
            newValue.setClauseNo(content.getClauseNo());
        }
        if (content.getClauseName() != null) {
            newValue.setClauseName(content.getClauseName());
        }
        newValue.setStatusCode(content.getStatus());
        newValue.setApplicationStatusCode(content.getApplicationStatusCode());

//        newValue.setCreatedBy(content.getCreatedBy());
        newValue.setCreatedBy(0L); //to show system auto generated this Fasal
        return newValue;
    }

    private void generalConstructor(ConstitutionContent sourceContent, String method, List<Integer> clauseNoToGenerate) {
        try {
            //get sourceValue
            List<ConstitutionValue> sourceValue = constitutionValueReadDomainService.findByConstitutionContentId(sourceContent.getId());

            //do by clauseNoToGenerate
            for (Integer clauseNo : clauseNoToGenerate) {
                //do another general checker to add other titleValues
                Map<String, String> titleValue = additionalTitleValues(sourceContent, clauseNo);

                ClauseEnum dependentClause = ClauseEnum.getClauseByTypeAndNo(Math.toIntExact(sourceContent.getConstitutionTypeId()), clauseNo);

                // Create or get dependentContent only once per clause
                ConstitutionContent dependentContent = new ConstitutionContent();
                if (sourceContent.getAmendmentId() != null) {
                    dependentContent = constitutionContentReadDomainService
                            .findByAmendmentIdAndClauseContentId(sourceContent.getSocietyId(), sourceContent.getAmendmentId(), (long) dependentClause.getId());
                } else {
                    dependentContent = constitutionContentReadDomainService
                            .findBySocietyIdAndClauseContentId(sourceContent.getSocietyId(), (long) dependentClause.getId());
                }

                if (method.equals("update") && dependentContent == null) {
                    continue;
                }

                if (dependentContent == null) {
                    // Only create once for "create" method
                    dependentContent = constitutionContentBuilder(sourceContent, dependentClause);
                    if (sourceContent.getAmendmentToggle() != null) {
                        dependentContent.setAmendmentToggle(sourceContent.getAmendmentToggle());
                    }
                    dependentContent.setCheckUpdate(0L);
                    dependentContent.setStatus(sourceContent.getStatus());
                    dependentContent.setApplicationStatusCode(sourceContent.getApplicationStatusCode());
                    constitutionContentWriteDomainService.create(dependentContent);
                }

                // Process values only once
                for (ConstitutionValue eachSourceValue : sourceValue) {
                    if (titleValue.containsValue(eachSourceValue.getTitleName())) {
                        Map<String, Object> params = new HashMap<>();
                        params.put("titleName", eachSourceValue.getTitleName());

                        if (method.equals("update")) {
                            // Get the corresponding value from the dependent content
                            ConstitutionValue dependentValue = constitutionValueReadDomainService.findByParam(
                                    dependentContent.getId(),
                                    params);

                            if (dependentValue != null) {
                                // Update if values are different
                                if (!Objects.equals(eachSourceValue.getDefinitionName(), dependentValue.getDefinitionName())) {
                                    dependentContent.setCheckUpdate(0L);
                                    constitutionContentWriteDomainService.update(dependentContent);

                                    dependentValue.setDefinitionName(eachSourceValue.getDefinitionName());
                                    dependentValue.setModifiedBy(sourceContent.getModifiedBy());
                                    constitutionValueWriteDomainService.update(dependentValue);
                                }
                            }
                        } else {
                            // Create new value with the existing dependentContent
                            ConstitutionValue dependentValue = constitutionValueBuilder(dependentContent, dependentClause);
                            dependentValue.setTitleName(eachSourceValue.getTitleName());
                            dependentValue.setDefinitionName(eachSourceValue.getDefinitionName());
                            dependentValue.setSocietyName(eachSourceValue.getSocietyName());
                            dependentValue.setStatusCode(dependentContent.getStatus());
                            dependentValue.setApplicationStatusCode(dependentContent.getApplicationStatusCode());
                            constitutionValueWriteDomainService.create(dependentValue);
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private Map<String, String> additionalTitleValues(ConstitutionContent sourceContent, Integer dependentClauseNo) {
        //get source clause details
        ClauseEnum clauseEnum = ClauseEnum.getClause(Math.toIntExact(sourceContent.getClauseContentId()));

        final boolean nonBranchType = clauseEnum.getConstitutionType() == ConstitutionTypeEnum.IndukNGO.getId() ||
                clauseEnum.getConstitutionType() == ConstitutionTypeEnum.IndukAgama.getId();

        final boolean branchType = clauseEnum.getConstitutionType() == ConstitutionTypeEnum.CawanganNGO.getId() ||
                clauseEnum.getConstitutionType() == ConstitutionTypeEnum.CawanganAgama.getId();

        final boolean faedahBersamaType = clauseEnum.getConstitutionType() == ConstitutionTypeEnum.FaedahBersama.getId();

        Map<String, String> titleValue = new HashMap<>();

        if (branchType) {
            switch (dependentClauseNo) {
                case 6, 16:
                    titleValue.put("titleName", "Jenis Mesyuarat Agung");
                    titleValue.put("titleName2", "Tempoh Pelantikan Jawatankuasa");
                    titleValue.put("titleName3", "Kekerapan Mesyuarat Jawatankuasa");
                    break;
                case 9:
                    titleValue.put("titleName", "Tahun Kewangan Bermula (Tahun Kewangan Bermula Dari Tahun Kewangan Yang Dibenarkan)");
                    break;
                case 10:
                    titleValue.put("titleName", "Jenis Mesyuarat Agung");
                    titleValue.put("titleName2", "Tempoh Pelantikan Jawatankuasa");
                    break;
                case 12, 15:
                    titleValue.put("titleName", "Jenis Mesyuarat Agung");
                    break;
                case 18:
                    titleValue.put("titleName", "Jenis Mesyuarat Agung");
                    titleValue.put("titleName2", "Tahun Kewangan Bermula (Tahun Kewangan Bermula Dari Tahun Kewangan Yang Dibenarkan)");
                    break;
            }
        } else if (nonBranchType) {
            switch (dependentClauseNo) {
                case 6, 10:
                    titleValue.put("titleName", "Jenis Mesyuarat Agung");
                    titleValue.put("titleName2", "Tempoh Pelantikan Jawatankuasa");
                    break;
                case 12:
                    titleValue.put("titleName", "Jenis Mesyuarat Agung");
                    break;
            }
        } else if (faedahBersamaType) {
            switch (dependentClauseNo) {
                case 5, 11:
                    titleValue.put("titleName", "Jenis Mesyuarat Agung");
                    titleValue.put("titleName2", "Tempoh Pelantikan Jawatankuasa");
                    break;
                case 14, 15:
                    titleValue.put("titleName", "Jenis Mesyuarat Agung");
                    break;
            }
        }

        return titleValue;
    }

    private void ajkTextTemplateConstructor(ConstitutionContent content, String method) {
        try {
            ClauseEnum sourceClause = ClauseEnum.getClause(Math.toIntExact(content.getClauseContentId()));

            // Only proceed if not Bebas or BebasCawangan
            if (sourceClause.getConstitutionType() != ConstitutionTypeEnum.BebasCawangan.getId() &&
                    sourceClause.getConstitutionType() != ConstitutionTypeEnum.Bebas.getId()) {

                List<ConstitutionValue> sourceValue =
                        constitutionValueReadDomainService.findByConstitutionContentId(content.getId());

                ClauseEnum dependentClause = ClauseEnum.getClauseByTypeAndNo(
                        sourceClause.getConstitutionType(),
                        sourceClause.getClauseNo() + 1
                );

                // Generate dependent clause text
                String dependentDescription = templateModification(sourceValue, dependentClause);

                ConstitutionContent dependentContent;
                if (content.getAmendmentId() != null) {
                    dependentContent = constitutionContentReadDomainService
                            .findByAmendmentIdAndClauseContentId(
                                    content.getSocietyId(),
                                    content.getAmendmentId(),
                                    (long) dependentClause.getId());
                } else {
                    dependentContent = constitutionContentReadDomainService
                            .findBySocietyIdAndClauseContentId(
                                    content.getSocietyId(),
                                    (long) dependentClause.getId());
                }

                if (dependentContent == null) {
                    dependentContent = constitutionContentBuilder(content, dependentClause);
                    dependentContent.setCheckUpdate(0L);
                    dependentContent.setDescription(dependentDescription);
                    dependentContent.setStatus(content.getStatus());
                    dependentContent.setApplicationStatusCode(content.getApplicationStatusCode());
                    constitutionContentWriteDomainService.create(dependentContent);
                }

                // Update amendment toggle if exists
                if (content.getAmendmentToggle() != null) {
                    dependentContent.setAmendmentToggle(content.getAmendmentToggle());
                }

                dependentContent.setCheckUpdate(0L);
                dependentContent.setDescription(dependentDescription);
                constitutionContentWriteDomainService.update(dependentContent);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private String templateModification(List<ConstitutionValue> sourceValue, ClauseEnum dependentClause) {
        ClauseContent dependentTemplate = clauseContentReadDomainService.getByConsTypeAndClauseNo(
                (long) dependentClause.getConstitutionType(),
                (long) dependentClause.getClauseNo()
        );

        String template = dependentTemplate.getContent().replaceAll("\r?\n+", "\n");
        Map<String, String> replacementMap = new HashMap<>();
        Set<String> fieldsToSkip = positionAndNumberPairs(sourceValue);

        for (ConstitutionValue value : sourceValue) {
            if (!fieldsToSkip.contains(value.getTitleName())) {
                replacementMap.put(value.getTitleName(), Optional.ofNullable(value.getDefinitionName()).orElse(""));
            }
        }

        Set<String> preservedPlaceholders = Set.of(
                "jumlah wang tangan yang dibenarkan",
                "jumlah wang tangan yang dibenarkan-dalam perkataan"
        );

        List<String> processedLines = new ArrayList<>();
        Pattern placeholderPattern = Pattern.compile("<<([^>]+)>>");

        for (String line : template.split("\n")) {
            String originalIndent = line.replaceAll("[^\t].*$", "");
            String trimmed = line.trim();
            if (trimmed.isEmpty()) continue;

            Matcher matcher = placeholderPattern.matcher(trimmed);
            boolean shouldSkip = false;
            StringBuffer replaced = new StringBuffer();

            while (matcher.find()) {
                String key = matcher.group(1);
                if (preservedPlaceholders.contains(key)) continue;

                String replacement = replacementMap.get(key);
                if (replacement == null || replacement.isBlank()) {
                    shouldSkip = true;
                    break;
                }
                matcher.appendReplacement(replaced, Matcher.quoteReplacement(replacement));
            }

            if (!shouldSkip) {
                matcher.appendTail(replaced);
                processedLines.add(originalIndent + replaced.toString());
            }
        }

        // === Re-numbering logic ===
        List<String> resultLines = new ArrayList<>();
        int mainCounter = 0;
        int i = 0;

        while (i < processedLines.size()) {
            String line = processedLines.get(i);
            String trimmed = line.trim();

            if (trimmed.matches("^\\d+\\.\\s.*")) {
                if (mainCounter > 0) resultLines.add("");
                mainCounter++;
                resultLines.add(mainCounter + ". " + trimmed.substring(trimmed.indexOf('.') + 1).trim());
                i++;

                // handle lettered subpoints (a., b., c.) — renumbering them
                char letter = 'a';
                while (i < processedLines.size()) {
                    String subLine = processedLines.get(i).trim();
                    if (subLine.matches("^[a-h]\\..*")) {
                        resultLines.add("\t" + letter + ". " + subLine.substring(subLine.indexOf('.') + 1).trim());
                        letter++;
                        i++;
                    } else if (subLine.matches("^(i{1,3}|iv|v|vi{0,3}|vii|viii|ix|x)\\..*")) {
                        // keep roman numerals exactly as-is with double tab
                        resultLines.add("\t\t" + subLine);
                        i++;
                    } else {
                        break;
                    }
                }
            } else {
                resultLines.add(line);
                i++;
            }
        }

        return String.join("\n", resultLines);
    }

    private void sumberKewanganTextTemplateConstructor(ConstitutionContent content, String method) {
        //get source values
        List<ConstitutionValue> sourceValue = constitutionValueReadDomainService.findByConstitutionContentId(content.getId());
        Optional<ConstitutionValue> match = sourceValue.stream()
                .filter(c -> "Ahli Seumur Hidup".equals(c.getTitleName()))
                .findFirst();

        String definitionName = match.map(ConstitutionValue::getDefinitionName).orElse(null);
        ClauseEnum dependentClause = ClauseEnum.getClauseByTypeAndName(Math.toIntExact(content.getConstitutionTypeId()), "Sumber Kewangan");
        ClauseContent clauseContent = clauseContentReadDomainService.getByConsTypeAndClauseName(content.getConstitutionTypeId(), "Sumber Kewangan");
        String descriptionContent;
        if (definitionName.equals("Tiada")) {
            descriptionContent = this.sumberKewanganTemplateModification(clauseContent.getContent());
        } else {
            descriptionContent = clauseContent.getContent();
        }

        //dependent content
        ConstitutionContent dependentContent;
        if (content.getAmendmentId() != null) {
            dependentContent = constitutionContentReadDomainService
                    .findByAmendmentIdAndClauseContentId(
                            content.getSocietyId(),
                            content.getAmendmentId(),
                            clauseContent.getId());
        } else {
            dependentContent = constitutionContentReadDomainService
                    .findBySocietyIdAndClauseContentId(
                            content.getSocietyId(),
                            clauseContent.getId());
        }

        try {
            if (dependentContent == null) {
                dependentContent = constitutionContentBuilder(content, dependentClause);
                dependentContent.setCheckUpdate(0L);
                dependentContent.setDescription(descriptionContent);
                dependentContent.setStatus(content.getStatus());
                dependentContent.setApplicationStatusCode(content.getApplicationStatusCode());
                constitutionContentWriteDomainService.create(dependentContent);
            }

            // Update amendment toggle if exists
            if (content.getAmendmentToggle() != null) {
                dependentContent.setAmendmentToggle(content.getAmendmentToggle());
            }

            dependentContent.setCheckUpdate(0L);
            dependentContent.setDescription(descriptionContent);
            constitutionContentWriteDomainService.update(dependentContent);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private String sumberKewanganTemplateModification(String content) {
        if (content == null || content.isBlank()) return content;

        StringBuilder result = new StringBuilder();
        String[] lines = content.split("\n", -1); // keep empty lines
        char currentLetter = 'a';
        boolean insideSubpoints = false;

        for (String line : lines) {
            String trimmed = line.trim();

            // Skip the specific "b. Yuran seumur hidup..." line
            if (trimmed.matches("^b\\.\\s*Yuran seumur hidup.*")) {
                continue;
            }

            // Renumber lines starting with subpoint letters like a. b. c. ...
            if (trimmed.matches("^[a-zA-Z]\\.\\s+.*")) {
                // Preserve original indentation
                String leadingSpaces = line.substring(0, line.indexOf(trimmed));
                String restOfLine = trimmed.replaceFirst("^[a-zA-Z]\\.", currentLetter + ".");
                result.append(leadingSpaces).append(restOfLine).append("\n");
                currentLetter++;
            } else {
                result.append(line).append("\n");
            }
        }

        return result.toString();
    }

    private Set<String> positionAndNumberPairs(List<ConstitutionValue> sourceValue) {
        // Define related pairs to check
        Map<String, String> relatedPairs = Map.of(
                "Bilangan Pengerusi", "Pengerusi",
                "Bilangan Timbalan Pengerusi", "Timbalan Pengerusi",
                "Bilangan Naib Pengerusi", "Naib Pengerusi",
                "Bilangan Setiausaha", "Setiausaha",
                "Bilangan Penolong Setiausaha", "Penolong Setiausaha",
                "Bilangan Bendahari", "Bendahari",
                "Bilangan Penolong Bendahari", "Penolong Bendahari",
                "Bilangan Ahli Jawatankuasa Biasa", "Ahli Jawatankuasa Biasa"
        );
        Set<String> fieldsToSkip = new HashSet<>();

        // First pass - identify fields to skip
        for (ConstitutionValue value : sourceValue) {
            if (relatedPairs.containsKey(value.getTitleName())) {
                String definition = value.getDefinitionName();
                if (definition == null || definition.isEmpty() || "0".equals(definition)) {
                    // Skip both this field and its related field
                    fieldsToSkip.add(value.getTitleName());
                    fieldsToSkip.add(relatedPairs.get(value.getTitleName()));
                }
            }
        }

        return fieldsToSkip;
    }

    public void generateAndSyncClause2(Society society) {
        try {
            ConstitutionTypeEnum constitutionTypeEnum = ConstitutionTypeEnum.getConstitutionType(society.getConstitutionType());

            // Get clause 2 content
            ClauseEnum clause2 = ClauseEnum.getClauseByTypeAndNo(constitutionTypeEnum.getId(), 2);
            ConstitutionContent clause2Content = constitutionContentReadDomainService
                    .findBySocietyIdAndClauseContentId(society.getId(), (long) clause2.getId());

            if (clause2Content == null) {
                ClauseContent clause2Template = clauseContentReadDomainService.getByConsTypeAndClauseNo((long) constitutionTypeEnum.getId(), (long) clause2.getClauseNo());

                clause2Content = new ConstitutionContent();
                clause2Content.setSocietyId(society.getId());
                clause2Content.setConstitutionTypeId(Long.valueOf(constitutionTypeEnum.getId()));
                clause2Content.setApplicationStatusCode((long) ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode());
                clause2Content.setStatus(StatusCode.AKTIF.getCode());
                clause2Content.setCreatedBy(society.getCreatedBy());
                clause2Content.setCheckUpdate(0L);

                clause2Content.setDescription(clause2Template.getContent());

                clause2Content = constitutionContentBuilder(clause2Content, clause2);
                constitutionContentWriteDomainService.create(clause2Content);
            }

            // Get existing clause 2 values
            List<ConstitutionValue> clause2Values = constitutionValueReadDomainService
                    .findByConstitutionContentId(clause2Content.getId());

            // Convert to map for fast lookup
            Map<String, ConstitutionValue> existingValuesMap = clause2Values.stream()
                    .collect(Collectors.toMap(ConstitutionValue::getTitleName, Function.identity(), (a, b) -> a));

            // Map for Clause 2 (addresses)
            Map<String, String> addresses = this.mapAddressAndClause2(society);

            for (Map.Entry<String, String> entry : addresses.entrySet()) {
                String title = entry.getKey();
                String definition = entry.getValue();

                ConstitutionValue clause2Value = existingValuesMap.get(title);

                if (clause2Value == null) {
                    clause2Value = constitutionValueBuilder(clause2Content, clause2);
                    clause2Value.setTitleName(title);
                    clause2Value.setDefinitionName(definition);
                    clause2Value.setSocietyName(society.getSocietyName());
                    clause2Value.setStatusCode(clause2Content.getStatus());
                    clause2Value.setApplicationStatusCode(clause2Content.getApplicationStatusCode());
                    constitutionValueWriteDomainService.create(clause2Value);
                } else {
                    if (!Objects.equals(clause2Value.getDefinitionName(), definition)) {
                        clause2Value.setDefinitionName(definition);
                        clause2Value.setModifiedBy(society.getModifiedBy());
                        constitutionValueWriteDomainService.update(clause2Value);
                    }
                }
            }

        } catch (Exception e) {
            throw new RuntimeException("Failed to generate and sync Clause 2", e);
        }
    }

    private Map<String, String> mapAddressAndClause2(Society society) {
        Map<String, String> addresses = new HashMap<>();

        addresses.put("Alamat Tempat Urusan", society.getAddress() != null ? society.getAddress() : "");
        addresses.put("Negeri", society.getStateCode() != null ? society.getStateCode() : "");
        addresses.put("Daerah", society.getDistrictCode() != null ? society.getDistrictCode() : "");
        addresses.put("Bandar", society.getCity() != null ? society.getCity() : "");
        addresses.put("Poskod", society.getPostcode() != null ? society.getPostcode() : "");

        addresses.put("Alamat Surat Menyurat", society.getMailingAddress() != null ? society.getMailingAddress() : "");
        addresses.put("Negeri Surat Menyurat", society.getMailingStateCode() != null ? society.getMailingStateCode() : "");
        addresses.put("Daerah Surat Menyurat", society.getMailingDistrictCode() != null ? society.getMailingDistrictCode() : "");
        addresses.put("Bandar Surat Menyurat", society.getMailingCity() != null ? society.getMailingCity() : "");
        addresses.put("Poskod Surat Menyurat", society.getMailingPostcode() != null ? society.getMailingPostcode() : "");

        return addresses;
    }
}
