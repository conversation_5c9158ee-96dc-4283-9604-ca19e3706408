package com.eroses.external.society.service;

import com.eroses.external.society.mappers.ExternalAgencyReviewDao;
import com.eroses.external.society.model.ExternalAgencyReview;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExternalAgencyReviewReadDomainService {
    private final ExternalAgencyReviewDao externalAgencyReviewDao;

    public ExternalAgencyReview findById(Long id) {
        return externalAgencyReviewDao.findById(id);
    }

    public List<ExternalAgencyReview> getAllBySocietyId(Long societyId) {
        return externalAgencyReviewDao.getAllBySocietyId(societyId);
    }

    public ExternalAgencyReview getLatestBySocietyId(Long societyId) {
        return externalAgencyReviewDao.getLatestBySocietyId(societyId);
    }
}
