package com.eroses.external.society.service.admin;

import com.eroses.config.cache.CacheNames;
import com.eroses.external.society.dto.database.lookup.DistrictWithState;
import com.eroses.external.society.model.AdmAddresses;
import com.eroses.external.society.mappers.AdmAddressesDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmAddressesReadDomainService {
    private final AdmAddressesDao admAddressesDao;

    @Cacheable(value = CacheNames.ADDRESSES, key = "'all_addresses'")
    public List<AdmAddresses> getAllAdmAddresses() {
        log.info("Fetching all addresses from database (cache miss)");
        return admAddressesDao.findAll();
    }

//    @Cacheable(value = CacheNames.ADDRESSES, key = "'address_' + #id")
    public AdmAddresses findById(Long id) {
        log.info("Fetching address by id {} from database (cache miss)", id);
        return admAddressesDao.findById(id);
    }

    @Cacheable(value = CacheNames.ADDRESSES, key = "'countries_' + #nameQuery + '_' + #offset + '_' + #limit")
    public List<AdmAddresses> getAllCountries(String nameQuery, Integer offset, Integer limit) {
        log.info("Fetching countries with query '{}', offset {}, limit {} from database (cache miss)", nameQuery, offset, limit);
        Map<String, Object> params = new HashMap<>();
        params.put("nameQuery", nameQuery);
        params.put("offset", offset);
        params.put("limit", limit);
        params.put("level", 0);
        return admAddressesDao.getAll(params);
    }

    public Long countAllCountries(String nameQuery) {
        Map<String, Object> params = new HashMap<>();
        params.put("nameQuery", nameQuery);
        params.put("level", 0);
        return admAddressesDao.countAll(params);
    }

    public boolean countryExistsByCode(String code) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("level", 0);
        return admAddressesDao.existsByCode(params);
    }

    public boolean countryExistsByCodeExcludingId(String code, Long id) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("id", id);
        params.put("level", 0);
        return admAddressesDao.existsByCodeExcludingId(params);
    }

    public List<AdmAddresses> getAllStates(String nameQuery, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        params.put("nameQuery", nameQuery);
        params.put("offset", offset);
        params.put("limit", limit);
        params.put("level", 1);
        return admAddressesDao.getAll(params);
    }

    public List<AdmAddresses> getAllActiveStates() {
        Map<String, Object> params = new HashMap<>();
        params.put("level", 1);
        return admAddressesDao.getAllActive(params);
    }

    public Long countAllStates(String nameQuery) {
        Map<String, Object> params = new HashMap<>();
        params.put("nameQuery", nameQuery);
        params.put("level", 1);
        return admAddressesDao.countAll(params);
    }

    public boolean stateExistsByCode(String code) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("level", 1);
        return admAddressesDao.existsByCode(params);
    }

    public boolean stateExistsByCodeExcludingId(String code, Long id) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("id", id);
        params.put("level", 1);
        return admAddressesDao.existsByCodeExcludingId(params);
    }

    public List<DistrictWithState> getAllDistricts(String nameQuery, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        params.put("nameQuery", nameQuery);
        params.put("offset", offset);
        params.put("limit", limit);
        return admAddressesDao.getAllDistricts(params);
    }

    public List<AdmAddresses> getAllActiveDistricts() {
        Map<String, Object> params = new HashMap<>();
        params.put("level", 2);
        return admAddressesDao.getAllActive(params);
    }

    public Long countAllDistricts(String nameQuery) {
        Map<String, Object> params = new HashMap<>();
        params.put("nameQuery", nameQuery);
        params.put("level", 2);
        return admAddressesDao.countAll(params);
    }

    public boolean districtExistsByCode(String code) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("level", 2);
        return admAddressesDao.existsByCode(params);
    }

    public boolean districtExistsByCodeExcludingId(String code, Long id) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("id", id);
        params.put("level", 2);
        return admAddressesDao.existsByCodeExcludingId(params);
    }
}
