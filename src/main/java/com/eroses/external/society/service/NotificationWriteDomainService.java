package com.eroses.external.society.service;


import com.eroses.external.society.mappers.NotificationDao;
import com.eroses.external.society.model.Notification;
import com.eroses.external.society.utils.Assert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationWriteDomainService {
    private final NotificationDao notificationDao;

    public Long create(Notification notification) throws Exception {
        notificationDao.create(notification);
        return notification.getId();
    }

    public Boolean edit(Notification notification) {
        boolean isOk = notificationDao.edit(notification);
        Assert.isTrue(isOk, "Update notification is unsuccessful.");

        return isOk;
    }
}
