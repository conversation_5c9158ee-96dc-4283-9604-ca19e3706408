package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmMeetingDao;
import com.eroses.external.society.model.AdmAddresses;
import com.eroses.external.society.model.AdmMeeting;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class AdmMeetingReadDomainService {

    private final AdmMeetingDao admMeetingDao;


    public List<AdmMeeting> getAllMeetingType() {
        return admMeetingDao.findAll();
    }
}