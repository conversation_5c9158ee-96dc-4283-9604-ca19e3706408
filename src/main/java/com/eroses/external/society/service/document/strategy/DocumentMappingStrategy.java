
package com.eroses.external.society.service.document.strategy;

import com.eroses.external.society.model.DocumentTemplate;
import java.util.Map;

/**
 * Strategy interface for document mapping operations.
 * Each document type will have its own implementation of this interface.
 */
public interface DocumentMappingStrategy {
    
    /**
     * Maps data to a document template and returns the processed HTML.
     *
     * @param templateCode The code of the document template
     * @param societyId The ID of the society
     * @param additionalParams Additional parameters needed for specific document types
     * @return The processed HTML content
     * @throws Exception If an error occurs during document generation
     */
    String mapDataToDocument(String templateCode, Long societyId, Map<String, Object> additionalParams) throws Exception;
    
    /**
     * Checks if this strategy can handle the given document template code.
     *
     * @param templateCode The code of the document template
     * @return true if this strategy can handle the template, false otherwise
     */
    boolean canHandle(String templateCode);

    /**
     * Gets the document template by code.
     *
     * @param templateCode The code of the template
     * @return The document template
     * @throws Exception If the template is not found
     */
    DocumentTemplate getDocumentTemplate(String templateCode) throws Exception;

    /**
     * Gets the filename of the document.
     *
     * @param documentTemplate The document template
     * @return The file name
     * @throws Exception If the template is not found
     */
    String getFileName(DocumentTemplate documentTemplate, Long societyId, Map<String, Object> additionalParams) throws Exception;
}
