package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmOccupationDao;
import com.eroses.external.society.model.lookup.AdmOccupation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdmOccupationWriteDomainService {
    private final AdmOccupationDao admOccupationDao;

    public Long create(AdmOccupation occupation) throws Exception {
        admOccupationDao.create(occupation);
        return occupation.getId();
    }

    public boolean update(AdmOccupation occupation) throws Exception {
        return admOccupationDao.update(occupation);
    }

    public Long delete(Long id) throws Exception {
        admOccupationDao.delete(id);
        return id;
    }
}