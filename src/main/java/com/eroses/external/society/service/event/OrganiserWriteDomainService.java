package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.OrganiserDao;
import com.eroses.external.society.model.Organiser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrganiserWriteDomainService {
    private final OrganiserDao organiserDao;

    public Organiser create(Organiser org){
        log.info("Creating Organiser..");
        int isCreateOrg = organiserDao.create(org);
        if(isCreateOrg > 0){
            return org;
        }else{
            return null;
        }
    }

    public Organiser update(Organiser org){
        log.info("Updating Organiser..");
        Boolean updateOrg = organiserDao.update(org);
        if(updateOrg){
            return org;
        }else{
            return null;
        }
    }
}

