package com.eroses.external.society.service;

import com.eroses.external.society.mappers.SocietyDao;
import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.PaymentStatus;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyWriteDomainService {
    private final SocietyReadDomainService societyReadDomainService;
    private final ConstitutionValueReadDomainService constitutionValueReadDomainService;
    private final SocietyDao societyDao;

    public Long create(Society society) throws Exception {
        societyDao.create(society);
        return society.getId();
    }

//    @CacheEvict(value = {"searchSocietiesByCriteria", "countSearchedSocietiesByCriteria"}, allEntries = true)
    public boolean update(Society society) throws Exception {
        return societyDao.update(society);
    }

    public boolean update(Society society, User user) throws Exception {
        society.setModifiedBy(user.getId());
        return societyDao.update(society);
    }

    public boolean updateSocietyStatus(Society society, int paymentStatusCode, String paymentMethod) throws Exception {

        if(paymentStatusCode == PaymentStatus.PAID.getCode()){
            society.setSubmissionDate(LocalDate.now());
            society.setApplicationStatusCode(ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode());
            society.setPaymentDate(LocalDate.now());
            society.setPaymentMethod(paymentMethod);
        }else {
            society.setApplicationStatusCode(ApplicationStatusCode.BAYARAN_GAGAL.getCode());
        }
        society.setModifiedBy(0L);
        return societyDao.update(society);
    }

    public Society updateRoDecision(Society society, User user, int applicationStatusCode, String status) throws Exception {

        // Update application status code and status based on RO decision
        society.setApplicationStatusCode(applicationStatusCode);
        society.setStatusCode(status);
        society.setModifiedBy(user.getId());
        societyDao.update(society);
        return society;
    }

    public Boolean resubmitSocietyApplication(List<Long> ids, int applicationStatusCode) {
        Map<String, Object> params = Map.of(
                "ids", ids,
                "applicationStatusCode", applicationStatusCode
        );
        return societyDao.resubmitSocietyApplication(params);
    }

    public Boolean updateExpiredSociety(List<Long> ids, int applicationStatusCode, String statusCode) {
        Map<String, Object> params = Map.of(
                "ids", ids,
                "applicationStatusCode", applicationStatusCode,
                "statusCode", statusCode
        );
        return societyDao.updateExpiredSociety(params);
    }

    public void updateSocietyAddressFromFasal2(ConstitutionContent content) throws Exception {
        Society society = societyReadDomainService.findById(content.getSocietyId());

        List<ConstitutionValue> clause2Values = constitutionValueReadDomainService.findByConstitutionContentId(content.getId());
        Map<String, String> existingValuesMap = clause2Values.stream()
                .collect(Collectors.toMap(
                        ConstitutionValue::getTitleName,
                        cv -> Optional.ofNullable(cv.getDefinitionName()).orElse(""),
                        (a, b) -> a // keep the first if duplicate
                ));

        Map<String, Consumer<String>> titleToSetter = new HashMap<>();
        titleToSetter.put("Alamat Tempat Urusan", society::setAddress);
        titleToSetter.put("Negeri", society::setStateCode);
        titleToSetter.put("Daerah", society::setDistrictCode);
        titleToSetter.put("Bandar", society::setCity);
        titleToSetter.put("Poskod", society::setPostcode);
        titleToSetter.put("Alamat Surat Menyurat", society::setMailingAddress);
        titleToSetter.put("Negeri Surat Menyurat", society::setMailingStateCode);
        titleToSetter.put("Daerah Surat Menyurat", society::setMailingDistrictCode);
        titleToSetter.put("Bandar Surat Menyurat", society::setMailingCity);
        titleToSetter.put("Poskod Surat Menyurat", society::setMailingPostcode);

        for (Map.Entry<String, String> eachValue : existingValuesMap.entrySet()) {
            Consumer<String> setter = titleToSetter.get(eachValue.getKey());
            if (setter != null && eachValue.getValue() != null) {
                setter.accept(eachValue.getValue());
            }
        }

        societyDao.update(society);
    }

    public void updateSocietyInfoFromFasal1(ConstitutionContent content) throws Exception {
        //reuse the logic in amendment service
        Society society = societyReadDomainService.findById(content.getSocietyId());
        List<ConstitutionValue> values =
                constitutionValueReadDomainService.findByConstitutionContentId(content.getId());

        Map<String, Consumer<String>> setterMap = Map.of(
                "Nama Pertubuhan", society::setSocietyName,
                "Taraf Pertubuhan", society::setSocietyLevel,
                "Singkatan Nama", society::setShortName
        );

        for (ConstitutionValue value : values) {
            String title = value.getTitleName();
            String definition = value.getDefinitionName();

            if (title != null && definition != null) {
                Consumer<String> setter = setterMap.get(title);
                if (setter != null) {
                    setter.accept(definition);
                }
            }
        }

        society.setModifiedBy(0L);
        society.setModifiedDate(LocalDateTime.now());
        societyDao.update(society);
    }
}
