package com.eroses.external.society.service;

import com.eroses.external.society.mappers.PropertyOfficerDao;
import com.eroses.external.society.model.PropertyOfficer;
import com.eroses.external.society.model.PropertyOfficerApplication;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class PropertyOfficerReadDomainService {

    private final PropertyOfficerDao propertyOfficerDao;

    public PropertyOfficerApplication findApplicationById(Long propertyApplicationId) {
        return propertyOfficerDao.findApplicationById(propertyApplicationId);
    }

    public PropertyOfficerApplication findByPaymentId(Long paymentId) {
        return propertyOfficerDao.findByPaymentId(paymentId);
    }

    public List<PropertyOfficerApplication> getSocietyPendingPropertyOfficerByCriteria(Integer applicationStatusCode, String stateCode, Long roId, String categoryCode, String subCategoryCode, String societyName, Integer offset, Integer limit) {
        return propertyOfficerDao.getAllSocietyPending(Map.of("applicationStatusCode", applicationStatusCode, "stateCode", stateCode, "ro", roId, "categoryCode", categoryCode, "subCategoryCode", subCategoryCode, "societyName", societyName, "offset", offset, "limit", limit,"deleteApplicationStatusCode",String.valueOf(ApplicationStatusCode.PADAM.getCode())));
    }

    public List<PropertyOfficerApplication> getBranchPendingPropertyOfficerApplication(Integer applicationStatusCode, String stateCode, Long roId, String stateCodeFilter, String searchQuery, Integer offset, Integer limit) {
        return propertyOfficerDao.getAllBranchPending(Map.of("searchQuery", searchQuery,"stateCodeFilter", stateCodeFilter, "applicationStatusCode", applicationStatusCode, "stateCode", stateCode, "ro", roId, "offset", offset, "limit", limit,"deleteApplicationStatusCode",String.valueOf(ApplicationStatusCode.PADAM.getCode())));
    }

    public Long countSocietyPendingPropertyOfficerByCriteria(Integer applicationStatusCode, String stateCode, Long roId, String categoryCode, String subCategoryCode, String societyName) {
        return propertyOfficerDao.countAllSocietyPending(Map.of("applicationStatusCode", applicationStatusCode, "stateCode", stateCode, "ro", roId, "categoryCode", categoryCode, "subCategoryCode", subCategoryCode, "societyName", societyName,"deleteApplicationStatusCode",String.valueOf(ApplicationStatusCode.PADAM.getCode())));
    }

    public Long countBranchPendingPropertyOfficerByCriteria(Integer applicationStatusCode, String stateCode, Long roId, String stateCodeFilter, String searchQuery) {
        return propertyOfficerDao.countAllBranchPending(Map.of("searchQuery", searchQuery,"stateCodeFilter", stateCodeFilter,"applicationStatusCode", applicationStatusCode, "stateCode", stateCode, "ro", roId,"deleteApplicationStatusCode",String.valueOf(ApplicationStatusCode.PADAM.getCode())));
    }

    public PropertyOfficerApplication findSocietyActivePropertyOfficerApplication(Long societyId) {
        return propertyOfficerDao.findApplicationByCriteria(Map.of("societyId", societyId, "applicationStatusCode", String.valueOf(ApplicationStatusCode.LULUS.getCode()), "status", ApplicationStatusCode.AKTIF.getCode(),"deleteApplicationStatusCode",String.valueOf(ApplicationStatusCode.PADAM.getCode())));
    }

    public PropertyOfficerApplication findBranchActivePropertyOfficerApplication(Long societyId, Long branchId) {
        return propertyOfficerDao.findApplicationByCriteria(Map.of("societyId", societyId, "branchId", branchId, "applicationStatusCode", String.valueOf(ApplicationStatusCode.LULUS.getCode()), "status", ApplicationStatusCode.AKTIF.getCode(),"deleteApplicationStatusCode",String.valueOf(ApplicationStatusCode.PADAM.getCode())));
    }

    public List<PropertyOfficerApplication> findAllSocietyPropertyOfficerApplication(Long societyId, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        params.put("deleteApplicationStatusCode", String.valueOf(ApplicationStatusCode.PADAM.getCode()));
        if (offset != null) params.put("offset", offset);
        if (limit != null) params.put("limit", limit);
        return propertyOfficerDao.findAllApplicationByCriteria(params);
    }

    public List<PropertyOfficerApplication> findAllBranchPropertyOfficerApplication(Long societyId, Long branchId, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        params.put("deleteApplicationStatusCode", String.valueOf(ApplicationStatusCode.PADAM.getCode()));
        if (offset != null) params.put("offset", offset);
        if (limit != null) params.put("limit", limit);
        return propertyOfficerDao.findAllApplicationByCriteria(params);
    }

    public Long countAllSocietyPropertyOfficerApplication(Long societyId) {
        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        params.put("deleteApplicationStatusCode", String.valueOf(ApplicationStatusCode.PADAM.getCode()));
        return propertyOfficerDao.countAllApplicationByCriteria(params);
    }

    public Long countAllBranchPropertyOfficerApplication(Long societyId, Long branchId) {
        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        params.put("deleteApplicationStatusCode", String.valueOf(ApplicationStatusCode.PADAM.getCode()));
        return propertyOfficerDao.countAllApplicationByCriteria(params);
    }

    public List<PropertyOfficer> findSocietyActivePropertyOfficers(Long societyId) {
        PropertyOfficerApplication propertyOfficerApplication = propertyOfficerDao.findLatestPropertyOfficerApplication(Map.of("societyId", societyId,"deleteApplicationStatusCode",String.valueOf(ApplicationStatusCode.PADAM.getCode())));
        if(propertyOfficerApplication==null){
            return Collections.emptyList();
        }
        return propertyOfficerDao.findOfficerByApplicationId(propertyOfficerApplication.getId());
    }

    public List<PropertyOfficer> findBranchActivePropertyOfficers(Long societyId, Long branchId) {
        PropertyOfficerApplication propertyOfficerApplication = propertyOfficerDao.findLatestPropertyOfficerApplication(Map.of("societyId", societyId, "branchId", branchId,"deleteApplicationStatusCode",String.valueOf(ApplicationStatusCode.PADAM.getCode())));
        if(propertyOfficerApplication==null){
            return Collections.emptyList();
        }
        return propertyOfficerDao.findOfficerByApplicationId(propertyOfficerApplication.getId());
    }

    public List<PropertyOfficer> findByApplicationId(Long id){
        return propertyOfficerDao.findOfficerByApplicationId(id);
    }

    public Long countSocietyPropertyOfficer(Long societyId) {
        return propertyOfficerDao.countPropertyOfficer(Map.of("societyId", societyId,"deleteApplicationStatusCode",String.valueOf(ApplicationStatusCode.PADAM.getCode())));
    }

    public Long countBranchPropertyOfficer(Long societyId, Long branchId) {
        return propertyOfficerDao.countPropertyOfficer(Map.of("societyId", societyId, "branchId", branchId,"deleteApplicationStatusCode",String.valueOf(ApplicationStatusCode.PADAM.getCode())));
    }

    public List<PropertyOfficer> paginateSocietyPropertyOfficer(Long societyId, Integer offset, Integer limit) {
        return propertyOfficerDao.findAllPropertyOfficer(Map.of("societyId", societyId, "offset", offset, "limit", limit,"deleteApplicationStatusCode",String.valueOf(ApplicationStatusCode.PADAM.getCode())));
    }

    public List<PropertyOfficer> paginateBranchPropertyOfficer(Long societyId, Long branchId, Integer offset, Integer limit) {
        return propertyOfficerDao.findAllPropertyOfficer(Map.of("societyId", societyId, "branchId", branchId, "offset", offset, "limit", limit,"deleteApplicationStatusCode",String.valueOf(ApplicationStatusCode.PADAM.getCode())));
    }

    public List<PropertyOfficerApplication> getAllPropertyOfficerApplicationPendingApproval(int applicationStatusCode, List<Integer> daysAfterSubmissionList) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "daysAfterSubmissionList", daysAfterSubmissionList);
        return propertyOfficerDao.getAllPropertyOfficerApplicationPendingApproval(params);
    }

    public boolean existsOnGoingSocietyPropertyOfficerApplication(List<Integer> applicationStatusCodes, Long societyId) {
        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null && !applicationStatusCodes.isEmpty()) params.put("applicationStatusCodes", applicationStatusCodes);
        if (societyId != null) params.put("societyId", societyId);
        return propertyOfficerDao.existsOnGoingSocietyPropertyOfficerApplication(params);
    }

    public boolean existsOnGoingBranchPropertyOfficerApplication(List<Integer> applicationStatusCodes, Long branchId) {
        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null && !applicationStatusCodes.isEmpty()) params.put("applicationStatusCodes", applicationStatusCodes);
        if (branchId != null) params.put("branchId", branchId);
        return propertyOfficerDao.existsOnGoingBranchPropertyOfficerApplication(params);
    }
}
