package com.eroses.external.society.service.document.strategy.impl;

import com.eroses.external.society.model.AdmAddresses;
import com.eroses.external.society.model.DocumentTemplate;
import com.eroses.external.society.model.Society;
import com.eroses.external.society.model.enums.*;
import com.eroses.external.society.service.DocumentTemplateReadDomainService;
import com.eroses.external.society.service.S3DomainService;
import com.eroses.external.society.service.SocietyReadDomainService;
import com.eroses.external.society.service.admin.AdmAddressesReadDomainService;
import com.eroses.external.society.service.document.strategy.AbstractDocumentMappingStrategy;
import com.eroses.external.society.service.pdf.PdfService;
import com.eroses.external.society.service.utils.HtmlGeneratorService;
import com.eroses.external.society.utils.RoDecisionUtilities;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Strategy implementation for Surat Iringan Kelulusan document type.
 */
@Slf4j
@Component
public class SuratIringanKelulusanStrategy extends AbstractDocumentMappingStrategy {

    private final AdmAddressesReadDomainService admAddressesReadDomainService;
    private final RoDecisionUtilities roDecisionUtilities;

    public SuratIringanKelulusanStrategy(
            DocumentTemplateReadDomainService documentTemplateReadDomainService,
            S3DomainService s3DomainService,
            PdfService pdfService,
            HtmlGeneratorService htmlGeneratorService,
            UserFacade userFacade,
            SocietyReadDomainService societyReadDomainService,
            AdmAddressesReadDomainService admAddressesReadDomainService,
            RoDecisionUtilities roDecisionUtilities) {
        super(documentTemplateReadDomainService, s3DomainService, pdfService, htmlGeneratorService, userFacade, societyReadDomainService);
        this.admAddressesReadDomainService = admAddressesReadDomainService;
        this.roDecisionUtilities = roDecisionUtilities;
    }

    @Override
    public String mapDataToDocument(String templateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        DocumentTemplate template = getDocumentTemplate(templateCode);
        String htmlTemplate = template.getHtmlContent();

        Map<String, String> mapFields = prepareDataForMapping(societyId, additionalParams);
        String processedHtml = htmlGeneratorService.processConditionals(htmlTemplate, mapFields);
        return htmlGeneratorService.mapToHtml(processedHtml, mapFields);
    }

    @Override
    protected Map<String, String> prepareDataForMapping(Long societyId, Map<String, Object> additionalParams) throws Exception {

        Society society = societyReadDomainService.findById(societyId);
        AdmAddresses societyState = admAddressesReadDomainService.findById(Long.valueOf(society.getStateCode()));
        SocietyCategoryEnum societyCategory = SocietyCategoryEnum.getCategoryById(Integer.valueOf(society.getCategoryCodeJppm()));

        //Get State Director of the state (Should have only 1 state director per state)
        List<User> stateDirectors = roDecisionUtilities.getAssignedApprovalOfficer(UserRoleEnum.PENOLONG_PENDAFTAR_PERTUBUHAN, society.getStateCode(), societyCategory);
        String directorName = stateDirectors.isEmpty() ? "-" : stateDirectors.getFirst().getName();
        Map<String, String> mapFields = getMapFields(society, societyState, directorName);
        return mapLetterHeader(mapFields, society.getStateCode(), societyCategory);
    }

    @Override
    public boolean canHandle(String templateCode) {
        return DocumentTemplateEnum.SURAT_IRINGAN_KELULUSAN.getCode().equals(templateCode);
    }

    private static Map<String, String> getMapFields(Society society, AdmAddresses societyState, String stateDirectorName) {

        Map<String, String> mapFields = new HashMap<>();
        mapFields.put("{{SOCIETY_NAME}}", getValueOrDefault(society.getSocietyName()).toUpperCase());
        mapFields.put("{{SOCIETY_NO}}", getValueOrDefault(society.getSocietyNo()).toUpperCase());
        mapFields.put("{{SOCIETY_ADDRESS}}", getValueOrDefault(society.getAddress()));
        mapFields.put("{{SOCIETY_POSTCODE}}", getValueOrDefault(society.getPostcode()));
        mapFields.put("{{SOCIETY_CITY}}", getValueOrDefault(society.getCity()).toUpperCase());
        mapFields.put("{{SOCIETY_STATE}}", getValueOrDefault(societyState.getName()).toUpperCase());
        mapFields.put("{{APPROVAL_DATE}}", formatToMalayDate(society.getApprovedDate()));
        mapFields.put("{{OFFICER_NAME}}", getValueOrDefault(stateDirectorName).toUpperCase());
        return mapFields;
    }

    private Map<String, String> mapLetterHeader(Map<String, String> mapFields, String stateCode, SocietyCategoryEnum societyCategory) throws Exception {
        // Null or empty check
        if (stateCode == null || stateCode.isEmpty()) {
            return mapFields;
        }

        // Letter Header is based on the state who will be handled the society
        String letterHeaderStateCode = roDecisionUtilities.getAssignedResponsibleStateCode(stateCode, societyCategory);
        mapFields.put("{{IS_" + letterHeaderStateCode  + "_STATE}}", "true");
        return mapFields;
    }
}