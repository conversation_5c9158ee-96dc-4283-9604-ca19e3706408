package com.eroses.external.society.service;

import com.eroses.external.society.model.MeetingMemberAttendance;
import com.eroses.external.society.mappers.MeetingMemberAttendanceDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingMemberAttendanceWriteDomainService {
    private final MeetingMemberAttendanceDao meetingMemberAttendanceDao;

    public Long create(MeetingMemberAttendance meetingMemberAttendance) throws Exception {
        meetingMemberAttendanceDao.create(meetingMemberAttendance);
        return meetingMemberAttendance.getId();
    }

    public void createMany(List<MeetingMemberAttendance> meetingMemberAttendances) {
        meetingMemberAttendanceDao.createMany(meetingMemberAttendances);
    }

    public Long update(MeetingMemberAttendance meetingMemberAttendance) throws Exception {
        meetingMemberAttendanceDao.update(meetingMemberAttendance);
        return meetingMemberAttendance.getId();
    }

    public void delete(Map<String, Object> params) {
        meetingMemberAttendanceDao.delete(params);
    }
}
