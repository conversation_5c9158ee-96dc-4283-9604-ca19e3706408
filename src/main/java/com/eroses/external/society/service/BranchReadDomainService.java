package com.eroses.external.society.service;

import com.eroses.external.society.dto.request.BranchReadRequest;
import com.eroses.external.society.dto.response.appeal.GetUserSocietiesForAppealResponse;
import com.eroses.external.society.dto.response.branch.BranchBasicInfoResponse;
import com.eroses.external.society.dto.response.branchCommittee.BranchSecretaryResponse;
import com.eroses.external.society.mappers.BranchCommitteeDao;
import com.eroses.external.society.mappers.BranchDao;
import com.eroses.external.society.mappers.ExtensionTimeDao;
import com.eroses.external.society.model.Branch;
import com.eroses.external.society.model.BranchCommittee;
import com.eroses.external.society.model.ExtensionTime;
import com.eroses.external.society.model.enums.StatusCode;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.model.enums.SubStatusCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class BranchReadDomainService {

    private final BranchDao branchDao;
    private final BranchCommitteeDao branchCommitteeDao;
    private final ExtensionTimeDao extensionTimeDao;

    public boolean existByBranchName(String name, Long societyId) {
        return branchDao.existByBranchName(name, societyId);
    }

    public List<Branch> getAllBranch(Map<String, Object> params) {
        return branchDao.findAll(params);
    }

    public Long countAllBranch(Map<String, Object> param) {
        return branchDao.countAllBranch(param);
    }

    public List<Branch> findAllByParams(Map<String, Object> params, Boolean isCommitteeRequired) {
        List<Branch> branches = branchDao.findAllByParams(params);
        if (Boolean.TRUE.equals(isCommitteeRequired)) {
            for (Branch branch : branches) {
                List<BranchCommittee> branchCommittees = branchCommitteeDao.findAllByBranchId(branch.getId());
                branch.setBranchCommittees(branchCommittees);
            }
        }
        return branches;
    }

    public Long countAllByParams(Map<String, Object> param) {
        return branchDao.countAllByParams(param);
    }


    public Branch findBranchNo(Long id) {
        Branch branchNo = branchDao.findBranchNo(id);
        return branchNo;
    }

    // TODO: rename to getBranchAndCommittees
    public Branch getBranchById(Long id) {
        Branch branch =  branchDao.findById(id);
        List<BranchCommittee> branchCommittees = branchCommitteeDao.findAllByBranchId(branch.getId());
        branch.setBranchCommittees(branchCommittees);
        return branch;
    }

    public List<BranchBasicInfoResponse> getBasicInfo(Long societyId) {
        return branchDao.findBasicInfo(societyId);
    }

    public List<BranchSecretaryResponse> getBranchSecretaries(Map<String, Object> params) {
        return branchDao.getBranchSecretaries(params);
    }

    public Long getBranchSecretariesCount(Map<String, Object> params) {
        return branchDao.getBranchSecretariesCount(params);
    }

    public Branch getByApplicationNo(String applicationNo) {
        return branchDao.getByApplicationNo(applicationNo);
    }

    public Branch getByPaymentId(Long paymentId) {
        return branchDao.getByPaymentId(paymentId);
    }

    public Branch getBranchByIdWithoutCommittee(Long id) {
        Branch branch =  branchDao.findById(id);
        return branch;
    }

    public Branch getBranchApprovalInfoById(Long branchId) {
        return branchDao.findById(branchId);
    }

    public Branch getBranchApprovalDecision(BranchReadRequest branchReadRequest) {

        return branchDao.findById(branchReadRequest.getBranchId());
    }

    public List<Branch> getAllExtendingBranch(Boolean extensionDuration) {
        if (extensionDuration) {
            return branchDao.findAllExtendingBranch();
        } else {
            return Collections.emptyList();
        }
    }

    public List<Branch> findBySocietyId(Long societyId) {
        return branchDao.findBySocietyId(societyId);
    }

    public List<Branch> getAllPending(Integer applicationStatusCode, Integer offset, Integer limit) {
        Map<String, Integer> params = new HashMap<>();
        params.put("applicationStatusCode", applicationStatusCode);
        params.put("offset", offset);
        params.put("limit", limit);
        return branchDao.findAllPending(params);
    }

    public Long countGetAllPending(int applicationStatusCode) {
        return branchDao.countAllPending(applicationStatusCode);
    }

    public List<Branch> getAllPendingUserStateCode(int applicationStatusCode, String stateCode, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        params.put("applicationStatusCode", applicationStatusCode);
        params.put("stateCode", stateCode);
        params.put("offset", offset);
        params.put("limit", limit);

        return branchDao.findAllPendingUserStateCode(params);
    }

    public Long countGetAllPendingUserStateCode(int applicationStatusCode, String stateCode) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "stateCode", stateCode
        );
        return branchDao.countAllPendingUserStateCode(params);
    }

    public int getBranchRegisteredToday(String currentDate){
        return branchDao.countBranchRegisteredToday(currentDate);
    }

    public List<Branch> getBranchByIdOrStatusCode(Map<String, Integer> param) {
        return branchDao.findByBranchIdOrStatusCode(param);
    }

    public int countStatusCode(String statusCode) {
        return branchDao.countStatusCode(statusCode);
    }

    public int countApplicationStatusCode(int statusCode) {
        return branchDao.countApplicationStatusCode(statusCode);
    }

    public int countApplicationStatusCodeAndPaymentDate(int statusCode, int year) {
        Map<String, Object> params = Map.of(
                "statusCode", statusCode,
                "year", year
        );
        return branchDao.countApplicationStatusCodeAndPaymentDate(params);
    }

    public Long countBranchStatusInSociety(Long societyId, String status) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "statusCode", status
        );
        return branchDao.countBranchStatusInSociety(params);
    }

    public List<Branch> getAllPendingByCriteria(List<Integer> applicationStatusCodes, String stateCode, Long roId, Integer isQueried, String search, Integer offset, Integer limit) {

        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (roId != null) params.put("ro", roId);
        if (isQueried != null) params.put("isQueried", isQueried);
        if (search != null && !search.isEmpty()) params.put("search", search);
        if (offset != null) params.put("offset", offset);
        if (limit != null) params.put("limit", limit);

        return branchDao.getAllPendingByCriteria(params);
    }

    public List<Branch> getBranchesByDetailedParam(Map<String, Object> param) {
        return branchDao.findBranchesByDetailedParam(param);
    }

    public List<Branch> getBranchesByDetailedParam(Map<String, Object> params, Boolean isExtensionTimeRequired) {
        List<Branch> branches = branchDao.findBranchesByDetailedParam(params);
        if(Boolean.TRUE.equals(isExtensionTimeRequired)) {
            for (Branch branch : branches) {
                List<ExtensionTime> extensionTimes = extensionTimeDao.findByBranchId(branch.getId());
                if (extensionTimes != null && !extensionTimes.isEmpty()) {
                    branch.setExtensionTimes(extensionTimes);
                }
            }
        }
        return branches;
    }

    public Long countBranchesByDetailedParam(Map<String, Object> param) {
        return branchDao.countBranchesByDetailedParam(param);
    }

    public Integer countAllPendingByCriteria(List<Integer> applicationStatusCodes, String stateCode, Long roId, Integer isQueried, String search) {

        Map<String, Object> params = new HashMap<>();
        if (applicationStatusCodes != null) params.put("applicationStatusCodes", applicationStatusCodes);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (roId != null) params.put("ro", roId);
        if (isQueried != null) params.put("isQueried", isQueried);
        if (search != null && !search.isEmpty()) params.put("search", search);

        return branchDao.countAllPendingByCriteria(params);
    }

    public Boolean isBranchNameExist(Long societyId, String branchName, int applicationStatusCode) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "branchName", branchName,
                "applicationStatusCode", applicationStatusCode
        );
        return branchDao.isBranchNameExist(params);
    }

    public List<Long> getAllExpiredBranchApplicationId(int applicationStatusCode) {
        return branchDao.getAllExpiredBranchApplicationId(applicationStatusCode);
    }

    public List<GetUserSocietiesForAppealResponse> findBranchForAppeal(Map<String, Object> param) {
        return branchDao.findBranchForAppeal(param);
    }

    public List<Branch> findAllActiveBySocietyId(Long societyId) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "applicationStatusCode", ApplicationStatusCode.LULUS.getCode(),
                "status", StatusCode.AKTIF.getCode()
        );
        return branchDao.findAllBySocietyIdAndApplicationStatusCodeAndStatus(params);
    }

    public List<Branch> findAllCancelledBySocietyId(Long societyId) {
        Map<String, Object> params = Map.of(
                "societyId", societyId,
                "status", StatusCode.TIDAK_AKTIF.getCode(),
                "subStatusCode", SubStatusCode.BATAL.getCode()
        );
        return branchDao.findAllBySocietyIdByStatusAndSubStatusCode(params);
    }

    public List<Branch> findAllByIds(List<Long> ids) {
        return branchDao.findByIds(ids);
    }

    public List<Branch> getAllBranchPendingApproval(int applicationStatusCode, String statusCode, int daysAfterSubmission) {
        Map<String, Object> params = Map.of(
                "applicationStatusCode", applicationStatusCode,
                "statusCode", statusCode,
                "daysAfterSubmission", daysAfterSubmission);
        return branchDao.getAllBranchPendingApproval(params);
    }

    public List<Long> findBranchIdListByIdentificationNo(String identificationNo) {
        return branchDao.findBranchIdListByIdentificationNo(identificationNo);
    }

    public List<Branch> getBranchesByCriteriaAndExcludingActiveSocietyCancellationBranches(String branchName, String branchNo, List<String> stateCodes, Integer societyCategoryCode, Integer applicationStatusCode, String statusCode, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        if (branchName != null && !branchName.isEmpty()) params.put("branchName", branchName);
        if (branchNo != null && !branchNo.isEmpty()) params.put("branchNo", branchNo);
        if (stateCodes != null && !stateCodes.isEmpty()) params.put("stateCodes", stateCodes);
        if (societyCategoryCode != null && societyCategoryCode != 0) params.put("societyCategoryCode", societyCategoryCode);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);
        if (statusCode != null && !statusCode.isEmpty()) params.put("statusCode", statusCode);
        params.put("offset", offset);
        params.put("limit", limit);
        return branchDao.getBranchesByCriteriaAndExcludingActiveSocietyCancellationBranches(params);
    }

    public Long countBranchesByCriteriaAndExcludingActiveSocietyCancellationBranches(String branchName, String branchNo, List<String> stateCodes, Integer societyCategoryCode, Integer applicationStatusCode, String statusCode) {
        Map<String, Object> params = new HashMap<>();
        if (branchName != null && !branchName.isEmpty()) params.put("branchName", branchName);
        if (branchNo != null && !branchNo.isEmpty()) params.put("branchNo", branchNo);
        if (stateCodes != null && !stateCodes.isEmpty()) params.put("stateCodes", stateCodes);
        if (societyCategoryCode != null && societyCategoryCode != 0) params.put("societyCategoryCode", societyCategoryCode);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);
        if (statusCode != null && !statusCode.isEmpty()) params.put("statusCode", statusCode);
        return branchDao.countBranchesByCriteriaAndExcludingActiveSocietyCancellationBranches(params);
    }
}
