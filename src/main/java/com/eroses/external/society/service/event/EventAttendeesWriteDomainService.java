package com.eroses.external.society.service.event;


import com.eroses.external.society.mappers.EventAttendeesDao;
import com.eroses.external.society.model.Event;
import com.eroses.external.society.model.EventAttendees;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventAttendeesWriteDomainService {
    private final EventAttendeesDao eventAttendeesDao;
    private final EventReadDomainService eventReadDomainService;
    private static final String ALPHA_NUMERIC_STRING = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final SecureRandom random = new SecureRandom();

    public EventAttendees create(EventAttendees eventAttendees) {
        Event event = eventReadDomainService.findOneById(eventAttendees.getEventId());
        String attendanceNO =  generateAttendanceNo(event.getEventName());
        eventAttendees.setAttendanceNo(attendanceNO);
         int attendees = eventAttendeesDao.create(eventAttendees);
         if (attendees > 0)
             return eventAttendees;
         else
             return null;
    }

    private String generateAttendanceNo(String eventName){
        String[] words = eventName.split("\\s+");
        String prefix;
        if (words.length >= 2) {
            // Take first letter from first and second words
            prefix = (words[0].substring(0, 1) + words[1].substring(0, 1)).toUpperCase();
        } else {
            // If only one word, take first two letters
            prefix = eventName.substring(0, Math.min(2, eventName.length())).toUpperCase();
        }

        int randomLength = random.nextBoolean() ? 5 : 6;
        String randomString = getRandomAlphaNumeric(randomLength);

        // Combine and return
        return prefix + randomString;
    }

    private static String getRandomAlphaNumeric(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(ALPHA_NUMERIC_STRING.length());
            sb.append(ALPHA_NUMERIC_STRING.charAt(index));
        }
        return sb.toString();
    }


    public EventAttendees update(EventAttendees eventAttendees){
        Boolean attendees =  eventAttendeesDao.update(eventAttendees);
        if(attendees)
            return eventAttendees;
        else
            return null;
    }

    public boolean delete(Long id) {
        log.info("Deleting event attendee with id: {}", id);
        try {
            return eventAttendeesDao.delete(id);
        } catch (Exception e) {
            log.error("Error deleting event attendee: {}", e.getMessage(), e);
            return false;
        }
    }

    public boolean cancelAttendance(Long id) {
        log.info("Cancelling event attendee with id: {}", id);
        try {
            return eventAttendeesDao.cancelAttendance(id);
        } catch (Exception e) {
            log.error("Error cancelling event attendee: {}", e.getMessage(), e);
            return false;
        }
    }

    public boolean deleteByEventId(Long eventId) {
        log.info("Deleting event attendee with event id: {}", eventId);
        try {
            return eventAttendeesDao.deleteByEventId(eventId);
        } catch (Exception e) {
            log.error("Error deleting event attendee: {}", e.getMessage(), e);
            return false;
        }
    }
}
