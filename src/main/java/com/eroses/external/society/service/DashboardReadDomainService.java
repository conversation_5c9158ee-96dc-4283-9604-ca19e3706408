package com.eroses.external.society.service;

import com.eroses.external.society.mappers.DashboardDao;
import com.eroses.external.society.model.Dashboard;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class DashboardReadDomainService {
    
    private final DashboardDao dashboardDao;
    
    /**
     * Find all dashboards with filtering
     * @param type Dashboard type filter (optional)
     * @param module Dashboard module filter (optional)
     * @param category Dashboard category filter (optional)
     * @return List of Dashboard objects
     */
    public List<Dashboard> findAll(String type, String module, String category) {
        Map<String, Object> params = new HashMap<>();
        
        if (type != null && !type.trim().isEmpty()) {
            params.put("type", type.trim());
        }
        if (module != null && !module.trim().isEmpty()) {
            params.put("module", module.trim());
        }
        if (category != null && !category.trim().isEmpty()) {
            params.put("category", category.trim());
        }

        log.debug("Finding dashboards with params: {}", params);
        return dashboardDao.findAllByParams(params);
    }
    
    /**
     * Find dashboard by ID
     * @param id Dashboard ID
     * @return Dashboard object
     */
    public Dashboard findById(Long id) {
        log.debug("Finding dashboard by ID: {}", id);
        return dashboardDao.findById(id);
    }
    
    /**
     * Find dashboard by code
     * @param code Dashboard code
     * @return Dashboard object
     */
    public Dashboard findByCode(String code) {
        log.debug("Finding dashboard by code: {}", code);
        return dashboardDao.findByCode(code);
    }
}
