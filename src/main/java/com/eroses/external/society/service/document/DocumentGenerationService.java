
package com.eroses.external.society.service.document;

import com.eroses.external.society.model.CustomMultipartFile;
import com.eroses.external.society.model.DocumentTemplate;
import com.eroses.external.society.service.S3DomainService;
import com.eroses.external.society.service.document.factory.DocumentMappingStrategyFactory;
import com.eroses.external.society.service.document.strategy.DocumentMappingStrategy;
import com.eroses.external.society.service.pdf.PdfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;


/**
 * Service for mapping data to documents.
 * Uses the Strategy pattern to delegate document mapping to appropriate strategies.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentGenerationService {
    private final DocumentMappingStrategyFactory strategyFactory;
    private final S3DomainService s3DomainService;
    private final PdfService pdfService;

    /**
     * Maps data to a document and returns the processed HTML content.
     * This method is the main entry point for document generation.
     *
     * @param documentTemplateCode The code of the document template
     * @param societyId The ID of the society
     * @param additionalParams Additional parameters needed for specific document types
     * @return The processed HTML content
     * @throws Exception If an error occurs during document generation
     */
    public String mapDataToDocument(String documentTemplateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        try {
            DocumentMappingStrategy strategy = strategyFactory.getStrategy(documentTemplateCode);
            return strategy.mapDataToDocument(documentTemplateCode, societyId, additionalParams);

        } catch (Exception e) {
            log.error("Error while processing document mapping", e);
            throw new RuntimeException("Error while processing document mapping", e);
        }
    }

    /**
     * Maps data to a document and returns the processed HTML content.
     * This method is the main entry point for document generation.
     *
     * @param documentTemplateCode The code of the document template
     * @param societyId The ID of the society
     * @return The processed HTML content
     * @throws Exception If an error occurs during document generation
     */
    public String mapDataToDocument(String documentTemplateCode, Long societyId) throws Exception {
        return mapDataToDocument(documentTemplateCode, societyId, new HashMap<>());
    }

    /**
     * Converts HTML content to PDF bytes.
     *
     * @param html The HTML content to convert
     * @return The PDF as byte array
     * @throws Exception If an error occurs during PDF generation
     */
    public byte[] convertHtmlToPdfBytes(String html) throws Exception {
        return Base64.getDecoder().decode(pdfService.generatePdfWithOpenHtmlToPdf(html));
    }

    /**
     * Processes data into a document template and converts it to PDF bytes in a single operation.
     * This method combines the data mapping and PDF conversion steps for convenience.
     *
     * @param documentTemplateCode The code of the document template
     * @param societyId The ID of the society
     * @param additionalParams Additional parameters needed for specific document types
     * @return The generated PDF document as a byte array
     * @throws Exception If any error occurs during template processing or PDF conversion
     */
    public byte[] generatePdfDocument(String documentTemplateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        try {
            String processedHtml = mapDataToDocument(documentTemplateCode, societyId, additionalParams);
            return convertHtmlToPdfBytes(processedHtml);
        } catch (Exception e) {
            throw new Exception("Error generating PDF document: " + e.getMessage(), e);
        }
    }

    /**
     * Processes data into a document template and converts it to PDF bytes in a single operation.
     * This method combines the data mapping and PDF conversion steps for convenience.
     *
     * @param documentTemplateCode The code of the document template
     * @param societyId The ID of the society
     * @return The generated PDF document as a byte array
     * @throws Exception If any error occurs during template processing or PDF conversion
     */
    public byte[] generatePdfDocumentWithItext(String documentTemplateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        try {
            String processedHtml = mapDataToDocument(documentTemplateCode, societyId, additionalParams);
            return pdfService.generatePdfFromHtml(processedHtml);
        } catch (Exception e) {
            throw new Exception("Error generating PDF document: " + e.getMessage(), e);
        }
    }

    /**
     * Uploads a file to S3 with the specified folder path.
     *
     * @param content The file content as byte array
     * @param fileName The name of the file
     * @param contentType The content type of the file
     * @param s3FolderPath The S3 folder path
     * @return The URL of the uploaded file
     * @throws Exception If an error occurs during file upload
     */
    public String uploadFileToS3(byte[] content, String fileName, String contentType, String s3FolderPath) throws Exception {
        String key = fileName + "_" + LocalDateTime.now();

        MultipartFile multipartFile = new CustomMultipartFile(
                content,
                key + getFileExtension(contentType),
                contentType
        );

        return s3DomainService.uploadFile(multipartFile, s3FolderPath, key);
    }

    /**
     * Retrieves a document from S3 storage.
     * This method serves as a wrapper around the S3DomainService's getDocumentFromS3 method,
     * providing centralized access to document retrieval functionality.
     *
     * @param s3Url The url of the S3 bucket containing the document
     * @return The document content as a byte array
     * @throws Exception If any error occurs during document retrieval
     */
    public byte[] retrieveDocumentFromS3ByUrl(String s3Url) throws Exception {
        try {
            return s3DomainService.getDocumentFromS3(s3Url);
        } catch (Exception e) {
            log.error("Failed to retrieve document from S3: url={}, key={}, error={}",
                    s3Url, e.getMessage(), e);
            throw new Exception("Error retrieving document from S3: " + e.getMessage(), e);
        }
    }

    /**
     * Maps data to a document and uploads it as PDF to S3.
     *
     * @param documentTemplateCode The code of the document template
     * @param societyId The ID of the society
     * @param additionalParams Additional parameters needed for specific document types
     * @param s3FolderPath The S3 folder path
     * @return The URL of the uploaded PDF
     * @throws Exception If an error occurs during document generation or upload
     */
    public String mapDataToDocumentAndUploadAsPdf(String documentTemplateCode, Long societyId,
                                                  Map<String, Object> additionalParams, String s3FolderPath) throws Exception {
        String processedHtml = mapDataToDocument(documentTemplateCode, societyId, additionalParams);
        byte[] pdfBytes = convertHtmlToPdfBytes(processedHtml);

        DocumentMappingStrategy strategy = strategyFactory.getStrategy(documentTemplateCode);
        DocumentTemplate template = strategy.getDocumentTemplate(documentTemplateCode);
        String fileName = strategy.getFileName(template, societyId, additionalParams);

        return uploadFileToS3(pdfBytes, fileName, "application/pdf", s3FolderPath);
    }

    /**
     * Maps data to a document and uploads it as DOCX to S3.
     * Note: This method requires implementation of HTML to DOCX conversion.
     *
     * @param documentTemplateCode The code of the document template
     * @param societyId The ID of the society
     * @param additionalParams Additional parameters needed for specific document types
     * @param s3FolderPath The S3 folder path
     * @return The URL of the uploaded DOCX
     * @throws Exception If an error occurs during document generation or upload
     */
    public String mapDataToDocumentAndUploadAsDocx(String documentTemplateCode, Long societyId,
                                                   Map<String, Object> additionalParams, String s3FolderPath) throws Exception {
        // This method requires implementation of HTML to DOCX conversion
        // For now, it's a placeholder
        throw new UnsupportedOperationException("HTML to DOCX conversion not implemented yet");
    }

    /**
     * Gets the file name for a document without generating the document.
     *
     * @param documentTemplateCode The code of the document template
     * @param societyId The ID of the society
     * @param additionalParams Additional parameters needed for specific document types
     * @return The file name for the document
     * @throws Exception If an error occurs
     */
    public String getDocumentFileName(String documentTemplateCode, Long societyId, Map<String, Object> additionalParams) throws Exception {
        DocumentMappingStrategy strategy = strategyFactory.getStrategy(documentTemplateCode);
        DocumentTemplate template = strategy.getDocumentTemplate(documentTemplateCode);
        return strategy.getFileName(template, societyId, additionalParams);
    }

    /**
     * Gets the file extension based on content type.
     *
     * @param contentType The content type
     * @return The file extension
     */
    private String getFileExtension(String contentType) {
        switch (contentType) {
            case "application/pdf":
                return ".pdf";
            case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                return ".docx";
            default:
                return "";
        }
    }
}

    // Below are for Mapping in docx format (Depreciated)

    // Process the document
    //   XWPFDocument document = processDocument(S3Url, mapFields, tableData, columnHeaders);
    //                return s3DomainService.uploadXWPFDocument(
    //                        document,
    //                        S3FolderPath.SEARCH_INFORMATION_DOCUMENT.getPath(),
    //                        searchInformationDocumentTemplate.getName() + "_" + user.getName() + "_" + LocalDateTime.now() + ".docx"
    //                );

//    public XWPFDocument processDocument(String s3Url, Map<String, String> replacements, List<Map<String, String>> tableData, List<String> columnHeaders) {
//        log.info("Starting document mapping with fields: {}", replacements);
//        try (ByteArrayInputStream documentStream = s3DomainService.getDocumentFromS3(s3Url)) {
//            XWPFDocument doc = new XWPFDocument(documentStream);
//
//            // Process regular paragraphs
//            for (XWPFParagraph paragraph : doc.getParagraphs()) {
//                replaceParagraphContent(paragraph, replacements);
//            }
//
//
//            // Process tables (dynamic table creation)
//            if (!tableData.isEmpty()) {
//                XWPFTable table = doc.getTables().get(0); // Assuming the first table is the target
//                for (Map<String, String> rowData : tableData) {
//                    XWPFTableRow row = table.createRow();
//                    for (int i = 0; i < columnHeaders.size(); i++) {
//                        String header = columnHeaders.get(i);
//                        addCell(row, i, rowData.get(header)); // Add cells dynamically based on column headers
//                    }
//                }
//            }
//
//            // Save
//            ByteArrayOutputStream output = new ByteArrayOutputStream();
//            doc.write(output);
//
//            return doc;
//        } catch (Exception e) {
//            log.error("Failed to process document: {}", e.getMessage());
//            throw new RuntimeException("Failed to process document: " + e.getMessage(), e);
//        }
//    }
//
//    private void replaceParagraphContent(XWPFParagraph paragraph, Map<String, String> replacements) {
//        String paragraphText = paragraph.getText();
//        log.debug("Processing paragraph text: {}", paragraphText);
//
//        for (Map.Entry<String, String> entry : replacements.entrySet()) {
//            String placeholder = entry.getKey();
//            if (paragraphText.contains(placeholder)) {
//                log.debug("Found placeholder: {} to replace with: {}", placeholder, entry.getValue());
//                List<XWPFRun> runs = paragraph.getRuns();
//
//                // Log all runs for debugging
//                for (int i = 0; i < runs.size(); i++) {
//                    XWPFRun run = runs.get(i);
//                    String runText = run.getText(0);
//                    log.debug("Run {}: '{}'", i, runText);
//                }
//
//                // First, collect all runs that make up the placeholder
//                StringBuilder placeholderBuilder = new StringBuilder();
//                int startRun = -1;
//                int endRun = -1;
//
//                for (int i = 0; i < runs.size(); i++) {
//                    String runText = runs.get(i).getText(0);
//                    if (runText == null) continue;
//
//                    placeholderBuilder.append(runText);
//
//                    // Check if we've found the start of the placeholder
//                    if (runText.contains("<<") && startRun == -1) {
//                        startRun = i;
//                    }
//
//                    // Check if we've found the end of the placeholder
//                    if (placeholderBuilder.toString().contains(placeholder)) {
//                        endRun = i;
//                        break;
//                    }
//                }
//
//                if (startRun != -1 && endRun != -1) {
//                    log.debug("Found placeholder from run {} to run {}", startRun, endRun);
//
//                    // Keep the formatting of the first run
//                    XWPFRun firstRun = runs.get(startRun);
//                    String firstRunText = firstRun.getText(0);
//
//                    // Handle the case where the placeholder starts in the middle of the first run
//                    int placeholderStart = firstRunText.indexOf("<<");
//                    String beforePlaceholder = placeholderStart > 0 ? firstRunText.substring(0, placeholderStart) : "";
//
//                    // Set the complete text in the first run
//                    firstRun.setText(beforePlaceholder + entry.getValue(), 0);
//
//                    // Remove the subsequent runs that contained parts of the placeholder
//                    for (int i = endRun; i > startRun; i--) {
//                        paragraph.removeRun(i);
//                    }
//                } else {
//                    log.warn("Placeholder {} was found in paragraph text but not in runs", placeholder);
//                }
//            }
//        }
//    }
//
//    private void addCell(XWPFTableRow row, int cellIndex, String text) {
//        XWPFTableCell cell = row.getCell(cellIndex);
//        if (cell == null) {
//            cell = row.addNewTableCell();
//        }
//        cell.removeParagraph(0);
//        XWPFParagraph paragraph = cell.addParagraph();
//        XWPFRun run = paragraph.createRun();
//        run.setText(text);
//    }
//}
