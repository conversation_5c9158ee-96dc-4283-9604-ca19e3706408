package com.eroses.external.society.service.lib;

import com.eroses.external.society.mappers.BranchDao;
import com.eroses.external.society.mappers.SocietyDao;
import com.eroses.external.society.model.Branch;
import com.eroses.external.society.model.Society;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PreWrite {
    private final SocietyDao societyDao;
    private final BranchDao branchDao;

    public SocietyAndBranchNo findBranchNoOrSocietyNo(Long societyId, Long branchId) {
        SocietyAndBranchNo societyAndBranchNo = new SocietyAndBranchNo();
        if (branchId != null) {
            Branch branch = branchDao.findBranchNo(branchId);
            if (branch != null) {
                societyAndBranchNo.setBranchNo(branch.getBranchNo());
            }
        }
        if (societyId != null) {
            Society society = societyDao.findSocietyNo(societyId);
            if (society != null) {
                societyAndBranchNo.setSocietyNo(society.getSocietyNo());
            }
        }
        return societyAndBranchNo;
    }
}
