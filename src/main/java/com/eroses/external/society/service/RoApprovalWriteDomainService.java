package com.eroses.external.society.service;

import com.eroses.external.society.mappers.RoApprovalDao;
import com.eroses.external.society.model.RoApproval;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class RoApprovalWriteDomainService {
    private final RoApprovalDao roApprovalDao;

    public boolean create(RoApproval roApproval) throws Exception {
        return roApprovalDao.create(roApproval);
    }

    public boolean update(RoApproval roApproval) throws Exception {
        return roApprovalDao.update(roApproval);
    }

    public void createSocietySecretaryApproval(RoApproval roApproval) {
        roApprovalDao.createSocietySecretaryApproval(roApproval);
    }
}
