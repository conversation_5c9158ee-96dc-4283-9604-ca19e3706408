
package com.eroses.external.society.service.blacklist;

import com.eroses.external.society.mappers.blacklist.BlacklistDao;
import com.eroses.external.society.model.blacklist.Blacklist;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class BlacklistReadDomainService {
    private final BlacklistDao blacklistDao;

    public Blacklist findById(Long id) {
        return blacklistDao.findById(id);
    }

    public List<Blacklist> findByBlacklistUserId(Long blacklistUserId) {
        return blacklistDao.findByBlacklistUserId(blacklistUserId);
    }

    public boolean existsActiveBlacklistByIdentificationAndSocietyIdAndBranchIdSection(String identificationNo, Long societyId, Long branchId, String section) {
        Map<String, Object> params = new HashMap<>();
        params.put("identificationNo", identificationNo);
        params.put("societyId", societyId);
        params.put("branchId", branchId);
        params.put("section", section);
        params.put("removalStatus", false);

        Long count = blacklistDao.countGetByCriteria(params);
        return count > 0;
    }


    public List<Blacklist> findBySocietyId(Integer societyId) {
        return blacklistDao.findBySocietyId(societyId);
    }

    public Blacklist findByIdentificationNo(String identificationNo) {
        return blacklistDao.findByIdentificationNo(identificationNo);
    }

    public List<Blacklist> search(Map<String, Object> params) {
        return blacklistDao.search(params);
    }

    public List<Blacklist> search(String searchQuery, String stateCode, String section, Boolean isWhitelisted, Integer offset, Integer limit) {
        Map<String, Object> params = new HashMap<>();
        if (searchQuery != null && !searchQuery.isEmpty()) params.put("searchQuery", searchQuery);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (section != null && !section.isEmpty()) params.put("section", section);
        if (isWhitelisted != null) params.put("removalStatus", isWhitelisted);
        params.put("offset", offset);
        params.put("limit", limit);
        return blacklistDao.search(params);
    }

    public Long countSearch(Map<String, Object> params) {
        return blacklistDao.countSearch(params);
    }

    public Long countSearch(String searchQuery, String stateCode, String section, Boolean isWhitelisted) {
        Map<String, Object> params = new HashMap<>();
        if (searchQuery != null && !searchQuery.isEmpty()) params.put("searchQuery", searchQuery);
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (section != null && !section.isEmpty()) params.put("section", section);
        if (isWhitelisted != null) params.put("removalStatus", isWhitelisted);
        return blacklistDao.countSearch(params);
    }

    public boolean isExists(Long id) {
        return findById(id) != null;
    }

    public boolean isExistsByIdentificationNo(String identificationNo) {
        return findByIdentificationNo(identificationNo) != null;
    }

    public List<Blacklist> findAllActiveBySocietyCancellationId(Long societyCancellationId) {
        Map<String, Object> params = new HashMap<>();
        params.put("societyCancellationId", societyCancellationId);
        params.put("isCompleted", 1);
        params.put("removalStatus", 0);
        return blacklistDao.findBySocietyCancellationIdAndIsCompletedAndRemovalStatus(params);
    }

    public List<Blacklist> findAllActiveByIdentificationNo(String identificationNo) {
        Map<String, Object> params = new HashMap<>();
        params.put("identificationNo", identificationNo);
        params.put("isCompleted", 1);
        params.put("removalStatus", 0);
        return blacklistDao.findByIdentificationNoAndIsCompletedAndRemovalStatus(params);
    }

    public List<Blacklist> findAllActiveByIdentificationNoExcludingId(String identificationNo, Long id) {
        Map<String, Object> params = new HashMap<>();
        params.put("identificationNo", identificationNo);
        params.put("isCompleted", 1);
        params.put("removalStatus", 0);
        params.put("id", id);
        return blacklistDao.findByIdentificationNoAndIsCompletedAndRemovalStatusExcludingId(params);
    }

    public List<Long> getAllPendingToProcessBlacklistId() {
        Map<String, Object> params = new HashMap<>();
        params.put("isCompleted", false);
        params.put("removalStatus", false);
        return blacklistDao.getAllPendingToProcessBlacklistId(params);
    }
}
