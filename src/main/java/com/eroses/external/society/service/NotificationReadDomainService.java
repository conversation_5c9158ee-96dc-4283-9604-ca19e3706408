package com.eroses.external.society.service;

import com.eroses.external.society.mappers.NotificationDao;
import com.eroses.external.society.model.Notification;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class NotificationReadDomainService {
    private final NotificationDao notificationDao;

    public List<Notification> getAll() {
        return notificationDao.getAll();
    }

    public Boolean isExists(Long id) {
        Notification notification = notificationDao.findById(id);
        return notification != null;
    }

    public Notification findByTemplateCode(String templateCode) {
        return notificationDao.findByTemplateCode(templateCode);
    }
}
