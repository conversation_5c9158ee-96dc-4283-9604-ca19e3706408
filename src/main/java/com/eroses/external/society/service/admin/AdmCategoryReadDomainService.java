package com.eroses.external.society.service.admin;

import com.eroses.external.society.mappers.AdmCategoryDao;
import com.eroses.external.society.model.AdmCategory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class AdmCategoryReadDomainService {

    private final AdmCategoryDao admCategoryDao;

    public List<AdmCategory> getAllCategory() {
        return admCategoryDao.findAll();
    }

    public AdmCategory findById(Long id) {
        return admCategoryDao.findById(id);
    }
}
