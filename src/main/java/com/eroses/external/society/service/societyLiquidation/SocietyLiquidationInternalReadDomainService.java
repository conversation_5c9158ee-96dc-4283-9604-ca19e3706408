package com.eroses.external.society.service.societyLiquidation;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.eroses.external.society.mappers.liquidation.RoLiquidationApprovalDao;
import com.eroses.external.society.model.AdmBranch;
import com.eroses.external.society.service.admin.AdmBranchReadDomainService;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyLiquidationInternalReadDomainService {
    private final RoLiquidationApprovalDao roLiquidationApprovalDao;
    private final AdmBranchReadDomainService admBranchReadDomainService;
    private final UserFacade userFacade;

    public long countSocietyLiquidationInternal(
        int[] applicationStatusCode,
        Integer branchLiquidation
    ) throws Exception {
        User me = userFacade.me();

        Map<String, Object> params = new HashMap<>();
        AdmBranch admBranch = admBranchReadDomainService.findById(me.getJppmBranchId());

        if (admBranch == null) {
            params.put("identificationNo", me.getIdentificationNo());
        } else if (admBranch.getCode().equals("KDN") || admBranch.getCode().equals("HQ")) {
        } else if (admBranch.getCode() != null) {
            params.put("stateCode", admBranch.getStateCode());
        }

        params.put("appStatusCode", applicationStatusCode);
        params.put("branchLiquidation", branchLiquidation);

        return roLiquidationApprovalDao.getTotalCount(params);
    }
}
