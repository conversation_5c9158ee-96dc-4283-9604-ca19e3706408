package com.eroses.external.society.service;

import com.eroses.external.society.api.converter.input.AuditTrailApiInputConverter;
import com.eroses.external.society.dto.request.auditTrail.UserAuditTrailCreateRequest;
import com.eroses.external.society.mappers.AuditTrailDao;
import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.AuditActionType;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuditTrailWriteDomainService {
    private final AuditTrailDao auditTrailDao;
    private final AuditTrailApiInputConverter converter;
    private final UserFacade userFacade;

    public Long create(AuditTrail auditTrail) throws Exception {
        AuditActionType type = AuditActionType.getAuditActionByActionType(auditTrail.getActionType());
        if (type != null) {
            auditTrail.setModule(type.getModule());
        }

        auditTrailDao.create(auditTrail);
        return auditTrail.getId();
    }

    public void createUserAuditTrail(UserAuditTrailCreateRequest request) throws Exception {
        AuditTrail auditTrail = new AuditTrail();

        auditTrail.setActionType(request.getActionType());
        AuditActionType type = AuditActionType.getAuditActionByActionType(auditTrail.getActionType());
        if (type != null) {
            auditTrail.setModule(type.getModule());
        }

        if (request.getInternalUserId() != null) {
            auditTrail.setInternalUserId(request.getInternalUserId());
        }

        auditTrail.setUserGroup(request.getUserGroup());
        auditTrail.setUserName(request.getUserName());
        auditTrail.setCreatedBy(request.getCreatedBy());
        auditTrail.setIdentificationNo(request.getIdentificationNo());
        
        String newData = stringifyData(request.getNewData());
        auditTrail.setNewData(newData);
        if (request.getOldData() != null) {
            String oldData = stringifyData(request.getOldData());
            auditTrail.setOldData(oldData);
        }

        auditTrailDao.create(auditTrail);
    }

    public <T extends BaseEntity> void createV2(T oldData, T newData, String actionType) throws Exception {
        AuditTrail auditTrail = new AuditTrail();
        User me = userFacade.me();

        ObjectMapper objectMapper = new ObjectMapper()
                .registerModule(new JavaTimeModule());
        String newDataJson = objectMapper.writeValueAsString(newData);
        auditTrail.setNewData(newDataJson);
        if (oldData != null) {
            String oldDataJson = objectMapper.writeValueAsString(oldData);
            auditTrail.setOldData(oldDataJson);
        }

        auditTrail.setActionType(actionType);
        AuditActionType type = AuditActionType.getAuditActionByActionType(auditTrail.getActionType());
        if (type != null) {
            auditTrail.setModule(type.getModule());
        }

        if (me != null){
            auditTrail.setIdentificationNo(me.getIdentificationNo());
            auditTrail.setUserGroup(me.getUserGroup());
            auditTrail.setUserName(me.getName());
            auditTrail.setCreatedBy(me.getId());
        } else {
            //Extra handling for payment callback
            auditTrail.setIdentificationNo(null);
            auditTrail.setUserGroup(null);
            auditTrail.setUserName("callback");
            auditTrail.setCreatedBy(0L);
        }


        if (newData instanceof Branch branch) {
            auditTrail.setBranchId(branch.getId());
            auditTrail.setBranchNo(branch.getBranchNo());
            auditTrail.setSocietyId(branch.getSocietyId());
            auditTrail.setSocietyNo(branch.getSocietyNo());
        } else if (newData instanceof Society society) {
            auditTrail.setSocietyId(society.getId());
            auditTrail.setSocietyNo(society.getSocietyNo());
        } else if (newData instanceof NonCitizenCommittee nonCitizenCommittee) {
            auditTrail.setBranchId(nonCitizenCommittee.getBranchId());
            auditTrail.setBranchNo(nonCitizenCommittee.getBranchNo());
            auditTrail.setSocietyId(nonCitizenCommittee.getSocietyId());
            auditTrail.setSocietyNo(nonCitizenCommittee.getSocietyNo());
        } else if (newData instanceof Committee committee) {
            auditTrail.setSocietyId(committee.getSocietyId());
            auditTrail.setSocietyNo(committee.getSocietyNo());
        } else if (newData instanceof Meeting meeting) {
            auditTrail.setBranchId(meeting.getBranchId());
            auditTrail.setBranchNo(meeting.getBranchNo());
            auditTrail.setSocietyId(meeting.getSocietyId());
            auditTrail.setSocietyNo(meeting.getSocietyNo());
        } else if (newData instanceof Amendment amendment) {
            auditTrail.setSocietyId(amendment.getSocietyId());
            auditTrail.setSocietyNo(amendment.getSocietyNo());
        } else if (newData instanceof ConstitutionContent constitutionContent) {
            auditTrail.setSocietyId(constitutionContent.getSocietyId());
            auditTrail.setSocietyNo(constitutionContent.getSocietyNo());
        } else if (newData instanceof ConstitutionValue constitutionValue) {
            auditTrail.setSocietyId(constitutionValue.getSocietyId());
            auditTrail.setSocietyNo(constitutionValue.getSocietyNo());
        } else if (newData instanceof TrusteeHolder trusteeHolder) {
            auditTrail.setSocietyId(trusteeHolder.getSocietyId());
            auditTrail.setBranchId(trusteeHolder.getBranchId());
            auditTrail.setSocietyNo(trusteeHolder.getSocietyNo());
            auditTrail.setBranchNo(trusteeHolder.getBranchNo());
        } else if(newData instanceof PublicOfficer publicOfficer){
            auditTrail.setSocietyId(publicOfficer.getSocietyId());
            auditTrail.setBranchId(publicOfficer.getBranchId());
            auditTrail.setSocietyNo(publicOfficer.getSocietyNo());
            auditTrail.setBranchNo(publicOfficer.getBranchNo());
        } else if(newData instanceof Statement statement){
            auditTrail.setSocietyId(statement.getSocietyId());
            auditTrail.setBranchId(statement.getBranchId());
            auditTrail.setSocietyNo(statement.getSocietyNo());
            auditTrail.setBranchNo(statement.getBranchNo());
        }
        auditTrailDao.create(auditTrail);
    }

    private String stringifyData(Object data) {
        ObjectMapper objectMapper = new ObjectMapper()
                .registerModule(new JavaTimeModule());

        try {
            String dataJson = objectMapper.writeValueAsString(data);
            return dataJson;
        } catch (JsonProcessingException ex) {
            throw new RuntimeException("Failed to convert object to json.");
        }
    }

    public Boolean update(AuditTrail auditTrail) throws Exception {
        return auditTrailDao.update(auditTrail);
    }
}
