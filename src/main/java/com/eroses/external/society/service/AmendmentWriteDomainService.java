package com.eroses.external.society.service;

import com.eroses.external.society.api.facade.SocietyReadFacade;
import com.eroses.external.society.api.facadeImp.ConstitutionContentReadFacadeImpl;
import com.eroses.external.society.dto.request.ConstitutionContentGetAllSocietiesRequest;
import com.eroses.external.society.mappers.AmendmentDao;
import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.PaymentStatus;
import com.eroses.external.society.model.enums.Position;
import com.eroses.external.society.model.enums.StatusCode;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.utils.Assert;
import com.eroses.service.notification.NotificationTemplateEnum;
import com.eroses.service.notification.dto.NotificationCreateRequest;
import com.eroses.service.notification.facade.NotificationFacade;
import com.eroses.service.notification.service.NotificationRequestBuilderService;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AmendmentWriteDomainService {
    private final AmendmentDao amendmentDao;
    private final ConstitutionContentReadFacadeImpl constitutionContentReadFacadeImpl;
    private final ConstitutionContentReadDomainService constitutionContentReadDomainService;
    private final ConstitutionContentWriteDomainService constitutionContentWriteDomainService;
    private final AmendmentReadDomainService amendmentReadDomainService;
    private final CommitteeReadDomainService committeeReadDomainService;
    private final SocietyReadFacade societyReadFacade;
    private final NotificationFacade notificationFacade;
    private final NotificationRequestBuilderService notificationRequestBuilderService;
    private final SocietyWriteDomainService societyWriteDomainService;
    private final ConstitutionValueReadDomainService constitutionValueReadDomainService;
    private final UserFacade userFacade;

    public Long create(Amendment amendment) throws Exception {
        boolean isOk = amendmentDao.create(amendment);
        Assert.isTrue(isOk, "Create amendment is unsuccessful.");

        return amendment.getId();
    }

    public Boolean update(Amendment amendment) throws Exception {
        boolean isOk = amendmentDao.update(amendment);
        Assert.isTrue(isOk, "Update amendment is unsuccessful.");

        return isOk;
    }

    public boolean updateAmendmentStatus(Amendment amendment, int paymentStatusCode, String paymentMethod) throws Exception {

        if(paymentStatusCode == PaymentStatus.PAID.getCode()) {
            amendment.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode()));
            amendment.setPaymentDate(LocalDate.now());
            amendment.setPaymentMethod(paymentMethod);
            amendment.setSubmissionDate(LocalDate.now());

            //generate notification for Pengerusi (1), Bendahari (6), Setiausaha (4)
            List<Committee> committeeList = committeeReadDomainService.findBySocietyId(amendment.getSocietyId());
            Society society = societyReadFacade.retrieveSociety(amendment.getSocietyId());

            List<Committee> notificationCommittee = new ArrayList<>();

            for (Committee committee : committeeList) {
                Position position = Position.getRole(Integer.parseInt(committee.getDesignationCode()));
                if (position != null && (position.getRank() == 1 || position.getRank() == 4 || position.getRank() == 6)) {
                    notificationCommittee.add(committee);
                }
            }

            if (notificationCommittee.isEmpty()) {
                User user = userFacade.getUserById(amendment.getCreatedBy());
                Committee committee = committeeReadDomainService.findBySocietyIdAndIdentificationNo(amendment.getSocietyId(), user.getIdentificationNo());
                notificationCommittee.add(committee);
            }

            NotificationCreateRequest notificationCreateRequest = notificationRequestBuilderService.buildRequestWithAmendmentDetailsAndUser(
                    notificationCommittee,
                    amendment,
                    society,
                    NotificationTemplateEnum.AMENDMENT_SOCIETY_CREATED.code());
            notificationFacade.sendNotificationByRequest(notificationCreateRequest);

        } else {
            amendment.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.BAYARAN_GAGAL.getCode()));
        }

        amendment.setModifiedBy(0L);
        return amendmentDao.update(amendment);
    }

    public void updateRoDecision(Amendment amendment, User currentUser, Integer applicationStatusCode, String statusCode) throws Exception {
        amendment.setApplicationStatusCode(String.valueOf(applicationStatusCode));
        amendment.setStatus(statusCode);
        amendment.setModifiedBy(currentUser.getId());
        amendmentDao.update(amendment);
    }

    public void updateAmendmentEntitiesStatus(Amendment amendment, String statusCode, Integer applicationStatusCode, User currentUser) throws Exception {
        ConstitutionContentGetAllSocietiesRequest request = new ConstitutionContentGetAllSocietiesRequest(null, null, amendment.getId(), amendment.getSocietyId(), null, null, null, null, null, null, null);
        List<ConstitutionContent> amendedConstitutionContent = constitutionContentReadDomainService.getAllConstitutionContents(request);

        List<Long> amendedConstitutionContentIds = amendedConstitutionContent.stream()
                .map(ConstitutionContent::getId)
                .toList();

        //deactivate original constitution content, only if approved
        if (applicationStatusCode == ApplicationStatusCode.AKTIF.getCode()) {
            Society society = societyReadFacade.retrieveSociety(amendment.getSocietyId());
            List<Long> currentConstitutionIds = new ArrayList<>();

            //check if constitution type not the same - if yes, inaktif all current ones
            if (!Objects.equals(society.getConstitutionType(), amendment.getConstitutionType())) {
                List<ConstitutionContent> currentConstitutions = constitutionContentReadDomainService.getAllBySocietyId(
                                amendment.getSocietyId(), StatusCode.AKTIF.getCode(), ApplicationStatusCode.AKTIF.getCode());

                currentConstitutionIds = currentConstitutions.stream()
                        .map(ConstitutionContent::getId)
                        .toList();

                Map<String, ConstitutionContent> currentMap = currentConstitutions.stream()
                        .filter(c -> c.getClauseNo() != null)
                        .collect(Collectors.toMap(
                                ConstitutionContent::getClauseNo,
                                Function.identity(),
                                (existing, replacement) -> existing // in case of duplicates
                        ));

                for (ConstitutionContent amendedConstitution : amendedConstitutionContent) {
                    String clauseNo = amendedConstitution.getClauseNo();
                    if (clauseNo != null && currentMap.containsKey(clauseNo)) {
                        ConstitutionContent currentMatch = currentMap.get(clauseNo);
                        if (currentMatch != null) {
                            amendedConstitution.setOldConstitutionContentId(currentMatch.getId());
                            constitutionContentWriteDomainService.update(amendedConstitution);
                        }
                    }
                }
            } else {  //constitution type same, only certain fasals amended
                for (ConstitutionContent eachAmendedConstitutionContent : amendedConstitutionContent) {

                    ConstitutionContent currentConstitution = constitutionContentReadDomainService.findForAmendment(
                            amendment.getSocietyId(),
                            eachAmendedConstitutionContent.getClauseContentId() != null ? eachAmendedConstitutionContent.getClauseContentId() : null,
                            eachAmendedConstitutionContent.getClauseNo(),
                            ApplicationStatusCode.AKTIF.getCode());

                    if (currentConstitution != null) {
                        currentConstitutionIds.add(currentConstitution.getId());
                        eachAmendedConstitutionContent.setOldConstitutionContentId(currentConstitution.getId());
                        constitutionContentWriteDomainService.update(eachAmendedConstitutionContent);
                    }
                }
            }

            if (!currentConstitutionIds.isEmpty()) {
                constitutionContentWriteDomainService.updateStatus(currentConstitutionIds, StatusCode.TIDAK_AKTIF.getCode(), ApplicationStatusCode.INAKTIF.getCode(), currentUser);
            }

            //handle other parts that concerns fasal (society address, ajk, etc.)
            this.updateSocietyBasedOnAmendment(amendment, currentUser);
        }

        constitutionContentWriteDomainService.updateStatus(amendedConstitutionContentIds, statusCode, applicationStatusCode, currentUser);
        if (applicationStatusCode == ApplicationStatusCode.AKTIF.getCode()) {
            constitutionContentReadFacadeImpl.constitutionsDocumentExport(amendment.getSocietyId(), amendment.getId(), (long) ApplicationStatusCode.AKTIF.getCode());
        }
    }

    private void updateSocietyBasedOnAmendment(Amendment amendment, User currentUser) {
        try {
            List<ConstitutionContent> constitutionContentList = constitutionContentReadDomainService.getAllConstitutionContents(
                    new ConstitutionContentGetAllSocietiesRequest(null, null, amendment.getId(), amendment.getSocietyId(), null, null, null, null, null, null, null));

            Society society = societyReadFacade.retrieveSociety(amendment.getSocietyId());

            //update constitution type changed no matter changed or not
            society.setConstitutionType(amendment.getConstitutionType());
            society.setSocietyLevel(amendment.getSocietyLevel());
            society.setCategoryCodeJppm(amendment.getCategoryCodeJppm());
            society.setSubCategoryCode(amendment.getSubCategoryCode());
            society.setHasBranch(amendment.getHasBranch());

            for (ConstitutionContent content : constitutionContentList) {
                switch (content.getClauseNo()) {
                    case "1" -> handleClause1(content, society);
                    //TODO: DOUBLE CHECK HERE
                    case "2" -> societyWriteDomainService.updateSocietyAddressFromFasal2(content);
                    default -> log.debug("Skipping unhandled clauseNo: {}", content.getClauseNo());
                }
            }

            societyWriteDomainService.update(society, currentUser);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void handleClause1(ConstitutionContent content, Society society) throws Exception {
        List<ConstitutionValue> values =
                constitutionValueReadDomainService.findByConstitutionContentId(content.getId());

        Map<String, Consumer<String>> setterMap = Map.of(
                "Nama Pertubuhan", society::setSocietyName,
                "Taraf Pertubuhan", society::setSocietyLevel,
                "Singkatan Nama", society::setShortName
        );

        for (ConstitutionValue value : values) {
            String title = value.getTitleName();
            String definition = value.getDefinitionName();

            if (title != null && definition != null) {
                Consumer<String> setter = setterMap.get(title);
                if (setter != null) {
                    setter.accept(definition);
                }
            }
        }

        society.setModifiedBy(0L);
        society.setModifiedDate(LocalDateTime.now());
        societyWriteDomainService.update(society);
    }
}