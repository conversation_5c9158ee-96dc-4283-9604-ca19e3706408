package com.eroses.external.society.service;

import com.eroses.external.society.mappers.BranchSecretaryDao;
import com.eroses.external.society.model.BranchSecretary;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class BranchSecretaryWriteDomainService {
    private final BranchSecretaryDao branchSecretaryDao;

    public Long create(BranchSecretary branchSecretary) throws Exception {
        branchSecretaryDao.create(branchSecretary);
        return branchSecretary.getSecretaryId();
    }

    public Boolean update(BranchSecretary branchSecretary) throws Exception {
        return branchSecretaryDao.update(branchSecretary);
    }
}
