package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.EventFeedbackQuestionDao;
import com.eroses.external.society.model.EventFeedbackQuestion;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventFeedbackQuestionWriteDomainService {
    private final EventFeedbackQuestionDao eFeedbackQuestionDao;

    public List<EventFeedbackQuestion> create(List<EventFeedbackQuestion> eventFeedbackQuestion) {
        log.info("Creating event feedback question");
        int creating = eFeedbackQuestionDao.create(eventFeedbackQuestion);
        if (creating > 0) {
            return eventFeedbackQuestion;
        } else {
            return null;
        }
    }

    public void delete(List<EventFeedbackQuestion> eventFeedbackQuestions){
        log.info("Deleting Event Feedback Question {}", eventFeedbackQuestions);
        eFeedbackQuestionDao.deleteByEventAndQuestionId(eventFeedbackQuestions);
    }

    public EventFeedbackQuestion update(EventFeedbackQuestion eventFeedbackQuestion) {
        log.info("updating event feedback question");
        boolean updating = eFeedbackQuestionDao.update(eventFeedbackQuestion);
        if (updating) {
            return eventFeedbackQuestion;
        }else {
            return null;
        }
    }

    public void deleteByEventId(Long eventId) {
        log.info("Deleting Event Feedback Question by event id {}", eventId);
        eFeedbackQuestionDao.deleteByEventId(eventId);
    }
}
