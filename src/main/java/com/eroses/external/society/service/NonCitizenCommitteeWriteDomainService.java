package com.eroses.external.society.service;

import com.eroses.external.society.mappers.NonCitizenCommitteeDao;
import com.eroses.external.society.model.NonCitizenCommittee;
import com.eroses.external.society.model.enums.PaymentStatus;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.model.enums.ErrorMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class NonCitizenCommitteeWriteDomainService {
    private final NonCitizenCommitteeDao nonCitizenCommitteeDao;

    public boolean create(NonCitizenCommittee nonCitizenCommittee) throws Exception {
        return nonCitizenCommitteeDao.create(nonCitizenCommittee);
    }

    public boolean update(NonCitizenCommittee nonCitizenCommittee) throws Exception {
        return nonCitizenCommitteeDao.update(nonCitizenCommittee);
    }

    public boolean updateAll(List<NonCitizenCommittee> nonCitizenCommitteeList) {
        return nonCitizenCommitteeDao.updateAll(nonCitizenCommitteeList) == nonCitizenCommitteeList.size();
    }

    public Long deleteAllBySocietyId(Long societyId) {
        try {
            Long deletedRecords = nonCitizenCommitteeDao.deleteAllBySocietyId(societyId);
            return deletedRecords;
        } catch (Exception e) {
            throw new RuntimeException(ErrorMessage.DATA_DELETE_FAILED.getBmMessage("Committee records"), e);
        }
    }

    public boolean updatePaymentStatus(NonCitizenCommittee nonCitizenCommittee, int paymentStatusCode, String paymentMethodCode) throws Exception {
        if (PaymentStatus.PAID.getCode() == paymentStatusCode) {
            nonCitizenCommittee.setApplicationStatusCode(ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode());
            nonCitizenCommittee.setPaymentDate(LocalDate.now());
            nonCitizenCommittee.setPaymentMethod(paymentMethodCode);

        } else if (PaymentStatus.FAILED.getCode() == paymentStatusCode) {
            nonCitizenCommittee.setApplicationStatusCode(ApplicationStatusCode.BAYARAN_GAGAL.getCode());
        }
        return nonCitizenCommitteeDao.update(nonCitizenCommittee);
    }

    public NonCitizenCommittee updateRoDecision(NonCitizenCommittee nonCitizenCommittee, int applicationStatusCode, String status) throws Exception {

        // Update application status code and status based on RO decision
        nonCitizenCommittee.setApplicationStatusCode(applicationStatusCode);
        nonCitizenCommittee.setStatus(status);
        nonCitizenCommitteeDao.update(nonCitizenCommittee);
        return nonCitizenCommittee;
    }
}
