package com.eroses.external.society.service.event;

import com.eroses.external.society.mappers.FeedbackQuestionDao;
import com.eroses.external.society.model.FeedbackQuestion;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class FeedbackQuestionReadDomainService {
    private  final FeedbackQuestionDao feedbackQuestionDao;

    public List<FeedbackQuestion> findAll(){
        log.info("Finding all Event Feedback Question");
        return feedbackQuestionDao.findAll();
    }

    public List<FeedbackQuestion> findMultipleByIds(List<Long> ids){
        log.info("Finding all Event Feedback Question by multiple ids");
        return feedbackQuestionDao.findByIds(ids);
    }

    public FeedbackQuestion findById(Long id) {
        log.info("Finding Event Feedback Question by Id:{}", id);
        return feedbackQuestionDao.findById(id);
    }

//    public List<FeedbackQuestion> findEventId(Long id) {
//        log.info("Finding Event Feedback Question by Event Id:{}", id);
//        return feedbackQuestionDao.findByEventId(id);
//    }


}
