package com.eroses.external.society.service.societyLiquidation;

import com.eroses.external.society.api.converter.input.societyLiquidation.AssetApiInputConverter;
import com.eroses.external.society.dto.request.societyLiquidation.SocietyLiquidationAssetCreateRequest;
import com.eroses.external.society.mappers.SocietyLiquidationAssetDao;
import com.eroses.external.society.model.societyLiquidation.SocietyLiquidation;
import com.eroses.external.society.model.societyLiquidation.SocietyLiquidationAsset;
import com.eroses.user.api.facade.UserFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SocietyLiquidationAssetWriteDomainService {
    private final SocietyLiquidationAssetDao dao;
    private final UserFacade userFacade;
    private final AssetApiInputConverter assetApiInputConverter;

    public void create(SocietyLiquidationAsset asset) throws Exception {
        dao.create(asset);
    }

    public void insertMany(List<SocietyLiquidationAssetCreateRequest> requests, SocietyLiquidation liquidation) throws Exception {
        List<SocietyLiquidationAsset> assets = new ArrayList<>();
        for (SocietyLiquidationAssetCreateRequest assetDto : requests) {
            SocietyLiquidationAsset asset = assetApiInputConverter.toModel(assetDto);
            asset.setIcNo(userFacade.me().getIdentificationNo());
            asset.setCreatedBy(userFacade.me().getId());
            asset.setSocietyNo(liquidation.getSocietyNo());
            asset.setSocietyId(liquidation.getSocietyId());
            asset.setLiquidationId(liquidation.getId());
            asset.setBranchId(liquidation.getBranchId());
            asset.setBranchNo(liquidation.getBranchNo());
            assets.add(asset);
        }
        dao.insertMany(assets);
    }
}
