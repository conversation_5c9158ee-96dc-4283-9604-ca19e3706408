package com.eroses.external.society.service;

import com.eroses.external.society.dto.request.statement.StatementCreateRequest;
import com.eroses.external.society.dto.request.statement.StatementEditRequest;
import com.eroses.external.society.mappers.StatementDao;
import com.eroses.external.society.model.Statement;
import com.eroses.external.society.utils.Assert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatementWriteDomainService {
    private final StatementDao statementDao;

    public Boolean create(Statement statement) throws Exception {
        Boolean isCreated = statementDao.create(statement);
        Assert.isTrue(isCreated, "Cipta Penyata Tahunan tidak berjaya");

        return isCreated;
    }

    public Boolean update(Statement statement) throws Exception {
        Boolean isUpdated = statementDao.update(statement);
        Assert.isTrue(isUpdated, "Kemaskini Penyata Tahunan tidak berjaya");

        return isUpdated;
    }

    public Boolean updateGeneralStatement(Long statementId, Statement statement) {
        statement.setStatementId(statementId);
        return statementDao.updateGeneralStatement(statement);
    }

    public Boolean deleteStatement(Long statementId, Long societyId, Long branchId) {
        Map<String, Object> params = new HashMap<>();
        params.put("statementId", statementId);
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        return statementDao.deleteStatement(params);
    }
}
