package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.*;
import com.eroses.external.society.api.facade.posting.PostingReadFacade;
import com.eroses.external.society.api.facade.posting.PostingWriteFacade;
import com.eroses.external.society.dto.request.AuditTrailPagingRequest;
import com.eroses.external.society.dto.request.auditTrail.UserAuditTrailCreateRequest;
import com.eroses.external.society.dto.request.auditTrail.UserAuditTrailReadRequest;
import com.eroses.external.society.dto.request.payment.PreparePaymentRecordDataRequest;
import com.eroses.external.society.dto.request.payment.UpdateRelatedEntityPaymentIdRequest;
import com.eroses.external.society.dto.request.payment.UpdateRelatedEntityStatusRequest;
import com.eroses.external.society.dto.request.pdf.PaymentSlipRequest;
import com.eroses.external.society.dto.request.posting.PostingCreateRequest;
import com.eroses.external.society.dto.request.posting.PostingUpdateRequest;
import com.eroses.external.society.dto.response.amendment.SocietyAmendmentGetResponse;
import com.eroses.external.society.dto.response.posting.PostingExpiredResponse;
import com.eroses.external.society.dto.response.branch.BranchGetResponse;
import com.eroses.external.society.dto.response.posting.PostingResponse;
import com.eroses.external.society.dto.response.propertyofficer.SocietyPropertyOfficerApplicationGetResponse;
import com.eroses.external.society.dto.response.publicofficer.SocietyPublicOfficerGetResponse;
import com.eroses.external.society.dto.response.society.SocietyGetResponse;
import com.eroses.external.society.dto.response.society.SocietyRoQueryResponse;
import com.eroses.external.society.model.*;
import com.eroses.external.society.model.payment.PaymentRecord;
import com.eroses.external.society.service.AuditTrailReadDomainService;
import com.eroses.external.society.service.AuditTrailWriteDomainService;
import com.eroses.external.society.dto.response.appeal.AppealRoQueryResponse;
import com.eroses.external.society.model.enums.ROApprovalType;
import com.eroses.external.society.utils.ResponseUtil;
import com.eroses.user.model.User;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/apiService")
public class ServiceController {

    private final BranchReadFacade branchReadFacade;
    private final BranchWriteFacade branchWriteFacade;
    private final SocietyReadFacade societyReadFacade;
    private final SocietyWriteFacade societyWriteFacade;
    private final SearchInformationDocumentReadFacade searchInformationDocumentReadFacade;
    private final SearchInformationDocumentWriteFacade searchInformationDocumentWriteFacade;
    private final AuditTrailWriteDomainService auditTrailWriteDomainService;
    private final AuditTrailReadFacade auditTrailReadFacade;
    private final AppealReadFacade appealReadFacade;
    private final AppealWriteFacade appealWriteFacade;
    private final SocietyCancellationReadFacade societyCancellationReadFacade;
    private final SocietyCancellationWriteFacade societyCancellationWriteFacade;
    private final BlacklistReadFacade blacklistReadFacade;
    private final BlacklistWriteFacade blacklistWriteFacade;
    private final BlacklistUserReadFacade blacklistUserReadFacade;
    private final BlacklistUserWriteFacade blacklistUserWriteFacade;
    private final PostingReadFacade postingReadFacade;
    private final PostingWriteFacade postingWriteFacade;
    private final PropertyOfficerReadFacade propertyOfficerReadFacade;
    private final PublicOfficerReadFacade publicOfficerReadFacade;
    private final AmendmentReadFacade amendmentReadFacade;
    private final CommitteeReadFacade committeeReadFacade;
    private final PaymentFacade paymentFacade;

    @PostMapping(value = "/audit-trail/create")
    public ResponseEntity<ApiResponse<String>> createAuditTrail(
        @RequestBody UserAuditTrailCreateRequest request
    ) throws Exception {
        auditTrailWriteDomainService.createUserAuditTrail(request);
        return ResponseUtil.buildResponse(null, HttpStatus.OK);
    }

    @GetMapping(value = "/audit-trail/get")
    public ApiResponse<Object> getAuditTrail(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String module,
            @RequestParam(required = false) String actionType,
            @RequestParam(required = false) Integer userGroup,
            @RequestParam(required = false) String identificationNo) throws Exception {

        UserAuditTrailReadRequest request = new UserAuditTrailReadRequest();
        request.setUserId(userId);
        request.setModule(module);
        request.setActionType(actionType);
        request.setUserGroup(userGroup);
        request.setIdentificationNo(identificationNo);

        return auditTrailReadFacade.findAuditTrailForUser(request);
    }

    @GetMapping(value = "/getAllExpiredBranchApplicationId")
    public ResponseEntity<ApiResponse<List<Long>>> getAllExpiredBranchApplicationId() {
        ApiResponse<List<Long>> response = branchReadFacade.getAllExpiredBranchApplicationId();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/updateExpiredBranch")
    public ResponseEntity<ApiResponse<Boolean>> updateExpiredBranch(@RequestBody List<Long> branchIds) {
        ApiResponse<Boolean> response = branchWriteFacade.updateExpiredBranch(branchIds);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllSocietyRoQueryInQueryStatus")
    public ResponseEntity<ApiResponse<List<SocietyRoQueryResponse>>> getAllSocietyRoQueryInQueryStatus() throws Exception {
        ApiResponse<List<SocietyRoQueryResponse>> response = societyReadFacade.getAllSocietyRoQueryInQueryStatus();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/resubmitSocietyApplication")
    public ResponseEntity<ApiResponse<Boolean>> resubmitSocietyApplication(@RequestBody List<Long> societyIds) {
        ApiResponse<Boolean> response = societyWriteFacade.resubmitSocietyApplication(societyIds);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllExpiredSocietyApplicationId")
    public ResponseEntity<ApiResponse<List<Long>>> getAllExpiredSocietyApplicationId() {
        ApiResponse<List<Long>> response = societyReadFacade.getAllExpiredSocietyApplicationId();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/updateExpiredSociety")
    public ResponseEntity<ApiResponse<Boolean>> updateExpiredSociety(@RequestBody List<Long> societyIds) {
        ApiResponse<Boolean> response = societyWriteFacade.updateExpiredSociety(societyIds);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllSoonToExpireSocietyApplication")
    public ResponseEntity<ApiResponse<List<SocietyGetResponse>>> getAllSoonToExpireSocietyApplication() {
        ApiResponse<List<SocietyGetResponse>> response = societyReadFacade.getAllSoonToExpireSocietyApplication();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllExpiredSearchInformationDocumentId")
    public ResponseEntity<ApiResponse<List<Long>>> getAllExpiredSearchInformationDocumentId() {
        ApiResponse<List<Long>> response = searchInformationDocumentReadFacade.getAllExpiredSearchInformationDocumentId();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/updateExpiredSearchInformationDocument")
    public ResponseEntity<ApiResponse<Boolean>> updateExpiredSearchInformationDocument(@RequestBody List<Long> searchInformationDocumentIds) {
        ApiResponse<Boolean> response = searchInformationDocumentWriteFacade.updateExpiredSearchInformationDocument(searchInformationDocumentIds);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllSocietyAppealQueryInQueryState")
    public ResponseEntity<ApiResponse<List<AppealRoQueryResponse>>> getAllSocietyAppealQueryInQueryState() {
        ApiResponse<List<AppealRoQueryResponse>> response = appealReadFacade.getAllAppealQueryInQueryState(ROApprovalType.SOCIETY_APPEAL.getCode());
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getSocietyById")
    public ResponseEntity<ApiResponse<SocietyGetResponse>> getSocietyById(@RequestParam Long societyId) {
        ApiResponse<SocietyGetResponse> response = societyReadFacade.getSocietyById(societyId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllExpiredUnpaidSocietyAppealId")
    public ResponseEntity<ApiResponse<List<Long>>> getAllExpiredUnpaidSocietyAppealId() {
        ApiResponse<List<Long>> response = appealReadFacade.getAllExpiredUnpaidSocietyAppealId();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/updateExpiredUnpaidSocietyAppeals")
    public ResponseEntity<ApiResponse<Boolean>> updateExpiredUnpaidSocietyAppeals(@RequestBody List<Long> appealIds) {
        ApiResponse<Boolean> response = appealWriteFacade.updateExpiredUnpaidSocietyAppeals(appealIds);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/resubmitSocietyAppeals")
    public ResponseEntity<ApiResponse<Boolean>> resubmitSocietyAppeals(@RequestBody List<Long> appealIds) {
        ApiResponse<Boolean> response = appealWriteFacade.resubmitSocietyAppeals(appealIds);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPendingToProcessSocietyCancellationId")
    public ResponseEntity<ApiResponse<List<Long>>> getAllPendingToProcessSocietyCancellationId() {
        ApiResponse<List<Long>> response = societyCancellationReadFacade.getAllPendingToProcessSocietyCancellationId();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/processScheduledSocietyCancellationRecords")
    public ResponseEntity<ApiResponse<Boolean>> processScheduledSocietyCancellationRecords(@RequestBody List<Long> societyCancellationIds) {
        ApiResponse<Boolean> response = societyCancellationWriteFacade.processScheduledSocietyCancellationRecords(societyCancellationIds);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPendingToProcessSocietyCancellationRevertId")
    public ResponseEntity<ApiResponse<List<Long>>> getAllPendingToProcessSocietyCancellationRevertId() {
        ApiResponse<List<Long>> response = societyCancellationReadFacade.getAllPendingToProcessSocietyCancellationRevertId();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/processScheduledSocietyCancellationRevertRecords")
    public ResponseEntity<ApiResponse<Boolean>> processScheduledSocietyCancellationRevertRecords(@RequestBody List<Long> societyCancellationRevertIds) {
        ApiResponse<Boolean> response = societyCancellationWriteFacade.processScheduledSocietyCancellationRevertRecords(societyCancellationRevertIds);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPendingToProcessBlacklistId")
    public ResponseEntity<ApiResponse<List<Long>>> getAllPendingToProcessBlacklistId() {
        ApiResponse<List<Long>> response = blacklistReadFacade.getAllPendingToProcessBlacklistId();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/processScheduledBlacklistRecords")
    public ResponseEntity<ApiResponse<Boolean>> processScheduledBlacklistRecords(@RequestBody List<Long> blacklistIds) {
        ApiResponse<Boolean> response = blacklistWriteFacade.processScheduledBlacklistRecords(blacklistIds);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPendingToProcessWhitelistId")
    public ResponseEntity<ApiResponse<List<Long>>> getAllPendingToProcessWhitelistId() {
        ApiResponse<List<Long>> response = blacklistReadFacade.getAllPendingToProcessWhitelistId();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/processScheduledWhitelistRecords")
    public ResponseEntity<ApiResponse<Boolean>> processScheduledWhitelistRecords(@RequestBody List<Long> whitelistIds) {
        ApiResponse<Boolean> response = blacklistWriteFacade.processScheduledWhitelistRecords(whitelistIds);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllExpiredBlacklistUserId")
    public ResponseEntity<ApiResponse<List<Long>>> getAllExpiredBlacklistUserId() {
        ApiResponse<List<Long>> response = blacklistUserReadFacade.getAllExpiredBlacklistUserId();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/updateExpiredBlacklistUser")
    public ResponseEntity<ApiResponse<Boolean>> updateExpiredBlacklistUser(@RequestBody List<Long> blacklistUserIds) {
        ApiResponse<Boolean> response = blacklistUserWriteFacade.updateExpiredBlacklistUsers(blacklistUserIds);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllSocietyPendingApproval")
    public ResponseEntity<ApiResponse<List<SocietyGetResponse>>> getAllSocietyPendingApproval() {
        ApiResponse<List<SocietyGetResponse>> response = societyReadFacade.getAllSocietyPendingApproval();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllBranchPendingApproval")
    public ResponseEntity<ApiResponse<List<BranchGetResponse>>> getAllBranchPendingApproval() {
        ApiResponse<List<BranchGetResponse>> response = branchReadFacade.getAllBranchPendingApproval();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllExpiredPosting")
    public ResponseEntity<ApiResponse<List<PostingExpiredResponse>>> getAllExpiredPosting() {
        ApiResponse<List<PostingExpiredResponse>> response = postingReadFacade.getPostingExpired();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/updatePosting")
    public ResponseEntity<ApiResponse<Boolean>> updatePosting(@RequestBody PostingUpdateRequest posting) {
        ApiResponse<Boolean> response = postingWriteFacade.updatePosting(posting);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllSocietyPropertyOfficerApplicationPendingApproval")
    public ResponseEntity<ApiResponse<List<SocietyPropertyOfficerApplicationGetResponse>>> getAllSocietyPropertyOfficerApplicationPendingApproval() {
        ApiResponse<List<SocietyPropertyOfficerApplicationGetResponse>> response = propertyOfficerReadFacade.getAllSocietyPropertyOfficerApplicationPendingApproval();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllSocietyPublicOfficerPendingApproval")
    public ResponseEntity<ApiResponse<List<SocietyPublicOfficerGetResponse>>> getAllSocietyPublicOfficerPendingApproval() {
        ApiResponse<List<SocietyPublicOfficerGetResponse>> response = publicOfficerReadFacade.getAllSocietyPublicOfficerPendingApproval();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllSocietyAmendmentPendingApproval")
    public ResponseEntity<ApiResponse<List<SocietyAmendmentGetResponse>>> getAllSocietyAmendmentPendingApproval() {
        ApiResponse<List<SocietyAmendmentGetResponse>> response = amendmentReadFacade.getAllSocietyAmendmentPendingApproval();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    @GetMapping(value = "/getAdminPROByUserId")
    public ResponseEntity<ApiResponse<List<User>>> getAdminPROByUserId(@RequestParam Long userId) {
        ApiResponse<List<User>> response = postingReadFacade.getAllAdmProByUserId(userId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getPostingPrePublish")
    public ResponseEntity<ApiResponse<List<PostingResponse>>> getPostingPrePublish() {
        ApiResponse<List<PostingResponse>> response = postingReadFacade.getPostingPrePublish();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getActiveSecretaryCommitteeBySocietyCategoryId")
    public ResponseEntity<ApiResponse<List<Committee>>> getActiveSecretaryCommitteeBySocietyCategoryId(@RequestParam List<Long> societyCategoriesId) {
        ApiResponse<List<Committee>> response = committeeReadFacade.getActiveSecretaryCommitteeBySocietyCategoryId(societyCategoriesId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/getActiveCommitteesInSocietyWithRoles")
    public ResponseEntity<ApiResponse<List<Committee>>> getActiveCommitteesInSocietyWithRoles(@RequestBody List<String> positions, @RequestParam Long societyId) {
        ApiResponse<List<Committee>> response = committeeReadFacade.getActiveCommitteesInSocietyWithRoles(positions, societyId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping("/createPosting")
    public ResponseEntity<ApiResponse<Long>> createPosting(
            @Valid @RequestBody PostingCreateRequest request) {
        ApiResponse<Long> response = postingWriteFacade.createPosting(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    // Payment Service
    @PostMapping("/payment/generatePaymentSlip")
    public ResponseEntity<ApiResponse<PaymentSlipRequest>> generatePaymentSlip(
            @Valid @RequestBody PaymentRecord paymentRecord) {
        ApiResponse<PaymentSlipRequest> response = paymentFacade.generatePaymentSlip(paymentRecord);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping("/payment/updateRelatedEntityStatus")
    public ResponseEntity<ApiResponse<Void>> updateRelatedEntityStatus(
            @Valid @RequestBody UpdateRelatedEntityStatusRequest request) {
        ApiResponse<Void> response = paymentFacade.updateRelatedEntityStatus(request.getPaymentRecord(), request.getPaymentStatusCode());
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping("/payment/updateRelatedEntityPaymentId")
    public ResponseEntity<ApiResponse<Void>> updateRelatedEntityPaymentId(
            @Valid @RequestBody UpdateRelatedEntityPaymentIdRequest request) {
        ApiResponse<Void> response = paymentFacade.updateRelatedEntityPaymentId(request.getPaymentRequest(), request.getPaymentId(), request.getUser());
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping("/payment/preparePaymentRecordData")
    public ResponseEntity<ApiResponse<PaymentRecord>> preparePaymentRecordData(
            @Valid @RequestBody PreparePaymentRecordDataRequest request) {
        ApiResponse<PaymentRecord> response = paymentFacade.preparePaymentRecordData(request.getPaymentRecord(), request.getPaymentRequest());
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}

