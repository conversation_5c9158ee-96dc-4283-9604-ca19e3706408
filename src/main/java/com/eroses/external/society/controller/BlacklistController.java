
package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.BlacklistReadFacade;
import com.eroses.external.society.api.facade.BlacklistWriteFacade;
import com.eroses.external.society.dto.request.blacklist.BlacklistCreateRequest;
import com.eroses.external.society.dto.request.blacklist.WhitelistCreateRequest;
import com.eroses.external.society.dto.response.blacklist.*;
import com.eroses.external.society.dto.response.branch.BranchGetOneResponse;
import com.eroses.external.society.dto.response.society.SocietyGetResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/blacklist")
public class BlacklistController {

    private final BlacklistWriteFacade blacklistWriteFacade;
    private final BlacklistReadFacade blacklistReadFacade;

    @PostMapping("/create")
    //TODO: Add - @PreAuthorize("hasAuthority('SekatanLiabiliti: EDIT')")
    public ResponseEntity<ApiResponse<BlacklistCreateResponse>> createBlacklist(@Valid @RequestBody BlacklistCreateRequest request) {
        ApiResponse<BlacklistCreateResponse> response = blacklistWriteFacade.createBlacklist(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping("/whitelist/create")
    //TODO: Add - @PreAuthorize("hasAuthority('SekatanLiabiliti: EDIT')")
    public ResponseEntity<ApiResponse<WhitelistCreateResponse>> createWhiteList(@Valid @RequestBody WhitelistCreateRequest request) {
        ApiResponse<WhitelistCreateResponse> response = blacklistWriteFacade.createWhitelist(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/getAll")
    //TODO: Add - @PreAuthorize("hasAuthority('SekatanLiabiliti: VIEW')")
    public ResponseEntity<ApiResponse<Paging<BlacklistGetResponse>>> getAll(
            @RequestParam(value = "searchQuery", required = false, defaultValue = "" ) String searchQuery,
            @RequestParam(value = "stateCode", required = false, defaultValue = "" ) String stateCode,
            @RequestParam(value = "section", required = false, defaultValue = "" ) String section,
            @RequestParam(value = "isWhitelisted", required = false) Boolean isWhitelisted,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<BlacklistGetResponse>> response = blacklistReadFacade.getAllBlacklist(searchQuery, stateCode, section, isWhitelisted, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/whitelist/getAll")
    //TODO: Add - @PreAuthorize("hasAuthority('SekatanLiabiliti: VIEW')")
    public ResponseEntity<ApiResponse<List<WhitelistGetResponse>>> getAll(
            @RequestParam(value = "blacklistId") Long blacklistId) {
        ApiResponse<List<WhitelistGetResponse>> response = blacklistReadFacade.getAllWhitelist(blacklistId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<BlacklistGetResponse>> findById(@PathVariable Long id) {
        ApiResponse<BlacklistGetResponse> response = blacklistReadFacade.findById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/isUserRegisteredInSociety")
    public ResponseEntity<ApiResponse<IsUserRegisteredInSocietyGetResponse>> isUserRegisteredInSociety(
            @RequestParam(value = "identificationNo", defaultValue = "" ) String identificationNo) {
        ApiResponse<IsUserRegisteredInSocietyGetResponse> response = blacklistReadFacade.isUserRegisteredInSociety(identificationNo);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/getSocietyRegisteredByUser")
    public ResponseEntity<ApiResponse<List<SocietyGetResponse>>> getSocietyRegisteredByUser(
            @RequestParam(value = "identificationNo", defaultValue = "" ) String identificationNo) {
        ApiResponse<List<SocietyGetResponse>> response = blacklistReadFacade.getSocietyRegisteredByUser(identificationNo);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/getBranchRegisteredByUser")
    public ResponseEntity<ApiResponse<List<BranchGetOneResponse>>> getBranchRegisteredByUser(
            @RequestParam(value = "identificationNo", defaultValue = "" ) String identificationNo) {
        ApiResponse<List<BranchGetOneResponse>> response = blacklistReadFacade.getBranchRegisteredByUser(identificationNo);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/isCurrentUserBlacklisted")
    public ResponseEntity<ApiResponse<Boolean>> isCurrentUserBlacklisted() {
        ApiResponse<Boolean> response = blacklistReadFacade.isCurrentUserBlacklisted();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
