package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.*;
import com.eroses.external.society.dto.request.societyCancellation.SocietyCancellationCreateRequest;
import com.eroses.external.society.dto.request.societyCancellation.SocietyCancellationRevertCreateRequest;
import com.eroses.external.society.dto.response.branch.BranchSearchResponse;
import com.eroses.external.society.dto.response.society.*;
import com.eroses.external.society.dto.response.societyCancellation.SocietyCancellationGetResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/cancellation")
public class SocietyCancellationController {
    private final SocietyCancellationReadFacade societyCancellationReadFacade;
    private final SocietyCancellationWriteFacade societyCancellationWriteFacade;

    @GetMapping(value = "/getAllActiveSociety")
    //TODO: Add - @PreAuthorize("hasAuthority('Pembatalan: VIEW')")
    public ResponseEntity<ApiResponse<Paging<SocietySearchResponse>>> getAllActiveSociety(
            @RequestParam(value = "societyName", required = false, defaultValue = "") String societyName,
            @RequestParam(value = "societyNo", required = false, defaultValue = "") String societyNo,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<SocietySearchResponse>> response = societyCancellationReadFacade.getAllActiveSociety(societyName, societyNo, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllActiveBranch")
    //TODO: Add - @PreAuthorize("hasAuthority('Pembatalan: VIEW')")
    public ResponseEntity<ApiResponse<Paging<BranchSearchResponse>>> getAllActiveBranch(
            @RequestParam(value = "branchName", required = false, defaultValue = "") String branchName,
            @RequestParam(value = "branchNo", required = false, defaultValue = "") String branchNo,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<BranchSearchResponse>> response = societyCancellationReadFacade.getAllActiveBranch(branchName, branchNo, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    //Get All Active Branch by Society
    //Excluding Branch that has cancelled record
    //is_reverted = 0, branch = xx.

    @GetMapping(value = "/getAllCancelledSociety")
    //TODO: Add - @PreAuthorize("hasAuthority('Pembatalan: VIEW')")
    public ResponseEntity<ApiResponse<Paging<SocietyCancellationGetResponse>>> getAllCancelledSociety(
            @RequestParam(value = "societyName", required = false) String societyName,
            @RequestParam(value = "societyNo", required = false) String societyNo,
            @RequestParam(value = "section", required = false) String section,
            @RequestParam(value = "cancelledDateFrom", required = false) LocalDate cancelledDateFrom,
            @RequestParam(value = "cancelledDateTo", required = false) LocalDate cancelledDateTo,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<SocietyCancellationGetResponse>> response = societyCancellationReadFacade.getAllCancelledSociety(societyName, societyNo, section, cancelledDateFrom, cancelledDateTo, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllCancelledSocietyCount")
    //TODO: Add - @PreAuthorize("hasAuthority('Pembatalan: VIEW')")
    public ResponseEntity<ApiResponse<Long>> getAllCancelledSocietyCount() {
        ApiResponse<Long> response = societyCancellationReadFacade.getAllCancelledSocietyCount();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    //TODO: Add - @PreAuthorize("hasAuthority('Pembatalan: VIEW')")
    public ResponseEntity<ApiResponse<SocietyCancellationGetResponse>> findSocietyCancellationById(@PathVariable Long id) {
        ApiResponse<SocietyCancellationGetResponse> response = societyCancellationReadFacade.findById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping("/create")
    //TODO: Add - @PreAuthorize("hasAuthority('Pembatalan: EDIT')")
    public ResponseEntity<ApiResponse<Long>> createSocietyCancellation(@Valid @RequestBody SocietyCancellationCreateRequest request) {
        ApiResponse<Long> response = societyCancellationWriteFacade.createSocietyCancellation(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    // New endpoints for SocietyCancellationRevert
    @PostMapping("/revert/create")
    //TODO: Add - @PreAuthorize("hasAuthority('Pembatalan: EDIT')")
    public ResponseEntity<ApiResponse<Long>> createSocietyCancellationRevert(@Valid @RequestBody SocietyCancellationRevertCreateRequest request) {
        ApiResponse<Long> response = societyCancellationWriteFacade.createSocietyCancellationRevert(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
