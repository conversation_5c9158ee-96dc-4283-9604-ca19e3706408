package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.FeedbackReadFacade;
import com.eroses.external.society.api.facade.FeedbackWriteFacade;
import com.eroses.external.society.api.facade.admin.AdmBranchReadFacade;
import com.eroses.external.society.dto.request.*;
import com.eroses.external.society.dto.response.FeedbackSatisfactionResponse;
import com.eroses.external.society.dto.response.feedback.FeedbackGetResponse;
import com.eroses.external.society.model.*;
import com.eroses.external.society.model.enums.UserRoleEnum;
import com.eroses.external.society.utils.ResponseUtil;
import com.eroses.user.api.facade.UserFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/society/feedback")
public class FeedbackController {
    private final FeedbackWriteFacade feedbackWriteFacade;
    private final FeedbackReadFacade feedbackReadFacade;
    private final UserFacade userFacade;

    private final AdmBranchReadFacade admBranchReadFacade;

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Long>> createFeedback(@RequestBody Feedback feedback) {
        ApiResponse<Long> feedbackId = feedbackWriteFacade.create(feedback);
        return ResponseUtil.buildResponse(feedbackId, HttpStatus.OK);
    }

    @PutMapping("/update")
    public ResponseEntity<ApiResponse<Long>> updateFeedback(@RequestBody Feedback feedback) {
        ApiResponse<Long> feedbackId = feedbackWriteFacade.update(feedback);
        return ResponseUtil.buildResponse(feedbackId, HttpStatus.OK);
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Paging<Feedback>>> getAll(
            @RequestParam(value = "identificationNo", required = false) String identificationNo,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "phoneNumber", required = false) String phoneNumber,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        FeedbackPagingRequest feedbackPagingRequest = new FeedbackPagingRequest();
        feedbackPagingRequest.setPageSize(pageSize);
        feedbackPagingRequest.setPageNo(pageNo);
        feedbackPagingRequest.setIdentificationNo(identificationNo);
        feedbackPagingRequest.setId(id);
        feedbackPagingRequest.setPhoneNumber(phoneNumber);

        ApiResponse<Paging<Feedback>> feedback = feedbackReadFacade.findAllByParams(feedbackPagingRequest);
        return ResponseUtil.buildResponse(feedback, HttpStatus.OK);
    }

    @GetMapping(value = "/{id}")
    public ResponseEntity<ApiResponse<FeedbackGetResponse>> getById(@PathVariable Long id) {
        ApiResponse<FeedbackGetResponse> response = feedbackReadFacade.getFeedbackById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/getNewFeedback")
    public ResponseEntity<ApiResponse<Paging<Feedback>>> getNewFeedback(
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "feedbackType", required = false) String feedbackType,
            @RequestParam(value = "feedbackStatus", required = false) Integer feedbackStatus,
            @RequestParam(value = "stateCode", required = false) Integer stateCode,
            @RequestParam(value = "fromDate", required = false) LocalDate fromDate,
            @RequestParam(value = "toDate", required = false) LocalDate toDate){
        FeedbackPagingRequest feedbackPagingRequest = new FeedbackPagingRequest();
        feedbackPagingRequest.setPageSize(pageSize);
        feedbackPagingRequest.setPageNo(pageNo);
        feedbackPagingRequest.setStateCode(stateCode);
        feedbackPagingRequest.setStatus(feedbackStatus);
        feedbackPagingRequest.setFeedbackType(feedbackType);
        feedbackPagingRequest.setFromDate(fromDate);
        feedbackPagingRequest.setToDate(toDate);

        ApiResponse<Paging<Feedback>> feedback = feedbackReadFacade.findAllExcludeSatisfactory(feedbackPagingRequest);
        return ResponseUtil.buildResponse(feedback, HttpStatus.OK);
    }

    @GetMapping("/getSatisfactoryFeedback")
    public ResponseEntity<ApiResponse<Paging<Feedback>>> getSatisfactoryFeedback(
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "identificationNo", required = false) String identificationNo,
            @RequestParam(value = "satisfaction", required = false) String satisfaction,
            @RequestParam(value = "year", required = false) Integer year,
            @RequestParam(value = "date", required = false) LocalDate date){
        FeedbackPagingRequest feedbackPagingRequest = new FeedbackPagingRequest();
        feedbackPagingRequest.setPageSize(pageSize);
        feedbackPagingRequest.setPageNo(pageNo);
        feedbackPagingRequest.setIdentificationNo(identificationNo);
        feedbackPagingRequest.setYear(year);
        feedbackPagingRequest.setSatisfaction(satisfaction);
        feedbackPagingRequest.setDate(date);

        ApiResponse<Paging<Feedback>> feedback = feedbackReadFacade.findOnlySatisfactory(feedbackPagingRequest);
        return ResponseUtil.buildResponse(feedback, HttpStatus.OK);
    }

    @GetMapping(value = "/satisfaction/count")
    public ResponseEntity<ApiResponse<FeedbackSatisfactionResponse>> countSatisfaction() {
        ApiResponse<FeedbackSatisfactionResponse> response = feedbackReadFacade.countSatisfaction();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/getFeedbackJPPMBranch")
    public ResponseEntity<ApiResponse<List<AdmBranch>>> getFeedbackJPPMBranch() throws Exception {
        final long jppmBranchId = userFacade.me().getJppmBranchId();
        ApiResponse<List<AdmBranch>> response = admBranchReadFacade.getAdmBranchByJppmBranchId(jppmBranchId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getFeedbackOfficer/{admBranchId}")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getFeedbackOfficer(@PathVariable Long admBranchId) throws Exception {
        List<Map<String, Object>> filteredResponse = userFacade.getUsersByUserRole(UserRoleEnum.PEGAWAI_HELPDESK.getRole())
                             .stream()
                             .filter(user -> user.getJppmBranchId() == admBranchId)
                             .map(user -> {
                                 Map<String, Object> filteredUser = new HashMap<>();
                                 filteredUser.put("id", user.getId());
                                 filteredUser.put("name", user.getName());
                                 filteredUser.put("jppmBranchId", user.getJppmBranchId());
                                 filteredUser.put("userGroup", user.getUserGroup());
                                 filteredUser.put("identificationNo", user.getIdentificationNo());
                                 return filteredUser;
                             })
                             .collect(Collectors.toList());

        return ResponseUtil.buildResponse(new ApiResponse<>("Successfully retrieve user list.", filteredResponse), HttpStatus.OK);
    }
}

