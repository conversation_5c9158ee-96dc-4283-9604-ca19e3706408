package com.eroses.external.society.controller;

import com.eroses.external.society.dto.response.ExternalApiResponse;
import com.eroses.external.society.service.admin.ExternalApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/society/admin")
public class ExternalApiController {

    private final ExternalApiService externalApiService;

    @Autowired
    public ExternalApiController(ExternalApiService externalApiService) {
        this.externalApiService = externalApiService;
    }

    @GetMapping("/checkUser")
    public ExternalApiResponse triggerApi(@RequestParam String data) {
        return externalApiService.callApi(data);
    }
}


