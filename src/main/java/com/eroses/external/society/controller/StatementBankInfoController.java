package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.StatementBankInfoReadFacade;
import com.eroses.external.society.api.facade.StatementBankInfoWriteFacade;
import com.eroses.external.society.dto.request.statement.StatementBankInfoCreateRequest;
import com.eroses.external.society.dto.request.statement.StatementBankInfoEditRequest;
import com.eroses.external.society.dto.request.statement.StatementBankInfoGetAllRequest;
import com.eroses.external.society.dto.request.statement.StatementBankInfoGetOneRequest;
import com.eroses.external.society.dto.response.statement.StatementBankInfoCreateResponse;
import com.eroses.external.society.dto.response.statement.StatementBankInfoDeleteResponse;
import com.eroses.external.society.dto.response.statement.StatementBankInfoEditResponse;
import com.eroses.external.society.dto.response.statement.StatementBankInfoGetResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/statement")
public class StatementBankInfoController {

    private final StatementBankInfoWriteFacade statementBankInfoWriteFacade;
    private final StatementBankInfoReadFacade statementBankInfoReadFacade;

    @PostMapping(value = "/bankInfo/create")
    public ResponseEntity<ApiResponse<StatementBankInfoCreateResponse>> createStatementBankInfo(
            @RequestBody StatementBankInfoCreateRequest request) {
        ApiResponse<StatementBankInfoCreateResponse> response = statementBankInfoWriteFacade.create(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/bankInfo/update")
    public ResponseEntity<ApiResponse<StatementBankInfoEditResponse>> updateStatementBankInfo(
            @RequestBody StatementBankInfoEditRequest request) {
        ApiResponse<StatementBankInfoEditResponse> response = statementBankInfoWriteFacade.update(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/bankInfo/get")
    public ResponseEntity<ApiResponse<StatementBankInfoGetResponse>> getStatementBankInfo(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "statementId", required = false) Long statementId,
            @RequestParam(value = "branchId", required = false) Long branchId) {
        Map<String, Object> params = new HashMap<>();
        if (id != null) params.put("id", id);
        if (societyId != null) params.put("societyId", societyId);
        if (statementId != null) params.put("statementId", statementId);
        if (branchId != null) params.put("branchId", branchId);

        ApiResponse<StatementBankInfoGetResponse> response = statementBankInfoReadFacade.getByParam(params);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/bankInfo/list")
    public ResponseEntity<ApiResponse<Paging<StatementBankInfoGetResponse>>> listStatementBankInfo(
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "statementId", required = false) Long statementId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (statementId != null) params.put("statementId", statementId);
        if (branchId != null) params.put("branchId", branchId);
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);

        ApiResponse<Paging<StatementBankInfoGetResponse>> response = statementBankInfoReadFacade.listStatementBankInfo(params);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    // Can use id because can pass in from list
    @PutMapping(value = "/bankInfo/{id}/delete")
    public ResponseEntity<ApiResponse<StatementBankInfoDeleteResponse>> deleteStatementBankInfo(
            @PathVariable Long id,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "statementId", required = false) Long statementId,
            @RequestParam(value = "branchId", required = false) Long branchId) throws Exception {

        ApiResponse<StatementBankInfoDeleteResponse> response = statementBankInfoWriteFacade.delete(id, societyId, statementId, branchId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    // =====================================================================================================//
}
