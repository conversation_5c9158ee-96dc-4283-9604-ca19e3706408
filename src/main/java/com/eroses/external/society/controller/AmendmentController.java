package com.eroses.external.society.controller;

import com.eroses.external.society.api.facadeImp.SocietyWriteFacadeImpl;
import com.eroses.external.society.dto.request.AmendmentEditRequest;
import com.eroses.external.society.dto.request.BranchUpdateRequest;
import com.eroses.external.society.dto.response.amendment.AmendmentByParamResponse;
import com.eroses.external.society.dto.response.amendment.AmendmentEditResponse;
import com.eroses.external.society.dto.response.amendment.AmendmentRegisterResponse;
import com.eroses.external.society.api.converter.input.AmendmentApiInputConverter;
import com.eroses.external.society.api.facade.AmendmentReadFacade;
import com.eroses.external.society.api.facade.AmendmentWriteFacade;
import com.eroses.external.society.dto.response.amendment.SearchAmendmentResponse;
import com.eroses.external.society.model.Amendment;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.dto.request.AmendmentRegisterRequest;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.model.enums.DocumentTemplateEnum;
import com.eroses.external.society.utils.ResponseUtil;
import com.eroses.external.society.utils.HeadersUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/amendment")
public class AmendmentController {

    private final AmendmentWriteFacade amendmentWriteFacade;
    private final AmendmentApiInputConverter amendmentApiInputConverter;
    private final AmendmentReadFacade amendmentReadFacade;
    private final SocietyWriteFacadeImpl societyWriteFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<AmendmentRegisterResponse>> registerAmendment(@Valid @RequestBody AmendmentRegisterRequest request) throws Exception {
        ApiResponse<AmendmentRegisterResponse> response = amendmentWriteFacade.register(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/update")
    public ResponseEntity<ApiResponse<AmendmentEditResponse>> editAmendment(@RequestBody AmendmentEditRequest request) throws Exception {
        ApiResponse<AmendmentEditResponse> response = amendmentWriteFacade.update(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/updateToggleButton")
    public ResponseEntity<ApiResponse<Object>> updateToggleButton(
            @RequestParam Long amendmentId,
            @RequestParam Long constitutionContentId,
            @RequestParam Boolean amendmentToggle) {
        ApiResponse<Object> response = amendmentWriteFacade.updateToggleButton(amendmentId, constitutionContentId, amendmentToggle);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAll")
    public ResponseEntity<ApiResponse<List<Amendment>>> getAllAmendments() {
        ApiResponse<List<Amendment>> response = amendmentReadFacade.getAll();
        HttpHeaders headers = HeadersUtil.createHeaders();
        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    @PutMapping(value = "/{id}/delete")
    public ApiResponse<Boolean> deleteAmendment(@PathVariable Long amendmentId) {
        return amendmentWriteFacade.delete(amendmentId);
    }

    @GetMapping(value = "/getAmendmentByParam")
    public ResponseEntity<ApiResponse<Paging<AmendmentByParamResponse>>> getAmendmentByParam(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "clauseType", required = false) String clauseType,
            @RequestParam(value = "meetingId", required = false) Long meetingId,
            @RequestParam(value = "clauseContentId", required = false) Long clauseContentId,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "applicationStatusCode", required = false) String applicationStatusCode,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Map<String, Object> param = new HashMap<>();

        if (id != null && id != 0) param.put("id", id);
        if (searchQuery != null && !searchQuery.isEmpty()) param.put("searchQuery", searchQuery);
        if (societyId != null && societyId != 0) param.put("societyId", societyId);
        if (clauseType != null && !clauseType.isEmpty()) param.put("clauseType", clauseType);
        if (meetingId != null && meetingId != 0) param.put("meetingId", meetingId);
        if (clauseContentId != null && clauseContentId != 0) param.put("clauseContentId", clauseContentId);
        if (status != null && !status.isEmpty()) param.put("status", status);
        if (applicationStatusCode != null && !applicationStatusCode.isEmpty()) {
            param.put("applicationStatusCode", applicationStatusCode);
        } else {
            param.put("applicationStatusCode", "-1");
        }

        param.put("pageNo", pageNo);
        param.put("pageSize", pageSize);

        ApiResponse<Paging<AmendmentByParamResponse>> response = amendmentReadFacade.getAmendmentByParam(param);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/searchAmendmentByClauseType")
    public ResponseEntity<ApiResponse<Paging<SearchAmendmentResponse>>> getAmendmentByClauseType(
            @RequestParam(value = "searchQuery", required = false, defaultValue = "") String searchQuery,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Map<String, Object> param = new HashMap<>();
        param.put("searchQuery", searchQuery);
        param.put("societyId", societyId);
        param.put("pageNo", pageNo);
        param.put("pageSize", pageSize);

        ApiResponse<Paging<SearchAmendmentResponse>> response = amendmentReadFacade.getAmendmentByClauseType(param);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/{id}/checkAndUpdate")
    public ResponseEntity<ApiResponse<Long>> checkAndUpdateSociety(@PathVariable Long id, @RequestBody AmendmentEditRequest request) {
        ApiResponse<Long> response = amendmentWriteFacade.checkAndUpdateAmendment(id, request);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }
}
