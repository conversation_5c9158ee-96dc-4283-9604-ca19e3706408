package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.societyLiquidation.SocietyLiquidationReadFacade;
import com.eroses.external.society.api.facade.societyLiquidation.SocietyLiquidationWriteFacade;
import com.eroses.external.society.dto.request.societyLiquidation.LiquidationDeleteRequest;
import com.eroses.external.society.dto.request.societyLiquidation.SocietyLiquidationCreateRequest;
import com.eroses.external.society.dto.request.societyLiquidation.SocietyLiquidationUpdateRequest;
import com.eroses.external.society.dto.response.societyLiquidation.*;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.security.annotation.RequireSocietyMembership;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/liquidate")
public class SocietyLiquidationController {
    private final SocietyLiquidationReadFacade societyLiquidationReadFacade;
    private final SocietyLiquidationWriteFacade societyLiquidationWriteFacade;

    @GetMapping("/paging")
    @RequireSocietyMembership(societyIdParam = "societyId")
    ResponseEntity<ApiResponse<LiquidationPaginationGetResponse>> getPaging(
            @RequestParam(value = "societyId") Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "createdYear", required = false) Integer createdYear,
            @RequestParam(value = "submissionYear", required = false) Integer submissionYear,
            @RequestParam(value = "decisionYear", required = false) Integer decisionYear,
            @RequestParam(value = "status", required = false) Integer applicationStatusCode,
            @RequestParam(value = "applicantName", required = false) String applicantName,
            @RequestParam(value = "pageNo", defaultValue = "1", required = false) Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10", required = false) Integer pageSize) {
        ApiResponse<LiquidationPaginationGetResponse> response = societyLiquidationReadFacade.getPaging(
                societyId, branchId, createdYear, submissionYear, decisionYear,
                applicationStatusCode, applicantName, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/{liquidationId}/feedback")
    ResponseEntity<ApiResponse<LiquidationBasicResponse>> prepareLiquidationFeedback(@PathVariable Long liquidationId) {
        ApiResponse<LiquidationBasicResponse> response = societyLiquidationReadFacade.prepareLiquidationFeedback(liquidationId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    ResponseEntity<ApiResponse<LiquidationGetResponse>> findLiquidation(@PathVariable Long id) {
        ApiResponse<LiquidationGetResponse> response = societyLiquidationReadFacade.findById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping("/create")
    ResponseEntity<ApiResponse<Long>> create(@Valid @RequestBody SocietyLiquidationCreateRequest societyLiquidationCreateRequest) {
        ApiResponse<Long> response = societyLiquidationWriteFacade.create(societyLiquidationCreateRequest);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping("/update")
    ResponseEntity<ApiResponse<SocietyLiquidationUpdateResponse>> update(@RequestBody SocietyLiquidationUpdateRequest request) {
        ApiResponse<SocietyLiquidationUpdateResponse> response = societyLiquidationWriteFacade.update(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping("/delete")
    ResponseEntity<ApiResponse<Long>> delete(@RequestBody LiquidationDeleteRequest request) {
        ApiResponse<Long> response = societyLiquidationWriteFacade.delete(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
