package com.eroses.external.society.controller;

import com.eroses.external.society.api.converter.input.AuditTrailApiInputConverter;
import com.eroses.external.society.api.facade.AuditTrailReadFacade;
import com.eroses.external.society.api.facade.AuditTrailWriteFacade;
import com.eroses.external.society.dto.request.*;
import com.eroses.external.society.dto.response.auditTrail.AuditTrailPagingResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.AuditTrail;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/society/audit-trail")
public class AuditTrailController {
    private final AuditTrailWriteFacade auditTrailWriteFacade;
    private final AuditTrailReadFacade auditTrailReadFacade;
    private final AuditTrailApiInputConverter auditTrailApiInputConverter;

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Long>> createAuditTrail(@RequestBody AuditTrailCreateRequest auditTrailCreateRequest) {
        AuditTrail auditTrail = auditTrailApiInputConverter.convertToModel(auditTrailCreateRequest);
        ApiResponse<Long> auditId =  auditTrailWriteFacade.create(auditTrail);
        return ResponseUtil.buildResponse(auditId, HttpStatus.OK);
    }

    @PutMapping("/update")
    public ResponseEntity<ApiResponse<Long>> updateAuditTrail(@RequestBody AuditTrailUpdateRequest auditTrailUpdateRequest) {
        AuditTrail auditTrail = auditTrailApiInputConverter.convertToModel(auditTrailUpdateRequest);
        ApiResponse<Long> auditId =  auditTrailWriteFacade.update(auditTrail);
        return ResponseUtil.buildResponse(auditId, HttpStatus.OK);
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Paging<AuditTrailPagingResponse>>> getAll(
            @RequestParam(value = "userGroup") String userGroup,
            @RequestParam(value = "identificationNo") String identificationNo,
            @RequestParam(value = "module", required = false) String module,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        AuditTrailPagingRequest auditTrailPagingRequest = new AuditTrailPagingRequest();
        auditTrailPagingRequest.setPageSize(pageSize);
        auditTrailPagingRequest.setPageNo(pageNo);
        auditTrailPagingRequest.setUserGroup(userGroup);
        auditTrailPagingRequest.setIdentificationNo(identificationNo);
        auditTrailPagingRequest.setModule(module);
        ApiResponse<Paging<AuditTrailPagingResponse>> auditTrails = auditTrailReadFacade.findAllByParams(auditTrailPagingRequest);
        return ResponseUtil.buildResponse(auditTrails, HttpStatus.OK);
    }

    @PutMapping("/updateModules")
    public ResponseEntity<ApiResponse<Object>> updateModules(@RequestParam String agree) {
        ApiResponse<Object> response = auditTrailWriteFacade.updateModules(agree);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
