package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.*;
import com.eroses.external.society.api.facade.admin.*;
import com.eroses.external.society.api.facadeImp.admin.ConstitutionTypeReadFacadeImpl;
import com.eroses.external.society.dto.request.BranchPagingRequest;
import com.eroses.external.society.dto.request.committee.NonCitizenCommitteeGetAllRequest;
import com.eroses.external.society.dto.request.statement.AdmStatementSearchRequest;
import com.eroses.external.society.dto.response.appeal.AppealAdminRecordByIdResponse;
import com.eroses.external.society.dto.response.appeal.AppealAdminRecordsResponse;
import com.eroses.external.society.dto.response.nonCitizenCommittee.NonCitizenCommitteeGetResponse;
import com.eroses.external.society.dto.response.society.SocietyGetOneResponse;
import com.eroses.external.society.dto.response.statement.AdmStatementGetSocietyInfoResponse;
import com.eroses.external.society.dto.response.statement.AdmStatementGetStatementInfoResponse;
import com.eroses.external.society.dto.response.statement.AdmStatementSearchResponse;
import com.eroses.external.society.dto.response.status.StatusCountResponse;
import com.eroses.external.society.model.*;
import com.eroses.external.society.utils.HeadersUtil;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/admin")
public class AdmController {
    private final AdmAddressesReadFacade admAddressesReadFacade;

    private final AdmMeetingReadFacade admMeetingReadFacade;

    private final AdmPositionJppmReadFacade admPositionJppmReadFacade;

    private final AdmCategoryReadFacade admCategoryReadFacade;

    private final ClauseContentReadFacade clauseContentReadFacade;

    private final ConstitutionTypeReadFacade constitutionTypeReadFacade;

    private final AdmBranchReadFacade admBranchReadFacade;

    private final AdmIntegrationReadFacade admIntegrationReadFacade;

    private final AdmIntegrationWriteFacade admIntegrationWriteFacade;

    private final AdmStatusReadFacade admStatusReadFacade;

    private final StatementReadFacade statementReadFacade;

    private final SocietyReadFacade societyReadFacade;

    private final BranchReadFacade branchReadFacade;

    private final NonCitizenCommitteeReadFacade nonCitizenCommitteeReadFacade;

    private final AppealReadFacade appealReadFacade;






    //private final AdmAddressesWriteFacade admAddressesWriteFacade;

    @GetMapping(value = "/address/list")
    public ResponseEntity<ApiResponse<List<AdmAddresses>>> getAllAdmAddresses() {
        ApiResponse<List<AdmAddresses>> response = admAddressesReadFacade.getAll();

        // Use the utility method to add headers
        HttpHeaders headers = HeadersUtil.createHeaders();

        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    @GetMapping(value = "/meeting/list")
    public ResponseEntity<ApiResponse<List<AdmMeeting>>> getAllMeetingType() {
        ApiResponse<List<AdmMeeting>> response = admMeetingReadFacade.getAllMeetingType();
        log.info(" 1 addresses response: {}", response);

        // Use the utility method to add headers
        HttpHeaders headers = HeadersUtil.createHeaders();

        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    @GetMapping(value = "/jppmPostion/list")
    public ResponseEntity<ApiResponse<List<AdmPositionJppm>>> getAllPositionJppm() {
        ApiResponse<List<AdmPositionJppm>> response = admPositionJppmReadFacade.getAllPositionJppm();

        // Use the utility method to add headers
        HttpHeaders headers = HeadersUtil.createHeaders();

        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    @GetMapping(value = "/category/list")
    public ResponseEntity<ApiResponse<List<AdmCategory>>> getAllCategory() {
        ApiResponse<List<AdmCategory>> response = admCategoryReadFacade.getAllCategory();

        // Use the utility method to add headers
        HttpHeaders headers = HeadersUtil.createHeaders();

        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    @GetMapping(value = "/constitutionType/list")
    public ResponseEntity<ApiResponse<List<ConstitutionType>>> getAllConstitutionType() {
        ApiResponse<List<ConstitutionType>> response = constitutionTypeReadFacade.getAllConstitutionType();

        // Use the utility method to add headers
        HttpHeaders headers = HeadersUtil.createHeaders();

        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    @GetMapping(value = "/clauseContent/list")
    public ResponseEntity<ApiResponse<List<ClauseContent>>> getAllClauseContent() {
        ApiResponse<List<ClauseContent>> response = clauseContentReadFacade.getAllClauseContent();

        // Use the utility method to add headers
        HttpHeaders headers = HeadersUtil.createHeaders();

        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    @GetMapping("/constitutionTypeWithClauseContent/list")
    public ResponseEntity<ApiResponse<List<ConstitutionType>>> getAllConstitutionTypeWithClauseContent() {
        ApiResponse<List<ConstitutionType>> response = constitutionTypeReadFacade.getAllConstitutionType();
        List<ConstitutionType> constitutionTypes = response.getData();

        for (ConstitutionType constitutionType : constitutionTypes) {
            List<ClauseContent> clauseContent = clauseContentReadFacade.getAllByConstitutionTypeId(constitutionType.getId());
            constitutionType.setClauseContents(clauseContent);
        }

        return buildResponse(new ApiResponse<>(constitutionTypes), HttpStatus.OK);
    }

    @GetMapping(value = "/branch/list")
    public ResponseEntity<ApiResponse<List<AdmBranch>>> getAllbranch(
            @RequestParam(value = "includeAll", required = false, defaultValue = "false") boolean includeAll) {
        ApiResponse<List<AdmBranch>> response = admBranchReadFacade.getAll(includeAll);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/status/count")
    public ResponseEntity<ApiResponse<StatusCountResponse>> countStatus() {
        ApiResponse<StatusCountResponse> response = admStatusReadFacade.countStatus();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/integration/list")
    public ResponseEntity<ApiResponse<List<Integration>>> getAllIntegration() {
        ApiResponse<List<Integration>> response = admIntegrationReadFacade.getAll();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping("/integration/update")
    public ResponseEntity<ApiResponse<Long>> updateIntegration(@RequestBody Integration integration) {
        ApiResponse<Long> id = admIntegrationWriteFacade.update(integration);
        return ResponseUtil.buildResponse(id, HttpStatus.OK);
    }

    @GetMapping(value = "/integration/payment/status")
    public ResponseEntity<Object> getPaymentCurrentStatus() {
        return ResponseEntity.ok(admIntegrationReadFacade.getPaymentCurrentStatus());
    }

    // Pengarah/Ro search statement
    @GetMapping(value = "/statement/search")
    public ResponseEntity<ApiResponse<Paging<AdmStatementSearchResponse>>> searchStatement(
            @RequestParam(required = false) String stateCode,
            @RequestParam(required = false) String applicationStatusCode,
            @RequestParam(required = false) Integer statementYear,
            @RequestParam(value = "searchQuery", required = false, defaultValue = "") String searchQuery,
            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize
    ) {
        AdmStatementSearchRequest request = new AdmStatementSearchRequest(stateCode, applicationStatusCode, statementYear, searchQuery, pageNo, pageSize);
        return statementReadFacade.admSearchStatement(request);
    }

    // Pengarah/Ro view society info
    @GetMapping(value = "/statement/{statementId}/getSocietyInfo")
    public ResponseEntity<ApiResponse<AdmStatementGetSocietyInfoResponse>> getSocietyInfoStatement(
            @PathVariable Long statementId,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId) {
        return statementReadFacade.getSocietyInfo(statementId, societyId, branchId);
    }

    // Pengarah/Ro view statement info
    @GetMapping(value = "/statement/{statementId}/getStatementInfo")
    public ResponseEntity<ApiResponse<AdmStatementGetStatementInfoResponse>> getStatementInfo(
            @PathVariable Long statementId,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId) {
        return statementReadFacade.getStatementInfo(statementId, societyId, branchId);
    }


    @GetMapping(value = "/society/findAllByParam")
    public ResponseEntity<ApiResponse<Paging<Society>>> society(
            @RequestParam(value = "searchQuery", required = false, defaultValue = "") String searchQuery,
            @RequestParam(value = "applicationStatusCode", required = false, defaultValue = "0") Integer applicationStatusCode,
            @RequestParam(value = "statusCode", required = false, defaultValue = "") String statusCode,
            @RequestParam(value = "state", required = false, defaultValue = "0") Integer state,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        ApiResponse<Paging<Society>> response = societyReadFacade.findAllByParam(searchQuery, applicationStatusCode, statusCode, state, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/branch/findAllByParam")
    public ResponseEntity<ApiResponse<Paging<Branch>>> getBranchByParam(
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "applicationStatusCode", required = false) Integer applicationStatusCode,
            @RequestParam(value = "state", required = false, defaultValue = "0") Integer state,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        BranchPagingRequest branchPagingRequest = new BranchPagingRequest();
        branchPagingRequest.setPageNo(pageNo);
        branchPagingRequest.setPageSize(pageSize);
        branchPagingRequest.setSearchQuery(searchQuery);
        branchPagingRequest.setApplicationStatusCode(applicationStatusCode);
        branchPagingRequest.setState(state);

        ApiResponse<Paging<Branch>> response = branchReadFacade.getAllByParams(branchPagingRequest);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/societyNonCitizen/findAllByParam")
    public ResponseEntity<ApiResponse<Paging<NonCitizenCommitteeGetResponse>>> getAllNonCitizenCommittee(
            @RequestParam(value = "searchQuery",required = false) String searchQuery,
            @RequestParam(value = "applicationStatusCode", required = false) Integer applicationStatusCode,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        NonCitizenCommitteeGetAllRequest request = new NonCitizenCommitteeGetAllRequest();
        if(searchQuery != null){
            request.setSearchQuery(searchQuery);
        }
        if (applicationStatusCode != null) {
            request.setApplicationStatusCode(applicationStatusCode);
        }
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        request.setIsBranch(0);

        ApiResponse<Paging<NonCitizenCommitteeGetResponse>> response = nonCitizenCommitteeReadFacade.getAllByParams(request);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }
    @GetMapping(value = "/branchNonCitizen/findAllByParam")
    public ResponseEntity<ApiResponse<Paging<NonCitizenCommitteeGetResponse>>> getbranchNonCitizenCommittee(
            @RequestParam(value = "searchQuery",required = false) String searchQuery,
            @RequestParam(value = "applicationStatusCode", required = false) Integer applicationStatusCode,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        NonCitizenCommitteeGetAllRequest request = new NonCitizenCommitteeGetAllRequest();
        if(searchQuery != null){
            request.setSearchQuery(searchQuery);
        }
        if (applicationStatusCode != null) {
            request.setApplicationStatusCode(applicationStatusCode);
        }
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        request.setIsBranch(1);

        ApiResponse<Paging<NonCitizenCommitteeGetResponse>> response = nonCitizenCommitteeReadFacade.getAllByParams(request);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @GetMapping(value = "/appeal/findAllByParam")
    public ResponseEntity<ApiResponse<Paging<AppealAdminRecordsResponse>>> getAppealByParam(
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "state", required = false) Integer state,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Map<String, Object> params = new HashMap<>();
        if (searchQuery != null) params.put("searchQuery", searchQuery);
        if (state != null) params.put("state", state);
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);

        ApiResponse<Paging<AppealAdminRecordsResponse>> response = appealReadFacade.getAllByParams(params);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/appeal/{appealId}/getAdminRecord")
    public ResponseEntity<ApiResponse<AppealAdminRecordByIdResponse>> getAdminRecord(@PathVariable Long appealId) {
        ApiResponse<AppealAdminRecordByIdResponse> response = appealReadFacade.getAdminRecord(appealId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }


//    @GetMapping(value = "/paging")
//    public ApiResponse<Paging<AdmAddresses>> getPaging(@RequestParam(value = "pageNo", defaultValue = "0") Integer pageNo,
//                                                       @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
//        // Validate the inputs if necessary
//        if (pageNo < 0 || pageSize <= 0) {
//            return new ApiResponse<>("Error", 500, "Data input error ", null, LocalDateTime.now());
//        }
//        log.info("Caven 1 addresses request: {}", pageNo);
//
//        // Call the facade to get the paging information
//        Paging<AdmAddresses> paging = admAddressesReadFacade.getPaging(pageNo, pageSize).getData();
//
//        log.info("Caven 1 addresses response: {}", paging);
//
//
//        // Wrap the result in an ApiResponse
//        return ApiResponse.ok(paging);
//    }

//    @PostMapping("/create")
//    public ApiResponse<Long> create(@RequestBody AdmAddressesCreateRequest admAddressesCreateRequest) {
//        return admAddressesWriteFacade.create(admAddressesCreateRequest);
//    }

    private <T> ResponseEntity<ApiResponse<T>> buildResponse(ApiResponse<T> response, HttpStatus status) {
        HttpHeaders headers = HeadersUtil.createHeaders();
        return new ResponseEntity<>(response, headers, status);
    }

}

