package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.PrincipalSecretaryReadFacade;
import com.eroses.external.society.api.facade.PrincipalSecretaryWriteFacade;
import com.eroses.external.society.api.facade.newSocietySecretary.NewSecretaryFeedbackWriteFacade;
import com.eroses.external.society.dto.request.*;
import com.eroses.external.society.dto.request.newSocietySecretary.NewSecretaryFeedbackCreateRequest;
import com.eroses.external.society.dto.request.newSocietySecretary.PrincipalSecretaryAssignRORequest;
import com.eroses.external.society.dto.response.PrincipalSecretaryCreateResponse;
import com.eroses.external.society.dto.response.PrincipalSecretaryEditResponse;
import com.eroses.external.society.dto.response.PrincipalSecretaryGetOneResponse;
import com.eroses.external.society.dto.response.PrincipalSecretarySearchResponse;
import com.eroses.external.society.dto.response.societySecretaryReplacement.PrincipleSecretaryResponse;
import com.eroses.external.society.model.*;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.eroses.external.society.utils.ResponseUtil.buildResponse;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/society/secretary/principal")
public class PrincipalSecretaryController {

    private final PrincipalSecretaryWriteFacade principalSecretaryWriteFacade;
    private final PrincipalSecretaryReadFacade principalSecretaryReadFacade;
    private final NewSecretaryFeedbackWriteFacade newSecretaryFeedbackWriteFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<PrincipalSecretaryCreateResponse>> registerPrincipalSecretary(@Valid @RequestBody PrincipalSecretaryCreateRequest request) throws Exception {
        ApiResponse<PrincipalSecretaryCreateResponse> response = principalSecretaryWriteFacade.create(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @DeleteMapping(value = "/{principalSecretaryId}")
    public ResponseEntity<ApiResponse<String>> delete(@PathVariable Long principalSecretaryId) {
        ApiResponse<String> response = principalSecretaryWriteFacade.delete(principalSecretaryId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/{id}")
    public ResponseEntity<ApiResponse<PrincipalSecretaryEditResponse>> editPrincipalSecretary(@PathVariable Long id, @RequestBody PrincipalSecretaryEditRequest request) throws Exception {
        ApiResponse<PrincipalSecretaryEditResponse> response = principalSecretaryWriteFacade.update(id, request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping
    public ResponseEntity<ApiResponse<String>> submit(@Valid @RequestBody PrincipalSecretaryEditRequest request) {
        ApiResponse<String> response = principalSecretaryWriteFacade.submit(request);
        return buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/ro")
    public ResponseEntity<ApiResponse<String>> assignRO(@Valid @RequestBody PrincipalSecretaryAssignRORequest request) {
        ApiResponse<String> response = principalSecretaryWriteFacade.assignRO(request);
        return buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/{id}")
    public ResponseEntity<ApiResponse<PrincipleSecretaryResponse>> findById(@PathVariable Long id) throws Exception {
        ApiResponse<PrincipleSecretaryResponse> response = principalSecretaryReadFacade.findById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/feedback")
    public ResponseEntity<ApiResponse<String>> create(@Valid @RequestBody NewSecretaryFeedbackCreateRequest request) throws Exception {
        ApiResponse<String> response = newSecretaryFeedbackWriteFacade.create(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/committee/{societyCommitteeId}")
    public ResponseEntity<ApiResponse<PrincipalSecretaryGetOneResponse>> findPrincipalSecretaryById(@PathVariable Long societyCommitteeId) {
        ApiResponse<PrincipalSecretaryGetOneResponse> response = principalSecretaryReadFacade.findByCommitteeId(societyCommitteeId);
        return buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAll")
    public ResponseEntity<ApiResponse<List<PrincipalSecretary>>> getAllPrincipalSecretaries() {
        ApiResponse<List<PrincipalSecretary>> response = principalSecretaryReadFacade.getAll();
        return buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/get-all")
    public ResponseEntity<ApiResponse<Paging<PrincipalSecretarySearchResponse>>> searchPrincipalSecretaries(
            @RequestParam(value = "societyName", required = false) String societyName,
            @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(value = "applicationStatusCode", required = false) Integer statusCode) {
        PrincipalSecretarySearchRequest request = new PrincipalSecretarySearchRequest(
                societyName,
                pageNo,
                pageSize,
                statusCode
        );

        ApiResponse<Paging<PrincipalSecretarySearchResponse>> response = principalSecretaryReadFacade.searchPrincipalSecretaries(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }


    @DeleteMapping("/delete")
    public ResponseEntity<?> deletePrincipalSecretary() {
        return ResponseEntity.ok("success");
    }
}
