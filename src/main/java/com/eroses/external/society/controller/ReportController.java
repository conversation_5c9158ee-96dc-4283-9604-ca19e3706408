package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.ReportReadFacade;
import com.eroses.external.society.dto.request.report.QuickSightEmbedUrlRequest;
import com.eroses.external.society.dto.response.dashboard.DashboardResponse;
import com.eroses.external.society.dto.response.report.QuickSightEmbedUrlResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.utils.ResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/report")
@Api(value = "Report API")
public class ReportController {
    
    private final ReportReadFacade reportReadFacade;
    
    /**
     * Generate registered user embed URL for QuickSight dashboard
     *
     * @param request The embed URL request parameters (required - must contain dashboardId)
     * @return ResponseEntity containing the embed URL response
     */
    @PostMapping("/registeredUserEmbedUrl")
    @ApiOperation(value = "Generate QuickSight registered user embed URL",
                  notes = "Generates a registered user embed URL for accessing QuickSight dashboard. Dashboard ID is required.")
    public ResponseEntity<ApiResponse<QuickSightEmbedUrlResponse>> generateRegisteredUserEmbedUrl(
            @RequestBody @Valid QuickSightEmbedUrlRequest request) {

        try {
            log.info("Controller: Received request to generate QuickSight registered user embed URL for dashboard: {}",
                    request.getDashboardId());

            ApiResponse<QuickSightEmbedUrlResponse> response = reportReadFacade.generateRegisteredUserEmbedUrl(request);

            if (response.getData() != null) {
                log.info("Controller: Successfully generated QuickSight embed URL");
                return ResponseUtil.buildResponse(response, HttpStatus.OK);
            } else {
                log.error("Controller: Failed to generate QuickSight embed URL");
                return ResponseUtil.buildResponse(response, HttpStatus.INTERNAL_SERVER_ERROR);
            }

        } catch (Exception e) {
            log.error("Controller: Unexpected error generating QuickSight embed URL: {}", e.getMessage(), e);
            ApiResponse<QuickSightEmbedUrlResponse> errorResponse = new ApiResponse<>(
                "ERROR", HttpStatus.INTERNAL_SERVER_ERROR.value(),
                "Unexpected error occurred while generating QuickSight embed URL",
                null, java.time.LocalDateTime.now());
            return ResponseUtil.buildResponse(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Generate anonymous embed URL for QuickSight dashboard (deprecated)
     *
     * @param request The embed URL request parameters (required - must contain dashboardId)
     * @return ResponseEntity containing the embed URL response
     * @deprecated Use generateRegisteredUserEmbedUrl instead for better security and user tracking
     */
    @PostMapping("/anonymousEmbedUrl")
    @ApiOperation(value = "Generate QuickSight anonymous embed URL (deprecated)",
                  notes = "Generates an anonymous embed URL for accessing QuickSight dashboard. Use registered-user-embed-url instead. Dashboard ID is required.")
    @Deprecated
    public ResponseEntity<ApiResponse<QuickSightEmbedUrlResponse>> generateAnonymousEmbedUrl(
            @RequestBody @Valid QuickSightEmbedUrlRequest request) {

        try {
            log.warn("Controller: Using deprecated anonymous embed URL endpoint. Consider using registered-user-embed-url instead.");
            log.info("Controller: Received request for dashboard: {}", request.getDashboardId());

            ApiResponse<QuickSightEmbedUrlResponse> response = reportReadFacade.generateAnonymousEmbedUrl(request);

            if (response.getData() != null) {
                log.info("Controller: Successfully generated QuickSight embed URL");
                return ResponseUtil.buildResponse(response, HttpStatus.OK);
            } else {
                log.error("Controller: Failed to generate QuickSight embed URL");
                return ResponseUtil.buildResponse(response, HttpStatus.INTERNAL_SERVER_ERROR);
            }

        } catch (Exception e) {
            log.error("Controller: Unexpected error generating QuickSight embed URL: {}", e.getMessage(), e);
            ApiResponse<QuickSightEmbedUrlResponse> errorResponse = new ApiResponse<>(
                "ERROR", HttpStatus.INTERNAL_SERVER_ERROR.value(),
                "Unexpected error occurred while generating QuickSight embed URL",
                null, java.time.LocalDateTime.now());
            return ResponseUtil.buildResponse(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get dashboard list with filtering capabilities
     *
     * @param type Dashboard type filter (optional)
     * @param module Dashboard module filter (optional)
     * @param category Dashboard category filter (optional)
     * @return ResponseEntity containing dashboard list
     */
    @GetMapping("/dashboards")
    @ApiOperation(value = "Get dashboard list with filtering",
                  notes = "Retrieves a list of dashboards with optional filtering by type, module, and category")
    public ResponseEntity<ApiResponse<List<DashboardResponse>>> getDashboardList(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String module,
            @RequestParam(required = false) String category) {

        try {
            log.info("Controller: Received request to get dashboard list with filters - type: {}, module: {}, category: {}",
                    type, module, category);

            ApiResponse<List<DashboardResponse>> response = reportReadFacade.getDashboardList(type, module, category);

            if (response.getData() != null) {
                log.info("Controller: Successfully retrieved dashboard list with {} items", response.getData().size());
                return ResponseUtil.buildResponse(response, HttpStatus.OK);
            } else {
                log.error("Controller: Failed to retrieve dashboard list");
                return ResponseUtil.buildResponse(response, HttpStatus.INTERNAL_SERVER_ERROR);
            }

        } catch (Exception e) {
            log.error("Controller: Error retrieving dashboard list: {}", e.getMessage(), e);
            ApiResponse<List<DashboardResponse>> errorResponse = new ApiResponse<>("ERROR", HttpStatus.INTERNAL_SERVER_ERROR.value(),
                "Error retrieving dashboard list: " + e.getMessage(), null, java.time.LocalDateTime.now());
            return ResponseUtil.buildResponse(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Health check endpoint for the report module
     *
     * @return ResponseEntity with health status
     */
    @GetMapping("/health")
    @ApiOperation(value = "Health check for Report module",
                  notes = "Returns the health status of the Report module")
    public ResponseEntity<ApiResponse<String>> healthCheck() {
        log.info("Controller: Report module health check requested");

        ApiResponse<String> response = new ApiResponse<>("Report module is healthy", "OK");
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
