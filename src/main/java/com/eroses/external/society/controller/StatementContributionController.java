package com.eroses.external.society.controller;

import com.amazonaws.services.lambda.runtime.events.APIGatewayV2CustomAuthorizerEvent;
import com.eroses.external.society.api.facade.StatementContributionReadFacade;
import com.eroses.external.society.api.facade.StatementContributionWriteFacade;
import com.eroses.external.society.dto.request.statement.StatementContributionCreateRequest;
import com.eroses.external.society.dto.request.statement.StatementContributionEditRequest;
import com.eroses.external.society.dto.request.statement.StatementContributionGetAllRequest;
import com.eroses.external.society.dto.request.statement.StatementContributionGetOneRequest;
import com.eroses.external.society.dto.response.statement.StatementContributionCreateResponse;
import com.eroses.external.society.dto.response.statement.StatementContributionDeleteResponse;
import com.eroses.external.society.dto.response.statement.StatementContributionEditResponse;
import com.eroses.external.society.dto.response.statement.StatementContributionGetOneResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.model.Statement;
import com.eroses.external.society.model.StatementContribution;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/statement/statement-contribution")
public class StatementContributionController {

    private final StatementContributionWriteFacade statementContributionWriteFacade;
    private final StatementContributionReadFacade statementContributionReadFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<StatementContributionCreateResponse>> createStatementContribution(
            @RequestParam Long societyId,
            @RequestParam(required = false) Long branchId,
            @RequestParam Long statementId,
            @RequestParam Long contributionCode,
            @RequestBody StatementContributionCreateRequest request) throws Exception {
        return statementContributionWriteFacade.create(societyId, branchId, statementId, contributionCode, request);
    }

    @GetMapping(value = "/list")
    public ResponseEntity<ApiResponse<Paging<StatementContributionGetOneResponse>>> listStatementContributions(
            @RequestParam Long societyId,
            @RequestParam(required = false) Long branchId,
            @RequestParam Long statementId,
            @RequestParam String contributionCode,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        StatementContributionGetAllRequest request = new StatementContributionGetAllRequest(societyId, branchId, statementId, contributionCode, pageNo, pageSize);
        ApiResponse<Paging<StatementContributionGetOneResponse>> response = statementContributionReadFacade.listStatementContributions(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/{id}")
    public ResponseEntity<ApiResponse<StatementContributionGetOneResponse>> getStatementContribution(
            @PathVariable Long id,
            @RequestParam(required = false) Long societyId,
            @RequestParam(required = false) Long branchId,
            @RequestParam(required = false) Long statementId) {
        StatementContributionGetOneRequest request = new StatementContributionGetOneRequest(id, statementId, societyId, branchId);
        ApiResponse<StatementContributionGetOneResponse> response = statementContributionReadFacade.getById(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/{id}/edit")
    public ResponseEntity<ApiResponse<StatementContributionEditResponse>> editStatementContribution(
            @PathVariable Long id, @RequestBody StatementContributionEditRequest request) throws Exception {
        return statementContributionWriteFacade.update(id, request);
    }

    @PutMapping(value = "/{id}/delete")
    public ResponseEntity<ApiResponse<StatementContributionDeleteResponse>> deleteStatementContribution(
            @PathVariable Long id,
            @RequestParam(required = false) Long societyId,
            @RequestParam(required = false) Long branchId,
            @RequestParam(required = false) Long statementId) throws Exception {
        return statementContributionWriteFacade.delete(id, statementId, societyId, branchId);
    }
}
