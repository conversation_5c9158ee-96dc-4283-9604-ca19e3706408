package com.eroses.external.society.controller;


import com.eroses.external.society.api.facade.ExampleReadFacade;
import com.eroses.external.society.api.facade.ExampleWriteFacade;
import com.eroses.external.society.dto.request.ExampleRequest;
import com.eroses.external.society.model.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/example")
public class ExampleController {

    private ExampleWriteFacade exampleWriteFacade;

    private ExampleReadFacade exampleReadFacade;


    @PostMapping(value = "/create")
    public Response<Long> createExample(@RequestBody ExampleRequest request){

        Response<Long> a = exampleWriteFacade.create(request);

        return a;
    }

//    @GetMapping(value = "/paging")
//    public Paging<ExampleInfo> getExample(@RequestBody ExampleRequest request){
//
//        Response<Long> a = exampleReadFacade.paging(request);
//
//        return Assert.take(exampleReadFacade.paging(request));
//    }


}
