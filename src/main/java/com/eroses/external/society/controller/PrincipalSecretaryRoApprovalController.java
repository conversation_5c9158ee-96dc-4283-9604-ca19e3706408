package com.eroses.external.society.controller;

import com.eroses.external.society.api.converter.input.PrincipalSecretaryRoApprovalApiInputConverter;
import com.eroses.external.society.api.facade.PrincipalSecretaryRoApprovalReadFacade;
import com.eroses.external.society.api.facade.PrincipalSecretaryRoApprovalWriteFacade;
import com.eroses.external.society.dto.request.BranchApprovalDecisionRequest;
import com.eroses.external.society.dto.request.PrincipalSecretaryRoApprovalCreateRequest;
import com.eroses.external.society.dto.request.PrincipalSecretaryRoApprovalPagingRequest;
import com.eroses.external.society.mappers.PrincipalSecretaryDao;
import com.eroses.external.society.model.*;
import com.eroses.external.society.utils.HeadersUtil;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/society/secretary/RoApproval")
public class PrincipalSecretaryRoApprovalController {

    private final PrincipalSecretaryRoApprovalApiInputConverter principalSecretaryRoApprovalApiInputConverter;
    private final PrincipalSecretaryRoApprovalWriteFacade principalSecretaryRoApprovalWriteFacade;
    private final PrincipalSecretaryRoApprovalReadFacade principalSecretaryRoApprovalReadFacade;
    private final PrincipalSecretaryDao principalSecretaryDao;

    @GetMapping("/admin/search")
    public ResponseEntity<ApiResponse<Paging<PrincipalSecretaryRoApproval>>> getAll(@ModelAttribute PrincipalSecretaryRoApprovalPagingRequest principalSecretaryRoApprovalPagingRequest) {
        ApiResponse<Paging<PrincipalSecretaryRoApproval>> principalSecretaryRoApprovals = principalSecretaryRoApprovalReadFacade.getAll(principalSecretaryRoApprovalPagingRequest);
        return ResponseUtil.buildResponse(principalSecretaryRoApprovals, HttpStatus.OK);
    }

    @PutMapping("/approvalInfo/admin/update")
    public ResponseEntity<ApiResponse<Long>> updatePrincipalSecretaryRoApproval(@RequestBody PrincipalSecretaryRoApprovalCreateRequest principalSecretaryRoApprovalCreateRequest){
//        Long societyCommitteeId = principalSecretaryRoApprovalCreateRequest.getSecretaryId();
//        PrincipalSecretary principalSecretary = principalSecretaryDao.findById(societyCommitteeId);
//        if (principalSecretary == null) {
//            throw new EntityNotFoundException("PrincipalSecretary not found for society_committee_id: " + societyCommitteeId);
//        }
//        PrincipalSecretaryRoApproval principalSecretaryRoApproval = principalSecretaryRoApprovalApiInputConverter.convertToModel(principalSecretaryRoApprovalCreateRequest);
//        principalSecretaryRoApproval.setPrincipalSecretary(principalSecretary);
//        ApiResponse<Long> response = principalSecretaryRoApprovalWriteFacade.updatePrincipalSecretaryRoApproval(principalSecretaryRoApproval);

        return ResponseUtil.buildResponse(null, HttpStatus.OK);
    }



    private <T> ResponseEntity<ApiResponse<T>> buildResponse(ApiResponse<T> response, HttpStatus status) {
        HttpHeaders headers = HeadersUtil.createHeaders();
        return new ResponseEntity<>(response, headers, status);
    }
}
