package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.NonCitizenCommitteeReadFacade;
import com.eroses.external.society.api.facade.NonCitizenCommitteeWriteFacade;
import com.eroses.external.society.dto.request.branchCommittee.BranchNonCitizenCommitteeCreateRequest;
import com.eroses.external.society.dto.request.branchCommittee.BranchNonCitizenCommitteeUpdateRequest;
import com.eroses.external.society.dto.request.committee.NonCitizenCommitteeGetAllRequest;
import com.eroses.external.society.dto.request.nonCitizenCommittee.NonCitizenCommitteeCreateRequest;
import com.eroses.external.society.dto.request.nonCitizenCommittee.NonCitizenCommitteeEditRequest;
import com.eroses.external.society.dto.request.nonCitizenCommittee.NonCitizenCommitteeSubmitRequest;
import com.eroses.external.society.dto.response.nonCitizenCommittee.*;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.NonCitizenCommittee;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.security.annotation.RequireTargetUserNotBlacklisted;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/nonCitizenCommittee")
public class SocietyNonCitizenCommitteeController {
    private final NonCitizenCommitteeWriteFacade nonCitizenCommitteeWriteFacade;
    private final NonCitizenCommitteeReadFacade nonCitizenCommitteeReadFacade;

    @RequireTargetUserNotBlacklisted
    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<NonCitizenCommitteeCreateResponse>> createNonCitizenCommittee(@RequestBody NonCitizenCommitteeCreateRequest request) throws Exception {
        ApiResponse<NonCitizenCommitteeCreateResponse> response = nonCitizenCommitteeWriteFacade.create(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @RequireTargetUserNotBlacklisted(identificationNoParam = "committeeIcNo")
    @PostMapping(value = "/branch/create")
    public ResponseEntity<ApiResponse<NonCitizenCommitteeCreateResponse>> createBranchNonCitizenCommittee(@Valid @RequestBody BranchNonCitizenCommitteeCreateRequest request) throws Exception {
        ApiResponse<NonCitizenCommitteeCreateResponse> response = nonCitizenCommitteeWriteFacade.createBranchNonCitizenCommittee(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @RequireTargetUserNotBlacklisted
    @PutMapping(value = "/{id}/edit")
    public ResponseEntity<ApiResponse<NonCitizenCommitteeEditResponse>> editNonCitizenCommittee(@PathVariable Long id, @RequestBody NonCitizenCommitteeEditRequest request) throws Exception {
        ApiResponse<NonCitizenCommitteeEditResponse> response = nonCitizenCommitteeWriteFacade.update(id, request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @RequireTargetUserNotBlacklisted(identificationNoParam = "committeeIcNo")
    @PutMapping(value = "/branch/{id}")
    public ResponseEntity<ApiResponse<Long>> updateBranchNonCitizenCommittee(@PathVariable Long id, @Valid @RequestBody BranchNonCitizenCommitteeUpdateRequest request) throws Exception {
        ApiResponse<Long> response = nonCitizenCommitteeWriteFacade.updateBranchNonCitizenCommittee(id, request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/update-by-list")
    public ResponseEntity<ApiResponse<List<NonCitizenCommitteeEditResponse>>> updateNonCitizenCommittee( @RequestBody NonCitizenCommitteeEditRequest request) throws Exception {
        ApiResponse<List<NonCitizenCommitteeEditResponse>> response = nonCitizenCommitteeWriteFacade.updateList(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping("/submit")
    public ResponseEntity<ApiResponse<List<NonCitizenCommitteeEditResponse>>> submitNonCitizenCommittee(@RequestBody @Valid NonCitizenCommitteeSubmitRequest request) {
        ApiResponse<List<NonCitizenCommitteeEditResponse>> response = nonCitizenCommitteeWriteFacade.submit(request.societyId());
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteNonCitizenCommittee(@PathVariable("id") Long id) {
        ApiResponse<Void> response = nonCitizenCommitteeWriteFacade.remove(id);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @GetMapping(value = "/getAll")
    public ResponseEntity<ApiResponse<Paging<NonCitizenCommittee>>> getAllNonCitizenCommittee(
            @RequestParam(value = "societyId",required = false) Long societyId,
            @RequestParam(value = "branchId",required = false) Long branchId,
            @RequestParam(value = "id",required = false) Long id,
            @RequestParam(value = "appointedDate", required = false) LocalDate appointedDate,
            @RequestParam(value = "meetingId", required = false) Long meetingId,
            @RequestParam(value = "documentType", required = false) Boolean documentType,
            @RequestParam(value = "pageNo", required = false) Integer pageNo,
            @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        NonCitizenCommitteeGetAllRequest request = new NonCitizenCommitteeGetAllRequest();
        if(societyId != null){
            request.setSocietyId(societyId);
        }
        if (branchId != null) {
            request.setBranchId(branchId);
        }
        if (id != null) {
            request.setId(id);
        }
        if (appointedDate != null) {
            request.setAppointedDate(appointedDate);
        }
        if (meetingId != null) {
            request.setMeetingId(meetingId);
        }
        if (documentType != null) {
            request.setDocumentType(documentType);
        }
        if (pageNo != null) {
            request.setPageNo(pageNo);
        }
        if (pageSize != null) {
            request.setPageSize(pageSize);
        }

        ApiResponse<Paging<NonCitizenCommittee>> response = nonCitizenCommitteeReadFacade.getAll(request);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @GetMapping("/countAll")
    public ResponseEntity<ApiResponse<NonCitizenCommitteeCountResponse>> countAllNonCitizenAjk(@Valid @NotNull @RequestParam("societyId") Long societyId) {
        return ResponseUtil.buildResponse(nonCitizenCommitteeReadFacade.countSocietyActiveCommittee(societyId), HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<NonCitizenCommitteeGetResponse>> findById(@PathVariable Long id) {
        ApiResponse<NonCitizenCommitteeGetResponse> response = nonCitizenCommitteeReadFacade.findById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @GetMapping("/getPositionsList")
    public ResponseEntity<ApiResponse<List<NonCitizenPositionsListResponse>>> getPositionsList(
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "appointedDate", required = false) LocalDate appointedDate) {
        ApiResponse<List<NonCitizenPositionsListResponse>> response = nonCitizenCommitteeReadFacade.getPositionsList(societyId, branchId, appointedDate);

        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }
}
