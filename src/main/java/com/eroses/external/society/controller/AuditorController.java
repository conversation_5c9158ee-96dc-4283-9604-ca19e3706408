package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.AuditorReadFacade;
import com.eroses.external.society.api.facade.AuditorWriteFacade;
import com.eroses.external.society.dto.request.statement.AuditorCreateRequest;
import com.eroses.external.society.dto.request.statement.AuditorEditRequest;
import com.eroses.external.society.dto.response.AuditorAppointmentDatesResponse;
import com.eroses.external.society.dto.response.statement.*;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.security.annotation.RequireSocietyMembership;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/statement/auditor")
public class AuditorController {
    private final AuditorWriteFacade auditorWriteFacade;
    private final AuditorReadFacade auditorReadFacade;

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<AuditorCreateResponse>> createAuditor(
            @RequestParam Long societyId,
            @RequestParam(required = false) Long branchId,
            @Valid @RequestBody AuditorCreateRequest request) throws Exception {
        return auditorWriteFacade.create(societyId, branchId, request);
    }

    @GetMapping("/list")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<Paging<AuditorListResponse>>> listAuditor(
            @RequestParam Long societyId,
            @RequestParam(required = false) Long branchId,
            @RequestParam(required = false) Long statementId,
            @RequestParam(value = "appointmentDate", required = false) LocalDate appointmentDate,
            @RequestParam(required = false, defaultValue = "1") Integer pageNo ,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        if (statementId != null) params.put("statementId", statementId);
        if (appointmentDate != null) params.put("appointmentDate", appointmentDate);
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);

        return auditorReadFacade.listAuditor(params);
    }

    @GetMapping("/{id}/get")
    public ResponseEntity<ApiResponse<AuditorListResponse>> getAuditor(
            @PathVariable Long id
    ) {
        return auditorReadFacade.getAuditor(id);
    }

    // Use to do update and delete
    @PutMapping("/{id}/edit")
    public ResponseEntity<ApiResponse<AuditorEditResponse>> editAuditor(
            @PathVariable Long id,
            @RequestBody AuditorEditRequest request
    ) throws Exception {
        return auditorWriteFacade.update(id, request);
    }

    @GetMapping("/countAuditors")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<AuditorCountResponse>> countAuditors(
            @RequestParam Long societyId,
            @RequestParam(required = false) Long branchId
    ) {
        return auditorReadFacade.countAuditors(societyId, branchId);
    }

    @PutMapping("/{id}/delete")
    public ResponseEntity<ApiResponse<AuditorDeleteResponse>> deleteAuditor(
            @PathVariable Long id
    ) throws Exception {
        return auditorWriteFacade.delete(id);
    }

    @GetMapping("/getAll")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<List<AuditorListResponse>>> getAll(
            @RequestParam Long societyId,
            @RequestParam(required = false) Long branchId) {

        return auditorReadFacade.getAll(societyId, branchId);
    }

        @GetMapping("/getAuditorAppointmentDates")
    public ResponseEntity<ApiResponse<AuditorAppointmentDatesResponse>> getAuditorAppointmentDates(
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId) {
        ApiResponse<AuditorAppointmentDatesResponse> response = auditorReadFacade.getAuditorAppointmentDates(societyId, branchId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
