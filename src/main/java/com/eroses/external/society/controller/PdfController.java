package com.eroses.external.society.controller;

import java.io.IOException;

import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.eroses.external.society.api.facade.PdfFacade;
import com.eroses.external.society.dto.request.pdf.BranchSecretarySlipRequest;
import com.eroses.external.society.dto.request.pdf.MeetingMinutesRequest;
import com.eroses.external.society.dto.request.pdf.PaymentSlipRequest;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/society/pdf/")
public class PdfController {
    private final PdfFacade pdfFacade;

    @PostMapping("/paymentReceipt")
    public ResponseEntity<Object> paymentReceipt(@RequestBody @Valid PaymentSlipRequest request) throws Exception {
        return ResponseEntity.ok(pdfFacade.getPaymentReceipt(request));
    }

    @PostMapping("/meetingMinutes")
    public ResponseEntity<Object> meetingMinutes(@RequestBody MeetingMinutesRequest request) throws IOException {
        return ResponseEntity.ok(pdfFacade.getMeetingMinutes(request));
    }

    @GetMapping("/getPDFTemplate")
    public ResponseEntity<Object> getPDFTemplate() {
        return ResponseEntity.ok(pdfFacade.getPDFTemplate());
    }

    @GetMapping("/branch-secretary-slip/{newSecretaryBranchId}")
    public ResponseEntity<byte[]> getBranchSecretarySlip(
        @PathVariable Long newSecretaryBranchId
    ) throws IOException {
        byte[] binary = pdfFacade.getBranchSecretarySlip(newSecretaryBranchId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDisposition(ContentDisposition.builder("attachment").filename("test.pdf").build());
        headers.setContentLength(binary.length);
        return new ResponseEntity<byte[]>(binary, headers, HttpStatus.OK);
    }

}
