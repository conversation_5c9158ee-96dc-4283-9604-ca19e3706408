package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.BranchAmendmentReadFacade;
import com.eroses.external.society.api.facade.BranchAmendmentWriteFacade;
import com.eroses.external.society.dto.request.BranchAmendmentCreateRequest;
import com.eroses.external.society.dto.request.BranchAmendmentUpdateRequest;
import com.eroses.external.society.dto.response.BranchAmendmentGetResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/external/branchAmendment")
public class BranchAmendmentController {
    private final BranchAmendmentWriteFacade branchAmendmentWriteFacade;
    private final BranchAmendmentReadFacade branchAmendmentReadFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<Long>> create(@RequestBody BranchAmendmentCreateRequest branchAmendmentCreateRequest) throws Exception {
        ApiResponse<Long> branchAmendmentId = branchAmendmentWriteFacade.create(branchAmendmentCreateRequest);
        return ResponseUtil.buildResponse(branchAmendmentId, HttpStatus.OK);
    }

    @GetMapping(value = "/branchAmendments")
    public ResponseEntity<ApiResponse<List<BranchAmendmentGetResponse>>> getAllByCriteria(
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchNameQuery", required = false) String branchNameQuery,
            @RequestParam(value = "applicationStatusCode", required = false, defaultValue = "0") Integer applicationStatusCode) {

        Map<String, Object> params = new HashMap<>();
        if (societyId != null) { params.put("societyId", societyId); }
        if (branchNameQuery != null) { params.put("branchNameQuery", branchNameQuery); }
        if (applicationStatusCode != 0) { params.put("applicationStatusCode", applicationStatusCode); }

        ApiResponse<List<BranchAmendmentGetResponse>> response = branchAmendmentReadFacade.getAllByCriteria(params);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/{id}")
    public ResponseEntity<ApiResponse<BranchAmendmentGetResponse>> findById(
            @PathVariable("id") Long id) {

        ApiResponse<BranchAmendmentGetResponse> response = branchAmendmentReadFacade.findById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PatchMapping(value = "/update")
    public ResponseEntity<ApiResponse<Long>> update(@RequestBody BranchAmendmentUpdateRequest branchAmendmentUpdateRequest) throws Exception {
        ApiResponse<Long> branchAmendmentId = branchAmendmentWriteFacade.update(branchAmendmentUpdateRequest);
        return ResponseUtil.buildResponse(branchAmendmentId, HttpStatus.OK);
    }

    @DeleteMapping(value = "/{id}")
    public ResponseEntity<ApiResponse<Long>> delete(
            @PathVariable("id") Long id) throws Exception {
        ApiResponse<Long> branchAmendmentId = branchAmendmentWriteFacade.delete(id);
        return ResponseUtil.buildResponse(branchAmendmentId, HttpStatus.OK);
    }
}