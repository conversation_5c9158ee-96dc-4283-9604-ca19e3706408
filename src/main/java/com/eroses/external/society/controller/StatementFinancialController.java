package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.StatementFinancialReadFacade;
import com.eroses.external.society.api.facade.StatementFinancialWriteFacade;
import com.eroses.external.society.dto.request.statement.StatementFinancialCreateRequest;
import com.eroses.external.society.dto.request.statement.StatementFinancialEditRequest;
import com.eroses.external.society.dto.request.statement.StatementFinancialGetRequest;
import com.eroses.external.society.dto.response.statement.StatementFinancialCreateResponse;
import com.eroses.external.society.dto.response.statement.StatementFinancialEditResponse;
import com.eroses.external.society.dto.response.statement.StatementFinancialGetOneResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/statement/statement-financial")
public class StatementFinancialController {
    private final StatementFinancialWriteFacade statementFinancialWriteFacade;
    private final StatementFinancialReadFacade statementFinancialReadFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<StatementFinancialCreateResponse>> createStatementFinancial(
            @RequestParam Long societyId,
            @RequestParam Long statementId,
            @RequestParam(required = false) Long branchId,
            @RequestBody StatementFinancialCreateRequest request) throws Exception {

        ApiResponse<StatementFinancialCreateResponse> response = statementFinancialWriteFacade.create(societyId, statementId, branchId, request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/get")
    public ResponseEntity<ApiResponse<StatementFinancialGetOneResponse>> getStatementFinancial(
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "statementId", required = false) Long statementId) {
        StatementFinancialGetRequest request = new StatementFinancialGetRequest(societyId, branchId, statementId);
        ApiResponse<StatementFinancialGetOneResponse> response = statementFinancialReadFacade.get(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    // Can use id because can pass in from the get api that they have to use to display the form for edit
    // Updates anything passed in, reuse for aset and liability page, needs to be update because its on the next form
    @PutMapping(value = "/{statementId}/edit")
    public ResponseEntity<ApiResponse<StatementFinancialEditResponse>> editStatementFinancial(
            @PathVariable Long statementId,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestBody StatementFinancialEditRequest request) throws Exception {


        ApiResponse<StatementFinancialEditResponse> response = statementFinancialWriteFacade.update(statementId, societyId, branchId, request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    // =====================================================================================================//
}
