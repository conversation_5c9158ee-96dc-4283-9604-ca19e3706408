package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.DocumentTemplateReadFacade;
import com.eroses.external.society.api.facade.DocumentTemplateWriteFacade;
import com.eroses.external.society.dto.request.documentTemplate.DocumentTemplateCreateRequest;
import com.eroses.external.society.dto.response.documentTemplate.DocumentTemplateCreateResponse;
import com.eroses.external.society.dto.response.documentTemplate.DocumentTemplateGetResponse;
import com.eroses.external.society.model.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/document-template")
public class DocumentTemplateController {
    private final DocumentTemplateWriteFacade documentTemplateWriteFacade;
    private final DocumentTemplateReadFacade documentTemplateReadFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<DocumentTemplateCreateResponse>> createDocumentTemplate(
            @RequestParam Long statementId,
            @RequestBody DocumentTemplateCreateRequest request) throws Exception {
        return documentTemplateWriteFacade.create(statementId, request);
    }

    @GetMapping(value = "/{id}/get")
    public ResponseEntity<ApiResponse<DocumentTemplateGetResponse>> getDocumentTemplate(
            @PathVariable Long id
    ) {
        return documentTemplateReadFacade.getDocumentTemplate(id);
    }
}