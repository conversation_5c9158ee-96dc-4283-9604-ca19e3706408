package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.SocietyDocumentTemplateReadFacade;
import com.eroses.external.society.api.facade.SocietyDocumentTemplateWriteFacade;
import com.eroses.external.society.dto.request.searchInformation.SocietyDocumentTemplateCreateRequest;
import com.eroses.external.society.dto.response.searchInformation.SocietyDocumentTemplateCreateResponse;
import com.eroses.external.society.dto.response.searchInformation.SocietyDocumentTemplateGetAllBySocietyIdResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/searchInformation/societyDocumentTemplate")
public class SocietyDocumentTemplateController {
    private final SocietyDocumentTemplateWriteFacade societyDocumentTemplateWriteFacade;
    private final SocietyDocumentTemplateReadFacade societyDocumentTemplateReadFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<SocietyDocumentTemplateCreateResponse>> create(@Valid @RequestBody SocietyDocumentTemplateCreateRequest request) throws Exception {
        ApiResponse<SocietyDocumentTemplateCreateResponse> response = societyDocumentTemplateWriteFacade.create(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @GetMapping(value = "/getAllBySocietyId")
    public ResponseEntity<ApiResponse<SocietyDocumentTemplateGetAllBySocietyIdResponse>> getAllBySocietyId(@RequestParam Long societyId) throws Exception {
        ApiResponse<SocietyDocumentTemplateGetAllBySocietyIdResponse> response = societyDocumentTemplateReadFacade.getAllBySocietyId(societyId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
