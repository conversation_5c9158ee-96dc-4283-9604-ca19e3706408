package com.eroses.external.society.controller;

import com.eroses.external.society.api.converter.input.MeetingApiInputConverter;
import com.eroses.external.society.api.converter.input.MeetingMemberAttendanceApiInputConverter;
import com.eroses.external.society.api.facade.MeetingMemberAttendanceReadFacade;
import com.eroses.external.society.api.facade.MeetingMemberAttendanceWriteFacade;
import com.eroses.external.society.api.facade.MeetingReadFacade;
import com.eroses.external.society.api.facade.MeetingWriteFacade;
import com.eroses.external.society.dto.request.MeetingCreateRequest;
import com.eroses.external.society.dto.request.MeetingMemberAttendanceUpdateRequest;
import com.eroses.external.society.dto.request.MeetingPagingRequest;
import com.eroses.external.society.dto.request.MeetingUpdateRequest;
import com.eroses.external.society.dto.request.statement.StatementMeetingListRequest;
import com.eroses.external.society.dto.response.meeting.BranchMeetingResponse;
import com.eroses.external.society.dto.response.meeting.MeetingPagingResponse;
import com.eroses.external.society.dto.response.meeting.MeetingResponse;
import com.eroses.external.society.dto.response.statement.StatementMeetingFindResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Meeting;
import com.eroses.external.society.model.MeetingMemberAttendance;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.security.annotation.RequireSocietyMembership;
import com.eroses.external.society.service.MeetingMemberAttendanceWriteDomainService;
import com.eroses.external.society.utils.HeadersUtil;
import com.eroses.external.society.utils.ResponseUtil;
import com.eroses.user.api.facade.UserFacade;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/society/meeting")
public class MeetingController {
    private final MeetingWriteFacade meetingWriteFacade;
    private final MeetingReadFacade meetingReadFacade;
    private final MeetingMemberAttendanceWriteFacade meetingMemberAttendanceWriteFacade;
    private final MeetingMemberAttendanceWriteDomainService meetingMemberWriteService;
    private final MeetingMemberAttendanceReadFacade meetingMemberAttendanceReadFacadeFacade;
    private final MeetingApiInputConverter meetingApiInputConverter;
    private final MeetingMemberAttendanceApiInputConverter meetingMemberAttendanceApiInputConverter;
    private final UserFacade userFacade;


    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Long>> createMeeting(@Valid @RequestBody MeetingCreateRequest meetingCreateRequest) {
        ApiResponse<Long> meetingId =  meetingWriteFacade.create(meetingCreateRequest);
        return buildResponse(meetingId, HttpStatus.OK);
    }

    @GetMapping("/getAll")
    public ResponseEntity<ApiResponse<List<Meeting>>> getAll() {
        ApiResponse<List<Meeting>> meetings = meetingReadFacade.getAll();
        for (Meeting meeting : meetings.getData()) {
            List<MeetingMemberAttendance> meetingMemberAttendances = meetingMemberAttendanceReadFacadeFacade.getAllByMeetingId(meeting.getId());
            meeting.setMeetingMemberAttendances(meetingMemberAttendances);
        }
        return buildResponse(meetings, HttpStatus.OK);
    }

    @GetMapping("/{meetingId}")
    public ResponseEntity<ApiResponse<MeetingResponse>> findById(@PathVariable Long meetingId) {
        ApiResponse<MeetingResponse> meeting = meetingReadFacade.findById(meetingId);
        return ResponseUtil.buildResponse(meeting, HttpStatus.OK);
    }

    @GetMapping("/findByBranchId/{branchId}")
    public ResponseEntity<ApiResponse<List<BranchMeetingResponse>>> findByBranchId(
            @PathVariable Long branchId, @RequestParam(required = false) List<Integer> meetingType) {
        ApiResponse<List<BranchMeetingResponse>> meeting = meetingReadFacade.findByBranchId(branchId, meetingType);
        return buildResponse(meeting, HttpStatus.OK);
    }

    @GetMapping("/findBySocietyId/{societyId}")
    public ResponseEntity<ApiResponse<List<Meeting>>> findBySocietyId(
            @PathVariable Long societyId, @RequestParam(required = false) List<Integer> meetingType) {
        ApiResponse<List<Meeting>> meetings = meetingReadFacade.findBySocietyId(societyId, meetingType);
        return ResponseUtil.buildResponse(meetings, HttpStatus.OK);
    }

    @GetMapping("/search")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<Paging<MeetingPagingResponse>>> getAll(
            @RequestParam(defaultValue = "1") int pageNo,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam Long societyId,
            @RequestParam(required = false) Long branchId,
            @RequestParam(required = false) Integer meetingType,
            @RequestParam(required = false) Integer meetingYear,
            @RequestParam(required = false) Integer status) {

        Map<String, Object> params = new HashMap<>();
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        if (meetingType != null) params.put("meetingType", meetingType);
        if (meetingYear != null) params.put("meetingYear", meetingYear);
        if (status != null) params.put("status", status);
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);

        ApiResponse<Paging<MeetingPagingResponse>> meetings = meetingReadFacade.search(params);

        return ResponseUtil.buildResponse(meetings, HttpStatus.OK);
    }

    @PutMapping("/update")
    public ResponseEntity<ApiResponse<Long>> update(@Valid @RequestBody MeetingUpdateRequest meetingUpdateRequest) {
        ApiResponse<Long> meetingId = meetingWriteFacade.update(meetingUpdateRequest);

        return ResponseUtil.buildResponse(meetingId, HttpStatus.OK);
    }

    @GetMapping(value = "/statement/get")
    public ResponseEntity<ApiResponse<StatementMeetingFindResponse>> findStatementMeeting(
            @RequestParam Long societyId,
            @RequestParam(required = false) Long branchId,
            @RequestParam Long statementId
    ) {
        StatementMeetingListRequest request = new StatementMeetingListRequest(societyId, branchId, statementId);
        return meetingReadFacade.findStatementMeeting(request);
    }

//    @PostMapping(value = "/meetingMinutes/pdf/download")
//    @GetMapping(value = "/meetingMinutes/pdf/download")
////    public void exportPdf(@RequestBody MeetingMinutesPdfRequest meetingMinutesPdfRequest, HttpServletResponse response) {
//        public ResponseEntity<byte[]> exportPdf(HttpServletResponse response) {
//        try {
//            ByteArrayOutputStream baos = PdfProvider.createMeetingMinutesPdf();
//
//            log.info("hosam controller baos:{}", baos);
//            log.info("hosam controller byte array:{}", baos.toByteArray());
//
////            response.setContentType("application/pdf");
////            response.setHeader("Content-Disposition", "inline");
////            response.setHeader("Pragma", "no-cache");
////            response.setHeader("Cache-Control", "no-cache");
////            response.setContentLength(baos.size());
////            response.getOutputStream().write(baos.toByteArray());
////            response.getOutputStream().flush();
//
//            HttpHeaders headers = new HttpHeaders();

    /// /            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=file.pdf");
//            headers.setContentDispositionFormData("attachment", "file.pdf");
//            headers.setContentType(MediaType.APPLICATION_PDF);
//            headers.setContentLength(baos.size());
//            return new ResponseEntity<byte[]>(baos.toByteArray(), headers, HttpStatus.OK);
//
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }

//    @PostMapping("/meetingMinutes/pdf/download")
//    public ResponseEntity<byte[]> downloadPdf(@RequestBody MeetingMinutesDto meetingMinutesDto) {
//        try {
//            // Generate PDF and get the base64 string
//            String base64data = pdfProvider.generatePdf(meetingMinutesDto);
//
//            // Decode Base64 to binary
//            byte[] binaryData = Base64.getDecoder().decode(base64data);
//
//            // Prepare headers
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentDispositionFormData("attachment", "file.pdf");
//            headers.setContentType(MediaType.APPLICATION_PDF);
//            headers.setContentLength(binaryData.length);
//
//            log.info("hosam controller binary:{}", binaryData);
//
//            // Return the binary data as a PDF
//            return new ResponseEntity<>(binaryData, headers, HttpStatus.OK);
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }
    private <T> ResponseEntity<ApiResponse<T>> buildResponse(ApiResponse<T> response, HttpStatus status) {
        HttpHeaders headers = HeadersUtil.createHeaders();
        return new ResponseEntity<>(response, headers, status);
    }
}
