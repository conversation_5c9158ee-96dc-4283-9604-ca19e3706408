package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.ForbiddenWriteFacade;
import com.eroses.external.society.api.facade.ForbiddenReadFacade;
import com.eroses.external.society.dto.request.fobbiden.*;
import com.eroses.external.society.dto.response.forbiddenRule.ForbiddenKeywordCreateResponse;

import com.eroses.external.society.dto.response.forbiddenRule.ForbiddenKeywordResponse;
import com.eroses.external.society.dto.response.forbiddenRule.ForbiddenKeywordSearchResponse;
import com.eroses.external.society.dto.response.forbiddenRule.ForbiddenLogoResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("society/forbidden")
public class ForbiddenRuleController {
    private final ForbiddenReadFacade forbiddenReadFacade;
    private final ForbiddenWriteFacade forbiddenWriteFacade;

    @PostMapping("/senarai/create")
    public ResponseEntity<ApiResponse<ForbiddenKeywordCreateResponse>> create(@Valid @RequestBody ForbiddenKeywordCreateRequest request) throws Exception {
        ApiResponse<ForbiddenKeywordCreateResponse> response = forbiddenWriteFacade.create(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @PostMapping("/senarai/delete/{id}")
    public ResponseEntity<ApiResponse<Long>> delete(@PathVariable Long id) throws Exception {
        ApiResponse<Long> response = forbiddenWriteFacade.delete(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping("/senarai/update/{id}")
    public ResponseEntity<ApiResponse<ForbiddenKeywordResponse>> update(@PathVariable Long id, @Valid @RequestBody ForbiddenKeywordUpdateRequest request) throws Exception {
        ApiResponse<ForbiddenKeywordResponse> response = forbiddenWriteFacade.update(id, request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/get-all")
    public ResponseEntity<ApiResponse<List<ForbiddenKeywordResponse>>> getAll() {
        ApiResponse<List<ForbiddenKeywordResponse>> response = forbiddenReadFacade.getAll();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/get-all/senarai-larangan")
    public ResponseEntity<ApiResponse<List<ForbiddenKeywordResponse>>> getAllSenaraiLarangan() {
        ApiResponse<List<ForbiddenKeywordResponse>> response = forbiddenReadFacade.getAllSenaraiLarangan();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/get-all/senarai-larangan/by-page")
    public ResponseEntity<ApiResponse<Paging<ForbiddenKeywordResponse>>> getAllSenaraiLaranganPagination(
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<ForbiddenKeywordResponse>> response = forbiddenReadFacade.getAllSenaraiLaranganPagination(pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);

    }

    @GetMapping("/get-all/senarai-kelabu")
    public ResponseEntity<ApiResponse<List<ForbiddenKeywordResponse>>> getAllSenaraiKelabu() {
         ApiResponse<List<ForbiddenKeywordResponse>> response = forbiddenReadFacade.getAllSenaraiKelabu();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/get-all/senarai-kelabu/by-page")
    public ResponseEntity<ApiResponse<Paging<ForbiddenKeywordResponse>>> getAllSenaraiKelabuPagination(
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<ForbiddenKeywordResponse>> response = forbiddenReadFacade.getAllSenaraiKelabuPagination(pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/nama-larangan/{id}")
    public ResponseEntity<ApiResponse<ForbiddenKeywordResponse>> getOneSenaraiById(@PathVariable Long id) {
        ApiResponse<ForbiddenKeywordResponse> response = forbiddenReadFacade.getOneById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping("/senarai-larangan/check")
    public ResponseEntity<ApiResponse<Boolean>> checkSenaraiLarangan(@RequestBody ForbiddenKeywordCheckRequest request) {
        ApiResponse<Boolean> response = forbiddenReadFacade.checkSenaraiLarangan(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Paging<ForbiddenKeywordSearchResponse>>> searchForbidden(
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "type", required = true) String forbiddenType,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "status", required = false) String statusCode,
            @RequestParam(value = "activeStatus", required = false) Boolean activeStatus) {
        ForbiddenKeywordSearchRequest request = new ForbiddenKeywordSearchRequest(keyword, forbiddenType, pageNo, pageSize, statusCode, activeStatus);
        ApiResponse<Paging<ForbiddenKeywordSearchResponse>> response = forbiddenReadFacade.searchLarangan(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("larangan-logo/search")
    public ResponseEntity<ApiResponse<Paging<ForbiddenLogoResponse>>> searchLaranganLogo(
            @RequestParam(value = "catatan", required = false) String catatan,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "activeStatus", required = false) Boolean activeStatus){
        ApiResponse<Paging<ForbiddenLogoResponse>> response = forbiddenReadFacade.searchLaranganLogo(catatan, pageNo, pageSize, activeStatus);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping("/senarai-kelabu/check")
    public ResponseEntity<ApiResponse<Boolean>> checkLaranganKelabu(@RequestBody ForbiddenKeywordCheckRequest request) {
        ApiResponse<Boolean> response = forbiddenReadFacade.checkSenaraiKelabu(request.getKeyword(), request.getCheckType());
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    //    @GetMapping("/larangan-kelabu/search/{keyword}")
//    public ResponseEntity<ApiResponse<List<ForbiddenKeywordCreateResponse>>> searchLaranganKelabu(@PathVariable String keyword) {
//        ApiResponse<List<ForbiddenKeywordCreateResponse>> response = forbiddenReadFacade.searchLaranganKelabu(keyword);
//        return ResponseUtil.buildResponse(response, HttpStatus.OK);
//    }
    @PostMapping("/larangan-logo/check-existing")
    public ResponseEntity<ApiResponse<Boolean>> checkExistingLaranganLogo(@RequestParam("logoFile") MultipartFile logo) {
        ApiResponse<Boolean> response = forbiddenReadFacade.checkExistingLogo(logo);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }


    @PostMapping("/larangan-logo/create")
    public ResponseEntity<ApiResponse<ForbiddenLogoResponse>> createLaranganLogo(@Valid @RequestBody ForbiddenLogoCreateRequest request) throws Exception {
        ApiResponse<ForbiddenLogoResponse> response = forbiddenWriteFacade.createLaranganLogo(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @PutMapping("/larangan-logo/update/{id}")
    public ResponseEntity<ApiResponse<ForbiddenLogoResponse>> updateLaranganLogo(@PathVariable Long id, @Valid @RequestBody ForbiddenLogoUpdateRequest request) throws Exception {
        ApiResponse<ForbiddenLogoResponse> response = forbiddenWriteFacade.updateLaranganLogo(id, request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @DeleteMapping("/larangan-logo/delete/{id}")
    public ResponseEntity<ApiResponse<Long>> deleteLaranganLogo(@PathVariable Long id) throws Exception {
        ApiResponse<Long> response = forbiddenWriteFacade.deleteLaranganLogo(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/larangan-logo/getAll")
    public ResponseEntity<ApiResponse<List<ForbiddenLogoResponse>>> getAllLaranganLogo() {
        ApiResponse<List<ForbiddenLogoResponse>> response = forbiddenReadFacade.getAllLaranganLogo();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/larangan-logo/{id}")
    public ResponseEntity<ApiResponse<ForbiddenLogoResponse>> getLaranganLogoById(@PathVariable Long id) {
        ApiResponse<ForbiddenLogoResponse> response = forbiddenReadFacade.getLaranganLogoById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping("/larangan-logo/check")
    public ResponseEntity<ApiResponse<Boolean>> checkLaranganLogoMultipart(@RequestParam("logoFile") MultipartFile request) {
        ApiResponse<Boolean> response = forbiddenReadFacade.checkLaranganLogo(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

//    @PostMapping("/larangan-logo/check")
//    public ResponseEntity<ApiResponse<Boolean>> checkLaranganLogo(@RequestBody ForbiddenLogoCheckRequest request)
//




}
