package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.SearchInformationDocumentTemplateReadFacade;
import com.eroses.external.society.api.facade.SearchInformationDocumentTemplateWriteFacade;
import com.eroses.external.society.dto.request.searchInformation.SearchInformationDocumentTemplateCreateRequest;
import com.eroses.external.society.dto.response.searchInformation.*;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/searchInformation/documentTemplate")
public class SearchInformationDocumentTemplateController {
    private final SearchInformationDocumentTemplateWriteFacade searchInformationDocumentTemplateWriteFacade;
    private final SearchInformationDocumentTemplateReadFacade searchInformationDocumentTemplateReadFacade;


    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<SearchInformationDocumentTemplateCreateResponse>> create(
            @RequestPart(value = "request", required = true) SearchInformationDocumentTemplateCreateRequest request,
            @RequestPart(value = "file", required = false) MultipartFile file) throws Exception {

        log.info("Request: {}", request);
        ApiResponse<SearchInformationDocumentTemplateCreateResponse> response = searchInformationDocumentTemplateWriteFacade.create(request, file);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @GetMapping(value = "/getByCode")
    public ResponseEntity<ApiResponse<SearchInformationDocumentTemplateGetByCodeResponse>> getByCode(@RequestParam String documentTemplateCode) throws Exception {

        ApiResponse<SearchInformationDocumentTemplateGetByCodeResponse> response = searchInformationDocumentTemplateReadFacade.getByCode(documentTemplateCode);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
