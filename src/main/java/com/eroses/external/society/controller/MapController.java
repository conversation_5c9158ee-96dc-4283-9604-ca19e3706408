package com.eroses.external.society.controller;

import com.eroses.external.society.utils.SecretsManagerUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/society/map")
public class MapController {

    @Value("${aws.region}")
    private String region;

    @Value("${aws.secrets-manager.map}")
    private String mapSecret;

    @GetMapping("/apiKey")
    public ResponseEntity<?> getSecret() {
        try {
            Map<String, String> mapSecrets = SecretsManagerUtil.getSecret(mapSecret, region);
            String apiKey = mapSecrets.get("apiKey");

            if (apiKey == null || apiKey.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "Map API key not found"));
            }

            return ResponseEntity.ok(Map.of("apiKey", apiKey));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of("error", "Failed to retrieve Map API key"));
        }
    }
}
