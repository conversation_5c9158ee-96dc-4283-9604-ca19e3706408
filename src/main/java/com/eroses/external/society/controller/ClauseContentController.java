package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.admin.ClauseContentReadFacade;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/constitution/clausecontenttemplate")
public class ClauseContentController {

    private final ClauseContentReadFacade clauseContentReadFacade;

    @GetMapping("/getByConsTypeAndClauseNo")
    public ResponseEntity<ApiResponse<Object>> getByConsTypeAndClauseNo(
            @RequestParam(value = "constitutionType") Long constitutionType,
            @RequestParam(value = "clauseNo") Long clauseNo,
            @RequestParam(value = "societyId", required = false) Long societyId) {

        ApiResponse<Object> response = clauseContentReadFacade.getByConsTypeAndClauseNo(constitutionType, clauseNo, societyId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
