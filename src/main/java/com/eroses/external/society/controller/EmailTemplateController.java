package com.eroses.external.society.controller;

import com.eroses.external.society.api.converter.input.EmailTemplateApiInputConverter;
import com.eroses.external.society.api.facade.*;
import com.eroses.external.society.dto.request.*;
import com.eroses.external.society.dto.request.society.SocietyEditRequest;
import com.eroses.external.society.dto.response.EmailTemplateEditResponse;
import com.eroses.external.society.dto.response.society.SocietyEditResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.EmailTemplate;
import com.eroses.external.society.utils.HeadersUtil;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/emailtemplate")
public class EmailTemplateController {

    private final EmailTemplateWriteFacade emailTemplateWriteFacade;
    private final EmailTemplateReadFacade emailTemplateReadFacade;
    private final EmailTemplateApiInputConverter emailTemplateApiInputConverter;

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Long>> createEmailTemplate(@RequestBody EmailTemplateCreateRequest emailTemplateCreateRequest) {
        try {
            EmailTemplate emailTemplate = emailTemplateApiInputConverter.convertToModel(emailTemplateCreateRequest);
            ApiResponse<Long> emailTemplateId =  emailTemplateWriteFacade.create(emailTemplate);
            return buildResponse(emailTemplateId, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/getAll")
    public ResponseEntity<ApiResponse<List<EmailTemplate>>> getAll() {
        List<EmailTemplate> emailTemplates = emailTemplateReadFacade.getAll();
        return buildResponse(new ApiResponse<>(emailTemplates), HttpStatus.OK);
    }

    @PutMapping(value = "/{id}/edit")
    public ResponseEntity<ApiResponse<EmailTemplateEditResponse>> editEmailTemplate(@PathVariable Long id, @RequestBody EmailTemplateEditRequest request) throws Exception {
        ApiResponse<EmailTemplateEditResponse> response = emailTemplateWriteFacade.edit(id, request);
        return buildResponse(response, HttpStatus.OK);
    }

    private <T> ResponseEntity<ApiResponse<T>> buildResponse(ApiResponse<T> response, HttpStatus status) {
        HttpHeaders headers = HeadersUtil.createHeaders();
        return new ResponseEntity<>(response, headers, status);
    }
}
