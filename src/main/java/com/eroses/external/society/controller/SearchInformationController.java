package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.SearchInformationReadFacade;
import com.eroses.external.society.api.facade.SearchInformationWriteFacade;
import com.eroses.external.society.dto.request.searchInformation.SearchInformationCreateRequest;
import com.eroses.external.society.dto.request.searchInformation.SearchInformationUpdateRequest;
import com.eroses.external.society.dto.response.searchInformation.*;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.security.annotation.RequirePortalAccess;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@Slf4j
@RestController
@RequiredArgsConstructor
@RequirePortalAccess(portalHeader = "portal")
@RequestMapping("/society/searchInformation")
public class SearchInformationController {
    private final SearchInformationWriteFacade searchInformationWriteFacade;
    private final SearchInformationReadFacade searchInformationReadFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<SearchInformationCreateResponse>> create(@Valid @RequestBody SearchInformationCreateRequest request) throws Exception {
        ApiResponse<SearchInformationCreateResponse> response = searchInformationWriteFacade.create(request);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @GetMapping(value = "/getAll")
    public ResponseEntity<ApiResponse<Paging<SearchInformationGetAllResponse>>> getAll(
            @RequestParam(value = "searchQuery", required = false, defaultValue = "") String searchQuery,
            @RequestParam(value = "paymentYear", required = false, defaultValue = "0") Integer paymentYear,
            @RequestParam(value = "applicationStatusCode", required = false, defaultValue = "0") Integer applicationStatusCode,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        ApiResponse<Paging<SearchInformationGetAllResponse>> response = searchInformationReadFacade.getAll(searchQuery, paymentYear, applicationStatusCode, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @GetMapping(value = "/findById")
    public ResponseEntity<ApiResponse<SearchInformationGetResponse>> findById(@RequestParam Long id) {
        ApiResponse<SearchInformationGetResponse> response = searchInformationReadFacade.findById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @GetMapping(value = "/getSocietyByName")
    public ResponseEntity<ApiResponse<Paging<SearchInformationGetSocietyByNameResponse>>> getSocietyByName(
            @RequestParam(value = "searchQuery", required = false, defaultValue = "") String searchQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        ApiResponse<Paging<SearchInformationGetSocietyByNameResponse>> response = searchInformationReadFacade.getSocietyByName(searchQuery, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @PutMapping(value = "/update/{id}")
    public ResponseEntity<ApiResponse<SearchInformationUpdateResponse>> update(
            @PathVariable Long id,
            @Valid @RequestBody SearchInformationUpdateRequest request) throws Exception {
        ApiResponse<SearchInformationUpdateResponse> response = searchInformationWriteFacade.update(id, request);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @DeleteMapping(value = "/delete")
    public ResponseEntity<ApiResponse<Long>> delete(@RequestParam Long searchInformationId) throws Exception {
        ApiResponse<Long> response = searchInformationWriteFacade.delete(searchInformationId);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }
}
