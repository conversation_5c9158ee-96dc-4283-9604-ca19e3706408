package com.eroses.external.society.controller;

import com.eroses.external.society.api.converter.input.DocumentApiInputConverter;
import com.eroses.external.society.api.facade.DocumentReadFacade;
import com.eroses.external.society.api.facade.DocumentWriteFacade;
import com.eroses.external.society.api.facade.SocietyReadFacade;
import com.eroses.external.society.dto.request.DocumentSubmitRequest;
import com.eroses.external.society.dto.request.DocumentWriteRequest;
import com.eroses.external.society.dto.request.S3EventNotificationRequest;
import com.eroses.external.society.dto.request.document.ExportPdfRequest;
import com.eroses.external.society.dto.response.document.ExportPdfResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Document;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.model.enums.DocumentTypeEnum;
import com.eroses.external.society.utils.ResponseUtil;
import com.eroses.user.api.facade.UserFacade;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/document")
public class DocumentController {
    private final DocumentReadFacade documentReadFacade;
    private final DocumentWriteFacade documentWriteFacade;
    private final DocumentApiInputConverter documentApiInputConverter;
    private final UserFacade userFacade;
    private final SocietyReadFacade societyReadFacade;

    @GetMapping("/allDocument")
    public ResponseEntity<ApiResponse<List<Document>>> getAllDocument() {
        ApiResponse<List<Document>> response = documentReadFacade.getAllDocuments();

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/documentById/{id}")
    public ResponseEntity<ApiResponse<Document>> getById(@PathVariable Long id) {
        ApiResponse<Document> response = documentReadFacade.getDocumentById(id);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/documentByParam")
    public ResponseEntity<ApiResponse<List<Document>>> getDocumentsByParam(
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "icNo", required = false) String icNo,
            @RequestParam(value = "meetingId", required = false) Long meetingId,
            @RequestParam(value = "societyCommitteeId", required = false) Long societyCommitteeId,
            @RequestParam(value = "branchCommitteeId", required = false) Long branchCommitteeId,
            @RequestParam(value = "appealId", required = false) Long appealId,
            @RequestParam(value = "type", required = false) Long type,
            @RequestParam(value = "note", required = false) String note,
            @RequestParam(value = "statementId", required = false) Long statementId,
            @RequestParam(value = "amendmentId", required = false) Long amendmentId,
            @RequestParam(value = "liquidationId", required = false) Long liquidationId,
            @RequestParam(value = "societyNonCitizenCommitteeId", required = false) Long societyNonCitizenCommitteeId,
            @RequestParam(value = "trainingId", required = false) Long trainingId,
            @RequestParam(value = "trainingMaterialId", required = false) Long trainingMaterialId,
            @RequestParam(value = "feedbackId", required = false) Long feedbackId,
            @RequestParam(value = "ajkAppointmentLetterDate", required = false) LocalDate ajkAppointmentLetterDate,
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "code", required = false) String code) {

        Map<String, Object> param = new HashMap<>();
        if (branchId != null) param.put("branchId", branchId);
        if (societyId != null) param.put("societyId", societyId);
        if (icNo != null && !icNo.isEmpty()) param.put("icNo", icNo);
        if (meetingId != null) param.put("meetingId", meetingId);
        if (societyCommitteeId != null) param.put("societyCommitteeId", societyCommitteeId);
        if (branchCommitteeId != null) param.put("branchCommitteeId", branchCommitteeId);
        if (appealId != null) param.put("appealId", appealId);
        if (type != null && type != 0) {
            String newType = DocumentTypeEnum.getDocumentType(Math.toIntExact(type));
            param.put("type", newType);
        }
        if (note != null && !note.isEmpty()) param.put("note", note);
        if (statementId != null) param.put("statementId", statementId);
        if (amendmentId != null) param.put("amendmentId", amendmentId);
        if (liquidationId != null) param.put("liquidationId", liquidationId);
        if (societyNonCitizenCommitteeId != null) param.put("societyNonCitizenCommitteeId", societyNonCitizenCommitteeId);
        if (trainingId != null) param.put("trainingId", trainingId);
        if (trainingMaterialId != null) param.put("trainingMaterialId", trainingMaterialId);
        if (feedbackId != null) param.put("feedbackId", feedbackId);
        if (ajkAppointmentLetterDate != null) param.put("ajkAppointmentLetterDate", ajkAppointmentLetterDate);
        if (searchQuery != null && !searchQuery.isEmpty()) param.put("searchQuery", searchQuery);
        if (code != null && !code.isEmpty()) param.put("code", code);

        if (param.isEmpty()) {
            throw new IllegalArgumentException("At least one parameter must be provided.");
        }

        ApiResponse<List<Document>> response = documentReadFacade.getDocumentsByParam(param);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/documentByParamV2")
    public ResponseEntity<ApiResponse<Paging<Document>>> getDocumentsByParamWithPagination(
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "icNo", required = false) String icNo,
            @RequestParam(value = "meetingId", required = false) Long meetingId,
            @RequestParam(value = "societyCommitteeId", required = false) Long societyCommitteeId,
            @RequestParam(value = "appealId", required = false) Long appealId,
            @RequestParam(value = "type", required = false) Long type,
            @RequestParam(value = "note", required = false) String note,
            @RequestParam(value = "statementId", required = false) Long statementId,
            @RequestParam(value = "amendmentId", required = false) Long amendmentId,
            @RequestParam(value = "liquidationId", required = false) Long liquidationId,
            @RequestParam(value = "societyNonCitizenCommitteeId", required = false) Long societyNonCitizenCommitteeId,
            @RequestParam(value = "trainingId", required = false) Long trainingId,
            @RequestParam(value = "trainingMaterialId", required = false) Long trainingMaterialId,
            @RequestParam(value = "feedbackId", required = false) Long feedbackId,
            @RequestParam(value = "ajkAppointmentLetterDate", required = false) LocalDate ajkAppointmentLetterDate,
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Map<String, Object> param = new HashMap<>();
        if (branchId != null) param.put("branchId", branchId);
        if (societyId != null) param.put("societyId", societyId);
        if (icNo != null && !icNo.isEmpty()) param.put("icNo", icNo);
        if (meetingId != null) param.put("meetingId", meetingId);
        if (societyCommitteeId != null) param.put("societyCommitteeId", societyCommitteeId);
        if (appealId != null) param.put("appealId", appealId);
        if (type != null && type != 0) {
            String newType = DocumentTypeEnum.getDocumentType(Math.toIntExact(type));
            param.put("type", newType);
        }
        if (note != null && !note.isEmpty()) param.put("note", note);
        if (statementId != null) param.put("statementId", statementId);
        if (amendmentId != null) param.put("amendmentId", amendmentId);
        if (liquidationId != null) param.put("liquidationId", liquidationId);
        if (societyNonCitizenCommitteeId != null) param.put("societyNonCitizenCommitteeId", societyNonCitizenCommitteeId);
        if (trainingId != null) param.put("trainingId", trainingId);
        if (trainingMaterialId != null) param.put("trainingMaterialId", trainingMaterialId);
        if (feedbackId != null) param.put("feedbackId", feedbackId);
        if (ajkAppointmentLetterDate != null) param.put("ajkAppointmentLetterDate", ajkAppointmentLetterDate);
        if (searchQuery != null && !searchQuery.isEmpty()) param.put("searchQuery", searchQuery);
        if (code != null && !code.isEmpty()) param.put("code", code);
        param.put("pageNo", pageNo);
        param.put("pageSize", pageSize);

        if (param.isEmpty()) {
            throw new IllegalArgumentException("At least one parameter must be provided.");
        }

        ApiResponse<Paging<Document>> response = documentReadFacade.getDocumentsByParamV2(param);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/viewDocument")
    public ResponseEntity<byte[]> viewDocument(@RequestParam Long documentId) {
        Map<String, Object> response = documentReadFacade.viewDocument(documentId);

        if ( response == null ) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        byte[] byteResponse = (byte[]) response.get("byte");
        HttpHeaders headers = (HttpHeaders) response.get("headers");

        return new ResponseEntity<>(byteResponse, headers, HttpStatus.OK);
    }

    @PostMapping("/uploadFile")
    public ResponseEntity<ApiResponse<String>> uploadFile(@RequestParam("file") MultipartFile file) {

        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body(new ApiResponse<>("File is empty", null));
        }

        ApiResponse<String> response = documentWriteFacade.uploadFile(file);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

//S3 Event Notification
    @PostMapping("/uploadPresignedUrl")
    public ResponseEntity<ApiResponse<Object>> uploadPresignedUrl(@RequestBody DocumentWriteRequest documentWriteRequest) {

        Document document = documentApiInputConverter.convertToModel(documentWriteRequest);
        ApiResponse<Object> response = documentReadFacade.uploadPresignedUrl(document);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping("/submitDocument")
    public ResponseEntity<ApiResponse<Object>> updateDocument(@RequestBody DocumentSubmitRequest request) {

        ApiResponse<Object> response = documentWriteFacade.submitDocument(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping("/registerFileS3Trigger")
    public ResponseEntity<ApiResponse<Long>> registerFileS3Trigger(@RequestBody S3EventNotificationRequest eventRequest) {
        ApiResponse<Long> response = documentWriteFacade.registerFileS3Trigger(eventRequest);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
//End

    @PutMapping("/registerFileInDb")
    public ResponseEntity<ApiResponse<Object>> registerFileInDb(@RequestBody DocumentWriteRequest documentWriteRequest) {
        Document document = documentApiInputConverter.convertToModel(documentWriteRequest);

        ApiResponse<Object> response = documentWriteFacade.registerFileInDb(document);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping("/updateDocument")
    public ResponseEntity<ApiResponse<Object>> updateDocument(@RequestBody DocumentWriteRequest documentWriteRequest) {

        Document document = documentApiInputConverter.convertToModel(documentWriteRequest);
        ApiResponse<Object> response = documentWriteFacade.updateDocument(document);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping("/deleteDocument")
    public ResponseEntity<ApiResponse<String>> deleteDocument(@RequestParam("id") Long id) {

        ApiResponse<String> response = documentWriteFacade.deleteDocument(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping("/exportPdf")
    public ResponseEntity<ApiResponse<ExportPdfResponse>> exportPdf(@Valid @RequestBody ExportPdfRequest request) throws Exception {
        ApiResponse<ExportPdfResponse> response = documentReadFacade.exportPdf(request);

        if (response.getCode() == HttpStatus.OK.value() ) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDisposition(ContentDisposition.builder("attachment")
                    .filename(response.getData().getFileName() + ".pdf").build());
        }

        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @GetMapping("/getDocumentTemplateUrl")
    public ResponseEntity<ApiResponse<String>> documentTemplate(@RequestParam("documentTemplateType") String documentTemplateType) throws Exception {
        ApiResponse<String> response = documentReadFacade.getDocumentTemplateUrl(documentTemplateType);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
