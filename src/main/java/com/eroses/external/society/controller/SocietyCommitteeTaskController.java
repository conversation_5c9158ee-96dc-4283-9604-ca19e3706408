package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.CommitteeTaskReadFacade;
import com.eroses.external.society.api.facade.CommitteeTaskWriteFacade;
import com.eroses.external.society.dto.request.committee.CommitteeTaskGetRequest;
import com.eroses.external.society.dto.request.committee.SocietyCommitteeTaskCreateRequest;
import com.eroses.external.society.dto.response.committee.CommitteeTaskGetResponse;
import com.eroses.external.society.dto.response.committee.CommitteeTaskModuleResponse;
import com.eroses.external.society.dto.response.committee.CommitteeTaskUpdateResponse;
import com.eroses.external.society.dto.response.statement.CommitteeTaskCreateResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.enums.StatusCode;
import com.eroses.external.society.security.annotation.RequireSocietyMembership;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/committee-task")
public class SocietyCommitteeTaskController {
    private final CommitteeTaskWriteFacade committeeTaskWriteFacade;
    private final CommitteeTaskReadFacade committeeTaskReadFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<CommitteeTaskCreateResponse>> createCommitteeTask(
            @RequestParam Long societyId,
            @RequestParam(required = false) Long branchId,
            @Valid @RequestBody SocietyCommitteeTaskCreateRequest request) throws Exception {
        return committeeTaskWriteFacade.create(societyId, branchId, request);
    }

    @GetMapping(value = "/list")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<List<CommitteeTaskGetResponse>>> listCommitteeTask(
            @RequestParam Long societyId,
            @RequestParam String module,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "branchId", required = false) Long branchId) {
        CommitteeTaskGetRequest committeeTaskGetRequest = new CommitteeTaskGetRequest(societyId, branchId,  status,module);
        return committeeTaskReadFacade.listCommitteeTask(committeeTaskGetRequest);
    }

    @GetMapping("/me")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<CommitteeTaskModuleResponse>> currentUserModuleCommitteeTask(@RequestParam Long societyId, @RequestParam(required = false, value = "branchId") Long branchId, @RequestParam("module") String module) {
        CommitteeTaskGetRequest committeeTaskGetRequest = new CommitteeTaskGetRequest(societyId, branchId, StatusCode.AKTIF.getCode(), module);

        return committeeTaskReadFacade.getCurrentUserModuleAccess(committeeTaskGetRequest);
    }

    @PutMapping("/{id}/activate")
    public ResponseEntity<ApiResponse<CommitteeTaskUpdateResponse>> activateCommitteeTask(@PathVariable("id") Long id) {
        return committeeTaskWriteFacade.activate(id);
    }

    @PutMapping("/{id}/deactivate")
    public ResponseEntity<ApiResponse<CommitteeTaskUpdateResponse>> deactivateCommitteeTask(@PathVariable("id") Long id) {
        return committeeTaskWriteFacade.deactivate(id);
    }


}