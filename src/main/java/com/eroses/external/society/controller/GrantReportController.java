package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.grant.GrantReportReadFacade;
import com.eroses.external.society.api.facade.grant.GrantReportWriteFacade;
import com.eroses.external.society.dto.request.grant.GrantReportCreateRequest;
import com.eroses.external.society.dto.request.grant.GrantReportSubmitRequest;
import com.eroses.external.society.dto.request.grant.GrantReportUpdateRequest;
import com.eroses.external.society.dto.response.grant.GrantReportDetailResponse;
import com.eroses.external.society.dto.response.grant.GrantReportResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.model.enums.GrantReportAttachmentTypeEnum;
import com.eroses.external.society.utils.ResponseUtil;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/grant/report")
public class GrantReportController {
    
    private final GrantReportReadFacade grantReportReadFacade;
    private final GrantReportWriteFacade grantReportWriteFacade;
    
    @GetMapping
    public ResponseEntity<ApiResponse<Paging<GrantReportResponse>>> findAll(
            @RequestParam(required = false) Long grantApplicationId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Boolean allowPublicDisplay,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        ApiResponse<Paging<GrantReportResponse>> response = grantReportReadFacade.findAll(
                grantApplicationId, status, allowPublicDisplay, page, size);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<GrantReportDetailResponse>> findById(@PathVariable Long id) {
        ApiResponse<GrantReportDetailResponse> response = grantReportReadFacade.findById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @GetMapping("/application/{grantApplicationId}")
    public ResponseEntity<ApiResponse<GrantReportDetailResponse>> findByGrantApplicationId(@PathVariable Long grantApplicationId) {
        ApiResponse<GrantReportDetailResponse> response = grantReportReadFacade.findByGrantApplicationId(grantApplicationId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @GetMapping("/public")
    public ResponseEntity<ApiResponse<List<GrantReportResponse>>> findPublicReports() {
        ApiResponse<List<GrantReportResponse>> response = grantReportReadFacade.findPublicReports();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Long>> createGrantReport(
            @Valid @RequestBody GrantReportCreateRequest request,
            @AuthenticationPrincipal User user) {
        
        ApiResponse<Long> response = grantReportWriteFacade.createGrantReport(request, user.getId());
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }
    
    @PutMapping("/update")
    public ResponseEntity<ApiResponse<Boolean>> updateGrantReport(
            @Valid @RequestBody GrantReportUpdateRequest request,
            @AuthenticationPrincipal User user) {
        
        ApiResponse<Boolean> response = grantReportWriteFacade.updateGrantReport(request, user.getId());
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @PutMapping("/submit")
    public ResponseEntity<ApiResponse<Boolean>> submitGrantReport(
            @Valid @RequestBody GrantReportSubmitRequest request,
            @AuthenticationPrincipal User user) {
        
        ApiResponse<Boolean> response = grantReportWriteFacade.submitGrantReport(request, user.getId());
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @PutMapping("/{id}/approve")
    public ResponseEntity<ApiResponse<Boolean>> approveGrantReport(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        
        ApiResponse<Boolean> response = grantReportWriteFacade.approveGrantReport(id, user.getId());
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @PutMapping("/{id}/reject")
    public ResponseEntity<ApiResponse<Boolean>> rejectGrantReport(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        
        ApiResponse<Boolean> response = grantReportWriteFacade.rejectGrantReport(id, user.getId());
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @PostMapping(value = "/{id}/attachment", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<Long>> addAttachment(
            @PathVariable Long id,
            @RequestParam("file") MultipartFile file,
            @RequestParam("fileType") String fileType,
            @AuthenticationPrincipal User user) {
        
        ApiResponse<Long> response = grantReportWriteFacade.addAttachment(id, file, fileType, user.getId());
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }
    
    @DeleteMapping("/attachment/{attachmentId}")
    public ResponseEntity<ApiResponse<Boolean>> deleteAttachment(@PathVariable Long attachmentId) {
        ApiResponse<Boolean> response = grantReportWriteFacade.deleteAttachment(attachmentId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Boolean>> deleteGrantReport(@PathVariable Long id) {
        ApiResponse<Boolean> response = grantReportWriteFacade.deleteGrantReport(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
