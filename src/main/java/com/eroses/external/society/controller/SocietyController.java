package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.*;
import com.eroses.external.society.api.facade.admin.AdmOrganizationWriteFacade;
import com.eroses.external.society.dto.request.PagingBranchCountRequest;
import com.eroses.external.society.dto.request.SocietyListMemberRequest;
import com.eroses.external.society.dto.request.committee.CommitteeCreateRequest;
import com.eroses.external.society.dto.request.committee.CommitteeEditRequest;
import com.eroses.external.society.dto.request.organization.OrganizationGetAllPendingRequest;
import com.eroses.external.society.dto.request.organization.OrganizationUpdateApprovalStatusRequest;
import com.eroses.external.society.dto.request.organization.OrganizationUpdateRoRequest;
import com.eroses.external.society.dto.request.society.*;
import com.eroses.external.society.dto.response.branch.BranchBasicInfoResponse;
import com.eroses.external.society.dto.response.branchCommittee.BranchSecretaryResponse;
import com.eroses.external.society.dto.response.committee.CommitteeCreateResponse;
import com.eroses.external.society.dto.response.committee.CommitteeEditResponse;
import com.eroses.external.society.dto.response.committee.CommitteeTaskModuleResponse;
import com.eroses.external.society.dto.response.meeting.MeetingBasicListResponse;
import com.eroses.external.society.dto.response.organization.OrganizationGetOnePendingResponse;
import com.eroses.external.society.dto.response.organization.OrganizationRoUpdateResponse;
import com.eroses.external.society.dto.response.organization.OrganizationUpdateApprovalStatusResponse;
import com.eroses.external.society.dto.response.society.*;
import com.eroses.external.society.mappers.SocietyDao;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Committee;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.model.Society;
import com.eroses.external.society.model.enums.*;
import com.eroses.external.society.security.annotation.RequireCurrentUserNotBlacklisted;
import com.eroses.external.society.security.annotation.RequireSocietyMembership;
import com.eroses.external.society.security.annotation.RequireTargetUserNotBlacklisted;
import com.eroses.external.society.service.CommitteeReadDomainService;
import com.eroses.external.society.service.CommitteeWriteDomainService;
import com.eroses.external.society.service.blacklist.BlacklistUserReadDomainService;
import com.eroses.external.society.utils.PaginationUtil;
import com.eroses.external.society.utils.ResponseUtil;
import com.eroses.service.notification.service.NotificationProcessorService;
import com.eroses.user.api.facade.UserFacade;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society")
public class SocietyController {
    private final SocietyWriteFacade societyWriteFacade;
    private final SocietyDao societyDao;
    private final SocietyReadFacade societyReadFacade;
    private final AdmOrganizationWriteFacade admOrganizationWriteFacade;
    private final MeetingReadFacade meetingReadFacade;
    private final ConstitutionContentReadFacade constitutionContentReadFacade;
    private final CommitteeReadFacade committeeReadFacade;
    private final UserFacade authFacade;
    private final CommitteeWriteFacade committeeWriteFacade;
    private final CommitteeReadDomainService committeeReadDomainService;
    private final CommitteeWriteDomainService committeeWriteDomainService;
    private final BranchReadFacade branchReadFacade;
    private final CommitteeTaskReadFacade committeeTaskReadFacade;
    private final BlacklistUserReadDomainService blacklistUserReadDomainService;
    private final NotificationProcessorService notificationProcessorService;

    @RequireCurrentUserNotBlacklisted
    @PostMapping(value = "/register")
    public ResponseEntity<ApiResponse<SocietyRegisterResponse>> registerSociety(@Valid @RequestBody SocietyRegisterRequest request) throws Exception {
        ApiResponse<SocietyRegisterResponse> response = societyWriteFacade.register(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @PutMapping(value = "/{id}/edit")
    public ResponseEntity<ApiResponse<SocietyEditResponse>> editSociety(@PathVariable Long id, @RequestBody SocietyEditRequest request) throws Exception {
        ApiResponse<SocietyEditResponse> response = societyWriteFacade.update(id, request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAll")
    public ResponseEntity<ApiResponse<List<Society>>> getAllSocieties() {
        ApiResponse<List<Society>> response = societyReadFacade.getAll();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/search")
    public ResponseEntity<ApiResponse<Paging<SocietySearchResponse>>> searchSocieties(
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "societyNo", required = false) String societyNo,
            @RequestParam(value = "status", required = false) String statusCode) {
        SocietySearchRequest request = new SocietySearchRequest(searchQuery, societyNo, pageNo, pageSize, statusCode);

        ApiResponse<Paging<SocietySearchResponse>> response = societyReadFacade.searchSocieties(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/searchByNameByPublic")
    public ResponseEntity<ApiResponse<Paging<SocietySearchPublic>>> searchSocietiesByName(
            @RequestParam(value = "societyName", required = true) String societyName,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        ApiResponse<Paging<SocietySearchPublic>> response = societyReadFacade.searchSocietiesByName(societyName, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/paging-branch-count")
    public ResponseEntity<ApiResponse<Paging<SocietyBranchListResponse>>> getCurrentUserSocietyBranchList(
            @RequestParam(value = "name", defaultValue = "", required = false) String name,
            @RequestParam(value = "pageNo", defaultValue = "1", required = false) Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10", required = false) Integer pageSize) {
        ApiResponse<Paging<SocietyBranchListResponse>> response = societyReadFacade.getCurrentUserSocietyBranchList(name, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @GetMapping(value = "/{id}")
    @RequireSocietyMembership(societyIdParam = "id")
    public ResponseEntity<ApiResponse<SocietyGetOneResponse>> findSocietyById(@PathVariable Long id) {
        ApiResponse<SocietyGetOneResponse> response = societyReadFacade.findById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/{societyId}/branches/{branchId}/meetings")
    public ResponseEntity<ApiResponse<List<MeetingBasicListResponse>>> findMeetings(
        @PathVariable Long societyId,
        @PathVariable Long branchId,
        @RequestParam(required = false) Integer meetingType,
        @RequestParam(required = false) LocalDate meetingDate
    ) {
        ApiResponse<List<MeetingBasicListResponse>> response = meetingReadFacade.findBySocietyIdAndBranchId(
            societyId,
            branchId,
            meetingType,
            meetingDate
        );
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/{societyId}/branches/secretaries")
    public ResponseEntity<ApiResponse<Paging<BranchSecretaryResponse>>> getBranchSecretaries(
            @PathVariable Long societyId,
            @RequestParam(required = false) String secretaryName,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize
    ) {
        Map<String, Object> params = new HashMap<>();
        params.put("societyId", societyId);
        params.put("secretaryName", secretaryName);
        params.put("offset", PaginationUtil.getOffset(pageNo, pageSize));
        params.put("limit", pageSize);

        ApiResponse<Paging<BranchSecretaryResponse>> response = branchReadFacade.getBranchSecretaries(params);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/{societyId}/branch/basic")
    public ResponseEntity<ApiResponse<List<BranchBasicInfoResponse>>> getBasicBranches(
            @PathVariable Long societyId
    ){
        ApiResponse<List<BranchBasicInfoResponse>> response = branchReadFacade.getBasicInfo(societyId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }


    @GetMapping(value = "/{id}/basic")
    public ResponseEntity<ApiResponse<SocietyBasicResponse>> findBasicById(@PathVariable Long id) {
        ApiResponse<SocietyBasicResponse> response = new ApiResponse<>(societyDao.findBasicById(id));
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getUserSociety")
    public ResponseEntity<ApiResponse<Paging<SocietyGetOneResponse>>> getUserSociety(
            @RequestParam(value = "searchQuery", required = false, defaultValue = "") String searchQuery,
            @RequestParam(value = "applicationStatusCode", required = false, defaultValue = "0") Integer applicationStatusCode,
            @RequestParam(value = "statusCode", required = false, defaultValue = "") String statusCode,
            @RequestParam(value = "registeredYear", required = false, defaultValue = "0") Integer registeredYear,
            @RequestParam(value = "designationCode", required = false, defaultValue = "") String designationCode,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        ApiResponse<Paging<SocietyGetOneResponse>> response = societyReadFacade.findSocietiesByUser(searchQuery, applicationStatusCode, statusCode, registeredYear, designationCode, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @DeleteMapping(value = "/{societyId}")
    public ApiResponse<Boolean> delete(@PathVariable Long societyId) {
        return societyWriteFacade.delete(societyId);
    }

    @GetMapping(value = "/getAllPending")
    public ResponseEntity<ApiResponse<Paging<OrganizationGetOnePendingResponse>>> getAllPending(@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                                                                                @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        OrganizationGetAllPendingRequest request = new OrganizationGetAllPendingRequest();
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        try {
            ApiResponse<Paging<OrganizationGetOnePendingResponse>> response = societyReadFacade.getAllPending(request);
            return ResponseUtil.buildResponse(response, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping(value = "{societyId}/getPendingBySocietyId")
    public ResponseEntity<ApiResponse<OrganizationGetOnePendingResponse>> getPendingBySocietyId(@PathVariable Long societyId) {
        try {
            ApiResponse<OrganizationGetOnePendingResponse> response = societyReadFacade.getPendingBySocietyId(societyId);
            return ResponseUtil.buildResponse(response, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @PatchMapping(value = "{societyId}/admin/updateRo")
    public ResponseEntity<ApiResponse<OrganizationRoUpdateResponse>> updateRo(@PathVariable Long societyId, @RequestBody OrganizationUpdateRoRequest request) {
        try {
            ApiResponse<OrganizationRoUpdateResponse> response = admOrganizationWriteFacade.updateRo(societyId, request);
            return ResponseUtil.buildResponse(response, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @PatchMapping(value = "{societyId}/admin/updateApprovalStatus")
    public ResponseEntity<ApiResponse<OrganizationUpdateApprovalStatusResponse>> updateApprovalStatus(@PathVariable Long societyId, @RequestBody OrganizationUpdateApprovalStatusRequest request) {
        try {
            ApiResponse<OrganizationUpdateApprovalStatusResponse> response = admOrganizationWriteFacade.updateApprovalStatus(societyId, request);
            return ResponseUtil.buildResponse(response, HttpStatus.OK);
        } catch (RuntimeException e) {
            throw new RuntimeException(e);
        }
    }

    @PutMapping(value = "/{id}/checkAndUpdate")
    public ResponseEntity<ApiResponse<SocietyEditResponse>> checkAndUpdateSociety(@PathVariable Long id, @RequestBody SocietyEditRequest request) {
        ApiResponse<SocietyEditResponse> response = societyWriteFacade.checkAndUpdateSociety(id, request);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @PutMapping(value = "/{id}/checkAndUpdateRegistration")
    public ResponseEntity<ApiResponse<Boolean>> checkAndUpdateRegistration(@PathVariable String id, @RequestParam Long page) {
        ApiResponse<Boolean> response = societyWriteFacade.checkAndUpdateRegistration(id, page);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/{id}/validateSocietyCommittee")
    public ResponseEntity<ApiResponse<Boolean>> validateSocietyCommittee(@PathVariable Long id) {
        ApiResponse<Boolean> response = societyWriteFacade.validateSocietyCommittee(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @RequireCurrentUserNotBlacklisted
    @PostMapping(value = "/{societyId}/join")
    public ResponseEntity<ApiResponse<SocietyJoinResponse>> joinSociety(@PathVariable Long societyId,
                                                                        @RequestParam(value = "joinOrCancel") String joinOrCancel) {
        try {
            if (joinOrCancel.equals("Join")) {
                String identificationNo = authFacade.me().getIdentificationNo();
                SocietyGetOneResponse societyResponse = societyReadFacade.findById(societyId).getData();

                // Check if user join request exists in the society
                boolean requestExists = committeeReadDomainService.existsByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus(identificationNo, societyId, ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode(), StatusCode.TIDAK_AKTIF.getCode());
                if (requestExists) {
                    ApiResponse<SocietyJoinResponse> response = new ApiResponse<>("Error", 409, "User join request already exist.", null, LocalDateTime.now());
                    return ResponseUtil.buildResponse(response, HttpStatus.CONFLICT);
                }

                // Check if the user already exists in the society
                boolean userExist = committeeReadDomainService.existsByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus(identificationNo, societyId, ApplicationStatusCode.AKTIF.getCode(), StatusCode.AKTIF.getCode());
                if (userExist) {
                    ApiResponse<SocietyJoinResponse> response = new ApiResponse<>("Error", 409, "User is already in this society.", null, LocalDateTime.now());
                    return ResponseUtil.buildResponse(response, HttpStatus.CONFLICT);
                }

                CommitteeCreateRequest request = new CommitteeCreateRequest();

                // Set status to aktif, can only join normal member, which don't need to approve
                request.setStatus(StatusCode.TIDAK_AKTIF.getCode());
                request.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode()));

                // Set society info
                request.setSocietyId(societyId);
                request.setSocietyNo(societyResponse.getSocietyNo());

                // Set role
                request.setDesignationCode(String.valueOf(Position.AHLI_BIASA.getCode()));

                // Populate fields from userAuth.me()
                request.setName(authFacade.me().getName());
                request.setNationalityStatus(authFacade.me().getCitizenshipTitle());
                request.setIdentificationType(String.valueOf(authFacade.me().getIdentificationType()));
                request.setIdentificationNo(authFacade.me().getIdentificationNo());
                request.setTitleCode(authFacade.me().getTitleCode());
                request.setGender(authFacade.me().getGender());
                request.setResidentialAddress(authFacade.me().getAddress());
                request.setResidentialCity(authFacade.me().getCity());
                request.setResidentialDistrictCode(authFacade.me().getDistrictCode());
                request.setResidentialStateCode(authFacade.me().getStateCode());
                request.setResidentialPostcode(authFacade.me().getPostcode());
                request.setPhoneNumber(authFacade.me().getMobilePhone());
                request.setEmail(authFacade.me().getEmail());
                request.setTelephoneNumber(authFacade.me().getHousePhone());

                // Set fields not available in userAuth to null
                request.setJobCode(null);
                request.setDateOfBirth(null);
                request.setPlaceOfBirth(null);
                request.setOtherDesignationCode(null);
                request.setEmployerAddressStatus(null);
                request.setEmployerName(null);
                request.setEmployerAddress(null);
                request.setEmployerPostcode(null);
                request.setEmployerCountryCode(null);
                request.setEmployerStateCode(null);
                request.setEmployerCity(null);
                request.setEmployerDistrict(null);
                request.setResidentialAddressStatus(null);
                request.setResidentialCountryCode(null);
                request.setNoTelP(null);
                request.setPegHarta(null);
                request.setTarikhTukarSu(null);
                request.setOtherPosition(null);
                request.setBatalFlat(null);
                request.setBlacklistNotice(null);
                request.setBenarAjk(null);

                ApiResponse<CommitteeCreateResponse> committeeCreateResponse = committeeWriteFacade.create(request);
                // Check if committee creation was successful
                if (committeeCreateResponse.getData() == null) {
                    // Return the error from committee creation
                    return ResponseUtil.buildResponse(
                        new ApiResponse<>(
                            committeeCreateResponse.getMsg(),
                            committeeCreateResponse.getCode(),
                            committeeCreateResponse.getStatus(),
                            null,
                            LocalDateTime.now()
                        ),
                        HttpStatus.valueOf(committeeCreateResponse.getCode())
                    );
                }

                SocietyJoinResponse societyJoinResponse = new SocietyJoinResponse(committeeCreateResponse.getData().getId());
                ApiResponse<SocietyJoinResponse> response = new ApiResponse<>("Success", 201, "User successfully joined society. Committee has been created.", societyJoinResponse, LocalDateTime.now());

                return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
            } else if (joinOrCancel.equals("Cancel")) {
                String identificationNo = authFacade.me().getIdentificationNo();

                // Check if user join request exists in the society
                boolean requestExists = committeeReadDomainService.existsByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus(identificationNo, societyId, ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode(), StatusCode.TIDAK_AKTIF.getCode());
                if (!requestExists) {
                    ApiResponse<SocietyJoinResponse> response = new ApiResponse<>("Error", 409, "User join request does not exist.", null, LocalDateTime.now());
                    return ResponseUtil.buildResponse(response, HttpStatus.CONFLICT);
                }

                // Check if the user already exists in the society
                boolean userExist = committeeReadDomainService.existsByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus(identificationNo, societyId, ApplicationStatusCode.AKTIF.getCode(), StatusCode.AKTIF.getCode());
                if (userExist) {
                    ApiResponse<SocietyJoinResponse> response = new ApiResponse<>("Error", 409, "User is already in this society.", null, LocalDateTime.now());
                    return ResponseUtil.buildResponse(response, HttpStatus.CONFLICT);
                }

                // Perform the deletion
                Long id = committeeWriteDomainService.deleteByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus(
                        identificationNo,
                        societyId,
                        ApplicationStatusCode.MENUNGGU_KEPUTUSAN.getCode(),
                        StatusCode.TIDAK_AKTIF.getCode()
                );
                SocietyJoinResponse societyJoinResponse = new SocietyJoinResponse(id);
                ApiResponse<SocietyJoinResponse> response = new ApiResponse<>("Success", 201, "User join request successfully deleted.", societyJoinResponse, LocalDateTime.now());
                return ResponseUtil.buildResponse(response, HttpStatus.OK);
            } else {
                ApiResponse<SocietyJoinResponse> response = new ApiResponse<>("Error", 500, "Invalid join or cancel value. (Join/Cancel)", null, LocalDateTime.now());
                return ResponseUtil.buildResponse(response, HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/{societyId}/checkUserSociety")
    public ResponseEntity<ApiResponse<SocietyCheckUserInSocietyResponse>> checkUserInSociety(@PathVariable Long societyId) {
        try {
            ApiResponse<SocietyCheckUserInSocietyResponse> response = societyReadFacade.checkUserInSociety(societyId);
            return ResponseUtil.buildResponse(response, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/{societyId}/listJoinRequests")
    public ResponseEntity<ApiResponse<Paging<SocietyListJoinRequestResponse>>> listJoinRequests(
            @PathVariable Long societyId,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        try {
            SocietyListJoinRequestRequest request = new SocietyListJoinRequestRequest(societyId, pageNo, pageSize);
            ApiResponse<Paging<SocietyListJoinRequestResponse>> response = societyReadFacade.listJoinRequests(request);

            return ResponseUtil.buildResponse(response, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/{societyId}/members/list")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<Paging<SocietyListMemberResponse>>> listMembers(
            @PathVariable Long societyId,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        try {
            SocietyListMemberRequest request = new SocietyListMemberRequest(societyId, pageNo, pageSize);

            ApiResponse<Paging<SocietyListMemberResponse>> response = societyReadFacade.listMembers(request);
            return ResponseUtil.buildResponse(response, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @RequireTargetUserNotBlacklisted
    @PostMapping("/{societyId}/members/register")
    public ResponseEntity<ApiResponse<SocietyRegisterMemberResponse>> registerMember(
            @PathVariable Long societyId,
            @RequestBody SocietyRegisterMemberRequest request) {
        try {
            ApiResponse<SocietyRegisterMemberResponse> response = societyWriteFacade.registerMember(societyId, request);
            return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @PutMapping("/{societyId}/members/requestApproval")
    public ResponseEntity<ApiResponse<List<String>>> approveJoinRequests(
            @PathVariable Long societyId,
            @RequestBody SocietyMemberRequestsApprovalRequest request) {
        // approve: application status code change to "aktif" and status of "aktif"
        // reject: application status code change to "tolak" and status of "inaktif"
        try {
            ApiResponse<List<String>> response = societyWriteFacade.approveSocietyJoinRequests(societyId, request);
            return ResponseUtil.buildResponse(response, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @PutMapping("/{societyId}/members/{identificationNo}/edit")
    public ResponseEntity<ApiResponse<CommitteeEditResponse>> editMember(
            @PathVariable Long societyId,
            @PathVariable String identificationNo,
            @RequestBody CommitteeEditRequest request
    ) {
        try {
            // Find committee member by identificationNo
            Committee committee = committeeReadDomainService.findBySocietyIdAndIdentificationNo(societyId, identificationNo);

            if (committee == null) {
                ApiResponse<CommitteeEditResponse> response = new ApiResponse<>("Error", 404, "Pengguna tidak wujud di dalam senarai ahli.", null, LocalDateTime.now());
                return ResponseUtil.buildResponse(response, HttpStatus.NOT_FOUND);
            }

            // Update the member
            request.setSocietyId(Math.toIntExact(societyId));
            ApiResponse<CommitteeEditResponse> response = committeeWriteFacade.update(committee.getId(), request);
            return ResponseUtil.buildResponse(response, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @PutMapping("/{id}/committee_task/activate")
    public ResponseEntity<ApiResponse<SocietyGetOneResponse>> activateSocietyCommitteeTask(@PathVariable("id") Long id, @RequestParam("module") String module) throws Exception {
        return ResponseUtil.buildResponse(societyWriteFacade.activateCommitteeTask(id,module),HttpStatus.OK);
    }

    @PutMapping("/{id}/committee_task/deactivate")
    public ResponseEntity<ApiResponse<SocietyGetOneResponse>> deactivateSocietyCommitteeTask(@PathVariable("id") Long id, @RequestParam("module") String module) throws Exception {
        return ResponseUtil.buildResponse(societyWriteFacade.deactivateCommitteeTask(id,module),HttpStatus.OK);
    }
    @GetMapping("/{id}/committee_task/status")
    @RequireSocietyMembership(societyIdParam = "id")
    public ResponseEntity<ApiResponse<CommitteeTaskModuleResponse>> societyCommitteeTaskStatus(@PathVariable("id") Long id, @RequestParam("module") String module) throws Exception {
        return committeeTaskReadFacade.societyCommitteeTaskStatus(id,module);
    }
    @PutMapping("/{societyId}/members/{identificationNo}/delete")
    public ResponseEntity<ApiResponse<CommitteeEditResponse>> deleteMember(
            @PathVariable Long societyId,
            @PathVariable String identificationNo
    ) {
        try {
            // Find committee member by identificationNo
            Committee committee = committeeReadDomainService.findBySocietyIdAndIdentificationNoAndApplicationStatusCodeAndStatus(societyId, identificationNo, ApplicationStatusCode.AKTIF.getCode(), StatusCode.AKTIF.getCode());

            if (committee == null) {
                ApiResponse<CommitteeEditResponse> response = new ApiResponse<>("Error", 404, "Pengguna tidak wujud di dalam senarai ahli.", null, LocalDateTime.now());
                return ResponseUtil.buildResponse(response, HttpStatus.NOT_FOUND);
            }

            // Delete the member
            committee.setApplicationStatusCode(String.valueOf(ApplicationStatusCode.PADAM.getCode()));
            committee.setStatus(StatusCode.PADAM.getCode());
            committeeWriteDomainService.update(committee);

            //Send notification on member removed
            notificationProcessorService.sendNotificationOnRemoveSocietyMember(societyId, committee);

            CommitteeEditResponse response = new CommitteeEditResponse(committee.getId());
            ApiResponse<CommitteeEditResponse> apiResponse = ApiResponse.ok(SuccessMessage.DATA_DELETED.getBmMessage(), response);
            return ResponseUtil.buildResponse(apiResponse, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/searchJoinSociety")
    public ResponseEntity<ApiResponse<List<SocietySearchJoinSocietyResponse>>> searchJoinSociety(
            @RequestParam(value = "searchQuery", required = false, defaultValue = "") String searchQuery) throws Exception {
        SocietySearchJoinSocietyRequest request = new SocietySearchJoinSocietyRequest(searchQuery);
        return societyReadFacade.searchJoinSociety(request);
    }

    @GetMapping("/isManageAuthorized")
    public ResponseEntity<ApiResponse<Boolean>> isManageAuthorized(
            @RequestParam(value = "societyId") Long societyId,
            @RequestParam(value = "branchId", required = false, defaultValue = "0") Long branchId) throws Exception {
        ApiResponse<Boolean> response = societyReadFacade.isManageAuthorized(societyId, branchId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    //<editor-fold> internal
    @PreAuthorize("hasRole('SERVICE')")
    @PostMapping("/internal/saveSociety")

    public ResponseEntity<ApiResponse<Boolean>> saveSociety(@RequestBody Society society) throws Exception {
        return ResponseEntity.ok(societyWriteFacade.saveSociety(society));
    }
    //</editor-fold>

    //checkSocietyName
    @GetMapping("/checkSocietyNameExists")
    public ResponseEntity<ApiResponse<Boolean>> checkSocietyName(@RequestParam String societyName) {
        ApiResponse<Boolean> response = societyReadFacade.checkSocietyNameExists(societyName);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
