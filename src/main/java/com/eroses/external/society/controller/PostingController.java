package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.posting.PostingReadFacade;
import com.eroses.external.society.api.facade.posting.PostingWriteFacade;
import com.eroses.external.society.dto.request.posting.PostingReportRequest;
import com.eroses.external.society.dto.response.posting.PostingPagingResponse;
import com.eroses.external.society.dto.response.posting.PostingResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.ResponseUtil;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/posting")
public class PostingController {

    private final PostingReadFacade postingReadFacade;
    private final PostingWriteFacade postingWriteFacade;

    /**
     * Get all postings with pagination and filtering
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Paging<PostingPagingResponse>>> findAll(
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String subCategory,
            @RequestParam(required = false) String dateFrom,
            @RequestParam(required = false) String dateTo,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {

        ApiResponse<Paging<PostingPagingResponse>> response = postingReadFacade.findAll(
                title, category, subCategory, dateFrom, dateTo, page, size);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    /**
     * Get a posting by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<PostingResponse>> findById(@PathVariable Long id) {
        ApiResponse<PostingResponse> response = postingReadFacade.findById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    /**
     * Get random postings for the landing page
     */
    @GetMapping("/random")
    public ResponseEntity<ApiResponse<List<PostingResponse>>> getRandomPostings(
            @RequestParam(defaultValue = "4") Integer count) {
        ApiResponse<List<PostingResponse>> response = postingReadFacade.getRandomPostings(count);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    /**
     * Get recommended postings based on a specific posting
     */
    @GetMapping("/recommended/{postingId}")
    public ResponseEntity<ApiResponse<List<PostingResponse>>> getRecommendedPostings(
            @PathVariable Long postingId,
            @RequestParam(defaultValue = "5") Integer count) {
        ApiResponse<List<PostingResponse>> response = postingReadFacade.getRecommendedPostings(postingId, count);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    /**
     * Report a posting for inappropriate content
     */
    @PostMapping("/report")
    public ResponseEntity<ApiResponse<Long>> reportPosting(
            @Valid @RequestBody PostingReportRequest request
         ) {
        ApiResponse<Long> response = postingWriteFacade.reportPosting(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    /**
     * Record engagement with a posting (view, like, share)
     */
    @PostMapping("/engagement/{postingId}/{type}")
    public ResponseEntity<ApiResponse<Boolean>> recordEngagement(
            @PathVariable Long postingId,
            @PathVariable String type,
            @RequestParam(required = false) String state,
            @RequestParam(required = false) String ipAddress) {
        ApiResponse<Boolean> response = postingWriteFacade.recordEngagement(postingId, type, state, ipAddress);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
