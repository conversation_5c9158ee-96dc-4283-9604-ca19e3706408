package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.EmailFacade;
import com.eroses.external.society.api.facade.LookupReadFacade;
import com.eroses.external.society.api.facade.LookupWriteFacade;
import com.eroses.external.society.dto.request.lookup.addresses.AdmAddressesCreateRequest;
import com.eroses.external.society.dto.request.lookup.addresses.AdmAddressesEditRequest;
import com.eroses.external.society.dto.request.lookup.emailQueue.ResendEmailRequest;
import com.eroses.external.society.dto.request.lookup.occupation.AdmOccupationCreateRequest;
import com.eroses.external.society.dto.request.lookup.occupation.AdmOccupationEditRequest;
import com.eroses.external.society.dto.request.lookup.race.AdmRaceCreateRequest;
import com.eroses.external.society.dto.request.lookup.race.AdmRaceEditRequest;
import com.eroses.external.society.dto.request.lookup.religion.AdmReligionCreateRequest;
import com.eroses.external.society.dto.request.lookup.religion.AdmReligionEditRequest;
import com.eroses.external.society.dto.request.lookup.branch.AdmBranchCreateRequest;
import com.eroses.external.society.dto.request.lookup.branch.AdmBranchEditRequest;
import com.eroses.external.society.dto.response.emailQueue.ResendEmailResponse;
import com.eroses.external.society.dto.response.lookup.addresses.AdmAddressesEditResponse;
import com.eroses.external.society.dto.response.lookup.addresses.AdmAddressesGetResponse;
import com.eroses.external.society.dto.response.lookup.occupation.AdmOccupationEditResponse;
import com.eroses.external.society.dto.response.lookup.occupation.AdmOccupationGetResponse;
import com.eroses.external.society.dto.response.lookup.race.AdmRaceEditResponse;
import com.eroses.external.society.dto.response.lookup.race.AdmRaceGetResponse;
import com.eroses.external.society.dto.response.lookup.religion.AdmReligionEditResponse;
import com.eroses.external.society.dto.response.lookup.religion.AdmReligionGetResponse;
import com.eroses.external.society.dto.response.lookup.branch.AdmBranchEditResponse;
import com.eroses.external.society.dto.response.lookup.branch.AdmBranchGetResponse;
import com.eroses.external.society.dto.request.lookup.insolvencyDepartment.AdmInsolvencyDepartmentCreateRequest;
import com.eroses.external.society.dto.request.lookup.insolvencyDepartment.AdmInsolvencyDepartmentEditRequest;
import com.eroses.external.society.dto.response.lookup.insolvency.AdmInsolvencyDepartmentEditResponse;
import com.eroses.external.society.dto.response.lookup.insolvency.AdmInsolvencyDepartmentGetResponse;
import com.eroses.external.society.dto.request.lookup.calendar.AdmCalendarCreateRequest;
import com.eroses.external.society.dto.request.lookup.calendar.AdmCalendarEditRequest;
import com.eroses.external.society.dto.response.lookup.calendar.AdmCalendarEditResponse;
import com.eroses.external.society.dto.response.lookup.calendar.AdmCalendarGetResponse;
import com.eroses.external.society.dto.request.lookup.position.AdmPositionJppmCreateRequest;
import com.eroses.external.society.dto.request.lookup.position.AdmPositionJppmEditRequest;
import com.eroses.external.society.dto.response.lookup.position.AdmPositionJppmEditResponse;
import com.eroses.external.society.dto.response.lookup.position.AdmPositionJppmGetResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/lookup")
public class LookupController {
    private final LookupWriteFacade lookupWriteFacade;
    private final LookupReadFacade lookupReadFacade;
    private final EmailFacade emailFacade;

    // Religion endpoints
    @PostMapping("/religion/create")
    public ResponseEntity<ApiResponse<Long>> createReligion(@Valid @RequestBody AdmReligionCreateRequest request) {
        return ResponseUtil.buildResponse(lookupWriteFacade.createReligion(request), HttpStatus.OK);
    }

    @GetMapping("/religion/{id}")
    public ResponseEntity<ApiResponse<AdmReligionGetResponse>> getReligionById(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupReadFacade.findReligionById(id), HttpStatus.OK);
    }

    @GetMapping("/religion/all")
    public ResponseEntity<ApiResponse<Paging<AdmReligionGetResponse>>> getAllReligions(
            @RequestParam(value = "nameQuery", required = false, defaultValue = "") String nameQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllReligions(nameQuery, pageNo, pageSize), HttpStatus.OK);
    }

    @GetMapping("/religion/allActive")
    public ResponseEntity<ApiResponse<List<AdmReligionGetResponse>>> getAllActiveReligions() {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllActiveReligions(), HttpStatus.OK);
    }

    @PutMapping("/religion/{id}/edit")
    public ResponseEntity<ApiResponse<AdmReligionEditResponse>> editReligion(
            @PathVariable Long id,
            @Valid @RequestBody AdmReligionEditRequest request) throws Exception {
        return ResponseUtil.buildResponse(lookupWriteFacade.updateReligion(id, request), HttpStatus.OK);
    }

    @DeleteMapping("/religion/{id}")
    public ResponseEntity<ApiResponse<Long>> deleteReligion(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupWriteFacade.deleteReligion(id), HttpStatus.OK);
    }

    // Occupation endpoints
    @PostMapping("/occupation/create")
    public ResponseEntity<ApiResponse<Long>> createOccupation(@Valid @RequestBody AdmOccupationCreateRequest request) {
        return ResponseUtil.buildResponse(lookupWriteFacade.createOccupation(request), HttpStatus.CREATED);
    }

    @GetMapping("/occupation/{id}")
    public ResponseEntity<ApiResponse<AdmOccupationGetResponse>> getOccupationById(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupReadFacade.findOccupationById(id), HttpStatus.OK);
    }

    @GetMapping("/occupation/all")
    public ResponseEntity<ApiResponse<Paging<AdmOccupationGetResponse>>> getAllOccupations(
            @RequestParam(value = "nameQuery", required = false, defaultValue = "") String nameQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllOccupations(nameQuery, pageNo, pageSize), HttpStatus.OK);
    }

    @GetMapping("/occupation/allActive")
    public ResponseEntity<ApiResponse<List<AdmOccupationGetResponse>>> getAllActiveOccupations() {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllActiveOccupations(), HttpStatus.OK);
    }

    @PutMapping("/occupation/{id}/edit")
    public ResponseEntity<ApiResponse<AdmOccupationEditResponse>> editOccupation(
            @PathVariable Long id,
            @Valid @RequestBody AdmOccupationEditRequest request) throws Exception {
        return ResponseUtil.buildResponse(lookupWriteFacade.updateOccupation(id, request), HttpStatus.OK);
    }

    @DeleteMapping("/occupation/{id}")
    public ResponseEntity<ApiResponse<Long>> deleteOccupation(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupWriteFacade.deleteOccupation(id), HttpStatus.OK);
    }

    // Race endpoints
    @PostMapping("/race/create")
    public ResponseEntity<ApiResponse<Long>> createRace(@Valid @RequestBody AdmRaceCreateRequest request) {
        return ResponseUtil.buildResponse(lookupWriteFacade.createRace(request), HttpStatus.CREATED);
    }

    @GetMapping("/race/{id}")
    public ResponseEntity<ApiResponse<AdmRaceGetResponse>> getRaceById(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupReadFacade.findRaceById(id), HttpStatus.OK);
    }

    @GetMapping("/race/all")
    public ResponseEntity<ApiResponse<Paging<AdmRaceGetResponse>>> getAllRaces(
            @RequestParam(value = "nameQuery", required = false, defaultValue = "") String nameQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllRaces(nameQuery, pageNo, pageSize), HttpStatus.OK);
    }

    @GetMapping("/race/allActive")
    public ResponseEntity<ApiResponse<List<AdmRaceGetResponse>>> getAllActiveRaces() {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllActiveRaces(), HttpStatus.OK);
    }

    @PutMapping("/race/{id}/edit")
    public ResponseEntity<ApiResponse<AdmRaceEditResponse>> editRace(
            @PathVariable Long id,
            @Valid @RequestBody AdmRaceEditRequest request) throws Exception {
        return ResponseUtil.buildResponse(lookupWriteFacade.updateRace(id, request), HttpStatus.OK);
    }

    @DeleteMapping("/race/{id}")
    public ResponseEntity<ApiResponse<Long>> deleteRace(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupWriteFacade.deleteRace(id), HttpStatus.OK);
    }

    //Country endpoints
    @PostMapping("/country/create")
    public ResponseEntity<ApiResponse<Long>> createCountry(@Valid @RequestBody AdmAddressesCreateRequest request) {
        return ResponseUtil.buildResponse(lookupWriteFacade.createCountry(request), HttpStatus.CREATED);
    }

    @GetMapping("/country/{id}")
    public ResponseEntity<ApiResponse<AdmAddressesGetResponse>> getCountryById(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupReadFacade.findCountryById(id), HttpStatus.OK);
    }

    @GetMapping("/country/all")
    public ResponseEntity<ApiResponse<Paging<AdmAddressesGetResponse>>> getAllCountries(
            @RequestParam(value = "nameQuery", required = false, defaultValue = "") String nameQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllCountries(nameQuery, pageNo, pageSize), HttpStatus.OK);
    }

    @PutMapping("/country/{id}/edit")
    public ResponseEntity<ApiResponse<AdmAddressesEditResponse>> editCountry(
            @PathVariable Long id,
            @Valid @RequestBody AdmAddressesEditRequest request) throws Exception {
        return ResponseUtil.buildResponse(lookupWriteFacade.updateCountry(id, request), HttpStatus.OK);
    }

    @DeleteMapping("/country/{id}")
    public ResponseEntity<ApiResponse<Long>> deleteCountry(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupWriteFacade.deleteCountry(id), HttpStatus.OK);
    }

    //State endpoints
    @PostMapping("/state/create")
    public ResponseEntity<ApiResponse<Long>> createState(@Valid @RequestBody AdmAddressesCreateRequest request) {
        return ResponseUtil.buildResponse(lookupWriteFacade.createState(request), HttpStatus.CREATED);
    }

    @GetMapping("/state/{id}")
    public ResponseEntity<ApiResponse<AdmAddressesGetResponse>> getStateById(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupReadFacade.findStateById(id), HttpStatus.OK);
    }

    @GetMapping("/state/all")
    public ResponseEntity<ApiResponse<Paging<AdmAddressesGetResponse>>> getAllStates(
            @RequestParam(value = "nameQuery", required = false, defaultValue = "") String nameQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllStates(nameQuery, pageNo, pageSize), HttpStatus.OK);
    }

    @GetMapping("/state/allActive")
    public ResponseEntity<ApiResponse<List<AdmAddressesGetResponse>>> getAllActiveStates() {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllActiveStates(), HttpStatus.OK);
    }

    @PutMapping("/state/{id}/edit")
    public ResponseEntity<ApiResponse<AdmAddressesEditResponse>> editState(
            @PathVariable Long id,
            @Valid @RequestBody AdmAddressesEditRequest request) throws Exception {
        return ResponseUtil.buildResponse(lookupWriteFacade.updateState(id, request), HttpStatus.OK);
    }

    @DeleteMapping("/state/{id}")
    public ResponseEntity<ApiResponse<Long>> deleteState(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupWriteFacade.deleteState(id), HttpStatus.OK);
    }

    //District endpoints
    @PostMapping("/district/create")
    public ResponseEntity<ApiResponse<Long>> createDistrict(@Valid @RequestBody AdmAddressesCreateRequest request) {
        return ResponseUtil.buildResponse(lookupWriteFacade.createDistrict(request), HttpStatus.CREATED);
    }

    @GetMapping("/district/{id}")
    public ResponseEntity<ApiResponse<AdmAddressesGetResponse>> getDistrictById(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupReadFacade.findDistrictById(id), HttpStatus.OK);
    }

    @GetMapping("/district/all")
    public ResponseEntity<ApiResponse<Paging<AdmAddressesGetResponse>>> getAllDistricts(
            @RequestParam(value = "nameQuery", required = false, defaultValue = "") String nameQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllDistricts(nameQuery, pageNo, pageSize), HttpStatus.OK);
    }

    @GetMapping("/district/allActive")
    public ResponseEntity<ApiResponse<List<AdmAddressesGetResponse>>> getAllActiveDistricts() {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllActiveDistricts(), HttpStatus.OK);
    }

    @PutMapping("/district/{id}/edit")
    public ResponseEntity<ApiResponse<AdmAddressesEditResponse>> editDistrict(
            @PathVariable Long id,
            @Valid @RequestBody AdmAddressesEditRequest request) throws Exception {
        return ResponseUtil.buildResponse(lookupWriteFacade.updateDistrict(id, request), HttpStatus.OK);
    }

    @DeleteMapping("/district/{id}")
    public ResponseEntity<ApiResponse<Long>> deleteDistrict(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupWriteFacade.deleteDistrict(id), HttpStatus.OK);
    }

    // JPPM Branch endpoints
    @PostMapping("/jppmBranch/create")
    public ResponseEntity<ApiResponse<Long>> createJppmBranch(@Valid @RequestBody AdmBranchCreateRequest request) {
        return ResponseUtil.buildResponse(lookupWriteFacade.createJppmBranch(request), HttpStatus.CREATED);
    }

    @GetMapping("/jppmBranch/{id}")
    public ResponseEntity<ApiResponse<AdmBranchGetResponse>> getJppmBranchById(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupReadFacade.findJppmBranchById(id), HttpStatus.OK);
    }

    @GetMapping("/jppmBranch/all")
    public ResponseEntity<ApiResponse<Paging<AdmBranchGetResponse>>> getAllJppmBranches(
            @RequestParam(value = "nameQuery", required = false, defaultValue = "") String nameQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllJppmBranches(nameQuery, pageNo, pageSize), HttpStatus.OK);
    }

    @GetMapping("/jppmBranch/allActive")
    public ResponseEntity<ApiResponse<List<AdmBranchGetResponse>>> getAllActiveJppmBranches() {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllActiveJppmBranches(), HttpStatus.OK);
    }

    @PutMapping("/jppmBranch/{id}/edit")
    public ResponseEntity<ApiResponse<AdmBranchEditResponse>> editJppmBranch(
            @PathVariable Long id,
            @Valid @RequestBody AdmBranchEditRequest request) throws Exception {
        return ResponseUtil.buildResponse(lookupWriteFacade.updateJppmBranch(id, request), HttpStatus.OK);
    }

    @DeleteMapping("/jppmBranch/{id}")
    public ResponseEntity<ApiResponse<Boolean>> deleteJppmBranch(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupWriteFacade.deleteJppmBranch(id), HttpStatus.OK);
    }

    // Insolvency Department endpoints
    @PostMapping("/insolvencyDepartment/create")
    public ResponseEntity<ApiResponse<Long>> createInsolvencyDepartment(@Valid @RequestBody AdmInsolvencyDepartmentCreateRequest request) {
        return ResponseUtil.buildResponse(lookupWriteFacade.createInsolvencyDepartment(request), HttpStatus.CREATED);
    }

    @GetMapping("/insolvencyDepartment/{id}")
    public ResponseEntity<ApiResponse<AdmInsolvencyDepartmentGetResponse>> getInsolvencyDepartmentById(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupReadFacade.findInsolvencyDepartmentById(id), HttpStatus.OK);
    }

    @GetMapping("/insolvencyDepartment/all")
    public ResponseEntity<ApiResponse<Paging<AdmInsolvencyDepartmentGetResponse>>> getAllInsolvencyDepartments(
            @RequestParam(value = "nameQuery", required = false, defaultValue = "") String nameQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllInsolvencyDepartments(nameQuery, pageNo, pageSize), HttpStatus.OK);
    }

    @GetMapping("/insolvencyDepartment/allActive")
    public ResponseEntity<ApiResponse<List<AdmInsolvencyDepartmentGetResponse>>> getAllActiveInsolvencyDepartments() {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllActiveInsolvencyDepartments(), HttpStatus.OK);
    }

    @PutMapping("/insolvencyDepartment/{id}/edit")
    public ResponseEntity<ApiResponse<AdmInsolvencyDepartmentEditResponse>> editInsolvencyDepartment(
            @PathVariable Long id,
            @Valid @RequestBody AdmInsolvencyDepartmentEditRequest request) throws Exception {
        return ResponseUtil.buildResponse(lookupWriteFacade.updateInsolvencyDepartment(id, request), HttpStatus.OK);
    }

    @DeleteMapping("/insolvencyDepartment/{id}")
    public ResponseEntity<ApiResponse<Long>> deleteInsolvencyDepartment(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupWriteFacade.deleteInsolvencyDepartment(id), HttpStatus.OK);
    }

    // Calendar endpoints
    @PostMapping("/calendar/create")
    public ResponseEntity<ApiResponse<Long>> createCalendar(@Valid @RequestBody AdmCalendarCreateRequest request) {
        return ResponseUtil.buildResponse(lookupWriteFacade.createCalendar(request), HttpStatus.CREATED);
    }

    @GetMapping("/calendar/{id}")
    public ResponseEntity<ApiResponse<AdmCalendarGetResponse>> getCalendarById(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupReadFacade.findCalendarById(id), HttpStatus.OK);
    }

    @GetMapping("/calendar/all")
    public ResponseEntity<ApiResponse<Paging<AdmCalendarGetResponse>>> getAllCalendars(
            @RequestParam(value = "nameQuery", required = false, defaultValue = "") String nameQuery,
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllCalendars(nameQuery, startDate, endDate, pageNo, pageSize), HttpStatus.OK);
    }

    @GetMapping("/calendar/allActive")
    public ResponseEntity<ApiResponse<List<AdmCalendarGetResponse>>> getAllActiveCalendars() {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllActiveCalendars(), HttpStatus.OK);
    }

    @PutMapping("/calendar/{id}/edit")
    public ResponseEntity<ApiResponse<AdmCalendarEditResponse>> editCalendar(
            @PathVariable Long id,
            @Valid @RequestBody AdmCalendarEditRequest request) throws Exception {
        return ResponseUtil.buildResponse(lookupWriteFacade.updateCalendar(id, request), HttpStatus.OK);
    }

    @DeleteMapping("/calendar/{id}")
    public ResponseEntity<ApiResponse<Long>> deleteCalendar(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupWriteFacade.deleteCalendar(id), HttpStatus.OK);
    }

    // Position JPPM endpoints
    @PostMapping("/positionJppm/create")
    public ResponseEntity<ApiResponse<Long>> createPositionJppm(@Valid @RequestBody AdmPositionJppmCreateRequest request) {
        return ResponseUtil.buildResponse(lookupWriteFacade.createPositionJppm(request), HttpStatus.CREATED);
    }

    @GetMapping("/positionJppm/{id}")
    public ResponseEntity<ApiResponse<AdmPositionJppmGetResponse>> getPositionJppmById(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupReadFacade.findPositionJppmById(id), HttpStatus.OK);
    }

    @GetMapping("/positionJppm/all")
    public ResponseEntity<ApiResponse<Paging<AdmPositionJppmGetResponse>>> getAllPositionJppms(
            @RequestParam(value = "nameQuery", required = false, defaultValue = "") String nameQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllPositionJppms(nameQuery, pageNo, pageSize), HttpStatus.OK);
    }

    @GetMapping("/positionJppm/allActive")
    public ResponseEntity<ApiResponse<List<AdmPositionJppmGetResponse>>> getAllActivePositionJppms() {
        return ResponseUtil.buildResponse(lookupReadFacade.getAllActivePositionJppms(), HttpStatus.OK);
    }

    @PutMapping("/positionJppm/{id}/edit")
    public ResponseEntity<ApiResponse<AdmPositionJppmEditResponse>> editPositionJppm(
            @PathVariable Long id,
            @Valid @RequestBody AdmPositionJppmEditRequest request) throws Exception {
        return ResponseUtil.buildResponse(lookupWriteFacade.updatePositionJppm(id, request), HttpStatus.OK);
    }

    @DeleteMapping("/positionJppm/{id}")
    public ResponseEntity<ApiResponse<Boolean>> deletePositionJppm(@PathVariable Long id) {
        return ResponseUtil.buildResponse(lookupWriteFacade.deletePositionJppm(id), HttpStatus.OK);
    }

    //Email Queue
    @PostMapping("/emailQueue/resend")
    public ResponseEntity<ApiResponse<ResendEmailResponse>> resendEmail(
            @Valid @RequestBody ResendEmailRequest request) throws Exception {
        return ResponseUtil.buildResponse(emailFacade.resendEmail(request), HttpStatus.OK);
    }
}
