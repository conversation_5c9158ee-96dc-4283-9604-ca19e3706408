package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.PublicOfficerReadFacade;
import com.eroses.external.society.api.facade.PublicOfficerWriteFacade;
import com.eroses.external.society.dto.request.publicofficer.*;
import com.eroses.external.society.dto.response.publicofficer.PublicOfficerCountResponse;
import com.eroses.external.society.dto.response.publicofficer.PublicOfficerCreateResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.model.PublicOfficer;
import com.eroses.external.society.security.annotation.RequireSocietyMembership;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/public_officer")
@CrossOrigin(origins = "http://localhost:5173")
public class PublicOfficerController {
    private final PublicOfficerReadFacade publicOfficerReadFacade;

    private final PublicOfficerWriteFacade publicOfficerWriteFacade;

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<PublicOfficerCreateResponse>> create(@RequestBody @Valid PublicOfficerCreateRequest publicOfficerCreateRequest) throws Exception {
        return publicOfficerWriteFacade.create(publicOfficerCreateRequest);
    }

    @PostMapping("/branch/create")
    public ResponseEntity<ApiResponse<PublicOfficerCreateResponse>> createBranchPublicOfficer(@RequestBody @Valid BranchPublicOfficerCreateRequest publicOfficerCreateRequest) throws Exception {
        return publicOfficerWriteFacade.create(publicOfficerCreateRequest);
    }

    @GetMapping("/getAll")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<Paging<PublicOfficer>>> getAll(@RequestParam("societyId") Long societyId, @RequestParam(value = "branchId", required = false) Long branchId, @RequestParam(defaultValue = "1", name = "pageNo") Integer pageNo, @RequestParam(defaultValue = "10", name = "pageSize") Integer pageSize) {
        ApiResponse<Paging<PublicOfficer>> response = publicOfficerReadFacade.getAll(new PublicOfficerListRequest(societyId, branchId, pageNo, pageSize));
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/countAll")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<PublicOfficerCountResponse>> countAll(@RequestParam("societyId") Long societyId, @RequestParam(value = "branchId",required = false) Long branchId) {
        return publicOfficerReadFacade.countAll(new PublicOfficerCountRequest(societyId, branchId));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<PublicOfficer>> findById(@PathVariable("id") Long id) {
        return publicOfficerReadFacade.findById(id);
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<PublicOfficer>> update(@PathVariable("id") Long id, @RequestBody PublicOfficerEditRequest publicOfficerEditRequest) throws Exception {
        return publicOfficerWriteFacade.update(id, publicOfficerEditRequest);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<PublicOfficer>> remove(@PathVariable("id") Long id) throws Exception {
        return publicOfficerWriteFacade.deactivate(id);
    }


    @PutMapping("/{id}/admin/updateRo")
    public ResponseEntity<ApiResponse<PublicOfficer>> updateRo(@PathVariable("id") Long id, @RequestBody PublicOfficerUpdateRoRequest publicOfficerUpdateRoRequest) throws Exception {

        return publicOfficerWriteFacade.updateRo(id, publicOfficerUpdateRoRequest);
    }

    @PutMapping("/{id}/admin/updateApprovalStatus")
    public ResponseEntity<ApiResponse<PublicOfficer>> updateApprovalStatus(@PathVariable("id") Long id, @RequestBody PublicOfficerUpdateApprovalStatusRequest publicOfficerUpdateApprovalStatusRequest) throws Exception {

        return publicOfficerWriteFacade.updateApprovalStatus(id, publicOfficerUpdateApprovalStatusRequest);
    }

    @GetMapping("/existsSocietyPublicOfficerApplication")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<Boolean>> existsSocietyPublicOfficerApplication(@RequestParam(value = "societyId") Long societyId){
        ApiResponse<Boolean> response = publicOfficerReadFacade.existsSocietyPublicOfficerApplication(societyId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/existsBranchPublicOfficerApplication")
    public ResponseEntity<ApiResponse<Boolean>> existsBranchPublicOfficerApplication(@RequestParam(value = "branchId") Long branchId){
        ApiResponse<Boolean> response = publicOfficerReadFacade.existsBranchPublicOfficerApplication(branchId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
