package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.ExternalAgencyReviewReadFacade;
import com.eroses.external.society.api.facade.ExternalAgencyReviewWriteFacade;
import com.eroses.external.society.dto.request.externalAgencyReview.ExternalAgencyReviewCreateRequest;
import com.eroses.external.society.dto.request.externalAgencyReview.ExternalAgencyReviewUpdateRequest;
import com.eroses.external.society.dto.response.ExternalAgencyReviewGetResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/internal/externalAgencyReview")
public class ExternalAgencyReviewController {
    private final ExternalAgencyReviewWriteFacade externalAgencyReviewWriteFacade;
    private final ExternalAgencyReviewReadFacade externalAgencyReviewReadFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<Long>> create(
            @Valid @RequestBody ExternalAgencyReviewCreateRequest request) throws Exception {
        ApiResponse<Long> externalAgencyReviewId = externalAgencyReviewWriteFacade.create(request);
        return ResponseUtil.buildResponse(externalAgencyReviewId, HttpStatus.OK);
    }

    @PatchMapping(value = "/submit")
    public ResponseEntity<ApiResponse<Long>> submit(
            @Valid @RequestBody ExternalAgencyReviewUpdateRequest request) throws Exception {
        ApiResponse<Long> externalAgencyReviewId = externalAgencyReviewWriteFacade.submit(request);
        return ResponseUtil.buildResponse(externalAgencyReviewId, HttpStatus.OK);
    }

    @GetMapping(value = "/getLatest")
    public ResponseEntity<ApiResponse<ExternalAgencyReviewGetResponse>> getLatest(
            @RequestParam(value = "societyId", required = false) Long societyId) {

        ApiResponse<ExternalAgencyReviewGetResponse> response = externalAgencyReviewReadFacade.getLatest(societyId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllBySocietyId")
    public ResponseEntity<ApiResponse<List<ExternalAgencyReviewGetResponse>>> getAllBySocietyId(
            @RequestParam(value = "societyId", required = false) Long societyId) {

        ApiResponse<List<ExternalAgencyReviewGetResponse>> response = externalAgencyReviewReadFacade.getAllBySocietyId(societyId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}