package com.eroses.external.society.controller.meeting;

import com.eroses.external.society.api.facade.MeetingMemberAttendanceWriteFacade;
import com.eroses.external.society.dto.request.meetingMember.MeetingMemberDeleteRequest;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/society/meeting")
public class MemberController {
    private final MeetingMemberAttendanceWriteFacade meetingMemberAttendanceWriteFacade;

    @PutMapping("/member/delete")
    public ResponseEntity<ApiResponse<String>> delete(@RequestBody MeetingMemberDeleteRequest request) {
        ApiResponse<String> response = meetingMemberAttendanceWriteFacade.delete(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
