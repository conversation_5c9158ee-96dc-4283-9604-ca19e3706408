package com.eroses.external.society.controller;

import com.eroses.external.society.api.converter.input.BranchSecretaryApiInputConverter;
import com.eroses.external.society.api.facade.BranchSecretaryReadFacade;
import com.eroses.external.society.api.facade.BranchSecretaryWriteFacade;
import com.eroses.external.society.dto.request.*;
import com.eroses.external.society.dto.response.branchSecretary.BranchSecretaryHistoryDetailsResponse;
import com.eroses.external.society.model.*;
import com.eroses.external.society.utils.HeadersUtil;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/society/branch/secretary")
public class BranchSecretaryController {

    private final BranchSecretaryWriteFacade branchSecretaryWriteFacade;
    private final BranchSecretaryApiInputConverter branchSecretaryApiInputConverter;
    private final BranchSecretaryReadFacade branchSecretaryReadFacade;

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<BranchSecretaryHistoryDetailsResponse>> findById(@PathVariable Long id) throws Exception {
        ApiResponse<BranchSecretaryHistoryDetailsResponse> secretaryId =  branchSecretaryReadFacade.getBranchSecretaryHistoryDetails(id);

        return buildResponse(secretaryId, HttpStatus.OK);
    }

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Long>> createBranchSecretary(@RequestBody NewBranchSecretaryCreateRequest newBranchSecretaryCreateRequest) {
        BranchSecretary branchSecretary = branchSecretaryApiInputConverter.convertToModel(newBranchSecretaryCreateRequest);
        ApiResponse<Long> secretaryId =  branchSecretaryWriteFacade.create(branchSecretary);

        return buildResponse(secretaryId, HttpStatus.OK);
    }

    @GetMapping("/getAll")
    public ResponseEntity<ApiResponse<List<BranchSecretary>>> getAll() {
        ApiResponse<List<BranchSecretary>> branchSecretaries = branchSecretaryReadFacade.getAll();

        return buildResponse(branchSecretaries, HttpStatus.OK);
    }


    @GetMapping("/findBySocietyId/{societyId}")
    public ResponseEntity<ApiResponse<List<BranchSecretary>>> findBySocietyId(@PathVariable Long societyId) {
        ApiResponse<List<BranchSecretary>> branchSecretaries = branchSecretaryReadFacade.findBySocietyId(societyId);
        return ResponseUtil.buildResponse(branchSecretaries, HttpStatus.OK);
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Paging<BranchSecretary>>> getAll(@ModelAttribute BranchSecretaryPagingRequest branchSecretaryPagingRequest) {
        ApiResponse<Paging<BranchSecretary>> branchSecretaries = branchSecretaryReadFacade.getAll(branchSecretaryPagingRequest);
        return ResponseUtil.buildResponse(branchSecretaries, HttpStatus.OK);
    }

    @PutMapping("/{secretaryId}/update")
    public ResponseEntity<ApiResponse<Long>> updateBranchSecretary(@PathVariable Long secretaryId, @RequestBody BranchSecretaryUpdateRequest branchSecretaryUpdateRequest) {

        BranchSecretary branchSecretary = branchSecretaryApiInputConverter.convertToModel(branchSecretaryUpdateRequest);
        branchSecretary.setSecretaryId(secretaryId);
        ApiResponse<Long> response = branchSecretaryWriteFacade.update(branchSecretary);

        return buildResponse(response, HttpStatus.OK);
    }

    private <T> ResponseEntity<ApiResponse<T>> buildResponse(ApiResponse<T> response, HttpStatus status) {
        HttpHeaders headers = HeadersUtil.createHeaders();
        return new ResponseEntity<>(response, headers, status);
    }
}
