package com.eroses.external.society.controller;

import com.eroses.external.society.api.converter.input.BranchApiConverter;
import com.eroses.external.society.api.converter.input.BranchCommitteeApiConverter;
import com.eroses.external.society.api.converter.input.RoApprovalApiInputConverter;
import com.eroses.external.society.api.converter.input.RoQueryApiInputConverter;
import com.eroses.external.society.api.facade.BranchCommitteeWriteFacade;
import com.eroses.external.society.api.facade.BranchReadFacade;
import com.eroses.external.society.api.facade.BranchWriteFacade;
import com.eroses.external.society.api.facade.CommitteeTaskReadFacade;
import com.eroses.external.society.api.facade.branchCommittee.BranchCommitteeReadFacade;
import com.eroses.external.society.dto.request.*;
import com.eroses.external.society.dto.response.branch.*;
import com.eroses.external.society.dto.response.branchCommittee.ActiveBranchSecretaryResponse;
import com.eroses.external.society.dto.response.branchCommittee.BranchCommitteeGetResponse;
import com.eroses.external.society.dto.response.committee.CommitteeTaskModuleResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Branch;
import com.eroses.external.society.model.BranchCommittee;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.security.annotation.RequireCurrentUserNotBlacklisted;
import com.eroses.external.society.utils.HeadersUtil;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/branch")
public class BranchController {
    private final BranchReadFacade branchReadFacade;
    private final BranchCommitteeWriteFacade branchCommitteeWriteFacade;
    private final BranchCommitteeReadFacade branchCommitteeReadFacade;
    private final BranchWriteFacade branchWriteFacade;
    private final BranchApiConverter branchApiConverter;
    private final BranchCommitteeApiConverter branchCommitteeApiConverter;
    private final RoApprovalApiInputConverter roApprovalApiInputConverter;
    private final RoQueryApiInputConverter roQueryApiInputConverter;
    private final CommitteeTaskReadFacade committeeTaskReadFacade;

    @GetMapping(value = "/getBranchAllOrById")
    public ResponseEntity<ApiResponse<Paging<Branch>>> getBranch(
            @RequestParam(value = "branchId", required = false) Integer branchId,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Map<String, Object> param = new HashMap<>();
        if (branchId != null) param.put("branchId", branchId);
        param.put("pageNo", pageNo);
        param.put("pageSize", pageSize);

        ApiResponse<Paging<Branch>> response = branchReadFacade.getBranch(param);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getById/{id}")
    public ResponseEntity<ApiResponse<BranchGetByIdResponse>> getById(@PathVariable Long id) {
        ApiResponse<BranchGetByIdResponse> response = branchReadFacade.getById(id);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getBranchByParam")
    public ResponseEntity<ApiResponse<Paging<Branch>>> getBranchByParam(
            @RequestParam(value = "branchId", required = false) Integer branchId,
            @RequestParam(value = "societyId", required = false) Integer societyId,
            @RequestParam(value = "applicationStatusCode", required = false) Integer applicationStatusCode,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "icNo", required = false, defaultValue = "") String icNo,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Map<String, Object> param = new HashMap<>();
        if (branchId != null) {
            param.put("branchId", branchId);
        }
        if (societyId != null) {
            param.put("societyId", societyId);
        }
        if (applicationStatusCode != null) {
            param.put("applicationStatusCode", applicationStatusCode);
        }
        if (status != null) param.put("status", status);
        if (icNo != null && !icNo.isEmpty()) {
            param.put("icNo", icNo);
        }
        param.put("offset", pageNo);
        param.put("limit", pageSize);

        ApiResponse<Paging<Branch>> response = branchReadFacade.getBranchByParam(param, true);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/isBranchNameExist")
    public ResponseEntity<ApiResponse<Boolean>> isBranchNameExist(
            @RequestParam(value = "societyId") Long societyId,
            @RequestParam(value = "branchName") String branchName) {

        ApiResponse<Boolean> response = branchReadFacade.isBranchNameExist(societyId, branchName);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getBranchesByParam")
    public ResponseEntity<ApiResponse<Paging<BranchGetAllResponse>>> getBranchesByParam(
            @RequestParam(value = "branchId", required = false) Integer branchId,
            @RequestParam(value = "branchNo", required = false) String branchNo,
            @RequestParam(value = "societyId", required = false) Integer societyId,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "applicationStatusCode", required = false) List<Integer> applicationStatusCode,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "icNo", required = false, defaultValue = "") String icNo,
            @RequestParam(value = "sortByName", required = false) Integer sortByName,
            @RequestParam(value = "sortByApplicationStatus", required = false) Integer sortByApplicationStatus,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        //This API Return only Branch Entity, does not return BranchCommittee
        Map<String, Object> param = new HashMap<>();
        if (branchId != null) param.put("branchId", branchId);
        if (branchNo != null && !branchNo.isEmpty()) param.put("branchNo", branchNo);
        if (societyId != null) param.put("societyId", societyId);
        if (name != null && !name.isEmpty()) param.put("name", name);
        if (applicationStatusCode != null) param.put("applicationStatusCode", applicationStatusCode);
        if (status != null) param.put("status", status);
        if (icNo != null && !icNo.isEmpty()) param.put("icNo", icNo);
        if (sortByName != null) param.put("sortByName", sortByName);
        if (sortByApplicationStatus != null) param.put("sortByApplicationStatus", sortByApplicationStatus);
        param.put("offset", pageNo);
        param.put("limit", pageSize);

        ApiResponse<Paging<BranchGetAllResponse>> response = branchReadFacade.getBranchesByDetailedParam(param);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending")
    public ResponseEntity<ApiResponse<Paging<BranchPendingResponse>>> getAllPending(
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Map<String, Integer> paging = new HashMap<>();
        paging.put("pageNo", pageNo);
        paging.put("pageSize", pageSize);

        try {
            ApiResponse<Paging<BranchPendingResponse>> response = branchReadFacade.getAllPending(paging);
            return ResponseUtil.buildResponse(response, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping(value = "/{branchId}/active-secretary")
    public ResponseEntity<ApiResponse<ActiveBranchSecretaryResponse>> getActiveSecretary(@PathVariable Long branchId) {
        ApiResponse<ActiveBranchSecretaryResponse> response = branchCommitteeReadFacade.findActiveSecretary(branchId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Paging<Branch>>> getAllByParams(
            @RequestParam(required = false) String name,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize
    ) {
        BranchPagingRequest branchPagingRequest = new BranchPagingRequest();
        branchPagingRequest.setPageNo(pageNo);
        branchPagingRequest.setPageSize(pageSize);
        branchPagingRequest.setName(name);

        ApiResponse<Paging<Branch>> result = branchReadFacade.getAllByParams(branchPagingRequest);
        return ResponseUtil.buildResponse(result, HttpStatus.OK);
    }

    @RequireCurrentUserNotBlacklisted
    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<Long>> createBranch(@RequestBody BranchWriteRequest branchWriteRequest) {
        Branch branch = branchApiConverter.convertToModel(branchWriteRequest);
        ApiResponse<Long> branchId = branchWriteFacade.create(branchWriteRequest);
        return ResponseUtil.buildResponse(branchId, HttpStatus.OK);
    }

    @PutMapping("/update")
    public ResponseEntity<ApiResponse<Long>> updateBranch(@RequestBody BranchUpdateRequest branchUpdateRequest) {
        ApiResponse<Long> branchId = branchWriteFacade.update(branchUpdateRequest);

        return ResponseUtil.buildResponse(branchId, HttpStatus.OK);
    }

    @PutMapping(value = "/{id}/checkAndUpdate")
    public ResponseEntity<ApiResponse<Long>> checkAndUpdateSociety(@PathVariable Long id, @RequestBody BranchUpdateRequest branchUpdateRequest) {
        ApiResponse<Long> response = branchWriteFacade.checkAndUpdateBranch(id, branchUpdateRequest);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @DeleteMapping("/delete")
    public ResponseEntity<?> deleteBranch() {
        return ResponseEntity.ok("success");
    }

    //View 1-4 page branch information
    @GetMapping("/approvalInfo/admin")
    public ResponseEntity<ApiResponse<BranchInfoResponse>> getBranchApprovalInfo(@RequestParam Long branchId) {
        ApiResponse<BranchInfoResponse> response = branchReadFacade.getBranchApprovalInfo(branchId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    //Update RO info in Maklumat Am (Page 1))
    @PutMapping("/approvalInfo/admin/update")
    public ResponseEntity<ApiResponse<Long>> updateBranchApprovalRo(@RequestBody BranchApprovalUpdateRequest branchApprovalUpdateRequest) {
        Branch branch = branchApiConverter.convertToModel(branchApprovalUpdateRequest);
        ApiResponse<Long> response = branchWriteFacade.updateBranchApprovalRo(branch);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    //Update page 5 information (approve/reject)
    @PutMapping("/approvalDecision/admin/update")
    public ResponseEntity<ApiResponse<Long>> updateBranchApprovalDecision(@RequestBody BranchApprovalDecisionRequest branchApprovalDecisionRequest) {

        Branch branch = branchApiConverter.convertToModel(branchApprovalDecisionRequest);

        ApiResponse<Long> response = branchWriteFacade.updateBranchApprovalDecision(branch, branchApprovalDecisionRequest.getApprovalType(),
                branchApprovalDecisionRequest.getRejectReason(), branchApprovalDecisionRequest.getApprovalNote());

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/extendingBranch/admin")
    public ResponseEntity<ApiResponse<Object>> getExtendingBranch(
            @RequestParam(required = false) Long branchId,
            @RequestParam Boolean extensionDuration) {

        ApiResponse<Object> response;
        if (branchId != null) {
            response = branchReadFacade.getExtendingBranchById(branchId, extensionDuration);
        } else {
            response = branchReadFacade.getAllExtendingBranch(extensionDuration);
        }

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping("/extendingBranch/admin/update")
    public ResponseEntity<ApiResponse<Long>> updateBranchExtension(@RequestBody BranchExtensionRequest branchExtensionRequest) {
        Branch branch = branchApiConverter.convertToModel(branchExtensionRequest);
        ApiResponse<Long> response = branchWriteFacade.updateBranchExtension(branch);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping("/{id}/committee-task/enable")
    public ResponseEntity<ApiResponse<Void>> enableBranchCommitteeTask(@PathVariable("id") Long id, @RequestParam("module") String module) {
        ApiResponse<Void> response = branchWriteFacade.enableBranchCommitteeTask(id, module);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @PutMapping("/{id}/committee-task/disable")
    public ResponseEntity<ApiResponse<Void>> disableBranchCommitteeTask(@PathVariable("id") Long id, @RequestParam("module") String module) {
        ApiResponse<Void> response = branchWriteFacade.disableBranchCommitteeTask(id, module);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @GetMapping("/{id}/committee-task/status")
    public ResponseEntity<ApiResponse<CommitteeTaskModuleResponse>> branchCommitteeTaskStatus(@PathVariable("id") Long branchId, @RequestParam("module") String module) {
        return committeeTaskReadFacade.branchCommitteeTaskStatus(branchId, module);
    }

    @GetMapping("/checkBranchNameExist")
    public ResponseEntity<ApiResponse<Boolean>> checkBranchNameExist(@RequestParam String branchName, Long societyId) {
        ApiResponse<Boolean> response = branchReadFacade.checkBranchNameExist(branchName, societyId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/getForRegistration")
    public ResponseEntity<ApiResponse<Paging<BranchCommitteeGetResponse>>> getForRegistration(
            @RequestParam(value = "branchId") Long branchId,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) throws Exception {
        ApiResponse<Paging<BranchCommitteeGetResponse>> response = branchCommitteeReadFacade.getForRegistration(branchId, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/getAllActiveBySocietyId")
    public ResponseEntity<ApiResponse<List<BranchBasicInfoResponse>>> getAllActiveBySocietyId(@RequestParam Long societyId) {
        ApiResponse<List<BranchBasicInfoResponse>> response = branchReadFacade.findAllActiveBySocietyId(societyId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

//    @PostMapping(value = "/createForRegistration")
//    public ResponseEntity<ApiResponse<Long>> createForRegistration(@RequestParam Long branchId) throws Exception {
//        ApiResponse<Long> response = branchCommitteeWriteFacade.createForRegistration(branchId);
//        return ResponseUtil.buildResponse(response, HttpStatus.OK);
//    }
//
//    @PutMapping("/updateForRegistration")
//    public ResponseEntity<ApiResponse<Long>> updateForRegistration(@RequestBody Long branchId) throws Exception {
//        ApiResponse<Long> response = branchCommitteeWriteFacade.updateForRegistration(request);
//        return ResponseUtil.buildResponse(response, HttpStatus.OK);
//    }

    //DEPRECATED API
    @GetMapping("/getBranch")
    public ResponseEntity<ApiResponse<Paging<Branch>>> getBranch(
            @RequestParam(value = "branchId", required = false) Integer branchId,
            @RequestParam(value = "statusCode", required = false) Integer statusCode,
            @RequestParam(value = "societyId", required = false) Integer societyId,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Map<String, Object> param = new HashMap<>();
        if (branchId != null) param.put("branchId", branchId);
        if (statusCode != null) param.put("statusCode", statusCode);
        if (societyId != null) param.put("societyId", societyId);
        param.put("offset", pageNo);
        param.put("limit", pageSize);

        ApiResponse<Paging<Branch>> response = branchReadFacade.getBranch(param);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/searchByIcNo")
    public ResponseEntity<ApiResponse<Paging<Branch>>> getAllByParams(
            @RequestParam(required = true) String icNo
    ) {

        ApiResponse<Paging<Branch>> result = branchReadFacade.getAllByIcNo(icNo);
        return ResponseUtil.buildResponse(result, HttpStatus.OK);
    }

    @GetMapping("/approvalDecision/admin")
    public ResponseEntity<ApiResponse<Object>> getBranchApprovalDecision(
            @RequestParam Long branchId) {

        BranchReadRequest branchReadRequest = new BranchReadRequest(branchId);
        ApiResponse<Object> response = branchReadFacade.getBranchApprovalDecision(branchReadRequest);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PreAuthorize("hasRole('SERVICE')")
    @PostMapping("/internal/updateBranch")
    public ResponseEntity<ApiResponse<Boolean>> updateBranch(@RequestBody Branch branch) throws Exception {
        return ResponseEntity.ok(branchWriteFacade.internalUpdateBranch(branch));
    }

    //DEPRECATED API ENDS

    @PutMapping("/testBranchCommittee")
    public ResponseEntity<ApiResponse<List<BranchCommittee>>> testBranchCommittee(@RequestParam Long branchId) {
        List<BranchCommittee> branchCommittees = branchCommitteeWriteFacade.createForRegistration(branchId);

        return buildResponse(ApiResponse.ok(branchCommittees), HttpStatus.OK);
    }

    private <T> ResponseEntity<ApiResponse<T>> buildResponse(ApiResponse<T> response, HttpStatus status) {
        HttpHeaders headers = HeadersUtil.createHeaders();
        return new ResponseEntity<>(response, headers, status);
    }

    @GetMapping("/testSuratKebenaran")
    public ResponseEntity<ApiResponse<Object>> testSuratKebenaran(@RequestParam Long branchId) {
        branchWriteFacade.generateSuratKebenaranCawangan(branchId);
        return ResponseEntity.ok(ApiResponse.ok(null));
    }
}
