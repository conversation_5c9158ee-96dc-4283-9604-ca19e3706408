package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.PropertyOfficerReadFacade;
import com.eroses.external.society.api.facade.PropertyOfficerWriteFacade;
import com.eroses.external.society.dto.request.propertyofficer.*;
import com.eroses.external.society.dto.response.propertyofficer.PropertyOfficerApplicationResponse;
import com.eroses.external.society.dto.response.propertyofficer.PropertyOfficerCreateResponse;
import com.eroses.external.society.dto.response.propertyofficer.PropertyOfficerResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.security.annotation.RequireSocietyMembership;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/property_officer")
@CrossOrigin(origins = "http://localhost:5173")
public class PropertyOfficerController {
    private final PropertyOfficerReadFacade propertyOfficerReadFacade;
    private final PropertyOfficerWriteFacade propertyOfficerWriteFacade;

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<PropertyOfficerCreateResponse>> createPropertyOfficers(@Valid @RequestBody PropertyOfficerCreateRequest request) {
        return propertyOfficerWriteFacade.create(request);
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<PropertyOfficerApplicationResponse>> updatePropertyOfficers(@PathVariable("id") Long id, @RequestBody PropertyOfficerUpdateRequest request) {
        return propertyOfficerWriteFacade.update(id, request);
    }

    @GetMapping("/getAllActive")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<List<PropertyOfficerResponse>>> getAllActive(@RequestParam("societyId") Long societyId, @RequestParam(value = "branchId", required = false) Long branchId) {
        return propertyOfficerReadFacade.getAllActive(new GetActivePropertyOfficerRequest(societyId, branchId));
    }

    @GetMapping("/getAll")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<Paging<PropertyOfficerApplicationResponse>>> getAll(@RequestParam("societyId") Long societyId, @RequestParam(value = "branchId", required = false) Long branchId, @RequestParam(value = "pageNo", required = false, defaultValue = "1") int pageNo, @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize) {
        return propertyOfficerReadFacade.findAll(new PropertyOfficerListRequest(societyId, branchId, pageNo, pageSize));
    }

    @PutMapping("/{id}/updateRo")
    public ResponseEntity<ApiResponse<PropertyOfficerApplicationResponse>> updateRo(@PathVariable("id") Long id, @RequestBody PropertyOfficerUpdateRoRequest request) {

        return propertyOfficerWriteFacade.updateRo(id, request);
    }

    @PutMapping("/{id}/updateApprovalStatus")
    public ResponseEntity<ApiResponse<PropertyOfficerApplicationResponse>> updateApprovalStatus(@PathVariable("id") Long id, @RequestBody PropertyOfficerUpdateApprovalStatusRequest request) {

        return propertyOfficerWriteFacade.updateApprovalStatus(id, request);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deletePropertyOfficerApplication(@PathVariable("id") Long id) {
        return propertyOfficerWriteFacade.delete(id);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<PropertyOfficerApplicationResponse>> findPropertyOfficerApplicationById(@PathVariable("id") Long id) {
        return propertyOfficerReadFacade.findPropertyOfficerApplicationById(id);
    }

    @GetMapping("/existsSocietyPropertyOfficerApplication")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<Boolean>> existsSocietyPropertyOfficerApplication(
            @RequestParam(value = "societyId") Long societyId){
        ApiResponse<Boolean> response = propertyOfficerReadFacade.existsSocietyPropertyOfficerApplication(societyId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/existsBranchPropertyOfficerApplication")
    public ResponseEntity<ApiResponse<Boolean>> existsBranchPropertyOfficerApplication(
            @RequestParam(value = "branchId") Long branchId){
        ApiResponse<Boolean> response = propertyOfficerReadFacade.existsBranchPropertyOfficerApplication(branchId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
