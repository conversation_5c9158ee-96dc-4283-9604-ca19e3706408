package com.eroses.external.society.controller.branch;

import com.eroses.external.society.api.facade.BranchCommitteeWriteFacade;
import com.eroses.external.society.api.facade.BranchReadFacade;
import com.eroses.external.society.api.facade.BranchWriteFacade;
import com.eroses.external.society.api.facade.branchCommittee.BranchCommitteeReadFacade;
import com.eroses.external.society.dto.request.NewBranchSecretaryCreateRequest;
import com.eroses.external.society.dto.request.branchCommittee.CreateBranchCommitteeRequest;
import com.eroses.external.society.dto.request.branchCommittee.ListBranchCommitteeRequest;
import com.eroses.external.society.dto.request.branchCommittee.UpdateBranchCommitteeRequest;
import com.eroses.external.society.dto.request.committee.SocietyCommitteeArrangeRequest;
import com.eroses.external.society.dto.response.branchCommittee.BranchCommitteeGetResponse;
import com.eroses.external.society.dto.response.branchCommittee.BranchCommitteeUpdateResponse;
import com.eroses.external.society.dto.response.branchSecretary.NewBranchSecretaryCreateResponse;
import com.eroses.external.society.dto.response.committee.BranchCommitteeGetOneResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.security.annotation.RequireTargetUserNotBlacklisted;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/branch/committee")
public class BranchCommitteeController {
    private final BranchReadFacade branchReadFacade;
    private final BranchWriteFacade branchWriteFacade;
    private final BranchCommitteeWriteFacade branchCommitteeWriteFacade;
    private final BranchCommitteeReadFacade branchCommitteeReadFacade;

    private <T> ResponseEntity<ApiResponse<T>> wrap(ApiResponse<T> tApiResponse){
        return ResponseUtil.buildResponse(tApiResponse, HttpStatus.valueOf(tApiResponse.getCode()));
    }

    @PostMapping("/secretary")
    public ResponseEntity<ApiResponse<NewBranchSecretaryCreateResponse>> createNewSecretary(@RequestBody NewBranchSecretaryCreateRequest request) throws Exception {
        ApiResponse<NewBranchSecretaryCreateResponse> response = branchCommitteeWriteFacade.createNewSecretary(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<BranchCommitteeUpdateResponse>> remove(@PathVariable("id") Long id) {
        return wrap(branchCommitteeWriteFacade.removeBranchCommittee(id));
    }

    @GetMapping("/list")
    public ResponseEntity<ApiResponse<Paging<BranchCommitteeGetOneResponse>>> listAll(
            @RequestParam("branchId") Long branchId,
            @RequestParam(value = "status", defaultValue = "", required = false) String statusCode,
            @RequestParam(value = "pageNo", defaultValue = "1", required = false) Integer pageNo,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        return wrap(branchCommitteeReadFacade.findMembersByBranchId(new ListBranchCommitteeRequest(branchId, statusCode, pageNo, pageSize)));
    }

    @GetMapping("/listAjk")
    public ResponseEntity<ApiResponse<Paging<BranchCommitteeGetOneResponse>>> listAllAjk(
            @RequestParam("branchId") Long branchId,
            @RequestParam(value = "status", defaultValue = "", required = false) String statusCode,
            @RequestParam(value = "pageNo", defaultValue = "1", required = false) Integer pageNo,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        return wrap(branchCommitteeReadFacade.findCommitteeByBranchId(new ListBranchCommitteeRequest(branchId, statusCode, pageNo, pageSize)));
    }

    @GetMapping("/listNonAjk")
    public ResponseEntity<ApiResponse<Paging<BranchCommitteeGetOneResponse>>> listAllNonAjk(
            @RequestParam("branchId") Long branchId,
            @RequestParam(value = "status", defaultValue = "", required = false) String statusCode,
            @RequestParam(value = "pageNo", defaultValue = "1", required = false) Integer pageNo,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        return wrap(branchCommitteeReadFacade.findNonCommitteeByBranchId(new ListBranchCommitteeRequest(branchId, statusCode, pageNo, pageSize)));
    }

    @RequireTargetUserNotBlacklisted(identificationNoParam = "committeeIcNo")
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<BranchCommitteeUpdateResponse>> update(@PathVariable("id") Long id, @RequestBody UpdateBranchCommitteeRequest request){
        return wrap(branchCommitteeWriteFacade.updateBranchCommittee(id,request));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<BranchCommitteeGetOneResponse>> findById(@PathVariable("id") Long id){
        return wrap(branchCommitteeReadFacade.findById(id));
    }

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<BranchCommitteeUpdateResponse>> create(@Valid @RequestBody CreateBranchCommitteeRequest request){
        return wrap(branchCommitteeWriteFacade.create(request));
    }

    @GetMapping("/getAllCommittee")
    public ResponseEntity<ApiResponse<Paging<BranchCommitteeGetOneResponse>>> getAllCommittee(
            @RequestParam("branchId") Long branchId,
            @RequestParam(value = "pageNo", defaultValue = "1", required = false) Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10", required = false) Integer pageSize) {
        return wrap(branchCommitteeReadFacade.getAllCommitteesByBranchId(branchId, pageNo, pageSize));
    }

    @GetMapping(value = "/getCommitteeTaskEligibleMembers")
    public ResponseEntity<ApiResponse<List<BranchCommitteeGetResponse>>> getCommitteeTaskEligibleMembers(
            @RequestParam (value = "branchId") Long branchId,
            @RequestParam (value = "module") String module) {
        ApiResponse<List<BranchCommitteeGetResponse>> response = branchCommitteeReadFacade.getCommitteeTaskEligibleMembers(branchId, module);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/arrangeCommittee")
    public ResponseEntity<ApiResponse<Object>> arrangeCommittee(@RequestBody SocietyCommitteeArrangeRequest request) {
        ApiResponse<Object> response = branchCommitteeWriteFacade.arrangeCommittee(request);

        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }
}
