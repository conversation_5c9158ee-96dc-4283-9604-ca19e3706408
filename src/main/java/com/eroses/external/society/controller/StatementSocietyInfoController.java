package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.StatementSocietyInfoReadFacade;
import com.eroses.external.society.api.facade.StatementSocietyInfoWriteFacade;
import com.eroses.external.society.dto.request.statement.StatementSocietyInfoCreateRequest;
import com.eroses.external.society.dto.request.statement.StatementSocietyInfoEditRequest;
import com.eroses.external.society.dto.request.statement.StatementSocietyInfoGetAllRequest;
import com.eroses.external.society.dto.request.statement.StatementSocietyInfoGetOneRequest;
import com.eroses.external.society.dto.response.statement.StatementGetGeneralInfoResponse;
import com.eroses.external.society.dto.response.statement.StatementSocietyInfoEditResponse;
import com.eroses.external.society.dto.response.statement.StatementSocietyInfoCreateResponse;
import com.eroses.external.society.dto.response.statement.StatementSocietyInfoGetOneResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

// todo: Not using now, check if really not using then delete
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/statement")
public class StatementSocietyInfoController {
    private final StatementSocietyInfoWriteFacade statementSocietyInfoWriteFacade;
    private final StatementSocietyInfoReadFacade statementSocietyInfoReadFacade;

    @PostMapping(value = "/societyInfo/create")
    public ResponseEntity<ApiResponse<StatementSocietyInfoCreateResponse>> createStatementSocietyInfo(
            @RequestBody StatementSocietyInfoCreateRequest request) throws Exception {
        ApiResponse<StatementSocietyInfoCreateResponse> response = statementSocietyInfoWriteFacade.create(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/societyInfo/update")
    public ResponseEntity<ApiResponse<StatementSocietyInfoEditResponse>> updateStatementSocietyInfo(
            @RequestBody StatementSocietyInfoEditRequest request) {
        ApiResponse<StatementSocietyInfoEditResponse> response = statementSocietyInfoWriteFacade.update(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/societyInfo/get")
    public ResponseEntity<ApiResponse<StatementSocietyInfoGetOneResponse>> getAllStatementSocietyInfos(
            @RequestParam (value = "societyId", required = false) Long societyId,
            @RequestParam (value = "branchId", required = false) Long branchId,
            @RequestParam (value = "year", required = false) Integer year,
            @RequestParam (value = "statementId") Long statementId) {

        Map<String, Object> param = new HashMap<>();
        if (societyId != null) param.put("societyId", societyId);
        if (branchId != null) param.put("branchId", branchId);
        if (year != null) param.put("year", year);
        param.put("statementId", statementId);

        ApiResponse<StatementSocietyInfoGetOneResponse> response = statementSocietyInfoReadFacade.getStatementSocietyInfo(param);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

//    @GetMapping(value = "/{id}")
//    public ResponseEntity<ApiResponse<StatementSocietyInfoGetOneResponse>> getStatementSocietyInfoById(
//            @PathVariable Long id) {
//        StatementSocietyInfoGetOneRequest request = new StatementSocietyInfoGetOneRequest(id);
//        ApiResponse<StatementSocietyInfoGetOneResponse> response = statementSocietyInfoReadFacade.getOne(request);
//        return ResponseUtil.buildResponse(response, HttpStatus.OK);
//    }
//
//    @PutMapping(value = "/{id}/edit")
//    public ResponseEntity<ApiResponse<StatementSocietyInfoEditResponse>> editStatementSocietyInfo(
//            @PathVariable Long id, @RequestBody StatementSocietyInfoEditRequest request) throws Exception {
//        ApiResponse<StatementSocietyInfoEditResponse> response = statementSocietyInfoWriteFacade.update(request);
//        return ResponseUtil.buildResponse(response, HttpStatus.OK);
//    }
}