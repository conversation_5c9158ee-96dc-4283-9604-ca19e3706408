package com.eroses.external.society.controller;

import com.eroses.external.society.api.converter.input.ConstitutionValueApiInputConverter;
import com.eroses.external.society.api.facade.ConstitutionValueReadFacade;
import com.eroses.external.society.api.facade.ConstitutionValueWriteFacade;
import com.eroses.external.society.dto.request.ConstitutionValueEditRequest;
import com.eroses.external.society.dto.request.ConstitutionValueGetAllRequest;
import com.eroses.external.society.dto.request.ConstitutionValueRegisterRequest;
import com.eroses.external.society.dto.response.constitution.ConstitutionValueCustomJawatanResponse;
import com.eroses.external.society.dto.response.constitution.ConstitutionValueEditResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.ConstitutionValue;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.HeadersUtil;
import com.eroses.external.society.utils.ResponseUtil;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/constitutionvalue")
public class ConstitutionValueController {

    private final ConstitutionValueWriteFacade constitutionValueWriteFacade;
    private final ConstitutionValueReadFacade constitutionValueReadFacade;
    private final ConstitutionValueApiInputConverter constitutionValueApiInputConverter;
    private final UserFacade userFacade;


    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Long>> createConstitutionValue(@RequestBody ConstitutionValueRegisterRequest constitutionValueRegisterRequest) {
        try {
            User currentUser = userFacade.me();
            ConstitutionValue constitutionvalue = constitutionValueApiInputConverter.constitutionvalueRegisterRequest2ConstitutionValue(constitutionValueRegisterRequest);
            constitutionvalue.setModifiedBy(currentUser.getId());
            ApiResponse<Long> constitutionValueId =  constitutionValueWriteFacade.create(constitutionvalue);
            return ResponseUtil.buildResponse(constitutionValueId, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

//    @PutMapping(value = "/{id}/edit")
//    public ResponseEntity<ApiResponse<ConstitutionValueEditResponse>> editConstitutionValue(@PathVariable Long id, @RequestBody ConstitutionValueEditRequest request) {
//        User currentUser = userFacade.me();
//        request.setModifiedBy(String.valueOf(currentUser.getId()));
//        ApiResponse<ConstitutionValueEditResponse> response = constitutionValueWriteFacade.update(id, request);
//        HttpHeaders headers = HeadersUtil.createHeaders();
//        return new ResponseEntity<>(response, headers, HttpStatus.OK);
//    }

    @PutMapping("/update")
    public ResponseEntity<ApiResponse<Long>> updateConstitutionValue(@RequestBody ConstitutionValueEditRequest constitutionValueEditRequest) {
        ConstitutionValue constitutionvalue = constitutionValueApiInputConverter.constitutionvalueEditRequest2ConstitutionValue(constitutionValueEditRequest);
        ApiResponse<Long> constitutionValueId = constitutionValueWriteFacade.update(constitutionvalue);
        return ResponseUtil.buildResponse(constitutionValueId, HttpStatus.OK);
    }

    @GetMapping(value = "/getAll")
    public ResponseEntity<ApiResponse<Paging<ConstitutionValue>>> getAllConstitutionValues(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "amendmentId", required = false) Long amendmentId,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "clauseContentId", required = false) Long clauseContentId,
            @RequestParam(value = "constitutionContentId", required = false) Long constitutionContentId,
            @RequestParam(value = "status", required = false) Long applicationStatusCode,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "30") Integer pageSize) {
        ConstitutionValueGetAllRequest request = new ConstitutionValueGetAllRequest();
        request.setId(id);
        request.setAmendmentId(amendmentId);
        request.setSocietyId(societyId);
        request.setClauseContentId(clauseContentId);
        request.setConstitutionContentId(constitutionContentId);
        request.setApplicationStatusCode(applicationStatusCode);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);

        ApiResponse<Paging<ConstitutionValue>> response = constitutionValueReadFacade.getAll(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    //not using
    @GetMapping(value = "/clause/{id}/findById")
    public ApiResponse<ConstitutionValue> findConstitutionValueById(@PathVariable Long id) {
        return constitutionValueReadFacade.findById(id);
    }

    @GetMapping(value = "/callCustomJawatan")
    public ResponseEntity<ApiResponse<List<ConstitutionValueCustomJawatanResponse>>> callCustomJawatan(
            @RequestParam(value = "societyId") Long societyId,
            @RequestParam(value = "constitutionContentId") Long constitutionContentId,
            @RequestParam(value = "amendmentId", required = false) Long amendmentId) {
        ApiResponse<List<ConstitutionValueCustomJawatanResponse>> response = constitutionValueReadFacade.callCustomJawatan(societyId, constitutionContentId, amendmentId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @DeleteMapping(value = "/deleteCustomJawatan")
    public ResponseEntity<ApiResponse<Object>> deleteCustomJawatan(
            @RequestParam(value = "id") Long id) {
        ApiResponse<Object> response = constitutionValueWriteFacade.deleteCustomJawatan(id);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
