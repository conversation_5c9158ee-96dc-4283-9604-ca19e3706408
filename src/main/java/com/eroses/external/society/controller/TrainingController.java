package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.training.TrainingCertificateReadFacade;
import com.eroses.external.society.api.facade.training.TrainingCourseReadFacade;
import com.eroses.external.society.api.facade.training.TrainingEnrollmentReadFacade;
import com.eroses.external.society.api.facade.training.TrainingEnrollmentWriteFacade;
import com.eroses.external.society.api.facade.training.TrainingMaterialReadFacade;
import com.eroses.external.society.api.facade.training.TrainingQuizReadFacade;
import com.eroses.external.society.api.facade.training.TrainingQuizWriteFacade;
import com.eroses.external.society.dto.request.training.QuizAttemptSubmitRequest;
import com.eroses.external.society.dto.request.training.QuizAttemptStartRequest;
import com.eroses.external.society.dto.request.training.TrainingEnrollmentRequest;
import com.eroses.external.society.dto.request.training.UpdateEnrollmentRequest;
import com.eroses.external.society.dto.response.training.TrainingCourseDetailResponse;
import com.eroses.external.society.dto.response.training.TrainingCourseResponse;
import com.eroses.external.society.dto.response.training.TrainingEnrollmentResponse;
import com.eroses.external.society.dto.response.training.TrainingMaterialResponse;
import com.eroses.external.society.dto.response.training.TrainingQuizResponse;
import com.eroses.external.society.dto.response.training.QuizAttemptResponse;
import com.eroses.external.society.dto.response.training.TrainingCertificateResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.service.training.TrainingUserContextService;
import com.eroses.user.model.User;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/society/training")
@Api(value = "Training API")
@RequiredArgsConstructor
public class TrainingController {

    private final TrainingCourseReadFacade trainingCourseReadFacade;
    private final TrainingMaterialReadFacade trainingMaterialReadFacade;
    private final TrainingQuizReadFacade trainingQuizReadFacade;
    private final TrainingQuizWriteFacade trainingQuizWriteFacade;
    private final TrainingEnrollmentReadFacade trainingEnrollmentReadFacade;
    private final TrainingEnrollmentWriteFacade trainingEnrollmentWriteFacade;
    private final TrainingCertificateReadFacade trainingCertificateReadFacade;
    private final TrainingUserContextService userContextService;

    // Training Course Endpoints

    @ApiOperation(value = "Get all published training courses")
    @GetMapping(value = "/courses", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<List<TrainingCourseResponse>> getAllPublishedTrainingCourses() {
        return trainingCourseReadFacade.getAllPublishedTrainingCourses();
    }

    @ApiOperation(value = "Get training course details by ID")
    @GetMapping(value = "/courses/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<TrainingCourseDetailResponse> getTrainingCourseDetailsById(@PathVariable Long id, Authentication authentication) {
        Long userId = userContextService.getUserIdAsLong(authentication);
        return trainingCourseReadFacade.getTrainingCourseDetailsById(id, userId);
    }

    @ApiOperation(value = "Enroll in a training course")
    @PostMapping(value = "/courses/enroll", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<TrainingEnrollmentResponse> enrollInTrainingCourse(@Valid @RequestBody TrainingEnrollmentRequest request, Authentication authentication) {
        Long userId = userContextService.getUserIdAsLong(authentication);
        return trainingEnrollmentWriteFacade.createTrainingEnrollment(request, userId);
    }

    // Training Material Endpoints

    @ApiOperation(value = "Get all training materials for a course")
    @GetMapping(value = "/courses/{courseId}/materials", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<List<TrainingMaterialResponse>> getAllTrainingMaterialsByCourseId(@PathVariable Long courseId) {
        return trainingMaterialReadFacade.getAllTrainingMaterialsByCourseId(courseId);
    }

    @ApiOperation(value = "Get training material by ID")
    @GetMapping(value = "/materials/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<TrainingMaterialResponse> getTrainingMaterialById(@PathVariable Long id) {
        return trainingMaterialReadFacade.getTrainingMaterialById(id);
    }

    @ApiOperation(value = "Update material progress")
    @PostMapping(value = "/materials/{id}/progress", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<Void> updateMaterialProgress(@PathVariable Long id, Authentication authentication) {
        Long userId = userContextService.getUserIdAsLong(authentication);
        return trainingEnrollmentWriteFacade.updateMaterialProgress(id, userId);
    }

    // Quiz Endpoints

    @ApiOperation(value = "Get quiz for a course")
    @GetMapping(value = "/courses/{courseId}/quiz", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<TrainingQuizResponse> getTrainingQuizByCourseId(@PathVariable Long courseId) {
        return trainingQuizReadFacade.getTrainingQuizByCourseId(courseId);
    }

    @ApiOperation(value = "Start a quiz attempt")
    @PostMapping(value = "/quiz/attempt", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<QuizAttemptResponse> startQuizAttempt(@RequestBody QuizAttemptStartRequest request, Authentication authentication) {
        Long userId = userContextService.getUserIdAsLong(authentication);
        return trainingQuizWriteFacade.startQuizAttempt(request.getId(), userId);
    }

    @ApiOperation(value = "Submit quiz answers")
    @PutMapping(value = "/quiz/attempts/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<QuizAttemptResponse> submitQuizAttempt(@Valid @RequestBody QuizAttemptSubmitRequest request, Authentication authentication) {
        Long userId = userContextService.getUserIdAsLong(authentication);
        return trainingQuizWriteFacade.submitQuizAttempt(request, userId);
    }

    @ApiOperation(value = "Get quiz attempt result")
    @GetMapping(value = "/quiz/attempts/{attemptId}/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<QuizAttemptResponse> getQuizAttemptResult(@PathVariable Long attemptId, Authentication authentication) {
        Long userId = userContextService.getUserIdAsLong(authentication);
        return trainingQuizReadFacade.getQuizAttemptResult(attemptId, userId);
    }

    // Enrollment Endpoints

    @ApiOperation(value = "Get all enrollments for current user")
    @GetMapping(value = "/enrollments", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<List<TrainingEnrollmentResponse>> getAllEnrollmentsForCurrentUser(Authentication authentication) {
        Long userId = userContextService.getUserIdAsLong(authentication);
        return trainingEnrollmentReadFacade.getAllTrainingEnrollmentsByUserId(userId);
    }

    @ApiOperation(value = "Get enrollment by ID")
    @GetMapping(value = "/enrollments/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<TrainingEnrollmentResponse> getEnrollmentById(@PathVariable Long id, Authentication authentication) {
        Long userId = userContextService.getUserIdAsLong(authentication);
        return trainingEnrollmentReadFacade.getTrainingEnrollmentById(id, userId);
    }

    @ApiOperation(value = "Update enrollment progress")
    @PostMapping(value = "/enrollments/progress", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<TrainingEnrollmentResponse> updateEnrollmentProgress(@RequestBody UpdateEnrollmentRequest request, Authentication authentication) {
        Long userId = userContextService.getUserIdAsLong(authentication);
        return trainingEnrollmentWriteFacade.updateEnrollmentProgress(request.getId(), request.getStep());
    }

    // Certificate Endpoints

    @ApiOperation(value = "Get all certificates for current user")
    @GetMapping(value = "/certificates", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<List<TrainingCertificateResponse>> getAllCertificatesForCurrentUser(Authentication authentication) {
        Long userId = userContextService.getUserIdAsLong(authentication);
        return trainingCertificateReadFacade.getAllTrainingCertificatesByUserId(userId);
    }

    @ApiOperation(value = "Get certificate by ID")
    @GetMapping(value = "/certificates/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<TrainingCertificateResponse> getCertificateById(@PathVariable Long id, Authentication authentication) {
        Long userId = userContextService.getUserIdAsLong(authentication);
        return trainingCertificateReadFacade.getTrainingCertificateById(id, userId);
    }

    @ApiOperation(value = "Download certificate PDF")
    @GetMapping(value = "/certificates/{id}/download")
    public ResponseEntity<Resource> downloadCertificate(@PathVariable Long id, Authentication authentication) {
        Long userId = userContextService.getUserIdAsLong(authentication);
        Resource resource = trainingCertificateReadFacade.downloadTrainingCertificate(id, userId);

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_PDF)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"certificate.pdf\"")
                .body(resource);
    }

    @ApiOperation(value = "Verify certificate by code")
    @GetMapping(value = "/certificates/verify/{code}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<TrainingCertificateResponse> verifyCertificate(@PathVariable String code) {
        return trainingCertificateReadFacade.verifyTrainingCertificate(code);
    }
}
