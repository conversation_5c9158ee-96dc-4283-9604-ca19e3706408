package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.StatementReadFacade;
import com.eroses.external.society.api.facade.StatementWriteFacade;
import com.eroses.external.society.dto.request.statement.*;
import com.eroses.external.society.dto.response.statement.*;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.security.annotation.RequireSocietyMembership;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/statement")
public class StatementController {
    private final StatementWriteFacade statementWriteFacade;
    private final StatementReadFacade statementReadFacade;

    //STAY --> previously, /create
    @PostMapping(value = "/general/create")
    public ResponseEntity<ApiResponse<StatementCreateResponse>> createStatement(@RequestBody StatementCreateRequest request) {
        ApiResponse<StatementCreateResponse> response = statementWriteFacade.create(request);

        if (String.valueOf(response.getCode()).matches("^2\\d\\d$")) {
            return ResponseUtil.buildResponse(response, HttpStatus.OK);
        } else {
            return ResponseUtil.buildResponse(response, HttpStatus.BAD_REQUEST);
        }
    }

    //added
    @PutMapping(value = "/general/update")
    public ResponseEntity<ApiResponse<StatementEditResponse>> updateStatement(@RequestBody StatementEditRequest request) {
        ApiResponse<StatementEditResponse> response = statementWriteFacade.update(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/general/checkAndUpdate")
    public ResponseEntity<ApiResponse<StatementEditResponse>> checkAndUpdateStatement(@RequestBody StatementEditRequest request) {
        //TODO: For Raja, Modify Request and Response Body based on needs
        ApiResponse<StatementEditResponse> response = statementWriteFacade.checkAndUpdate(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    //STAY --> previously, /list
    @GetMapping(value = "/general/list")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<Paging<StatementGetOneResponse>>> listStatements(
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "applicationStatusCode", required = false) Integer applicationStatusCode,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Map<String, Object> params = new HashMap<>();
        if (searchQuery != null && !searchQuery.isEmpty()) params.put("searchQuery", searchQuery);
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);

        ApiResponse<Paging<StatementGetOneResponse>> response = statementReadFacade.listStatements(params);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    //added
    @GetMapping(value = "/general/get")
    public ResponseEntity<ApiResponse<StatementGetOneResponse>> getStatement(
            @RequestParam(value = "statementId", required = false) Long statementId,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "statementYear", required = false) Long statementYear) {

        Map<String, Object> params = new HashMap<>();
        if (statementId != null) params.put("statementId", statementId);
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        if (statementYear != null) params.put("statementYear", statementYear);

        ApiResponse<StatementGetOneResponse> response = statementReadFacade.getGeneralStatementInfo(params);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    // ========================================= MEETING =================================================== //
    @GetMapping(value = "/general/meeting/get")
    //if no branchId, don't select meetings under branch, if branchId exist, only get meeting under branch
    public ResponseEntity<ApiResponse<Object>> getMeetingInfo(
            @RequestParam(value = "meetingId", required = false) Long meetingId,
            @RequestParam(value = "statementId", required = false) Long statementId,
            @RequestParam(value = "societyId") Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "meetingDate", required = false) LocalDate meetingDate) {

        Map<String, Object> params = new HashMap<>();
        if (meetingId != null) params.put("meetingId", meetingId);
        if (statementId != null) params.put("statementId", statementId);
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        if (meetingDate != null) params.put("meetingDate", meetingDate);

        ApiResponse<Object> response = statementReadFacade.getMeetingInfo(params);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/general/meeting/update")
    public ResponseEntity<ApiResponse<Object>> updateMeetingIntoStatement(
            @RequestParam(value = "meetingId") Long meetingId,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "statementId") Long statementId) {

        ApiResponse<Object> response = statementWriteFacade.updateMeetingIntoStatement(meetingId, statementId, societyId, branchId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    // =====================================================================================================//

    // todo: After SU/bendahari pressing hantar statement, set all the child status to selesai
    // todo: Set all the committee rows status to menunggu (Need UI for committee feedback table columns)
//    @PutMapping(value = "/{statementId}/sendStatement")
//    public ResponseEntity<ApiResponse<StatementSendResponse>> sendStatement(
//            @PathVariable("statementId") Long statementId
//    ) throws Exception {
//        return statementWriteFacade.sendStatement(statementId);
//    }

    // todo: Set all the committee rows status to selesai (Need UI for committee feedback table columns)
    @PutMapping(value = "/{statementId}/feedbackAjk")
    public ResponseEntity<ApiResponse<StatementFeedbackAjkResponse>> feedbackAjk(
            @PathVariable("statementId") Long statementId
    ) throws Exception {
        return statementWriteFacade.feedbackAjk(statementId);
    }


    // 1. Export
    // for branch committee, recheck for branch committee
    @GetMapping("/{statementId}/exportPdf/{societyId}")
    public ResponseEntity<byte[]> exportPdf(@PathVariable Long statementId, @PathVariable Long societyId,
                                            @RequestParam(value = "branchId", required = false) Long branchId) {

        Map<String, Object> response = statementReadFacade.exportPdfV2(statementId, societyId, branchId);

        if (response == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        byte[] byteResponse = (byte[]) response.get("byte");
        HttpHeaders headers = (HttpHeaders) response.get("headers");

        return new ResponseEntity<>(byteResponse, headers, HttpStatus.OK);
    }

    @GetMapping("/exportPdfV2")
    public ResponseEntity<byte[]> exportPdfV2(
            @RequestParam(value = "statementId") Long statementId,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId) {

        Map<String, Object> response = statementReadFacade.exportPdfV2(statementId, societyId, branchId);

        if (response == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        byte[] byteResponse = (byte[]) response.get("byte");
        HttpHeaders headers = (HttpHeaders) response.get("headers");

        return new ResponseEntity<>(byteResponse, headers, HttpStatus.OK);
    }

    // 2. Download pdf api
    @PostMapping("{statementId}/downloadStatementPdf")
    public ResponseEntity<ApiResponse<StatementDownloadPdfResponse>> downloadStatement(
            @PathVariable Long statementId,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam MultipartFile multipartFile,
            byte[] contentBytes) throws Exception {
        return statementWriteFacade.downloadStatementPdf(statementId, societyId, branchId, multipartFile, contentBytes);
    }

    //Delete Statement
    @DeleteMapping("/{statementId}/delete")
    public ResponseEntity<ApiResponse<Object>> deleteStatement(@PathVariable Long statementId,
                                                               @RequestParam(value = "societyId", required = false) Long societyId,
                                                               @RequestParam(value = "branchId", required = false) Long branchId) {
        ApiResponse<Object> response = statementWriteFacade.deleteStatement(statementId, societyId, branchId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping("/{statementId}/submit")
    public ResponseEntity<ApiResponse<StatementEditResponse>> submitStatement(
            @PathVariable("statementId") Long statementId,
            @RequestBody StatementGeneralEditRequest request
    ) throws Exception {

        ApiResponse<StatementEditResponse> response = statementWriteFacade.submitStatement(statementId, request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/submittedStatements")
    public ResponseEntity<ApiResponse<List<StatementSubmittedResponse>>> submittedStatements(
            @RequestParam(value = "societyId") Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId) {
        ApiResponse<List<StatementSubmittedResponse>> response = statementReadFacade.submittedStatements(societyId, branchId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/{statementId}/getCommitteesAppointedDates")
    public ResponseEntity<ApiResponse<GetCommitteesAppointedDatesResponse>> getCommitteesAppointedDates(
            @PathVariable("statementId") Long statementId,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId) {

        ApiResponse<GetCommitteesAppointedDatesResponse> response = statementReadFacade.getCommitteesAppointedDates(statementId, societyId, branchId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/{statementId}/getCommitteesForStatement")
    public ResponseEntity<ApiResponse<GetCommitteesForStatementResponse>> getCommitteesForStatement(
            @PathVariable("statementId") Long statementId,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "appointedDate", required = false) LocalDate appointedDate) {

        ApiResponse<GetCommitteesForStatementResponse> response = statementReadFacade.getCommitteesForStatement(statementId, societyId, branchId, appointedDate);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/internal/getStatement")
    public ResponseEntity<ApiResponse<Paging<StatementInfoForInternalResponse>>> getStatementForInternal(
            @RequestParam(value = "statementId", required = false) Long statementId,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "stateCode", required = false) String stateCode,
            @RequestParam(value = "applicationStatusCode", required = false) String applicationStatusCode,
            @RequestParam(value = "statementYear", required = false) Long statementYear,
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "isBranch", required = false, defaultValue = "false") Boolean isBranch,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Map<String, Object> params = new HashMap<>();
        if (statementId != null) params.put("statementId", statementId);
        if (societyId != null) params.put("societyId", societyId);
        if (isBranch != null) params.put("isBranch", isBranch);
        if (branchId != null) {
            params.put("isBranch", true);
            params.put("branchId", branchId);
        }
        if (stateCode != null && !stateCode.isEmpty()) params.put("stateCode", stateCode);
        if (applicationStatusCode != null) params.put("applicationStatusCode", applicationStatusCode);
        if (statementYear != null) params.put("statementYear", statementYear);
        if (searchQuery != null && !searchQuery.isEmpty()) params.put("searchQuery", searchQuery);
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);

        ApiResponse<Paging<StatementInfoForInternalResponse>> response = statementReadFacade.getStatementListForInternal(params);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping("/internal/changeStatementYear")
    public ResponseEntity<ApiResponse<Object>> changeStatementYear(@Valid @RequestBody StatementChangeYearRequest request) {
        ApiResponse<Object> response = statementWriteFacade.changeStatementYear(request);

        if (response.getCode() == 200) {
            return ResponseUtil.buildResponse(response, HttpStatus.OK);
        } else {
            return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
        }
    }

    @GetMapping("/getLaporanAktivitiTemplate")
    public ResponseEntity<ApiResponse<Object>> getLaporanAktivitiTemplate() {
        ApiResponse<Object> response = statementReadFacade.getLaporanAktivitiTemplate();

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/getJuruauditInfo")
    public ResponseEntity<ApiResponse<StatementJuruauditFasalInfoResponse>> getJuruauditInfo(
            @RequestParam (value = "societyId", required = false) Long societyId,
            @RequestParam (value = "branchId", required = false) Long branchId) {
        ApiResponse<StatementJuruauditFasalInfoResponse> response = statementReadFacade.getJuruauditInfo(societyId, branchId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
