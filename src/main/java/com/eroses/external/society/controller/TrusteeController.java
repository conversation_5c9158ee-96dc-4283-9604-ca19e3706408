package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.TrusteeReadFacade;
import com.eroses.external.society.api.facade.TrusteeWriteFacade;
import com.eroses.external.society.dto.request.trustee.TrusteeCreateRequest;
import com.eroses.external.society.dto.request.trustee.TrusteeEditRequest;
import com.eroses.external.society.dto.request.trustee.TrusteeGetAllRequest;
import com.eroses.external.society.dto.response.trustee.TrusteeGetResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.model.TrusteeHolder;
import com.eroses.external.society.security.annotation.RequireSocietyMembership;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/trustee")
public class TrusteeController {

    private final TrusteeReadFacade trusteeReadFacade;

    private final TrusteeWriteFacade trusteeWriteFacade;

    @GetMapping("/getAll")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<Paging<TrusteeGetResponse>>> getAllTrustee(@RequestParam(value = "societyId") Long societyId,
                                                                                 @RequestParam(value = "branchId",required = false) Long branchId,
                                                                                 @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                                                                 @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        TrusteeGetAllRequest req = new TrusteeGetAllRequest(societyId,branchId, pageNo, pageSize);
        return ResponseUtil.buildResponse(trusteeReadFacade.getAll(req), HttpStatus.OK);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<TrusteeGetResponse>> getTrusteeById(@PathVariable("id") Long id) {
        return ResponseUtil.buildResponse(trusteeReadFacade.findById(id), HttpStatus.OK);
    }

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<TrusteeGetResponse>> createTrustee(@RequestBody @Valid TrusteeCreateRequest request) throws Exception {
        return trusteeWriteFacade.create(request);
    }

    @PutMapping("{id}")
    public ResponseEntity<ApiResponse<TrusteeGetResponse>> updateTrustee(@PathVariable("id") Long id, @RequestBody TrusteeEditRequest trusteeEditRequest) throws Exception {
        return trusteeWriteFacade.update(id, trusteeEditRequest);

    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<TrusteeHolder>> deactivateTrustee(@PathVariable("id") Long id) throws Exception {
        return trusteeWriteFacade.deactivate(id);
    }

}
