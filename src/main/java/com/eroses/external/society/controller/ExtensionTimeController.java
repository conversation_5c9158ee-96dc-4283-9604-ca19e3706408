package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.ExtensionTimeReadFacade;
import com.eroses.external.society.api.facade.ExtensionTimeWriteFacade;
import com.eroses.external.society.dto.request.*;
import com.eroses.external.society.dto.response.BranchAmendmentGetResponse;
import com.eroses.external.society.dto.response.ExtensionTimeFindByIdResponse;
import com.eroses.external.society.dto.response.ExtensionTimeGetResponse;
import com.eroses.external.society.model.*;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/extensionTime")
public class ExtensionTimeController {
    private final ExtensionTimeWriteFacade extensionTimeWriteFacade;
    private final ExtensionTimeReadFacade extensionTimeReadFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<Long>> create(@RequestBody ExtensionTimeCreateRequest extensionTimeCreateRequest) throws Exception {
        ApiResponse<Long> extensionId = extensionTimeWriteFacade.create(extensionTimeCreateRequest);
        return ResponseUtil.buildResponse(extensionId, HttpStatus.OK);
    }

    @GetMapping(value = "/findExisting")
    public ResponseEntity<ApiResponse<ExtensionTimeGetResponse>> findExisting(
            @RequestParam(value = "branchId", required = false) Long branchId) {

        ApiResponse<ExtensionTimeGetResponse> response = extensionTimeReadFacade.findExisting(branchId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/{id}")
    public ResponseEntity<ApiResponse<ExtensionTimeFindByIdResponse>> findById(
            @PathVariable("id") Long id) {

        ApiResponse<ExtensionTimeFindByIdResponse> response = extensionTimeReadFacade.findById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}