package com.eroses.external.society.controller.branch;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.eroses.external.society.api.facade.branchCommittee.BranchCommitteeReadFacade;
import com.eroses.external.society.dto.request.NewBranchSecretaryCreateRequest;
import com.eroses.external.society.dto.response.branchCommittee.BranchCommitteeBasicResponse;
import com.eroses.external.society.dto.response.branchCommittee.BranchCommitteeDetailResponse;
import com.eroses.external.society.dto.response.branchSecretary.NewBranchSecretaryCreateResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.ResponseUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/{societyId}")
public class BranchCommitteeControllerV2 {
    private final BranchCommitteeReadFacade branchCommitteeReadFacade;

    private <T> ResponseEntity<ApiResponse<T>> wrap(ApiResponse<T> tApiResponse){
        return ResponseUtil.buildResponse(tApiResponse, HttpStatus.valueOf(tApiResponse.getCode()));
    }

    @GetMapping("/branch/{branchId}/committees")
    public ResponseEntity<ApiResponse<Paging<BranchCommitteeBasicResponse>>> findAllBranchCommittees(
        @PathVariable long societyId,
        @PathVariable long branchId,
        @RequestParam(defaultValue = "10") int pageSize,
        @RequestParam(defaultValue = "1") int pageNo
    ) throws Exception {
        ApiResponse<Paging<BranchCommitteeBasicResponse>> response = branchCommitteeReadFacade.findAllBranchCommittees(
            branchId,
            pageSize,
            pageNo
        );
        return wrap(response);
    }

    @GetMapping("/branch/{branchId}/committees/{committeeId}")
    public ResponseEntity<ApiResponse<BranchCommitteeDetailResponse>> findBranchCommitteeDetail(
        @PathVariable long committeeId
    ) throws Exception {
        ApiResponse<BranchCommitteeDetailResponse> response = branchCommitteeReadFacade.findBranchCommitteeDetail(
            committeeId
        );
        return wrap(response);
    }
}
