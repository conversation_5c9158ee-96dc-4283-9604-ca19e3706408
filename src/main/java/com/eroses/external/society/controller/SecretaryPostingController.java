package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.posting.PostingWriteFacade;
import com.eroses.external.society.dto.request.posting.PostingCreateRequest;
import com.eroses.external.society.dto.request.posting.PostingUpdateRequest;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.utils.ResponseUtil;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/secretary/posting")
public class SecretaryPostingController {

    private final PostingWriteFacade postingWriteFacade;

    /**
     * Create a new posting (Secretary)
     * Only allows ACTIVITY and PROMOTION categories
     */
    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Long>> createPosting(
            @Valid @RequestBody PostingCreateRequest request) {
        ApiResponse<Long> response = postingWriteFacade.createPostingBySecretary(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    /**
     * Update an existing posting (Secretary)
     * Only allows updating DRAFT postings
     */
    @PutMapping("/update")
    public ResponseEntity<ApiResponse<Boolean>> updatePosting(
            @Valid @RequestBody PostingUpdateRequest request) {
        ApiResponse<Boolean> response = postingWriteFacade.updatePostingBySecretary(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    /**
     * Delete a posting (Secretary)
     * Only allows deleting DRAFT postings
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Boolean>> deletePosting(
            @PathVariable Long id
           ) {
        ApiResponse<Boolean> response = postingWriteFacade.deletePosting(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
