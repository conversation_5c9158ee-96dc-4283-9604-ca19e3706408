package com.eroses.external.society.controller;

import com.eroses.external.society.api.converter.input.AppealApiInputConverter;
import com.eroses.external.society.api.converter.input.RoAppealApiInputConverter;
import com.eroses.external.society.api.facade.AppealReadFacade;
import com.eroses.external.society.api.facade.AppealWriteFacade;
import com.eroses.external.society.api.facade.RoAppealApprovalReadFacade;
import com.eroses.external.society.api.facade.RoAppealApprovalWriteFacade;
import com.eroses.external.society.dto.request.*;
import com.eroses.external.society.dto.request.appeal.AppealSearchRequest;
import com.eroses.external.society.dto.response.appeal.AppealByIdResponse;
import com.eroses.external.society.dto.response.appeal.GetByParamAppealResponse;
import com.eroses.external.society.dto.response.appeal.GetSearchAppealResponse;
import com.eroses.external.society.dto.response.appeal.GetUserSocietiesForAppealResponse;
import com.eroses.external.society.model.*;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequiredArgsConstructor
@RequestMapping("/society/appeal")
public class AppealController {
    private final AppealReadFacade appealReadFacade;
    private final AppealWriteFacade appealWriteFacade;
    private final AppealApiInputConverter appealApiInputConverter;
    private final RoAppealApiInputConverter roAppealApiInputConverter;
    private final RoAppealApprovalReadFacade roAppealReadFacade;
    private final RoAppealApprovalWriteFacade roAppealWriteFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<Object>> createAppeal(@RequestBody AppealCreateRequest appealCreateRequest) {
        Appeal appeal = appealApiInputConverter.convertToModel(appealCreateRequest);
        ApiResponse<Object> response = appealWriteFacade.createAppeal(appeal);

        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @GetMapping(value = "/get")
    public ResponseEntity<ApiResponse<Paging<Appeal>>> getAppeal(
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Map<String, Integer> paging = new HashMap<>();
        paging.put("pageNo", pageNo);
        paging.put("pageSize", pageSize);

        ApiResponse<Paging<Appeal>> response = appealReadFacade.getAppeal(paging);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/search") //use for internal
    public ResponseEntity<ApiResponse<Paging<GetSearchAppealResponse>>> searchAppeal(
            @RequestBody AppealSearchRequest appealSearchRequest,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        ApiResponse<Paging<GetSearchAppealResponse>> response = appealReadFacade.searchAppeal(appealSearchRequest, pageNo, pageSize);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getUserSocieties")
    public ResponseEntity<ApiResponse<Paging<GetUserSocietiesForAppealResponse>>> getAppealsByUser(
            @RequestParam(required = false) String searchQuery, //society name and society no
            @RequestParam(required = false) Integer filterAppealApplicationStatus, //status permohonan
            @RequestParam(required = false) Integer filterIdSebab, //jenis rayuan
            @RequestParam(required = false) LocalDate filterAppealDate, //tarikh permohonan
            @RequestParam(required = false) Integer filterSocietyStatus,//status pertubuhan
            @RequestParam(required = false) String identificationNo,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Map<String, Object> param = new HashMap<>();
        if (searchQuery != null && !searchQuery.isEmpty()) param.put("searchQuery", searchQuery);
        if (filterAppealApplicationStatus != null && filterAppealApplicationStatus != 0) param.put("filterAppealApplicationStatus", filterAppealApplicationStatus);
        if (filterIdSebab != null && filterIdSebab != 0) param.put("filterIdSebab", filterIdSebab);
        if (filterAppealDate != null) param.put("filterAppealDate", filterAppealDate);
        if (filterSocietyStatus != null && filterSocietyStatus != 0) param.put("filterSocietyStatus", filterSocietyStatus);
        if (identificationNo != null) param.put("identificationNo", identificationNo);
        param.put("pageNo", pageNo);
        param.put("pageSize", pageSize);

        ApiResponse<Paging<GetUserSocietiesForAppealResponse>> response = appealReadFacade.getAppealsByUser(param);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getByParam")
    public ResponseEntity<ApiResponse<Paging<GetByParamAppealResponse>>> getAppealByParam(
            @RequestParam(required = false) String searchQuery,
            @RequestParam(required = false) Integer appealApplicationStatus, //status permohonan
            @RequestParam(required = false) Integer idSebab, //jenis rayuan
            @RequestParam(required = false) LocalDate appealDate, //tarikh permohonan
            @RequestParam(required = false) Integer societyApplicationStatus,
            @RequestParam(required = false, defaultValue = "false") Boolean isIndividualAppeal,
            @RequestParam(required = false) String identificationNo,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Map<String, Object> param = new HashMap<>();
        if (searchQuery != null && !searchQuery.isEmpty()) param.put("searchQuery", searchQuery);
        if (appealApplicationStatus != null) param.put("appealApplicationStatus", appealApplicationStatus);
        if (idSebab != null) param.put("idSebab", idSebab);
        if (appealDate != null) param.put("appealDate", appealDate);
        if (societyApplicationStatus != null) param.put("societyApplicationStatus", societyApplicationStatus);
        if (identificationNo != null) param.put("identificationNo", identificationNo);
        param.put("isIndividualAppeal", isIndividualAppeal);
        param.put("pageNo", pageNo);
        param.put("pageSize", pageSize);

        ApiResponse<Paging<GetByParamAppealResponse>> response = appealReadFacade.getAppealByParam(param);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getById/{id}")
    public ResponseEntity<ApiResponse<AppealByIdResponse>> getById(@PathVariable Long id) {
        ApiResponse<AppealByIdResponse> response = appealReadFacade.getById(id);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/update")
    public ResponseEntity<ApiResponse<Object>> update(AppealCreateRequest appealCreateRequest) {
        Appeal appeal = appealApiInputConverter.convertToModel(appealCreateRequest);
        ApiResponse<Object> response = appealWriteFacade.updateAppeal(appeal);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getUserCommitteeInfo")
    public ResponseEntity<ApiResponse<Object>> getUserCommitteeInfo(@RequestParam Long societyId) {
        ApiResponse<Object> response = appealReadFacade.getUserCommitteeInfo(societyId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/appealApplication")
    public ResponseEntity<ApiResponse<Object>> appealApplication(@RequestBody AppealApplyRequest appealApplyRequest) {
        Appeal appeal = appealApiInputConverter.convertToModel(appealApplyRequest);
        ApiResponse<Object> response = appealWriteFacade.appealApplication(appeal);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/createRoAppealApproval")
    public ResponseEntity<ApiResponse<Object>> createRoAppealApproval(@RequestBody RoAppealCreateRequest roAppealCreateRequest) {
        RoAppealApproval roAppealApproval = roAppealApiInputConverter.roAppealCreateRequest2RoAppealApproval(roAppealCreateRequest);
        ApiResponse<Object> response = roAppealWriteFacade.createRoAppealApproval(roAppealApproval);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getQueryHistory")
    public ResponseEntity<ApiResponse<Object>> getQueryHistory(@RequestParam Long appealId) {
        ApiResponse<Object> response = roAppealReadFacade.getQueryHistory(appealId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/countByType")
    public ResponseEntity<ApiResponse<Object>> getCountByType() {
        ApiResponse<Object> response = appealReadFacade.getCountByType();

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/getRoAppeal")
    public ResponseEntity<ApiResponse<Object>> getRoAppeal(@RequestBody GetRoAppealRequest getRoAppealRequest) {
        ApiResponse<Object> response = roAppealReadFacade.getRoAppeal(getRoAppealRequest);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/removeAppealApplication")
    public ResponseEntity<ApiResponse<Object>> removeAppealApplication(@RequestParam Long id) {
        ApiResponse<Object> response = appealWriteFacade.removeAppealApplication(id);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getApplicantInfo")
    public ResponseEntity<ApiResponse<Object>> getApplicantInfo(@RequestParam Long appealId) {
        ApiResponse<Object> response = appealReadFacade.getApplicantInfo(appealId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
