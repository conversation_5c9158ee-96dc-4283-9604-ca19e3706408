package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.*;
import com.eroses.external.society.dto.request.roDecision.UpdateApprovalStatusRequest;
import com.eroses.external.society.dto.request.roDecision.UpdateExternalAgencyReviewStatusRequest;
import com.eroses.external.society.dto.request.roDecision.UpdateQueryRequest;
import com.eroses.external.society.dto.request.roDecision.UpdateRoRequest;
import com.eroses.external.society.dto.response.appeal.GetAllDecisionRecordAppealResponse;
import com.eroses.external.society.dto.response.roDecision.GetAllDecisionRecordAmendmentResponse;
import com.eroses.external.society.dto.response.roDecision.*;
import com.eroses.external.society.model.*;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/roDecision")
public class RoDecisionController {
    private final RoDecisionReadFacade roDecisionReadFacade;
    private final RoDecisionWriteFacade roDecisionWriteFacade;

    @GetMapping(value = "/getAllPendingCount/society")
    public ResponseEntity<ApiResponse<GetAllPendingCountResponse>> getAllPendingCountSociety() {
        ApiResponse<GetAllPendingCountResponse> response = roDecisionReadFacade.getAllPendingCountSociety();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPendingCount/branch")
    public ResponseEntity<ApiResponse<GetAllPendingCountResponse>> getAllPendingCountBranch() {
        ApiResponse<GetAllPendingCountResponse> response = roDecisionReadFacade.getAllPendingCountBranch();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPendingCount/query")
    public ResponseEntity<ApiResponse<GetAllPendingCountResponse>> getAllPendingCountQuery() {
        ApiResponse<GetAllPendingCountResponse> response = roDecisionReadFacade.getAllPendingCountQuery();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending/society/registration")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingSocietyRegistrationResponse>>> getAllPendingSocietyRegistration(@RequestParam Integer isQuery,
                                                                                                                          @RequestParam(value = "organizationCategory", defaultValue = "") String categoryCode,
                                                                                                                          @RequestParam(value = "subOrganizationCategory", defaultValue = "") String subCategoryCode,
                                                                                                                          @RequestParam(value = "societyName", defaultValue = "") String societyName,
                                                                                                                          @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                                                                                                          @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        //IsQuery = 0, Keputusan Induk Tab, IsQuery = 1, Kuiri Tab
        ApiResponse<Paging<GetAllPendingSocietyRegistrationResponse>> response = roDecisionReadFacade.getAllPendingSocietyRegistration(isQuery, categoryCode, subCategoryCode, societyName, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending/society/externalAgencyReview")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingSocietyExternalAgencyReviewResponse>>> getAllPendingSocietyExternalAgencyReview(
                                                                                                                          @RequestParam(value = "categoryCode", defaultValue = "") String categoryCode,
                                                                                                                          @RequestParam(value = "subCategoryCode", defaultValue = "") String subCategoryCode,
                                                                                                                          @RequestParam(value = "societyName", defaultValue = "") String societyName,
                                                                                                                          @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                                                                                                          @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllPendingSocietyExternalAgencyReviewResponse>> response = roDecisionReadFacade.getAllPendingSocietyExternalAgencyReview(categoryCode, subCategoryCode, societyName, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending/society/public_officer")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingSocietyPublicOfficerResponse>>> getAllPendingSocietyPublicOfficer(
            @RequestParam(value = "categoryCode", defaultValue = "") String categoryCode,
            @RequestParam(value = "subCategoryCode", defaultValue = "") String subCategoryCode,
            @RequestParam(value = "societyName", defaultValue = "") String societyName,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllPendingSocietyPublicOfficerResponse>> response = roDecisionReadFacade.getAllPendingSocietyPublicOfficer(categoryCode, subCategoryCode, societyName, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending/branch/public_officer")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingBranchPublicOfficerResponse>>> getAllPendingPublicOfficer(
            @RequestParam(value = "stateCode", defaultValue = "") String stateCode,
            @RequestParam(value = "searchQuery", defaultValue = "") String searchQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllPendingBranchPublicOfficerResponse>> response = roDecisionReadFacade.getAllPendingBranchPublicOfficer(stateCode, searchQuery, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/getAllPending/society/property_officer")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingSocietyPropertyOfficerResponse>>> getAllPendingSocietyPropertyOfficer(
            @RequestParam(value = "categoryCode", defaultValue = "") String categoryCode,
            @RequestParam(value = "subCategoryCode", defaultValue = "") String subCategoryCode,
            @RequestParam(value = "societyName", defaultValue = "") String societyName,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllPendingSocietyPropertyOfficerResponse>> response = roDecisionReadFacade.getAllPendingSocietyPropertyOfficer(categoryCode, subCategoryCode, societyName, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/getAllPending/branch/property_officer")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingBranchPropertyOfficerResponse>>> getAllPendingBranchPropertyOfficer(
            @RequestParam(value = "stateCode", defaultValue = "") String stateCode,
            @RequestParam(value = "searchQuery", defaultValue = "") String searchQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllPendingBranchPropertyOfficerResponse>> response = roDecisionReadFacade.getAllPendingBranchPropertyOfficer(stateCode, searchQuery, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending/branch/registration")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingBranchRegistrationResponse>>> getAllPendingBranchRegistration(@RequestParam Integer isQuery,
                                                                                                                        @RequestParam(value = "search", defaultValue = "") String search,
                                                                                                                        @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                                                                                                        @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        //IsQuery = 0, Keputusan Induk Tab, IsQuery = 1, Kuiri Tab
        ApiResponse<Paging<GetAllPendingBranchRegistrationResponse>> response = roDecisionReadFacade.getAllPendingBranchRegistration(isQuery, search, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending/society/nonCitizen")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingSocietyNonCitizenResponse>>> getAllPendingSocietyNonCitizen(@RequestParam(value = "categoryCode", defaultValue = "") String categoryCode,
                                                                                                                      @RequestParam(value = "subCategoryCode", defaultValue = "") String subCategoryCode,
                                                                                                                      @RequestParam(value = "societyName", defaultValue = "") String societyName,
                                                                                                                      @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                                                                                                      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllPendingSocietyNonCitizenResponse>> response = roDecisionReadFacade.getAllPendingSocietyNonCitizen(categoryCode, subCategoryCode, societyName, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending/branch/nonCitizen")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingBranchNonCitizenResponse>>> getAllPendingBranchNonCitizen(@RequestParam(value = "categoryCode", defaultValue = "") String categoryCode,
                                                                                                                      @RequestParam(value = "subCategoryCode", defaultValue = "") String subCategoryCode,
                                                                                                                      @RequestParam(value = "societyName", defaultValue = "") String societyName,
                                                                                                                      @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                                                                                                      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllPendingBranchNonCitizenResponse>> response = roDecisionReadFacade.getAllPendingBranchNonCitizen(categoryCode, subCategoryCode, societyName, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending/branch/extensionTime")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingBranchExtensionTimeResponse>>> getAllPendingBranchExtensionTime(@RequestParam(value = "searchQuery", defaultValue = "") String searchQuery,
                                                                                                                    @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                                                                                                    @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllPendingBranchExtensionTimeResponse>> response = roDecisionReadFacade.getAllPendingBranchExtensionTime(searchQuery, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending/appeal")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingAppealResponse>>> getAllPendingAppeal(
            @RequestParam(value = "isQuery", required = false) Integer isQuery,
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "categoryCodeJppm", required = false) String categoryCodeJppm,
            @RequestParam(value = "subCategoryCode", required = false) String subCategoryCode,
            @RequestParam(value = "societyName", required = false) String societyName,
            @RequestParam(value = "idSebab", required = false) Long idSebab,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllPendingAppealResponse>> response = roDecisionReadFacade.getAllPendingAppeal(isQuery, searchQuery, categoryCodeJppm, subCategoryCode, societyName, idSebab, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending/amendment")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingAmendmentResponse>>> getAllPendingAmendments(
            @RequestParam(value = "isQuery", required = false) Integer isQuery,
            @RequestParam(value = "societyName", required = false) String societyName,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "categoryCodeJppm", required = false) String categoryCodeJppm,
            @RequestParam(value = "subCategoryCode", required = false) String subCategoryCode,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        ApiResponse<Paging<GetAllPendingAmendmentResponse>> response = roDecisionReadFacade.getAllPendingAmendments(isQuery, societyName, societyId, categoryCodeJppm, subCategoryCode, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending/branch/amendment")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingBranchAmendmentResponse>>> getAllPendingBranchAmendment(
            @RequestParam(value = "searchQuery", defaultValue = "") String searchQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllPendingBranchAmendmentResponse>> response = roDecisionReadFacade.getAllPendingBranchAmendment(searchQuery, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getDecisionRecord/amendment")
    public ResponseEntity<ApiResponse<Paging<GetAllDecisionRecordAmendmentResponse>>> getAllDecisionRecordAmendment(
            @RequestParam(value = "state", required = false) Long stateCode,
            @RequestParam(value = "status", required = false) String decision,
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllDecisionRecordAmendmentResponse>> response = roDecisionReadFacade.getAllDecisionRecordAmendment(stateCode, decision, searchQuery, pageNo, pageSize);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getDecisionRecord/appeal")
    public ResponseEntity<ApiResponse<Paging<GetAllDecisionRecordAppealResponse>>> getAllDecisionRecordAppeal(
            @RequestParam(value = "stateCode", required = false) Long stateCode,
            @RequestParam(value = "decision", required = false) String decision,
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllDecisionRecordAppealResponse>> response = roDecisionReadFacade.getAllDecisionRecordAppeal(stateCode, decision, searchQuery, pageNo, pageSize);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending/society/liquidation")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingLiquidationResponse>>> getAllPendingSocietyLiquidation(@RequestParam(value = "categoryCode", defaultValue = "", required = false) String categoryCode,
                                                                                                                 @RequestParam(value = "subCategoryCode", defaultValue = "", required = false) String subCategoryCode,
                                                                                                                 @RequestParam(value = "societyName", defaultValue = "", required = false) String societyName,
                                                                                                                 @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                                                                                                 @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllPendingLiquidationResponse>> response = roDecisionReadFacade.getAllPendingLiquidation(categoryCode, subCategoryCode, societyName, pageNo, pageSize, false, "");
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending/branch/liquidation")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingLiquidationResponse>>> getAllPendingBranchLiquidation(@RequestParam(value = "searchQuery", defaultValue = "", required = false) String searchQuery,
                                                                                                                @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                                                                                                @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllPendingLiquidationResponse>> response = roDecisionReadFacade.getAllPendingLiquidation("", "", "", pageNo, pageSize, true, searchQuery);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAllPending/society/newSecretary")
    public ResponseEntity<ApiResponse<Paging<GetAllPendingSocietyNewSecretaryResponse>>> getAllPendingSocietyNewSecretary(@RequestParam Integer isQuery,
                                                                                                                          @RequestParam(value = "categoryCode", defaultValue = "", required = false) String categoryCode,
                                                                                                                          @RequestParam(value = "subCategoryCode", defaultValue = "", required = false) String subCategoryCode,
                                                                                                                          @RequestParam(value = "societyName", defaultValue = "", required = false) String societyName,
                                                                                                                          @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                                                                                                          @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<GetAllPendingSocietyNewSecretaryResponse>> response = roDecisionReadFacade.getAllPendingSocietyNewSecretary(isQuery, categoryCode, subCategoryCode, societyName, pageNo, pageSize);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PatchMapping(value = "/updateRo")
    public ResponseEntity<ApiResponse<UpdateRoResponse>> updateRo(@RequestBody UpdateRoRequest updateRoRequest) {
        ApiResponse<UpdateRoResponse> response = roDecisionWriteFacade.updateRo(updateRoRequest);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PatchMapping(value = "/updateApprovalStatus")
    public ResponseEntity<ApiResponse<UpdateApprovalStatusResponse>> updateApprovalStatus(@RequestBody UpdateApprovalStatusRequest updateApprovalStatusRequest) throws Exception {
        ApiResponse<UpdateApprovalStatusResponse> response = roDecisionWriteFacade.updateApprovalStatus(updateApprovalStatusRequest);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PatchMapping(value = "/updateQuery")
    public ResponseEntity<ApiResponse<UpdateQueryResponse>> updateQuery(@RequestBody UpdateQueryRequest updateQueryRequest) throws Exception {
        ApiResponse<UpdateQueryResponse> response = roDecisionWriteFacade.updateQuery(updateQueryRequest);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PatchMapping("/external-agency-review")
    public ResponseEntity<ApiResponse<Long>> updateExternalAgencyReviewStatus(
            @Valid @RequestBody UpdateExternalAgencyReviewStatusRequest request) throws Exception {

        ApiResponse<Long> response = roDecisionWriteFacade.updateExternalAgencyReviewStatus(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
