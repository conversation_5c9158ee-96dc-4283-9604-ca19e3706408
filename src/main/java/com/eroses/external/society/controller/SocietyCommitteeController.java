package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.CommitteeReadFacade;
import com.eroses.external.society.api.facade.CommitteeWriteFacade;
import com.eroses.external.society.dto.request.committee.*;
import com.eroses.external.society.dto.request.statement.CommitteeListAjkRequest;
import com.eroses.external.society.dto.response.committee.CommitteeCreateResponse;
import com.eroses.external.society.dto.response.committee.CommitteeEditResponse;
import com.eroses.external.society.dto.response.committee.CommitteeGetResponse;
import com.eroses.external.society.dto.response.committee.CommitteeListAjkResponse;
import com.eroses.external.society.dto.response.constitution.CommitteePositionValuesResponse;
import com.eroses.external.society.dto.response.statement.CommitteeCountAjkResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.model.enums.StatusCode;
import com.eroses.external.society.security.annotation.RequireSocietyMembership;
import com.eroses.external.society.security.annotation.RequireTargetUserNotBlacklisted;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/committee")
@CrossOrigin(origins = "http://localhost:5173")
public class SocietyCommitteeController {
    private final CommitteeWriteFacade committeeWriteFacade;
    private final CommitteeReadFacade committeeReadFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<CommitteeCreateResponse>> createCommittee(@Valid @RequestBody CommitteeCreateRequest request) throws Exception {
        ApiResponse<CommitteeCreateResponse> response = committeeWriteFacade.create(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @PostMapping(value = "/createForRegistration")
    public ResponseEntity<ApiResponse<Long>> createForRegistration(@RequestBody CommitteeCreateListRequest request) throws Exception {
        ApiResponse<Long> response = committeeWriteFacade.createForRegistration(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @PutMapping(value = "/updateForRegistration")
    public ResponseEntity<ApiResponse<Long>> updateForRegistration(@RequestBody CommitteeCreateListRequest request) throws Exception {
        ApiResponse<Long> response = committeeWriteFacade.updateForRegistration(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @RequireTargetUserNotBlacklisted
    @PutMapping(value = "/{id}/edit")
    public ResponseEntity<ApiResponse<CommitteeEditResponse>> editCommittee(@PathVariable Long id, @RequestBody CommitteeEditRequest request) throws Exception {
        ApiResponse<CommitteeEditResponse> response = committeeWriteFacade.update(id, request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAll")
    public ResponseEntity<ApiResponse<Paging<CommitteeGetResponse>>> getAllCommittees(
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "sortBy", required = false, defaultValue = "") String sortBy,
            @RequestParam(value = "sortDir", required = false, defaultValue = "") String sortDir,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        CommitteeGetAllSocietiesRequest request = new CommitteeGetAllSocietiesRequest();
        request.setSocietyId(societyId);
        request.setSortBy(sortBy);
        request.setSortDir(sortDir);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);

        ApiResponse<Paging<CommitteeGetResponse>> response = committeeReadFacade.getAll(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/listMember")
    public ResponseEntity<ApiResponse<List<CommitteeListAjkResponse>>> listNormalMember(@RequestParam Long societyId){
        CommitteeListAjkRequest request = new CommitteeListAjkRequest(societyId, StatusCode.AKTIF.getCode(), null,null);
        ApiResponse<List<CommitteeListAjkResponse>> response = committeeReadFacade.listNormalMemberInSociety(request);

        return ResponseUtil.buildResponse(response,HttpStatus.valueOf(response.getCode()));
    }

    @GetMapping("/listActive")
    public ResponseEntity<ApiResponse<List<CommitteeListAjkResponse>>> listAllActive(
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "committeeName", required = false, defaultValue = "") String committeeName){
        ApiResponse<List<CommitteeListAjkResponse>> response = committeeReadFacade.listAllActiveInSociety(societyId, committeeName);

        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }


    @PutMapping(value = "/{id}/delete")
    public ResponseEntity<ApiResponse<Long>> deleteCommittee(@PathVariable Long id) {
        ApiResponse<Long> response = committeeWriteFacade.delete(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/countAjk")
    public ResponseEntity<ApiResponse<CommitteeCountAjkResponse>> countAjkInSociety(@RequestParam Long societyId) {
        ApiResponse<CommitteeCountAjkResponse> response = committeeReadFacade.countAjkInSociety(societyId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/listAjk")
    @RequireSocietyMembership(societyIdParam = "societyId")
    public ResponseEntity<ApiResponse<Paging<CommitteeListAjkResponse>>> listAjkInSociety(
            @RequestParam Long societyId,
            @RequestParam(value = "status", required = false, defaultValue = "001") @Pattern(regexp = "00[1|8]") String status,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        CommitteeListAjkRequest request = new CommitteeListAjkRequest(societyId,status, pageNo, pageSize);
        ApiResponse<Paging<CommitteeListAjkResponse>> response = committeeReadFacade.listAjkInSociety(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/exportAjk")
    public ResponseEntity<ApiResponse<String>> exportAjk(
            @RequestParam(value = "societyId") Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId) {
        ApiResponse<String> response = committeeReadFacade.exportAjk(societyId, branchId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/exportAjkGeneral")
    public ResponseEntity<ApiResponse<String>> exportAjkGeneral(
            @RequestParam(value = "societyId") Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId) {
        ApiResponse<String> response = committeeReadFacade.exportAjkGeneral(societyId, branchId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/listOfCommitteePositions")
    public ResponseEntity<ApiResponse<CommitteePositionValuesResponse>> listOfCommitteePositions(
            @RequestParam (value = "societyId") Long societyId) {
        ApiResponse<CommitteePositionValuesResponse> response = committeeReadFacade.listOfCommitteePositions(societyId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getCommitteeTaskEligibleMembers")
    public ResponseEntity<ApiResponse<List<CommitteeGetResponse>>> getCommitteeTaskEligibleMembers(
            @RequestParam (value = "societyId") Long societyId,
            @RequestParam (value = "module") String module) {
        ApiResponse<List<CommitteeGetResponse>> response = committeeReadFacade.getCommitteeTaskEligibleMembers(societyId, module);

        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }

    @PutMapping(value = "/arrangeCommittee")
    public ResponseEntity<ApiResponse<Object>> arrangeCommittee(@RequestBody SocietyCommitteeArrangeRequest request) {
        ApiResponse<Object> response = committeeWriteFacade.arrangeCommittee(request);

        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }
}
