package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.RoQueryReadFacade;
import com.eroses.external.society.api.facade.RoQueryWriteFacade;
import com.eroses.external.society.dto.request.roDecision.RoQueryGetRequest;
import com.eroses.external.society.dto.request.roQuery.RoQueryCreateRequest;
import com.eroses.external.society.dto.request.roQuery.RoQueryEditRequest;
import com.eroses.external.society.dto.response.roDecision.RoQueryGetResponse;
import com.eroses.external.society.dto.response.roQuery.RoQueryCreateResponse;
import com.eroses.external.society.dto.response.roQuery.RoQueryEditResponse;
import com.eroses.external.society.dto.response.roQuery.RoQueryGetAllRequest;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.model.RoQuery;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/roQuery")
@CrossOrigin(origins = "http://localhost:5173")
public class RoQueryController {
    private final RoQueryWriteFacade roQueryWriteFacade;
    private final RoQueryReadFacade roQueryReadFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<RoQueryCreateResponse>> createRoQuery(@Valid @RequestBody RoQueryCreateRequest request) throws Exception {
        ApiResponse<RoQueryCreateResponse> response = roQueryWriteFacade.create(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @PutMapping(value = "/{id}/edit")
    public ResponseEntity<ApiResponse<RoQueryEditResponse>> editRoQuery(@PathVariable Long id, @Valid @RequestBody RoQueryEditRequest request) throws Exception {
        ApiResponse<RoQueryEditResponse> response = roQueryWriteFacade.update(id, request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAll")
    public ResponseEntity<ApiResponse<Paging<RoQuery>>> getAllRoQuery(
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        RoQueryGetAllRequest request = new RoQueryGetAllRequest();
        request.setSocietyId(societyId);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);

        ApiResponse<Paging<RoQuery>> response = roQueryReadFacade.getAll(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/getQuery")
    public ResponseEntity<ApiResponse<List<RoQueryGetResponse>>> getQuery(@RequestBody RoQueryGetRequest getQueryRequest) {
        ApiResponse<List<RoQueryGetResponse>> response = roQueryReadFacade.getQuery(getQueryRequest);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
