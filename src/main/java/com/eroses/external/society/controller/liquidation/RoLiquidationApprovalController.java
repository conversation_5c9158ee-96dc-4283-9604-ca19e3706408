package com.eroses.external.society.controller.liquidation;

import com.eroses.external.society.api.facade.societyLiquidation.RoLiquidationApprovalReadFacade;
import com.eroses.external.society.api.facade.societyLiquidation.RoLiquidationApprovalWriteFacade;
import com.eroses.external.society.dto.request.societyLiquidation.LiquidationAssignRORequest;
import com.eroses.external.society.dto.request.societyLiquidation.RoLiquidationApprovalCreateRequest;
import com.eroses.external.society.dto.response.societyLiquidation.LiquidationInternalResponse;
import com.eroses.external.society.dto.response.societyLiquidation.LiquidationPagingInternalResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/liquidate/approval")
public class RoLiquidationApprovalController {
    private final RoLiquidationApprovalWriteFacade writeFacade;
    private final RoLiquidationApprovalReadFacade roLiquidationApprovalReadFacade;

    @PostMapping("/create")
    ResponseEntity<ApiResponse<Long>> create(@RequestBody RoLiquidationApprovalCreateRequest request) throws Exception {
        ApiResponse<Long> response = writeFacade.create(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

//    @PutMapping("/ro")
//    ResponseEntity<ApiResponse<String>> assignRO(@RequestBody LiquidationAssignRORequest request) {
//        ApiResponse<String> response = writeFacade.assignRO(request);
//        return ResponseUtil.buildResponse(response, HttpStatus.OK);
//    }

    @GetMapping("/{liquidationId}")
    ResponseEntity<ApiResponse<LiquidationInternalResponse>> findLiquidation(@PathVariable Long liquidationId) {
        ApiResponse<LiquidationInternalResponse> response = roLiquidationApprovalReadFacade.findLiquidation(liquidationId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

//    @GetMapping("/paging")
//    ResponseEntity<ApiResponse<Paging<LiquidationPagingInternalResponse>>> getPaging(
//            @RequestParam(value = "pageNo", defaultValue = "1", required = false) int pageNo,
//            @RequestParam(value = "pageSize", defaultValue = "10", required = false) int pageSize,
//            @RequestParam(value = "categoryCode", required = false) String categoryCode,
//            @RequestParam(value = "subCategoryCode", required = false) String subCategoryCode,
//            @RequestParam(value = "societyName", required = false) String societyName,
//            @RequestParam(value = "branchLiquidation", required = false) Integer branchLiquidation
//    ) {
//        ApiResponse<Paging<LiquidationPagingInternalResponse>> response = roLiquidationApprovalReadFacade.getPaging(
//            pageNo,
//            pageSize,
//            categoryCode,
//            subCategoryCode,
//            societyName,
//            branchLiquidation
//        );
//        return ResponseUtil.buildResponse(response, HttpStatus.OK);
//    }
}
