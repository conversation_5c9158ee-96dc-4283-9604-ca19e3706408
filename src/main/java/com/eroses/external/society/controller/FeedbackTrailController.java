package com.eroses.external.society.controller;

import com.eroses.external.society.api.converter.input.FeedbackApiInputConverter;
import com.eroses.external.society.api.facade.FeedbackTrailReadFacade;
import com.eroses.external.society.api.facade.FeedbackTrailWriteFacade;
import com.eroses.external.society.dto.request.feedback.FeedbackTrailCreateRequest;
import com.eroses.external.society.dto.response.feedback.FeedbackTrailGetResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.FeedbackTrail;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/society/feedbacktrail")
public class FeedbackTrailController {
    private final FeedbackTrailWriteFacade feedbackTrailWriteFacade;
    private final FeedbackTrailReadFacade feedbackTrailReadFacade;
    private final FeedbackApiInputConverter feedbackApiInputConverter;

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Long>> createFeedbackTrail(@Valid @RequestBody FeedbackTrailCreateRequest feedbackTrailCreateRequest) {
        FeedbackTrail feedbackTrail = feedbackApiInputConverter.convertToModel(feedbackTrailCreateRequest);
        ApiResponse<Long> feedbackTrailId = feedbackTrailWriteFacade.create(feedbackTrail, feedbackTrailCreateRequest.getFeedbackType());
        return ResponseUtil.buildResponse(feedbackTrailId, HttpStatus.OK);
    }

    @PutMapping("/update")
    public ResponseEntity<ApiResponse<Long>> updateFeedbackTrail(@RequestBody FeedbackTrail feedbackTrail) {
        ApiResponse<Long> id = feedbackTrailWriteFacade.update(feedbackTrail);
        return ResponseUtil.buildResponse(id, HttpStatus.OK);
    }

    @GetMapping(value = "/{id}")
    public ResponseEntity<ApiResponse<FeedbackTrail>> getById(@PathVariable Long id) {
        ApiResponse<FeedbackTrail> response = feedbackTrailReadFacade.getFeedbackTrailById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping("/getAll")
    public ResponseEntity<ApiResponse<List<FeedbackTrailGetResponse>>> getAll(
            @RequestParam(value = "feedbackId", required = true) Long feedbackId) {

        ApiResponse<List<FeedbackTrailGetResponse>> feedback = feedbackTrailReadFacade.getAll(feedbackId);
        return ResponseUtil.buildResponse(feedback, HttpStatus.OK);
    }
}

