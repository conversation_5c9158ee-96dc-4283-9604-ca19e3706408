package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.grant.GrantTemplateReadFacade;
import com.eroses.external.society.api.facade.grant.GrantTemplateWriteFacade;
import com.eroses.external.society.dto.request.grant.GrantTemplateCreateRequest;
import com.eroses.external.society.dto.request.grant.GrantTemplatePublishRequest;
import com.eroses.external.society.dto.request.grant.GrantTemplateUpdateRequest;
import com.eroses.external.society.dto.response.grant.GrantTemplateDetailResponse;
import com.eroses.external.society.dto.response.grant.GrantTemplateResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.ResponseUtil;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/grant/template")
public class GrantTemplateController {
    
    private final GrantTemplateReadFacade grantTemplateReadFacade;
    private final GrantTemplateWriteFacade grantTemplateWriteFacade;
    
    @GetMapping
    public ResponseEntity<ApiResponse<Paging<GrantTemplateResponse>>> findAll(
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        ApiResponse<Paging<GrantTemplateResponse>> response = grantTemplateReadFacade.findAll(title, status, page, size);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @GetMapping("/drafts")
    public ResponseEntity<ApiResponse<List<GrantTemplateResponse>>> findAllDrafts() {
        ApiResponse<List<GrantTemplateResponse>> response = grantTemplateReadFacade.findAllDrafts();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @GetMapping("/published")
    public ResponseEntity<ApiResponse<List<GrantTemplateResponse>>> findAllPublished() {
        ApiResponse<List<GrantTemplateResponse>> response = grantTemplateReadFacade.findAllPublished();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<GrantTemplateDetailResponse>> findById(@PathVariable Long id) {
        ApiResponse<GrantTemplateDetailResponse> response = grantTemplateReadFacade.findById(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @GetMapping("/society/{societyId}")
    public ResponseEntity<ApiResponse<List<GrantTemplateResponse>>> findAvailableForSociety(@PathVariable Long societyId) {
        ApiResponse<List<GrantTemplateResponse>> response = grantTemplateReadFacade.findAvailableForSociety(societyId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Long>> createGrantTemplate(
            @Valid @RequestBody GrantTemplateCreateRequest request,
            @AuthenticationPrincipal User user) {
        
        ApiResponse<Long> response = grantTemplateWriteFacade.createGrantTemplate(request, user.getId());
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }
    
    @PutMapping("/update")
    public ResponseEntity<ApiResponse<Boolean>> updateGrantTemplate(
            @Valid @RequestBody GrantTemplateUpdateRequest request,
            @AuthenticationPrincipal User user) {
        
        ApiResponse<Boolean> response = grantTemplateWriteFacade.updateGrantTemplate(request, user.getId());
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @PutMapping("/publish")
    public ResponseEntity<ApiResponse<Boolean>> publishGrantTemplate(
            @Valid @RequestBody GrantTemplatePublishRequest request,
            @AuthenticationPrincipal User user) {
        
        ApiResponse<Boolean> response = grantTemplateWriteFacade.publishGrantTemplate(request, user.getId());
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Boolean>> deleteGrantTemplate(@PathVariable Long id) {
        ApiResponse<Boolean> response = grantTemplateWriteFacade.deleteGrantTemplate(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
