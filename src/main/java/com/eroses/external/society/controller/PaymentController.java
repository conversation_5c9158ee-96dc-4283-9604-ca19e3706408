//package com.eroses.external.society.controller;
//
//import com.eroses.external.society.api.facade.PaymentRecordFacade;
//import com.eroses.external.society.dto.request.payment.GetPaymentListRequest;
//import com.eroses.external.society.dto.request.payment.MakePaymentRequest;
//import com.eroses.external.society.dto.request.payment.UpdatePaymentRequest;
//import com.eroses.external.society.model.ApiResponse;
//import com.eroses.external.society.model.Paging;
//import com.eroses.external.society.model.payment.PaymentRecord;
//import jakarta.validation.Valid;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//
//@Slf4j
//@RestController
//@RequiredArgsConstructor
//@RequestMapping("/society/payment")
//public class PaymentController {
//    private final PaymentRecordFacade paymentRecordFacade;
//
//    @PostMapping("/getPaymentList")
//    public ResponseEntity<ApiResponse<Paging<PaymentRecord>>> getPaymentList(@RequestBody @Valid GetPaymentListRequest request) {
//        return ResponseEntity.ok(paymentRecordFacade.getPaymentList(request));
//    }
//
//    @PutMapping("/updatePayment")
//    public ResponseEntity<ApiResponse> updatePayment(@RequestBody @Valid UpdatePaymentRequest updatePaymentRequest) throws Exception {
//        return ResponseEntity.ok(paymentRecordFacade.updatePayment(updatePaymentRequest));
//    }
//
//    @PostMapping("/makePayment")
//    public ResponseEntity<Object> makePayment(@RequestBody MakePaymentRequest makePaymentRequest) throws Exception {
//        log.info("Make payment request: {}", makePaymentRequest);
//        return ResponseEntity.ok(paymentRecordFacade.makePayment(makePaymentRequest));
//    }
//
//    @PostMapping("/callback")
//    public ResponseEntity<Object> paymentCallBack(@RequestParam("response") String response) {
//        log.info("Payment callback request: {}", response);
//        try{
//            return ResponseEntity.ok(paymentRecordFacade.paymentCallback(response));
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }
//
//    @GetMapping("/paymentCallback")
//    public ResponseEntity<Object> getPaymentCallBack() {
//        return ResponseEntity.ok(paymentRecordFacade.getPaymentCallback());
//    }
//
//    @GetMapping("/getBankList")
//    public ResponseEntity<Object> getBankList() {
//        return ResponseEntity.ok(paymentRecordFacade.getBankList());
//    }
//}
