package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.RoApprovalReadFacade;
import com.eroses.external.society.api.facade.RoApprovalWriteFacade;
import com.eroses.external.society.dto.request.roApproval.RoApprovalCreateRequest;
import com.eroses.external.society.dto.request.roApproval.RoApprovalEditRequest;
import com.eroses.external.society.dto.request.roApproval.RoApprovalSocietySecretaryCreateRequest;
import com.eroses.external.society.dto.request.roDecision.RoApprovalGetRequest;
import com.eroses.external.society.dto.response.roApproval.RoApprovalCreateResponse;
import com.eroses.external.society.dto.response.roApproval.RoApprovalEditResponse;
import com.eroses.external.society.dto.response.roApproval.RoApprovalGetResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.utils.ResponseUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/roApproval")
public class RoApprovalController {
    private final RoApprovalWriteFacade roApprovalWriteFacade;
    private final RoApprovalReadFacade roApprovalReadFacade;

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<RoApprovalCreateResponse>> createRoApproval(@Valid @RequestBody RoApprovalCreateRequest request) throws Exception {
        ApiResponse<RoApprovalCreateResponse> response = roApprovalWriteFacade.create(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @Deprecated
    @PostMapping(value = "/secretary/create")
    public ResponseEntity<ApiResponse<String>> createSocietySecretaryApproval(@Valid @RequestBody RoApprovalSocietySecretaryCreateRequest request) {
        ApiResponse<String> response = roApprovalWriteFacade.createSocietySecretaryApproval(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @PutMapping(value = "/{id}/edit")
    public ResponseEntity<ApiResponse<RoApprovalEditResponse>> editRoApproval(@PathVariable Long id, @Valid @RequestBody RoApprovalEditRequest request) throws Exception {
        ApiResponse<RoApprovalEditResponse> response = roApprovalWriteFacade.update(id, request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/getAll")
    public ResponseEntity<ApiResponse<List<RoApprovalGetResponse>>> getAllRoApproval(@RequestBody RoApprovalGetRequest request) throws Exception {
        ApiResponse<List<RoApprovalGetResponse>> response = roApprovalReadFacade.getAll(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
