package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.SocietyCommitteeArchiveReadFacade;
import com.eroses.external.society.api.facade.SocietyCommitteeArchiveWriteFacade;
import com.eroses.external.society.dto.request.SocietyCommitteeArchiveCreateRequest;
import com.eroses.external.society.dto.request.SocietyCommitteeArchiveEditRequest;
import com.eroses.external.society.dto.request.UpdateCommitteeRequest;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.model.SocietyCommitteeArchive;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/committeeArchive")
@CrossOrigin(origins = "http://localhost:5173")
public class SocietyCommitteeArchiveController {
    private final SocietyCommitteeArchiveReadFacade societyCommitteeArchiveReadFacade;
    private final SocietyCommitteeArchiveWriteFacade societyCommitteeArchiveWriteFacade;

    @GetMapping
    public ResponseEntity<ApiResponse<Object>> getCommitteeArchive(
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "identificationNo", required = false) String identificationNo,
            @RequestParam(value = "meetingId", required = false) Long meetingId,
            @RequestParam(value = "meetingDate", required = false) LocalDate meetingDate,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        if (societyId != null) param.put("societyId", societyId);
        if (name != null && !name.isEmpty()) param.put("name", name);
        if (identificationNo != null && !identificationNo.isEmpty()) param.put("identificationNo", identificationNo);
        if (meetingId != null) param.put("meetingId", meetingId);
        if (meetingDate != null) param.put("meetingDate", meetingDate);

        ApiResponse<Object> response = societyCommitteeArchiveReadFacade.getCommitteeArchive(param, pageNo, pageSize);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping
    public ResponseEntity<ApiResponse<Object>> createCommitteeArchive(SocietyCommitteeArchiveCreateRequest request) {
        ApiResponse<Object> response = societyCommitteeArchiveWriteFacade.create(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping
    public ResponseEntity<ApiResponse<Object>> updateCommitteeArchive(SocietyCommitteeArchiveEditRequest request) {
        ApiResponse<Object> response = societyCommitteeArchiveWriteFacade.update(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/archiveExist")
    public ResponseEntity<ApiResponse<Object>> checkIfArchiveExist(
            @RequestParam(value = "societyId") Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "meetingId") Long meetingId,
            @RequestParam(value = "meetingDate", required = false) LocalDate meetingDate) {

//        Map<String, Object> param = new HashMap<>();
//        param.put("societyId", societyId);
//        param.put("meetingId", meetingId);
//        if (branchId != null) param.put("branchId", branchId);
//        if (meetingDate != null) param.put("meetingDate", meetingDate);

        ApiResponse<Object> response = societyCommitteeArchiveReadFacade.checkIfArchiveExist(societyId, branchId, meetingId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/updateCommittee")
    public ResponseEntity<ApiResponse<Object>> updateCommittee(
            @RequestParam(value = "societyId") Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "meetingId") Long meetingId,
            @RequestParam(value = "meetingDate", required = false) LocalDate meetingDate) {
        //to update meeting date into current committee and activate archived committee
        ApiResponse<Object> response = societyCommitteeArchiveWriteFacade.updateCommittee(societyId, branchId, meetingId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
