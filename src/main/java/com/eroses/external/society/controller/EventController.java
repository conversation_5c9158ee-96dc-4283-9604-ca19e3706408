package com.eroses.external.society.controller;

import com.eroses.external.society.api.converter.input.EventAdminApiInputConverter;
import com.eroses.external.society.api.converter.input.EventApiInputConverter;
import com.eroses.external.society.api.converter.input.EventFeedbackApiInputConverter;
import com.eroses.external.society.api.converter.input.OrganiserApiInputConverter;
import com.eroses.external.society.api.facade.event.*;

import com.eroses.external.society.dto.request.event.*;
import com.eroses.external.society.dto.response.event.EventAttendeesCanceledDetailResponse;
import com.eroses.external.society.dto.response.event.EventAttendeesNameResponse;
import com.eroses.external.society.dto.response.event.EventBannerImageResponse;
import com.eroses.external.society.dto.response.event.EventResponse;
import com.eroses.external.society.mappers.EventCertificateDao;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.EventAdmin;
import com.eroses.external.society.service.S3DomainService;
import com.eroses.external.society.service.event.EventAttendeesReadDomainService;
import com.eroses.external.society.utils.HeadersUtil;
import com.eroses.external.society.utils.ResponseUtil;
import com.google.zxing.WriterException;
import lombok.RequiredArgsConstructor;
import com.eroses.external.society.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.http.HttpStatusCode;

import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/event")
public class EventController {
    private final EventAdminReadFacade eventAdminFacade;
    private final EventAdminApiInputConverter eventAdminApiInputConverter;
    private final EventApiInputConverter eventApiInputConverter;
    private final EventAdminWriteFacade eventAdminWriteFacade;
    private final EventWriteFacade eventWriteFacade;
    private final EventReadFacade eventReadFacade;
    private final OrganiserReadFacade organiserReadFacade;
    private final OrganiserWriteFacade organiserWriteFacade;
    private final EventOrganiserWriteFacade eventOrganiserWriteFacade;
    private final EventOrganiserReadFacade eventOrganiserReadFacade;
    private final OrganiserApiInputConverter organiserApiInputConverter;
    private final EventAttendeesWriteFacade eventAttendeesWriteFacade;
    private final EventAttendeesReadFacade eventAttendeesReadFacade;
    private final FeedbackQuestionReadFacade feedbackQuestionReadFacade;
    private final FeedbackQuestionWriteFacade feedbackQuestionWriteFacade;
    private final EventFeedbackQuestionReadFacade eventFeedbackQuestionReadFacade;
    private final EventFeedbackQuestionWriteFacade eventFeedbackQuestionWriteFacade;
    private final EventCertificateDao eventCertificateDao;
    private final EventCertificateReadFacade eventCertificateReadFacade;
    private final EventFeedbackWriteFacade eventFeedbackWriteFacade;
    private final EventFeedbackApiInputConverter eventFeedbackApiInputConverter;
    private final EventAttendeesReadDomainService eventAttendeesReadDomainService;
    private final EventFeedbackScaleReadFacade eventFeedbackScaleReadFacade;
    private final S3DomainService s3DomainService;
    private final EventFeedbackReadFacade eventFeedbackReadFacade;
//    private static final String SECRET_KEY = "1234567890123456";

    // get all existing  event


    @GetMapping(value = "/getAll/{year}")
    public ResponseEntity<ApiResponse<List<EventResponse>>> getAllEvent(@PathVariable Integer year) {
        ApiResponse<List<EventResponse>> response = eventReadFacade.getAll(year);
        HttpHeaders headers = HeadersUtil.createHeaders();
        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    @GetMapping(value = "/getPublished/{year}")
    public ResponseEntity<ApiResponse<List<EventResponse>>> getPublishedEvents(@PathVariable Integer year) {
        ApiResponse<List<EventResponse>> response = eventReadFacade.getAllPublished(year);
        HttpHeaders headers = HeadersUtil.createHeaders();
        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    @GetMapping(value = "/getPastEvents/{year}")
    public ResponseEntity<ApiResponse<List<EventResponse>>> getPastEvents(@PathVariable Integer year) {
        ApiResponse<List<EventResponse>> response = eventReadFacade.getPastEvents(year);
        HttpHeaders headers = HeadersUtil.createHeaders();
        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    @GetMapping(value = "/{eventNo}")
    public ResponseEntity<ApiResponse<EventResponse>> getEventByEventNo(@PathVariable String eventNo) {
        ApiResponse<EventResponse> response = eventReadFacade.getOneByEventNo(eventNo);


        // Use the utility method to add headers
        HttpHeaders headers = HeadersUtil.createHeaders();

        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<Object>> createEvent(@RequestBody EventCreateRequest eventCreateRequest) {
        Event event = eventApiInputConverter.convertToModel(eventCreateRequest);
        List<Long> orgs = null;
        List<Long> societies = null;

        if (eventCreateRequest.getSocietiesId() != null && !eventCreateRequest.getSocietiesId().isEmpty())
            societies = eventCreateRequest.getSocietiesId();

        ApiResponse<Object> response = eventWriteFacade.createEvent(event, orgs, societies);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }


    @PostMapping(value = "/update/{eventNo}")
    public ResponseEntity<ApiResponse<Object>> updateEvent(@PathVariable String eventNo,
                                                           @RequestBody EventCreateRequest eventCreateRequest) {
        Event event = eventApiInputConverter.convertToModel(eventCreateRequest);
        ApiResponse<Object> response = eventWriteFacade.updateEvent(eventNo, event,  eventCreateRequest.getSocietiesId());
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);

    }

    @PostMapping(value = "/delete/{eventNo}")
    public ResponseEntity<ApiResponse<Object>> deleteEvent(@PathVariable String eventNo) {
        ApiResponse<Object> response = eventWriteFacade.deleteEvent(eventNo);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @PostMapping(value = "/delete-feedback-question/{eventNo}")
    public ResponseEntity<ApiResponse<Object>> deleteEventFeedback(@PathVariable String eventNo) {
        ApiResponse<Object> response = eventFeedbackQuestionWriteFacade.deleteEventFeedbackQuestion(eventNo);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);

    }


    @PostMapping("/banner-upload")
    public ResponseEntity<ApiResponse<EventBannerImageResponse>> uploadBanner(@RequestParam("file") MultipartFile file) {
        ApiResponse<EventBannerImageResponse> response = eventWriteFacade.uploadBanner(file);

        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @PostMapping(value = "/publish/{eventNo}")
    public ResponseEntity<ApiResponse<Object>> publishEvent(@PathVariable String eventNo) {
        ApiResponse<Object> response = eventWriteFacade.publishEvent(eventNo);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/admin/list")
    public ResponseEntity<ApiResponse<List<EventAdmin>>> getAllEventAdmin() {
        ApiResponse<List<EventAdmin>> response = eventAdminFacade.getAll();

        // Use the utility method to add headers
        HttpHeaders headers = HeadersUtil.createHeaders();

        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    // get event admin by identification no
    @GetMapping(value = "/admin/{identificationNo}")
    public ResponseEntity<ApiResponse<EventAdmin>> getEventAdmin(@PathVariable String identificationNo) {
        ApiResponse<EventAdmin> response = eventAdminFacade.getAdminByIdentificationNo(identificationNo);

        // Use the utility method to add headers
        HttpHeaders headers = HeadersUtil.createHeaders();

        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    @PostMapping(value = "/admin/create")
    public ResponseEntity<ApiResponse<Object>> createEventAdmin(@RequestBody EventAdminCreateRequest eventAdminCreateRequest) {
        EventAdmin eventAdmin = eventAdminApiInputConverter.convertToModel(eventAdminCreateRequest);
        ApiResponse<Object> response = eventAdminWriteFacade.createEventAdmin(eventAdmin);

        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @PostMapping(value = "/admin/update/{identificationNo}")
    public ResponseEntity<ApiResponse<Object>> updateEventAdmin(@PathVariable String identificationNo,
                                                                @RequestBody EventAdmin eventAdmin) {
//        EventAdmin eventAdmin = eventAdminApiInputConverter.convertToModel(eventAdminCreateRequest);
        ApiResponse<Object> response = eventAdminWriteFacade.updateEventAdmin(identificationNo, eventAdmin);

        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    @GetMapping(value = "/organiser/getAll")
    public ResponseEntity<ApiResponse<List<Organiser>>> getAllOrganiser() {
        ApiResponse<List<Organiser>> response = organiserReadFacade.getAll();

        // Use the utility method to add headers
        HttpHeaders headers = HeadersUtil.createHeaders();

        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }


    //ORGANISER
    @GetMapping(value = "/organiser/{identificationNo}")
    public ResponseEntity<ApiResponse<Organiser>> getOrganiserByIdentificationNo(@PathVariable String identificationNo) {
        ApiResponse<Organiser> response = organiserReadFacade.getOneByIdentificationNo(identificationNo);

        // Use the utility method to add headers
        HttpHeaders headers = HeadersUtil.createHeaders();

        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    @GetMapping(value = "/organiser/id/{organiserId}")
    public ResponseEntity<ApiResponse<Organiser>> getOrganiserById(@PathVariable Long organiserId) {
        ApiResponse<Organiser> response = organiserReadFacade.getOneById(organiserId);
        HttpHeaders headers = HeadersUtil.createHeaders();
        return new ResponseEntity<>(response, headers, HttpStatus.OK);//TEST THIS API
    }

    @PostMapping(value = "/organiser/create")
    public ResponseEntity<ApiResponse<Object>> createOrganiser(@RequestBody OrganiserCreateRequest organiserCreateRequest) {
        Organiser organiser = organiserApiInputConverter.convertToModel(organiserCreateRequest);
        ApiResponse<Object> response = organiserWriteFacade.createOrganiser(organiser);
//        if(response.getCode())
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @PostMapping(value = "/organiser/update/{organiserId}")
    public ResponseEntity<ApiResponse<Object>> updateOrganiser(@PathVariable Long organiserId, @RequestBody OrganiserCreateRequest organiserCreateRequest) {
        Organiser organiser = organiserApiInputConverter.convertToModel(organiserCreateRequest);
        ApiResponse<Object> response = organiserWriteFacade.updateOrganiser(organiserId, organiser);
//        if(response.getCode())
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/event-organiser/by-event/{eventNo}")
    public ResponseEntity<ApiResponse<List<EventOrganiser>>> createEventOrganiser(@PathVariable String eventNo) {
        ApiResponse<List<EventOrganiser>> response = eventOrganiserReadFacade.getAllByEventNo(eventNo);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/attendees/getAll")
    public ResponseEntity<ApiResponse<List<EventAttendees>>> getAllAttendees() {
        ApiResponse<List<EventAttendees>> response = eventAttendeesReadFacade.getAll();
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/attendees/by-identification-no/{identificationNo}")
    public ResponseEntity<ApiResponse<List<EventAttendees>>> getAttendeesByIdentificationNo(@PathVariable String identificationNo) {
        ApiResponse<List<EventAttendees>> response = eventAttendeesReadFacade.getByIdentificationNo(identificationNo);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/attendees/by-identification-no/{identificationNo}/event/{eventNo}")
    public ResponseEntity<ApiResponse<EventAttendees>> getAttendeesByIdentificationNoAndEventNo(
            @PathVariable String identificationNo,
            @PathVariable String eventNo) {
        ApiResponse<EventAttendees> response = eventAttendeesReadFacade.getByIdentificationNoAndEventNo(identificationNo, eventNo);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/attendees/by-event-no/{eventNo}")
    public ResponseEntity<ApiResponse<List<EventAttendees>>> getAttendeesByEventNo(@PathVariable String eventNo) {
        ApiResponse<List<EventAttendees>> response = eventAttendeesReadFacade.getByEventNo(eventNo);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/attendees-list/by-event-no/{eventNo}")
    public ResponseEntity<ApiResponse<List<EventAttendeesNameResponse>>> getDetailAttendeesByEventNo(@PathVariable String eventNo) {
        ApiResponse<List<EventAttendeesNameResponse>> response = eventAttendeesReadFacade.getListByEventNo(eventNo);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/attendees-canceled-list/by-event-no/{eventNo}")
    public ResponseEntity<ApiResponse<List<EventAttendeesCanceledDetailResponse>>> getDetailCanceledAttendeesByEventNo(@PathVariable String eventNo) {
        ApiResponse<List<EventAttendeesCanceledDetailResponse>> response = eventAttendeesReadFacade.getCancelledListByEventNo(eventNo);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/attendees/by-attendance-no/{attendanceNo}")
    public ResponseEntity<ApiResponse<EventAttendees>> getAttendeesByAttendanceNo(@PathVariable String attendanceNo) {
        ApiResponse<EventAttendees> response = eventAttendeesReadFacade.getByAttendanceNo(attendanceNo);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/attendees/by-attendance-id/{attendeesId}")
    public ResponseEntity<ApiResponse<EventAttendees>> getAttendeesByAttendanceNo(@PathVariable Long attendanceId) {
        ApiResponse<EventAttendees> response = eventAttendeesReadFacade.getById(attendanceId);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @PostMapping(value = "/attendees/create")
    public ResponseEntity<ApiResponse<Object>> createEventAttendees(@RequestBody EventAttendeesCreateRequest attendeesCreateRequest) {
        ApiResponse<Object> response = eventAttendeesWriteFacade.createEventAttendees(attendeesCreateRequest);
//        if(response.getCode())
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @PostMapping(value = "/attendees/cancel")
    public ResponseEntity<ApiResponse<Object>> removeEventAttendees(@RequestBody EventAttendeesDeleteRequest attendeesDeleteRequest) {
        ApiResponse<Object> response = eventAttendeesWriteFacade.removeEventAttendees(attendeesDeleteRequest);
//        if(response.getCode())
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }



    @PostMapping(value = "/attendees/update-status/{eventAttendeesId}")
    public ResponseEntity<ApiResponse<Object>> updateStatusAttendees(@PathVariable Long eventAttendeesId, @RequestBody EventAttendeesUpdateStatusRequest attendeesStatusCreateRequest) {
        ApiResponse<Object> response = eventAttendeesWriteFacade.updateAttendeesStatus(eventAttendeesId, attendeesStatusCreateRequest);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @PostMapping(value = "/attendees/update-present/{eventAttendeesId}")
    public ResponseEntity<ApiResponse<Object>> updatePresentAttendees(@PathVariable Long eventAttendeesId, @RequestBody EventAttendeesUpdatePresentRequest updatePresentRequest) {
        ApiResponse<Object> response = eventAttendeesWriteFacade.updateAttendeesPresent(eventAttendeesId, updatePresentRequest);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @PostMapping(value = "/attendees/update-present")
    public ResponseEntity<ApiResponse<Object>> updatePresentAttendeesByIdentificationNo(@RequestBody EventAttendeesUpdatePresentRequest updatePresentRequest) {
        ApiResponse<Object> response = eventAttendeesWriteFacade.updateAttendeesPresentByIdentificationNo(updatePresentRequest);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @PostMapping(value = "/attendees/update-present-by-credential/{eventNo}/{identificationNo}")
    public ResponseEntity<ApiResponse<Object>> updatePresentByCredential(@PathVariable Long eventAttendeesId ) {
        EventAttendeesUpdatePresentRequest updatePresentRequest = new EventAttendeesUpdatePresentRequest();
        updatePresentRequest.setPresent(true);
        ApiResponse<Object> response = eventAttendeesWriteFacade.updateAttendeesPresent(eventAttendeesId, updatePresentRequest);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/attendees-submitted-feedback/by-event-no/{eventNo}")
    public ResponseEntity<ApiResponse<List<EventAttendees>>> getAllAttendeesSubmitFeedbackByEventNo(@PathVariable String eventNo) {
        ApiResponse<List<EventAttendees>> response = eventAttendeesReadFacade.getSubmittedFeedbackByEventNo(eventNo);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }


    @GetMapping(value = "/get-feedback-answer/by-attendance-no/{attendanceNo}")
    public ResponseEntity<ApiResponse<List<EventFeedback>>> getAllFeedbackQuestion(@PathVariable String attendanceNo) {
        ApiResponse<List<EventFeedback>> response = eventFeedbackReadFacade.getEventFeedbackByAttendanceNo(attendanceNo);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/feedback-question/getAll")
    public ResponseEntity<ApiResponse<List<FeedbackQuestion>>> getAllFeedbackQuestion() {
        ApiResponse<List<FeedbackQuestion>> response = feedbackQuestionReadFacade.getAll();
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/feedback-question/{eventFeedbackQuestionId}")
    public ResponseEntity<ApiResponse<FeedbackQuestion>> getFeedbackQuestionById(@PathVariable Long eventFeedbackQuestionId) {
        ApiResponse<FeedbackQuestion> response = feedbackQuestionReadFacade.getOneById(eventFeedbackQuestionId);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @PostMapping(value = "/feedback-question-selected")
    public ResponseEntity<ApiResponse<List<FeedbackQuestion>>> getFeedbackQuestionByMultipleIds(@RequestBody FeedbackQuestionIdsRequest requestIds) {
        ApiResponse<List<FeedbackQuestion>> response = feedbackQuestionReadFacade.getMultipleByIds(requestIds.getFeedbackQuestionIds());
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/get-feedback-question/{eventNo}")
    public ResponseEntity<ApiResponse<List<FeedbackQuestionResponse>>> getFeedbackQuestionByEventNo(@PathVariable String eventNo) {
        ApiResponse<List<FeedbackQuestionResponse>> response = feedbackQuestionReadFacade.getByEventNo(eventNo);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @PostMapping(value = "/feedback-question/create")
    public ResponseEntity<ApiResponse<Object>> createFeedbackQuestion(@RequestBody FeedbackQuestion feedbackQuestion) {
        ApiResponse<Object> response = feedbackQuestionWriteFacade.createQuestion(feedbackQuestion);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @PostMapping(value = "/feedback-question/update/{eventFeedbackQuestionId}")
    public ResponseEntity<ApiResponse<Object>> updateFeedbackQuestion(@PathVariable Long eventFeedbackQuestionId, @RequestBody FeedbackQuestion feedbackQuestion) {
        ApiResponse<Object> response = feedbackQuestionWriteFacade.updateQuestion(eventFeedbackQuestionId, feedbackQuestion);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @PostMapping(value = "/event-feedback-question/create/{eventNo}")
    public ResponseEntity<ApiResponse<Object>> createEventFeedbackQuestion(@PathVariable String eventNo, @RequestBody EventFeedbackQuestionCreateRequest eventFeedbackQuestionCreateRequest) {
        ApiResponse<Object> response = eventFeedbackQuestionWriteFacade.createEventFeedbackQuestion(eventNo, eventFeedbackQuestionCreateRequest);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @PostMapping(value = "/event-feedback/create/{attendanceNo}")
    public ResponseEntity<ApiResponse<Object>> createEventFeedback(@PathVariable String attendanceNo, @RequestBody List<EventFeedbackCreateRequest> eventFeedbackCreateRequest) {
        List<EventFeedback> feedbacks =  eventFeedbackApiInputConverter.convertToModels( eventFeedbackCreateRequest);
        ApiResponse<Object> response = eventFeedbackWriteFacade.createEventFeedback(attendanceNo, eventFeedbackCreateRequest);
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping(value = "/event-cert/{eventId}/{identificationNo}")
    public ResponseEntity<ApiResponse<FeedbackQuestion>> getEventCert(@PathVariable Long eventFeedbackQuestionId) {
        ApiResponse<FeedbackQuestion> response = feedbackQuestionReadFacade.getOneById(eventFeedbackQuestionId);
        String templatePath = "C:/templates/myTemplate.doc";

        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }

    @GetMapping("/generate/{templateCode}/{eventNo}/{identificationNo}")
    public ResponseEntity<ApiResponse<String>> generateEventCertificate(@PathVariable String eventNo, @PathVariable String identificationNo,@PathVariable String templateCode) throws Exception {
        try {
//            ApiResponse<byte[]> pdfByteResponse = eventCertificateReadFacade.generateSijil(eventNo, identificationNo, templateCode );
//            if (pdfByteResponse.getCode() != 200) {
//                return ResponseEntity.status(pdfByteResponse.getCode())
//                        .body(("Error generating certificate: " + pdfByteResponse.getMsg()).getBytes());
//            }
            ApiResponse<String> response = eventCertificateReadFacade.generateSijil(eventNo, identificationNo, templateCode);

            return ResponseUtil.buildResponse(response, HttpStatus.OK);


        } catch (Exception e) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body(("Error generating certificate: " + e.getMessage()).getBytes());
            return ResponseUtil.buildResponse(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @GetMapping(value = "/generate-attendance-qr/{attendanceNo}", produces = MediaType.IMAGE_PNG_VALUE)
    public ResponseEntity<ByteArrayResource> generateQrAttendance(@PathVariable String attendanceNo) throws WriterException, IOException {
        ApiResponse<ByteArrayResource> response = eventAttendeesReadFacade.getQrByAttendanceNo(attendanceNo);

        if (response.getCode() != 200 || response.getData() == null) {
            return ResponseEntity.status(response.getCode())
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(null);
        }

        return ResponseEntity.ok()
                .contentType(MediaType.IMAGE_PNG)
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"attendees-qr.png\"")
                .body(response.getData());
    }

    @GetMapping(value = "/generate-event-attendees-qr/{eventNo}", produces = MediaType.IMAGE_PNG_VALUE)
    public ResponseEntity<ByteArrayResource> generateEventAttendeesQR(@PathVariable String eventNo) throws WriterException, IOException {
        ApiResponse<ByteArrayResource> response = eventReadFacade.getQrEndPointUpdatePresent(eventNo);

        if (response.getCode() != 200 || response.getData() == null) {
            return ResponseEntity.status(response.getCode())
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(null);
        }

        return ResponseEntity.ok()
                .contentType(MediaType.IMAGE_PNG)
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"attendees-qr.png\"")
                .body(response.getData());
    }

    @PostMapping("/qr-update-present")
    public ResponseEntity<ApiResponse<Object>> updatePresentAttendeeWithQr(@RequestBody EventAttendeesUpdatePresentQrRequest updatePresentRequest ) {
        try {
            // Step 1: Decrypt the ID
            ApiResponse<Object> response = eventAttendeesWriteFacade.updateAttendeesPresentByQr(updatePresentRequest);
            HttpStatus statusCode = validateHttpStatus(response.getCode());
            return ResponseUtil.buildResponse(response, statusCode);

        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse<>(e.getMessage(), 400));
        }
    }

    @GetMapping(value = "/feedback-scale/getAll")
    public ResponseEntity<ApiResponse<List<EventFeedbackScale>>> getAllFeedbackScale() {
        ApiResponse<List<EventFeedbackScale>> response = eventFeedbackScaleReadFacade.getAll();
        HttpStatus statusCode = validateHttpStatus(response.getCode());
        return ResponseUtil.buildResponse(response, statusCode);
    }



    private HttpStatus validateHttpStatus(int code) {
        // 2xx Success
        if (code == HttpStatusCode.OK)
            return HttpStatus.OK;
        if (code == HttpStatusCode.CREATED)
            return HttpStatus.CREATED;
        if (code == HttpStatusCode.ACCEPTED)
            return HttpStatus.ACCEPTED;
        if (code == HttpStatusCode.NO_CONTENT)
            return HttpStatus.NO_CONTENT;

        // 3xx Redirection
        if (code == HttpStatusCode.MOVED_PERMANENTLY)
            return HttpStatus.MOVED_PERMANENTLY;
        if (code == HttpStatusCode.TEMPORARY_REDIRECT)
            return HttpStatus.TEMPORARY_REDIRECT;


        // 4xx Client Errors
        if (code == HttpStatusCode.BAD_REQUEST)
            return HttpStatus.BAD_REQUEST;
        if (code == HttpStatusCode.UNAUTHORIZED)
            return HttpStatus.UNAUTHORIZED;
        if (code == HttpStatusCode.FORBIDDEN)
            return HttpStatus.FORBIDDEN;
        if (code == HttpStatusCode.NOT_FOUND)
            return HttpStatus.NOT_FOUND;
        if (code == HttpStatusCode.METHOD_NOT_ALLOWED)
            return HttpStatus.METHOD_NOT_ALLOWED;
        if (code == HttpStatusCode.NOT_ACCEPTABLE)
            return HttpStatus.NOT_ACCEPTABLE;


        // 5xx Server Errors
        if (code == HttpStatusCode.INTERNAL_SERVER_ERROR)
            return HttpStatus.INTERNAL_SERVER_ERROR;
        if (code == HttpStatusCode.BAD_GATEWAY)
            return HttpStatus.BAD_GATEWAY;
        if (code == HttpStatusCode.SERVICE_UNAVAILABLE)
            return HttpStatus.SERVICE_UNAVAILABLE;
        if (code == HttpStatusCode.GATEWAY_TIMEOUT)
            return HttpStatus.GATEWAY_TIMEOUT;

        // Default response if no matching status code is found
        return HttpStatus.OK;
    }

}
