package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.BlacklistUserReadFacade;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.blacklist.BlacklistUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/blacklist-user")
@Api(tags = "Blacklist User API")
public class BlacklistUserController {
    private final BlacklistUserReadFacade blacklistUserReadFacade;

    @GetMapping("/{id}")
    @ApiOperation("Find blacklist user by ID")
    public ApiResponse<BlacklistUser> findById(@PathVariable Long id) {
        return blacklistUserReadFacade.findById(id);
    }

    @GetMapping("/identification-no/{identificationNo}")
    @ApiOperation("Find blacklist user by identification number")
    public ApiResponse<BlacklistUser> findByIdentificationNo(@PathVariable String identificationNo) {
        return blacklistUserReadFacade.findByIdentificationNo(identificationNo);
    }

    @PostMapping("/search")
    @ApiOperation("Search blacklist users")
    public ApiResponse<List<BlacklistUser>> search(@RequestBody Map<String, Object> params) {
        return blacklistUserReadFacade.search(params);
    }
}