package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.posting.PostingReadFacade;
import com.eroses.external.society.api.facade.posting.PostingWriteFacade;
import com.eroses.external.society.dto.request.posting.PostingReviewRequest;
import com.eroses.external.society.dto.request.posting.PostingCreateRequest;
import com.eroses.external.society.dto.request.posting.PostingUpdateRequest;
import com.eroses.external.society.dto.response.posting.PostingReviewResponse;
import com.eroses.external.society.dto.response.posting.PostingEngagementStatsResponse;
import com.eroses.external.society.dto.response.posting.PostingReportResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/admin/posting")
public class AdminPostingController {

    private final PostingReadFacade postingReadFacade;
    private final PostingWriteFacade postingWriteFacade;

    /**
     * Create a new posting (Admin PRO)
     */
    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Long>> createPosting(
            @Valid @RequestBody PostingCreateRequest request) {
        ApiResponse<Long> response = postingWriteFacade.createPosting(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    /**
     * Update an existing posting (Admin PRO)
     */
    @PutMapping("/update")
    public ResponseEntity<ApiResponse<Boolean>> updatePosting(
            @Valid @RequestBody PostingUpdateRequest request) {
        ApiResponse<Boolean> response = postingWriteFacade.updatePosting(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    /**
     * Publish a posting (Admin PRO)
     */
    @PutMapping("/{id}/publish")
    public ResponseEntity<ApiResponse<Boolean>> publishPosting(
            @PathVariable Long id) {
        ApiResponse<Boolean> response = postingWriteFacade.publishPosting(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    /**
     * Unpublish a posting (Admin PRO)
     */
    @PutMapping("/{id}/unpublish")
    public ResponseEntity<ApiResponse<Boolean>> unpublishPosting(
            @PathVariable Long id) {
        ApiResponse<Boolean> response = postingWriteFacade.unpublishPosting(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    /**
     * Delete a posting (Admin PRO)
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Boolean>> deletePosting(
            @PathVariable Long id) {
        ApiResponse<Boolean> response = postingWriteFacade.deletePosting(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    /**
     * Get all reported postings (Admin PRO)
     */
    @GetMapping("/reports")
    public ResponseEntity<ApiResponse<Paging<PostingReportResponse>>> getReportedPostings(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        ApiResponse<Paging<PostingReportResponse>> response = postingReadFacade.getReportedPostings(page, size);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    /**
     * Get posting reviews for a posting (Admin PRO)
     */
    @GetMapping("/reviews/{postingId}")
    public ResponseEntity<ApiResponse<List<PostingReviewResponse>>> getPostingReviews(
            @PathVariable Long postingId) {
        ApiResponse<List<PostingReviewResponse>> response = postingReadFacade.getPostingReviews(postingId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    /**
     * Get engagement statistics (Admin PRO)
     */
    @GetMapping("/engagement/stats")
    public ResponseEntity<ApiResponse<PostingEngagementStatsResponse>> getEngagementStats(
            @RequestParam(required = false) String dateFrom,
            @RequestParam(required = false) String dateTo) {
        ApiResponse<PostingEngagementStatsResponse> response = postingReadFacade.getEngagementStats(dateFrom, dateTo);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    /**
     * Submit posting review (Committee members)
     */
    @PostMapping("/review")
    public ResponseEntity<ApiResponse<Long>> submitPostingReview(
            @Valid @RequestBody PostingReviewRequest request) {
        ApiResponse<Long> response = postingWriteFacade.submitPostingReview(request);
        return ResponseUtil.buildResponse(response, HttpStatus.CREATED);
    }

    /**
     * Mark a notification as read
     * TODO: This is a placeholder for future notification functionality
     */
    @PutMapping("/notification/{id}/read")
    public ResponseEntity<ApiResponse<Boolean>> markNotificationAsRead(
            @PathVariable Long id) {
        // TODO: Implement notification functionality
        ApiResponse<Boolean> response = postingWriteFacade.markNotificationAsRead(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    /**
     * Mark all notifications as read
     * TODO: This is a placeholder for future notification functionality
     */
    @PutMapping("/notification/read-all")
    public ResponseEntity<ApiResponse<Boolean>> markAllNotificationsAsRead() {
        // TODO: Implement notification functionality
        ApiResponse<Boolean> response = postingWriteFacade.markAllNotificationsAsRead();
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
