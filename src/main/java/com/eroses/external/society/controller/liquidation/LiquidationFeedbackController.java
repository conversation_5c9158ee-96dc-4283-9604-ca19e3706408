package com.eroses.external.society.controller.liquidation;

import com.eroses.external.society.api.facade.societyLiquidation.SocietyLiquidationReadFacade;
import com.eroses.external.society.api.facadeImp.societyLiquidation.LiquidationFeedbackWriteFacadeImpl;
import com.eroses.external.society.dto.request.societyLiquidation.LiquidationFeedbackCreateRequest;
import com.eroses.external.society.dto.request.societyLiquidation.LiquidationFeedbackUpdateRequest;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/liquidate/feedback")
public class LiquidationFeedbackController {
    public final LiquidationFeedbackWriteFacadeImpl writeFacade;
    private final SocietyLiquidationReadFacade societyLiquidationReadFacade;

    @PostMapping("/create")
    ResponseEntity<ApiResponse<Long>> create(@RequestBody LiquidationFeedbackCreateRequest request) {
        ApiResponse<Long> response = writeFacade.create(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping("/update")
    ResponseEntity<ApiResponse<Long>> update(@RequestBody LiquidationFeedbackUpdateRequest request) {
        ApiResponse<Long> response = writeFacade.update(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
