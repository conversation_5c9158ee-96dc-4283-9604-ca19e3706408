package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.CommitteeDraftReadFacade;
import com.eroses.external.society.api.facade.CommitteeDraftWriteFacade;
import com.eroses.external.society.api.facade.CommitteeReadFacade;
import com.eroses.external.society.api.facade.CommitteeWriteFacade;
import com.eroses.external.society.dto.request.CommitteeDraft.GetOrCreateByIdRequest;
import com.eroses.external.society.dto.request.CommitteeDraft.SubmitAjkRequest;
import com.eroses.external.society.dto.request.CommitteeDraftCreateRequest;
import com.eroses.external.society.dto.request.CommitteeDraftUpdateRequest;
import com.eroses.external.society.dto.response.committee.CommitteeListAjkResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.CommitteeDraft;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/committee/draft")
public class CommitteeDraftController {

    private final CommitteeDraftReadFacade committeeDraftReadFacade;
    private final CommitteeDraftWriteFacade committeeDraftWriteFacade;

    @GetMapping(value = "/list")
    public ResponseEntity<ApiResponse<Paging<CommitteeListAjkResponse>>> listDraftCommittees(
            @RequestParam(value = "societyId") Long societyId,
            @RequestParam(value = "branchId", required = false) Long branchId,
            @RequestParam(value = "pageNo", defaultValue = "0") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ApiResponse<Paging<CommitteeListAjkResponse>> response = committeeDraftReadFacade.listDraftCommittees(societyId, branchId, pageNo, pageSize);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/{id}/get")
    public ResponseEntity<ApiResponse<Object>> getDraftCommittee(@PathVariable Long id) {
        ApiResponse<Object> response = committeeDraftReadFacade.getDraftCommittee(id);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/create")
    public ResponseEntity<ApiResponse<Object>> createDraftCommittee(@RequestBody CommitteeDraftCreateRequest request) {
        ApiResponse<Object> response = committeeDraftWriteFacade.createDraftCommittee(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/update")
    public ResponseEntity<ApiResponse<Object>> updateDraftCommittee(@RequestBody CommitteeDraftUpdateRequest request) {
        ApiResponse<Object> response = committeeDraftWriteFacade.updateDraftCommittee(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PostMapping(value = "/getOrCreateByMeetingId")
    public ResponseEntity<ApiResponse<Paging<CommitteeDraft>>> getOrCreateByMeetingId(@RequestBody GetOrCreateByIdRequest request,
                                                                                      @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                                      @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        ApiResponse<Paging<CommitteeDraft>> response = committeeDraftWriteFacade.getOrCreateByMeetingId(request, pageNo, pageSize);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @PutMapping(value = "/submitAjk")
    public ResponseEntity<ApiResponse<Object>> submitAjk(@RequestBody SubmitAjkRequest request) {
        ApiResponse<Object> response = committeeDraftWriteFacade.submitAjk(request);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
