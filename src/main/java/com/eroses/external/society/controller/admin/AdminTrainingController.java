package com.eroses.external.society.controller.admin;

import com.eroses.external.society.api.facade.training.TrainingCourseReadFacade;
import com.eroses.external.society.api.facade.training.TrainingCourseWriteFacade;
import com.eroses.external.society.api.facade.training.TrainingMaterialReadFacade;
import com.eroses.external.society.api.facade.training.TrainingMaterialWriteFacade;
import com.eroses.external.society.api.facade.training.TrainingQuizReadFacade;
import com.eroses.external.society.api.facade.training.TrainingQuizWriteFacade;
import com.eroses.external.society.dto.request.training.QuizQuestionCreateRequest;
import com.eroses.external.society.dto.request.training.QuizQuestionUpdateRequest;
import com.eroses.external.society.dto.request.training.TrainingCourseCreateRequest;
import com.eroses.external.society.dto.request.training.TrainingCourseStatusUpdateRequest;
import com.eroses.external.society.dto.request.training.TrainingCourseUpdateRequest;
import com.eroses.external.society.dto.request.training.TrainingMaterialCreateRequest;
import com.eroses.external.society.dto.request.training.TrainingMaterialUpdateRequest;
import com.eroses.external.society.dto.request.training.TrainingQuizCreateRequest;
import com.eroses.external.society.dto.request.training.TrainingQuizUpdateRequest;
import com.eroses.external.society.dto.response.training.TrainingCourseResponse;
import com.eroses.external.society.dto.response.training.TrainingCourseDetailResponse;
import com.eroses.external.society.dto.response.training.TrainingMaterialResponse;
import com.eroses.external.society.dto.response.training.TrainingQuizResponse;
import com.eroses.external.society.dto.response.training.QuizQuestionResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.service.training.TrainingUserContextService;
import com.eroses.external.society.utils.ResponseUtil;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/society/admin/training")
@Api(value = "Admin Training Management API")
@RequiredArgsConstructor
public class AdminTrainingController {

    private final TrainingCourseReadFacade trainingCourseReadFacade;
    private final TrainingCourseWriteFacade trainingCourseWriteFacade;
    private final TrainingMaterialReadFacade trainingMaterialReadFacade;
    private final TrainingMaterialWriteFacade trainingMaterialWriteFacade;
    private final TrainingQuizReadFacade trainingQuizReadFacade;
    private final TrainingQuizWriteFacade trainingQuizWriteFacade;
    private final TrainingUserContextService userContextService;

    // Training Course Endpoints

    @ApiOperation(value = "Get all training courses")
    @GetMapping(value = "/courses", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<List<TrainingCourseResponse>> getAllTrainingCourses() {
        return trainingCourseReadFacade.getAllTrainingCourses();
    }

    @ApiOperation(value = "Get training course by ID")
    @GetMapping(value = "/courses/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<TrainingCourseResponse> getTrainingCourseById(@PathVariable Long id) {
        return trainingCourseReadFacade.getTrainingCourseById(id);
    }

    @ApiOperation(value = "Get training course details by ID")
    @GetMapping(value = "/courses/{id}/details", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<TrainingCourseDetailResponse> getTrainingCourseDetailsById(@PathVariable Long id, Authentication authentication) {
        Long userId = userContextService.getUserIdAsLong(authentication);
        return trainingCourseReadFacade.getTrainingCourseDetailsById(id, userId);
    }

    @ApiOperation(value = "Get all training courses by status")
    @GetMapping(value = "/courses/status/{status}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<List<TrainingCourseResponse>> getAllTrainingCoursesByStatus(@PathVariable Integer status) {
        return trainingCourseReadFacade.getAllTrainingCoursesByStatus(status);
    }

    @ApiOperation(value = "Create a new training course")
    @PostMapping(value = "/courses", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<Long> createTrainingCourse(@Valid @RequestBody TrainingCourseCreateRequest request, Authentication authentication) {
        // Get user ID as createdBy
        Long userId = userContextService.getUserIdAsLong(authentication);
        return trainingCourseWriteFacade.createTrainingCourse(request, userId);
    }

    @ApiOperation(value = "Update an existing training course")
    @PutMapping(value = "/courses", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<Long> updateTrainingCourse(@Valid @RequestBody TrainingCourseUpdateRequest request, Authentication authentication) {
        Long modifiedBy = userContextService.getUserIdAsLong(authentication);
        return trainingCourseWriteFacade.updateTrainingCourse(request, modifiedBy);
    }

    @ApiOperation(value = "Update training course status")
    @PutMapping(value = "/courses/{id}/status", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<Long> updateTrainingCourseStatus(@PathVariable Long id, @Valid @RequestBody TrainingCourseStatusUpdateRequest request, Authentication authentication) {
        Long modifiedBy = userContextService.getUserIdAsLong(authentication);
        return trainingCourseWriteFacade.updateTrainingCourseStatus(id, request, modifiedBy);
    }

    // Training Material Endpoints

    @ApiOperation(value = "Get all training materials for a course")
    @GetMapping(value = "/courses/{courseId}/materials", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<List<TrainingMaterialResponse>> getAllTrainingMaterialsByCourseId(@PathVariable Long courseId) {
        return trainingMaterialReadFacade.getAllTrainingMaterialsByCourseId(courseId);
    }

    @ApiOperation(value = "Get training material by ID")
    @GetMapping(value = "/materials/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<TrainingMaterialResponse> getTrainingMaterialById(@PathVariable Long id) {
        return trainingMaterialReadFacade.getTrainingMaterialById(id);
    }

    @ApiOperation(value = "Add material to a course")
    @PostMapping(value = "/materials", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<Long> createTrainingMaterial(@Valid @RequestBody TrainingMaterialCreateRequest request, Authentication authentication) {
        Long createdBy = userContextService.getUserIdAsLong(authentication);
        return trainingMaterialWriteFacade.createTrainingMaterial(request, createdBy);
    }

    @ApiOperation(value = "Update a material")
    @PutMapping(value = "/materials", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<Long> updateTrainingMaterial(@Valid @RequestBody TrainingMaterialUpdateRequest request, Authentication authentication) {
        Long modifiedBy = userContextService.getUserIdAsLong(authentication);
        return trainingMaterialWriteFacade.updateTrainingMaterial(request, modifiedBy);
    }

    @ApiOperation(value = "Remove a material")
    @DeleteMapping(value = "/materials/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<Void> deleteTrainingMaterial(@PathVariable Long id) {
        return trainingMaterialWriteFacade.deleteTrainingMaterial(id);
    }

    // Quiz Endpoints

    @ApiOperation(value = "Get quiz for a course")
    @GetMapping(value = "/courses/{courseId}/quiz", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<TrainingQuizResponse> getTrainingQuizByCourseId(@PathVariable Long courseId) {
        return trainingQuizReadFacade.getTrainingQuizByCourseId(courseId);
    }

    @ApiOperation(value = "Get quiz by ID")
    @GetMapping(value = "/quiz/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<TrainingQuizResponse> getTrainingQuizById(@PathVariable Long id) {
        return trainingQuizReadFacade.getTrainingQuizById(id);
    }

    @ApiOperation(value = "Create a quiz for a course")
    @PostMapping(value = "/quiz", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<Long> createTrainingQuiz(@Valid @RequestBody TrainingQuizCreateRequest request, Authentication authentication) {
        Long createdBy = userContextService.getUserIdAsLong(authentication);
        return trainingQuizWriteFacade.createTrainingQuiz(request, createdBy);
    }

    @ApiOperation(value = "Update a quiz")
    @PutMapping(value = "/quiz", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<Long> updateTrainingQuiz(@Valid @RequestBody TrainingQuizUpdateRequest request, Authentication authentication) {
        Long modifiedBy = userContextService.getUserIdAsLong(authentication);
        return trainingQuizWriteFacade.updateTrainingQuiz(request, modifiedBy);
    }

    @ApiOperation(value = "Add a question to a quiz")
    @PostMapping(value = "/quiz/questions", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<Long> createQuizQuestion(@Valid @RequestBody QuizQuestionCreateRequest request, Authentication authentication) {
        Long createdBy = userContextService.getUserIdAsLong(authentication);
        return trainingQuizWriteFacade.createQuizQuestion(request, createdBy);
    }

    @ApiOperation(value = "Update a question")
    @PutMapping(value = "/quiz/questions", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<Long> updateQuizQuestion(@Valid @RequestBody QuizQuestionUpdateRequest request, Authentication authentication) {
        Long modifiedBy = userContextService.getUserIdAsLong(authentication);
        return trainingQuizWriteFacade.updateQuizQuestion(request, modifiedBy);
    }

    @ApiOperation(value = "Remove a question")
    @DeleteMapping(value = "/quiz/questions/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<Void> deleteQuizQuestion(@PathVariable Long id) {
        return trainingQuizWriteFacade.deleteQuizQuestion(id);
    }

    @ApiOperation(value = "Get all questions for a quiz")
    @GetMapping(value = "/quiz/{quizId}/questions", produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResponse<List<QuizQuestionResponse>> getAllQuizQuestionsByQuizId(@PathVariable Long quizId) {
        return trainingQuizReadFacade.getAllQuizQuestionsByQuizId(quizId);
    }

    @ApiOperation(value = "Delete training course by ID")
    @DeleteMapping(value = "/courses/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse<Long>> deleteTrainingCourseById(@PathVariable Long id) {
        ApiResponse<Long> trainingCourseId = trainingCourseWriteFacade.delete(id);
        return ResponseUtil.buildResponse(trainingCourseId, HttpStatus.OK);
    }
}
