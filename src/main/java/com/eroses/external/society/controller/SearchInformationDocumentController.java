package com.eroses.external.society.controller;

import com.eroses.external.society.api.facade.SearchInformationDocumentReadFacade;
import com.eroses.external.society.dto.response.searchInformation.SearchInformationDocumentGetAllBySearchInformationIdResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/searchInformation/document")
public class SearchInformationDocumentController {
    private final SearchInformationDocumentReadFacade searchInformationDocumentReadFacade;

    @GetMapping(value = "/getAllBySearchInformationId")
    public ResponseEntity<ApiResponse<List<SearchInformationDocumentGetAllBySearchInformationIdResponse>>> getAllBySearchInformationId(
            @RequestParam(value = "searchInformationId", required = true) Long searchInformationId) {
        ApiResponse<List<SearchInformationDocumentGetAllBySearchInformationIdResponse>> response = searchInformationDocumentReadFacade.getAllBySearchInformationId(searchInformationId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getDocumentUrlById")
    public ResponseEntity<ApiResponse<String>> getDocumentUrlById(
            @RequestParam(value = "searchInformationDocumentId", required = true) Long searchInformationDocumentId) {
        //Is it save enough to pass the whole string (able to see on FE)
        ApiResponse<String> response = searchInformationDocumentReadFacade.getDocumentUrlById(searchInformationDocumentId);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
