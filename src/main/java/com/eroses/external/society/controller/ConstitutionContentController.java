package com.eroses.external.society.controller;

import com.eroses.external.society.api.converter.input.ConstitutionContentApiInputConverter;
import com.eroses.external.society.api.converter.input.ConstitutionValueApiInputConverter;
import com.eroses.external.society.api.facade.ConstitutionContentReadFacade;
import com.eroses.external.society.api.facade.ConstitutionContentWriteFacade;
import com.eroses.external.society.api.facade.ConstitutionValueWriteFacade;
import com.eroses.external.society.dto.request.*;
import com.eroses.external.society.dto.response.constitution.GetConstitutionAjkTypeResponse;
import com.eroses.external.society.model.*;
import com.eroses.external.society.utils.ResponseUtil;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/society/constitutioncontent")
public class ConstitutionContentController {

    private final ConstitutionContentApiInputConverter constitutionContentApiInputConverter;
    private final ConstitutionContentWriteFacade constitutionContentWriteFacade;
    private final ConstitutionContentReadFacade constitutionContentReadFacade;
    private final ConstitutionValueApiInputConverter constitutionValueApiInputConverter;
    private final ConstitutionValueWriteFacade constitutionValueWriteFacade;
    private final UserFacade userFacade;

    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Long>> createConstitutionContent(@Valid @RequestBody ConstitutionContentRegisterRequest constitutionContentRegisterRequest) {
        try {
            User currentUser = userFacade.me();
//            ConstitutionContent constitutioncontent = constitutionContentApiInputConverter.constitutioncontentRegisterRequest2ConstitutionContent(constitutionContentRegisterRequest);
//            ApiResponse<Long> constitutionContentId =  constitutionContentWriteFacade.create(constitutioncontent);
            ApiResponse<Long> constitutionContentId = constitutionContentWriteFacade.createConstitution(constitutionContentRegisterRequest);

            return ResponseUtil.buildResponse(constitutionContentId, HttpStatus.OK);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @PutMapping("/update")
    public ResponseEntity<ApiResponse<Long>> updateConstitutionContent(@Valid @RequestBody ConstitutionContentEditRequest constitutionContentEditRequest) {
//        ConstitutionContent constitutioncontent = constitutionContentApiInputConverter.constitutioncontentEditRequest2ConstitutionContent(constitutionContentEditRequest);
//        ApiResponse<Long> constitutionContentId = constitutionContentWriteFacade.update(constitutioncontent);
        ApiResponse<Long> constitutionContentId = constitutionContentWriteFacade.updateConstitution(constitutionContentEditRequest);

        return ResponseUtil.buildResponse(constitutionContentId, HttpStatus.OK);
    }

    @GetMapping(value = "/get")
    public ResponseEntity<ApiResponse<Paging<ConstitutionContent>>> getAllConstitutionContents(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "oldConstitutionContentId", required = false) Long oldConstitutionContentId,
            @RequestParam(value = "amendmentId", required = false) Long amendmentId,
            @RequestParam(value = "societyId", required = false) Long societyId,
            @RequestParam(value = "clauseContentId", required = false) Long clauseContentId,
            @RequestParam(value = "clauseNo", required = false) String clauseNo,
            @RequestParam(value = "clauseName", required = false) String clauseName,
            @RequestParam(value = "status", required = false) Long applicationStatusCode,
            @RequestParam(value = "oldConstitution", required = false) Boolean oldConstitution,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "30") Integer pageSize) {
        ConstitutionContentGetAllSocietiesRequest request = new ConstitutionContentGetAllSocietiesRequest();
        request.setId(id);
        request.setOldConstitutionContentId(oldConstitutionContentId);
        request.setAmendmentId(amendmentId);
        request.setSocietyId(societyId);
        request.setClauseContentId(clauseContentId);
        request.setClauseNo(clauseNo);
        request.setClauseName(clauseName);
        request.setApplicationStatusCode(applicationStatusCode);
        request.setOldConstitution(oldConstitution);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);

        ApiResponse<Paging<ConstitutionContent>> response = constitutionContentReadFacade.getAll(request);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/exportConstitutions")
    public ResponseEntity<byte[]> constitutionsDocumentExport(
            @RequestParam(value = "societyId") Long societyId,
            @RequestParam(value = "amendmentId", required = false) Long amendmentId,
            @RequestParam(value = "applicationStatusCode", required = false) Long applicationStatusCode) {
        Map<String, Object> response = constitutionContentReadFacade.constitutionsDocumentExport(societyId, amendmentId, applicationStatusCode);

        if ( response == null ) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        byte[] byteResponse = (byte[]) response.get("byte");
        HttpHeaders headers = (HttpHeaders) response.get("headers");

        return new ResponseEntity<>(byteResponse, headers, HttpStatus.OK);
    }

    @GetMapping(value = "/downloadConstitutions")
    public ResponseEntity<ApiResponse<Object>> constitutionsDocumentDownload(
            @RequestParam(value = "societyId") Long societyId,
            @RequestParam(value = "amendmentId", required = false) Long amendmentId,
            @RequestParam(value = "applicationStatusCode", required = false) Long applicationStatusCode) {

        ApiResponse<Object> response = constitutionContentReadFacade.constitutionsDocumentDownload(societyId, amendmentId, applicationStatusCode);

        if ( response == null ) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @DeleteMapping(value = "/hardDeleteConstitution")
    public ResponseEntity<ApiResponse<Object>> hardDeleteConstitution(
            @RequestParam(value = "constitutionContentId") Long constitutionContentId) {

        ApiResponse<Object> response = constitutionContentWriteFacade.hardDelete(constitutionContentId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getAjkType")
    public ResponseEntity<ApiResponse<GetConstitutionAjkTypeResponse>> getAjkType(
            @RequestParam(value = "societyId") Long societyId) {
        ApiResponse<GetConstitutionAjkTypeResponse> response = constitutionContentReadFacade.getAjkType(societyId);

        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }
}
