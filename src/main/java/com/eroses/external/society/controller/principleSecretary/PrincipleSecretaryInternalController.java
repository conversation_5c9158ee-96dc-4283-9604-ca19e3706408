package com.eroses.external.society.controller.principleSecretary;

import com.eroses.external.society.api.facade.newSocietySecretary.PrincipalSecretaryInternalReadFacade;
import com.eroses.external.society.dto.response.principalSecretaryInternal.PrincipalSecretaryInternalResponse;
import com.eroses.external.society.dto.response.societySecretaryReplacement.PrincipleSecretaryPaginationResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.utils.ResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/society/new/secretaries")
public class PrincipleSecretaryInternalController {
    private final PrincipalSecretaryInternalReadFacade principalSecretaryInternalReadFacade;

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<PrincipalSecretaryInternalResponse>> findPrincipalSecretary(@PathVariable Long id) throws Exception {
        ApiResponse<PrincipalSecretaryInternalResponse> response = principalSecretaryInternalReadFacade.findPrincipalSecretary(id);
        return ResponseUtil.buildResponse(response, HttpStatus.OK);
    }

    @GetMapping
    public ResponseEntity<ApiResponse<Paging<PrincipleSecretaryPaginationResponse>>> findPrincipalSecretaries(
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(defaultValue = "1") int pageNo,
            @RequestParam(value = "searchQuery", required = false) String searchQuery,
            @RequestParam(value = "stateCode", required = false) Integer stateCode,
            @RequestParam(value = "applicationStatusCode", required = false) Integer applicationStatusCode) throws Exception {
        ApiResponse<Paging<PrincipleSecretaryPaginationResponse>> response = principalSecretaryInternalReadFacade.
                findPrincipalSecretaries(pageSize, pageNo, searchQuery, stateCode, applicationStatusCode);
        return ResponseUtil.buildResponse(response, HttpStatus.valueOf(response.getCode()));
    }
}
