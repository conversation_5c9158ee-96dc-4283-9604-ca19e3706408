package com.eroses.external.society.utils;

import java.util.List;
import java.util.Objects;

import com.eroses.external.society.model.enums.UserRoleEnum;
import com.eroses.user.dto.response.GetAllUserRoleByUserIdResponse;

public class InternalUserUtil {
    public static Boolean checkUserRO(List<GetAllUserRoleByUserIdResponse> roles) {
        Boolean result = null;
        for (GetAllUserRoleByUserIdResponse role : roles) {
            if (Objects.equals(role.getRoleId(), UserRoleEnum.SUPER_ADMIN.getRole()) ||
                Objects.equals(role.getRoleId(), UserRoleEnum.PENOLONG_PENDAFTAR_PERTUBUHAN.getRole())
            ) {
                return false;
            } else if (Objects.equals(role.getRoleId(), UserRoleEnum.PEGAWAI_PENDAFTARAN_PERTUBUHAN.getRole())) {
                result = true;
            }
        }
        return result;
    }
}
