package com.eroses.external.society.utils;

import java.util.HashMap;
import java.util.Map;

public class MonthNameConverter {

    private static final Map<String, String> monthNameMap = new HashMap<>();

    static {
        monthNameMap.put("JANUARY", "<PERSON><PERSON><PERSON>");
        monthNameMap.put("FEBRUARY", "Februari");
        monthNameMap.put("MARCH", "Mac");
        monthNameMap.put("APRIL", "April");
        monthNameMap.put("MAY", "Mei");
        monthNameMap.put("JUNE", "Jun");
        monthNameMap.put("JULY", "Julai");
        monthNameMap.put("AUGUST", "Ogos");
        monthNameMap.put("SEPTEMBER", "September");
        monthNameMap.put("OCTOBER", "Oktober");
        monthNameMap.put("NOVEMBER", "November");
        monthNameMap.put("DECEMBER", "Disember");
    }

    public static String convertMonthNameToMalay(String englishMonthName) {
        return monthNameMap.getOrDefault(englishMonthName.toUpperCase(), englishMonthName);
    }
}
