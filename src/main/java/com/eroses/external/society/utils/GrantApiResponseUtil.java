package com.eroses.external.society.utils;

import com.eroses.external.society.model.ApiResponse;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * Utility class for creating standardized ApiResponse objects for the Grant Management module.
 */
@Slf4j
public class GrantApiResponseUtil {
    
    /**
     * Creates a success response with the given data.
     *
     * @param data The data to include in the response
     * @param <T>  The type of the data
     * @return An ApiResponse with the data and success status
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>("Success", 200, null, data, LocalDateTime.now());
    }
    
    /**
     * Creates a success response with the given data and message.
     *
     * @param message The success message
     * @param data    The data to include in the response
     * @param <T>     The type of the data
     * @return An ApiResponse with the data, message, and success status
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(message, 200, null, data, LocalDateTime.now());
    }
    
    /**
     * Creates an error response with the given error code.
     *
     * @param errorCode The error code from GrantErrorCodes
     * @param <T>       The type of the data (null in this case)
     * @return An ApiResponse with the error code, message, and error status
     */
    public static <T> ApiResponse<T> error(String errorCode) {
        String errorMessage = GrantErrorCodes.getMessage(errorCode);
        log.error("Error: {} - {}", errorCode, errorMessage);
        return new ApiResponse<>("Error", 400, errorCode + ": " + errorMessage, null, LocalDateTime.now());
    }
    
    /**
     * Creates an error response with the given error code and additional details.
     *
     * @param errorCode The error code from GrantErrorCodes
     * @param details   Additional details about the error
     * @param <T>       The type of the data (null in this case)
     * @return An ApiResponse with the error code, message, details, and error status
     */
    public static <T> ApiResponse<T> error(String errorCode, String details) {
        String errorMessage = GrantErrorCodes.getMessage(errorCode);
        log.error("Error: {} - {} - {}", errorCode, errorMessage, details);
        return new ApiResponse<>("Error", 400, errorCode + ": " + errorMessage + " - " + details, null, LocalDateTime.now());
    }
    
    /**
     * Creates an error response with the given error code and exception.
     *
     * @param errorCode The error code from GrantErrorCodes
     * @param e         The exception that caused the error
     * @param <T>       The type of the data (null in this case)
     * @return An ApiResponse with the error code, message, exception details, and error status
     */
    public static <T> ApiResponse<T> error(String errorCode, Exception e) {
        String errorMessage = GrantErrorCodes.getMessage(errorCode);
        log.error("Error: {} - {}", errorCode, errorMessage, e);
        return new ApiResponse<>("Error", 500, errorCode + ": " + errorMessage + " - " + e.getMessage(), null, LocalDateTime.now());
    }
    
    /**
     * Creates a not found error response.
     *
     * @param resourceType The type of resource that was not found
     * @param resourceId   The ID of the resource that was not found
     * @param <T>          The type of the data (null in this case)
     * @return An ApiResponse with the not found error code, message, and error status
     */
    public static <T> ApiResponse<T> notFound(String resourceType, Long resourceId) {
        String details = resourceType + " with ID " + resourceId + " not found";
        log.error("Error: {} - {}", GrantErrorCodes.RESOURCE_NOT_FOUND, details);
        return new ApiResponse<>("Error", 404, GrantErrorCodes.RESOURCE_NOT_FOUND + ": " + details, null, LocalDateTime.now());
    }
    
    /**
     * Creates an unauthorized error response.
     *
     * @param details Additional details about the unauthorized access
     * @param <T>     The type of the data (null in this case)
     * @return An ApiResponse with the unauthorized error code, message, and error status
     */
    public static <T> ApiResponse<T> unauthorized(String details) {
        log.error("Error: {} - {}", GrantErrorCodes.UNAUTHORIZED_ACCESS, details);
        return new ApiResponse<>("Error", 401, GrantErrorCodes.UNAUTHORIZED_ACCESS + ": " + GrantErrorCodes.getMessage(GrantErrorCodes.UNAUTHORIZED_ACCESS) + " - " + details, null, LocalDateTime.now());
    }
    
    /**
     * Creates a validation error response.
     *
     * @param details Additional details about the validation error
     * @param <T>     The type of the data (null in this case)
     * @return An ApiResponse with the validation error code, message, and error status
     */
    public static <T> ApiResponse<T> validationError(String details) {
        log.error("Error: {} - {}", GrantErrorCodes.VALIDATION_ERROR, details);
        return new ApiResponse<>("Error", 400, GrantErrorCodes.VALIDATION_ERROR + ": " + GrantErrorCodes.getMessage(GrantErrorCodes.VALIDATION_ERROR) + " - " + details, null, LocalDateTime.now());
    }
}
