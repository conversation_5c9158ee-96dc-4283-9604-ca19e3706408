package com.eroses.external.society.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.TextStyle;
import java.time.temporal.ChronoField;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

public class DayNameConverter {

    private static final Map<String, String> dayNameMap = new HashMap<>();

    static {
        dayNameMap.put("Sunday", "Ahad");
        dayNameMap.put("Monday", "Isnin");
        dayNameMap.put("Tuesday", "Selasa");
        dayNameMap.put("Wednesday", "Rabu");
        dayNameMap.put("Thursday", "Khamis");
        dayNameMap.put("Friday", "Jumaat");
        dayNameMap.put("Saturday", "Sabtu");
    }

    public static String convertDayNameToMalay(String englishDayName) {
        return dayNameMap.getOrDefault(englishDayName, englishDayName);
    }

    public static String convertToMalayDate(String englishDate) {
        DateTimeFormatter inputFormatter = new DateTimeFormatterBuilder()
                .parseCaseInsensitive()
                .appendPattern("EEEE, d/M/yyyy hh:mm:ss ")
                .appendText(ChronoField.AMPM_OF_DAY, TextStyle.SHORT)
                .toFormatter(Locale.ENGLISH);

        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("EEEE, dd-MM-yyyy hh:mm:ss a", Locale.forLanguageTag("ms"));

        // Parse the input date
        LocalDateTime dateTime = LocalDateTime.parse(englishDate, inputFormatter);

        // Format the day name and replace it
        String englishDayName = dateTime.format(DateTimeFormatter.ofPattern("EEEE", Locale.ENGLISH));
        String malayDayName = convertDayNameToMalay(englishDayName);

        // Format the full date
        String formattedDate = dateTime.format(outputFormatter);

        // Replace the day name in the formatted date
        formattedDate = formattedDate.replaceFirst(englishDayName, malayDayName);
        formattedDate = formattedDate.replace("PG", "AM").replace("PTG", "PM");

        return formattedDate;
    }

}
