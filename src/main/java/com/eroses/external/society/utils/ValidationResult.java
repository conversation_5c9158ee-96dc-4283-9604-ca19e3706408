package com.eroses.external.society.utils;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Getter
public class ValidationResult {
    private final List<String> errors;
    private final boolean valid;

    private ValidationResult(List<String> errors) {
        this.errors = Collections.unmodifiableList(errors);
        this.valid = errors.isEmpty();
    }

    public static ValidationResult valid() {
        return new ValidationResult(Collections.emptyList());
    }

    public static ValidationResult invalid(String error) {
        List<String> errors = new ArrayList<>();
        errors.add(error);
        return new ValidationResult(errors);
    }

    public static ValidationResult invalid(List<String> errors) {
        return new ValidationResult(new ArrayList<>(errors));
    }

    public ValidationResult and(ValidationResult other) {
        if (this.valid && other.valid) {
            return ValidationResult.valid();
        }

        List<String> combinedErrors = new ArrayList<>(this.errors);
        combinedErrors.addAll(other.errors);
        return new ValidationResult(combinedErrors);
    }

    public String getErrorMessage() {
        if (valid) {
            return "";
        }
        return String.join("; ", errors);
    }
}
