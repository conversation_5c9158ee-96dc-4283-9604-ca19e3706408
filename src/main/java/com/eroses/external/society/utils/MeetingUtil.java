package com.eroses.external.society.utils;

import java.time.Duration;
import java.time.LocalTime;

public class MeetingUtil {
    public static Long calcDurationMinutes(
        LocalTime meetingTimeFrom,
        LocalTime meetingTimeTo
    ) {
        if (meetingTimeFrom == null || meetingTimeTo == null) {
            return null;
        }
        Duration duration = Duration.between(meetingTimeFrom, meetingTimeTo);
        return duration.toMinutes();
    }
}
