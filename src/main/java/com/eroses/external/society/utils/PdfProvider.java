package com.eroses.external.society.utils;

import com.eroses.external.society.dto.MeetingMinutesDto;
import com.eroses.external.society.dto.request.MeetingMinutesPdfRequest;
import com.eroses.external.society.model.MeetingMemberAttendance;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.draw.SolidLine;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.LineSeparator;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;

import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.List;

public class PdfProvider {
        public String generatePdf(MeetingMinutesDto meetingMinutesDto) {
                try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                        // Initialize PDF writer
                        PdfWriter writer = new PdfWriter(outputStream);
                        PdfDocument pdfDoc = new PdfDocument(writer);
                        Document document = new Document(pdfDoc);

                        // Add content to the PDF
                        document.add(new Paragraph("Meeting Minutes"));
                        document.add(new Paragraph("Society No: " + meetingMinutesDto.getSocietyNo()));
                        document.add(new Paragraph("Society Name: " + meetingMinutesDto.getSocietyName()));
                        document.add(new Paragraph("Address: " + meetingMinutesDto.getAddress()));
                        document.add(new Paragraph("Meeting Time: " + meetingMinutesDto.getMeetingTime()));
                        document.add(new Paragraph("Meeting Place: " + meetingMinutesDto.getMeetingPlace()));
                        document.add(new Paragraph("Meeting Type: " + meetingMinutesDto.getMeetingType()));
                        document.add(new Paragraph("Total Attendees: " + meetingMinutesDto.getTotalAttendees()));
                        document.add(new Paragraph("Meeting Content: " + meetingMinutesDto.getMeetingContent()));
                        document.add(new Paragraph("Provided By: " + meetingMinutesDto.getProvidedBy()));
                        document.add(new Paragraph("Confirmed By: " + meetingMinutesDto.getConfirmBy()));

                        // Add committees
                        document.add(new Paragraph("Committees:"));
                        for (MeetingMinutesDto.Committee committee : meetingMinutesDto.getCommittees()) {
                                document.add(new Paragraph("Name: " + committee.getName() + ", Occupation: " + committee.getOccupation()));
                        }

                        // Close the document
                        document.close();

                        // Convert to Base64
                        byte[] pdfBytes = outputStream.toByteArray();
                        return Base64.getEncoder().encodeToString(pdfBytes);
                } catch (Exception e) {
                        throw new RuntimeException("Error generating PDF", e);
                }
        }

//        public static ByteArrayOutputStream createMeetingMinutesPdf() {
//                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
//                PdfWriter writer = new PdfWriter(byteArrayOutputStream);
//                PdfDocument pdfDocument = new PdfDocument(writer);
//                Document document = new Document(pdfDocument);
//
//                document.add(
//                        new Paragraph("test")
//                                .setTextAlignment(TextAlignment.CENTER).setFontSize(12)
//                );
//
//                SolidLine line = new SolidLine(1f);
//                LineSeparator ls = new LineSeparator(line);
//                document.add(ls);
//
//                document.add(
//                        new Paragraph("Tarikh          :")
//                                .setBold()
//                );
//                document.add(
//                        new Paragraph("Kehadiran       :")
//                                .setBold()
//                );
//
//                Table table = new Table(new float[]{1, 6, 3});
//                table.setWidth(UnitValue.createPercentValue(100));
//                table.addCell(new Cell().add(new Paragraph("Bil.")));
//                table.addCell(new Cell().add(new Paragraph("Nama")));
//                table.addCell(new Cell().add(new Paragraph("Jwatan")));
//
//                // Placeholder for dynamic content
//                // document.add(table);
//
//                document.close();
//                return byteArrayOutputStream;
//        }

////    public static ByteArrayOutputStream createMeetintMinutesPdf(MeetingMinutesPdfRequest meetingMinutesPdfRequest) {
//        public static ByteArrayOutputStream createMeetintMinutesPdf() {
//        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
//        PdfWriter writer = new PdfWriter(byteArrayOutputStream);
//        PdfDocument pdfDocument = new PdfDocument(writer);
//        Document document = new Document(pdfDocument);
//
//            document.add(
//                    new Paragraph("test")
//                            .setTextAlignment(TextAlignment.CENTER).setFontSize(12)
//            );
////        document.add(
////                new Paragraph(meetingMinutesPdfRequest.getSocietyNo())
////                        .setTextAlignment(TextAlignment.CENTER).setFontSize(12)
////        );
//
//        SolidLine line = new SolidLine(1f);
//        LineSeparator ls = new LineSeparator(line);
//        document.add(ls);
//
//        document.add(
//                new Paragraph("Tarikh          :")
//                        .setBold()
//        );
//        document.add(
//                new Paragraph("Kehadiran       :")
//                        .setBold()
//        );
//
//        Table table = new Table(new float[]{1, 6, 3});
//        table.setWidth(UnitValue.createPercentValue(100));
//        table.addCell(new Cell().add(new Paragraph("Bil.")));
//        table.addCell(new Cell().add(new Paragraph("Nama")));
//        table.addCell(new Cell().add(new Paragraph("Jwatan")));
//
////        List<MeetingMemberAttendance> committees = meetingMinutesPdfRequest.getCommittees();
////
////        for (int i = 0; i < committees.size(); i++) {
////            MeetingMemberAttendance committee = committees.get(i);
////            table.addCell(new Cell().add(new Paragraph(Integer.toString(i + 1))));
////            table.addCell(new Cell().add(new Paragraph(committee.getName())));
////            table.addCell(new Cell().add(new Paragraph(committee.getPosition())));
////        }
//        document.add(table);
//
//        document.close();
//        return byteArrayOutputStream;
//    }
}
