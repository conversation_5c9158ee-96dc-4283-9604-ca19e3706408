package com.eroses.external.society.utils;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class JsonConverter {
    public static Map<String, Object> JsonToMap(String jsonString) {
        JSONObject jsonObject = new JSONObject(jsonString);
        Map<String, Object> map = new HashMap<>();
        Iterator<String> keys = jsonObject.keys();
        while (keys.hasNext()) {
            String key = keys.next();
            Object value = jsonObject.get(key);
            map.put(key, value);
        }
        return map;
    }

    // Converts Map to JSON string with escaped quotes
    public static String mapToJsonStringWithEscapedQuotes(Map<String, Object> map) {
        JSONObject jsonObject = new JSONObject(map);
        // Get the JSON string and replace quotes to escape them
        return jsonObject.toString().replace("\"", "\\\"");
    }
}
