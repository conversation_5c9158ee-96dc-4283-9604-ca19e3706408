package com.eroses.external.society.utils;

/**
 * Error codes for the Grant Management module.
 * This class defines standardized error codes and messages for various error scenarios
 * that can occur in the grant management feature.
 */
public class GrantErrorCodes {

    // General errors (1000-1099)
    public static final String GENERAL_ERROR = "GRN-1000";
    public static final String VALIDATION_ERROR = "GRN-1001";
    public static final String UNAUTHORIZED_ACCESS = "GRN-1002";
    public static final String RESOURCE_NOT_FOUND = "GRN-1003";

    // Grant Template errors (1100-1199)
    public static final String TEMPLATE_NOT_FOUND = "GRN-1100";
    public static final String TEMPLATE_ALREADY_PUBLISHED = "GRN-1101";
    public static final String TEMPLATE_FIELD_INVALID = "GRN-1102";
    public static final String TEMPLATE_SOCIETY_CATEGORY_INVALID = "GRN-1103";

    // Grant Application errors (1200-1299)
    public static final String APPLICATION_NOT_FOUND = "GRN-1200";
    public static final String APPLICATION_ALREADY_SUBMITTED = "GRN-1201";
    public static final String APPLICATION_FIELD_MISSING = "GRN-1202";
    public static final String APPLICATION_INVALID_STATUS_TRANSITION = "GRN-1203";
    public static final String APPLICATION_SOCIETY_NOT_ELIGIBLE = "GRN-1204";

    // Grant Query errors (1300-1399)
    public static final String QUERY_NOT_FOUND = "GRN-1300";
    public static final String QUERY_ALREADY_RESPONDED = "GRN-1301";

    // Grant Report errors (1400-1499)
    public static final String REPORT_NOT_FOUND = "GRN-1400";
    public static final String REPORT_ALREADY_SUBMITTED = "GRN-1401";
    public static final String REPORT_ATTACHMENT_INVALID = "GRN-1402";
    public static final String REPORT_INVALID_STATUS_TRANSITION = "GRN-1403";

    // File upload errors (1500-1599)
    public static final String FILE_UPLOAD_FAILED = "GRN-1500";
    public static final String FILE_TYPE_NOT_SUPPORTED = "GRN-1501";
    public static final String FILE_SIZE_EXCEEDED = "GRN-1502";

    // Error messages
    public static final String getMessage(String errorCode) {
        switch (errorCode) {
            // General errors
            case GENERAL_ERROR:
                return "Ralat tidak dijangka berlaku";
            case VALIDATION_ERROR:
                return "Ralat pengesahan berlaku";
            case UNAUTHORIZED_ACCESS:
                return "Akses tidak dibenarkan ke sumber";
            case RESOURCE_NOT_FOUND:
                return "Sumber tidak dijumpai";

            // Grant Template errors
            case TEMPLATE_NOT_FOUND:
                return "Templat geran tidak dijumpai";
            case TEMPLATE_ALREADY_PUBLISHED:
                return "Templat geran telah diterbitkan";
            case TEMPLATE_FIELD_INVALID:
                return "Medan templat geran tidak sah";
            case TEMPLATE_SOCIETY_CATEGORY_INVALID:
                return "Kategori pertubuhan tidak sah untuk templat geran";

            // Grant Application errors
            case APPLICATION_NOT_FOUND:
                return "Grant application not found";
            case APPLICATION_ALREADY_SUBMITTED:
                return "Grant application is already submitted";
            case APPLICATION_FIELD_MISSING:
                return "Required field is missing in grant application";
            case APPLICATION_INVALID_STATUS_TRANSITION:
                return "Invalid status transition for grant application";
            case APPLICATION_SOCIETY_NOT_ELIGIBLE:
                return "Society is not eligible for this grant";

            // Grant Query errors
            case QUERY_NOT_FOUND:
                return "Grant query not found";
            case QUERY_ALREADY_RESPONDED:
                return "Grant query is already responded";

            // Grant Report errors
            case REPORT_NOT_FOUND:
                return "Grant report not found";
            case REPORT_ALREADY_SUBMITTED:
                return "Grant report is already submitted";
            case REPORT_ATTACHMENT_INVALID:
                return "Invalid attachment for grant report";
            case REPORT_INVALID_STATUS_TRANSITION:
                return "Invalid status transition for grant report";

            // File upload errors
            case FILE_UPLOAD_FAILED:
                return "Failed to upload file";
            case FILE_TYPE_NOT_SUPPORTED:
                return "File type not supported";
            case FILE_SIZE_EXCEEDED:
                return "File size exceeded the maximum limit";

            default:
                return "Unknown error";
        }
    }
}
