package com.eroses.external.society.utils;

import com.eroses.external.society.model.AdmBranch;
import com.eroses.external.society.model.enums.AdmAddressesEnum;
import com.eroses.external.society.model.enums.AdmBranchEnum;
import com.eroses.external.society.model.enums.SocietyCategoryEnum;
import com.eroses.external.society.model.enums.UserRoleEnum;
import com.eroses.external.society.service.admin.AdmBranchReadDomainService;
import com.eroses.user.api.facade.UserFacade;
import com.eroses.user.model.User;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Utility class for RO Decision related operations
 * Contains methods for filtering users by role and state code
 */
@Component
@RequiredArgsConstructor
public class RoDecisionUtilities {

    private final UserFacade userFacade;
    private final AdmBranchReadDomainService admBranchReadDomainService;

    /**
     * Get users with a specific role for a given state code
     *
     * @param userRole the user role to filter by
     * @param stateCode the state code to filter by
     * @return list of users matching the criteria
     */
    // Depreciated, recheck logic from posting
    public List<User> getUsersByRoleAndState(UserRoleEnum userRole, String stateCode) throws Exception {
        List<User> filteredUsers = new ArrayList<>();

        if (isHQStateCodes(stateCode)) {
            // For HQ states, get users from all HQ branches
            List<AdmBranch> admBranchesHQ = admBranchReadDomainService.getAllByCodes(getHQCodes());
            filteredUsers = userFacade.getUsersByUserRole(userRole.getRole())
                    .stream()
                    .filter(user -> admBranchesHQ.stream()
                            .anyMatch(admBranch -> user.getJppmBranchId() == admBranch.getId()))
                    .collect(Collectors.toList());
        } else if (isLabBranchCode(stateCode)) {
            // For LAB branch, get users from Sabah state
            AdmBranch sabahBranch = admBranchReadDomainService.getByCode(AdmBranchEnum.SBH.getCode());
            filteredUsers = userFacade.getUsersByUserRole(userRole.getRole())
                    .stream()
                    .filter(user -> user.getJppmBranchId() == sabahBranch.getId())
                    .collect(Collectors.toList());
        } else {
            // For non-HQ states, get users from the specific state branch
            AdmBranch admBranch = admBranchReadDomainService.getByStateCode(stateCode);
            filteredUsers = userFacade.getUsersByUserRole(userRole.getRole())
                    .stream()
                    .filter(user -> user.getJppmBranchId() == admBranch.getId())
                    .collect(Collectors.toList());
        }

        return filteredUsers;
    }

    /**
     * Check if the given state code is one of the HQ state codes
     *
     * @param stateCode the state code to check
     * @return true if it's an HQ state code, false otherwise
     */
    public static boolean isHQStateCodes(String stateCode) {
        //Extra logic to group for LAB, KUL, PJY state code
        if (Objects.equals(stateCode, "258") ||
                Objects.equals(stateCode, "256")) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Check if the given state code corresponds to LAB branch
     *
     * @param stateCode the state code to check
     * @return true if it's LAB branch code, false otherwise
     */
    public static boolean isLabBranchCode(String stateCode) {
        // LAB branch code (257) should get users from Sabah state
        return Objects.equals(stateCode, "257");
    }

    /**
     * Get the list of HQ branch codes
     *
     * @return list of HQ branch codes
     */
    public static List<String> getHQCodes() {
        return List.of(
                AdmBranchEnum.HQ.getCode(),
                AdmBranchEnum.KDN.getCode(),
                AdmBranchEnum.KUL.getCode()
        );
    }

    /**
     * Retrieves approval officers assigned to handle society applications based on user role,
     * state jurisdiction, and society category.
     *
     * Business Rules:
     * - Political societies: Handled by HQ branch (Pengurusan Pertubuhan) officers only
     * - PP KDN officer: Will only exist in by KDN branch
     * - Labuan societies: Managed by Sabah branch officers
     * - Putrajaya societies: Managed by Kuala Lumpur branch officers
     * - Other societies: Handled by officers from their respective state branches
     *
     * @param userRole the required user role for approval authority
     * @param stateCode the state code where the society is located
     * @param societyCategory the category of society (e.g., POLITIK, etc.)
     * @return list of users authorized to approve applications for the given criteria
     * @throws Exception if there's an error retrieving branch or user data
     */
    public List<User> getAssignedApprovalOfficer(UserRoleEnum userRole, String stateCode, SocietyCategoryEnum societyCategory) throws Exception {

        // Handle political societies - only HQ branch officers
        if (SocietyCategoryEnum.POLITIK.equals(societyCategory)) {
            return getOfficersByBranch(userRole, AdmBranchEnum.PP.getCode());
        }

        //Handle for Appeal Approval case - only KDN branch officers
        if (userRole.equals(UserRoleEnum.PP_KDN)){
            return getOfficersByBranch(userRole, AdmBranchEnum.KDN.getCode());
        }

        // Determine target state based on special jurisdiction rules
        String targetStateCode = determineTargetStateCode(stateCode);

        // Get officers from branches in the target state
        return getOfficersByStateCode(userRole, targetStateCode);
    }

    /**
     * Retrieves officers assigned to handle society applications based on user role,
     * state jurisdiction, and society category.
     *
     * Business Rules:
     * - Labuan societies: Managed by Sabah branch officers
     * - Putrajaya societies: Managed by Kuala Lumpur branch officers
     * - Other societies: Handled by officers from their respective state branches
     *
     * @param userRole the required user role for approval authority
     * @param stateCode the state code where the society is located
     * @return list of users authorized to approve applications for the given criteria
     * @throws Exception if there's an error retrieving branch or user data
     */
    public List<User> getAssignedHelpDeskOfficer(UserRoleEnum userRole, String stateCode) throws Exception {

        // Determine target state based on special jurisdiction rules
        String targetStateCode = determineHelpDeskTargetStateCode(stateCode);

        // Get officers from branches in the target state
        return getOfficersByStateCode(userRole, targetStateCode);
    }

    /**
     * Determines the target state code based on special jurisdiction mappings for federal territories.
     * This method centralizes the jurisdiction mapping logic to ensure consistency across the application.
     *
     * Business Rules:
     * - Labuan Federal Territory: Handled by Sabah state authorities
     * - Putrajaya Federal Territory: Handled by Kuala Lumpur state authorities
     * - All other states: Handled by their own state authorities
     *
     * @param stateCode the original state code
     * @return the target state code for officer assignment and responsibility
     */
    private String determineTargetStateCode(String stateCode) {
        // Labuan Federal Territory is managed by Sabah state
        if (AdmAddressesEnum.WILAYAH_PERSEKUTUAN_LABUAN.getId().equals(stateCode)) {
            return AdmAddressesEnum.SABAH.getId();
        }

        // Putrajaya Federal Territory is managed by Kuala Lumpur state
        if (AdmAddressesEnum.WILAYAH_PERSEKUTUAN_PUTRAJAYA.getId().equals(stateCode)) {
            return AdmAddressesEnum.WILAYAH_PERSEKUTUAN_KUALA_LUMPUR.getId();
        }

        // All other states handle their own societies
        return stateCode;
    }

    /**
     * Determines the target state code based on special jurisdiction mappings for federal territories.
     * This method centralizes the jurisdiction mapping logic to ensure consistency across the application.
     *
     * Business Rules:
     * - Labuan Federal Territory: Handled by Sabah state authorities
     * - Putrajaya Federal Territory: Handled by Kuala Lumpur state authorities
     * - All other states: Handled by their own state authorities
     *
     * @param stateCode the original state code
     * @return the target state code for officer assignment and responsibility
     */
    private String determineHelpDeskTargetStateCode(String stateCode) {
        // Labuan Federal Territory is managed by Sabah state
        if (AdmAddressesEnum.WILAYAH_PERSEKUTUAN_LABUAN.getId().equals(stateCode)) {
            return AdmAddressesEnum.SABAH.getId();
        }

        // All other states handle their own societies
        return stateCode;
    }

    /**
     * Retrieves officers from a specific branch by branch code.
     *
     * @param userRole the required user role
     * @param branchCode the branch code to filter by
     * @return list of users from the specified branch with the required role
     * @throws Exception if there's an error retrieving branch data
     */
    private List<User> getOfficersByBranch(UserRoleEnum userRole, String branchCode) throws Exception {
        AdmBranch targetBranch = admBranchReadDomainService.getByCode(branchCode);

        return userFacade.getUsersByUserRole(userRole.getRole())
                .stream()
                .filter(user -> user.getJppmBranchId() == targetBranch.getId())
                .sorted(Comparator.comparing(User::getId).reversed())
                .collect(Collectors.toList());
    }

    /**
     * Retrieves officers from all branches within a specific state.
     *
     * @param userRole the required user role
     * @param stateCode the state code to get branches from
     * @return list of users from branches in the specified state with the required role
     * @throws Exception if there's an error retrieving branch data
     */
    private List<User> getOfficersByStateCode(UserRoleEnum userRole, String stateCode) throws Exception {
        List<AdmBranch> admBranches = admBranchReadDomainService.getAllByStateCode(stateCode);
        Set<Long> admBranchIds = admBranches.stream()
                .map(AdmBranch::getId)
                .collect(Collectors.toSet());

        return userFacade.getUsersByUserRole(userRole.getRole())
                .stream()
                .filter(user -> admBranchIds.contains(user.getJppmBranchId()))
                .sorted(Comparator.comparing(User::getId).reversed())
                .collect(Collectors.toList());
    }

    /**
     * Determines the responsible state code for handling society applications based on
     * jurisdiction rules and society category.
     *
     * Business Rules:
     * - Political societies: Always handled by Putrajaya (federal headquarters)
     * - Labuan societies: Managed by Sabah state authorities
     * - Putrajaya societies: Managed by Kuala Lumpur state authorities
     * - Other societies: Handled by their respective state authorities
     *
     * @param stateCode the original state code where the society is located
     * @param societyCategory the category of society (e.g., POLITIK, etc.)
     * @return the state code of the authority responsible for handling the society
     * @throws Exception if there's an error processing the request
     */
    public String getAssignedResponsibleStateCode(String stateCode, SocietyCategoryEnum societyCategory) throws Exception {

        // Political societies are always handled by federal headquarters in Putrajaya
        if (SocietyCategoryEnum.POLITIK.equals(societyCategory)) {
            return AdmAddressesEnum.WILAYAH_PERSEKUTUAN_PUTRAJAYA.getId();
        }

        // Apply special jurisdiction mappings for federal territories
        return determineTargetStateCode(stateCode);
    }

    public boolean checkUserIsFromHQBranch(User user) throws Exception {
        AdmBranch currentUserAdmBranch = admBranchReadDomainService.findById(user.getJppmBranchId());
        return Objects.equals(currentUserAdmBranch.getCode(), AdmBranchEnum.HQ.getCode()) ||
                Objects.equals(currentUserAdmBranch.getCode(), AdmBranchEnum.PT.getCode()) ||
                Objects.equals(currentUserAdmBranch.getCode(), AdmBranchEnum.PUU.getCode()) ||
                Objects.equals(currentUserAdmBranch.getCode(), AdmBranchEnum.PP.getCode()) ||
                Objects.equals(currentUserAdmBranch.getCode(), AdmBranchEnum.PTM.getCode()) ||
                Objects.equals(currentUserAdmBranch.getCode(), AdmBranchEnum.PK.getCode()) ||
                Objects.equals(currentUserAdmBranch.getCode(), AdmBranchEnum.KP.getCode()) ||
                Objects.equals(currentUserAdmBranch.getCode(), AdmBranchEnum.PS.getCode());
    }

    public List<String> retrieveResponsibleStatesOfUser(User user) throws Exception {
        AdmBranch currentUserAdmBranch = admBranchReadDomainService.findById(user.getJppmBranchId());

        List<String> stateCodes = new ArrayList<>();
        if (Objects.equals(currentUserAdmBranch.getStateCode(), AdmAddressesEnum.SABAH.getId())) {
            stateCodes.add(AdmAddressesEnum.WILAYAH_PERSEKUTUAN_LABUAN.getId());
        } else if (Objects.equals(currentUserAdmBranch.getStateCode(), AdmAddressesEnum.WILAYAH_PERSEKUTUAN_KUALA_LUMPUR.getId())) {
            stateCodes.add(AdmAddressesEnum.WILAYAH_PERSEKUTUAN_PUTRAJAYA.getId());
        }

        stateCodes.add(currentUserAdmBranch.getStateCode());

        return stateCodes.stream().distinct().collect(Collectors.toList());
    }

    /**
     * Retrieves the list of states responsible for handling feedback for a given user.
     *
     * @param user the user for whom to retrieve the responsible states
     * @return list of state codes responsible for handling feedback for the user
     * @throws Exception if there's an error retrieving branch data
     */
    public List<String> retrieveResponsibleFeedbackStatesOfUser(User user) throws Exception {
        AdmBranch currentUserAdmBranch = admBranchReadDomainService.findById(user.getJppmBranchId());

        List<String> stateCodes = new ArrayList<>();
        if (Objects.equals(currentUserAdmBranch.getStateCode(), AdmAddressesEnum.SABAH.getId())) {
            stateCodes.add(AdmAddressesEnum.WILAYAH_PERSEKUTUAN_LABUAN.getId());
        }

        stateCodes.add(currentUserAdmBranch.getStateCode());

        return stateCodes.stream().distinct().collect(Collectors.toList());
    }
}
