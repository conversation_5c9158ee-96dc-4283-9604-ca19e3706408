package com.eroses.external.society.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.github.cdimascio.dotenv.Dotenv;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;

@Slf4j
@Component
public class AwsS3Utils {

    @Value("${aws.s3.region}")
    private String region;

    @Value("${aws.s3.secret-name}")
    private String secretName;

    @Value("${spring.profiles.active}")
    private String env;

    public Region getRegion() {
        return Region.of(region);
    }

    // Create S3Client
    public S3Client createS3Client() {
        AwsBasicCredentials awsCredentials = awsBasicCredentials();
        return S3Client.builder()
                .region(Region.of(region))
                .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                .build();
    }

    public S3Presigner createS3Presigner() {
        AwsBasicCredentials awsBasicCredentials = awsBasicCredentials();
        return S3Presigner.builder()
                .region(Region.of(region))
                .credentialsProvider(StaticCredentialsProvider.create(awsBasicCredentials))
                .build();
    }

    // Retrieve AWS credentials from Secrets Manager
    public AwsBasicCredentials awsBasicCredentials() {
        String accessKey;
        String secretKey;

        if(env.equals("local")){
            Dotenv dotenv = Dotenv.configure()
                .directory("./") // Look in the current directory
                .ignoreIfMissing() // Don't throw an exception if the file is missing
                .load();

            // Try to get from .env file first
            accessKey = dotenv.get("AWS_ACCESS_KEY_ID_DEV");
            secretKey = dotenv.get("AWS_SECRET_ACCESS_KEY_DEV");

            // If not found in .env, try system environment variables
            if (accessKey == null || secretKey == null) {
                accessKey = System.getenv("AWS_ACCESS_KEY_ID_DEV");
                secretKey = System.getenv("AWS_SECRET_ACCESS_KEY_DEV");
            }

            return AwsBasicCredentials.create(accessKey, secretKey);
        }

        SecretsManagerClient secretsManagerClient = SecretsManagerClient.builder()
                .region(Region.of(region))
                .build();

        GetSecretValueRequest getSecretValueRequest = GetSecretValueRequest.builder()
                .secretId(secretName)
                .build();

        GetSecretValueResponse getSecretValueResponse = secretsManagerClient.getSecretValue(getSecretValueRequest);

        String secret = getSecretValueResponse.secretString();

        ObjectMapper objectMapper = new ObjectMapper();
        try{
            JsonNode secretJson = objectMapper.readTree(secret);
            accessKey = secretJson.get("accessKey").asText();
            secretKey = secretJson.get("secretKey").asText();
            return AwsBasicCredentials.create(accessKey, secretKey);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}