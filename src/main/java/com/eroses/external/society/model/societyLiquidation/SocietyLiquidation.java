package com.eroses.external.society.model.societyLiquidation;

import com.eroses.external.society.model.BaseEntity;
import com.eroses.external.society.model.Branch;
import com.eroses.external.society.model.Society;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Notes:
 *
 * transferDate: the date of PPP assigning a RO.
 * ro: the name of ro.
 *
 * Not using status, only application_status_code.
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SocietyLiquidation extends BaseEntity {
    @OneToOne(fetch = FetchType.LAZY)
    private Society society;
    private Long societyId;
    private String societyNo;
    @OneToOne(fetch = FetchType.LAZY)
    private Branch branch;
    private Long branchId;
    private String branchNo;
    private Integer liquidationDocumentType;
    private Long meetingId;
    private LocalDate meetingDate;
    private Integer committeeVoteCount;
    private Integer committeeAttendCount;
    private Integer committeeAgreeCount;
    private BigDecimal totalFixedAssets;
    private BigDecimal cashInHand;
    private BigDecimal cashInBank;
    private BigDecimal totalAsset;
    private BigDecimal shortTermLiability;
    private BigDecimal longTermLiability;
    private BigDecimal totalLiability;
    private BigDecimal totalFixedAssetsFinance;
    private BigDecimal cashInHandFinance;
    private BigDecimal cashInBankFinance;
    private BigDecimal totalAssetFinance;
    private BigDecimal shortTermLiabilityFinance;
    private BigDecimal longTermLiabilityFinance;
    private BigDecimal totalLiabilityFinance;
    private String applicantName;
    private LocalDate submissionDate;
    private LocalDate decisionDate;
    private Integer applicationStatusCode;
    private String ro;
    private Integer branchLiquidation;
    private String notePpp;
    private LocalDate transferDate;
    private String noteRo;
    private Integer status;
    private Integer roDecisionCode;
    private String roDecisionNote;
    private Long statementId;
}
