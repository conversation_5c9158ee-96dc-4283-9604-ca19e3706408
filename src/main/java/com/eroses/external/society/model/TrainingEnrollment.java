package com.eroses.external.society.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Data
@SuperBuilder
@NoArgsConstructor
public class TrainingEnrollment extends BaseEntity {
    private Long id;
    private Long trainingCourseId;
    private Long userId;
    private LocalDateTime enrollmentDate;
    private LocalDateTime completionDate;
    private String completionStatus; // Enum: 'IN_PROGRESS', 'COMPLETED', 'FAILED'
    private String certificateId;
    private Integer totalStep;
    private Integer currentStep;
}
