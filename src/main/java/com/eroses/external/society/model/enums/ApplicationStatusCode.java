package com.eroses.external.society.model.enums;

import lombok.Getter;

//for application_status_code columns

@Getter
public enum ApplicationStatusCode {
    BELUM_DIHANTAR(1),
    MENUNGGU_KEPUTUSAN(2),
    LULUS(3),
    TOLAK(4),
    MENUNGGU_BAYARAN_KAUNTER(5),
    MENUNGGU_BAYARAN_ONLINE(6),
    MAKLUMAT_TIDAK_LENGKAP(7),
    LUPUT(8),
    DIBENARKAN(9),
    TIDAK_DIBENARKAN(10),
    AKTIF(11),
    MENUNGGU_PENGAKTIFAN_CAWANGAN(12),
    TIADA_MAKLUMAT_MIGRASI(13),
    TAMAT_TEMPOH_CARIAN(14),
    SETIAUSAHA_BUKAN_WARGA(15),
    BUBAR(16),
    SELESAI(17),
    DISYORKA<PERSON>(18),
    TIDAK_DISYORKAN(19),
    <PERSON><PERSON><PERSON>(20),
    MENUNGGU_SYOR(21),
    TEST_<PERSON>AT<PERSON>(22),
    MENUNGGU_MAKLUMBALAS(23),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>HA<PERSON>(25),
    <PERSON>AR<PERSON><PERSON>_BALIK(26),
    <PERSON>TA<PERSON>_<PERSON>HA<PERSON>(27),
    MENUNGGU_PENGESAHAN_JAWATANKUASA_INDUK(28),
    LULUS_BERSYARAT(29),
    DALAM_TINDAKAN_KIV(30),
    KUIRI(36),
    PINDAH(37),
    MENUNGGU_PENGESAHAN_BAYARAN(38),
    MENUNGGU_KEPUTUSAN_MENTERI(39),
    MENUNGGU_ULASAN(40),
    MENUNGGU_ULASAN_AGENSI_LUAR(41),
    NOTIS_MESYUARAT_DIHANTAR(42),
    INAKTIF(43),
    BAYARAN_GAGAL(44),
    PADAM(-1);


    private final int code;
    ApplicationStatusCode(int code) {
        this.code = code;
    }

    public static ApplicationStatusCode getStatus(int code) {
        for (ApplicationStatusCode status : ApplicationStatusCode.values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }
}
