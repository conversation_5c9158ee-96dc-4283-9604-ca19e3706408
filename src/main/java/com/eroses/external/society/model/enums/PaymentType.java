package com.eroses.external.society.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum PaymentType {
    //PaymentCode Fixed for External API requirement
    SOCIETY_ONLINE_PAYMENT("Pendaftaran Pertubuhan Baru (Pembayaran ONLINE)", "Pendaftaran Pertubuhan Baru", "RESIT001"),
    SOCIETY_KAUNTER_PAYMENT("Pendaftaran Pertubuhan Baru (Pembayaran KAUNTER)", "Pendaftaran Pertubuhan Baru", "RESIT001"),
    BRANCH_ONLINE_PAYMENT("Pendaftaran Cawangan (Pembayaran ONLINE)", "Pendaftaran Cawangan Baru", "RESIT002"),
    BRANCH_KAUNTER_PAYMENT("Pendaftaran Cawangan (Pembayaran KAUNTER)", "Pendaftaran Cawangan Baru", "RESIT002"),
    AMENDMENT_ONLINE_PAYMENT("Pindaan <PERSON>dang-Undang (Pembayaran ONLINE)", "Pindaan Undang-Undang", "RESIT003"),
    AMENDMENT_KAUNTER_PAYMENT("Pindaan Undang-Undang (Pembayaran KAUNTER)", "Pindaan Undang-Undang", "RESIT003"),
    PROPERTY_OFFICER_ONLINE_PAYMENT("Pendaftaran Pegawai Harta (Pembayaran ONLINE)", "Pendaftaran Pegawai Harta", "RESIT004"),
    PROPERTY_OFFICER_KAUNTER_PAYMENT("Pendaftaran Pegawai Harta (Pembayaran KAUNTER)", "Pendaftaran Pegawai Harta", "RESIT004"),
    PUBLIC_OFFICER_ONLINE_PAYMENT("Pendaftaran Pegawai Awam (Pembayaran ONLINE)", "Pendaftaran Pegawai Awam", "RESIT005"),
    PUBLIC_OFFICER_KAUNTER_PAYMENT("Pendaftaran Pegawai Awam (Pembayaran KAUNTER)", "Pendaftaran Pegawai Awam", "RESIT005"),
    SEARCH_INFORMATION_ONLINE_PAYMENT("Carian Maklumat (Pembayaran ONLINE)", "Carian Maklumat", "RESIT006"),
    SEARCH_INFORMATION_KAUNTER_PAYMENT("Carian Maklumat (Pembayaran KAUNTER)", "Carian Maklumat", "RESIT006"),
    APPEAL_ONLINE_PAYMENT("Rayuan (Pembayaran ONLINE)", "Permohonan Rayuan", "RESIT007"),
    APPEAL_KAUNTER_PAYMENT("Rayuan (Pembayaran KAUNTER)", "Permohonan Rayuan", "RESIT007"),
    BRANCH_AMENDMENT_ONLINE_PAYMENT("Pindaan Cawangan (Pembayaran ONLINE)", "Pindaan Cawangan", "RESIT009"),
    BRANCH_AMENDMENT_KAUNTER_PAYMENT("Pindaan Cawangan (Pembayaran KAUNTER)", "Pindaan Cawangan", "RESIT009"),
    ;


    private final String code;

    private final String description;

    private final String docCode;

    public static PaymentType getPaymentType(String paymentTypeCode) {
        for (PaymentType paymentType : PaymentType.values()) {
            if (Objects.equals(paymentType.code, paymentTypeCode)){
                return paymentType;
            }
        }
        return null;
    }

    /**
     * Adds a prefix to the ID based on the payment type code.
     *
     * @param code The payment type code.
     * @param id   The ID to be formatted.
     * @return Formatted string with prefix if applicable, otherwise the original ID.
     */
    public static String convertToTransactionIdFormat(String code, String id) {
        if (PROPERTY_OFFICER_ONLINE_PAYMENT.code.equals(code) || PROPERTY_OFFICER_KAUNTER_PAYMENT.code.equals(code)) {
            return "HRT-" + id;
        } else if (PUBLIC_OFFICER_ONLINE_PAYMENT.code.equals(code) || PUBLIC_OFFICER_KAUNTER_PAYMENT.code.equals(code)) {
            return "AWM-" + id;
        } else if (SEARCH_INFORMATION_ONLINE_PAYMENT.code.equals(code) || SEARCH_INFORMATION_KAUNTER_PAYMENT.code.equals(code)) {
            return "CAR-" + id;
        } else if (APPEAL_ONLINE_PAYMENT.code.equals(code) || APPEAL_KAUNTER_PAYMENT.code.equals(code)) {
            return "RAY-" + id;
        } else if (BRANCH_AMENDMENT_ONLINE_PAYMENT.code.equals(code) || BRANCH_AMENDMENT_KAUNTER_PAYMENT.code.equals(code)) {
                return "PCAW-" + id;
        } else {
            return id; // No prefix for other cases
        }
    }
}
