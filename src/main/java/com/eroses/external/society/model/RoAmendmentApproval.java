package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class RoAmendmentApproval extends BaseEntity {

    @ApiModelProperty("idMinit")
    private Long idMinit;

    @ApiModelProperty("amendmentId")
    private Long amendmentId;

    @ApiModelProperty("societyNo")
    private String societyNo;

    @ApiModelProperty("decision")
    private Long decision;

    @ApiModelProperty("notes")
    private String notes;

    @ApiModelProperty("decisionDate")
    private LocalDateTime decisionDate;
}
