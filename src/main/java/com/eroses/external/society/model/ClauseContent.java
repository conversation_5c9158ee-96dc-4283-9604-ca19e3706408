package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ClauseContent extends BaseEntity implements Serializable {
    @ApiModelProperty("id")
    public Long id;

    @ApiModelProperty("constitutionTypeId")
    public Long constitutionTypeId;

    @ApiModelProperty("clauseNo")
    public String clauseNo;

    @ApiModelProperty("name")
    public String name;

    @ApiModelProperty("content")
    public String content;

    @ApiModelProperty("keyValue")
    public String keyValue;

    @ApiModelProperty("status")
    public Integer status;

}


