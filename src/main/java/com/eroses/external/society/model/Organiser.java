package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Organiser extends BaseEntity implements Serializable {
    @ApiModelProperty("id")
    public Long id;

    @ApiModelProperty("name")
    public String name;

    @ApiModelProperty("identification_no")
    public String identificationNo;

    @ApiModelProperty("phone_number")
    public String phoneNumber;
}
