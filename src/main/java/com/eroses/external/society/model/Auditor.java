package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Auditor extends BaseEntity implements Serializable {
    private Long id;
    private Long societyId;
    private String societyNo;
    private Long branchId;
    private String branchNo;
    private Long statementId;
    private String auditorType;
    private String titleCode;
    private String name;
    private String licenseNo;
    private String companyName;
    private String companyNo;
    private String gender;
    private String nationalityStatus;
    private String identificationType;
    private String identificationNo;
    private LocalDate dateOfBirth;
    private String placeOfBirth;
    private String employmentCode;
    private String address;
    private String countryCode;
    private String stateCode;
    private String districtCode;
    private String smallDistrictCode;
    private String city;
    private String postcode;
    private String email;
    private String telephoneNo;
    private String phoneNo;
    private LocalDate appointmentDate;
    private String status;
    private String deleteStatus;
    private String pemCaw;
}
