package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class Society extends BaseEntity {

    @ApiModelProperty("societyNo")
    private String societyNo;

    @ApiModelProperty("applicationNo")
    private String applicationNo;

    @ApiModelProperty("searchQuery")
    private String societyName;

    @ApiModelProperty("shortName")
    private String shortName;

    @ApiModelProperty("societyLevel")
    private String societyLevel;

    @ApiModelProperty("constitutionType")
    private String constitutionType;

    @ApiModelProperty("hasBranch")
    private String hasBranch;

    @ApiModelProperty("registeredDate")
    private LocalDate registeredDate;

    @ApiModelProperty("approvedDate")
    private LocalDate approvedDate;

    @ApiModelProperty("submissionDate")
    private LocalDate submissionDate;

    @ApiModelProperty("identificationNo")
    private String identificationNo;

    @ApiModelProperty("categoryCodeJppm")
    private String categoryCodeJppm;

    @ApiModelProperty("subCategoryCode")
    private String subCategoryCode;

    @ApiModelProperty("countryCode")
    private String countryCode;

    @ApiModelProperty("stateCode")
    private String stateCode;

    @ApiModelProperty("districtCode")
    private String districtCode;

    @ApiModelProperty("smallDistrictCode")
    private String smallDistrictCode;

    @ApiModelProperty("cityCode")
    private String cityCode;

    @ApiModelProperty("city")
    private String city;

    @ApiModelProperty("postcode")
    private String postcode;

    @ApiModelProperty("address")
    private String address;

    @ApiModelProperty("addressLatitude")
    private BigDecimal addressLatitude;

    @ApiModelProperty("addressLongitude")
    private BigDecimal addressLongitude;

    @ApiModelProperty("mailingCountryCode")
    private String mailingCountryCode;

    @ApiModelProperty("mailingStateCode")
    private String mailingStateCode;

    @ApiModelProperty("mailingDistrictCode")
    private String mailingDistrictCode;

    @ApiModelProperty("mailingSmallDistrictCode")
    private String mailingSmallDistrictCode;

    @ApiModelProperty("mailingCityCode")
    private String mailingCityCode;

    @ApiModelProperty("mailingCity")
    private String mailingCity;

    @ApiModelProperty("mailingPostcode")
    private String mailingPostcode;

    @ApiModelProperty("mailingAddress")
    private String mailingAddress;

    @ApiModelProperty("mailingAddressLatitude")
    private BigDecimal mailingAddressLatitude;

    @ApiModelProperty("mailingAddressLongitude")
    private BigDecimal mailingAddressLongitude;

    @ApiModelProperty("phoneNumber")
    private String phoneNumber;

    @ApiModelProperty("faxNumber")
    private String faxNumber;

    @ApiModelProperty("email")
    private String email;

    @ApiModelProperty("statusCode")
    private String statusCode;

    @ApiModelProperty("subStatusCode")
    private String subStatusCode;

    @ApiModelProperty("statement")
    private String statement;

    @ApiModelProperty("statementDate")
    private LocalDate statementDate;

    @ApiModelProperty("paymentMethod")
    private String paymentMethod;

    @ApiModelProperty("paymentId")
    private Long paymentId;

    @ApiModelProperty("idOsol")
    private Long idOsol;

    @ApiModelProperty("kodPtj")
    private String kodPtj;

    @ApiModelProperty("receiptNo")
    private String receiptNo;

    @ApiModelProperty("paymentDate")
    private LocalDate paymentDate;

    @ApiModelProperty("inquire")
    private String inquire;

    @ApiModelProperty("applicationStatusCode")
    private Integer applicationStatusCode;

    @ApiModelProperty("receiptStatus")
    private Integer receiptStatus;

    @ApiModelProperty("noPPMLama")
    private String noPPMLama;

    @ApiModelProperty("noPPPLama")
    private String noPPPLama;

    @ApiModelProperty("bankName")
    private String bankName;

    @ApiModelProperty("bankReferenceNo")
    private String bankReferenceNo;

    @ApiModelProperty("ro")
    private String ro;

    @ApiModelProperty("roBatal")
    private String roBatal;

    @ApiModelProperty("roUpdate")
    private String roUpdate;

    @ApiModelProperty("akuiAjk")
    private String akuiAjk;

    @ApiModelProperty("bubar")
    private Boolean bubar;

    @ApiModelProperty("appealStatus")
    private Integer appealStatus;

    @ApiModelProperty("originName")
    private String originName;

    @ApiModelProperty("originAddress")
    private String originAddress;

    @ApiModelProperty("originStateCode")
    private String originStateCode;

    @ApiModelProperty("originDistrictCode")
    private String originDistrictCode;

    @ApiModelProperty("originSmallDistrictCode")
    private String originSmallDistrictCode;

    @ApiModelProperty("originPostcode")
    private String originPostcode;

    @ApiModelProperty("originCity")
    private String originCity;

    @ApiModelProperty("notis1")
    private String notis1;

    @ApiModelProperty("migrateStat")
    private Integer migrateStat;

    @ApiModelProperty("migrateAjk")
    private String migrateAjk;

    @ApiModelProperty("migrateUndang")
    private String migrateUndang;

    @ApiModelProperty("tarikhAlih")
    private LocalDate tarikhAlih;

    @ApiModelProperty("replyNotis")
    private String replyNotis;

    @ApiModelProperty("noteRo")
    private String noteRo;

    @ApiModelProperty("statBebas")
    private Boolean statBebas;

    @ApiModelProperty("statPindaKecaw")
    private Boolean statPindaKecaw;

    @ApiModelProperty("appealStatement")
    private String appealStatement;

    @ApiModelProperty("reconcileDate")
    private LocalDate reconcileDate;

    @ApiModelProperty("flatPadam")
    private Boolean flatPadam;

    @ApiModelProperty("rujuk")
    private String rujuk;

    @ApiModelProperty("benarAjk")
    private Boolean benarAjk;

    @ApiModelProperty("isQueried")
    private Integer isQueried;

    @ApiModelProperty("approvalReviewed")
    private Boolean approvalReviewed;

    @ApiModelProperty("branches")
    private List<Branch> branches;
}