package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class RoAppealApproval extends BaseEntity {

    @ApiModelProperty("appealId")
    private Long appealId;

    @ApiModelProperty("societyId")
    private Long societyId;

    @ApiModelProperty("societyNo")
    private String societyNo;

    @ApiModelProperty("decision")
    private Long decision;

    @ApiModelProperty("decisionNotes")
    private String decisionNotes;

    @ApiModelProperty("decisionDate")
    private LocalDate decisionDate;

    @ApiModelProperty("rejectReason")
    private String rejectReason;

    @ApiModelProperty("letterNo")
    private String letterNo;

    @ApiModelProperty("letter")
    private String letter;

    @ApiModelProperty("approvedBy")
    private Long approvedBy;

    @ApiModelProperty("recommendPpp")
    private String recommendPpp;

    @ApiModelProperty("recommendByPpp")
    private String recommendByPpp;

    @ApiModelProperty("recommendPppDate")
    private LocalDate recommendPppDate;

    @ApiModelProperty("notesPpp")
    private String notesPpp;

    @ApiModelProperty("recommendKpp")
    private String recommendKpp;

    @ApiModelProperty("recommendByKpp")
    private String recommendByKpp;

    @ApiModelProperty("recommendKppDate")
    private LocalDate recommendKppDate;

    @ApiModelProperty("notesKpp")
    private String notesKpp;

    @ApiModelProperty("recommendDato")
    private String recommendDato;

    @ApiModelProperty("recommendByDato")
    private String recommendByDato;

    @ApiModelProperty("recommendDatoDate")
    private LocalDate recommendDatoDate;

    @ApiModelProperty("notesDato")
    private String notesDato;

    @ApiModelProperty("recommendFinal")
    private String recommendFinal;

    @ApiModelProperty("recommendFinalBy")
    private String recommendFinalBy;

    @ApiModelProperty("recommendFinalDate")
    private LocalDate recommendFinalDate;

    @ApiModelProperty("notesFinal")
    private String notesFinal;

    @ApiModelProperty("kdnReview")
    private String kdnReview;

    @ApiModelProperty("reviewDate")
    private LocalDate reviewDate;

    @ApiModelProperty("kdnDecision")
    private String kdnDecision;

    @ApiModelProperty("recommendByKdn")
    private String recommendByKdn;

    @ApiModelProperty("kdnSend")
    private String kdnSend;

    @ApiModelProperty("kdnSendDate")
    private LocalDate kdnSendDate;

    @ApiModelProperty("kdnRecord")
    private String kdnRecord;

    @ApiModelProperty("notesKdn")
    private String notesKdn;

    @ApiModelProperty("queryReceiver")
    private String queryReceiver;

    @ApiModelProperty("notesQuery")
    private String notesQuery;

    @ApiModelProperty("queryAnswer")
    private String queryAnswer;
}
