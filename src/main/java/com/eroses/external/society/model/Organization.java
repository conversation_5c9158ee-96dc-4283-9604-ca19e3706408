package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Organization {
    private Society society;
    private List<Meeting> meeting;
    private List<Committee> societyCommittee;
    private List<NonCitizenCommittee> societyNonCitizenCommittee;
}
