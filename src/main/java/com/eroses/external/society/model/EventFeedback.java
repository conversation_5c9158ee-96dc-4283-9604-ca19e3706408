package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class EventFeedback extends BaseEntity implements Serializable {
    @ApiModelProperty("event_feedback_scale_code")
    public String eventFeedbackScaleCode;

    @ApiModelProperty("feedback_question_id")
    public Long feedbackQuestionId;

    @ApiModelProperty("event_attendees_id")
    public Long eventAttendeesId;
}
