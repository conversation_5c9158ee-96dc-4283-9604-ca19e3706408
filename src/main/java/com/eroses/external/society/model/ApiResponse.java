package com.eroses.external.society.model;

import com.eroses.external.society.model.enums.ErrorCode;
import com.eroses.external.society.model.enums.ErrorMessage;
import com.eroses.external.society.model.enums.ExceptionMessage;
import com.eroses.external.society.model.enums.SuccessMessage;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Getter
@Setter
@NoArgsConstructor
public class ApiResponse<T> implements Serializable {
    private String status;
    private int code;
    private String msg;
    private String msgCode;
    private T data;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime timeStamp;

    // Constructor with all fields
    public ApiResponse(String status, int code, String msg, String msgCode, T data, LocalDateTime timeStamp) {
        this.status = status;
        this.code = code;
        this.msg = msg;
        this.msgCode = msgCode;
        this.data = data;
        this.timeStamp = timeStamp;
    }

    // Backward compatible constructor (without msgCode)
    public ApiResponse(String status, int code, String msg, T data, LocalDateTime timeStamp) {
        this.status = status;
        this.code = code;
        this.msg = msg;
        this.msgCode = "LEGACY_" + status;
        this.data = data;
        this.timeStamp = timeStamp;
    }

    public ApiResponse(String msg, T data) {
        this.status = "SUCCESS";
        this.code = 200;
        this.msg = msg;
        this.msgCode = "SUCCESS_DEFAULT";
        this.data = data;
        this.timeStamp = LocalDateTime.now();
    }

    public ApiResponse(T data) {
        this.status = "SUCCESS";
        this.code = 200;
        this.msg = null;
        this.msgCode = "SUCCESS_DEFAULT";
        this.data = data;
        this.timeStamp = LocalDateTime.now();
    }

    public static <T> ApiResponse<T> ok(T data){
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("SUCCESS");
        apiResponse.setCode(200);
        apiResponse.setMsg(null);
        apiResponse.setMsgCode("SUCCESS_OK");
        apiResponse.setData(data);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> ok(String msg,T data){
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("SUCCESS");
        apiResponse.setCode(200);
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode("SUCCESS_OK");
        apiResponse.setData(data);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }
    public static <T> ApiResponse<T> okCreated(String msg,T data){
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("SUCCESS");
        apiResponse.setCode(201);
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode("SUCCESS_CREATED");
        apiResponse.setData(data);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }


    public static <T> ApiResponse<T> okEmpty(String msg){
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("SUCCESS");
        apiResponse.setCode(204);
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode("SUCCESS_EMPTY");
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> fail(ErrorCode errorCode){
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(errorCode.getCode());
        apiResponse.setMsg(errorCode.getMessage());
        apiResponse.setMsgCode("ERROR_CODE_" + errorCode.getCode());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> fail(ErrorCode errorCode, String msg){
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(errorCode.getCode());
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode("ERROR_CODE_" + errorCode.getCode());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> fail(HttpStatus errorCode){
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(errorCode.value());
        apiResponse.setMsg(errorCode.getReasonPhrase());
        apiResponse.setMsgCode("ERROR_HTTP_" + errorCode.value());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> fail(HttpStatus errorCode, String msg){
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(errorCode.value());
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode("ERROR_HTTP_" + errorCode.value());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> fail(HttpStatus errorCode, String msg, T data){
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(errorCode.value());
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode("ERROR_HTTP_" + errorCode.value());
        apiResponse.setData(data);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> failAndRollback(HttpStatus errorCode, String msg, T data){
        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(errorCode.value());
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode("ERROR_HTTP_" + errorCode.value());
        apiResponse.setData(data);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> failAndRollback(HttpStatus errorCode, String msg){
        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(errorCode.value());
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode("ERROR_HTTP_" + errorCode.value());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> fail(int errorCode, T data){
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(errorCode);
        apiResponse.setMsg(null);
        apiResponse.setMsgCode("ERROR_" + errorCode);
        apiResponse.setData(data);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> error(String errorCode, String errorMessage) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(400);
        apiResponse.setMsg(errorCode + ": " + errorMessage);
        apiResponse.setMsgCode("ERROR_CUSTOM");
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> error(String message) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(400);
        apiResponse.setMsg(message);
        apiResponse.setMsgCode("ERROR_GENERAL");
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> error(ErrorMessage errorMessage) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(400);
        apiResponse.setMsg(errorMessage.getBmMessage());
        apiResponse.setMsgCode(errorMessage.name());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> success(String message, T data) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("SUCCESS");
        apiResponse.setCode(200);
        apiResponse.setMsg(message);
        apiResponse.setMsgCode("SUCCESS_GENERAL");
        apiResponse.setData(data);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> success(SuccessMessage successMessage, T data) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("SUCCESS");
        apiResponse.setCode(200);
        apiResponse.setMsg(successMessage.getBmMessage());
        apiResponse.setMsgCode(successMessage.name());
        apiResponse.setData(data);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    // New methods with msgCode support for SuccessMessage
    public static <T> ApiResponse<T> okCreated(SuccessMessage successMessage, T data) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("SUCCESS");
        apiResponse.setCode(201);
        apiResponse.setMsg(successMessage.getBmMessage());
        apiResponse.setMsgCode(successMessage.name());
        apiResponse.setData(data);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> okEmpty(SuccessMessage successMessage) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("SUCCESS");
        apiResponse.setCode(204);
        apiResponse.setMsg(successMessage.getBmMessage());
        apiResponse.setMsgCode(successMessage.name());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    // New methods with msgCode support for ErrorMessage
    public static <T> ApiResponse<T> fail(ErrorMessage errorMessage) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(400);
        apiResponse.setMsg(errorMessage.getBmMessage());
        apiResponse.setMsgCode(errorMessage.name());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> fail(ErrorMessage errorMessage, T data) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(400);
        apiResponse.setMsg(errorMessage.getBmMessage());
        apiResponse.setMsgCode(errorMessage.name());
        apiResponse.setData(data);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> failAndRollback(ErrorMessage errorMessage) {
        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(400);
        apiResponse.setMsg(errorMessage.getBmMessage());
        apiResponse.setMsgCode(errorMessage.name());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> failAndRollback(ErrorMessage errorMessage, T data) {
        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(400);
        apiResponse.setMsg(errorMessage.getBmMessage());
        apiResponse.setMsgCode(errorMessage.name());
        apiResponse.setData(data);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    // New overloaded methods for backward compatibility with enum msgCode parameter

    public static <T> ApiResponse<T> ok(String msg, T data, SuccessMessage successMessage) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("SUCCESS");
        apiResponse.setCode(200);
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode(successMessage.name());
        apiResponse.setData(data);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> okCreated(String msg, T data, SuccessMessage successMessage) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("SUCCESS");
        apiResponse.setCode(201);
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode(successMessage.name());
        apiResponse.setData(data);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> okEmpty(String msg, SuccessMessage successMessage) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("SUCCESS");
        apiResponse.setCode(204);
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode(successMessage.name());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> fail(HttpStatus errorCode, String msg, ErrorMessage errorMessage) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(errorCode.value());
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode(errorMessage.name());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> fail(HttpStatus errorCode, String msg, T data, ErrorMessage errorMessage) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(errorCode.value());
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode(errorMessage.name());
        apiResponse.setData(data);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> error(String message, ErrorMessage errorMessage) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(400);
        apiResponse.setMsg(message);
        apiResponse.setMsgCode(errorMessage.name());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> failAndRollback(HttpStatus errorCode, String msg, boolean rollback, ErrorMessage errorMessage) {
        if (rollback) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(errorCode.value());
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode(errorMessage.name());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    // Methods for ExceptionMessage enum support - using different method names to avoid conflicts
    public static <T> ApiResponse<T> failWithException(HttpStatus errorCode, String msg, ExceptionMessage exceptionMessage) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(errorCode.value());
        apiResponse.setMsg(msg);
        apiResponse.setMsgCode(exceptionMessage.name());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }

    public static <T> ApiResponse<T> failWithException(ExceptionMessage exceptionMessage) {
        ApiResponse<T> apiResponse = new ApiResponse<>();
        apiResponse.setStatus("ERROR");
        apiResponse.setCode(400);
        apiResponse.setMsg(exceptionMessage.getMessage());
        apiResponse.setMsgCode(exceptionMessage.name());
        apiResponse.setData(null);
        apiResponse.setTimeStamp(LocalDateTime.now());
        return apiResponse;
    }
}
