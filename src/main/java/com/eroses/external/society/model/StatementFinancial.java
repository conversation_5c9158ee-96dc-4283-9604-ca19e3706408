package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class StatementFinancial extends BaseEntity {
    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("Statement ID")
    private Long statementId;

    @ApiModelProperty("Society ID")
    private Long societyId;

    @ApiModelProperty("Society Number")
    private String societyNo;

    @ApiModelProperty("Branch ID")
    private Long branchId;

    @ApiModelProperty("Branch Number")
    private String branchNo;

    @ApiModelProperty("Financial Declaration")
    private Boolean financialDeclaration;

    @ApiModelProperty("Fee Income")
    private BigDecimal feeIncome;

    @ApiModelProperty("Fee Income from Members")
    private BigDecimal feeIncomeMember;

    @ApiModelProperty("Fund Income")
    private BigDecimal fundIncome;

    @ApiModelProperty("Donation Income")
    private BigDecimal donationIncome;

    @ApiModelProperty("Others Income")
    private BigDecimal othersIncome;

    @ApiModelProperty("Food Revenue")
    private BigDecimal foodRevenue;

    @ApiModelProperty("Book Revenue")
    private BigDecimal bookRevenue;

    @ApiModelProperty("Service Revenue")
    private BigDecimal serviceRevenue;

    @ApiModelProperty("Other Revenue")
    private BigDecimal otherRevenue;

    @ApiModelProperty("Rental Investment")
    private BigDecimal rentalInvestment;

    @ApiModelProperty("Dividend Investment")
    private BigDecimal dividendInvestment;

    @ApiModelProperty("Interest Investment")
    private BigDecimal interestInvestment;

    @ApiModelProperty("Property Investment")
    private BigDecimal propertyInvestment;

    @ApiModelProperty("Other Investment")
    private BigDecimal otherInvestment;

    @ApiModelProperty("Government Grant")
    private BigDecimal govGrant;

    @ApiModelProperty("Private Grant")
    private BigDecimal privateGrant;

    @ApiModelProperty("Individual Grant")
    private BigDecimal individualGrant;

    @ApiModelProperty("Other Grants")
    private BigDecimal otherGrant;

    @ApiModelProperty("Other Income")
    private BigDecimal otherIncome;

    @ApiModelProperty("Total Income")
    private BigDecimal totalIncome;

    @ApiModelProperty("Summary of Expenses")
    private BigDecimal sumbExpense;

    @ApiModelProperty("Donation Expenses")
    private BigDecimal donationExpense;

    @ApiModelProperty("Tax Expenses")
    private BigDecimal taxExpense;

    @ApiModelProperty("Other Expenses")
    private BigDecimal otherExpenses;

    @ApiModelProperty("General Welfare Expenses")
    private BigDecimal generalWelfare;

    @ApiModelProperty("Death Welfare Expenses")
    private BigDecimal deathWelfare;

    @ApiModelProperty("Gift Welfare Expenses")
    private BigDecimal giftWelfare;

    @ApiModelProperty("Scholarship Welfare Expenses")
    private BigDecimal scholarshipWelfare;

    @ApiModelProperty("Zakat Welfare Expenses")
    private BigDecimal zakatWelfare;

    @ApiModelProperty("Donation Welfare Expenses")
    private BigDecimal donationWelfare;

    @ApiModelProperty("Organized Activity Costs")
    private BigDecimal organizedActivity;

    @ApiModelProperty("Promotional Activity Costs")
    private BigDecimal promoActivity;

    @ApiModelProperty("Banquet Activity Costs")
    private BigDecimal banquetActivity;

    @ApiModelProperty("Tour Activity Costs")
    private BigDecimal tourActivity;

    @ApiModelProperty("Rental Costs")
    private BigDecimal rentalCost;

    @ApiModelProperty("Utility Costs")
    private BigDecimal utilityCost;

    @ApiModelProperty("Supply Costs")
    private BigDecimal supplyCost;

    @ApiModelProperty("Other Costs")
    private BigDecimal otherCost;

    @ApiModelProperty("Miscellaneous Expenses")
    private BigDecimal otherExpense;

    @ApiModelProperty("Total Expenses")
    private BigDecimal totalExpense;

    @ApiModelProperty("Building Asset Value")
    private BigDecimal buildingAsset;

    @ApiModelProperty("Investment Asset Value")
    private BigDecimal investmentAsset;

    @ApiModelProperty("intangible Asset Value")
    private BigDecimal intangibleAsset;

    @ApiModelProperty("Assets on Hand")
    private BigDecimal assetOnHand;

    @ApiModelProperty("Bank Asset Value")
    private BigDecimal bankAsset;

    @ApiModelProperty("Account Asset Value")
    private BigDecimal accountAsset;

    @ApiModelProperty("Inventory Asset Value")
    private BigDecimal inventoryAsset;

    @ApiModelProperty("Investment Asset Value")
    private BigDecimal investAsset;

    @ApiModelProperty("Deposit Asset Value")
    private BigDecimal depositAsset;

    @ApiModelProperty("Tax Asset Value")
    private BigDecimal taxAsset;

    @ApiModelProperty("Total Asset Value")
    private BigDecimal totalAsset;

    @ApiModelProperty("Creditor Liability")
    private BigDecimal creditorLiability;

    @ApiModelProperty("Tax Liability")
    private BigDecimal taxLiability;

    @ApiModelProperty("Loan Liability")
    private BigDecimal loanLiability;

    @ApiModelProperty("Debt Liability")
    private BigDecimal debtLiability;

    @ApiModelProperty("Deferred Tax Liability")
    private BigDecimal deferredTaxLiability;

    @ApiModelProperty("Borrow Liability")
    private BigDecimal borrowLiability;

    @ApiModelProperty("Total Liability")
    private BigDecimal totalLiability;

    @ApiModelProperty("Application Status Code")
    private String applicationStatusCode;

    @ApiModelProperty("Card Costs")
    private BigDecimal cardCost;

    @ApiModelProperty("Investment Activity Costs")
    private BigDecimal investmentActivity;

    @ApiModelProperty("Fee Activity Costs")
    private BigDecimal feeActivity;

    @ApiModelProperty("Other Activity Costs")
    private BigDecimal otherActivity;

    @ApiModelProperty("Salary Costs")
    private BigDecimal salaryCost;

    @ApiModelProperty("Bonus Costs")
    private BigDecimal bonusCost;

    @ApiModelProperty("KWSP Costs")
    private BigDecimal kwspCost;

    @ApiModelProperty("Insurance Costs")
    private BigDecimal insuranceCost;

    @ApiModelProperty("Maintenance Costs")
    private BigDecimal maintenanceCost;

    @ApiModelProperty("Renovation Costs")
    private BigDecimal renovationCost;

    @ApiModelProperty("Transportation Costs")
    private BigDecimal transportationCost;

    @ApiModelProperty("Photocopy Costs")
    private BigDecimal photocopyCost;

    @ApiModelProperty("Bank Charges Costs")
    private BigDecimal bankChargeCost;

    @ApiModelProperty("Asset Number")
    private String assetNo;

    @ApiModelProperty("Land Asset Value")
    private BigDecimal landAsset;

    @ApiModelProperty("Vehicle Asset Value")
    private BigDecimal vehicleAsset;

    @ApiModelProperty("Machine Asset Value")
    private BigDecimal machineAsset;

    @ApiModelProperty("Furniture Asset Value")
    private BigDecimal furnitureAsset;

    @ApiModelProperty("Office Asset Value")
    private BigDecimal officeAsset;

    @ApiModelProperty("Uniform Costs")
    private BigDecimal uniformCost;

    @ApiModelProperty("Expense Number")
    private String expenseNo;
}
