package com.eroses.external.society.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum DocumentTypeEnum {
    SOCIETY(1, "society", "Society Registration"),
    BRANCH(2, "branch", "Branch Registration"),
    APPEAL(3, "appeal", "Appeal case"),
    MEETING(4, "meeting-minutes", "Meeting information"),
    AMENDMENT(5, "amendment", "Amendment application"),
    STATEMENT(6, "statement", "Statement Creation"),
    LIQUIDATION(7, "liquidation", "Liquidation"),
    CITIZEN_COMMITTEE(8, "citizen-committee", "Citizen committee documents"),
    NON_CITIZEN_COMMITTEE(9, "non-citizen-committee", "Non citizen committee documents"),
    FEEDBACK(10, "feedback", "Feedbacks"),
    SUPPORTING_DOCUMENT(11, "dokumen-sokongan", "Supporting documents"),
    PROFILE_PICTURE(12, "profile-picture", "User's profile picture"),
    TAKWIM_BANNER(13, "takwim-banner", "Takwim banner image"),
    EVENT_CERT(14, "event-cert", "Event certificate"),
    FINANCIAL_STATEMENT(15, "financial-statement", "Financial statement"),
    ACTIVITY_REPORT_STATEMENT(16, "activity-report-statement", "Activity report statement"),
    SOCIETY_CANCELLATION_REVERT(17, "society-cancellation-revert", "Society cancellation revert"),
    NOTICE(18, "notice", "Notice"),
    TRAINING_POSTER(19, "training-poster", "Training Poster"),
    TRAINING_MATERIAL(20, "training-material", "Training Material"),
    EXTERNAL_DOCUMENT(21, "external-document", "External Document"),
    LOGO_LARANGAN(22, "forbidden-logo", "Logo Larangan")
    ;

    private final Integer code;
    private final String type;
    private final String description;

    public static DocumentTypeEnum getDocumentEnum(Integer code) {
        for (DocumentTypeEnum documentTypeEnum : DocumentTypeEnum.values()) {
            if (Objects.equals(documentTypeEnum.getCode(), code)) {
                return documentTypeEnum;
            }
        }
        return null;
    }

    public static String getDocumentType(Integer code) {
        for (DocumentTypeEnum documentTypeEnum : DocumentTypeEnum.values()) {
            if (Objects.equals(documentTypeEnum.getCode(), code)) {
                return documentTypeEnum.getType();
            }
        }
        return null;
    }

    public static Integer getDocumentCode(String type) {
        for (DocumentTypeEnum documentTypeEnum : DocumentTypeEnum.values()) {
            if (Objects.equals(documentTypeEnum.getType(), type)) {
                return documentTypeEnum.getCode();
            }
        }
        return null;
    }
}
