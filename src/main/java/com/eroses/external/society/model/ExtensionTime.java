package com.eroses.external.society.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ExtensionTime extends BaseEntity {

    @ApiModelProperty
    @Column(name = "society_id", nullable = false)
    private Long societyId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "society_id", insertable = false, updatable = false)
    private Society society;

    @ApiModelProperty
    @Column(name = "society_no", length = 50)
    private String societyNo;

    @ApiModelProperty
    @Column(name = "branch_id", nullable = false)
    private Long branchId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "branch_id", insertable = false, updatable = false)
    private Branch branch;

    @ApiModelProperty
    @Column(name = "branch_no", length = 50)
    private String branchNo;

    @ApiModelProperty
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "application_date")
    private LocalDateTime applicationDate;

    @ApiModelProperty
    @Column(name = "decision", length = 50)
    private String decision;

    @ApiModelProperty
    @Column(name = "extension_days")
    private Integer extensionDays;

    @ApiModelProperty
    @Column(name = "approved_extension_days")
    private Integer approvedExtensionDays;

    @ApiModelProperty
    @Column(name = "note", length = 100)
    private String note;

    @ApiModelProperty
    @Column(name = "acknowledgement")
    private Boolean acknowledgement;

    @ApiModelProperty
    @Column(name = "application_status_code")
    private Integer applicationStatusCode;

    @ApiModelProperty
    @Column(name = "ro", length = 50)
    private String ro;


}