package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;


@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class FeedbackTrail extends BaseEntity {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("feedbackId")
    private Long feedbackId;

    @ApiModelProperty("feedbackRecipientId")
    private Long feedbackRecipientId;

    @ApiModelProperty("feedbackRecipientName")
    private String feedbackRecipientName;

    @ApiModelProperty("feedbackRecipientBranchId")
    private Long feedbackRecipientBranchId;

    @ApiModelProperty("jppmbranchid")
    private Long jppmBranchId;

    @ApiModelProperty("feedbackAction")
    private String feedbackAction;

    @ApiModelProperty("complaintLevel")
    private String complaintLevel;

    @ApiModelProperty("feedbackNote")
    private String feedbackNote;

    @ApiModelProperty("status")
    private Integer status;
}

