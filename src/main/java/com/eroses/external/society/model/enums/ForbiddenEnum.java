package com.eroses.external.society.model.enums;

public enum ForbiddenEnum {
    SENARAI_LARANGAN("SENARAI_LARANGAN", "Senarai larangan"),
    SENARAI_KELABU("SENARAI_KELABU", "Senarai kelabu");

    private final String value;
    private final String description;

    ForbiddenEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }
    public String getDescription() {
        return description;
    }
    public static ForbiddenEnum getForbiddenEnum(String value) {
        for (ForbiddenEnum forbiddenEnum : ForbiddenEnum.values()) {
            if (forbiddenEnum.getValue().equals(value)) {
                return forbiddenEnum;
            }
        }
        return null;
    }
}
