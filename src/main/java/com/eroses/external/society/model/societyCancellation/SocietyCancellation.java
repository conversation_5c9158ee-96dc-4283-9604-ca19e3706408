package com.eroses.external.society.model.societyCancellation;

import com.eroses.external.society.model.BaseEntity;
import com.eroses.external.society.model.Branch;
import com.eroses.external.society.model.Society;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class SocietyCancellation extends BaseEntity {

    @OneToOne(fetch = FetchType.LAZY)
    private Society society;

    @ApiModelProperty("societyId")
    private Long societyId;

    @ApiModelProperty("societyNo")
    private String societyNo;

    @OneToOne(fetch = FetchType.LAZY)
    private Branch branch;

    @ApiModelProperty("branchId")
    private Long branchId;

    @ApiModelProperty("branchNo")
    private String branchNo;

    @ApiModelProperty("cancelledDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate cancelledDate;

    @ApiModelProperty("section")
    private String section;

    @ApiModelProperty("reason")
    private String reason;

    /**
     * Flag to indicate if cancellation process completed
     */
    @ApiModelProperty("isCancelled")
    private boolean isCancelled;

    /**
     * Flag to indicate if user submit to revert (revert process could be not completed yet)
     */
    @ApiModelProperty("isReverted")
    private boolean isReverted;

    /**
     * Flag to indicate if cancellation is due to liquidation
     */
    @ApiModelProperty("isLiquidation")
    private boolean isLiquidation;
}
