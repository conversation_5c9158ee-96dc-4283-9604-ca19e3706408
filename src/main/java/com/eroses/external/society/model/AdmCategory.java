package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class AdmCategory extends BaseEntity {
    @ApiModelProperty("id")
    public Long id;

    @ApiModelProperty("pid")
    public Long pid;

    @ApiModelProperty("categoryCodeJppm")
    public String categoryCodeJppm;

    @ApiModelProperty("categoryNameEn")
    public String categoryNameEn;

    @ApiModelProperty("categoryNameBm")
    public String categoryNameBm;

    @ApiModelProperty("level")
    public Long level;

    @ApiModelProperty("cawStat")
    public String cawStat;

    @ApiModelProperty("status")
    public Integer status;

}

