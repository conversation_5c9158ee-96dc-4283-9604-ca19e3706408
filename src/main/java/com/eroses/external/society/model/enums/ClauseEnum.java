package com.eroses.external.society.model.enums;

import lombok.Getter;

@Getter
public enum ClauseEnum {

    CLAUSE_1(1, 1, 1, "<PERSON><PERSON>"),
    CLAUSE_2(2, 1, 2, "<PERSON>mp<PERSON> Urusan"),
    CLAUSE_3(3, 1, 3, "<PERSON><PERSON><PERSON>"),
    CLAUSE_4(4, 1, 4, "<PERSON><PERSON><PERSON>"),
    CLAUSE_5(5, 1, 5, "<PERSON><PERSON><PERSON><PERSON><PERSON> dan <PERSON>"),
    CLAUSE_6(6, 1, 6, "Jawatan<PERSON><PERSON>"),
    CLAUSE_7(7, 1, 7, "Tugas-tugas Jawatan<PERSON>asa"),
    CLAUSE_8(8, 1, 8, "<PERSON><PERSON>wang<PERSON>"),
    CLAUSE_9(9, 1, 9, "<PERSON><PERSON><PERSON><PERSON>"),
    CLAUSE_10(10, 1, 10, "<PERSON><PERSON><PERSON><PERSON>"),
    CLAUSE_11(11, 1, 11, "<PERSON><PERSON><PERSON><PERSON> harta dan/atau pegawai awam"),
    CLAUSE_12(12, 1, 12, "<PERSON><PERSON>uarat <PERSON>"),
    CLAUSE_13(13, 1, 13, "<PERSON><PERSON><PERSON>"),
    CLAUSE_14(14, 1, 14, "<PERSON><PERSON><PERSON><PERSON>"),
    CLAUSE_15(15, 1, 15, "<PERSON>asi<PERSON>/<PERSON>aung"),
    CLAUS<PERSON>_16(16, 1, 16, "Larangan"),
    CLAUS<PERSON>_17(17, 1, 17, "<PERSON><PERSON>bu<PERSON>n"),
    <PERSON>LA<PERSON><PERSON>_18(18, 1, 18, "<PERSON>era, <PERSON>ang dan <PERSON>cana"),
    <PERSON>LAUS<PERSON>_19(19, 2, 1, "<PERSON>a"),
    <PERSON><PERSON><PERSON><PERSON>_20(20, 2, 2, "Tempat <PERSON><PERSON><PERSON>"),
    <PERSON>LA<PERSON><PERSON>_21(21, 2, 3, "Matlamat"),
    CLAUSE_22(22, 2, 4, "Keahlian"),
    CLAUSE_23(23, 2, 5, "Pemberhentian dan Pemecatan Ahli"),
    CLAUSE_24(24, 2, 6, "Jawatankuasa Induk"),
    CLAUSE_25(25, 2, 7, "Tugas-tugas Jawatankuasa Induk"),
    CLAUSE_26(26, 2, 8, "Sumber Kewangan"),
    CLAUSE_27(27, 2, 9, "Pengurusan Kewangan"),
    CLAUSE_28(28, 2, 10, "Juruaudit"),
    CLAUSE_29(29, 2, 11, "Pegawai harta dan/atau pegawai awam"),
    CLAUSE_30(30, 2, 12, "Mesyuarat Agung"),
    CLAUSE_31(31, 2, 13, "Pembubaran"),
    CLAUSE_32(32, 2, 14, "Penubuhan dan Pembubaran Cawangan-cawangan"),
    CLAUSE_33(33, 2, 15, "Mesyuarat Agung Cawangan"),
    CLAUSE_34(34, 2, 16, "Jawatankuasa Cawangan"),
    CLAUSE_35(35, 2, 17, "Tugas-tugas Jawatankuasa Cawangan"),
    CLAUSE_36(36, 2, 18, "Kewangan Cawangan"),
    CLAUSE_37(37, 2, 19, "Peruntukan-peruntukan Umum Mengenai Cawangan"),
    CLAUSE_38(38, 2, 20, "Pindaan Perlembagaan"),
    CLAUSE_39(39, 2, 21, "Tafsiran Perlembagaan"),
    CLAUSE_40(40, 2, 22, "Larangan"),
    CLAUSE_41(41, 2, 23, "Bendera, Lambang dan Lencana"),
    CLAUSE_42(42, 3, 1, "Nama"),
    CLAUSE_43(43, 3, 2, "Tempat Urusan"),
    CLAUSE_44(44, 3, 3, "Matlamat"),
    CLAUSE_45(45, 3, 4, "Keahlian"),
    CLAUSE_46(46, 3, 5, "Pemberhentian dan Pemecatan Ahli"),
    CLAUSE_47(47, 3, 6, "Jawatankuasa"),
    CLAUSE_48(48, 3, 7, "Tugas-tugas Jawatankuasa"),
    CLAUSE_49(49, 3, 8, "Sumber Kewangan"),
    CLAUSE_50(50, 3, 9, "Pengurusan Kewangan"),
    CLAUSE_51(51, 3, 10, "Juruaudit"),
    CLAUSE_52(52, 3, 11, "Pegawai harta dan/atau pegawai awam"),
    CLAUSE_53(53, 3, 12, "Mesyuarat Agung"),
    CLAUSE_54(54, 3, 13, "Pindaan Perlembagaan"),
    CLAUSE_55(55, 3, 14, "Tafsiran Perlembagaan"),
    CLAUSE_56(56, 3, 15, "Penasihat/Penaung"),
    CLAUSE_57(57, 3, 16, "Larangan"),
    CLAUSE_58(58, 3, 17, "Pembubaran"),
    CLAUSE_59(59, 3, 18, "Bendera, Lambang dan Lencana"),
    CLAUSE_60(60, 4, 1, "Nama"),
    CLAUSE_61(61, 4, 2, "Tempat Urusan"),
    CLAUSE_62(62, 4, 3, "Matlamat"),
    CLAUSE_63(63, 4, 4, "Keahlian"),
    CLAUSE_64(64, 4, 5, "Pemberhentian dan Pemecatan Ahli"),
    CLAUSE_65(65, 4, 6, "Jawatankuasa Induk"),
    CLAUSE_66(66, 4, 7, "Tugas-tugas Jawatankuasa Induk"),
    CLAUSE_67(67, 4, 8, "Sumber Kewangan"),
    CLAUSE_68(68, 4, 9, "Pengurusan Kewangan"),
    CLAUSE_69(69, 4, 10, "Juruaudit"),
    CLAUSE_70(70, 4, 11, "Pegawai harta dan/atau pegawai awam"),
    CLAUSE_71(71, 4, 12, "Mesyuarat Agung"),
    CLAUSE_72(72, 4, 13, "Pembubaran"),
    CLAUSE_73(73, 4, 14, "Penubuhan dan Pembubaran Cawangan-cawangan"),
    CLAUSE_74(74, 4, 15, "Mesyuarat Agung Cawangan"),
    CLAUSE_75(75, 4, 16, "Jawatankuasa Cawangan"),
    CLAUSE_76(76, 4, 17, "Tugas-tugas Jawatankuasa Cawangan"),
    CLAUSE_77(77, 4, 18, "Pengurusan Kewangan Cawangan"),
    CLAUSE_78(78, 4, 19, "Peruntukan-peruntukan Umum Mengenai Cawangan"),
    CLAUSE_79(79, 4, 20, "Pindaan Perlembagaan"),
    CLAUSE_80(80, 4, 21, "Tafsiran Perlembagaan"),
    CLAUSE_81(81, 4, 22, "Larangan"),
    CLAUSE_82(82, 4, 23, "Bendera, Lambang dan Lencana"),
    CLAUSE_83(83, 5, 1, "Nama"),
    CLAUSE_84(84, 5, 2, "Tempat Urusan"),
    CLAUSE_85(85, 5, 3, "Matlamat"),
    CLAUSE_86(86, 5, 4, "Keahlian"),
    CLAUSE_87(87, 5, 5, "Jawatankuasa"),
    CLAUSE_88(88, 5, 6, "Tugas-tugas Jawatankuasa"),
    CLAUSE_89(89, 5, 7, "Sumber Kewangan"),
    CLAUSE_90(90, 5, 8, "Wang Tabungan"),
    CLAUSE_91(91, 5, 9, "Bantuan Kewangan Atau Nafkah"),
    CLAUSE_92(92, 5, 10, "Syarat Bantuan Bagi Yang Berkaitan Sahaja"),
    CLAUSE_93(93, 5, 11, "Juruaudit"),
    CLAUSE_94(94, 5, 12, "Pemegang Amanah"),
    CLAUSE_95(95, 5, 13, "Perkara Am"),
    CLAUSE_96(96, 5, 14, "Mesyuarat Agung"),
    CLAUSE_97(97, 5, 15, "Mesyuarat Agung Khas"),
    CLAUSE_98(98, 5, 16, "Pindaan Perlembagaan"),
    CLAUSE_99(99, 5, 17, "Tafsiran Perlembagaan Pertubuhan"),
    CLAUSE_100(100, 5, 18, "Larangan"),
    CLAUSE_101(101, 5, 19, "Pembubaran"),
    CLAUSE_102(102, 5, 20, "Bendera, Lambang dan Lencana"),
    CLAUSE_104(104, 6, 1, "Nama"),
    CLAUSE_105(105, 6, 2, "Tempat Urusan"),
    CLAUSE_106(106, 6, 3, "Mesyuarat Agung"),
    CLAUSE_107(107, 6, 4, "Jawatankuasa"),
    CLAUSE_108(108, 6, 5, "Pengurusan Kewangan"),
    CLAUSE_109(109, 6, 6, "Juruaudit"),
    CLAUSE_110(110, 7, 1, "Nama"),
    CLAUSE_111(111, 7, 2, "Tempat Urusan"),
    CLAUSE_112(112, 7, 3, "Mesyuarat Agung"),
    CLAUSE_113(113, 7, 4, "Jawatankuasa Induk"),
    CLAUSE_114(114, 7, 5, "Pengurusan Kewangan"),
    CLAUSE_115(115, 7, 6, "Juruaudit"),
    CLAUSE_116(116, 7, 7, "Mesyuarat Agung Cawangan"),
    CLAUSE_117(117, 7, 8, "Jawatankuasa Cawangan"),
    CLAUSE_118(118, 7, 9, "Kewangan Cawangan");

    private final int id;
    private final int constitutionType;
    private final int clauseNo;
    private final String clauseName;

    ClauseEnum(int id, int constitutionType, int clauseNo, String clauseName) {
        this.id = id;
        this.constitutionType = constitutionType;
        this.clauseNo = clauseNo;
        this.clauseName = clauseName;
    }

    public static ClauseEnum getClause(int id) {
        for (ClauseEnum clause : ClauseEnum.values()) {
            if (clause.id == id) {
                return clause;
            }
        }
        return null;
    }

    public static ClauseEnum getClauseByTypeAndNo(int type, int no) {
        for (ClauseEnum clause : ClauseEnum.values()) {
            if (clause.constitutionType == type && clause.clauseNo == no) {
                return clause;
            }
        }
        return null;
    }

    public static ClauseEnum getClauseByTypeAndName(int type, String name) {
        for (ClauseEnum clause : ClauseEnum.values()) {
            if (clause.constitutionType == type && clause.clauseName.equals(name)) {
                return clause;
            }
        }
        return null;
    }
}
