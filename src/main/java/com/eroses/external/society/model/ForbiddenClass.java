package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ForbiddenClass extends BaseEntity implements Serializable {
    @ApiModelProperty("remarks")
    public String remarks;

    @ApiModelProperty("active")
    public Boolean active;

    @ApiModelProperty("active_remarks")
    public String activeRemarks;

    @ApiModelProperty("status")
    public int status;
}
