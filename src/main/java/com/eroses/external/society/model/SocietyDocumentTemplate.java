package com.eroses.external.society.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SocietyDocumentTemplate extends BaseEntity {

    @ApiModelProperty
    @Column(name = "society_id")
    private Long societyId;

    @ApiModelProperty
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "society_id", insertable = false, updatable = false)
    private Society society;

    @ApiModelProperty
    @Column(name = "document_template_id")
    private Long documentTemplateId;

    @ApiModelProperty
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "template_id", insertable = false, updatable = false)
    private SearchInformationDocumentTemplate searchInformationDocumentTemplate;

    @ApiModelProperty
    @Column(name = "assigned_at")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime assignedAt;

    @ApiModelProperty
    @Column(name = "status")
    private Integer status;
}