package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class EventCertificate extends BaseEntity implements Serializable {
    @ApiModelProperty("event_name")
    public String eventName;

    @ApiModelProperty("identification_no")
    public String identificationNo;

    @ApiModelProperty("start_date")
    public String startDate;

    @ApiModelProperty("end_date")
    public String endDate;

    @ApiModelProperty("name")
    public String name;
}