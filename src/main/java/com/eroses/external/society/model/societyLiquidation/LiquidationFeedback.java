package com.eroses.external.society.model.societyLiquidation;

import com.eroses.external.society.model.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LiquidationFeedback extends BaseEntity {
    private Long id;
    private Long liquidationId;
    private Long societyId;
    private String societyNo;
    private Long branchId;
    private String branchNo;
    private String icNo;
    private String feedback;
    private Integer nonCommittee;
    private String nonCommitteeYear;
    private Integer falseStatement;
    private Integer other;
    private String otherReason;
}