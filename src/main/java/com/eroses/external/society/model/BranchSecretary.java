package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class BranchSecretary extends BaseEntity {

    @ApiModelProperty("secretaryId")
    private Long secretaryId;

    @OneToOne(fetch = FetchType.LAZY)
    @ApiModelProperty("branchCommittee")
    private BranchCommittee branchCommittee;

    @ApiModelProperty("societyId")
    private Long societyId;

    @ApiModelProperty("societyNo")
    private String societyNo;

    @ApiModelProperty("branchId")
    private Long branchId;

    @ApiModelProperty("branchNo")
    private String branchNo;

    @ApiModelProperty("branch")
    private Branch branch;

    @ApiModelProperty("meetingId")
    private Long meetingId;

    @ApiModelProperty("meetingDate")
    private String meetingDate;

    @ApiModelProperty("meetingType")
    private String meetingType;

    @ApiModelProperty("reasonOfChange")
    private String reasonOfChange;

    @ApiModelProperty("replacementDate")
    private LocalDate replacementDate;
}
