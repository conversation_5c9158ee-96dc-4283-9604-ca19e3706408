package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackQuestion extends BaseEntity implements Serializable {
    @ApiModelProperty("question")
    public String question;

    @ApiModelProperty("published")
    public Boolean published;

}
