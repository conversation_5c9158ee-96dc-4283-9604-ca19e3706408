package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ForbiddenLogo extends ForbiddenClass implements Serializable {
    @ApiModelProperty("logo_url")
    public String logoUrl;
    @ApiModelProperty("logo_vector")
    public byte[] logoVector;
}
