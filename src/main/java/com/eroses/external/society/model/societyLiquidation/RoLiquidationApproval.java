package com.eroses.external.society.model.societyLiquidation;

import com.eroses.external.society.model.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class RoLiquidationApproval extends BaseEntity {
    private Long id;
    private Long liquidationId;
    private Long societyId;
    private String societyNo;
    private Long branchId;
    private String branchNo;
    private String decision;
    private String rejectReason;
    private String note;
    private String approvedBy;
    private LocalDate decisionDate;
}