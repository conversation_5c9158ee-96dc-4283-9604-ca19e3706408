package com.eroses.external.society.model;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class Paging<T> implements Serializable {
    private static final long serialVersionUID = 755183539178076901L;
    private Long total;
    private List<T> data;
    private Long pageNo;
    private Integer pageSize;

    public Paging() {
    }

    public Paging(Long total, List<T> data, Integer pageSize) {
        this.data = data;
        this.total = total;
        this.pageSize = pageSize;
        if (total > 0L && pageSize > 0) {
            long l = total % (long)pageSize;
            if (l == 0L) {
                this.pageNo = total / (long)pageSize;
            } else {
                this.pageNo = total / (long)pageSize + 1L;
            }
        } else {
            this.pageNo = 0L;
        }

    }

    public Paging(Long total, List<T> data) {
        this.data = data;
        this.total = total;
    }

    public List<T> getData() {
        return this.data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public Long getTotal() {
        return this.total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Boolean isEmpty() {
        return Objects.equals(0L, this.total) || this.data == null || this.data.isEmpty();
    }

    public Long getPageNo() {
        return this.pageNo;
    }

    public Paging pageCount(Long pageCount) {
        this.pageNo = pageCount;
        return this;
    }

    public Integer getPageSize() {
        return this.pageSize;
    }

    public Paging pageSize(Integer pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    public static <T> Paging<T> empty(Class<T> clazz) {
        List<T> emptyList = Collections.emptyList();
        return new Paging(0L, emptyList);
    }

    public static <T> Paging<T> empty() {
        return new Paging(0L, Collections.emptyList());
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof Paging)) {
            return false;
        } else {
            Paging other = (Paging)o;
            if (!Objects.equals(this.total, other.total)) {
                return false;
            } else {
                return Objects.equals(this.data, other.data);
            }
        }
    }

//    public int hashCode() {
//        int PRIME = true;
//        int result = 1;
//        result = result * 59 + (this.total == null ? 0 : this.total.hashCode());
//        result = result * 59 + (this.data == null ? 0 : this.data.hashCode());
//        return result;
//    }
}

