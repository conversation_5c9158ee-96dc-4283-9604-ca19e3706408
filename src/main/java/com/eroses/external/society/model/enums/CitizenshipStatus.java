package com.eroses.external.society.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum CitizenshipStatus {
    WARGANEGARA(1, "CITIZ<PERSON>"),
    BUKAN_WARAGANEGARA(2, "NON_CITIZEN");

    private final Integer code;
    private final String description;

    public static CitizenshipStatus getCitizenshipStatusCode(Integer code) {
        for (CitizenshipStatus citizenshipStatus : CitizenshipStatus.values()) {
            if (Objects.equals(citizenshipStatus.getCode(), code)) {
                return citizenshipStatus;
            }
        }
        return null;
    }
}
