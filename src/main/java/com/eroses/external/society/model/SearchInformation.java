package com.eroses.external.society.model;

import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.*;
import lombok.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SearchInformation extends BaseEntity {

    @ApiModelProperty
    @Column(name = "society_id")
    private Long societyId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "society_id", insertable = false, updatable = false)
    private Society society;

    @ApiModelProperty
    private String societyNo;

    @ApiModelProperty
    private String viewCertificate;

    @ApiModelProperty
    private String viewConstitution;

    @ApiModelProperty
    private String viewCommittee;

    @ApiModelProperty
    private String committeeYear;

    @ApiModelProperty
    private BigDecimal paymentTotal;

    @ApiModelProperty
    private Boolean acknowledgement;

    @ApiModelProperty
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acknowledgementDate;

    @ApiModelProperty
    private Long paymentId;

    @ApiModelProperty
    private String paymentMethod;

    @ApiModelProperty
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paymentDate;

    @ApiModelProperty
    private String receiptNo;

    @ApiModelProperty
    private Integer applicationStatusCode;

    @ApiModelProperty
    private String noPPPOld;

    @ApiModelProperty
    private String noPPMOld;

    @ApiModelProperty
    private String bankName;

    @ApiModelProperty
    private String bankReferenceNo;

    @ApiModelProperty
    private Integer receiptStatus;

    @ApiModelProperty
    private String ro;

    @ApiModelProperty
    private Long ePaymentId;

    @ApiModelProperty
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reconciliationDate;

    @ApiModelProperty
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approvedDate;

    @ApiModelProperty
    private Integer status;

}
