package com.eroses.external.society.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Committee extends Model implements Serializable {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("committeeTableOldId")
    private Long committeeTableOldId;

    @ApiModelProperty("jobCode")
    private String jobCode;

    @ApiModelProperty("societyId")
    private Long societyId;

    @ApiModelProperty("societyNo")
    private String societyNo;

    @ApiModelProperty("titleCode")
    private String titleCode;

    @ApiModelProperty("name")
    private String name;

    @ApiModelProperty("gender")
    private String gender;

    @ApiModelProperty("nationalityStatus")
    private String nationalityStatus;

    @ApiModelProperty("identificationType")
    private String identificationType;

    @ApiModelProperty("identificationNo")
    private String identificationNo;

    @ApiModelProperty("dateOfBirth")
    @JsonFormat(pattern="yyyy-MM-dd")
    private LocalDate dateOfBirth;

    @ApiModelProperty("placeOfBirth")
    private String placeOfBirth;

    @ApiModelProperty("designationCode")
    private String designationCode;

    @ApiModelProperty("otherDesignationCode")
    private String otherDesignationCode;

    @ApiModelProperty("positionArrangement")
    private Integer positionArrangement;

    @ApiModelProperty("employerAddressStatus")
    private String employerAddressStatus;

    @ApiModelProperty("employerName")
    private String employerName;

    @ApiModelProperty("employerAddress")
    private String employerAddress;

    @ApiModelProperty("employerPostcode")
    private String employerPostcode;

    @ApiModelProperty("employerCountryCode")
    private String employerCountryCode;

    @ApiModelProperty("employerStateCode")
    private String employerStateCode;

    @ApiModelProperty("employerCity")
    private String employerCity;

    @ApiModelProperty("employerDistrict")
    private String employerDistrict;

    @ApiModelProperty("residentialAddress")
    private String residentialAddress;

    @ApiModelProperty("residentialPostcode")
    private String residentialPostcode;

    @ApiModelProperty("residentialAddressStatus")
    private String residentialAddressStatus;

    @ApiModelProperty("residentialCountryCode")
    private String residentialCountryCode;

    @ApiModelProperty("residentialStateCode")
    private String residentialStateCode;

    @ApiModelProperty("residentialDistrictCode")
    private String residentialDistrictCode;

    @ApiModelProperty("residentialCity")
    private String residentialCity;

    @ApiModelProperty("email")
    private String email;

    @ApiModelProperty("telephoneNumber")
    private String telephoneNumber;

    @ApiModelProperty("phoneNumber")
    private String phoneNumber;

    @ApiModelProperty("noTelP")
    private String noTelP;

    @ApiModelProperty("membershipNo")
    private String membershipNo;

    @ApiModelProperty("status")
    private String status;

    @ApiModelProperty("applicationStatusCode")
    private String applicationStatusCode;

    @ApiModelProperty("pegHarta")
    private String pegHarta;

    @ApiModelProperty("tarikhTukarSu")
    @JsonFormat(pattern="yyyy-MM-dd")
    private LocalDate tarikhTukarSu;

    @ApiModelProperty("otherPosition")
    private String otherPosition;

    @ApiModelProperty("batalFlat")
    private Boolean batalFlat;

    @ApiModelProperty("blacklistNotice")
    private Boolean blacklistNotice;

    @ApiModelProperty("benarAjk")
    private Boolean benarAjk;

    @ApiModelProperty("meeting id")
    private Long meetingId;

    @ApiModelProperty("documentId")
    private Long documentId;

    @ApiModelProperty("documentType")
    private Boolean documentType;

    @ApiModelProperty("membershipRegistrationDate")
    private LocalDate membershipRegistrationDate;

    @ApiModelProperty("Appointment date")
    private LocalDate appointedDate;
}

