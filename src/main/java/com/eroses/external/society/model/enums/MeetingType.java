package com.eroses.external.society.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum MeetingType {
    MESYUARAT_PENUBUHAN(1, "Mesyuarat <PERSON>"),
    MESYUARAT_AGUNG(2, "Mesyuarat Agung"),
    MESYUARAT_AGUNG_LUAR_BIASA_KHAS(3, "Mesyuarat Agung Luar Biasa / Khas"),
    MESYUARAT_AJK(4, "Mesyuarat AJK"),
    MESYUARAT_AGUNG_KHAS_PEMBUBARAN(5, "Mesyuarat Agung <PERSON>has (Pembubaran)");

    private final Integer code;
    private final String type;

    public static String getType(Integer code) {
        for (MeetingType meetingType : MeetingType.values()) {
            if (Objects.equals(meetingType.getCode(), code)) {
                return meetingType.getType();
            }
        }
        return null;
    }
}
