package com.eroses.external.society.model.societyLiquidation;

import com.eroses.external.society.model.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class SocietyLiquidationAsset extends BaseEntity {
    private Long liquidationId;
    private Long societyId;
    private String societyNo;
    private Long branchId;
    private String branchNo;
    private String icNo;
    private String assetType;
    private BigDecimal assetValue;
    private Integer donation;
    private Integer liability;
    private Integer balance;
    private Integer other;
    private String otherReason;
    private Integer status;
}