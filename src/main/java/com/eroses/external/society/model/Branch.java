package com.eroses.external.society.model;

import com.eroses.external.society.dto.response.branchCommittee.BranchNonCitizenCommitteeGetResponse;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.FetchType;
import jakarta.persistence.ManyToOne;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Branch extends BaseEntity {
    @ApiModelProperty("branchApplicationNo")
    private String branchApplicationNo;
    @ApiModelProperty("branchNo")
    private String branchNo;
    @ManyToOne(fetch = FetchType.LAZY)
    private Society society;
    @ApiModelProperty("societyId")
    private Long societyId;
    @ApiModelProperty("societyNo")
    private String societyNo;
    @ApiModelProperty("icNo")
    private String icNo;
    @ApiModelProperty("name")
    private String name;
    @ApiModelProperty("branchLevel")
    private String branchLevel;
    @ApiModelProperty("requestDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private LocalDate requestDate;
    @ApiModelProperty("address")
    private String address;
    @ApiModelProperty("countryCode")
    private String countryCode;
    @ApiModelProperty("stateCode")
    private String stateCode;
    @ApiModelProperty("districtCode")
    private String districtCode;
    @ApiModelProperty("smallDistrictCode")
    private String smallDistrictCode;
    @ApiModelProperty("cityCode")
    private String cityCode;
    @ApiModelProperty("city")
    private String city;
    @ApiModelProperty("postcode")
    private String postcode;
    @ApiModelProperty("mailingAddress")
    private String mailingAddress;
    @ApiModelProperty("mailingCountryCode")
    private String mailingCountryCode;
    @ApiModelProperty("mailingStateCode")
    private String mailingStateCode;
    @ApiModelProperty("mailingDistrictCode")
    private String mailingDistrictCode;
    @ApiModelProperty("mailingSmallDistrictCode")
    private String mailingSmallDistrictCode;
    @ApiModelProperty("mailingCityCode")
    private String mailingCityCode;
    @ApiModelProperty("mailingCity")
    private String mailingCity;
    @ApiModelProperty("mailingPostcode")
    private String mailingPostcode;
    @ApiModelProperty("phoneNumber")
    private String phoneNumber;
    @ApiModelProperty("faxNumber")
    private String faxNumber;
    @ApiModelProperty("email")
    private String email;
    @ApiModelProperty("branchStatusCode")
    private Integer branchStatusCode;
    @ApiModelProperty("confession")
    private String confession;
    @ApiModelProperty("confessionDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private LocalDate confessionDate;
    @ApiModelProperty("paymentId")
    private Long paymentId;
    @ApiModelProperty("paymentMethod")
    private String paymentMethod;
    @ApiModelProperty("paymentDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private LocalDate paymentDate;
    @ApiModelProperty("submissionDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private LocalDate submissionDate;
    @ApiModelProperty("bodyDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private LocalDate bodyDate;
    @ApiModelProperty("receiptNumber")
    private String receiptNumber;
    @ApiModelProperty("status")
    private String status;
    @ApiModelProperty("subStatusCode")
    private String subStatusCode;
    @ApiModelProperty("applicationStatusCode")
    private Integer applicationStatusCode;
    @ApiModelProperty("noPPMInduk")
    private String noPPMInduk;
    @ApiModelProperty("noPPMCawangan")
    private String noPPMCawangan;
    @ApiModelProperty("bankName")
    private String bankName;
    @ApiModelProperty("bankReferenceNo")
    private String bankReferenceNo;
    @ApiModelProperty("receiptStatus")
    private String receiptStatus;
    @ApiModelProperty("migrateStat")
    private String migrateStat;
    @ApiModelProperty("migrateAjk")
    private String migrateAjk;
    @ApiModelProperty("ro")
    private String ro;
    @ApiModelProperty("appealStatus")
    private String appealStatus;
    @ApiModelProperty("transferDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private LocalDate transferDate;
    @ApiModelProperty("reconcileDate")
    @JsonFormat(pattern="yyyy-MM-dd")
    private LocalDate reconcileDate;
    @ApiModelProperty("cancelSociety")
    private String cancelSociety;
    @ApiModelProperty("query")
    private String query;
    @ApiModelProperty("noteRo")
    private String noteRo;
    @ApiModelProperty("isQueried")
    private Integer isQueried;
    @ApiModelProperty("applicationExpirationDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applicationExpirationDate;
    @ApiModelProperty("isExtended")
    private Integer isExtended;
    private Boolean migrate;
    // TODO: move to branch response object
    private List<BranchCommittee> branchCommittees;
    private List<BranchNonCitizenCommitteeGetResponse> branchNonCitizenCommittees;
    private String applicantName;
    private List<ExtensionTime> extensionTimes;
}
