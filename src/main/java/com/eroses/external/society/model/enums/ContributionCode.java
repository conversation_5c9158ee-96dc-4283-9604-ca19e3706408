package com.eroses.external.society.model.enums;

import lombok.Getter;

@Getter
public enum ContributionCode {
    DARI_LUAR_NEGARA(1),
    KE_LUAR_NEGARA(2);

    private final int code;

    ContributionCode(int code) {
        this.code = code;
    }

    public static ContributionCode getContributionCode(int code) {
        for (ContributionCode contributionCode : ContributionCode.values()) {
            if (contributionCode.code == code) {
                return contributionCode;
            }
        }
        return null;
    }
}
