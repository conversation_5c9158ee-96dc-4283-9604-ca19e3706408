package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class PrincipalSecretaryRoApproval extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "secretary_id", referencedColumnName = "_society_committee_id", nullable = false)
    private PrincipalSecretary principalSecretary;

    @ApiModelProperty("result")
    private String result;

    @ApiModelProperty("notes")
    private String notes;

    @ApiModelProperty("approvedBy")
    private String approvedBy;

    @ApiModelProperty("approvalDate")
    private String approvalDate;

    @ApiModelProperty("migrateStat")
    private String migrateStat;

}
