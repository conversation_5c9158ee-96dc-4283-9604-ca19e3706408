package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import jakarta.persistence.FetchType;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class PropertyOfficerApplication extends Model{
    private String societyNo;
    private Long societyId;
    @ManyToOne(fetch = FetchType.LAZY)
    private Society society;
    private String branchNo;
    private Long branchId;
    private LocalDate appointmentDate;
    private Boolean agreementAcknowledgment;
    private LocalDate acknowledgmentDate;
    private LocalDate submissionDate;
    private String paymentMethod;
    private Long paymentId;
    private String receiptNumber;
    private LocalDate paymentDate;
    private Long ePaymentId;
    private String bankName;
    private String bankReferenceNumber;
    private String applicationStatusCode;
    private Integer status;
    private String receiptStatus;
    private String branchOfficer;
    private String ro;
    private LocalDate flowDate;
    private String approver;
    private LocalDate reconcileDate;
    private String roNote;
    private List<PropertyOfficer> propertyOfficers;
    private Branch branch;
}
