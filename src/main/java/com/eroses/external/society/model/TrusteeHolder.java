package com.eroses.external.society.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@RequiredArgsConstructor
@AllArgsConstructor
public class TrusteeHolder extends BaseEntity {

    private String societyNo;
    private Long societyId;
    private String branchNo;
    private Long branchId;
    private String titleCode;
    private String name;
    private String gender;
    private String citizenshipStatus;
    private String identificationType;
    private String identificationNo;

    @JsonFormat(pattern = "dd-MM-yyyy")
    private LocalDate dateOfBirth;

    private String placeOfBirth;
    private String occupationCode;
    private String address;
    private String countryCode;
    private String stateCode;
    private String districtCode;
    private String subDistrictCode;
    private String city;
    private String postalCode;
    private String email;
    private String officePhoneNumber;
    private String homePhoneNumber;
    private String mobilePhoneNumber;

    @JsonFormat(pattern = "dd-MM-yyyy")
    private LocalDate appointmentDate;

    private Integer status;
    private String branchTrustee;

    @JsonFormat(pattern = "dd-MM-yyyy")
    private LocalDate visaExpirationDate;

    @JsonFormat(pattern = "dd-MM-yyyy")
    private LocalDate permitExpirationDate;

    private String visaNo;
    private String permitNo;
}
