package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.services.s3.endpoints.internal.BooleanEqualsFn;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class EventOrganiser extends BaseEntity implements Serializable {
    @ApiModelProperty("id")
    public Long id;

    @ApiModelProperty("event_id")
    public Long eventId;

    @ApiModelProperty("organiser_id")
    public Long organiserId;

    @ApiModelProperty("active")
    public Boolean active;
}
