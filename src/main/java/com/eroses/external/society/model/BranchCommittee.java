package com.eroses.external.society.model;

import java.io.Serializable;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class BranchCommittee extends BaseEntity implements Serializable {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("committeeTableOldId")
    private Long committeeTableOldId;
    @ApiModelProperty("branchId")
    private Long branchId;
    @ApiModelProperty("branchNo")
    private String branchNo;
    @ApiModelProperty("designationCode")
    private Integer designationCode;
    @ApiModelProperty("positionArrangement")
    private Integer positionArrangement;
    @ApiModelProperty("titleCode")
    private String titleCode;
    @ApiModelProperty("committeeName")
    private String committeeName;
    @ApiModelProperty("gender")
    private String gender;
    @ApiModelProperty("citizenshipStatus")
    private String citizenshipStatus;
    @ApiModelProperty("identityType")
    private String identityType;
    @ApiModelProperty("committeeIcNo")
    private String committeeIcNo;
    @JsonFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty("dateOfBirth")
    private LocalDate dateOfBirth;
    @ApiModelProperty("placeOfBirth")
    private String placeOfBirth;
    @ApiModelProperty("jobCode")
    private String jobCode;
    @ApiModelProperty("committeeAddressStatus")
    private String committeeAddressStatus;
    @ApiModelProperty("committeeAddress")
    private String committeeAddress;
    @ApiModelProperty("committeeCountryCode")
    private String committeeCountryCode;
    @ApiModelProperty("committeeStateCode")
    private String committeeStateCode;
    @ApiModelProperty("committeeDistrict")
    private String committeeDistrict;
    @ApiModelProperty("committeeSmallDistrict")
    private String committeeSmallDistrict;
    @ApiModelProperty("committeeCity")
    private String committeeCity;
    @ApiModelProperty("postcode")
    private String postcode;
    @ApiModelProperty("email")
    private String email;
    @ApiModelProperty("homePhoneNumber")
    private String homePhoneNumber;
    @ApiModelProperty("phoneNumber")
    private String phoneNumber;
    @ApiModelProperty("officePhoneNumber")
    private String officePhoneNumber;
    @ApiModelProperty("committeeEmployerName")
    private String committeeEmployerName;
    @ApiModelProperty("committeeEmployerAddressStatus")
    private String committeeEmployerAddressStatus;
    @ApiModelProperty("committeeEmployerAddress")
    private String committeeEmployerAddress;
    @ApiModelProperty("committeeEmployerCountryCode")
    private String committeeEmployerCountryCode;
    @ApiModelProperty("committeeEmployerStateCode")
    private String committeeEmployerStateCode;
    @ApiModelProperty("committeeEmployerDistrict")
    private String committeeEmployerDistrict;
    @ApiModelProperty("committeeEmployerCity")
    private String committeeEmployerCity;
    @ApiModelProperty("committeeEmployerPostcode")
    private String committeeEmployerPostcode;
    @ApiModelProperty("status")
    private String status;
    @ApiModelProperty("batalFlat")
    private String batalFlat;
    @ApiModelProperty("applicationStatusCode")
    private String applicationStatusCode;
    @ApiModelProperty("pegHarta")
    private String pegHarta;
    @ApiModelProperty("otherPosition")
    private String otherPosition;
    @JsonFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty("tarikhTukarSu")
    private LocalDate tarikhTukarSu;
    @ApiModelProperty("idSu")
    private int idSu;
    @ApiModelProperty("meetingId")
    private Long meetingId;
    @ApiModelProperty("documentId")
    private Long documentId;
    @ApiModelProperty("documentType")
    private Boolean documentType;
    @ApiModelProperty("meetingDate")
    private LocalDate meetingDate;
    @ApiModelProperty("appointedDate")
    private LocalDate appointedDate;
}
