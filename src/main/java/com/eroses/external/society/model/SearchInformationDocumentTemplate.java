package com.eroses.external.society.model;

import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SearchInformationDocumentTemplate extends BaseEntity {

    @ApiModelProperty
    @Column(name = "code", nullable = false)
    private String code;

    @ApiModelProperty
    @Column(name = "name")
    private String name;

    @ApiModelProperty
    @Column(name = "description")
    private String description;

    @ApiModelProperty
    @Column(name = "type")
    private String type;

    @ApiModelProperty
    @Column(name = "download_period")
    private Integer downloadPeriod;

    @ApiModelProperty
    @Column(name = "amount")
    private Float amount;

    @ApiModelProperty
    @Column(name = "html_content")
    private String htmlContent;

    @ApiModelProperty
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty
    @Column(name = "format")
    private String format;

    @ApiModelProperty
    @Column(name = "language")
    private String language;

    @ApiModelProperty
    @Column(name = "s3_url")
    private String s3Url;

    @ApiModelProperty
    @Column(name = "is_generated")
    private Boolean isGenerated;
}