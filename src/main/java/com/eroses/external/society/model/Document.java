package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class Document extends BaseEntity implements Serializable {

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("type")
    private String type;
    @ApiModelProperty("societyId")
    private Long societyId;
    @ApiModelProperty("societyNo")
    private String societyNo;
    @ApiModelProperty("branchId")
    private Long branchId;
    @ApiModelProperty("branchNo")
    private String branchNo;
    @ApiModelProperty("meetingId")
    private Long meetingId;
    @ApiModelProperty("societyCommitteeId")
    private Long societyCommitteeId;
    @ApiModelProperty("branchCommitteeId")
    private Long branchCommitteeId;
    @ApiModelProperty("societyNonCitizenCommitteeId")
    private Long societyNonCitizenCommitteeId;
    @ApiModelProperty("appealId")
    private Long appealId;
    @ApiModelProperty("statementId")
    private Long statementId;
    @ApiModelProperty("amendmentId")
    private Long amendmentId;
    @ApiModelProperty("liquidationId")
    private Long liquidationId;
    @ApiModelProperty("feedbackId")
    private Long feedbackId;
    @ApiModelProperty("eventId")
    private Long eventId;
    @ApiModelProperty("trainingId")
    private Long trainingId;
    @ApiModelProperty("trainingMaterialId")
    private Long trainingMaterialId;
    @ApiModelProperty("ajkAppointmentLetterDate")
    private LocalDate ajkAppointmentLetterDate;
    @ApiModelProperty("icNo")
    private String icNo;
    @ApiModelProperty("name")
    private String name;
    @ApiModelProperty("code")
    private String code;
    @ApiModelProperty("note")
    private String note;
    @ApiModelProperty("url")
    private String url;
    @ApiModelProperty("doc")
    private byte[] doc;
    @ApiModelProperty("status")
    private Long status;
}
