package com.eroses.external.society.model.societyCancellation;

import com.eroses.external.society.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class SocietyCancellationRevert extends BaseEntity {

    @ApiModelProperty("societyId")
    private Long societyId;

    @ApiModelProperty("societyNo")
    private String societyNo;

    @ApiModelProperty("branchId")
    private Long branchId;

    @ApiModelProperty("branchNo")
    private String branchNo;

    @ApiModelProperty("societyCancellationId")
    private Long societyCancellationId;

    @ApiModelProperty("revertDate")
    @JsonFormat(pattern = "dd-MM-yyyy")
    private LocalDate revertDate;

    @ApiModelProperty("revertReason")
    private String revertReason;

    @ApiModelProperty("note")
    private String note;
    
    @ApiModelProperty("documentId")
    private Long documentId;

    /**
     * Flag to indicate if revert process completed
     */
    @ApiModelProperty("isReverted")
    private boolean isReverted;
}