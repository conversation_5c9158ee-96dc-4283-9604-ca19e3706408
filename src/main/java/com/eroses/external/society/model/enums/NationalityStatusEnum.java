package com.eroses.external.society.model.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum NationalityStatusEnum {
    CITIZEN("1", "Citizen", "Warganegara"),
    NON_CITIZEN("2", "Non-Citizen", "Bukan Warganegara");

    private final String code;
    private final String description;
    private final String descriptionBm;

    NationalityStatusEnum(String code, String description, String descriptionBm) {
        this.code = code;
        this.description = description;
        this.descriptionBm = descriptionBm;
    }

    public static String getNationalityStatusInBmById(String nationalityStatus) {
        for (NationalityStatusEnum nationalityStatusEnum : NationalityStatusEnum.values()) {
            if (Objects.equals(nationalityStatusEnum.code, nationalityStatus)) {
                return nationalityStatusEnum.getDescriptionBm();
            }
        }
        return null;
    }
}
