package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class PrivateEventSociety extends BaseEntity implements Serializable {
    @ApiModelProperty("event_id")
    public Long eventId;

    @ApiModelProperty("society_id")
    public Long societyId;

    @ApiModelProperty("is_manual_selection")
    public Boolean isManualSelection;
}
