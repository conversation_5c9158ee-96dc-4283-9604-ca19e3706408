package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class MeetingMemberAttendance extends BaseEntity {
    @ApiModelProperty("meetingId")
    private Long meetingId;
    @ApiModelProperty("societyId")
    private Long societyId;
    @ApiModelProperty("societyNo")
    private String societyNo;
    @ApiModelProperty("branchId")
    private Long branchId;
    @ApiModelProperty("branchNo")
    private String branchNo;
    @ApiModelProperty("meetingDate")
    private LocalDate meetingDate;
    @ApiModelProperty("icNo")
    private String icNo;
    @ApiModelProperty("present")
    private int present;
    @ApiModelProperty("name")
    private String name;
    @ApiModelProperty("position")
    private String position;
    private Integer status;
}
