package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class RoAmendmentQuery extends BaseEntity {

    @ApiModelProperty("idMinit")
    private Long idMinit;

    @ApiModelProperty("amendmentId")
    private Long amendmentId;

    @ApiModelProperty("societyNo")
    private String societyNo;

    @ApiModelProperty("notes")
    private String notes;

    @ApiModelProperty("done")
    private String done;
}
