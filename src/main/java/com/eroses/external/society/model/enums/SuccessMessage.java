package com.eroses.external.society.model.enums;

import lombok.Getter;

@Getter
public enum SuccessMessage {
    DATA_CREATED("Data created successfully", "Data berjaya dicipta"),
    DATA_UPDATED("Data updated successfully", "Data berjaya dikemaskini"),
    DATA_FOUND("Data found successfully: %s", "Data berjaya dijumpai: %s"),
    NO_DATA_FOUND("No data found", "Tiada data dijumpai"),
    DATA_DELETED("Data deleted successfully", "Data berjaya dipadam"),
    EMAIL_SENT("Email sent successfully", "Emel berjaya dihantar"),
    PAYMENT_MADE("Payment made successfully", "Pembayaran berjaya dibuat"),
    PAYMENT_CANCELLED("Payment cancelled successfully", "Pembayaran berjaya dibatalkan"),
    NO_DATA_FOUND_BY_CRITERIA("No data found by criteria", "Tiada data dijumpai berdasarkan kriteria"),
    OPERATION_COMPLETED("Operation completed successfully: %s", "Operasi berjaya diselesaikan: %s"),
    RETRIEVAL_OK("Retrieved successfully", "Berjaya diperoleh"),
    PDF_GENERATED("PDF generated successfully", "PDF berjaya dijana"),
    APPLICATION_SENT("Application have been successfully sent", "Permohonan telah dihantar untuk kelulusan"),

    //Society related success messages
    SOCIETY_NAME_TAKEN("Society name is taken", "Nama pertubuhan telah wujud"),
    SOCIETY_NAME_AVAILABLE("Society name is available", "Nama pertubuhan boleh digunapakai"),

    // Posting related success messages
    POSTING_CREATED("Posting created successfully", "Hebahan berjaya dicipta"),
    POSTING_UPDATED("Posting updated successfully", "Hebahan berjaya dikemaskini"),
    POSTING_DELETED("Posting deleted successfully", "Hebahan berjaya dipadam"),
    POSTING_PUBLISHED("Posting published successfully", "Hebahan berjaya diterbitkan"),
    POSTING_UNPUBLISHED("Posting unpublished successfully", "Hebahan berjaya dibatalkan penerbitan"),
    POSTING_FOUND("Posting found successfully", "Hebahan berjaya dijumpai"),
    POSTINGS_FOUND("Postings found successfully", "Hebahan-hebahan berjaya dijumpai"),
    POSTING_REPORTED("Posting reported successfully", "Hebahan berjaya dilaporkan"),
    POSTING_REPORTS_FOUND("Posting reports found successfully", "Laporan hebahan berjaya dijumpai"),
    POSTING_REVIEWS_FOUND("Posting reviews found successfully", "Ulasan hebahan berjaya dijumpai"),
    POSTING_EXPIRED_FOUND("Posting expired found successfully", "Hebahan tamat tempoh berjaya dijumpai"),
    ENGAGEMENT_STATS_FOUND("Engagement statistics found successfully", "Statistik penglibatan berjaya dijumpai"),
    ENGAGEMENT_RECORDED("Engagement recorded successfully", "Penglibatan berjaya direkodkan"),
    POSTING_REVIEW_SUBMITTED("Posting review submitted successfully", "Ulasan hebahan berjaya dihantar"),
    NOTIFICATION_MARKED_AS_READ("Notification marked as read successfully", "Pemberitahuan berjaya ditandakan sebagai dibaca"),
    ALL_NOTIFICATIONS_MARKED_AS_READ("All notifications marked as read successfully", "Semua pemberitahuan berjaya ditandakan sebagai dibaca"),
    POSTING_USER_FOUND("Posting user found successfully","Pengguna yang membuat hebahan berjaya ditemui"),
    POSTING_PRE_PUBLISH("Posting Pre Publish found successfully","Hebahan Pra-Siar berjaya ditemui"),
    SUBMIT_SUCCESS("Submitted successfully.", "Berjaya dihantar."),
    QUERY_SUBMIT_SUCCESS("Query submitted successfully.", "Kuiri berjaya dihantar."),

    // Committee task success messages
    COMMITTEE_TASK_CREATED("Committee task created successfully", "Aliran Tugas berjaya dicipta"),
    COMMITEE_FOUND("Commitee found successfully", "Jawatankuasa berjaya dijumpai"),
    COMMITTEE_TASK_DEACTIVATED("Committee task deactivated successfully", "Aliran Tugas berjaya dinyahaktifkan"),
    COMMITTEE_TASK_ACTIVATED("Committee task activated successfully", "Aliran Tugas berjaya diaktifkan"),
    SOCIETY_COMMITTEE_TASK_MODULE_ENABLED("Committee task module enabled successfully", "Tugas Jawatankuasa Pertubuhan telah diaktifkan"),
    SOCIETY_COMMITTEE_TASK_MODULE_DISABLED("Committee task module disabled successfully", "Tugas Jawatankuasa Pertubuhan telah dinyahaktifkan"),
    BRANCH_COMMITTEE_TASK_MODULE_ENABLED("Committee task module enabled successfully", "Tugas Jawatankuasa Cawangan telah diaktifkan"),
    BRANCH_COMMITTEE_TASK_MODULE_DISABLED("Committee task module disabled successfully", "Tugas Jawatankuasa Cawangan telah dinyahaktifkan"),

    // Meeting success messages
    MEETING_CREATED("Meeting created successfully", "Mesyuarat berjaya dicipta"),
    MEETING_UPDATED("Meeting updated successfully", "Mesyuarat berjaya dikemaskini"),

    // Trustee
    TRUSTEE_CREATED("Trustee created successfully", "Pemegang Amanah berjaya dicipta"),
    TRUSTEE_UPDATED("Trustee updated successfully", "Pemegang Amanah berjaya dikemaskini"),
    TRUSTEE_DEACTIVATED("Trustee deactivated successfully", "Pemegang Amanah berjaya dinyahaktifkan"),

    // Appeal related success messages
    APPEAL_CREATED("Appeal case created successfully", "Kes rayuan berjaya dicipta"),
    APPEAL_UPDATED("Appeal case updated successfully", "Kes rayuan berjaya dikemaskini"),
    APPEAL_REMOVED("Appeal case removed successfully", "Kes rayuan berjaya dikeluarkan"),

    // Public Officer related success messages
    PUBLIC_OFFICER_CREATED("Public officer created successfully", "Pegawai awam berjaya dicipta"),
    PUBLIC_OFFICER_UPDATED("Public officer updated successfully", "Pegawai awam berjaya dikemaskini"),
    PUBLIC_OFFICER_REMOVED("Public officer removed successfully", "Pegawai awam berjaya dikeluarkan"),
    PUBLIC_OFFICER_APPROVED("Public officer approved successfully", "Pegawai awam berjaya diluluskan"),
    PUBLIC_OFFICER_RETRIEVED("Public officer retrieved successfully", "Pegawai awam berjaya diperoleh"),
    BRANCH_PUBLIC_OFFICER_RETRIEVED("Branch public officer retrieved successfully", "Pegawai awam cawangan berjaya diperoleh"),
    PUBLIC_OFFICER_COUNT_RETRIEVED("Public officer count retrieved successfully", "Kiraan pegawai awam berjaya diperoleh"),
    BRANCH_PUBLIC_OFFICER_COUNT_RETRIEVED("Branch public officer count retrieved successfully", "Kiraan pegawai awam cawangan berjaya diperoleh"),

    // Property Officer related success messages
    PROPERTY_OFFICER_APPLICATION_CREATED("Property officer application created successfully", "Permohonan pegawai harta berjaya dicipta"),
    PROPERTY_OFFICER_APPROVED("Property officer approved successfully", "Pegawai harta berjaya diluluskan"),
    PROPERTY_OFFICER_UPDATED("Property officer updated", "Pegawai harta dikemaskini"),
    PROPERTY_OFFICER_APPLICATION_ALREADY_DELETED("Property officer application is already deleted", "Permohonan pegawai harta telah dipadam"),
    PROPERTY_OFFICER_APPLICATION_DELETED("Property officer application deleted successfully", "Permohonan pegawai harta berjaya dipadam");

    private final String message;
    private final String bmMessage;

    SuccessMessage(String message, String bmMessage) {
        this.message = message;
        this.bmMessage = bmMessage;
    }

    public String getMessage(Object... args) {
        return formatMessage(message, args);
    }

    public String getBmMessage(Object... args) {
        return formatMessage(bmMessage, args);
    }

    private String formatMessage(String template, Object... args) {
        if (template.contains("%s")) {
            if (args == null || args.length == 0) {
                return template.replaceAll("%s", "");
            }
            return String.format(template, args);
        }
        return template;
    }
}
