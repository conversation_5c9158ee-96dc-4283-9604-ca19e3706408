package com.eroses.external.society.model.enums;

import lombok.Getter;

@Getter
public enum ErrorMessage {
    DATA_NOT_FOUND("Data not found, message: %s", "Data tidak dijumpai, mesej: %s"),
    DATA_NOT_FOUND_WITH_CRITERIA("Data not found with criteria", "Data tidak dijumpai berdasarkan kriteria"),
    DATA_ALREADY_EXISTS("Data already exists, id: %s", "Data telah wujud, id: %s"),
    DATA_CREATE_FAILED("Data create failed: %s", "Gagal mencipta data: %s"),
    DATA_UPDATE_FAILED("Data update failed, id: %s", "Gagal mengemaskini data, id: %s"),
    DATA_DELETE_FAILED("Data delete failed, id: %s", "Gagal memadam data, id: %s"),
    DATABASE_ERROR("Database error occurred, error message: %s", "Ralat pangkalan data berlaku, mesej: %s"),
    NULL_POINTER_EXCEPTION("Null pointer exception occurred, error message: %s", "Pengecualian null pointer berlaku, mesej: %s"),
    UNEXPECTED_ERROR("An unexpected error occurred, error message: %s", "Ralat tidak dijangka berlaku, mesej: %s"),
    USER_NOT_AUTHENTICATED("User is not authenticated", "Pengguna tidak disahkan"),
    TEMPLATE_NOT_FOUND("Template not found, code: %s", "Templat tidak dijumpai, kod: %s"),
    TEMPLATE_ALREADY_EXIST("Template already exist, code: %s", "Templat telah wujud, kod: %s"),
    EMAIL_NOT_SENT("Email not sent", "Emel tidak berjaya dihantar"),
    FILE_NOT_ATTACHED("No file attached", "Tiada fail dilampirkan"),
    S3_UPLOAD_FAILED("Failed to upload to S3", "Gagal memuat naik ke S3"),
    PAYMENT_FAILED("Payment failed: %s", "Pembayaran gagal: %s"),
    INVALID_CODE("Invalid code: %s", "Kod tidak sah: %s"),
    INVALID_USER("Invalid user: %s", "Pengguna tidak sah: %s"),
    INVALID_REQUEST("Invalid request: %s", "Permintaan tidak sah: %s"),
    INTERNAL_SERVER_ERROR("Internal server error", "Ralat pelayan dalaman"),
    MISSING_REQUEST_DTO("Missing request dto: %s", "DTO permintaan hilang: %s"),
    CODE_ALREADY_EXISTS("Code %s '%s' already exists", "Kod %s '%s' telah wujud"),
    PROCESS_DOCUMENT_FAILED("Failed to process document", "Gagal memproses dokumen"),
    MISSING_DATA_REQUEST("Missing data in request: %s", "Data hilang dalam permintaan: %s"),
    USER_ALREADY_BLACKLISTED("User is already blacklisted: %s", "Pemegang jawatan ini telah disenarai hitam: %s"),
    NO_BLACKLIST_POLITIC_SOCIETY("Invalid request: Politic society cannot be blacklisted", "Permohonan tidak sah: Pertubuhan Politik tidak boleh dilarang."),
    USER_IS_BLACKLISTED("User is blacklisted: %s", "Pengguna ini telah disenarai hitam: %s"),
    USER_ALREADY_IS_MEMBER_OF_SOCIETY("User is already a member of society: %s", "Pengguna telah dalam pertubuhan: %s"),
    DOCUMENT_NOT_FOUND("Document not found: %s", "Dokumen tidak dijumpai: %s"),
    DUPLICATE_MEETING_DATE_TYPE("Meeting with the same date and type already exists.", "Mesyuarat dengan tarikh dan jenis yang sama telah wujud."),

    // Posting related error messages
    POSTING_NOT_FOUND("Posting not found", "Hebahan tidak dijumpai"),
    POSTING_ALREADY_EXISTS("Posting already exists", "Hebahan telah wujud"),
    POSTING_CREATE_FAILED("Failed to create posting", "Gagal mencipta hebahan"),
    POSTING_UPDATE_FAILED("Failed to update posting", "Gagal mengemaskini hebahan"),
    POSTING_DELETE_FAILED("Failed to delete posting", "Gagal memadam hebahan"),
    POSTING_PUBLISH_FAILED("Failed to publish posting", "Gagal menerbitkan hebahan"),
    POSTING_UNPUBLISH_FAILED("Failed to unpublish posting", "Gagal membatalkan penerbitan hebahan"),
    INVALID_CATEGORY_FOR_SECRETARY("Invalid category for secretary", "Kategori tidak sah untuk setiausaha"),
    CANNOT_UPDATE_SUBMITTED_POSTING("Cannot update submitted posting", "Tidak boleh mengemaskini hebahan yang telah dihantar"),
    ALREADY_REPORTED_TODAY("Already reported this posting today", "Telah melaporkan hebahan ini hari ini"),
    POSTING_REVIEW_ALREADY_SUBMITTED("Posting review already submitted", "Ulasan hebahan telah dihantar"),
    REJECTION_COMMENT_REQUIRED("Rejection comment is required", "Komen penolakan diperlukan"),

    // Committee related error messages
    SOCIETY_NOT_FOUND_ID("Society with the given id cannot be found.","Pertubuhan dengan id yang diberikan tidak dapat ditemui."),
    NULL_BRANCH_COMMITTEE("Branch committee id cannot be null.", "Id jawatankuasa cawangan tidak boleh null."),
    BRANCH_COMMITTEE_NOT_FOUND("Branch committee with the given id cannot be found.","Jawatankuasa cawangan dengan id yang diberikan tidak dapat ditemui"),
    COMMITTEE_NOT_FOUND("Committee with the given id cannot be found.","Jawatankuasa dengan id yang diberikan tidak dapat ditemui."),
    COMMITTEE_TASK_EXIST("Committee task for module %s already exists","Sila pilih sekurang-kurangnya satu AJK sebelum mengemaskini."),
    COMMITTEE_CREATE_AUDIT_TRAIL_FAILED("Committee created, but failed to create audit trail: %s", "Jawatankuasa dicipta, tetapi gagal mencipta jejak audit: %s"),
    COMMITTEE_INTERNAL_SERVER_ERROR("A database error occurred while retrieving the committee: %s", "Ralat pangkalan data berlaku semasa mendapatkan jawatankuasa: %s"),
    COMMITEE_EMPTY("Empty data was found while retrieving the committee.","Data kosong ditemui semasa mendapatkan jawatankuasa."),
    COMMITEE_UNEXPECTEDERROR("An unexpected error occurred while getting the committee: %s","Ralat tidak dijangka berlaku semasa mendapatkan jawatankuasa: %s"),
    COMMITEE_SEARCH_NOT_FOUND("No committees found with the given search criteria.","Tiada jawatankuasa ditemui dengan kriteria carian yang diberikan."),
    NON_ADDITIONAL_INFORMATION("No additional information is available.", "Tiada maklumat tambahan tersedia."),
    COMMITTEE_DETAIL_NOT_FILLED("Committee detail is not filled.", "Butiran jawatankuasa tidak diisikan dengan lengkap."),
    NON_CITIZEN_APPLICATION_NOT_SUBMITTED("Non-citizen application is not submitted.", "Terdapat permohonan ahli jawatan kuasa bukan warganegara belum dihantar."),

    //Branch
    BRANCH_NOT_FOUND("Branch not found", "Cawangan tidak dijumpai"),

    //Trustee
    TRUSTEE_NOT_FOUND("Trustee not found", "Pemegang Amanah tidak dijumpai"),
    TRUSTEE_ALREADY_EXISTS("Trustee already exists", "Pemegang Amanah telah wujud"),
    TRUSTEE_CREATE_FAILED("Failed to create trustee", "Gagal mencipta Pemegang Amanah"),
    TRUSTEE_UPDATE_FAILED("Failed to update trustee", "Gagal mengemaskini Pemegang Amanah"),
    TRUSTEE_DEACTIVATE_FAILED("Failed to deactivate trustee", "Gagal nyahaktifkan Pemegang Amanah"),
    TRUSTEE_CREATE_AUDIT_TRAIL_FAILED("Trustee created, but failed to create audit trail: %s","Pemegang Amanah dicipta, tetapi gagal mencipta jejak audit: %s"),
    TRUSTEE_UPDATE_AUDIT_TRAIL_FAILED("Trustee updated, but failed to create audit trail: %s","Pemegang Amanah dikemaskini, tetapi gagal mencipta jejak audit: %s"),

    //Secretary
    CURRENT_SECRETARY_CANNOT_APPLY("Current secretary cannot apply for secretary position", "Setiausaha semasa tidak boleh mendaftar untuk tukar setiausaha baharu"),

    // Appeal related error messages
    APPEAL_CREATE_FAILED("Unable to create Appeal case", "Tidak dapat mencipta kes rayuan"),
    APPEAL_UPDATE_FAILED("Appeal case unable to be updated", "Kes rayuan tidak dapat dikemaskini"),
    APPEAL_REMOVE_FAILED("Appeal case record failed to be updated", "Rekod kes rayuan gagal dikemaskini"),

    // Public Officer related error messages
    PUBLIC_OFFICER_CREATE_FAILED("Failed to create public officer", "Gagal mencipta pegawai awam"),
    PUBLIC_OFFICER_CREATE_AUDIT_FAILED("Public officer created but failed to insert audit log", "Pegawai awam dicipta tetapi gagal memasukkan log audit"),
    PUBLIC_OFFICER_NOT_FOUND("Public officer not found", "Pegawai awam tidak dijumpai"),
    PUBLIC_OFFICER_NOT_FOUND_WITH_ID("Public officer with id %s not found", "Pegawai awam dengan id %s tidak dijumpai"),
    PUBLIC_OFFICER_UPDATE_FAILED("Failed to update public officer", "Gagal mengemaskini pegawai awam"),
    PUBLIC_OFFICER_AUDIT_TRAIL_FAILED("Failed to create audit trail record", "Gagal mencipta rekod jejak audit"),
    PUBLIC_OFFICER_ALREADY_SUBMITTED("Public officer already been submitted", "Pegawai awam telah dihantar"),
    PUBLIC_OFFICER_DELETE_FAILED("Failed to delete public officer", "Gagal memadam pegawai awam"),
    PUBLIC_OFFICER_AUDIT_LOG_FAILED("Failed to create audit log", "Gagal mencipta log audit"),
    PUBLIC_OFFICER_APPLICATION_STATUS_UPDATE_FAILED("Failed to update public officer application status", "Gagal mengemaskini status permohonan pegawai awam"),
    RO_APPROVAL_CREATE_FAILED("Failed to create Ro Approval", "Gagal mencipta Kelulusan RO"),
    INVALID_APPROVAL_STATUS("Only LULUS and TOLAK is allowed", "Hanya LULUS dan TOLAK dibenarkan"),

    // Property Officer related error messages
    PROPERTY_OFFICER_APPLICATION_CREATE_FAILED("Failed to create property officer application", "Gagal mencipta permohonan pegawai harta"),
    PROPERTY_OFFICER_CREATE_FAILED("Failed to create property officer", "Gagal mencipta pegawai harta"),
    PROPERTY_OFFICER_APPLICATION_NOT_FOUND("Property officer application not found", "Permohonan pegawai harta tidak dijumpai"),
    PROPERTY_OFFICER_APPLICATION_NOT_FOUND_WITH_ID("Property officer application with id %s not found", "Permohonan pegawai harta dengan id %s tidak dijumpai"),
    PROPERTY_OFFICER_RO_UPDATE_FAILED("Failed to update property officer ro", "Gagal mengemaskini ro pegawai harta"),
    PROPERTY_OFFICER_DEACTIVATE_FAILED("Failed to deactivate previous property officer", "Gagal nyahaktifkan pegawai harta sebelumnya"),
    PROPERTY_OFFICER_APPLICATION_STATUS_UPDATE_FAILED("Failed to update public officer application status", "Gagal mengemaskini status permohonan pegawai awam"),
    PROPERTY_OFFICER_APPROVAL_STATUS_UPDATE_FAILED("Failed to update approval status for property officer application", "Gagal mengemaskini status kelulusan untuk permohonan pegawai harta"),
    UNSUPPORTED_STATUS("Unsupported status", "Status tidak disokong"),
    UPDATE_OPERATION_NOT_ALLOWED("The update operation is not allowed", "Operasi kemaskini tidak dibenarkan"),
    PROPERTY_OFFICER_UPDATE_FAILED("Failed to update property officers", "Gagal mengemaskini pegawai harta"),
    INVALID_REQUEST_UPDATE_BOTH("Invalid request, you can update application status code or officers but not both", "Permintaan tidak sah, anda boleh mengemaskini kod status permohonan atau pegawai tetapi bukan kedua-duanya"),
    DRAFT_PROPERTY_OFFICER_DELETE_ONLY("Only draft property officer application can be deleted", "Hanya draf permohonan pegawai harta boleh dipadam"),
    PROPERTY_OFFICER_APPLICATION_UPDATE_FAILED("Failed to update property officer application", "Gagal mengemaskini permohonan pegawai harta"),

    //Blacklist
    BLACKLIST_USER_NOT_FOUND("Blacklist User not found", "Pengguna disenarai hitam tidak dijumpai"),

    //Committee task
    COMMITTEE_TASK_MODULE_ALREADY_ENABLED("Committee task already enabled", "Tugas jawatankuasa sudah diaktifkan"),
    COMMITTEE_TASK_MODULE_NOT_ENABLED("Committee task not enabled", "Tugas jawatankuasa belum diaktifkan"),
    COMMITTEE_TASK_FOR_MODULE_IS_NOT_APPLICABLE_FOR_BRANCH("Committee task for module %s is not applicable for branch", "Tugas jawatankuasa untuk modul %s tidak dibenarkan untuk cawangan"),

    //Meeting
    MEETING_NOT_CREATED("Meeting not created", "Mesyuarat tidak dicipta"),
    ;

    private final String message;
    private final String bmMessage;

    ErrorMessage(String message, String bmMessage) {
        this.message = message;
        this.bmMessage = bmMessage;
    }

    public String getMessage(Object... args) {
        return formatMessage(message, args);
    }

    public String getBmMessage(Object... args) {
        return formatMessage(bmMessage, args);
    }

    private String formatMessage(String template, Object... args) {
        if (template.contains("%s")) {
            if (args == null || args.length == 0) {
                return template.replaceAll("%s", "");
            }
            return String.format(template, args);
        }
        return template;
    }
}
