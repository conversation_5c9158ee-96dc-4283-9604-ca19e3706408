package com.eroses.external.society.model.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum CommitteeTaskModule {
    PERLEMBAGAAN("PERLEMBAGAAN", true),
    PENGURUSAN_AJK("PENGURUSAN_AJK", false),
    PENGURUSAN_MESYUARAT("PENGURUSAN_MESYUARAT", false),
    PENYATAAN_TAHUNAN("PENYATAAN_TAHUNAN", false),
    PENGURUSAN_GERAN("PENGURUSAN_GERAN", false),
    ;
    private final String code;

    private final boolean societyOnly;

    CommitteeTaskModule(String code, boolean societyOnly) {
        this.code = code;
        this.societyOnly = societyOnly;
    }

    public static CommitteeTaskModule of(String _code) {
        return Arrays.stream(values()).filter(m -> m.getCode().equals(_code)).findFirst().orElse(null);
    }
}
