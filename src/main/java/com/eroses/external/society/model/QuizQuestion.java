package com.eroses.external.society.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
public class QuizQuestion extends BaseEntity {
    private Long trainingQuizId;
    private String questionText;
    private String questionType; // Currently only 'MULTIPLE_CHOICE'
    private Integer sequenceOrder;

    // Transient field for options, not stored in DB directly
    private List<QuizOption> options;
}
