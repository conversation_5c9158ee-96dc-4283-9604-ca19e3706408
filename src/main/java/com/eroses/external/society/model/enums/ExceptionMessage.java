package com.eroses.external.society.model.enums;

import lombok.Getter;

@Getter
public enum ExceptionMessage {
    SEND_NOTIFICATION_FAIL("Gagal menghantar notifikasi"),
    DATA_NOT_FOUND("Data tidak dijumpai : %s"),
    UNEXPECTED_ERROR_OCCUR("Ralat tidak dijangka berlaku : %s"),
    REQUEST_NOT_VALID("Permintaan tidak sah : %s"),
    NULL_POINTER_EXCEPTION("Pengecualian null pointer : %s"),
    DATABASE_ACCESS_EXCEPTION("Pengecualian akses pangkalan data : %s"),
    RUNTIME_EXCEPTION("Pengecualian runtime : %s"),
    MISSING_DATA_REQUEST("Data hilang dalam permintaan : %s"),
    INVALID_ARGUMENT_EXCEPTION("Pengecualian Argumen Tidak Sah"),
    ;


    private final String message;
    ExceptionMessage(String message) {
        this.message = message;
    }

    public String getMessage(Object... args) {
        // Allows dynamic values using String.format
        return String.format(message, args);
    }
}
