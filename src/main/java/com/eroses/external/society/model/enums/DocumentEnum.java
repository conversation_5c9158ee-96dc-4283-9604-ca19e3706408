package com.eroses.external.society.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum DocumentEnum {
    SURAT_CARA_PEMBUBARAN("SURAT_CARA_PEMBUBARAN", "Liquidation Legal Notice"),
    SURAT_AKUAN_BERKANUN("SURAT_AKUAN_BERKANUN", "Statutory Declaration Letter for Liquidation"),
    SECTION_13_1_A_B_C("SECTION_13_1_A_B_C", "13(1)(a), (b) & (c)"),
    SECTION_13_1_D("SECTION_13_1_D", "13(1)(d)"),
    SIJIL_KELULUSAN_PERMOHONAN_INDUK("SIJIL_KELULUSAN_PERMOHONAN_INDUK", "Certificate of Registration of Society"),
    SURAT_IRINGAN_KELULUSAN("SURAT_IRINGAN_KELULUSAN", "Surat iringan <PERSON>"),
    PENYATA_TAHUNAN("PENYATA_TAHUNAN", "Annual Statement"),
    SURAT_PEMBUBARAN("SURAT_PEMBUBARAN", "Surat Pembubaran"),
    SURAT_INSOLVENSI("SURAT_INSOLVENSI", "Surat Insolvensi"),
    SURAT_KEBENARAN_CAWANGAN("SURAT_KEBENARAN_CAWANGAN", "Surat Kebenaran Cawangan"),
    SUPPORTING_DOCUMENT("SUPPORTING_DOCUMENT", "Supporting Document"),
    PERLEMBAGAAN("PERLEMBAGAAN", "Constitution")
    ;

    private final String code;
    private final String description;

    public static DocumentEnum getDocument(String code) {
        for (DocumentEnum documentEnum : DocumentEnum.values()) {
            if (Objects.equals(documentEnum.code, code)) {
                return documentEnum;
            }
        }
        return null;
    }
}
