package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class AdmAddresses extends BaseEntity {
    @ApiModelProperty("id")
    public Long id;

    @ApiModelProperty("pid")
    public Long pid;

    @ApiModelProperty("name")
    public String name;

    @ApiModelProperty("level")
    public Long level;

    @ApiModelProperty("shortCode")
    public String shortCode;

    @ApiModelProperty("code")
    public String code;

    @ApiModelProperty("status")
    public Integer status;

    @ApiModelProperty("migrate")
    public Integer migrate;
}

