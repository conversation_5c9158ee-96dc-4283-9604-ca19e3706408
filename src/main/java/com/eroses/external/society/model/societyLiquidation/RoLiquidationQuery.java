package com.eroses.external.society.model.societyLiquidation;

import com.eroses.external.society.model.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RoLiquidationQuery extends BaseEntity {
    private Long id;
    private Long liquidationId;
    private String type;
    private String societyNo;
    private Long societyId;
    private Integer branchId;
    private String branchNo;
    private String note;
    private int finished;
    private Long createdBy;
    private Long modifiedBy;
}
