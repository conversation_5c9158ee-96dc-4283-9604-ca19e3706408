package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class EventCertTemplateConfiguration extends BaseEntity implements Serializable {
    @ApiModelProperty("template_code")
    public String templateCode;

    @ApiModelProperty("templateName")
    public String templateName;

    @ApiModelProperty("outputFileName")
    public String outputFileName;

    @ApiModelProperty("description")
    public String description;
}
