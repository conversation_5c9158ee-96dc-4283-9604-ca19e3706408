package com.eroses.external.society.model.enums;

import lombok.Getter;

@Getter
public enum SocietyRegistrationPage {
    GENERAL_INFO(1, "maklumat-am"),
    MEETING_MINUTE(2, "mesyuarat-pen<PERSON><PERSON>an"),
    CONSTITUTION(3, "perlembagaan"),
    COMMITTEE(4, "senarai-ajk"),
    SUPPORTING_DOC(5, "dokumen-sokongan");

    private final int pageNumber;
    private final String pageName;

    SocietyRegistrationPage(int pageNumber, String pageName) {
        this.pageNumber = pageNumber;
        this.pageName = pageName;
    }

    public static SocietyRegistrationPage getSocietyRegistrationPageByNumber(int pageNumber) {
        for (SocietyRegistrationPage societyRegistrationPage : SocietyRegistrationPage.values()) {
            if (societyRegistrationPage.getPageNumber() == pageNumber) {
                return societyRegistrationPage;
            }
        }
        throw new IllegalArgumentException("Invalid page number: " + pageNumber);
    }
}
