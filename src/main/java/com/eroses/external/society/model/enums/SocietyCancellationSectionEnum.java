
package com.eroses.external.society.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum representing the different sections under which a society can be cancelled.
 */
@Getter
@AllArgsConstructor
public enum SocietyCancellationSectionEnum {
    SECTION_13("SECTION_13", "13"),
    SECTION_9A("SECTION_9A", "9A");

    private final String code;
    private final String description;

    /**
     * Find enum by code.
     *
     * @param code the code
     * @return the enum value or null if not found
     */
    public static SocietyCancellationSectionEnum findByCode(String code) {
        for (SocietyCancellationSectionEnum section : values()) {
            if (section.getCode().equals(code)) {
                return section;
            }
        }
        return null;
    }

    /**
     * Find enum by description.
     *
     * @param description the description
     * @return the enum value or null if not found
     */
    public static SocietyCancellationSectionEnum findByDescription(String description) {
        for (SocietyCancellationSectionEnum section : values()) {
            if (section.getDescription().equals(description)) {
                return section;
            }
        }
        return null;
    }
}