package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Notification extends BaseEntity {

    @ApiModelProperty
    private String templateCode;

    @ApiModelProperty
    private String templateLanguage;

    @ApiModelProperty
    private String description;

    @ApiModelProperty
    private String subject;

    @ApiModelProperty
    private String content;

    @ApiModelProperty
    private Integer status;
}
