package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class DocumentTemplate extends BaseEntity implements Serializable {
    private String templateCode;
    private String templateName;
    private String description;
    private String subject;
    private String templateLanguage;
    private String templateFormat;
    private String type;
    private String htmlContent;
    private String byteContent;
    private String url;
    private String status;
}
