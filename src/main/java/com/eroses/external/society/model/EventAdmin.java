package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class EventAdmin extends BaseEntity implements Serializable {

    @ApiModelProperty("id")
    public Long id;

    @ApiModelProperty("identificationNo")
    public String identificationNo;

    @ApiModelProperty("username")
    public String username;


}
