package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ConstitutionType extends BaseEntity implements Serializable {
    @ApiModelProperty("id")
    public Long id;

    @ApiModelProperty("name")
    public String name;

    @ApiModelProperty("version")
    public String version;

    @ApiModelProperty("status")
    public String status;

    @ApiModelProperty("clauseContents")
    private List<ClauseContent> clauseContents;

}


