package com.eroses.external.society.model.enums;

import lombok.Getter;

@Getter
public enum AdmAddressesEnum {
    // Addresses for Malaysia
    JOHOR("243", "Jo<PERSON>"),
    KEDAH("244", "Kedah"),
    KELANTAN("245", "Kelantan"),
    MELAKA("246", "Mel<PERSON>"),
    NEGERI_SEMBILAN("247", "Negeri Sembilan"),
    PAHANG("248", "Pahang"),
    PULAU_PINANG("249", "Pulau Pinang"),
    PERAK("250", "Perak"),
    PERLIS("251", "Perlis"),
    SELANGOR("252", "Selangor"),
    TERENGGANU("253", "Terengganu"),
    SABAH("254", "Sabah"),
    SARAWAK("255", "Sarawak"),
    WILAYAH_PERSEKUTUAN_KUALA_LUMPUR("256", "Wilayah Persekutuan Kuala Lumpur"),
    WILAYAH_PERSEKUTUAN_LABUAN("257", "Wilayah Persekutuan Labuan"),
    WILAYAH_PERSEKUTUAN_PUTRAJAYA("258", "Wilayah Persekutuan Putrajaya");

    private final String id; // same as state code in other Model
    private final String description;

    AdmAddressesEnum(String id, String description) {
        this.id = id;
        this.description = description;
    }
}
