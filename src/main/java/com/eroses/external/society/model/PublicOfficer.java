package com.eroses.external.society.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PublicOfficer extends BaseEntity{
    private String societyNo;
    private Long societyId;
    private String branchNo;
    private Long branchId;
    private String titleCode;
    private String name;
    private String gender;
    private String citizenshipStatus;
    private String identificationType;
    private String identificationNo;
    private LocalDate dateOfBirth;
    private String placeOfBirth;
    private String occupationCode;
    private String addressStatus;
    private String address;
    private String countryCode;
    private String stateCode;
    private String districtCode;
    private String subDistrictCode;
    private String city;
    private String postalCode;
    private String email;
    private String homePhoneNumber;
    private String mobilePhoneNumber;
    private String officePhoneNumber;
    private Boolean agreed;
    private Boolean acknowledgment;
    private LocalDate acknowledgmentDate;
    private LocalDate submissionDate;
    private Long paymentId;
    private String paymentMethod;
    private LocalDate paymentDate;
    private String receiptNumber;
    private Long ePaymentId;
    private String bankName;
    private String bankReferenceNumber;
    private String receiptStatus;
    private String ro;
    private LocalDate appointmentDate;
    private String applicationStatusCode;
    private Integer status;
    private String branchOfficer;
    private LocalDate flowDate;
    private String approver;
    private LocalDate reconcileDate;
    private String roNote;
    private Society society;
    private Branch branch;
}
