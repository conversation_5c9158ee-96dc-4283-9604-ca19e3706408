package com.eroses.external.society.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum ROApprovalType {
    // Type means record belongs to which table
    SOCIETY_REGISTRATION("SOCIETY_REGISTRATION", "SOCIETY", "Society Registration", "Pendaftaran Induk"),
    SOCIETY_LIQUIDATION("SOCIETY_LIQUIDATION", "LIQUIDATION", "Society Liquidation", "Pembubaran Induk"),
    BRANCH_LIQUIDATION("BRANCH_LIQUIDATION", "LIQUIDATION", "Branch Liquidation", "Pembubaran Cawangan"),
    SOCIETY_AMENDMENT("SOCIETY_AMENDMENT", "AMENDMENT", "Society Amendment", "Pindaan Induk"),
    SOCIETY_APPEAL("SOCIETY_APPEAL", "APPEAL", "Society Appeal", "Rayuan Induk"),
    SOCIETY_NEW_SECRETARY("SOCIETY_NEW_SECRETARY", "SOCIETY_NEW_SECRETARY", "Society New Secretary", "Setiausaha Pembaharuan Induk"),
    SOCIETY_NON_CITIZEN("SOCIETY_NON_CITIZEN", "NON_CITIZEN", "Society Non Citizen", "Induk Bukan Warganegara"),
    BRANCH_NON_CITIZEN("BRANCH_NON_CITIZEN", "NON_CITIZEN", "Branch Non Citizen", "Cawangan Bukan Warganegara"),
    BRANCH_REGISTRATION("BRANCH_REGISTRATION", "BRANCH", "Branch Registration", "Pendaftaran Cawangan"),
    BRANCH_EXTENSION_TIME("BRANCH_EXTENSION_TIME", "EXTENSION_TIME", "Branch Extension Time", "Lanjutan Masa Cawangan"),
    SOCIETY_PROPERTY_OFFICER("SOCIETY_PROPERTY_OFFICER","PROPERTY_OFFICER_APPLICATION","Society property officer application", "Permohonan Pegawai Harta Induk"),
    BRANCH_PROPERTY_OFFICER("BRANCH_PROPERTY_OFFICER","PROPERTY_OFFICER_APPLICATION","Branch property officer application", "Permohonan Pegawai Harta Cawangan"),
    SOCIETY_PUBLIC_OFFICER("SOCIETY_PUBLIC_OFFICER","PUBLIC_OFFICER","Society public officer", "Pegawai Awam Induk"),
    BRANCH_PUBLIC_OFFICER("BRANCH_PUBLIC_OFFICER","PUBLIC_OFFICER","Branch public officer", "Pegawai Awam Cawangan"),
    BRANCH_AMENDMENT("BRANCH_AMENDMENT","BRANCH","Branch Amendment", "Pindaan Cawangan"),
    ;


    private final String code;
    private final String type;
    private final String description;
    private final String descriptionMalay;

    public static ROApprovalType getROApprovalType(String roApprovalTypeCode) {
        for (ROApprovalType roApprovalType : ROApprovalType.values()) {
            if (Objects.equals(roApprovalType.code, roApprovalTypeCode)) {
                return roApprovalType;
            }
        }
        return null;
    }
}
