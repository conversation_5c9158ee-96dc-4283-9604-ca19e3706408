package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class PrincipalSecretary extends BaseEntity {
    private Long societyCommitteeId;
    private String jobCode;
    @OneToOne(fetch = FetchType.LAZY)
    private Society society;
    private Long societyId;
    private String societyNo;
    private String titleCode;
    private String committeeName;
    private String gender;
    private String citizenshipStatus;
    private String identificationType;
    private String identificationNo;
    private LocalDate dateOfBirth;
    private String placeOfBirth;
    private String committeePosition;
    private String employerAddressStatus;
    private String employerName;
    private String employerAddress;
    private String employerPostcode;
    private String employerCountryCode;
    private String employerStateCode;
    private String employerDistrictCode;
    private String employerCityCode;
    private String employerCity;
    private String residenceAddress;
    private String residencePostcode;
    private String residenceAddressStatus;
    private String residenceCountryCode;
    private String residenceStateCode;
    private String residenceDistrictCode;
    private String residenceCityCode;
    private String residenceCity;
    private String email;
    private String homeTelNo;
    private String hpNo;
    private String workTelNo;
    private Integer applicationStatusCode;
    private String reasonOfChange;
    private String oldSecretaryName;
    private String oldSecretaryIdentificationNumber;
    private String acknowledgement1;
    private String acknowledgement2;
    private String activeStatus;
    private String migrateStatSecretary;
    private Long meetingId;
    private LocalDate meetingDate;
    private String meetingType;
    private String pdfDocument;
    private LocalDate submitDate;
    private LocalDate transferDate;
    private String ro;
    private String roName;
    private String noteRo;
    private String createdByName;
    private Boolean isQueried;
}
