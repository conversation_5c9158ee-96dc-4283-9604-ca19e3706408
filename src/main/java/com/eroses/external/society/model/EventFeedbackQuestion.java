package com.eroses.external.society.model;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class EventFeedbackQuestion extends BaseEntity implements Serializable {
    @ApiModelProperty("event_id")
    public Long eventId;

    @ApiModelProperty("feedback_question_id")
    public Long feedbackQuestionId;
}
