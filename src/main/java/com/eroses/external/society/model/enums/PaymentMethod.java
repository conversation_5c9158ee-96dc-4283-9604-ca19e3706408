package com.eroses.external.society.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum PaymentMethod {
    KAUNTER("K", "KAUNTER","Kaunter / Counter Payment"),
    ONLINE("O", "ONLINE","Online Payment");

    private final String identifier;

    private final String code;

    private final String description;

    public static PaymentMethod getMethodByCode(String code) {
        for (PaymentMethod method : PaymentMethod.values()) {
            if (Objects.equals(method.code, code)) {
                return method;
            }
        }
        return null;
    }
}
