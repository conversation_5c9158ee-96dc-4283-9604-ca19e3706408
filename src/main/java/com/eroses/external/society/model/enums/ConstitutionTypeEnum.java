package com.eroses.external.society.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.EnumSet;
import java.util.Objects;

//for application_status_code columns

@Getter
public enum ConstitutionTypeEnum {
    //ENUMs from FE
    IndukNGO( "Perlembagaan Induk NGO", 1),
    CawanganNGO("Perlembagaan Bercawangan Semua NGO", 2),
    IndukAgama("Perlembagaan Induk Keagamaan", 3),
    CawanganAgama("Perlembagaan Bercawangan Keagamaan", 4),
    FaedahBersama("Perlembagaan Faedah Bersama", 5),
    Bebas("Perlembagaan Bebas", 6),
    BebasCawangan("Perlembagaan Bebas Bercawangan", 7);


    private final String code;
    private final Integer id;

    ConstitutionTypeEnum(String code, Integer id) {
        this.code = code;
        this.id = id;
    }

    public static ConstitutionTypeEnum getConstitutionType(String code) {
        for (ConstitutionTypeEnum type : ConstitutionTypeEnum.values()) {
            if (Objects.equals(type.code, code)) {
                return type;
            }
        }
        return null;
    }

    public static Long getConstitutionTypeId(String code) {
        for (ConstitutionTypeEnum type : ConstitutionTypeEnum.values()) {
            if (Objects.equals(type.code, code)) {
                return Long.valueOf(type.getId());
            }
        }
        return null;
    }

    public static EnumSet<ConstitutionTypeEnum> getCawanganType() {
        return EnumSet.of(CawanganAgama, CawanganNGO, BebasCawangan);
    }

    public static boolean isBranchType(Integer id) {
        return Arrays.asList(2, 4, 7).contains(id);
    }
}
