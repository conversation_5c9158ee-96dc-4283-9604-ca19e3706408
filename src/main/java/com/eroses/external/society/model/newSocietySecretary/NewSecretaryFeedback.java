package com.eroses.external.society.model.newSocietySecretary;

import com.eroses.external.society.model.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NewSecretaryFeedback extends BaseEntity {
    private Long id;
    private Long societyCommitteeId;
    private Long secretaryId;
    private Long societyId;
    private String societyNo;
    private String identificationNo;
    private String feedback;
    private String otherReason;
}