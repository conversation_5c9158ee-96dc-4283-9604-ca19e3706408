package com.eroses.external.society.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Data
@SuperBuilder
@NoArgsConstructor
public class TrainingMaterial extends BaseEntity {
    private Long id;
    private Long trainingCourseId;
    private String materialType; // Enum: 'VIDEO', 'PDF', 'TEXT', 'IMAGE'
    private String contentPath; // For files
    private String contentText; // For direct text content
    private Integer sequenceOrder;
    private String title;
    private String youtubeLink;
    private Integer duration;
    private String description;
    private String media;
}
