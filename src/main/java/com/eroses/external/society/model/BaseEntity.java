package com.eroses.external.society.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@SuperBuilder
@NoArgsConstructor
@ApiModel
public abstract class BaseEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty("Unique identifier")
    private Long id;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("Timestamp when the position was created")
    private LocalDateTime createdDate;

    @ApiModelProperty("User who created the record")
    private Long createdBy;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("Timestamp when the position was last modified")
    private LocalDateTime modifiedDate;

    @ApiModelProperty("User who last modified the record")
    private Long modifiedBy;

    public void initializeAuditFields(Long userId) {
        LocalDateTime now = LocalDateTime.now();
        this.createdDate = now;
        this.modifiedDate = now;
        this.createdBy = userId;
        this.modifiedBy = userId;
    }

    public void updateAuditFields(Long userId) {
        this.modifiedDate = LocalDateTime.now();
        this.modifiedBy = userId;
    }

    public void updateAuditFieldsBySystem() {
        this.modifiedDate = LocalDateTime.now();
        this.modifiedBy = 0L;
    }
}
