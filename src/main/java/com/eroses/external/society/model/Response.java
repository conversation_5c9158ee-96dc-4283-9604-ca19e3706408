package com.eroses.external.society.model;

import java.io.Serializable;
import java.util.Objects;
import java.util.function.Supplier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Response<T> implements Serializable {
    private static final long serialVersionUID = -750644833749014618L;
    private static final Logger logger = LoggerFactory.getLogger(Response.class);
    private boolean success;
    private T result;
    private String code;
    private Object[] args;
    private String error;
    private String sourceIp;
    private String sourceStack;

    public Response() {
    }

    public boolean isSuccess() {
        return this.success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    private void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return this.code;
    }

    public Object[] getArgs() {
        return this.args;
    }

    public void setArgs(Object[] args) {
        this.args = args;
    }

    public boolean errorOf(String codeToCompare) {
        return Objects.equals(this.code, codeToCompare);
    }

    public void setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
    }

    public String getSourceIp() {
        return this.sourceIp;
    }

    public void setSourceStack(String sourceStack) {
        this.sourceStack = sourceStack;
    }

    public String getSourceStack() {
        return this.sourceStack;
    }

    public T getResult() {
        return this.result;
    }

    public void setResult(T result) {
        this.success = true;
        this.result = result;
    }

    public String getError() {
        return this.error;
    }

    public void setError(String error) {
        this.success = false;
        this.error = error;
    }

    public void setError(String code, String error) {
        this.success = false;
        this.code = code;
        this.error = error;
    }

    public void setError(String code, String error, String sourceStack) {
        this.success = false;
        this.code = code;
        this.error = error;
        this.sourceStack = sourceStack;
    }

    public void setError(String code, Object[] args) {
        this.success = false;
        this.code = code;
        this.args = args;
    }

    public void setError(String code, String error, Object[] args) {
        this.success = false;
        this.error = error;
        this.code = code;
        this.args = args;
    }

    public void setError(String code, String error, Object[] args, String sourceStack) {
        this.success = false;
        this.error = error;
        this.code = code;
        this.args = args;
        this.sourceStack = sourceStack;
    }


    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof Response)) {
            return false;
        } else {
            Response other = (Response)o;
            if (!Objects.equals(this.success, other.success)) {
                return false;
            } else if (!Objects.equals(this.result, other.result)) {
                return false;
            } else if (!Objects.equals(this.code, other.code)) {
                return false;
            } else if (!Objects.equals(this.error, other.error)) {
                return false;
            } else if (!Objects.equals(this.sourceIp, other.sourceIp)) {
                return false;
            } else {
                return Objects.equals(this.sourceStack, other.sourceStack);
            }
        }
    }


    public static <T> Response<T> ok(T data) {
        Response<T> resp = new Response();
        resp.setResult(data);
        return resp;
    }

    public static <T> Response<T> fail(String error) {
        Response<T> resp = new Response();
        resp.setError(error);
        return resp;
    }

    public static <T> Response<T> get(Supplier<T> supplier, String errorCode) {
        try {
            T result = supplier.get();
            return ok(result);
        } catch (Exception var3) {
            logger.error("error when get response call", var3);
            return fail(errorCode);
        }
    }

    public static <T> Response<T> fail(String code, String error) {
        Response<T> resp = new Response();
        resp.setError(code, error);
        return resp;
    }

    public static <T> Response<T> fail(String code, String error, String sourceStack) {
        Response<T> resp = new Response();
        resp.setError(code, error, sourceStack);
        return resp;
    }

    public static <T> Response<T> fail(String code, Object[] args) {
        Response<T> resp = new Response();
        resp.setError(code, args);
        return resp;
    }

    public static <T> Response<T> fail(String code, String error, Object[] args) {
        Response<T> resp = new Response();
        resp.setError(code, error, args);
        return resp;
    }

    public static <T> Response<T> fail(String code, String error, Object[] args, String sourceStack) {
        Response<T> resp = new Response();
        resp.setError(code, error, args, sourceStack);
        return resp;
    }

    public Response<T> code(String code) {
        this.code = code;
        return this;
    }

    public Response<T> sourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
        return this;
    }

    public Response<T> sourceStack(String sourceStack) {
        this.sourceStack = sourceStack;
        return this;
    }
}

