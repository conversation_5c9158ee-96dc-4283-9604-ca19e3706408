package com.eroses.external.society.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
public class QuizAttempt extends BaseEntity {
    private Long trainingEnrollmentId;
    private Long trainingQuizId;
    private Integer attemptNumber;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer score;
    private <PERSON>olean passed;

    // Transient field for answers, not stored in DB directly
    private List<QuizAnswer> answers;
}
