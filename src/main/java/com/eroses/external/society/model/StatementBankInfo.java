package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class StatementBankInfo extends BaseEntity {
    private Long id;
    private Long societyId;
    private String societyNo;
    private Long branchId;
    private String branchNo;
    private Long statementId;
    private String bankName;
    private String accountNo;
    private String applicationStatusCode;
}
