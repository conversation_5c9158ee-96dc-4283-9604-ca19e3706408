package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class NonCitizenCommittee extends BaseEntity {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("ids")
    private List<Long> ids;

    @ApiModelProperty("activeCommitteeId")
    private Long activeCommitteeId;

    @ApiModelProperty("name")
    private String name;

    @ApiModelProperty("citizenship status")
    private String citizenshipStatus;

    @ApiModelProperty("identification type")
    private String identificationType;

    @ApiModelProperty("identification number")
    private String identificationNo;

    @ApiModelProperty("applicant country code")
    private String applicantCountryCode;

    @ApiModelProperty("visa expiration date")
    private LocalDate visaExpirationDate;

    @ApiModelProperty("permit expiration date")
    private LocalDate permitExpirationDate;

    @ApiModelProperty("visa number")
    private String visaNo;

    @ApiModelProperty("permit number")
    private String permitNo;

    @ApiModelProperty("designation code")
    private String designationCode;

    @ApiModelProperty("visa or permit number")
    private String visaPermitNo;

    @ApiModelProperty("purpose of visit to Malaysia")
    private String tujuanDMalaysia;

    @ApiModelProperty("duration of stay in Malaysia")
    private Integer stayDurationDigit;

    @ApiModelProperty("duration unit")
    private String stayDurationUnit;

    @ApiModelProperty("residential address")
    private String residentialAddress;

    @ApiModelProperty("residential city")
    private String residentialCity;

    @ApiModelProperty("summary")
    private String summary;

    @ApiModelProperty("society name")
    private String societyName;

    @ApiModelProperty("society")
    @OneToOne(fetch = FetchType.LAZY)
    private Society society;

    @ApiModelProperty("society id")
    private Long societyId;

    @ApiModelProperty("society number")
    private String societyNo;

    @ApiModelProperty("branch")
    @OneToOne(fetch = FetchType.LAZY)
    private Branch branch;

    @ApiModelProperty("branch id")
    private Long branchId;

    @ApiModelProperty("branch number")
    private String branchNo;

    @ApiModelProperty("application status code")
    private Integer applicationStatusCode;

    @ApiModelProperty("status")
    private String status;

    @ApiModelProperty("ro")
    private String ro;

    @ApiModelProperty("renewal code SU")
    private String pembaharuanSu;

    @ApiModelProperty("pem caw")
    private String pemCaw;

    @ApiModelProperty("other designation code")
    private String otherDesignationCode;

    @ApiModelProperty("transfer date")
    private LocalDate transferDate;

    @ApiModelProperty("note ro")
    private String noteRo;

    @ApiModelProperty("meeting id")
    private Long meetingId;

    @ApiModelProperty("documentType")
    private Boolean documentType;

    @ApiModelProperty("Appointment Date")
    private LocalDate appointedDate;

    @ApiModelProperty("payment id")
    private Long paymentId;

    @ApiModelProperty("Payment date")
    private LocalDate paymentDate;

    @ApiModelProperty("Payment method")
    private String paymentMethod;

    @ApiModelProperty("appealStatus")
    private Integer appealStatus;

    @ApiModelProperty("titleCode")
    private String titleCode;

    @ApiModelProperty("gender")
    private String gender;

    @ApiModelProperty("nationalityStatus")
    private String nationalityStatus;

    @ApiModelProperty("email")
    private String email;

    @ApiModelProperty("phoneNumber")
    private String phoneNumber;

    @ApiModelProperty("telephoneNumber")
    private String telephoneNumber;

    @ApiModelProperty("isFromRegistration")
    private Boolean isFromRegistration;
}
