package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class CommitteeTask extends BaseEntity implements Serializable {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("societyId")
    private Long societyId;

    @ApiModelProperty("societyNo")
    private String societyNo;

    @ApiModelProperty("branchId")
    private Long branchId;

    @ApiModelProperty("branchNo")
    private String branchNo;

    @ApiModelProperty("societyCommitteeId")
    private Long societyCommitteeId;

    @ApiModelProperty("branchCommitteeId")
    private Long branchCommitteeId;

    @ApiModelProperty("identificationNo")
    private String identificationNo;

    @ApiModelProperty("designationCode")
    private String designationCode;

    @ApiModelProperty("statementId")
    private Long statementId;

    @ApiModelProperty("name")
    private String name;

    @ApiModelProperty("status")
    private String status;

    @ApiModelProperty("taskActivateDate")
    private LocalDate taskActivateDate;

    @ApiModelProperty("taskDeactivateDate")
    private LocalDate taskDeactivateDate;

    private String module;
}
