package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ForbiddenKeyword extends ForbiddenClass implements Serializable {
    @ApiModelProperty("keyword")
    public String keyword;

    @ApiModelProperty("forbidden_type")
    public String forbiddenType;

}
