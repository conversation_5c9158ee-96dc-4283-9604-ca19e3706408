package com.eroses.external.society.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum S3FolderPath {
    SEARCH_INFORMATION_DOCUMENT_TEMPLATE("Search Information Document Template", "searchInformation/documentTemplate"),
    SEARCH_INFORMATION_DOCUMENT("Search Information Document", "searchInformation/document"),
    SOCIETY_DOCUMENT("Society Document", "society/document"),
    LIQUIDATION_DOCUMENT("Liquidation Document", "liquidation/document"),
    STATEMENT_DOCUMENT("Statement Document", "statement/document"),
    BR<PERSON>CH_DOCUMENT("Branch Document", "branch/document"),
    APPEAL_DOCUMENT("Appeal Document", "appeal/document");

    private final String name;
    private final String path;
}
