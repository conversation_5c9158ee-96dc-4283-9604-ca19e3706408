package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class StatementSocietyInfo extends BaseEntity {
    private Long id;
    private Long societyId;
    private String societyNo;
    private Long branchId;
    private String branchNo;
    private Long statementId;
    private String societyName;
    private String address;
    private String category;
    private String subcategory;
    private String societyPhoneNo;
    private String societyFaxNo;
    private String financialYearStart;
    private Long memberCount;
    private Long committeeCount;
    private Boolean federation;
    private Long meetingId;
    private String meetingFrequency;
    private LocalDate meetingDate;
    private Integer regMemberCount;
    private Integer votingMemberCount;
    private Integer attendanceCount;
    private Boolean approvalStatement;
    private LocalDate statementDate;
    private LocalDate submissionDate;
    private Boolean acknowledgeAjk;
    private String applicationStatusCode;
    private String ro;
}
