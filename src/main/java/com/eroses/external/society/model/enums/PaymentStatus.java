package com.eroses.external.society.model.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum PaymentStatus {
    PAID(1, "PAID", "P"),
    UNPAID(2, "UNPAID", "UP"),
    CANCEL(3, "CANCEL", "C"),
    FAILED(4, "FAILED", "F");

    private final int code;
    private final String name;
    private final String identifier;

    PaymentStatus(int code, String name, String identifier) {
        this.code = code;
        this.name = name;
        this.identifier = identifier;
    }

    public static PaymentStatus getStatus(String name) {
        for (PaymentStatus status : PaymentStatus.values()) {
            if (Objects.equals(status.name, name)) {
                return status;
            }
        }
        return null;
    }

    public static PaymentStatus getStatusByCode(int code) {
        for (PaymentStatus status : PaymentStatus.values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }
        return null;
    }
}
