package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class SecretaryCommitteeFeedback extends BaseEntity {

    @ApiModelProperty("societyCommitteeId")
    private Long societyCommitteeId;

    @ApiModelProperty("secretaryId")
    private Long secretaryId;

    @ApiModelProperty("societyId")
    private Long societyId;

    @ApiModelProperty("societyNo")
    private String societyNo;

    @ApiModelProperty("identificationNo")
    private String identificationNo;

    @ApiModelProperty("feedback")
    private String feedback;

    @ApiModelProperty("otherReason")
    private String otherReason;
}
