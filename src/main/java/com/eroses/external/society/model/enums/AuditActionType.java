package com.eroses.external.society.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AuditActionType {
    BRANCH_EDIT("BRANCH_EDIT", "CAWANGAN"),
    BRANCH_CREATE("BRANCH_CREATE", "CAWA<PERSON>AN"),
    BRANCH_APPROVAL_EDIT("BRANCH_APPROVAL_EDIT", "CAWA<PERSON>AN"),
    BRANCH_APPROVAL_DECISION_EDIT("BRANCH_APPROVAL_DECISION_EDIT", "CAWANGAN"),
    BRANCH_EXTENSION_EDIT("BRANCH_EXTENSION_EDIT", "<PERSON><PERSON><PERSON><PERSON>"),
    MEETING_CREATE("MEETING_CREATE", "<PERSON>SYUAR<PERSON>"),
    MEETING_EDIT("MEETING_EDIT", "MESYUARAT"),
    AMENDMENT_CREATE("AMENDMENT_CREATE", "PIN<PERSON><PERSON>"),
    AMENDMENT_EDIT("AMENDMENT_EDIT", "<PERSON>IN<PERSON><PERSON>"),
    SOCIETY_CREATE("SOCIETY_CREATE", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"),
    SOCIETY_EDIT("SOCIETY_EDIT", "PERTUBUHAN"),
    NON_CITIZEN_COMMITTEE_CREATE("NON_CITIZEN_COMMITTEE_CREATE", "AJK BUKAN WARGANEGARA"),
    NON_CITIZEN_COMMITTEE_EDIT("NON_CITIZEN_COMMITTEE_EDIT", "AJK BUKAN WARGANEGARA"),
    COMMITTEE_CREATE("COMMITTEE_CREATE", "AJK"),
    COMMITTEE_EDIT("COMMITTEE_EDIT", "AJK"),
    BRANCH_COMMITTEE_CREATE("BRANCH_COMMITTEE_CREATE", "AJK CAWANGAN"),
    TRUSTEE_HOLDER_CREATE("TRUSTEE_HOLDER_CREATE", "PEMEGANG AMANAH"),
    TRUSTEE_HOLDER_UPDATE("TRUSTEE_HOLDER_UPDATE", "PEMEGANG AMANAH"),
    TRUSTEE_HOLDER_DEACTIVATE("TRUSTEE_HOLDER_DEACTIVATE", "PEMEGANG AMANAH"),
    PUBLIC_OFFICER_CREATE("PUBLIC_OFFICER_CREATE", "PEGAWAI AWAM"),
    PUBLIC_OFFICER_UPDATE("PUBLIC_OFFICER_UPDATE", "PEGAWAI AWAM"),
    PUBLIC_OFFICER_DELETE("PUBLIC_OFFICER_DELETE", "PEGAWAI AWAM"),

    //New integration
    LIQUIDATION_CREATE("LIQUIDATION_CREATE", "PEMBUBARAN"),
    LIQUIDATION_EDIT("LIQUIDATION_EDIT", "PEMBUBARAN"),
    APPEAL_CREATE("APPEAL_CREATE", "RAYUAN"),
    APPEAL_EDIT("APPEAL_EDIT", "RAYUAN"),

    // **Internal User Actions**
    INTERNAL_SOCIETY_REGISTRATION_UPDATE_RO("INTERNAL_SOCIETY_REGISTRATION_UPDATE_RO", "PERTUBUHAN (DALAMAN)"), // Pendaftaran pertubuhan Induk
    INTERNAL_SOCIETY_REGISTRATION_DECISION("INTERNAL_SOCIETY_REGISTRATION_DECISION", "PERTUBUHAN (DALAMAN)"),
    INTERNAL_SOCIETY_EXTERNAL_REVIEW_UPDATE_RO("INTERNAL_SOCIETY_EXTERNAL_REVIEW_UPDATE_RO", "PERTUBUHAN (DALAMAN)"), // Pendaftaran Induk - menunggu ulasan luar
    INTERNAL_SOCIETY_EXTERNAL_REVIEW_DECISION("INTERNAL_SOCIETY_EXTERNAL_REVIEW_DECISION", "PERTUBUHAN (DALAMAN)"),
    INTERNAL_CONSTITUTION_AMENDMENT_UPDATE_RO("INTERNAL_CONSTITUTION_AMENDMENT_UPDATE_RO", "PINDAAN PERLEMBAGAAN (DALAMAN)"), // Pindaan Perlembagaan
    INTERNAL_CONSTITUTION_AMENDMENT_DECISION("INTERNAL_CONSTITUTION_AMENDMENT_DECISION", "PINDAAN PERLEMBAGAAN (DALAMAN)"),
    INTERNAL_SOCIETY_LIQUIDATION_UPDATE_RO("INTERNAL_SOCIETY_LIQUIDATION_UPDATE_RO", "PEMBUBARAN (DALAMAN)"), // Pembubaran
    INTERNAL_SOCIETY_LIQUIDATION_DECISION("INTERNAL_SOCIETY_LIQUIDATION_DECISION", "PEMBUBARAN (DALAMAN)"),
    INTERNAL_BRANCH_LIQUIDATION_UPDATE_RO("INTERNAL_BRANCH_LIQUIDATION_UPDATE_RO", "PEMBUBARAN CAWANGAN (DALAMAN)"),
    INTERNAL_BRANCH_LIQUIDATION_DECISION("INTERNAL_BRANCH_LIQUIDATION_DECISION", "PEMBUBARAN CAWANGAN (DALAMAN)"),
    INTERNAL_SOCIETY_NON_CITIZEN_APPLICATION_UPDATE_RO("INTERNAL_SOCIETY_NON_CITIZEN_APPLICATION_UPDATE_RO", "AJK BUKAN WARGANEGARA (DALAMAN)"), // Permohonan bukan Warganegara
    INTERNAL_SOCIETY_NON_CITIZEN_APPLICATION_DECISION("INTERNAL_SOCIETY_NON_CITIZEN_APPLICATION_DECISION", "AJK BUKAN WARGANEGARA (DALAMAN)"),
    INTERNAL_BRANCH_NON_CITIZEN_APPLICATION_UPDATE_RO("INTERNAL_BRANCH_NON_CITIZEN_APPLICATION_UPDATE_RO", "AJK BUKAN WARGANEGARA CAWANGAN (DALAMAN)"), // Permohonan bukan Warganegara
    INTERNAL_BRANCH_NON_CITIZEN_APPLICATION_DECISION("INTERNAL_BRANCH_NON_CITIZEN_APPLICATION_DECISION", "AJK BUKAN WARGANEGARA CAWANGAN (DALAMAN)"),
    INTERNAL_SOCIETY_SECRETARY_RENEWAL_RO("INTERNAL_SOCIETY_SECRETARY_RENEWAL_UPDATE_RO", "PEMBAHARUAN SETIAUSAHA (DALAMAN)"), // Pembaharuan Setiausaha
    INTERNAL_SOCIETY_SECRETARY_RENEWAL_DECISION("INTERNAL_SOCIETY_SECRETARY_RENEWAL_DECISION", "PEMBAHARUAN SETIAUSAHA (DALAMAN)"),
    INTERNAL_BRANCH_SECRETARY_RENEWAL_RO("INTERNAL_BRANCH_SECRETARY_RENEWAL_UPDATE_RO", "PEMBAHARUAN SETIAUSAHA CAWANGAN (DALAMAN)"), // Pembaharuan Setiausaha
    INTERNAL_BRANCH_SECRETARY_RENEWAL_DECISION("INTERNAL_BRANCH_SECRETARY_RENEWAL_DECISION", "PEMBAHARUAN SETIAUSAHA CAWANGAN (DALAMAN)"),
    INTERNAL_SOCIETY_PUBLIC_OFFICER_UPDATE_RO("INTERNAL_SOCIETY_PUBLIC_OFFICER_UPDATE_RO", "PEGAWAI AWAM (DALAMAN)"), // Pegawai Awam
    INTERNAL_SOCIETY_PUBLIC_OFFICER_DECISION("INTERNAL_SOCIETY_PUBLIC_OFFICER_DECISION", "PEGAWAI AWAM (DALAMAN)"),
    INTERNAL_SOCIETY_PROPERTY_OFFICER_UPDATE_RO("INTERNAL_SOCIETY_PROPERTY_OFFICER_UPDATE_RO", "PEGAWAI HARTA (DALAMAN)"), // Pegawai Harta
    INTERNAL_SOCIETY_PROPERTY_OFFICER_DECISION("INTERNAL_SOCIETY_PROPERTY_OFFICER_DECISION", "PEGAWAI HARTA (DALAMAN)"),
    INTERNAL_BRANCH_PUBLIC_OFFICER_UPDATE_RO("INTERNAL_BRANCH_PUBLIC_OFFICER_UPDATE_RO", "PEGAWAI AWAM CAWANGAN (DALAMAN)"), // Pegawai Awam
    INTERNAL_BRANCH_PUBLIC_OFFICER_DECISION("INTERNAL_BRANCH_PUBLIC_OFFICER_DECISION", "PEGAWAI AWAM CAWANGAN (DALAMAN)"),
    INTERNAL_BRANCH_PROPERTY_OFFICER_UPDATE_RO("INTERNAL_BRANCH_PROPERTY_OFFICER_UPDATE_RO", "PEGAWAI HARTA CAWANGAN (DALAMAN)"), // Pegawai Harta
    INTERNAL_BRANCH_PROPERTY_OFFICER_DECISION("INTERNAL_BRANCH_PROPERTY_OFFICER_DECISION", "PEGAWAI HARTA CAWANGAN (DALAMAN)"),
    INTERNAL_BRANCH_EXTENSION_TIME_UPDATE_RO("INTERNAL_BRANCH_EXTENSION_TIME_UPDATE_RO", "LANJUTAN MASA CAWANGAN (DALAMAN)"),
    INTERNAL_BRANCH_EXTENSION_TIME_DECISION("INTERNAL_BRANCH_EXTENSION_TIME_DECISION", "LANJUTAN MASA CAWANGAN (DALAMAN)"),
    INTERNAL_BRANCH_CHANGE_NAME_AND_ADDRESS_UPDATE_RO("INTERNAL_BRANCH_CHANGE_NAME_AND_ADDRESS_UPDATE_RO", "PINDAAN CAWANGAN (DALAMAN)"),
    INTERNAL_BRANCH_CHANGE_NAME_AND_ADDRESS_DECISION("INTERNAL_BRANCH_CHANGE_NAME_AND_ADDRESS_DECISION", "PINDAAN CAWANGAN (DALAMAN)"),

    // Pengurusan Peranan
    ROLE_CATEGORY_SEARCH("ROLE_CATEGORY_SEARCH", "PENGURUSAN PERANAN"),
    ROLE_CATEGORY_ADD("ROLE_CATEGORY_ADD", "PENGURUSAN PERANAN"),
    ROLE_CATEGORY_UPDATE("ROLE_CATEGORY_UPDATE", "PENGURUSAN PERANAN"),
    ROLE_CATEGORY_VIEW("ROLE_CATEGORY_VIEW", "PENGURUSAN PERANAN"),
    PAGE_ACCESS_SEARCH("PAGE_ACCESS_SEARCH", "PENGURUSAN PERANAN"),
    PAGE_ACCESS_ADD("PAGE_ACCESS_ADD", "PENGURUSAN PERANAN"),
    PAGE_ACCESS_UPDATE("PAGE_ACCESS_UPDATE", "PENGURUSAN PERANAN"),
    PAGE_ACCESS_VIEW("PAGE_ACCESS_VIEW", "PENGURUSAN PERANAN"),
    PAGE_ACCESS_DELETE("PAGE_ACCESS_DELETE", "PENGURUSAN PERANAN"),

    // Pengurusan Pengguna
    JPPM_USER_SEARCH("JPPM_USER_SEARCH", "PENGURUSAN PENGGUNA"),
    JPPM_USER_VIEW("JPPM_USER_VIEW", "PENGURUSAN PENGGUNA"),
    JPPM_USER_ADD("JPPM_USER_ADD", "PENGURUSAN PENGGUNA"),
    JPPM_USER_UPDATE("JPPM_USER_UPDATE", "PENGURUSAN PENGGUNA"),
    JPPM_USER_APPROVAL("JPPM_USER_APPROVAL", "PENGURUSAN PENGGUNA"),
    JPPM_USER_ROLE_UPDATE("JPPM_USER_ROLE_UPDATE", "PENGURUSAN PENGGUNA"),
    EXTERNAL_USER_SEARCH("EXTERNAL_USER_SEARCH", "PENGURUSAN PENGGUNA"),
    EXTERNAL_USER_UPDATE("EXTERNAL_USER_UPDATE", "PENGURUSAN PENGGUNA"),
    EXTERNAL_USER_DEACTIVATE("EXTERNAL_USER_DEACTIVATE", "PENGURUSAN PENGGUNA"), //Refer to delete button in the searching external user screen
    EXTERNAL_USER_VIEW("EXTERNAL_USER_VIEW", "PENGURUSAN PENGGUNA"),

    //Penyata Tahunan
    STATEMENT_CREATE("STATEMENT_CREATE", "PENYATA TAHUNAN"),
    STATEMENT_EDIT("STATEMENT_EDIT", "PENYATA TAHUNAN"),
    STATEMENT_SUBMIT("STATEMENT_SUBMIT", "PENYATA TAHUNAN"),
    STATEMENT_SOCIETY_INFO_ADD("STATEMENT_SOCIETY_INFO_ADD", "PENYATA TAHUNAN"),
    STATEMENT_SOCIETY_INFO_EDIT("STATEMENT_SOCIETY_INFO_EDIT", "PENYATA TAHUNAN"),
    STATEMENT_BANK_INFO_ADD("STATEMENT_BANK_INFO_ADD", "PENYATA TAHUNAN"),
    STATEMENT_BANK_INFO_EDIT("STATEMENT_BANK_INFO_EDIT", "PENYATA TAHUNAN"),
    STATEMENT_CONTRIBUTION_ADD("STATEMENT_CONTRIBUTION_ADD", "PENYATA TAHUNAN"),
    STATEMENT_CONTRIBUTION_EDIT("STATEMENT_CONTRIBUTION_EDIT", "PENYATA TAHUNAN"),
    STATEMENT_FINANCIAL_ADD("STATEMENT_FINANCIAL_ADD", "PENYATA TAHUNAN"),
    STATEMENT_FINANCIAL_EDIT("STATEMENT_FINANCIAL_EDIT", "PENYATA TAHUNAN"),

    //Takwim
    EVENT_PARTICIPANT_CANCEL("EVENT_PARTICIPANT_CANCEL", "TAKWIM"),
    EVENT_DETAILS_UPDATE("EVENT_DETAILS_UPDATE", "TAKWIM"),
    EVENT_DETAILS_CREATE("EVENT_DETAILS_CREATE", "TAKWIM"),
    EVENT_DETAILS_PUBLISH("EVENT_DETAILS_PUBLISH", "TAKWIM"),
    EVENT_DETAILS_UNPUBLISH("EVENT_DETAILS_UNPUBLISH", "TAKWIM"),
    EVENT_DETAILS_DELETE("EVENT_DETAILS_DELETE", "TAKWIM"),

    SENARAI_LARANGAN_UPDATE("SENARAI_LARANGAN_UPDATE", "LARANGAN"),
    SENARAI_LARANGAN_DELETE("SENARAI_LARANGAN_DELETE", "LARANGAN"),
    LARANGAN_LOGO_UPDATE("LARANGAN_LOGO_UPDATE", "LARANGAN"),
    LARANGAN_LOGO_DELETE("LARANGAN_LOGO_DELETE", "LARANGAN");
    private final String actionType;
    private final String module;

    public String getActionType() {
        return actionType;
    }

    public static AuditActionType getAuditActionByActionType(String actionType) {
        for (AuditActionType auditActionType : AuditActionType.values()) {
            if (auditActionType.getActionType().equals(actionType)) {
                return auditActionType;
            }
        }
        return null;
    }
}
