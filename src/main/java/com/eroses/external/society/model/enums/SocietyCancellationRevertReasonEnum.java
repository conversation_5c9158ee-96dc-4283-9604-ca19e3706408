
package com.eroses.external.society.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum representing the different reasons for reverting a society cancellation.
 */
@Getter
@AllArgsConstructor
public enum SocietyCancellationRevertReasonEnum {
    COURT_ORDER("COURT_ORDER", "Perintah Mahkamah"),
    OTHERS("OTHERS", "Lain-Lain");

    private final String code;
    private final String description;

    /**
     * Find enum by code.
     *
     * @param code the code
     * @return the enum value or null if not found
     */
    public static SocietyCancellationRevertReasonEnum findByCode(String code) {
        for (SocietyCancellationRevertReasonEnum reason : values()) {
            if (reason.getCode().equals(code)) {
                return reason;
            }
        }
        return null;
    }

    /**
     * Find enum by description.
     *
     * @param description the description
     * @return the enum value or null if not found
     */
    public static SocietyCancellationRevertReasonEnum findByDescription(String description) {
        for (SocietyCancellationRevertReasonEnum reason : values()) {
            if (reason.getDescription().equals(description)) {
                return reason;
            }
        }
        return null;
    }
}