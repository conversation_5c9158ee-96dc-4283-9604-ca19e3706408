package com.eroses.external.society.model;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.Email;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class EventAttendees extends BaseEntity implements Serializable {

    @ApiModelProperty("event_id")
    public Long eventId;

    @ApiModelProperty("identification_no")
    public String identificationNo;

    @ApiModelProperty("email")
    public String email;

    @ApiModelProperty("phone_number")
    public String phoneNumber;

    @ApiModelProperty("society_id")
    public Long societyId;

    @ApiModelProperty("application_status_code")
    public int applicationStatusCode;

    @ApiModelProperty("state")
    public String state;

    @ApiModelProperty("present")
    public Boolean present;

    @ApiModelProperty("full_name")
    public String fullName;

    @ApiModelProperty("event_certificate_name")
    public String eventCertificateName;

    @ApiModelProperty("is_feedback_completed")
    public Boolean isFeedbackCompleted;

    @ApiModelProperty("attendance_no")
    public String attendanceNo;

    @ApiModelProperty("cancelled_date_time")
    public LocalDateTime cancelledDateTime;

    @ApiModelProperty("registered_date_time")
    public LocalDateTime registeredDateTime;

    @ApiModelProperty("checked_in_date_time")
    public LocalDateTime checkedInDateTime;

    public String eventName;


    public EventAttendees(EventAttendees existingAttendee) {
        this.eventId = existingAttendee.getEventId();
        this.identificationNo = existingAttendee.getIdentificationNo();
        this.email = existingAttendee.getEmail();
        this.phoneNumber = existingAttendee.getPhoneNumber();
        this.societyId = existingAttendee.getSocietyId();
        this.applicationStatusCode = existingAttendee.getApplicationStatusCode();
        this.eventName = existingAttendee.getEventName();

    }
}
