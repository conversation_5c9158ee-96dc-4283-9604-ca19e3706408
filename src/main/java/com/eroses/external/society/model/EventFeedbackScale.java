package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class EventFeedbackScale extends BaseEntity implements Serializable {
    @ApiModelProperty("code")
    public String code;

    @ApiModelProperty("label")
    public String label;

    @ApiModelProperty("sort_order")
    public Integer sortOrder;



}
