package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class AdmMeeting extends BaseEntity {
    @ApiModelProperty("id")
    public Long id;

    @ApiModelProperty("pid")
    public Long pid;

    @ApiModelProperty("nameEn")
    public String nameEn;

    @ApiModelProperty("nameBm")
    public String nameBm;

    @ApiModelProperty("desciption")
    public String desciption;

    @ApiModelProperty("status")
    public Integer status;

}
