
package com.eroses.external.society.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * Enum representing the different sections under which a society can be cancelled.
 */
@Getter
@AllArgsConstructor
public enum SocietyCancellationDetailSectionEnum {
    SECTION_13_1_A("SECTION_13_1_A", "13(1)(a)", "13", false),
    SECTION_13_1_B("SECTION_13_1_B", "13(1)(b)", "13", false),
    SECTION_13_1_C_I("SECTION_13_1_C_I", "13(1)(c)(i)", "13", false),
    SECTION_13_1_C_II("SECTION_13_1_C_II", "13(1)(c)(ii)", "13", false),
    SECTION_13_1_C_III("SECTION_13_1_C_III", "13(1)(c)(iii)", "13", false),
    SECTION_13_1_C_IV("SECTION_13_1_C_IV", "13(1)(c)(iv)", "13", false),
    SECTION_13_1_C_V("SECTION_13_1_C_V", "13(1)(c)(v)", "13", false),
    SECTION_13_1_C_VI("SECTION_13_1_C_VI", "13(1)(c)(vi)", "13", false),
    SECTION_13_1_C_VII("SECTION_13_1_C_VII", "13(1)(c)(vii)", "13", true),
    SECTION_13_1_C_VIII("SECTION_13_1_C_VIII", "13(1)(c)(viii)", "13", false),
    SECTION_13_1_C_IX("SECTION_13_1_C_IX", "13(1)(c)(ix)", "13", false),
    SECTION_13_1_D("SECTION_13_1_D", "13(1)(d)", "13", false),
    SECTION_9A_1_A("SECTION_9A_1_A", "9A(1)(a)", "9A", true),
    SECTION_9A_1_B("SECTION_9A_1_B", "9A(1)(b)", "9A", true),
    SECTION_9A_1_C("SECTION_9A_1_C", "9A(1)(c)", "9A", true),
    SECTION_9A_1_D("SECTION_9A_1_D", "9A(1)(d)", "9A", true),
    SECTION_9A_1_E("SECTION_9A_1_E", "9A(1)(e)", "9A", true);

    private final String code;
    private final String description;
    private final String section;
    private final boolean isBlacklistPermanent;

    /**
     * Find enum by code.
     *
     * @param code the code
     * @return the enum value or null if not found
     */
    public static SocietyCancellationDetailSectionEnum findByCode(String code) {
        for (SocietyCancellationDetailSectionEnum section : values()) {
            if (section.getCode().equals(code)) {
                return section;
            }
        }
        return null;
    }

    /**
     * Find enum by description.
     *
     * @param description the description
     * @return the enum value or null if not found
     */
    public static SocietyCancellationDetailSectionEnum findByDescription(String description) {
        for (SocietyCancellationDetailSectionEnum section : values()) {
            if (section.getDescription().equals(description)) {
                return section;
            }
        }
        return null;
    }

    /**
     * Find all enums by section.
     *
     * @param section the section (13 or 9A)
     * @return a list of enum values that match the given section
     */
    public static List<SocietyCancellationDetailSectionEnum> findAllBySection(String section) {
        List<SocietyCancellationDetailSectionEnum> result = new ArrayList<>();
        for (SocietyCancellationDetailSectionEnum sectionEnum : values()) {
            if (sectionEnum.getSection().equals(section)) {
                result.add(sectionEnum);
            }
        }
        return result;
    }
}