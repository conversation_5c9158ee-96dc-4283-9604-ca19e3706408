package com.eroses.external.society.model.enums;

import lombok.Getter;

@Getter
public enum EventEnum {


    EVENT_PRIVATE(1, "PRIVATE"),
    EVENT_PUBLIC(2, "PUBLIC");


    private final int code;
    private final String description;

    EventEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

//    public static EventEnum getNameByCode(String code) {
//        for (AdmBranchEnum branch : AdmBranchEnum.values()) {
//            if (branch.code.equals(code)) {
//                return branch;
//            }
//        }
//        return null;
//    }
}
