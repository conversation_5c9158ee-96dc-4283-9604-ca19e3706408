package com.eroses.external.society.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum SearchInformationDocumentTemplateEnum {
    SIJIL_KELULUSAN_PERMOHONAN_INDUK("Sijil <PERSON> Permohonan Induk", "SIJIL_KELULUSAN_PERMOHONAN_INDUK"),
    SENARAI_AHLI_JAWATAN_KUASA("Senarai Ahli Jawatan Kuasa", "SENARAI_AHLI_JAWATAN_KUASA"),
    PERLEMBAGAAN("Perlembagaan", "PERLEMBAGAAN_TERKINI"),
    PENYATA_TAHUNAN("Penya<PERSON>", "PENYATA_TAHUNAN");

    private final String name;

    private final String code;

    public static SearchInformationDocumentTemplateEnum getDocumentTemplate(String code) {
        for (SearchInformationDocumentTemplateEnum documentTemplateEnum : SearchInformationDocumentTemplateEnum.values()) {
            if (Objects.equals(documentTemplateEnum.code, code)) {
                return documentTemplateEnum;
            }
        }
        return null;
    }
}
