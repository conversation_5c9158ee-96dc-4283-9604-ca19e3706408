package com.eroses.external.society.model.enums;

/**
 * Error codes for the Grant Management module.
 */
public enum GrantErrorCode {

    // General errors (1000-1099)
    GENERAL_ERROR("GRN-1000", "<PERSON>lat tidak dijangka berlaku"),
    VALIDATION_ERROR("GRN-1001", "<PERSON><PERSON> pengesahan berlaku"),
    UNAUTHORIZED_ACCESS("GRN-1002", "<PERSON><PERSON><PERSON> tidak dibenarkan ke sumber"),
    RESOURCE_NOT_FOUND("GRN-1003", "Sumber tidak dijumpai"),

    // Grant Template errors (1100-1199)
    TEMPLATE_NOT_FOUND("GRN-1100", "Templat geran tidak dijumpai"),
    TEMPLATE_ALREADY_PUBLISHED("GRN-1101", "Templat geran telah diterbitkan"),
    TEMPLATE_FIELD_INVALID("GRN-1102", "Medan templat geran tidak sah"),
    TEMPLATE_SOCIETY_CATEGORY_INVALID("GRN-1103", "<PERSON><PERSON>i pertubuhan tidak sah untuk templat geran"),

    // Grant Application errors (1200-1299)
    APPLICATION_NOT_FOUND("GRN-1200", "Grant application not found"),
    APPLICATION_ALREADY_SUBMITTED("GRN-1201", "Grant application is already submitted"),
    APPLICATION_FIELD_MISSING("GRN-1202", "Required field is missing in grant application"),
    APPLICATION_INVALID_STATUS_TRANSITION("GRN-1203", "Invalid status transition for grant application"),
    APPLICATION_SOCIETY_NOT_ELIGIBLE("GRN-1204", "Society is not eligible for this grant"),

    // Grant Query errors (1300-1399)
    QUERY_NOT_FOUND("GRN-1300", "Grant query not found"),
    QUERY_ALREADY_RESPONDED("GRN-1301", "Grant query is already responded"),

    // Grant Report errors (1400-1499)
    REPORT_NOT_FOUND("GRN-1400", "Grant report not found"),
    REPORT_ALREADY_SUBMITTED("GRN-1401", "Grant report is already submitted"),
    REPORT_ATTACHMENT_INVALID("GRN-1402", "Invalid attachment for grant report"),
    REPORT_INVALID_STATUS_TRANSITION("GRN-1403", "Invalid status transition for grant report"),

    // File upload errors (1500-1599)
    FILE_UPLOAD_FAILED("GRN-1500", "Failed to upload file"),
    FILE_TYPE_NOT_SUPPORTED("GRN-1501", "File type not supported"),
    FILE_SIZE_EXCEEDED("GRN-1502", "File size exceeded the maximum limit");

    private final String code;
    private final String message;

    GrantErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
