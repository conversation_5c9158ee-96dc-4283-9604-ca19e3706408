package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;


@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class Feedback extends BaseEntity {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("type")
    private String type;

    @ApiModelProperty("title")
    private String title;

    @ApiModelProperty("Satisfaction")
    private String Satisfaction;

    @ApiModelProperty("details")
    private String details;

    @ApiModelProperty("location")
    private String location;

    @ApiModelProperty("parliament")
    private String parliament;

    @ApiModelProperty("name")
    private String name;

    @ApiModelProperty("identificationType")
    private String identificationType;

    @ApiModelProperty("identificationNo")
    private String identificationNo;

    @ApiModelProperty("gender")
    private String gender;

    @ApiModelProperty("email")
    private String email;

    @ApiModelProperty("contactNo")
    private String contactNo;

    @ApiModelProperty("homeContactNo")
    private String homeContactNo;

    @ApiModelProperty("stateCode")
    private String stateCode;

    @ApiModelProperty("districtCode")
    private String districtCode;

    @ApiModelProperty("city")
    private String city;

    @ApiModelProperty("postcode")
    private String postcode;

    @ApiModelProperty("status")
    private Integer status;

    @ApiModelProperty("PIC")
    private String PIC;

    @ApiModelProperty("note")
    private String note;

    @ApiModelProperty("techFeedback")
    private String techFeedback;

    @ApiModelProperty("durationOfComplaintsReceived")
    private Long durationOfComplaintsReceived;
}

