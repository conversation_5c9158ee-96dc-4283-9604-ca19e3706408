package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Example implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("name")
    private String name;

    @ApiModelProperty("age")
    private Integer age;

    @ApiModelProperty("email")
    private String email;

    // Lombok's @Data annotation will generate getter and setter methods for you

    public Long getId() {
        return id;
    }
}
