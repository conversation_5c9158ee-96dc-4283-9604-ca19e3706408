package com.eroses.external.society.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum DocumentTemplateEnum {
    //all documents template should be using this enums here, to be standardized/generalized
    SIJIL_KELULUSAN_PERTUBUHAN("SIJIL_KELULUSAN_PERTUBUHAN", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"),
    SURAT_IRINGAN_KELULUSAN("SURAT_IRINGAN_KELULUSAN", "Surat Iringan Kelulusan Tubuh Pertubuhan", "Surat iringan"),
    RESIT_PEMBAYARAN("RESIT_PEMBAYARAN", "Resit-resit Pem<PERSON>aran", "Resit"),
    SURAT_KELULUSAN_PINDAAN("SURAT_KELULUSAN_PINDAAN", "Surat Kelulusan Pindaan Undang-Undang", "Surat kelulusan pindaan"),
    SIJIL_SELEPAS_MIGRASI("S<PERSON><PERSON>IL_SELEPAS_MIGRASI", "<PERSON><PERSON><PERSON>ses Migrasi", "<PERSON><PERSON>l rasmi selepas migrasi"),
    SURAT_INSOLVENSI("SURAT_INSOLVENSI", "Surat Insolvensi Negeri", "Surat insolvensi"),
    SURAT_INSOLVENSI_CAWANGAN("SURAT_INSOLVENSI_CAWANGAN", "Surat Insolvensi Cawangan Negeri", "Surat insolvensi cawangan"),
    PERLEMBAGAAN("PERLEMBAGAAN_TERKINI", "Perlembagaan", "Document template for Perlembagaan"),
    SIJIL_KELULUSAN_PERMOHONAN_INDUK("SIJIL_KELULUSAN_PERMOHONAN_INDUK", "Sijil Kelulusan Permohonan Induk", "Document template for Sijil Kelulusan Permohonan Induk"),
    SENARAI_AHLI_JAWATAN_KUASA("SENARAI_AHLI_JAWATAN_KUASA", "Senarai Ahli Jawatan Kuasa", "Document template for Senarai Ahli Jawatan Kuasa"),
    PENYATA_TAHUNAN("PENYATA_TAHUNAN", "Penyata Tahunan", "Penyata tahunan Induk"),
    PENYATA_TAHUNAN_CAWANGAN("PENYATA_TAHUNAN_CAWANGAN", "Penyata Tahunan Cawangan", "Penyata tahunan cawangan"),
    SIJIL_ACARA("SIJIL_ACARA", "Sijil Penyertaan Acara", "Sijil Acara / Event Certificate"),
    SURAT_RAYUAN_LULUS("SURAT_RAYUAN_LULUS", "Surat Kelulusan Rayuan", "Surat kelulusan rayuan"),
    SURAT_RAYUAN_TOLAK("SURAT_RAYUAN_TOLAK", "Surat Penolakan Rayuan", "Surat penolakan rayuan"),
    SURAT_RAYUAN_LULUS_BERSYARAT("SURAT_RAYUAN_LULUS_BERSYARAT", "Surat Kelulusan Bersyarat Rayuan", "Surat kelulusan bersyarat rayuan"),
    SECTION_13_1_A_B_C("SECTION_13_1_A_B_C", "13(1)(a)(b)(c)", "13(1)(a), (b) & (c)"),
    SECTION_13_1_A_B_C_CAWANGAN("SECTION_13_1_A_B_C_CAWANGAN", "13(1)(a)(b)(c)", "13(1)(a), (b) & (c)"),
    SECTION_13_1_D("SECTION_13_1_D", "13(1)(d)", "13(1)(d)"),
    CETAK_AJK("CETAK_AJK", "Cetak AJK", "Cetak AJK"),
    CETAK_UMUM("CETAK_UMUM", "Cetak Umum", "Cetak Umum"),
    PERLEMBAGAAN_AKTIF("PERLEMBAGAAN_AKTIF", "Perlembagaan Aktif", "Perlembagaan Aktif"),
    DRAF_PERLEMBAGAAN_INDUK_NGO("DRAF_PERLEMBAGAAN_INDUK_NGO", "Draf Perlembagaan Induk NGO", "Draf Perlembagaan Induk NGO"),
    DRAF_PERLEMBAGAAN_BERCAWANGAN_SEMUA_NGO("DRAF_PERLEMBAGAAN_BERCAWANGAN_SEMUA_NGO", "Draf Perlembagaan Bercawangan Semua NGO", "Draf Perlembagaan Bercawangan Semua NGO"),
    DRAF_PERLEMBAGAAN_INDUK_KEAGAMAAN("DRAF_PERLEMBAGAAN_INDUK_KEAGAMAAN", "Draf Perlembagaan Induk Keagamaan", "Draf Perlembagaan Induk Keagamaan"),
    DRAF_PERLEMBAGAAN_BERCAWANGAN_KEAGAMAAN("DRAF_PERLEMBAGAAN_BERCAWANGAN_KEAGAMAAN", "Draf Perlembagaan Bercawangan Keagamaan", "Draf Perlembagaan Bercawangan Keagamaan"),
    DRAF_PERLEMBAGAAN_FAEDAH_BERSAMA("DRAF_PERLEMBAGAAN_FAEDAH_BERSAMA", "Draf Perlembagaan Faedah Bersama", "Draf Perlembagaan Faedah Bersama"),
    SURAT_PEMBUBARAN("SURAT_PEMBUBARAN", "Surat Pembubaran", "Surat pembubaran"),
    SURAT_KEBENARAN_CAWANGAN("SURAT_KEBENARAN_CAWANGAN", "Surat Kebenaran Cawangan", "Surat kebenaran cawangan"),
    SURAT_PEMBUBARAN_CAWANGAN("SURAT_PEMBUBARAN_CAWANGAN", "Surat Pembubaran Cawangan", "Surat pembubaran cawangan"),
    SLIP_PENGESAHAN_PEMBAHARUAN_SETIAUSAHA_CAWANGAN("SLIP_PENGESAHAN_PEMBAHARUAN_SETIAUSAHA_CAWANGAN", "Slip Pengesahan Pembaharuan Setiausaha Cawangan", "Slip pengesahan pembaharuan setiausaha cawangan"),
    TEMPLAT_RUJUKAN_LAPORAN_AKTIVITI("TEMPLAT_RUJUKAN_LAPORAN_AKTIVITI", "Templat Rujukan Laporan Aktiviti", "Templat rujukan untuk laporan aktiviti")
    ;

    private final String code;
    private final String type;
    private final String description;

    public static DocumentTemplateEnum getDocumentTemplate(String code) {
        for (DocumentTemplateEnum documentTemplateEnum : DocumentTemplateEnum.values()) {
            if (Objects.equals(documentTemplateEnum.code, code)) {
                return documentTemplateEnum;
            }
        }
        return null;
    }
}
