package com.eroses.external.society.model.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum BranchAmendmentTypeEnum {
    //Code based on old db
    PINDAAN_NAMA_DAN_ALAMAT_CAWANGAN("pinda_nama_alamat", "Pindaan <PERSON>awanga<PERSON>"),
    PINDAAN_NAMA_CAWANGAN("pinda_nama", "Pindaan Nama Cawangan"),
    PINDAAN_ALAMAT_CAWANGAN("pinda_alamat", "Pindaan Alamat Cawangan");

    private final String code;
    private final String description;

    BranchAmendmentTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static BranchAmendmentTypeEnum getTypeByCode(String code) {
        for (BranchAmendmentTypeEnum branchAmendmentTypeEnum : BranchAmendmentTypeEnum.values()) {
            if (Objects.equals(branchAmendmentTypeEnum.code, code)) {
                return branchAmendmentTypeEnum;
            }
        }
        return null;
    }
}
