package com.eroses.external.society.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.Entity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Meeting extends BaseEntity {
    @ApiModelProperty("amendmentId")
    private Long amendmentId;
    @ApiModelProperty("societyId")
    private Long societyId;
    @ApiModelProperty("societyNo")
    private String societyNo;
    @ApiModelProperty("branchId")
    private Long branchId;
    @ApiModelProperty("branchNo")
    private String branchNo;
    @ApiModelProperty("statementId")
    private Long statementId;
    @ApiModelProperty("meetingType")
    private String meetingType;
    @ApiModelProperty("meetingPurpose")
    private String meetingPurpose;
    @ApiModelProperty("meetingPlace")
    private String meetingPlace;
    @ApiModelProperty("meetingMethod")
    private String meetingMethod;
    @ApiModelProperty("platformType")
    private String platformType;
    @ApiModelProperty("meetingDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate meetingDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    @ApiModelProperty("meetingTime")
    private LocalTime meetingTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    private LocalTime meetingTimeTo;
    private Long meetingTimeDurationMinutes;
    @ApiModelProperty("GISInformation")
    @JsonProperty("GISInformation")
    private String GISInformation;
    @ApiModelProperty("meetingAddress")
    private String meetingAddress;
    @ApiModelProperty("state")
    private String state;
    @ApiModelProperty("district")
    private String district;
    @ApiModelProperty("city")
    private String city;
    @ApiModelProperty("postcode")
    private String postcode;
    @ApiModelProperty("totalAttendees")
    private Integer totalAttendees;
    @ApiModelProperty("openingRemarks")
    private String openingRemarks;
    @ApiModelProperty("meetingContent")
    private String meetingContent;
    @ApiModelProperty("mattersDiscussed")
    private String mattersDiscussed;
    @ApiModelProperty("otherMatters")
    private String otherMatters;
    @ApiModelProperty("closing")
    private String closing;
    @ApiModelProperty("providedBy")
    private String providedBy;
    @ApiModelProperty("confirmBy")
    private String confirmBy;
    @ApiModelProperty("meetingMinute")
    private String meetingMinute;
    @ApiModelProperty("status")
    private String status;
    //TODO: move to response class
    @ApiModelProperty("meetingMemberAttendances")
    private List<MeetingMemberAttendance> meetingMemberAttendances;
}
