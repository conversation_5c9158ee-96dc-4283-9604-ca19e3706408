package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class RoQuery extends BaseEntity {

    @ApiModelProperty("type")
    private String type;

    @ApiModelProperty("societyNo")
    private String societyNo;

    @ApiModelProperty("societyId")
    private Long societyId;

    @ApiModelProperty("branchId")
    private Long branchId;

    @ApiModelProperty("branchNo")
    private String branchNo;

    @ApiModelProperty("appealId")
    private Long appealId;

    @ApiModelProperty("queryReceiver")
    private String queryReceiver;

    @ApiModelProperty("societyNonCitizenCommitteeId")
    private Long societyNonCitizenCommitteeId;

    @ApiModelProperty("note")
    private String note;

    @ApiModelProperty("finished")
    private Boolean finished = false;

    @ApiModelProperty("amendmentId")
    private Long amendmentId;

    @ApiModelProperty("principalSecretaryId")
    private Long principalSecretaryId;

    @ApiModelProperty("liquidationId")
    private Long liquidationId;
}
