package com.eroses.external.society.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class AuditTrail extends BaseEntity implements Serializable {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("module")
    private String module;
    @ApiModelProperty("actionType")
    private String actionType;
    @ApiModelProperty("userGroup")
    private Integer userGroup;
    private String userName;
    @ApiModelProperty("identificationNo")
    private String identificationNo;
    @ApiModelProperty("societyId")
    private Long societyId;
    @ApiModelProperty("societyNo")
    private String societyNo;
    @ApiModelProperty("branchId")
    private Long branchId;
    @ApiModelProperty("branchNo")
    private String branchNo;
    @ApiModelProperty("internalUserId")
    private Long internalUserId;
    @ApiModelProperty("oldData")
    private String oldData;
    @ApiModelProperty("newData")
    private String newData;
    @ApiModelProperty("createdBy")
    private Long createdBy;
    @ApiModelProperty("createdDate")
    @JsonFormat(pattern="yyyy-MM-dd@HH:mm:ss")
    private LocalDateTime createdDate;
}
