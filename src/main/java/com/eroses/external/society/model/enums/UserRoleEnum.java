package com.eroses.external.society.model.enums;

import lombok.Getter;

@Getter
public enum UserRoleEnum {
    // Enum constants
    SUPER_ADMIN(4, "SUPER ADMIN", "Highest level account"),
    PENTADBIR_SISTEM(6, "PENTADBIR SISTEM", "Access to administration function"),
    PENDAFTAR(8, "PENDAFTAR", "Exclusive to Ketua Pengarah"),
    TIMBALAN_PENDAFTAR(9, "TIMBALAN PENDAFTAR", "Exclusive to <PERSON>bal<PERSON> Pen<PERSON>"),
    PENOLONG_PEGAWAI_PENDAFTAR(10, "PENOLONG PEGAWAI PENDAFTAR", "Assistant for <PERSON><PERSON><PERSON> Negeri. To approve or reject all permohonan after being assigned by their pengarah"),
    PEGAWAI_PENDAFTARAN_PERTUBUHAN(12, "PEGAWAI PENDAFTARAN PERTUBUHAN (RO)", "Assistant for <PERSON><PERSON><PERSON>ege<PERSON>. To approve or reject all permohonan after being assigned by their pengarah"),
    ADMIN_KEWANGAN(14, "ADMIN KEWANGAN", "Pegawai Helpdesk (LEGACY)"),
    PEGAWAI_HELPDESK(15, "PEGAWAI HELPDESK", "JPPM first level support. Access to check all application status and notes"),
    PT_KEWANGAN(16, "PT KEWANGAN", "Same role as Admin Kewangan. Pegawai Helpdesk (LEGACY)"),
    PENOLONG_PENDAFTAR_PERTUBUHAN(17, "PENOLONG PENDAFTAR PERTUBUHAN", "Exclusive to Pengarah Negeri. Limited to State Application."),
    PP_KDN(19, "PP KDN", "Exclusive for KDN Officer to handle Appeal cases. View - all approvals, Update - Appeal only"),
    PP_PEMATUHAN(21, "PP PEMATUHAN", "Pematuhan module"),
    ADMIN_PEMATUHAN(23, "ADMIN PEMATUHAN", "Modul Pematuhan. Given to officer from PDRM to check society information. Check all statuses but cannot access Approval"),
    PENGARAH_BAHAGIAN_PENGURUSAN_PERTUBUHAN(24, "PENGARAH BAHAGIAN PENGURUSAN PERTUBUHAN (BPP)", "Exclusive to Pengarah BPP, to approve or reject all society under category Politik"),
    PP_PEMATUHAN_NEGERI(26, "PP PEMATUHAN NEGERI", "Modul Pematuhan"),
    PPP_PEMATUHAN_NEGERI(27, "PPP PEMATUHAN NEGERI", "Modul Pematuhan"),
    PENOLONG_PENDAFTAR_SEMENTARA(28, "PENOLONG PENDAFTAR PERTUBUHAN (SEMENTARA)", "Temporarily assuming role as Pengarah (state level only)"),
    PENOLONG_MANTAN_PENDAFTAR(30, "PENOLONG MANTAN PENDAFTAR PERTUBUHAN", "Not in use"),
    ADMIN_PEGAWAI_PERHUBUNGAN_AWAM(49,"ADMIN PEGAWAI PERHUBUNGAN AWAM","handle jppm's social media and marketing"),
    ADMIN_GERAN(53,"ADMIN GERAN", "responsible for managing grant");
    // Fields
    private final long id;
    private final String role;
    private final String description;

    // Constructor
    UserRoleEnum(long id, String role, String description) {
        this.id = id;
        this.role = role;
        this.description = description;
    }
}
