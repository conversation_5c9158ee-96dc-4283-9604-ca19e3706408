package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Statement extends BaseEntity {

    private Long id;

    private Long statementId;

    private Long societyId;

    private String societyNo;

    private Long branchId;

    private String branchNo;

    private Integer statementYear;

    private LocalDate financialYearStart;

    private LocalDate financialYearEnd;

    private LocalDate tarikhAkuan;

    private String akuanPapar;

    private LocalDate tarikhAkuanPapar;

    private LocalDate submissionDate;

    private LocalDate mainSubmissionDate;

    private String applicationStatusCode;

    private String ptStat;

    private Boolean akuanSetuju;

    private Boolean akuanSetujuInduk;

    private String pemCaw;

    private Long meetingId;

    private Boolean akuanLaporanAktiviti;

    private LocalDate ajkAppointedDate;

    private LocalDate juruauditAppointedDate;
}
