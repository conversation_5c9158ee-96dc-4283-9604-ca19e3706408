package com.eroses.external.society.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import software.amazon.awssdk.services.lambda.endpoints.internal.Value.Int;

import java.time.LocalDateTime;
import java.util.List;

import jakarta.persistence.criteria.CriteriaBuilder.In;

@Data
@SuperBuilder
@NoArgsConstructor
public class TrainingCourse extends BaseEntity {
    private String title;
    private String description;
    private String difficultyLevel; // Enum: '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'
    private Integer status; // 0: Draft, 1: Published, 2: Hidden
    private Integer passingCriteria; // e.g., 70 for 70%
    private Integer duration;
    private String explanation;
    private String objective;
    private String poster;
    private List<TrainingMaterial> materials;
}
