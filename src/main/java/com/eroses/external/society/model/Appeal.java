package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Appeal extends BaseEntity implements Serializable {
    private Long id;
    private String appealNo;
    private Long societyId;
    private String societyNo;
    private Long branchId;
    private String branchNo;
    private Long amendmentId;
    private Long societyNonCitizenCommitteeId;
    private String identificationNo;
    private Long idSebab;
    private String sebabLain;
    private String pdfFile;
    private String confession;
    private LocalDate confessionDate;
    private Long paymentRecordId;
    private String paymentMethod;
    private LocalDate paymentDate;
    private String receiptNo;
    private String bankName;
    private String bankReferenceNo;
    private String receiptStatus;
    private String letterReferenceNo;
    private String ro;
    private Long applicationStatusCode;
    private String idEpayment;
    private String pemCaw;
    private String appealDate;
    private LocalDate date132;
    private LocalDate date142;
    private LocalDate date145;
    private LocalDate reconcileDate;
    private String sentEpp;
    private Long emailId;
    private LocalDate approvedDate;
    private Integer isQueried;
    private LocalDateTime registerDate;
}
