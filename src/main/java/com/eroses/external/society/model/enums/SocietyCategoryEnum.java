package com.eroses.external.society.model.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum SocietyCategoryEnum {
    KEBAJIKAN(2, "Kebajikan"),
    SOSIAL(3, "Sosial"),
    REKREASI(4, "<PERSON><PERSON><PERSON><PERSON>"),
    KEBUDAYAAN_DAN_KESENIAN(5, "Kebudayaan Dan Kesenian"),
    PERDAGANGAN(6, "Perdagangan"),
    IKHTISAS(7, "Ikhtisas"),
    HAK_ASASI(8, "Hak Asasi"),
    KESELAMATAN(9, "Keselamatan"),
    FAEDAH_BERSAMA(10, "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)"),
    KEAGAMAAN(11, "Keagamaan"),
    POLITIK(12, "Politik");

    private final Integer id;
    private final String description;

    SocietyCategoryEnum(Integer id, String description) {
        this.id = id;
        this.description = description;
    }

    public static SocietyCategoryEnum getCategoryById(Integer id) {
        for (SocietyCategoryEnum societyCategoryEnum : SocietyCategoryEnum.values()) {
            if (Objects.equals(societyCategoryEnum.id, id)) {
                return societyCategoryEnum;
            }
        }
        return null;
    }

    public static String getCategoryDescriptionById(Integer id) {
        for (SocietyCategoryEnum societyCategoryEnum : SocietyCategoryEnum.values()) {
            if (Objects.equals(societyCategoryEnum.id, id)) {
                return societyCategoryEnum.getDescription();
            }
        }
        return null;
    }
}
