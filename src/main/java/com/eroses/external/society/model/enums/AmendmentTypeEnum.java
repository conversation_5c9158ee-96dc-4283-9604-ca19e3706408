package com.eroses.external.society.model.enums;

import lombok.Getter;

@Getter
public enum AmendmentTypeEnum {
    PINDAAN_PERLEMBAGAAN(1L, "Pindaan Perlembagaan"),
    PERLEMBAGAAN_ASAL_TANPA_PINDAAN(2L, "Perlembagaan Asal Tanpa Pindaan"),
    PERLEMBAGAAN_ASAL_DENGAN_PINDAAN(3L, "Perlembagaan Asal Dengan Pindaan"),
    MENGIKUT_TEMPLAT(4L, "Mengikut Templat"),
    BEBAS(5L, "Bebas");

    private final Long typeCode;
    private final String typeMessage;

    AmendmentTypeEnum(Long typeCode, String typeMessage) {
        this.typeCode = typeCode;
        this.typeMessage = typeMessage;
    }

    public static AmendmentTypeEnum getTypeCode(Long typeCode) {
        for (AmendmentTypeEnum amendmentTypeEnum : AmendmentTypeEnum.values()) {
            if (amendmentTypeEnum.typeCode == typeCode) {
                return amendmentTypeEnum;
            }
        }
        return null;
    }
}
