package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class RoApproval extends BaseEntity {

    @ApiModelProperty("type")
    private String type;

    @ApiModelProperty("applicationNo")
    private String applicationNo;

    @ApiModelProperty("societyNo")
    private String societyNo;

    @ApiModelProperty("societyId")
    private Long societyId;

    @ApiModelProperty("branchId")
    private Long branchId;

    @ApiModelProperty("branchNo")
    private String branchNo;

    @ApiModelProperty("societyNonCitizenCommitteeId")
    private Long societyNonCitizenCommitteeId;

    @ApiModelProperty("extensionTimeId")
    private Long extensionTimeId;

    @ApiModelProperty("branchAmendmentId")
    private Long branchAmendmentId;

    @ApiModelProperty("decision")
    private Integer decision;

    @ApiModelProperty("akta1")
    private String akta1;

    @ApiModelProperty("akta2")
    private String akta2;

    @ApiModelProperty("extensionDays")
    private Integer extensionDays;

    @ApiModelProperty("rejectReason")
    private String rejectReason;

    @ApiModelProperty("note")
    private String note;

    @ApiModelProperty("approvedBy")
    private Long approvedBy;

    @ApiModelProperty("decisionDate")
    private LocalDate decisionDate;

    @ApiModelProperty("amendmentId")
    private Long amendmentId;

    @ApiModelProperty("appealId")
    private Long appealId;

    @ApiModelProperty("principalSecretaryId")
    private Long principalSecretaryId;

    @ApiModelProperty("liquidationId")
    private Long liquidationId;

    @ApiModelProperty("propertyOfficerApplicationId")
    private Long propertyOfficerApplicationId;

    @ApiModelProperty("publicOfficerId")
    private Long publicOfficerId;
}
