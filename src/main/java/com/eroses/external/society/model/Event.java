package com.eroses.external.society.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;




@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Event extends BaseEntity implements Serializable {
    @ApiModelProperty("id")
    public Long id;

    @ApiModelProperty("visibility")
    public String visibility;

    @ApiModelProperty("event_name")
    public String eventName;

    @ApiModelProperty("event_admin_id")
    public Long eventAdminId;

    @ApiModelProperty("has_max")
    public Boolean hasMax;

    @ApiModelProperty("published")
    public Boolean published;

    @ApiModelProperty("status")
    public Integer status;

    @ApiModelProperty("reg_start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    public LocalDate regStartDate;

    @ApiModelProperty("reg_end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    public LocalDate regEndDate;

    @ApiModelProperty("max_participants")
    public Long maxParticipants;

    @ApiModelProperty("description")
    public String description;

    @ApiModelProperty("address_2")
    public String address2;

    //for target society
    @ApiModelProperty("state")
    public String state;

    @ApiModelProperty("postcode")
    public String postcode;

    @ApiModelProperty("event_start_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    public LocalDate eventStartDate;

    @ApiModelProperty("event_end_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    public LocalDate eventEndDate;

    @ApiModelProperty("event_no")
    public String eventNo;

    @ApiModelProperty("organiser_id")
    public Long organiserId;

    @ApiModelProperty("start_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    public LocalTime startTime;

    @ApiModelProperty("end_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    public LocalTime endTime;

    @ApiModelProperty("venue")
    public String venue;

    @ApiModelProperty("collaborator_name")
    public String collaboratorName;

    @ApiModelProperty("map_url")
    public String mapUrl;

    @ApiModelProperty("feedback_name")
    public String feedbackName;

    @ApiModelProperty("organisation_level")
    public String organisationLevel ;

    @ApiModelProperty("city")
    public String city ;

    @ApiModelProperty("district")
    public String district;

    @ApiModelProperty("position")
    public String position;

    @ApiModelProperty("organisationCategory")
    public String organisationCategory;

    @ApiModelProperty("banner_url")
    public String bannerUrl;

    @ApiModelProperty("pic_contact_no")
    public String picContactNo;

    //for venue address
    @ApiModelProperty("address_1")
    public String address1;
    @ApiModelProperty("state_address")
    public Long stateAddress;
    @ApiModelProperty("district_address")
    public Long districtAddress;
    @ApiModelProperty("city_address")
    public String cityAddress;
    @ApiModelProperty("postcode_address")
    public String postcodeAddress;

    public List<EventOrganiser> eventOrganisers;

    public List<PrivateEventSociety> privateEventSocieties;

   public Event(Event other) {
       this.id = other.id;
       this.visibility = other.visibility;
       this.eventName = other.eventName;
       this.eventAdminId = other.eventAdminId;
       this.hasMax = other.hasMax;
       this.published = other.published;
       this.status = other.status;
       this.regStartDate = other.regStartDate;
       this.regEndDate = other.regEndDate;
       this.maxParticipants = other.maxParticipants;
       this.description = other.description;
       this.address1 = other.address1;
       this.address2 = other.address2;
       this.state = other.state;
       this.postcode = other.postcode;
       this.eventStartDate = other.eventStartDate;
       this.eventEndDate = other.eventEndDate;
       this.eventNo = other.eventNo;
       this.organiserId = other.organiserId;
       this.startTime = other.startTime;
       this.endTime = other.endTime;
       this.venue = other.venue;
       this.collaboratorName = other.collaboratorName;
       this.mapUrl = other.mapUrl;
       this.feedbackName = other.feedbackName;
       this.organisationLevel = other.organisationLevel;
       this.city = other.city;
       this.district = other.district;
       this.position = other.position;
       this.organisationCategory = other.organisationCategory;
       this.bannerUrl = other.bannerUrl;
       this.eventOrganisers = other.eventOrganisers != null ? new java.util.ArrayList<>(other.eventOrganisers) : null;
       this.privateEventSocieties = other.privateEventSocieties != null ? new java.util.ArrayList<>(other.privateEventSocieties) : null;
       this.picContactNo = other.picContactNo;
       this.stateAddress = other.stateAddress;
       this.districtAddress = other.districtAddress;
       this.cityAddress = other.cityAddress;
       this.postcodeAddress = other.postcodeAddress;
   }
}
