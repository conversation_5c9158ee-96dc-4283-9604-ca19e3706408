package com.eroses.external.society.model.enums;

import lombok.Getter;

@Getter
public enum CommitteeTaskStatus {
    ACTIVE(1),
    INACTIVE(2),
    DELETED(-1);

    private final int code;

    CommitteeTaskStatus(int code) {
        this.code = code;
    }

    public static CommitteeTaskStatus getStatus(int code) {
        for (CommitteeTaskStatus status : CommitteeTaskStatus.values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }
}
