package com.eroses.external.society.model.enums;

import lombok.Getter;

//for status columns, cannot change due to external organization is using this status codes

@Getter
public enum StatusCode {
    BATAL("003"),
    TOLAK("002"),
    AKTIF("001"),
    TARIK_BALIK("004"),
    BUBAR("005"),
    BUBAR_SEMENTARA("006"),
    BARU("007"),
    TIDAK_AKTIF("008"),
    DIBENARKAN("009"),
    DILUPUTKAN("010"),
    DIGUGURKAN("998"),
    TIADA_MAKLUMAT("999"),
    TEST("678"),
    PINDA_NAMA("011"),
    PERTELINGKAHAN_SIASATAN("101"),
    BATAL_KHAS("102"),
    TIDAK_AKTIF_PINDAH("103"),
    DALAM_PERHATIAN("104"),
    TIDAK_AKTIF1("306"),
    PADAM("-001");

    private final String code;

    StatusCode(String code) {
        this.code = code;
    }

    public int getCodeAsInt() {
        return Integer.parseInt(code);
    }

    public static StatusCode getSocietyStatus(String code) {
        for (StatusCode statusCode : StatusCode.values()) {
            if (statusCode.code.equals(code)) {
                return statusCode;
            }
        }
        return null;
    }

    public static StatusCode getSocietyStatus(int code) {
        String codeStr = String.format("%03d", code); // Convert int to zero-padded string
        for (StatusCode statusCode : StatusCode.values()) {
            if (statusCode.code.equals(codeStr)) {
                return statusCode;
            }
        }
        return null;
    }
}
