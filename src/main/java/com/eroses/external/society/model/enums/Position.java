package com.eroses.external.society.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum Position {
    PENGERUSI(1, "<PERSON>ger<PERSON><PERSON>", 1, 1,true),
    PRESIDEN(17, "Presiden", 1, 1,true),
    PENGARAH(18, "Pengara<PERSON>", 1, 1,true),
    YANG_DIPERTUA(41, "Yang Dipertua", 1, 1,true),
    PENGERUSI_CAWANGAN(31, "Pengerusi Cawangan", 1, 1,true),
    PRESIDEN_CAWANGAN(32, "Presiden Cawangan", 1, 1,true),
    PENGARAH_CAWANGAN(33, "Pengarah Cawangan", 1, 1,true),
    YANG_DIPERTUA_CAWANGAN(44, "Yang Dipertua Cawangan", 1, 1,true),

    TIMBALAN_PENGERUSI(2, "<PERSON><PERSON><PERSON>", 2, 1,true),
    TIMBALAN_PRESIDEN(19, "<PERSON><PERSON><PERSON>siden", 2, 1,true),
    TIMBALAN_PENGARAH(20, "<PERSON><PERSON><PERSON>garah", 2, 1,true),
    TIMBALAN_YANG_DIPERTUA(42, "Timbalan Yang Dipertua", 2, 1,true),
    TIMBALAN_PENGERUSI_CAWANGAN(53, "<PERSON>balan Pengerusi Cawangan", 2, 1,true),
    TIMBALAN_PRESIDEN_CA<PERSON>NGAN(54, "<PERSON>balan Presiden <PERSON>awangan", 2, 1,true),
    TIMBALAN_YANG_DIPERTUA_CAWANGAN(55, "Timbalan Yang Dipertua Cawangan", 2, 1,true),

    NAIB_PENGERUSI(8, "Naib Pengerusi", 3, 1,true),
    NAIB_PRESIDEN(22, "Naib Presiden", 3, 1,true),
    NAIB_PENGARAH(23, "Naib Pengarah", 3, 1,true),
    NAIB_YANG_DIPERTUA(43, "Naib Yang Dipertua", 3, 1,true),
    NAIB_PENGERUSI_CAWANGAN(34, "Naib Pengerusi Cawangan", 3, 1,true),
    NAIB_PRESIDEN_CAWANGAN(35, "Naib Presiden Cawangan", 3, 1,true),
    NAIB_PENGARAH_CAWANGAN(36, "Naib Pengarah Cawangan", 3, 1,true),
    NAIB_YANG_DIPERTUA_CAWANGAN(45, "Naib Yang Dipertua Cawangan", 3, 1,true),

    SETIAUSAHA(3, "Setiausaha", 4, 1, true),
    SETIAUSAHA_AGUNG(21, "Setiausaha Agung", 4, 1, true),
    SETIAUSAHA_CAWANGAN(37, "Setiausaha Cawangan", 4, 1, true),

    PENOLONG_SETIAUSAHA(4, "Penolong Setiausaha", 5, 1, true),
    PENOLONG_SETIAUSAHA_AGUNG(24, "Penolong Setiausaha Agung", 5, 1, true),
    PENOLONG_SETIAUSAHA_CAWANGAN(38, "Penolong Setiausaha Cawangan", 5, 1, true),

    BENDAHARI(5, "Bendahari", 6, 1,true),
    BENDAHARI_AGUNG(25, "Bendahari Agung", 6, 1,true),
    KETUA_BENDAHARI(26, "Ketua Bendahari", 6, 1,true),
    BENDAHARI_KEHORMAT(27, "Bendahari Kehormat", 6, 1,true),
    BENDAHARI_CAWANGAN(39, "Bendahari Cawangan", 6, 1,true),
    BENDAHARI_KEHORMAT_CAWANGAN(46, "Bendahari Kehormat Cawangan", 6, 1,true),

    PENOLONG_BENDAHARI(9, "Penolong Bendahari", 7, 1,true),
    PENOLONG_BENDAHARI_AGUNG(28, "Penolong Bendahari Agung", 7, 1,true),
    PENOLONG_KETUA_BENDAHARI(29, "Penolong Ketua Bendahari", 7, 1,true),
    PENOLONG_BENDAHARI_KEHORMAT(30, "Penolong Bendahari Kehormat", 7, 1,true),
    PENOLONG_BENDAHARI_CAWANGAN(40, "Penolong Bendahari Cawangan", 7, 1,true),
    PENOLONG_BENDAHARI_KEHORMAT_CAWANGAN(47, "Penolong Bendahari Kehormat Cawangan", 7, 1,true),

    AHLI_JAWATANKUASA_BIASA(6, "Ahli Jawatankuasa Biasa", 8, 1,false),
    AHLI_JAWATANKUASA_BIASA_CAWANGAN(48, "Ahli Jawatankuasa Biasa Cawangan", 8, 1,false),
    PENGGUNA_BIASA(7, "Pengguna Biasa", 9, 0,false),
    AHLI_BIASA(10, "Ahli Biasa", 10, 0,false),
    PERWAKILAN_NEGERI(11, "Perwakilan Negeri", 11, 1,false),
    LAIN_LAIN(12, "Lain Lain", 12, 1,false),

    BIRO_EKONOMI(13, "Biro Ekonomi", 13, 1,true),
    BIRO_KEBAJIKAN(14, "Biro Kebajikan", 14, 1,true),
    BIRO_SUKAN_DAN_SOSIAL(15, "Biro Sukan dan Sosial", 15, 1,true),
    BIRO_AGAMA(16, "Biro Agama", 16, 1,true),

    AHLI_BERSEKUTU(49, "Ahli Bersekutu", 10, 0,false),
    AHLI_KEHORMAT(50, "Ahli Kehormat", 10, 0,false),
    SEUMUR_HIDUP(51, "Seumur Hidup", 10, 0,false),
    AHLI_REMAJA(52, "Ahli Remaja", 10, 0,false);

    private final int code;
    private final String name;
    private final int rank;
    private final int isCommittee;
    private final boolean isHighRankCommittee;

    Position(int code, String name,int rank, int isCommittee, boolean isHighRankCommittee) {
        this.code = code;
        this.rank = rank;
        this.name = name;
        this.isCommittee = isCommittee;
        this.isHighRankCommittee = isHighRankCommittee;
    }

    public static Position getRole(int code) {
        for (Position position : Position.values()) {
            if (position.code == code) {
                return position;
            }
        }
        return null;
    }

    private static final Map<String, Position> NAME_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(Position::getName, Function.identity()));

    public static Position getPositionByName(String name) {
        return NAME_MAP.get(name);
    }

    private static final Map<Integer, Position> CODE_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(Position::getCode, Function.identity()));

    public static Position sortByRank(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }

    public static List<String> highRankPositionCodes() {
        return Arrays.stream(values()).filter(p -> p.isHighRankCommittee).map(p -> String.valueOf(p.getCode())).toList();
    }

    public static List<String> nonHighRankPositionCodes(){
        return Arrays.stream(values()).filter(p -> !p.isHighRankCommittee).map(p -> String.valueOf(p.getCode())).toList();
    }

    public static List<String> committeePositionCodes() {
        return Arrays.stream(values()).filter(p -> p.isCommittee == 1).map(p -> String.valueOf(p.getCode())).toList();
    }

    public static List<String> noncommitteePositionCodes() {
        return Arrays.stream(values()).filter(p -> p.isCommittee == 0).map(p -> String.valueOf(p.getCode())).toList();
    }

    public static boolean checkEligibility(Integer designationCode) {
        return Arrays.asList(1, 3, 17, 18, 21, 31, 32, 33, 37).contains(designationCode);
    }

    public static List<Integer> pengerusiPositionCodes() {
        return Arrays.asList(1, 17, 18, 31, 32, 33);
    }

    /**
     * Retrieves all secretary positions.
     * Secretary positions have role code 4.
     *
     * @return List of Position enums that represent secretary roles
     */
    public static List<Position> getAllSecretaryPositions() {
        return Arrays.stream(Position.values())
                .filter(position -> position.getRank() == 4)
                .collect(Collectors.toList());
    }

    /**
     * Retrieves all president positions.
     * President positions have role code 1.
     *
     * @return List of Position enums that represent president roles
     */
    public static List<Position> getAllPresidentPositions() {
        return Arrays.stream(Position.values())
                .filter(position -> position.getRank() == 1)
                .collect(Collectors.toList());
    }

    /**
     * Retrieves all treasurer positions.
     * President treasurer have role code 6.
     *
     * @return List of Position enums that represent treasurer roles
     */
    public static List<Position> getAllTreasurerPositions() {
        return Arrays.stream(Position.values())
                .filter(position -> position.getRank() == 6)
                .collect(Collectors.toList());
    }

    /**
     * Retrieves all assistant secretary positions.
     * Secretary positions have role code 5.
     *
     * @return List of Position enums that represent secretary roles
     */
    public static List<Position> getAllAssistantSecretaryPositions() {
        return Arrays.stream(Position.values())
                .filter(position -> position.getRank() == 5)
                .collect(Collectors.toList());
    }
}
