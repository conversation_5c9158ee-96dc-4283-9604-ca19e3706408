package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class StatementContribution extends BaseEntity{
    private Long statementId;
    private Long societyId;
    private String societyNo;
    private Long branchId;
    private String branchNo;
    private String contributionCode;
    private String contribution;
    private String countryOrigin;
    private BigDecimal value;
    private String applicationStatusCode;
}
