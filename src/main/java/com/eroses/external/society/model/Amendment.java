package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.FetchType;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Amendment extends Model implements Serializable {

    @ApiModelProperty
    private Long id;

    @ApiModelProperty
    private Long oldAmendmentId;

    @ManyToOne(fetch = FetchType.LAZY)
    private Society society;

    @ApiModelProperty
    private String amendedConstitutionRemarks;

    @ApiModelProperty
    private Long societyId;

    @ApiModelProperty
    private String societyNo;

    @ApiModelProperty
    private String clauseType;

    @ApiModelProperty
    private String meetingType;

    @ApiModelProperty
    private LocalDate meetingDate;

    @ApiModelProperty
    private Long meetingId;

    @ApiModelProperty
    private Byte[] pdfDocument;

    @ApiModelProperty
    private String amendmentClause;

    @ApiModelProperty
    private String goal;

    @ApiModelProperty
    private String agreement;

    @ApiModelProperty
    private LocalDate agreementDate;

    @ApiModelProperty
    private String paymentMethod;

    @ApiModelProperty
    private Long paymentId;

    @ApiModelProperty
    private String receiptNo;

    @ApiModelProperty
    private Integer receiptStatus;

    @ApiModelProperty
    private LocalDate paymentDate;

    @ApiModelProperty
    private LocalDate submissionDate;

    @ApiModelProperty
    private String referenceNo;

    @ApiModelProperty
    private String bankName;

    @ApiModelProperty
    private String bankReferenceNo;

    @ApiModelProperty
    private String ro;

    @ApiModelProperty
    private Integer appealActive;

    @ApiModelProperty
    private LocalDate transferDate;

    @ApiModelProperty
    private String noteRo;

    @ApiModelProperty
    private String kuiri;

    @ApiModelProperty
    private String status;

    @ApiModelProperty
    private String applicationStatusCode;

    @ApiModelProperty
    private String societyLevel;

    @ApiModelProperty
    private String hasBranch;

    @ApiModelProperty
    private String categoryCodeJppm;

    @ApiModelProperty
    private String subCategoryCode;

    @ApiModelProperty
    private String constitutionType;

    @ApiModelProperty
    private String amendmentType;

    @ApiModelProperty
    private LocalDate approvedDate;

    @ApiModelProperty
    private Integer isQueried;

    @ApiModelProperty
    private Integer appealStatus;
}
