package com.eroses.external.society.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BranchAmendment extends BaseEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty("id")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty("societyId")
    @Column(name = "society_id", nullable = false)
    private Long societyId;

    @ApiModelProperty("societyNo")
    @Column(name = "society_no", length = 50)
    private String societyNo;

    @ApiModelProperty("branchId")
    @Column(name = "branch_id", nullable = false)
    private Long branchId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "branch_id", insertable = false, updatable = false)
    private Branch branch;

    @ApiModelProperty("branchNo")
    @Column(name = "branch_no", length = 50)
    private String branchNo;

    @ApiModelProperty("amendmentType")
    @Column(name = "amendment_type", length = 255)
    private String amendmentType;

    @ApiModelProperty("meetingId")
    @Column(name = "meeting_id")
    private Long meetingId;

    @ApiModelProperty("meetingType")
    @Column(name = "meeting_type", length = 255)
    private String meetingType;

    @ApiModelProperty("meetingDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    @Column(name = "meeting_date")
    private LocalDate meetingDate;

    @ApiModelProperty("meetingPlace")
    @Column(name = "meeting_place", length = 255)
    private String meetingPlace;

    @ApiModelProperty("meetingTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    @Column(name = "meeting_time")
    private LocalTime meetingTime;

    @ApiModelProperty("totalAttendees")
    @Column(name = "total_attendees")
    private Integer totalAttendees;

    @ApiModelProperty("branchName")
    @Column(name = "branch_name", length = 255)
    private String branchName;

    @ApiModelProperty("address")
    @Column(name = "address", columnDefinition = "TEXT")
    private String address;

    @ApiModelProperty("countryCode")
    @Column(name = "country_code", length = 50)
    private String countryCode;

    @ApiModelProperty("stateCode")
    @Column(name = "state_id", length = 50)
    private String stateCode;

    @ApiModelProperty("districtCode")
    @Column(name = "district_id", length = 50)
    private String districtCode;

    @ApiModelProperty("smallDistrictCode")
    @Column(name = "small_district_code", length = 50)
    private String smallDistrictCode;

    @ApiModelProperty("cityCode")
    @Column(name = "city_code", length = 50)
    private String cityCode;

    @ApiModelProperty("city")
    @Column(name = "city", length = 100)
    private String city;

    @ApiModelProperty("postcode")
    @Column(name = "postcode", length = 20)
    private String postcode;

    @ApiModelProperty("mailingAddress")
    @Column(name = "mailing_address", columnDefinition = "TEXT")
    private String mailingAddress;

    @ApiModelProperty("mailingStateId")
    @Column(name = "mailing_state_id", length = 50)
    private String mailingStateId;

    @ApiModelProperty("mailingDistrictId")
    @Column(name = "mailing_district_id", length = 50)
    private String mailingDistrictId;

    @ApiModelProperty("mailingCity")
    @Column(name = "mailing_city", length = 100)
    private String mailingCity;

    @ApiModelProperty("mailingPostcode")
    @Column(name = "mailing_postcode", length = 20)
    private String mailingPostcode;

    @ApiModelProperty("acknowledge")
    @Column(name = "acknowledge")
    private Integer acknowledge;

    @ApiModelProperty("acknowledgeDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    @Column(name = "acknowledge_date")
    private LocalDate acknowledgeDate;

    @ApiModelProperty("paymentId")
    @Column(name = "payment_id")
    private Long paymentId;

    @ApiModelProperty("paymentMethod")
    @Column(name = "payment_method", length = 50)
    private String paymentMethod;

    @ApiModelProperty("paymentDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "payment_date")
    private LocalDateTime paymentDate;

    @ApiModelProperty("bankReferenceNo")
    @Column(name = "bank_reference_no", length = 50)
    private String bankReferenceNo;

    @ApiModelProperty("bankName")
    @Column(name = "bank_name", length = 255)
    private String bankName;

    @ApiModelProperty("receiptNo")
    @Column(name = "receipt_no", length = 255)
    private String receiptNo;

    @ApiModelProperty("receiptStatus")
    @Column(name = "receipt_status")
    private Integer receiptStatus;

    @ApiModelProperty("applicationStatusCode")
    @Column(name = "application_status_code")
    private Integer applicationStatusCode;

    @ApiModelProperty("ro")
    @Column(name = "ro", length = 50)
    private String ro;

    @ApiModelProperty("noteRo")
    @Column(name = "noteRo")
    private String noteRo;

    @ApiModelProperty("transferDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    @Column(name = "transfer_date")
    private LocalDate transferDate;

    @ApiModelProperty("originalBranchName")
    @Column(name = "original_branch_name", length = 255)
    private String originalBranchName;

    @ApiModelProperty("originalAddress")
    @Column(name = "original_address", columnDefinition = "TEXT")
    private String originalAddress;

    @ApiModelProperty("originalCountryCode")
    @Column(name = "original_country_code", length = 50)
    private String originalCountryCode;

    @ApiModelProperty("originalStateCode")
    @Column(name = "original_state_code", length = 50)
    private String originalStateCode;

    @ApiModelProperty("originalDistrictCode")
    @Column(name = "original_district_code", length = 50)
    private String originalDistrictCode;

    @ApiModelProperty("originalSmallDistrictCode")
    @Column(name = "original_small_district_code", length = 50)
    private String originalSmallDistrictCode;

    @ApiModelProperty("originalCityCode")
    @Column(name = "original_city_code", length = 50)
    private String originalCityCode;

    @ApiModelProperty("originalCity")
    @Column(name = "original_city", length = 100)
    private String originalCity;

    @ApiModelProperty("originalPostcode")
    @Column(name = "original_postcode", length = 20)
    private String originalPostcode;

    @ApiModelProperty("originalMailingAddress")
    @Column(name = "original_mailing_address", columnDefinition = "TEXT")
    private String originalMailingAddress;

    @ApiModelProperty("originalMailingStateId")
    @Column(name = "original_mailing_state_id", length = 50)
    private String originalMailingStateId;

    @ApiModelProperty("originalMailingDistrictId")
    @Column(name = "original_mailing_district_id", length = 50)
    private String originalMailingDistrictId;

    @ApiModelProperty("originalMailingCity")
    @Column(name = "original_mailing_city", length = 100)
    private String originalMailingCity;

    @ApiModelProperty("originalMailingPostcode")
    @Column(name = "original_mailing_postcode", length = 20)
    private String originalMailingPostcode;

    @ApiModelProperty("epaymentId")
    @Column(name = "epayment_id", length = 100)
    private String epaymentId;

    @ApiModelProperty("reconcileDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    @Column(name = "reconcile_date")
    private LocalDate reconcileDate;
}
