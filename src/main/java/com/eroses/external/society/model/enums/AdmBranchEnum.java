package com.eroses.external.society.model.enums;

import lombok.Getter;

@Getter
public enum AdmBranchEnum {
    HQ("HQ", "HQ", "IBU PEJABAT JABATAN PENDAFTARAN PERTUBUHAN MALAYSIA"), //depreciated, not active
    PRK("PRK", "PER<PERSON><PERSON>", "PERA<PERSON>"),
    PRLS("PRLS", "PER<PERSON><PERSON>", "PERLIS"),
    KDH("KDH", "KEDAH", "KEDAH"),
    TGNU("TGNU", "TERENGGANU", "TERENG<PERSON>NU"),
    KUL("KUL", "KUALA LUMPUR", "WPKL"),
    JHR("JHR", "JOH<PERSON>", "JOH<PERSON>"),
    SEL("SEL", "SELANGOR", "SELANGOR"),
    N9("N9", "NEGERI SEMBILAN", "NEGERI SEMBILAN"),
    SAR("009", "SARAWA<PERSON>", "SARAWAK"),
    MEL("ME<PERSON>", "MELAKA", "MELAKA"),
    SBH("SBH", "SABA<PERSON>", "SABAH"),
    PPG("PPG", "<PERSON><PERSON><PERSON>U PINANG", "PULAU PINANG"),
    PHG("PHG", "PAHANG", "PAHANG"),
    KEL("KEL", "KELANTAN", "KELANTAN"),
    LAB("LAB", "LABUAN", "LABUAN"),  //depreciated, not active
    KDN("KDN", "KDN", "HAL EHWAL PENDAFTARAN NEGARA DAN PENDAFTARAN KEMENTERIAN DALAM NEGERI"),
    PT("BPT", "PUTRAJAYA", "PENGURUSAN TERTINGGI"),
    PUU("BPUU", "PUTRAJAYA", "PENASIHAT UNDANG-UNDANG"),
    PP("BPP", "PUTRAJAYA", "PENGURUSAN PERTUBUHAN"), // HQ branch, for politik
    PTM("BPTM", "PUTRAJAYA", "PENGURUSAN TEKNOLOGI MAKLUMAT"),
    PK("BPK", "PUTRAJAYA", "PENGUATKUASAAN"),
    KP("BKP", "PUTRAJAYA", "KHIDMAT PENGURUSAN"),
    PS("BPS", "PUTRAJAYA", "PENGURUSAN STRATEGIK"),
    MR("MR", "SARAWAK", "PENGURUSAN STRATEGIK MIRI");

    private final String code;
    private final String state;
    private final String description;

    AdmBranchEnum(String code, String state, String description) {
        this.code = code;
        this.state = state;
        this.description = description;
    }

    public static AdmBranchEnum getBranchByCode(String code) {
        for (AdmBranchEnum branch : AdmBranchEnum.values()) {
            if (branch.code.equals(code)) {
                return branch;
            }
        }
        return null;
    }
}
