package com.eroses.external.society.model.enums;

import lombok.Getter;

@Getter
public enum EventAttendeesEnum {
    ATTENDEES_TELAH_DIHANTAR(100, "PENDAFTARAN TELAH DIHANTAR"),
    ATTENDEES_DISAHKAN(102, "PENDAFTARABN DILULUSKAN"),
    ATTENDEES_DITOLAK(103, "PENDAFTARAN DITOLAK"),
    ATTENDEES_DIBATALKAN(104, "PENDAFTARAN DIBATALKAN");


    private final int code;
    private final String description;

    EventAttendeesEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static EventAttendeesEnum getDesByCode(int code) {
        for (EventAttendeesEnum status : EventAttendeesEnum.values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }
}
