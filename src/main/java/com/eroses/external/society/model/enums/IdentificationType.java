package com.eroses.external.society.model.enums;

import lombok.Getter;

@Getter
public enum IdentificationType {
    MYKAD(1, "MyKad"),
    POLIS(2, "No. Polis"),
    TENTERA(3, "No. Tentera"),
    MYPR(4, "MyPR"),
    PASSPORT(5, "No. Passport"),
    VISA(6, "No. Visa"),
    PERMIT(7, "No. Permit");

    private final int code;
    private final String name;

    IdentificationType(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(int code) {
        for (IdentificationType identificationType : IdentificationType.values()) {
            if (identificationType.getCode() == code) {
                return identificationType.getName();
            }
        }
        return null;
    }
}
