package com.eroses.external.society.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ExternalAgencyReview extends BaseEntity implements Serializable {

    @ApiModelProperty
    @Column(name = "society_id", nullable = false)
    private Long societyId;

    @ApiModelProperty("societyNo")
    @Column(name = "society_no", length = 255)
    private String societyNo;

    @ApiModelProperty("status")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty("submissionDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "submission_date")
    private LocalDateTime submissionDate;

    @ApiModelProperty("submittedBy")
    @Column(name = "submitted_by", length = 255)
    private Long submittedBy;

    @ApiModelProperty("note")
    @Column(name = "note", columnDefinition = "TEXT")
    private String note;
}
