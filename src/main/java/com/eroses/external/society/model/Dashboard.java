package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Dashboard extends BaseEntity implements Serializable {
    
    @ApiModelProperty("Dashboard ID")
    private Long id;

    @ApiModelProperty("Dashboard ID (string)")
    private String dashboardId;

    @ApiModelProperty("Dashboard code")
    private String code;
    
    @ApiModelProperty("Dashboard name")
    private String name;
    
    @ApiModelProperty("Dashboard type")
    private String type;
    
    @ApiModelProperty("Dashboard module")
    private String module;
    
    @ApiModelProperty("Dashboard category")
    private String category;
    
    @ApiModelProperty("Dashboard permission")
    private String permission;
    
    @ApiModelProperty("Order sequence")
    private Integer orderSequence;
}
