package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ConstitutionValue extends Model implements Serializable {
    @ApiModelProperty
    private Long id;

    @ApiModelProperty
    private Long amendmentId;

    @ApiModelProperty
    private Long societyId;

    @ApiModelProperty
    private String societyNo;

    @ApiModelProperty
    private Long constitutionTypeId;

    @ApiModelProperty
    private Long clauseContentId;

    @ApiModelProperty
    private String clauseNo;

    @ApiModelProperty
    private String clauseName;

    @ApiModelProperty
    private Long constitutionContentId;

    @ApiModelProperty
    private String societyName;

    @ApiModelProperty
    private String titleName;

    @ApiModelProperty
    private String definitionName;

    @ApiModelProperty
    private String societyAddress;

    @ApiModelProperty
    private String societyCity;

    @ApiModelProperty
    private String societyDistrictCode;

    @ApiModelProperty
    private String societyPostcode;

    @ApiModelProperty
    private String societyParlimentCode;

    @ApiModelProperty
    private String societyState;

    @ApiModelProperty
    private String mailingAddress;

    @ApiModelProperty
    private String mailingAddressCity;

    @ApiModelProperty
    private String mailingAddressDistrictCode;

    @ApiModelProperty
    private String mailingAddressPostcode;

    @ApiModelProperty
    private String mailingAddressParlimentCode;

    @ApiModelProperty
    private String mailingAddressState;

    @ApiModelProperty
    private String meetingFrequency;

    @ApiModelProperty
    private String parentBeginningFiscalYear;

    @ApiModelProperty
    private String numberOfPrincipalAuditors;

    @ApiModelProperty
    private String branchMeetingFrequency;

    @ApiModelProperty
    private String financialYearBeginningBranch;

    @ApiModelProperty
    private String numberOfBranchAuditors;

    @ApiModelProperty
    private String parentPosition;

    @ApiModelProperty
    private String branchPosition;

    @ApiModelProperty
    private Long applicationStatusCode;

    @ApiModelProperty
    private String statusCode;

    @ApiModelProperty
    private Boolean customJawatan;
}
