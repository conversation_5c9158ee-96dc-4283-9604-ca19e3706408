package com.eroses.external.society.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.*;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SearchInformationDocument extends BaseEntity {

    @ApiModelProperty
    @Column(name = "search_information_id", nullable = false)
    private Long searchInformationId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "search_information_id", insertable = false, updatable = false)
    private SearchInformation searchInformation;

    @ApiModelProperty
    @Column(name = "name")
    private String name;

    @ApiModelProperty
    @Column(name = "template_code")
    private String templateCode;

    @ApiModelProperty
    @Column(name = "s3_url")
    private String s3Url;

    @ApiModelProperty
    @Column(name = "is_generated", nullable = false)
    private Boolean isGenerated = false;

    @ApiModelProperty
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "first_accessed_at")
    private LocalDateTime firstAccessedAt;

    @ApiModelProperty
    @Column(name = "type")
    private String type;

    @ApiModelProperty
    @Column(name = "downloadPeriod")
    private Integer downloadPeriod;

    @ApiModelProperty
    @Column(name = "amount")
    private Float amount;

    @ApiModelProperty
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty
    @Column(name = "format")
    private String format;

    @ApiModelProperty
    @Column(name = "language")
    private String language;

    @ApiModelProperty
    private Integer applicationStatusCode;
}