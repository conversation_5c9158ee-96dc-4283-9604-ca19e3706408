package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class AdmBranch extends BaseEntity {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("code")
    private String code;

    @ApiModelProperty("description")
    private String description;

    @ApiModelProperty("address")
    private String address;

    @ApiModelProperty("cityCode")
    private String cityCode;

    @ApiModelProperty("districtCode")
    private String districtCode;

    @ApiModelProperty("stateCode")
    private String stateCode;

    @ApiModelProperty("postcode")
    private Integer postcode;

    @ApiModelProperty("phoneNumber")
    private String phoneNumber;

    @ApiModelProperty("faxNumber")
    private String faxNumber;

    @ApiModelProperty("status")
    private Integer status;
}
