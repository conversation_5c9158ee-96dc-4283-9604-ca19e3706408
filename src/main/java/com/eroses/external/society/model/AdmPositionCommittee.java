package com.eroses.external.society.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.*;
import lombok.*;

@Data
@Table(name = "adm_position_committee")
@ApiModel(description = "Represents a position in the committee with details.")
@NoArgsConstructor
@AllArgsConstructor
public class AdmPositionCommittee extends BaseEntity {

    @Column(nullable = false, unique = true)
    @ApiModelProperty("Unique code for the position")
    private Integer code;

    @Column(nullable = false, length = 255)
    @ApiModelProperty("Name of the position")
    private String name;

    @Column(nullable = false)
    @ApiModelProperty("Ranking of the position")
    private Integer rank;

    @Column(name = "is_committee", nullable = false)
    @ApiModelProperty("Indicates if the position is part of a committee (1: Yes, 0: No)")
    private Boolean isCommittee;

    @Column(name = "is_high_rank_committee", nullable = false)
    @ApiModelProperty("Indicates if the position is a high-rank committee (1: Yes, 0: No)")
    private Boolean isHighRankCommittee;

    @Column(nullable = false, columnDefinition = "TINYINT DEFAULT 1")
    @ApiModelProperty("Status of the position (1: Active, 0: Inactive)")
    private Boolean status = true;
}