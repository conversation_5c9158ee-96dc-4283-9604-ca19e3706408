package com.eroses.external.society.model;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ConstitutionContent extends Model implements Serializable {
    @ApiModelProperty
    private Long id;

    @ApiModelProperty
    private Long oldConstitutionContentId;

    @ApiModelProperty
    private Long amendmentId;

    @ApiModelProperty
    private Long societyId;

    @ApiModelProperty
    private String societyNo;

    @ApiModelProperty
    private Long constitutionTypeId;

    @ApiModelProperty
    @Column(name = "clause_content_id")
    private Long clauseContentId;

    @ApiModelProperty
    private String clauseNo;

    @ApiModelProperty
    private String clauseName;

    @ApiModelProperty
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "clause_content_id", insertable = false, updatable = false)
    private ClauseContent clauseContent;

    @ApiModelProperty
    private String description;

    @ApiModelProperty
    private String modifiedTemplate;

    @ApiModelProperty
    private Long applicationStatusCode;

    @ApiModelProperty
    private String status;

    @ApiModelProperty
    private Long checkUpdate;

    @ApiModelProperty
    private Boolean amendmentToggle;

    @ApiModelProperty
    private Boolean hideConstitution;
}
