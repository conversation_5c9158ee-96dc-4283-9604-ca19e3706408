package com.eroses.external.society.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Data
@SuperBuilder
@NoArgsConstructor
public class TrainingQuiz extends BaseEntity {
    private Long id;
    private Long trainingCourseId;
    private String title;
    private String description;
    private Integer timeLimitMinutes;
    private Boolean isMandatory;
    private Integer minScore;
    private String quizNo;
}
