package com.eroses.external.society.model.enums;

import com.eroses.external.society.model.EventCertificate;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum EventCertificateTemplate {
    SIJIL_PENGLIBATAN("Si<PERSON><PERSON>", "SIJILPENGLIBATAN" );


    private final String name;

    private final String code;
    public static EventCertificateTemplate getCertificateTemplate(String code) {
        for (EventCertificateTemplate certificateTemplateEnum : EventCertificateTemplate.values()) {
            if (Objects.equals(certificateTemplateEnum.code, code)) {
                return certificateTemplateEnum;
            }
        }
        return null;
    }
}
