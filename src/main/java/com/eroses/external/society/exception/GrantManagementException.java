package com.eroses.external.society.exception;

import com.eroses.external.society.model.enums.GrantErrorCode;

/**
 * Custom exception for the Grant Management feature.
 */
public class GrantManagementException extends RuntimeException {
    
    private final String errorCode;
    
    /**
     * Constructs a new GrantManagementException with the specified error code and detail message.
     *
     * @param errorCode the error code from GrantErrorCode enum
     * @param message the detail message
     */
    public GrantManagementException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    /**
     * Constructs a new GrantManagementException with the specified error code, detail message, and cause.
     *
     * @param errorCode the error code from GrantError<PERSON>ode enum
     * @param message the detail message
     * @param cause the cause
     */
    public GrantManagementException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    /**
     * Constructs a new GrantManagementException with the specified GrantErrorCode enum and detail message.
     *
     * @param errorCode the error code from GrantError<PERSON>ode enum
     * @param message the detail message
     */
    public GrantManagementException(GrantErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode.getCode();
    }
    
    /**
     * Constructs a new GrantManagementException with the specified GrantErrorCode enum, detail message, and cause.
     *
     * @param errorCode the error code from GrantErrorCode enum
     * @param message the detail message
     * @param cause the cause
     */
    public GrantManagementException(GrantErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode.getCode();
    }
    
    /**
     * Gets the error code.
     *
     * @return the error code
     */
    public String getErrorCode() {
        return errorCode;
    }
}
