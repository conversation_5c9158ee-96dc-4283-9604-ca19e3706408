package com.eroses.external.society.scheduler;

import com.eroses.external.society.service.posting.PostingWriteDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class PostingScheduler {
    
    private final PostingWriteDomainService postingWriteDomainService;
    
    /**
     * Scheduled task to check for expired postings and update their status to UNPUBLISHED
     * Runs every hour
     */
    @Scheduled(cron = "0 0 * * * *") // Every hour
    public void processExpiredPostings() {
        try {
            log.info("Starting scheduled task: processExpiredPostings");
            postingWriteDomainService.processExpiredPostings();
            log.info("Completed scheduled task: processExpiredPostings");
        } catch (Exception e) {
            log.error("Error in scheduled task: processExpiredPostings", e);
        }
    }
    
    /**
     * Scheduled task to check for postings that need to be published based on their pre-publish date
     * Runs every 15 minutes
     */
    @Scheduled(cron = "0 */15 * * * *") // Every 15 minutes
    public void processPostingsToPublish() {
        try {
            log.info("Starting scheduled task: processPostingsToPublish");
            postingWriteDomainService.processPostingsToPublish();
            log.info("Completed scheduled task: processPostingsToPublish");
        } catch (Exception e) {
            log.error("Error in scheduled task: processPostingsToPublish", e);
        }
    }
}
