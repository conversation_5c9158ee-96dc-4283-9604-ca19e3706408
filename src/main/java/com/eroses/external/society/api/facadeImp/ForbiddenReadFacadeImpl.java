package com.eroses.external.society.api.facadeImp;

import com.eroses.external.society.api.annotation.MicroService;
import com.eroses.external.society.api.converter.output.ForbiddenApiOutputConverter;
import com.eroses.external.society.api.facade.ForbiddenReadFacade;
import com.eroses.external.society.dto.request.fobbiden.ForbiddenKeywordCheckRequest;
import com.eroses.external.society.dto.request.fobbiden.ForbiddenKeywordSearchRequest;
import com.eroses.external.society.dto.response.forbiddenRule.ForbiddenKeywordResponse;
import com.eroses.external.society.dto.response.forbiddenRule.ForbiddenKeywordSearchResponse;
import com.eroses.external.society.dto.response.forbiddenRule.ForbiddenLogoResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.ForbiddenKeyword;
import com.eroses.external.society.model.ForbiddenLogo;
import com.eroses.external.society.model.Paging;
import com.eroses.external.society.model.enums.ForbiddenEnum;
import com.eroses.external.society.service.*;
import dev.brachtendorf.jimagehash.hash.Hash;
import dev.brachtendorf.jimagehash.hashAlgorithms.*;
import dev.brachtendorf.jimagehash.matcher.exotic.SingleImageMatcher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.datavec.image.loader.NativeImageLoader;
import org.deeplearning4j.nn.graph.ComputationGraph;

import org.nd4j.linalg.api.ndarray.INDArray;
import org.nd4j.linalg.dataset.api.preprocessor.DataNormalization;
import org.nd4j.linalg.dataset.api.preprocessor.VGG16ImagePreProcessor;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
//import java.awt.*;
//import java.awt.image.BufferedImage;
//import java.awt.image.DataBuffer;
import java.io.*;
import java.net.URL;
import java.nio.FloatBuffer;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.*;
import java.util.List;
import java.util.regex.MatchResult;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ForbiddenReadFacadeImpl implements ForbiddenReadFacade {
    private final ForbiddenKeywordReadDomainService forbiddenKeywordReadDomainService;
    private final ForbiddenApiOutputConverter forbiddenApiOutputConverter;
    private final ForbiddenLogoReadDomainService forbiddenLogoReadDomainService;
    private final ForbiddenLogoWriteDomainService forbiddenLogoWriteDomainService;
    private ComputationGraph model;
    private static final double DEFAULT_THRESHOLD = 0.75;
    ;
    private static final double LOGO_MATCH_THRESHOLD = 0.65;
    private final ForbiddenLogoService forbiddenLogoService;
    private final ForbiddenLogoService2 forbiddenLogoService2;


    public static class MatchResult {
        public final String url;
        public final double similarity;

        public MatchResult(String url, double similarity) {
            this.url = url;
            this.similarity = similarity;
        }
    }


    @Override
    @MicroService
    public ApiResponse<List<ForbiddenKeywordResponse>> getAll() {
        try {
            List<ForbiddenKeyword> forbiddens = forbiddenKeywordReadDomainService.findAll();
            List<ForbiddenKeywordResponse> responses = forbiddens.stream()
                    .map(forbiddenApiOutputConverter::convertToResponse)
                    .collect(Collectors.toList());
            return new ApiResponse<>("Data senarai larangan & larangan kelabu berjaya diperoleh.", responses);
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mendapatkan senarai larangan & larangan kelabu: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mendapatkan senarai larangan & larangan kelabu.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mendapatkan senarai larangan & larangan kelabu: " + e.getMessage(), null, LocalDateTime.now());
        }
    }

    @Override
    @MicroService
    public ApiResponse<List<ForbiddenKeywordResponse>> getAllSenaraiLarangan() {
        try {
            List<ForbiddenKeyword> forbiddens = forbiddenKeywordReadDomainService.findByForbiddenType(ForbiddenEnum.SENARAI_LARANGAN.getValue());
            List<ForbiddenKeywordResponse> responses = forbiddens.stream()
                    .map(forbiddenApiOutputConverter::convertToResponse)
                    .collect(Collectors.toList());
            return new ApiResponse<>("Data senarai larangan berjaya diperoleh.", responses);
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mendapatkan senarai larangan:" + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mendapatkan senarai larangan.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mendapatkan senarai larangan: " + e.getMessage(), null, LocalDateTime.now());
        }
    }

    @Override
    @MicroService
    public ApiResponse<Paging<ForbiddenKeywordResponse>> getAllSenaraiLaranganPagination(Integer pageNo, Integer pageSize) {
        try {
            Integer offset = (pageNo - 1) * pageSize;
            Integer limit = pageSize;
            List<ForbiddenKeyword> forbiddens = forbiddenKeywordReadDomainService.findByForbiddenTypePagination(ForbiddenEnum.SENARAI_LARANGAN.getValue(), offset, limit);
            Long totalElements = forbiddenKeywordReadDomainService.countByForbiddenType(ForbiddenEnum.SENARAI_LARANGAN.getValue());
            List<ForbiddenKeywordResponse> responses = forbiddens.stream()
                    .map(forbiddenApiOutputConverter::convertToResponse)
                    .collect(Collectors.toList());
            Paging<ForbiddenKeywordResponse> pagingResponse = new Paging<>(
                    totalElements,
                    responses,
                    pageSize
            );
            return new ApiResponse<>("Data senarai larangan berjaya diperoleh.", pagingResponse);
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mendapatkan senarai larangan: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mendapatkan senarai larangan.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mendapatkan senarai larangan: " + e.getMessage(), null, LocalDateTime.now());
        }
    }

    @Override
    @MicroService
    public ApiResponse<List<ForbiddenKeywordResponse>> getAllSenaraiKelabu() {
        try {
            List<ForbiddenKeyword> forbiddens = forbiddenKeywordReadDomainService.findByForbiddenType(ForbiddenEnum.SENARAI_KELABU.getValue());
            List<ForbiddenKeywordResponse> responses = forbiddens.stream()
                    .map(forbiddenApiOutputConverter::convertToResponse)
                    .collect(Collectors.toList());
            return new ApiResponse<>("Data larangan kelabu berjaya diperoleh.", responses);
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mendapatkan larangan kelabu: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mendapatkan larangan kelabu.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mendapatkan larangan kelabu: " + e.getMessage(), null, LocalDateTime.now());
        }
    }

    @Override
    @MicroService
    public ApiResponse<Paging<ForbiddenKeywordResponse>> getAllSenaraiKelabuPagination(Integer pageNo, Integer pageSize) {
        try {
            Integer offset = (pageNo - 1) * pageSize;
            Integer limit = pageSize;
            List<ForbiddenKeyword> forbiddens = forbiddenKeywordReadDomainService.findByForbiddenTypePagination(ForbiddenEnum.SENARAI_KELABU.getValue(), offset, limit);
            Long totalElements = forbiddenKeywordReadDomainService.countByForbiddenType(ForbiddenEnum.SENARAI_KELABU.getValue());
            List<ForbiddenKeywordResponse> responses = forbiddens.stream()
                    .map(forbiddenApiOutputConverter::convertToResponse)
                    .collect(Collectors.toList());
            Paging<ForbiddenKeywordResponse> pagingResponse = new Paging<>(
                    totalElements,
                    responses,
                    pageSize
            );
            return new ApiResponse<>("Data larangan kelabu berjaya diperoleh.", pagingResponse);
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mendapatkan larangan kelabu: " + rootCauseMessage, null, LocalDateTime.now());

        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mendapatkan larangan kelabu.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mendapatkan larangan kelabu: " + e.getMessage(), null, LocalDateTime.now());
        }


    }

    @Override
    @MicroService
    public ApiResponse<ForbiddenKeywordResponse> getOneById(Long id) {
        try {
            ForbiddenKeyword forbidden = forbiddenKeywordReadDomainService.findSenaraiById(id);
            if (forbidden == null) {
                return new ApiResponse<>("Error", HttpStatus.NOT_FOUND.value(), "Nama larangan tidak dijumpai.", null, LocalDateTime.now());
            }
            ForbiddenKeywordResponse response = forbiddenApiOutputConverter.convertToResponse(forbidden);
            return new ApiResponse<>("Forbidden retrieved successfully.", response);
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mendapatkan larangan: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mendapatkan larangan.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mendapatkan larangan: " + e.getMessage(), null, LocalDateTime.now());
        }
    }

    @Override
    @MicroService
    public ApiResponse<Boolean> checkSenaraiLarangan(ForbiddenKeywordCheckRequest request) {
        try {
            List<String> forbiddens = forbiddenKeywordReadDomainService.findOnlyKeywordByForbiddenType(ForbiddenEnum.SENARAI_LARANGAN.getValue());
            if (forbiddens.isEmpty()) {
                return new ApiResponse<>("Semakan selesai", true);
            }
            String matchedForbidden = isForbiddenKeyword(request.getKeyword(), forbiddens);
            if (!matchedForbidden.isEmpty()) {
                return new ApiResponse<>(
                        String.format("%s menggunakan nama terlarang: %s.", request.getCheckType(), matchedForbidden),
                        true
                );

            } else {
                return new ApiResponse<>("Semakan selesai", true);
            }
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa memeriksa larangan: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa memeriksa larangan.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa memeriksa larangan: " + e.getMessage(), null, LocalDateTime.now());
        }
    }

    @Override
    @MicroService
    public ApiResponse<Paging<ForbiddenKeywordSearchResponse>> searchLarangan(ForbiddenKeywordSearchRequest request) {
        Integer offset = (request.pageNo() - 1) * request.pageSize();
        Integer limit = request.pageSize();
        String forbiddenType = Objects.requireNonNull(ForbiddenEnum.getForbiddenEnum(request.forbiddenType())).getDescription();
        try {
            List<ForbiddenKeyword> forbiddens = forbiddenKeywordReadDomainService.searchLaranganOrLaranganKelabu(request.searchQuery(), request.forbiddenType(), offset, limit, request.statusCode(), request.activeStatus());
            Long totalElements = forbiddenKeywordReadDomainService.countSearchedLarangans(request.searchQuery(), request.statusCode(), request.activeStatus(), request.forbiddenType());


            if (forbiddens != null && !forbiddens.isEmpty()) {
                // Generate pagination response
                List<ForbiddenKeywordSearchResponse> forbiddensSearchResponse = forbiddenApiOutputConverter.forbiddenKeyword2SearchResponse(forbiddens);

                Paging<ForbiddenKeywordSearchResponse> pagingResponse = new Paging<>(
                        totalElements,
                        forbiddensSearchResponse,
                        request.pageSize()
                );
                return new ApiResponse<>(
                        String.format("%s berjaya ditemui.", forbiddenType),
                        pagingResponse
                );
            }
            return new ApiResponse<>("Tiada larangan ditemui berdasarkan kriteria carian yang diberikan", 202, String.format("Tiada %s ditemui berdasarkan kriteria carian yang diberikan", forbiddenType), null, LocalDateTime.now());
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mencari larangan: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mencari larangan.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mencari larangan: " + e.getMessage(), null, LocalDateTime.now());
        }
    }

    @MicroService
    public ApiResponse<Paging<ForbiddenLogoResponse>> searchLaranganLogo(String catatan, Integer pageNo, Integer pageSize, Boolean activeStatus) {
        try {
            Integer offset = (pageNo - 1) * pageSize;
            Integer limit = pageSize;
            List<ForbiddenLogo> forbiddens = forbiddenLogoReadDomainService.searchLaranganLogo(catatan, offset, limit, activeStatus);
            Long totalElements = forbiddenLogoReadDomainService.countSearchLaranganLogo(catatan, activeStatus);
            if (forbiddens != null && !forbiddens.isEmpty()) {
                // Generate pagination response
                List<ForbiddenLogoResponse> forbiddensSearchResponse = forbiddenApiOutputConverter.forbiddenLogo2SearchResponse(forbiddens);
                Paging<ForbiddenLogoResponse> pagingResponse = new Paging<>(
                        totalElements,
                        forbiddensSearchResponse,
                        pageSize
                );
                return new ApiResponse<>("Logo retrieved successfully.", pagingResponse);
            }
            return new ApiResponse<>("No logo found with the given search criteria.", 202, "No logo match the criteria.", null, LocalDateTime.now());

        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mencari larangan logo: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mencari larangan logo.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mencari larangan logo: " + e.getMessage(), null, LocalDateTime.now());
        }
    }

    @Override
    @MicroService
    public ApiResponse<Boolean> checkSenaraiKelabu(String keyword, String checkType) {
        try {
            List<String> greyLists = forbiddenKeywordReadDomainService.findOnlyKeywordByForbiddenType(ForbiddenEnum.SENARAI_KELABU.getValue());
            String isMatchedGreyList = isGreyList(keyword, greyLists);

            if (Objects.isNull(isMatchedGreyList)) {
                return new ApiResponse<>("Semakan selesai", true);
            }

            return new ApiResponse<>(String.format("%s menggunakan nama kelabu.", checkType), true);
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa memeriksa larangan kelabu: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa memeriksa larangan kelabu.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa memeriksa larangan kelabu: " + e.getMessage(), null, LocalDateTime.now());
        }
    }


//    @Override
//    @MicroService
//    public ApiResponse<List<ForbiddenGetResponse>> searchLaranganKelabu(String keyword) {
//        try {
//            List<Forbidden> forbiddens = forbiddenReadDomainService.searchByKeyword(keyword, "LARANGAN_KELABU");
//            List<ForbiddenGetResponse> responses = forbiddens.stream()
//                    .map(forbiddenApiOutputConverter::convertToGetResponse)
//                    .collect(Collectors.toList());
//            return new ApiResponse<>("Search completed successfully.", responses);
//        } catch (DataAccessException e) {
//            Throwable rootCause = e.getRootCause();
//            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
//            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mencari larangan kelabu: " + rootCauseMessage, null, LocalDateTime.now());
//        } catch (NullPointerException e) {
//            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mencari larangan kelabu.", null, LocalDateTime.now());
//        } catch (Exception e) {
//            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mencari larangan kelabu: " + e.getMessage(), null, LocalDateTime.now());
//        }
//    }

    @Override
    @MicroService
    public ApiResponse<List<ForbiddenLogoResponse>> getAllLaranganLogo() {
        try {
            List<ForbiddenLogo> forbiddensLogo = forbiddenLogoReadDomainService.findAllLaranganLogo();
            List<ForbiddenLogoResponse> responses = forbiddenApiOutputConverter.forbiddenLogoListToResponseList(forbiddensLogo);
            return new ApiResponse<>("Senarai larangan logo berjaya diperoleh.", responses);
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mendapatkan larangan logo: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mendapatkan larangan logo.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mendapatkan larangan logo: " + e.getMessage(), null, LocalDateTime.now());
        }
    }

    @Override
    @MicroService
    public ApiResponse<ForbiddenLogoResponse> getLaranganLogoById(Long id) {
        try {
            ForbiddenLogo forbidden = forbiddenLogoReadDomainService.findLaranganLogoById(id);
            if (forbidden == null) {
                return new ApiResponse<>("Error", HttpStatus.NOT_FOUND.value(), "Larangan logo not found.", null, LocalDateTime.now());
            }
            ForbiddenLogoResponse response = forbiddenApiOutputConverter.forbiddenLogo2forbiddenLogoResponse(forbidden);
            return new ApiResponse<>("Larangan logo berjaya diperoleh.", response);
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mendapatkan larangan logo: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mendapatkan larangan logo.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mendapatkan larangan logo: " + e.getMessage(), null, LocalDateTime.now());
        }
    }

    @Override
    @MicroService
    public ApiResponse<Boolean> checkExistingLogo(MultipartFile logo) {
        List<String> logoUrlList = forbiddenLogoReadDomainService.findAllLaranganLogoImageOnly();
        List<ForbiddenLogo> logos = forbiddenLogoReadDomainService.findAllLaranganLogo();
        List<String> logoVectorList = forbiddenLogoReadDomainService.findAllLaranganLogoVectorOnly();
//        List<String> logoUrlList = new ArrayList<>();
        if (logoUrlList.isEmpty()) {
            return new ApiResponse<>("Logo belum wujud", false);
        }
        String logoToTest = "https://doc-dev.ros.gov.my/takwim-banner/Subaru-logo.jpg_5892c14c-8878-42f9-8b2c-af2a29006462?versionId=hoingl2QOmMjCeYkktXWybgpOEJIJETX";
        try {
            boolean matched = forbiddenLogoService2.checkContainForbiddenLogo(logo, logoVectorList);
            if (matched) {
                return new ApiResponse<>("logo telah wujud " + matched, false);

            } else {
                return new ApiResponse<>("logo belum wujud " + matched, true);
            }


        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa memeriksa larangan logo: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa memeriksa larangan logo:" + e.getMessage(), null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa memeriksa larangan logo: " + e.getMessage(), null, LocalDateTime.now());
        }
    }

    @Override
    @MicroService
    public ApiResponse<Boolean> checkLaranganLogo(MultipartFile logo) {
        try {
//            List<String> forbiddenLogoList = forbiddenLogoReadDomainService.findAllLaranganLogoImageOnly();
//            String matchedLogoOrText = checkIfLogoIsForbidden(logo);
            List<String> forbiddenLogoList = forbiddenLogoReadDomainService.findAllLaranganLogoVectorOnly();
            boolean matchedLogo = forbiddenLogoService2.checkContainForbiddenLogo(logo, forbiddenLogoList);
            if (matchedLogo) {
                return new ApiResponse<>("Logo menggunakan logo terlarang.", true);
            }

            return new ApiResponse<>("Logo dibenarkan.", false);

//            return new ApiResponse<>("Semakkan selesai", false);
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa memeriksa larangan logo: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mendapatkan larangan logo.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa memeriksa larangan logo: " + e.getMessage(), null, LocalDateTime.now());
        }
    }


    public String isForbiddenKeyword(String input, List<String> forbiddenKeywords) {
        if (input == null || forbiddenKeywords == null || forbiddenKeywords.isEmpty()) {
            return null;
        }
        String normalizedInput = input.trim().toLowerCase();

        // 1. Check for full phrase match (multi-word or single-word)
        for (String forbidden : forbiddenKeywords) {
            String normalizedForbidden = forbidden.trim().toLowerCase();
            if (normalizedInput.contains(normalizedForbidden)) {
                return forbidden;
            }
        }

        // 2. Check for single word match
        String[] inputWords = normalizedInput.split("\\s+");
        for (String word : inputWords) {
            for (String forbidden : forbiddenKeywords) {
                if (word.equals(forbidden.trim().toLowerCase())) {
                    return forbidden;
                }
            }
        }

        // 3. Check for subset match
        Set<String> inputWordsSet = new HashSet<>(Arrays.asList(normalizedInput.split("\\s+")));
        for (String forbidden : forbiddenKeywords) {
            String normalizedForbidden = forbidden.trim().toLowerCase();
            Set<String> forbiddenWordsSet = new HashSet<>(Arrays.asList(normalizedForbidden.split("\\s+")));
            if (inputWordsSet.containsAll(forbiddenWordsSet) && !forbiddenWordsSet.isEmpty()) {
                return forbidden;
            }
        }
        return null;
    }

    public String isGreyList(String input, List<String> forbiddenKeywords) {
        if (input == null || forbiddenKeywords == null || forbiddenKeywords.isEmpty()) {
            return null;
        }

        // First check full phrase match
        String normalizedInput = input.trim().toLowerCase();
        Map<String, String> forbiddenMap = forbiddenKeywords.stream()
                .collect(Collectors.toMap(
                        s -> s.trim().toLowerCase(),
                        s -> s,
                        (existing, replacement) -> existing
                ));

        // Check full phrase
        if (forbiddenMap.containsKey(normalizedInput)) {
            return forbiddenMap.get(normalizedInput);
        }

        // Then check individual words if no full phrase match
        String[] inputWords = normalizedInput.split("\\s+");
        for (String word : inputWords) {
            if (forbiddenMap.containsKey(word)) {
                return forbiddenMap.get(word);
            }
        }
        return null;
    }


    private String checkIfLogoIsForbidden(MultipartFile logo) {
        try {
//            1st Layer Check label/text in Image
//            Map<String, String> result = forbiddenLogoService.validateMultipartFile(logo);
//            if (result.get("status").equals("INVALID")) {
//                return "Logo mengandungi teks terlarang: " + result.get("msg");
            List<String> forbiddenLogoList = forbiddenLogoReadDomainService.findAllLaranganLogoVectorOnly();
            boolean matchedLogo = forbiddenLogoService2.checkContainForbiddenLogo(logo, forbiddenLogoList);
            if (matchedLogo) {
                return "Logo menggunakan logo terlarang.";
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return "Logo dibenarkan";
    }


    //V2 comparison


}