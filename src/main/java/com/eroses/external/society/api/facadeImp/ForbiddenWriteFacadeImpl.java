package com.eroses.external.society.api.facadeImp;

import com.eroses.external.society.api.annotation.MicroService;
import com.eroses.external.society.api.converter.input.ForbiddenApiInputConverter;
import com.eroses.external.society.api.converter.output.ForbiddenApiOutputConverter;
import com.eroses.external.society.api.facade.ForbiddenWriteFacade;
//import com.eroses.external.society.api.facade.;
import com.eroses.external.society.dto.request.fobbiden.ForbiddenKeywordCreateRequest;
import com.eroses.external.society.dto.request.fobbiden.ForbiddenLogoCreateRequest;
import com.eroses.external.society.dto.request.fobbiden.ForbiddenLogoUpdateRequest;
import com.eroses.external.society.dto.response.forbiddenRule.ForbiddenKeywordCreateResponse;
import com.eroses.external.society.dto.request.fobbiden.ForbiddenKeywordUpdateRequest;
import com.eroses.external.society.dto.response.forbiddenRule.ForbiddenKeywordResponse;
import com.eroses.external.society.dto.response.forbiddenRule.ForbiddenLogoResponse;
import com.eroses.external.society.model.ApiResponse;
import com.eroses.external.society.model.ForbiddenKeyword;
import com.eroses.external.society.model.ForbiddenLogo;
import com.eroses.external.society.model.enums.ApplicationStatusCode;
import com.eroses.external.society.model.enums.AuditActionType;
import com.eroses.external.society.service.*;
import com.eroses.user.api.facade.UserFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class ForbiddenWriteFacadeImpl implements ForbiddenWriteFacade {
    private final ForbiddenApiInputConverter forbiddenApiInputConverter;
    private final ForbiddenApiOutputConverter forbiddenApiOutputConverter;
    private final ForbiddenKeywordWriteDomainService forbiddenWriteDomainService;
    private final ForbiddenKeywordReadDomainService forbiddenKeywordReadDomainService;
    private final UserFacade userFacade;
    private final AuditTrailWriteDomainService auditTrailWriteDomainService;
    private final ForbiddenLogoWriteDomainService forbiddenLogoWriteDomainService;
    private final ForbiddenLogoReadDomainService forbiddenLogoReadDomainService;
    private final ForbiddenLogoService2 forbiddenLogoService2;


    @Override
    @MicroService
    public ApiResponse<ForbiddenKeywordCreateResponse> create(ForbiddenKeywordCreateRequest request) throws Exception {
        try {
            ForbiddenKeyword forbidden = forbiddenApiInputConverter.forbiddenKeywordCreateRequest2ForbiddenLogo(request);
            forbidden.setCreatedBy(userFacade.me().getId());
            ForbiddenKeyword createdForbidden = forbiddenWriteDomainService.createSenarai(forbidden);

            ForbiddenKeywordCreateResponse response = forbiddenApiOutputConverter.forbiddenKeyWordToCreateResponse(createdForbidden);

            return new ApiResponse<>("Forbidden created successfully.", HttpStatus.CREATED.value(), "Success", response, LocalDateTime.now());
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mencipta kata kunci larangan: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mencipta kata kunci larangan.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mencipta kata kunci larangan: " + e.getMessage(), null, LocalDateTime.now());
        }
    }

    @Override
    @MicroService
    public ApiResponse<Long> delete(Long id) throws Exception {
        try {
            Boolean isDeleted = forbiddenWriteDomainService.deleteSenarai(id);
            if (!isDeleted) {
                return new ApiResponse<>("Error", 500, "Ralat berlaku semasa memadam kata kunci larangan.", null, LocalDateTime.now());
            }
            ForbiddenKeyword oldData = forbiddenKeywordReadDomainService.findSenaraiById(id);
            auditTrailWriteDomainService.createV2(oldData, null, AuditActionType.SENARAI_LARANGAN_DELETE.getActionType());
            return new ApiResponse<>("Forbidden deleted successfully.", HttpStatus.OK.value(), "Success", id, LocalDateTime.now());
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa memadam kata kunci larangan: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa memadam kata kunci larangan.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa memadam kata kunci larangan: " + e.getMessage(), null, LocalDateTime.now());
        }
    }

    @Override
    @MicroService
    public ApiResponse<ForbiddenKeywordResponse> update(Long id, ForbiddenKeywordUpdateRequest request) throws Exception {
        try {
            ForbiddenKeyword oldData = forbiddenKeywordReadDomainService.findSenaraiById(id);
            ForbiddenKeyword forbidden = forbiddenApiInputConverter.forbiddenKeywordCreateRequest2ForbiddenLogo(request);
            forbidden.setId(id);
            forbidden.setModifiedBy(userFacade.me().getId());

            Boolean isUpdated = forbiddenWriteDomainService.updateSenarai(forbidden);

            ForbiddenKeywordResponse response = forbiddenApiOutputConverter.convertToResponse(forbidden);
            auditTrailWriteDomainService.createV2(oldData, forbidden, AuditActionType.SENARAI_LARANGAN_UPDATE.getActionType());
            if (isUpdated) {
                return new ApiResponse<>("Success", HttpStatus.OK.value(), "Kata kunci larangan berjaya dikemas kini.", response, LocalDateTime.now());
            } else {
                return new ApiResponse<>("Error", HttpStatus.FORBIDDEN.value(), "Kemaskini kata kunci larangan tidak berjaya.", null, LocalDateTime.now());
            }

        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mengemaskini kata kunci larangan: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mengemaskini kata kunci larangan.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mengemaskini kata kunci larangan: " + e.getMessage(), null, LocalDateTime.now());
        }
    }

    @Override
    @MicroService
    public ApiResponse<ForbiddenLogoResponse> createLaranganLogo(ForbiddenLogoCreateRequest request) throws Exception {
        try {
            ForbiddenLogo forbidden = forbiddenApiInputConverter.forbiddenLogoRequest2ForbiddenLogo(request);
            forbidden.setCreatedBy(userFacade.me().getId());
            forbidden.setStatus(ApplicationStatusCode.AKTIF.getCode());
            String vectorString = forbiddenLogoService2.checkExistingToUpload(request.getLogoUrl());
            if (vectorString == null) {
                return new ApiResponse<>("Error", 404, "Logo yang dimasukkan adalah sama dengan logo yang telah ada.", null, LocalDateTime.now());
            }

            forbidden.setLogoVector(vectorString.getBytes());
            ForbiddenLogo createdForbiddenLogo = forbiddenLogoWriteDomainService.createLaranganLogo(forbidden);
            ForbiddenLogoResponse response = forbiddenApiOutputConverter.forbiddenLogo2forbiddenLogoResponse(createdForbiddenLogo);

            return new ApiResponse<>("Larangan logo berjaya dicipta.", HttpStatus.CREATED.value(), "Success", response, LocalDateTime.now());
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mencipta larangan logo: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mencipta larangan logo.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mencipta larangan logo: " + e.getMessage(), null, LocalDateTime.now());
        }

    }

    @Override
    @MicroService
    public ApiResponse<ForbiddenLogoResponse> updateLaranganLogo(Long id, ForbiddenLogoUpdateRequest request) throws Exception {
        try {
            ForbiddenLogo oldData = forbiddenLogoReadDomainService.findLaranganLogoById(id);
            if (oldData == null) {
                return new ApiResponse<>("Error", 404, "Larangan logo dengan id " + id + " tidak dijumpai.", null, LocalDateTime.now());
            }

            ForbiddenLogo forbidden = forbiddenApiInputConverter.forbiddenLogoUpdateRequest2ForbiddenLogo(request);
            forbidden.setId(id);
            forbidden.setModifiedBy(userFacade.me().getId());

            ForbiddenLogo updatedForbiddenLogo = forbiddenLogoWriteDomainService.updateLaranganLogo(forbidden);
            ForbiddenLogoResponse response = forbiddenApiOutputConverter.forbiddenLogo2forbiddenLogoResponse(updatedForbiddenLogo);
            auditTrailWriteDomainService.createV2(oldData, forbidden, AuditActionType.LARANGAN_LOGO_UPDATE.getActionType());


            return new ApiResponse<>("Larangan logo berjaya dikemas kini.", HttpStatus.OK.value(), "Success", response, LocalDateTime.now());

        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa mengemaskini larangan logo: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa mengemaskini larangan logo.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa mengemaskini larangan logo: " + e.getMessage(), null, LocalDateTime.now());
        }
    }


    @Override
    @MicroService
    public ApiResponse<Long> deleteLaranganLogo(Long id) throws Exception {
        try {
            Boolean isDeleted = forbiddenLogoWriteDomainService.deleteLaranganLogo(id);
            if (!isDeleted) {
                return new ApiResponse<>("Error", 500, "Ralat berlaku semasa memadam logo larangan.", null, LocalDateTime.now());
            }
            ForbiddenLogo oldData = forbiddenLogoReadDomainService.findLaranganLogoById(id);
            auditTrailWriteDomainService.createV2(oldData, null, AuditActionType.LARANGAN_LOGO_DELETE.getActionType());
            return new ApiResponse<>("Larangan logo deleted successfully.", HttpStatus.OK.value(), "Success", id, LocalDateTime.now());
        } catch (DataAccessException e) {
            Throwable rootCause = e.getRootCause();
            String rootCauseMessage = (rootCause != null) ? rootCause.getMessage() : "No additional information available.";
            return new ApiResponse<>("Error", 500, "Ralat pangkalan data berlaku semasa memadam larangan logo: " + rootCauseMessage, null, LocalDateTime.now());
        } catch (NullPointerException e) {
            return new ApiResponse<>("Error", 500, "Data null ditemui semasa memadam larangan logo.", null, LocalDateTime.now());
        } catch (Exception e) {
            return new ApiResponse<>("Error", 500, "Ralat tidak dijangka berlaku semasa memadam larangan logo: " + e.getMessage(), null, LocalDateTime.now());
        }
    }
}