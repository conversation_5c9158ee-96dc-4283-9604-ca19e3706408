package com.eroses.external.society.mappers.grant;

import com.eroses.external.society.model.grant.GrantReport;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class GrantReportDao extends MyBatisDao<GrantReport> {
    
    public List<GrantReport> findAll(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAll"), params);
    }
    
    public Long countAll(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAll"), params);
    }
    
    public GrantReport findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }
    
    public GrantReport findByGrantApplicationId(Long grantApplicationId) {
        return this.sqlSession.selectOne(this.sqlId("findByGrantApplicationId"), grantApplicationId);
    }
    
    public List<GrantReport> findByStatus(String status) {
        return this.sqlSession.selectList(this.sqlId("findByStatus"), status);
    }
    
    public List<GrantReport> findPublicReports() {
        return this.sqlSession.selectList(this.sqlId("findPublicReports"));
    }
}
