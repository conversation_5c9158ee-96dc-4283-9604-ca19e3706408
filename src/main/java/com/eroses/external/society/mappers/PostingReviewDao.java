package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.posting.PostingReviewResponse;
import com.eroses.external.society.model.posting.PostingReview;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class PostingReviewDao extends MyBatisDao<PostingReview> {
    
    public List<PostingReviewResponse> findByPostingId(Long postingId) {
        return this.sqlSession.selectList(this.sqlId("findByPostingId"), postingId);
    }
    
    public List<PostingReview> findByReviewerId(Long reviewerId) {
        return this.sqlSession.selectList(this.sqlId("findByReviewerId"), reviewerId);
    }
    
    public PostingReview findByReviewerIdAndPostingId(Long reviewerId, Long postingId) {
        Map<String, Object> params = Map.of("reviewerId", reviewerId, "postingId", postingId);
        return this.sqlSession.selectOne(this.sqlId("findByReviewerIdAndPostingId"), params);
    }
    
    public Long countByPostingIdAndReviewStatus(Long postingId, String reviewStatus) {
        Map<String, Object> params = Map.of("postingId", postingId, "reviewStatus", reviewStatus);
        return this.sqlSession.selectOne(this.sqlId("countByPostingIdAndReviewStatus"), params);
    }
}
