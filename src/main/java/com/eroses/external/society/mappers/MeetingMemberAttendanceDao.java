package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.meeting.BranchMeetingResponse;
import com.eroses.external.society.dto.response.meeting.MeetingMemberAttendanceResponse;
import com.eroses.external.society.model.Meeting;
import com.eroses.external.society.model.MeetingMemberAttendance;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class MeetingMemberAttendanceDao extends MyBatisDao<MeetingMemberAttendance> {
    //Boolean create(MeetingMemberAttendance meetingMemberAttendance);
    public Boolean createMany(List<MeetingMemberAttendance> meetingMemberAttendances){
        return this.sqlSession.insert(this.sqlId("createMany"), meetingMemberAttendances) == meetingMemberAttendances.size();
    }
//    Boolean update(MeetingMemberAttendance meetingMemberAttendance);
    public void delete(Map<String, Object> params){
        this.sqlSession.delete(this.sqlId("delete"), params);
    }
    public List<MeetingMemberAttendance> getAllByMeetingId(Long meetingId){
        return this.sqlSession.selectList(this.sqlId("getAllByMeetingId"), meetingId);
    }
    public List<MeetingMemberAttendanceResponse> findByMeetingId(Long meetingId){
        return this.sqlSession.selectList(this.sqlId("findByMeetingId"), meetingId);
    }
}
