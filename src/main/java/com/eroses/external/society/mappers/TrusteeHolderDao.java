package com.eroses.external.society.mappers;

import com.eroses.external.society.model.TrusteeHolder;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class TrusteeHolderDao extends MyBatisDao<TrusteeHolder> {
//    Long create(TrusteeHolder trusteeHolder);

    public List<TrusteeHolder> findBySocietyId(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findBySocietyId"), params);
    }

    public Long countBySocietyId(Map<String,Object> params){
        return this.sqlSession.selectOne(this.sqlId("countBySocietyId"), params);
    }

//    boolean update(TrusteeHolder trusteeHolder);

//    TrusteeHolder findById(Long id);

    public boolean existsBySocietyIdAndIdentificationNumber(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("existsBySocietyIdAndIdentificationNumber"), params);
    }

    public List<TrusteeHolder> findActiveTrusteesByParam(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findActiveTrusteesByParam"), params);
    }
}
