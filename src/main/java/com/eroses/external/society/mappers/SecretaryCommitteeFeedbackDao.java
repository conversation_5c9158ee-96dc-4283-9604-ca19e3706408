package com.eroses.external.society.mappers;

import com.eroses.external.society.model.SecretaryCommitteeFeedback;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.List;

@Repository
@Slf4j
public class SecretaryCommitteeFeedbackDao extends MyBatisDao<SecretaryCommitteeFeedback> {
//    Boolean create(SecretaryCommitteeFeedback secretaryCommitteeFeedback) throws SQLIntegrityConstraintViolationException;
    public List<SecretaryCommitteeFeedback> getAll(){
        return this.sqlSession.selectList(this.sqlId("getAll"));
    }
}
