package com.eroses.external.society.mappers.grant;

import com.eroses.external.society.model.grant.GrantReportAttachment;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class GrantReportAttachmentDao extends MyBatisDao<GrantReportAttachment> {
    
    public List<GrantReportAttachment> findByGrantReportId(Long grantReportId) {
        return this.sqlSession.selectList(this.sqlId("findByGrantReportId"), grantReportId);
    }
    
    public GrantReportAttachment findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }
    
    public void deleteByGrantReportId(Long grantReportId) {
        this.sqlSession.delete(this.sqlId("deleteByGrantReportId"), grantReportId);
    }
}
