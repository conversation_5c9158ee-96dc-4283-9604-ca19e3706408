package com.eroses.external.society.mappers;

import com.eroses.external.society.model.lookup.AdmOccupation;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@Repository
public class AdmOccupationDao extends MyBatisDao<AdmOccupation> {

    public List<AdmOccupation> getAll() {
        return this.sqlSession.selectList(this.sqlId("getAll"));
    }

    public List<AdmOccupation> getAllActive() {
        return this.sqlSession.selectList(this.sqlId("getAllActive"));
    }

    public List<AdmOccupation> getAll(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAll"), params);
    }

    public Long countAll(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAll"), params);
    }

    @Override
    public Boolean delete(Long id) {
        return this.sqlSession.update(this.sqlId("delete"), id) == 1;
    }

    public boolean existsByCode(String code) {
        return this.sqlSession.selectOne(this.sqlId("existsByCode"), code);
    }

    public boolean existsByCodeExcludingId(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsByCodeExcludingId"), params);
    }
}
