package com.eroses.external.society.mappers;

import com.eroses.external.society.model.PrincipalSecretary;
import com.eroses.external.society.model.TrainingCourse;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class TrainingCourseDao extends MyBatisDao<TrainingCourse> {

//    public boolean create(TrainingCourse trainingCourse) {
//        return this.sqlSession.insert(this.sqlId("create"), trainingCourse) == 1;
//    }
//
//    public boolean update(TrainingCourse trainingCourse) {
//        return this.sqlSession.update(this.sqlId("update"), trainingCourse) == 1;
//    }

    public TrainingCourse findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }

    public TrainingCourse findByIdWithMaterials(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findByIdWithMaterials"), id);
    }

    public List<TrainingCourse> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

    public List<TrainingCourse> findAllPublished() {
        return this.sqlSession.selectList(this.sqlId("findAllPublished"));
    }

    public List<TrainingCourse> findAllByStatus(Integer status) {
        return this.sqlSession.selectList(this.sqlId("findAllByStatus"), status);
    }

    public List<TrainingCourse> findAllByCreator(Long createdBy) {
        return this.sqlSession.selectList(this.sqlId("findAllByCreator"), createdBy);
    }

    public boolean updateStatus(Long id, Integer status, Long modifiedBy) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("status", status);
        params.put("modifiedBy", modifiedBy);
        return this.sqlSession.update(this.sqlId("updateStatus"), params) == 1;
    }
}
