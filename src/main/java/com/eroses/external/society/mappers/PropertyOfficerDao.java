package com.eroses.external.society.mappers;

import com.eroses.external.society.model.PropertyOfficer;
import com.eroses.external.society.model.PropertyOfficerApplication;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class PropertyOfficerDao extends MyBatisDao<PropertyOfficer> {

    public PropertyOfficerApplication findApplicationById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findApplicationById"), id);
    }

//    boolean create(PropertyOfficer propertyOfficer);

    public List<PropertyOfficerApplication> getAllSocietyPending(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllSocietyPending"), params);
    }

    public List<PropertyOfficerApplication> getAllBranchPending(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllBranchPending"), params);
    }

    public Long countAllSocietyPending(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllSocietyPending"), params);
    }

    public Long countAllBranchPending(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllBranchPending"), params);
    }

    public PropertyOfficerApplication findByPaymentId(Long paymentId) {
        return this.sqlSession.selectOne(this.sqlId("findByPaymentId"), paymentId);
    }

    public boolean updatePropertyOfficer(PropertyOfficer propertyOfficer) {
        return this.sqlSession.update(this.sqlId("updatePropertyOfficer"), propertyOfficer) == 1;
    }

    public boolean updatePropertyOfficerApplication(PropertyOfficerApplication propertyOfficerApplication) {
        return this.sqlSession.update(this.sqlId("updatePropertyOfficerApplication"), propertyOfficerApplication) == 1;
    }

    public boolean createPropertyOfficerApplication(PropertyOfficerApplication propertyOfficerApplication) {
        return this.sqlSession.insert(this.sqlId("createPropertyOfficerApplication"), propertyOfficerApplication) == 1;
    }

    public boolean createPropertyOfficers(List<PropertyOfficer> propertyOfficers) {
        return this.sqlSession.insert(this.sqlId("createPropertyOfficers"), propertyOfficers) == propertyOfficers.size();
    }

    public PropertyOfficerApplication findApplicationByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findApplicationByCriteria"), params);
    }

    public List<PropertyOfficerApplication> findAllApplicationByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAllApplicationByCriteria"), params);
    }

    public Long countAllApplicationByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllApplicationByCriteria"), params);
    }

    public Long countPropertyOfficer(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countPropertyOfficer"), params);
    }

    public List<PropertyOfficer> findAllPropertyOfficer(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAllPropertyOfficer"), params);
    }

    public List<PropertyOfficer> findAllActivePropertyOfficers(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAllActivePropertyOfficers"), params);
    }

    public List<PropertyOfficer> findOfficerByApplicationId(Long propertyOfficerApplicationId) {
        return this.sqlSession.selectList(this.sqlId("findOfficerByApplicationId"), propertyOfficerApplicationId);
    }

    public PropertyOfficerApplication findLatestPropertyOfficerApplication(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findLatestPropertyOfficerApplication"), params);
    }

    public int removePropertyOfficers(Long propertyOfficerApplicationId) {
        return this.sqlSession.update(this.sqlId("removePropertyOfficers"), propertyOfficerApplicationId);
    }

    public List<PropertyOfficerApplication> getAllPropertyOfficerApplicationPendingApproval(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllPropertyOfficerApplicationPendingApproval"), params);
    }

    public boolean existsOnGoingSocietyPropertyOfficerApplication(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsOnGoingSocietyPropertyOfficerApplication"), params);
    }

    public boolean existsOnGoingBranchPropertyOfficerApplication(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsOnGoingBranchPropertyOfficerApplication"), params);
    }
}
