package com.eroses.external.society.mappers.blacklist;

import com.eroses.external.society.model.blacklist.Whitelist;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class WhitelistDao extends MyBatisDao<Whitelist> {
    
    public List<Whitelist> findByBlacklistId(Long blacklistId) {
        return this.sqlSession.selectList(this.sqlId("findByBlacklistId"), blacklistId);
    }
    
    public Whitelist findByReferenceNo(String referenceNo) {
        return this.sqlSession.selectOne(this.sqlId("findByReferenceNo"), referenceNo);
    }
    
    public List<Whitelist> search(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("search"), params);
    }
    
    public Long countSearch(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countSearch"), params);
    }

    public List<Long> getAllPendingToProcessWhitelistId(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllPendingToProcessWhitelistId"), params);
    }
}