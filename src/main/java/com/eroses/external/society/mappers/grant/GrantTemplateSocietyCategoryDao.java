package com.eroses.external.society.mappers.grant;

import com.eroses.external.society.model.grant.GrantTemplateSocietyCategory;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class GrantTemplateSocietyCategoryDao extends MyBatisDao<GrantTemplateSocietyCategory> {
    
    public List<GrantTemplateSocietyCategory> findByGrantTemplateId(Long grantTemplateId) {
        return this.sqlSession.selectList(this.sqlId("findByGrantTemplateId"), grantTemplateId);
    }
    
    public void deleteByGrantTemplateId(Long grantTemplateId) {
        this.sqlSession.delete(this.sqlId("deleteByGrantTemplateId"), grantTemplateId);
    }
    
    public boolean existsBySocietyCategoryId(Long societyCategoryId) {
        return this.sqlSession.selectOne(this.sqlId("existsBySocietyCategoryId"), societyCategoryId);
    }
}
