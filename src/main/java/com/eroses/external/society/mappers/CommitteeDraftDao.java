package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.committee.CommitteeListAjkResponse;
import com.eroses.external.society.model.CommitteeDraft;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class CommitteeDraftDao extends MyBatisDao<CommitteeDraft> {
    public List<CommitteeListAjkResponse> findAllByParams(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAllByParams"), params);
    }

    public Long countAllByParams(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllByParams"), params);
    }

    public List<CommitteeDraft> findByParam(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByParam"), params);
    }

    public CommitteeDraft findOneByParams(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findOneByParams"), params);
    }

    public Long countByParam(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countByParam"), params);
    }
}
