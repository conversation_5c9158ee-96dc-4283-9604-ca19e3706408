package com.eroses.external.society.mappers;

import com.eroses.external.society.model.CommitteeTask;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class CommitteeTaskDao extends MyBatisDao<CommitteeTask> {
    // Old methods, now commented out
    // void create(CommitteeTask committeeTask);
    // List<CommitteeTask> listCommitteeTask(Map<String, Object> params);
    // Long countListCommitteeTask(Map<String, Object> params);
    // CommitteeTask findById(Long id);
    // boolean update(CommitteeTask committeeTask);
    // CommitteeTask findByStatementId(Long statementId);

//    public void create(CommitteeTask committeeTask) {
//        this.sqlSession.insert(this.sqlId("create"), committeeTask);
//    }

    public List<CommitteeTask> listCommitteeTask(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("listCommitteeTask"), params);
    }

    public Long countListCommitteeTask(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countListCommitteeTask"), params);
    }

    public boolean updateStatus(CommitteeTask committeeTask) {
        return this.sqlSession.update(this.sqlId("updateStatus"), committeeTask) > 0;
    }

//    public CommitteeTask findById(Long id) {
//        return this.sqlSession.selectOne(this.sqlId("findById"), id);
//    }

//    public boolean update(CommitteeTask committeeTask) {
//        return this.sqlSession.update(this.sqlId("update"), committeeTask) > 0;
//    }

    public CommitteeTask findByStatementId(Long statementId) {
        return this.sqlSession.selectOne(this.sqlId("findByStatementId"), statementId);
    }

    public CommitteeTask findOneByCriteria(Map<String,Object> params){
        return this.sqlSession.selectOne(this.sqlId("findByCriteria"),params);
    }
}
