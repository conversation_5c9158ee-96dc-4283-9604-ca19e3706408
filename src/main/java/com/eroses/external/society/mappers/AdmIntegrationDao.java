package com.eroses.external.society.mappers;

import com.eroses.external.society.model.AuditTrail;
import com.eroses.external.society.model.Integration;
import com.eroses.mysql.dao.MyBatisDao;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AdmIntegrationDao extends MyBatisDao<Integration> {

    // Old method, now commented out
    // List<Integration> findAll();
    public List<Integration> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

    // Old method, now commented out
    // Long update(Integration integration);
//    public Long update(Integration integration) {
//        return (long) this.sqlSession.update(this.sqlId("update"), integration) == 1;
//    }
}

