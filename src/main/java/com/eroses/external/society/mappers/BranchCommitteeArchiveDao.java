package com.eroses.external.society.mappers;

import com.eroses.external.society.model.BranchCommitteeArchive;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class BranchCommitteeArchiveDao extends MyBatisDao<BranchCommitteeArchive> {

    public List<BranchCommitteeArchive> getBranchCommitteeArchive(Map<String, Object> param) {
        return this.sqlSession.selectList(this.sqlId("getBranchCommitteeArchive"), param);
    }

    public Long countBranchCommitteeArchive(Map<String, Object> param) {
        return this.sqlSession.selectOne(this.sqlId("countBranchCommitteeArchive"), param);
    }

    public List<BranchCommitteeArchive> findByMeetingDate(LocalDate meetingDate) {
        return this.sqlSession.selectList(this.sqlId("findByMeetingDate"), meetingDate);
    }

    public BranchCommitteeArchive findByCommitteeOldId(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findByCommitteeOldId"), id);
    }

    public BranchCommitteeArchive findOneByParams(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findOneByParams"), params);
    }

    public List<LocalDate> findAllAppointedDates(Long branchId) {
        return this.sqlSession.selectList(this.sqlId("findAllAppointedDates"), branchId);
    }

    public List<BranchCommitteeArchive> findByParam(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByParam"), params);
    }

    public List<BranchCommitteeArchive> findActiveCommitteesInSocietyArchiveWithRoles(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findActiveCommitteesInBranchArchiveWithRoles"), params);
    }
}
