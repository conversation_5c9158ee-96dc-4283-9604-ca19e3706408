package com.eroses.external.society.mappers;

import com.eroses.external.society.model.Feedback;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class FeedbackDao extends MyBatisDao<Feedback> {
//    void create(Feedback feedback);
//    int update(Feedback feedback);

//    List<Feedback> findAllByParams(Map<String, Object> params);
    public List<Feedback> findAllByParams(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAllByParams"), params);
    }
//    List<Feedback> findAllExcludeSatisfactory(Map<String, Object> params);
    public List<Feedback> findAllExcludeSatisfactory(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAllExcludeSatisfactory"), params);
    }
//    List<Feedback> findOnlySatisfactory(Map<String, Object> params);
    public List<Feedback> findOnlySatisfactory(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findOnlySatisfactory"), params);
    }

    public Long countFindOnlySatisfactory(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countFindOnlySatisfactory"), params);
    }
//    int countMemuaskan(String satisfaction);
    public int countMemuaskan(String satisfaction){
        return this.sqlSession.selectOne(this.sqlId("countMemuaskan"), satisfaction);
    }
//    int countTidakMemuaskan(String satisfaction);
    public int countSangatMemuaskan(String satisfaction){
        return this.sqlSession.selectOne(this.sqlId("countSangatMemuaskan"), satisfaction);
    }
//    int countTidakMemuaskan(String satisfaction);
    public int countTidakMemuaskan(String satisfaction){
        return this.sqlSession.selectOne(this.sqlId("countTidakMemuaskan"), satisfaction);
    }

    public Long countAllExcludeSatisfactory(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllExcludeSatisfactory"), params);
    }
}
