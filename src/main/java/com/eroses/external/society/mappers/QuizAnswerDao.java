package com.eroses.external.society.mappers;

import com.eroses.external.society.model.QuizAnswer;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class QuizAnswerDao extends MyBatisDao<QuizAnswer> {

//    public boolean create(QuizAnswer quizAnswer) {
//        return this.sqlSession.insert(this.sqlId("create"), quizAnswer) == 1;
//    }

    public boolean createBatch(List<QuizAnswer> quizAnswers) {
        Map<String, Object> params = new HashMap<>();
        params.put("answers", quizAnswers);
        return this.sqlSession.insert(this.sqlId("createBatch"), params) > 0;
    }

    public List<QuizAnswer> findAllByAttemptId(Long quizAttemptId) {
        return this.sqlSession.selectList(this.sqlId("findAllByAttemptId"), quizAttemptId);
    }

    public QuizAnswer findByAttemptIdAndQuestionId(Long quizAttemptId, Long quizQuestionId) {
        Map<String, Object> params = new HashMap<>();
        params.put("quizAttemptId", quizAttemptId);
        params.put("quizQuestionId", quizQuestionId);
        return this.sqlSession.selectOne(this.sqlId("findByAttemptIdAndQuestionId"), params);
    }

    public int countCorrectAnswersByAttemptId(Long quizAttemptId) {
        return this.sqlSession.selectOne(this.sqlId("countCorrectAnswersByAttemptId"), quizAttemptId);
    }

    public boolean deleteAllByAttemptId(Long quizAttemptId) {
        return this.sqlSession.delete(this.sqlId("deleteAllByAttemptId"), quizAttemptId) >= 0;
    }
}
