package com.eroses.external.society.mappers;

import com.eroses.external.society.model.BranchCommitteeTaskConfig;
import com.eroses.external.society.model.enums.CommitteeTaskModule;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public class BranchCommitteeTaskConfigDao extends MyBatisDao<BranchCommitteeTaskConfig> {

    public int enableCommitteeTaskForModule(Long branchId, CommitteeTaskModule module) {
        return this.sqlSession.insert(this.sqlId("insertCommitteeTaskForModule"), Map.of("branchId", branchId, "module", module.getCode()));
    }

    public int disableCommitteeTaskForModule(Long branchId, CommitteeTaskModule module) {
        return this.sqlSession.delete(this.sqlId("removeCommitteeTaskForModule"), Map.of("branchId", branchId, "module", module.getCode()));
    }

    public boolean isCommitteeTaskEnabled(Long branchId, CommitteeTaskModule module) {
        return this.sqlSession.selectOne(this.sqlId("findCommitteeTaskForModule"), Map.of("branchId", branchId, "module", module.getCode())) != null;
    }
}
