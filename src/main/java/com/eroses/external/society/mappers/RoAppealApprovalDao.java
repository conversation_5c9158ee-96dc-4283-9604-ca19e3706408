package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.request.GetRoAppealRequest;
import com.eroses.external.society.model.RoAppealApproval;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class RoAppealApprovalDao extends MyBatisDao<RoAppealApproval> {
    public List<RoAppealApproval> getQueryHistory(Map<String, Object> params){

        return this.sqlSession.selectList(this.sqlId("getQueryHistory"), params);
    }
//    Long create(RoAppealApproval roAppealApproval);
//    Long update(RoAppealApproval roAppealApproval);

    public List<RoAppealApproval> getRoAppeal(GetRoAppealRequest getRoAppealRequest){

        return this.sqlSession.selectList(this.sqlId("getRoAppeal"), getRoAppealRequest);
    }

}
