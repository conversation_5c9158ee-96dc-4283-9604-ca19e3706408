package com.eroses.external.society.mappers;

import com.eroses.external.society.model.SearchInformation;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class SearchInformationDao extends MyBatisDao<SearchInformation> {

    public List<SearchInformation> getAll(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("getAll"), params);
    }

    public Long countGetAll(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countGetAll"), params);
    }

    public int delete(SearchInformation searchInformation){
        return this.sqlSession.update(this.sqlId("delete"), searchInformation);
    }

    public SearchInformation getByPaymentId(Long paymentId){
        return this.sqlSession.selectOne(this.sqlId("getByPaymentId"), paymentId);
    }
}