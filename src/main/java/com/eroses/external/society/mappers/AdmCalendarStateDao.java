package com.eroses.external.society.mappers;

import com.eroses.external.society.model.lookup.AdmCalendarState;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AdmCalendarStateDao extends MyBatisDao<AdmCalendarState> {

    public List<AdmCalendarState> findByCalendarId(Long calendarId) {
        return this.sqlSession.selectList(this.sqlId("findByCalendarId"), calendarId);
    }

    public List<Long> findStateIdsByCalendarId(Long calendarId) {
        return this.sqlSession.selectList(this.sqlId("findStateIdsByCalendarId"), calendarId);
    }

    public void deleteByCalendarId(Long calendarId) {
        this.sqlSession.delete(this.sqlId("deleteByCalendarId"), calendarId);
    }
}