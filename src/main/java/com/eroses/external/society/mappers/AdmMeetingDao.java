package com.eroses.external.society.mappers;
import java.util.List;

import com.eroses.external.society.model.AdmMeeting;
import com.eroses.mysql.dao.MyBatisDao;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

@Repository
public class AdmMeetingDao extends MyBatisDao<AdmMeeting> {

    // Old method, now commented out
    // List<AdmMeeting> findAll();
    public List<AdmMeeting> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }
}


