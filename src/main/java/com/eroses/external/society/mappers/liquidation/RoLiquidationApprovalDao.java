package com.eroses.external.society.mappers.liquidation;

import com.eroses.external.society.dto.response.societyLiquidation.LiquidationApprovalResponse;
import com.eroses.external.society.dto.response.societyLiquidation.LiquidationInternalResponse;
import com.eroses.external.society.dto.response.societyLiquidation.LiquidationPagingInternalResponse;
import com.eroses.external.society.model.societyLiquidation.RoLiquidationApproval;
import com.eroses.mysql.dao.MyBatisDao;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class RoLiquidationApprovalDao extends MyBatisDao<RoLiquidationApproval> {
//    void insert(RoLiquidationApproval roLiquidationApproval);
//
//    void update(RoLiquidationApproval roLiquidationApproval);

    // Old method, now commented out
    // Long getTotalCount(Map<String, Object> params);
    public Long getTotalCount(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("getTotalCount"), params);
    }

    // Old method, now commented out
    // LiquidationInternalResponse findLiquidationById(Long liquidationId);
    public LiquidationInternalResponse findLiquidationById(Long liquidationId) {
        return this.sqlSession.selectOne(this.sqlId("findLiquidationById"), liquidationId);
    }

    // Old method, now commented out
    // List<LiquidationApprovalResponse> findApprovalById(Long liquidationId);
    public List<LiquidationApprovalResponse> findApprovalById(Long liquidationId) {
        return this.sqlSession.selectList(this.sqlId("findApprovalById"), liquidationId);
    }

    // Old method, now commented out
    // List<LiquidationPagingInternalResponse> getPaging(Map<String, Object> params);
    public List<LiquidationPagingInternalResponse> getPaging(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getPaging"), params);
    }
}
