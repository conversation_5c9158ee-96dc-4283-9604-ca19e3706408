package com.eroses.external.society.mappers;

import com.eroses.external.society.model.ExternalAgencyReview;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class ExternalAgencyReviewDao extends MyBatisDao<ExternalAgencyReview> {

    public List<ExternalAgencyReview> getAllBySocietyId(Long societyId){
        return this.sqlSession.selectList(this.sqlId("getAllBySocietyId"), societyId);
    }

    public ExternalAgencyReview getLatestBySocietyId(Long societyId){
        return this.sqlSession.selectOne(this.sqlId("getLatestBySocietyId"), societyId);
    }
}
