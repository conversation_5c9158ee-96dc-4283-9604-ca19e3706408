package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.branchSecretary.BranchSecretaryHistoryDetailsResponse;
import com.eroses.external.society.dto.response.branchSecretary.BranchSecretarySlipResponse;
import com.eroses.external.society.model.BranchSecretary;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Map;

@Repository
public class BranchSecretaryDao extends MyBatisDao<BranchSecretary> {

    public BranchSecretaryHistoryDetailsResponse getNewBranchSecretary(Long newSecretaryBranchId) {
        return this.sqlSession.selectOne(this.sqlId("getNewBranchSecretary"), newSecretaryBranchId);
    }

    public List<BranchSecretary> getAll() {
        return this.sqlSession.selectList(this.sqlId("getAll"));
    }

    public List<BranchSecretary> getAllByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllByCriteria"), params);
    }

    public Long countAllByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllByCriteria"), params);
    }

    public List<BranchSecretary> search(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("search"), params);
    }

    public List<BranchSecretary> findBySocietyId(Long societyId) {
        return this.sqlSession.selectList(this.sqlId("findBySocietyId"), societyId);
    }

    public BranchSecretarySlipResponse getSlipData(Long newSecretaryBranchId) {
        return this.sqlSession.selectOne(this.sqlId("getSlipData"), newSecretaryBranchId);
    }
}

