package com.eroses.external.society.mappers;

import com.eroses.external.society.model.Dashboard;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class DashboardDao extends MyBatisDao<Dashboard> {
    
    /**
     * Find all dashboards with filtering parameters
     * @param params Map containing filter parameters (type, module, category)
     * @return List of Dashboard objects
     */
    public List<Dashboard> findAllByParams(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAllByParams"), params);
    }
    
    /**
     * Find dashboard by ID
     * @param id Dashboard ID
     * @return Dashboard object
     */
    public Dashboard findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }
    
    /**
     * Find dashboard by code
     * @param code Dashboard code
     * @return Dashboard object
     */
    public Dashboard findByCode(String code) {
        return this.sqlSession.selectOne(this.sqlId("findByCode"), code);
    }
}
