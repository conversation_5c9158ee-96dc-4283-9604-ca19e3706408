package com.eroses.external.society.mappers;

import com.eroses.external.society.model.payment.PaymentRecord;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class PaymentRecordDao extends MyBatisDao<PaymentRecord> {
    public List<PaymentRecord> getAllPaymentRecordsByCriteria(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("getAllPaymentRecordsByCriteria"), params);
    }
    public Long countGetAllPaymentRecordsByCriteria(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countGetAllPaymentRecordsByCriteria"), params);
    }
    public void save(PaymentRecord paymentRecord){
        this.sqlSession.insert(this.sqlId("save"), paymentRecord);
    }
//    void update(PaymentRecord paymentRecord);
    public PaymentRecord getLatestPaymentRecordByDocCodeAndReferenceNo(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("getLatestPaymentRecordByDocCodeAndReferenceNo"), params);
    }
    public PaymentRecord selectById(long id){
        return this.sqlSession.selectOne(this.sqlId("selectById"), id);
    }
    public Long count(){
        return this.sqlSession.selectOne(this.sqlId("count"));
    }
}
