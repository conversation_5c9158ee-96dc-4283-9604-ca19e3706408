package com.eroses.external.society.mappers.grant;

import com.eroses.external.society.model.grant.GrantTemplateField;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class GrantTemplateFieldDao extends MyBatisDao<GrantTemplateField> {
    
    public List<GrantTemplateField> findByGrantTemplateId(Long grantTemplateId) {
        return this.sqlSession.selectList(this.sqlId("findByGrantTemplateId"), grantTemplateId);
    }
    
    public GrantTemplateField findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }
    
    public void deleteByGrantTemplateId(Long grantTemplateId) {
        this.sqlSession.delete(this.sqlId("deleteByGrantTemplateId"), grantTemplateId);
    }
}
