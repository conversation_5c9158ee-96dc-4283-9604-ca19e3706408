package com.eroses.external.society.mappers;

import com.eroses.external.society.model.payment.BankType;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public class BankTypeDao extends MyBatisDao<BankType> {

    // Old methods, now commented out
    // List<BankType> findAll();
    // BankType findById(long id);
    // BankType findByBankCode(String bankCode);
    // void save(BankType bankType);
    // void update(BankType bankType);

    public List<BankType> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

//    public BankType findById(long id) {
//        return this.sqlSession.selectOne(this.sqlId("findById"), id);
//    }

    public BankType findByBankCode(String bankCode) {
        return this.sqlSession.selectOne(this.sqlId("findByBankCode"), bankCode);
    }

    public void save(BankType bankType) {
        this.sqlSession.insert(this.sqlId("save"), bankType);
    }

//    public void update(BankType bankType) {
//        this.sqlSession.update(this.sqlId("update"), bankType);
//    }
}

