package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.appeal.GetUserSocietiesForAppealResponse;
import com.eroses.external.society.dto.response.roDecision.GetAllDecisionRecordAmendmentResponse;
import com.eroses.external.society.dto.response.GetRoAmendmentResponse;
import com.eroses.external.society.dto.response.roDecision.GetAllPendingAmendmentResponse;
import com.eroses.external.society.model.Amendment;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class AmendmentDao extends MyBatisDao<Amendment> {

//    public Boolean create(Amendment amendment) {
//        return this.sqlSession.insert(this.sqlId("create"), amendment) > 0;
//    }
//
//    public Boolean update(Amendment amendment) {
//        return this.sqlSession.update(this.sqlId("update"), amendment) > 0;
//    }
//
//    public Amendment findById(Long id) {
//        return this.sqlSession.selectOne(this.sqlId("findById"), id);
//    }
//
    public List<Amendment> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

    public List<Amendment> searchAmendments(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("searchAmendments"), params);
    }

    public long countSearchedAmendments(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countSearchedAmendments"), params);
    }

    //commented because not being used
//    public List<Amendment> findByUserInvolved(Map<String, Object> params, int offset, int limit) {
//        params.put("offset", offset);
//        params.put("limit", limit);
//        return this.sqlSession.selectList(this.sqlId("findByUserInvolved"), params);
//    }
//
//    public List<Amendment> findByUserCreated(Map<String, Object> params, int offset, int limit) {
//        params.put("offset", offset);
//        params.put("limit", limit);
//        return this.sqlSession.selectList(this.sqlId("findByUserCreated"), params);
//    }


    public List<Amendment> findByParam(Map<String, Object> param) {
        return this.sqlSession.selectList(this.sqlId("findByParam"), param);
    }

    public Amendment getByPaymentId(Long paymentId) {
        return this.sqlSession.selectOne(this.sqlId("getByPaymentId"), paymentId);
    }

    public List<Amendment> findByClauseType(Map<String, Object> param) {
        return this.sqlSession.selectList(this.sqlId("findByClauseType"), param);
    }

    public Long countAmendments(Map<String, Object> param) {
        return this.sqlSession.selectOne(this.sqlId("countAmendments"), param);
    }

    public List<GetRoAmendmentResponse> getRoAmendmentsByUser(Map<String, Object> param) {
        return this.sqlSession.selectList(this.sqlId("getRoAmendmentsByUser"), param);
    }

    public Long countRoAmendmentsByUser(Map<String, Object> param) {
        return this.sqlSession.selectOne(this.sqlId("countRoAmendmentsByUser"), param);
    }

    public List<GetAllPendingAmendmentResponse> getAllPendingAmendments(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllPendingAmendments"), params);
    }

    public Long countAllPending(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllPending"), params);
    }

    public List<GetAllDecisionRecordAmendmentResponse> getAllDecisionRecordAmendment(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllDecisionRecordAmendment"), params);
    }

    public Long countAllDecisionRecordAmendment(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllDecisionRecordAmendment"), params);
    }

    public List<GetUserSocietiesForAppealResponse> findAmendmentForAppeal(Map<String, Object> param) {
        return this.sqlSession.selectList(this.sqlId("findAmendmentForAppeal"), param);
    }

    public List<Amendment> getAllAmendmentPendingApproval(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllAmendmentPendingApproval"), params);
    }

    // Old Interface Methods (Commented Out)

    // public interface AmendmentDao {
    //     Boolean create(Amendment amendment);
    //     Boolean update(Amendment amendment);
    //     Amendment findById(Long id);
    //     List<Amendment> findAll();
    //     List<Amendment> searchAmendments(Map<String, Object> params);
    //     long countSearchedAmendments(Map<String, Object> params);
    //     Page<Amendment> findByUserInvolved(Map<String, Object> params);
    //     Page<Amendment> findByUserCreated(Map<String, Object> params);
    //     List<Amendment> findByParam(Map<String, Object> param);
    //     Amendment getByPaymentId(Long paymentId);
    //     List<Amendment> findByClauseType(Map<String, Object> param);
    //     Long countAmendments(Map<String, Object> param);
    //     List<GetRoAmendmentResponse> getRoAmendmentsByUser(Map<String, Object> param);
    //     Long countRoAmendmentsByUser(Map<String, Object> param);
    //     List<GetAllPendingAmendmentResponse> getAllPendingAmendments(Map<String, Object> params);
    //     Long countAllPending(Map<String, Object> params);
    // }
}

