package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.posting.PostingPagingResponse;
import com.eroses.external.society.model.posting.Posting;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class PostingDao extends MyBatisDao<Posting> {

    public List<PostingPagingResponse> search(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("search"), params);
    }

    public Long findPostingsTotalCount(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findPostingsTotalCount"), params);
    }

    public List<Posting> findRandomPostings(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findRandomPostings"), params);
    }

    public List<Posting> findRecommendedPostings(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findRecommendedPostings"), params);
    }

    public List<Posting> findByCategory(String category) {
        return this.sqlSession.selectList(this.sqlId("findByCategory"), category);
    }

    public List<Posting> findBySubCategory(String subCategory) {
        return this.sqlSession.selectList(this.sqlId("findBySubCategory"), subCategory);
    }

    public List<Posting> findByStatus(String status) {
        return this.sqlSession.selectList(this.sqlId("findByStatus"), status);
    }

    public List<Posting> findExpiredPostings() {
        return this.sqlSession.selectList(this.sqlId("findExpiredPostings"));
    }

    public List<Posting> findPostingsToPublish() {
        return this.sqlSession.selectList(this.sqlId("findPostingsToPublish"));
    }

    public Boolean updateStatus(Long id, String status) {
        Map<String, Object> params = Map.of("id", id, "status", status);
        return this.sqlSession.update(this.sqlId("updateStatus"), params) == 1;
    }

    public List<Posting> getAll() {
        return this.sqlSession.selectList(this.sqlId("getAll"));
    }
}
