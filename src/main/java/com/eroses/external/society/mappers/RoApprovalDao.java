package com.eroses.external.society.mappers;

import com.eroses.external.society.model.RoApproval;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class RoApprovalDao extends MyBatisDao<RoApproval> {
//    Boolean create(RoApproval roApproval);

//    Boolean update(RoApproval roApproval);

//    RoApproval findById(Long id);

    public RoApproval findByBranchId(Long id){
        return this.sqlSession.selectOne(this.sqlId("findByBranchId"), id);
    }

    public List<RoApproval> findAllByCriteria(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAllByCriteria"), params);
    }

    public Long countFindAll(Long societyId){
        return this.sqlSession.selectOne(this.sqlId("countFindAll"), societyId);
    }

    public void createSocietySecretaryApproval(RoApproval roApproval){
        this.sqlSession.insert(this.sqlId("createSocietySecretaryApproval"), roApproval);
    }

    public RoApproval findByParamForAppeal(Map<String, Object> param) {
        return this.sqlSession.selectOne(this.sqlId("findByParamForAppeal"), param);
    }

    public RoApproval findRoApprovalByModuleId(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findRoApprovalByModuleId"), params);
    }
}
