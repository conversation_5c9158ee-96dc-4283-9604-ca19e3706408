package com.eroses.external.society.mappers;

import com.eroses.external.society.model.societyCancellation.SocietyCancellation;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class SocietyCancellationDao extends MyBatisDao<SocietyCancellation> {

    public SocietyCancellation findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }

    public List<SocietyCancellation> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

    public List<SocietyCancellation> findBySocietyId(Long societyId) {
        return this.sqlSession.selectList(this.sqlId("findBySocietyId"), societyId);
    }

    public SocietyCancellation findBySocietyNo(String societyNo) {
        return this.sqlSession.selectOne(this.sqlId("findBySocietyNo"), societyNo);
    }

    public List<SocietyCancellation> findByParam(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByParam"), params);
    }

    public List<SocietyCancellation> findActiveSocietyCancellationsByParam(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findActiveSocietyCancellationsByParam"), params);
    }

    public Long countByParam(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countByParam"), params);
    }

    public Long countActiveSocietyCancellationsByParam(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countActiveSocietyCancellationsByParam"), params);
    }

    public List<Long> getAllPendingToProcessSocietyCancellationId(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllPendingToProcessSocietyCancellationId"), params);
    }
}