package com.eroses.external.society.mappers;

import com.eroses.external.society.model.societyCancellation.SocietyCancellationRevert;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class SocietyCancellationRevertDao extends MyBatisDao<SocietyCancellationRevert> {

    public SocietyCancellationRevert findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }

    public List<SocietyCancellationRevert> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

    public List<SocietyCancellationRevert> findBySocietyId(Long societyId) {
        return this.sqlSession.selectList(this.sqlId("findBySocietyId"), societyId);
    }

    public SocietyCancellationRevert findBySocietyNo(String societyNo) {
        return this.sqlSession.selectOne(this.sqlId("findBySocietyNo"), societyNo);
    }
    
    public List<SocietyCancellationRevert> findBySocietyCancellationId(Long societyCancellationId) {
        return this.sqlSession.selectList(this.sqlId("findBySocietyCancellationId"), societyCancellationId);
    }

    public List<SocietyCancellationRevert> findByParam(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByParam"), params);
    }

    public Long countByParam(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countByParam"), params);
    }

    public List<Long> getAllPendingToProcessSocietyCancellationRevertId(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllPendingToProcessSocietyCancellationRevertId"), params);
    }
}