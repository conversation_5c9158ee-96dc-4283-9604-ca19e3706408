package com.eroses.external.society.mappers;

import com.eroses.external.society.model.ForbiddenKeyword;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class ForbiddenKeywordDao extends MyBatisDao<ForbiddenKeyword> {

    public ForbiddenKeyword createSenarai(ForbiddenKeyword forbiddenKeyword) {
        this.sqlSession.insert(this.sqlId("create"), forbiddenKeyword);
        return forbiddenKeyword;
    }

    public Boolean delete(Long id) {
        return this.sqlSession.delete(this.sqlId("delete"), id) == 1;
    }

    public Boolean update(ForbiddenKeyword forbiddenKeyword) {
        return this.sqlSession.update(this.sqlId("update"), forbiddenKeyword) == 1;
    }

    public List<ForbiddenKeyword> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

    public List<ForbiddenKeyword> findByType(String type) {
        return this.sqlSession.selectList(this.sqlId("findByType"), type);
    }

    public List<ForbiddenKeyword> findByTypePagination(String type, Integer offset, Integer limit) {
        return this.sqlSession.selectList(this.sqlId("findByTypePagination"), Map.of("type", type, "offset", offset, "limit", limit));
    }
    public Long countByForbiddenType(String type) {
        return this.sqlSession.selectOne(this.sqlId("countByForbiddenType"), type);
    }

    public List<String> findOnlyKeywordByType(String type) {
        return this.sqlSession.selectList(this.sqlId("findOnlyKeywordByType"), type);
    }

    public ForbiddenKeyword findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }

    public List<ForbiddenKeyword> checkKeyLarangan(String keyword, String laranganType) {
        Map<String, Object> params = Map.of("keyword", keyword, "type", laranganType);
        return this.sqlSession.selectList(this.sqlId("checkKeyWord"), params);
    }

    public List<ForbiddenKeyword> searchLaranganOrKelabu(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("searchLaranganOrKelabu"), params);
    }

    public Long countSearchedForbiddens(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countSearchedForbiddens"), params);
    }

}
