package com.eroses.external.society.mappers;

import com.eroses.external.society.model.TrainingQuiz;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class TrainingQuizDao extends MyBatisDao<TrainingQuiz> {

//    public boolean create(TrainingQuiz trainingQuiz) {
//        return this.sqlSession.insert(this.sqlId("create"), trainingQuiz) == 1;
//    }
//
//    public boolean update(TrainingQuiz trainingQuiz) {
//        return this.sqlSession.update(this.sqlId("update"), trainingQuiz) == 1;
//    }

    public TrainingQuiz findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }

    public TrainingQuiz findByCourseId(Long trainingCourseId) {
        return this.sqlSession.selectOne(this.sqlId("findByCourseId"), trainingCourseId);
    }
//
//    public boolean delete(Long id) {
//        return this.sqlSession.delete(this.sqlId("delete"), id) == 1;
//    }
}
