package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.appeal.GetUserSocietiesForAppealResponse;
import com.eroses.external.society.dto.response.society.SocietyBasicResponse;
import com.eroses.external.society.dto.response.society.SocietyGetOneResponse;
import com.eroses.external.society.model.Society;
import com.eroses.external.society.model.enums.Position;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
@Slf4j
public class SocietyDao extends MyBatisDao<Society> {

    public SocietyBasicResponse findBasicById(Long id){
        return this.sqlSession.selectOne(this.sqlId("findBasicById"), id);
    }

    public Society findSocietyNo(Long id){
        return this.sqlSession.selectOne(this.sqlId("findSocietyNo"), id);
    }

    public Society findBySocietyNo(String societyNo){
        return this.sqlSession.selectOne(this.sqlId("findBySocietyNo"), societyNo);
    }

    public Society findByApplicationNo(String applicationNo){
        return this.sqlSession.selectOne(this.sqlId("findByApplicationNo"), applicationNo);
    }

    public Society findByPaymentId(Long paymentId){
        return this.sqlSession.selectOne(this.sqlId("findByPaymentId"), paymentId);
    }

    public List<Society> findAll(){
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

    public List<Society> searchSocieties(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("searchSocieties"), params);
    }

    public List<Society> getAllCurrentUserSocietyBranchList(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("getAllCurrentUserSocietyBranchList"), params);
    }

    public Long countAllCurrentUserSocietyBranchList(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllCurrentUserSocietyBranchList"), params);
    }

    public Long countSearchedSocieties(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countSearchedSocieties"), params);
    }

    public Long countSearchedSocietiesByCriteria(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countSearchedSocietiesByCriteria"), params);
    }

    public List<Society> findAllPending(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAllPending"), params);
    }

    public long countFindAllPending(Integer applicationStatusCode){
        return this.sqlSession.selectOne(this.sqlId("countFindAllPending"), applicationStatusCode);
    }

    public List<Society> getAllPendingExternalAgencyReviewByCriteria(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("getAllPendingExternalAgencyReviewByCriteria"), params);
    }

    public Integer countAllPendingExternalAgencyReviewByCriteria(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllPendingExternalAgencyReviewByCriteria"), params);
    }

    public Society findPendingById(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("findPendingById"), params);
    }

    public int countRegisteredToday(){
        return this.sqlSession.selectOne(this.sqlId("countRegisteredToday"));
    }

    public int countApprovedToday(){
        return this.sqlSession.selectOne(this.sqlId("countApprovedToday"));
    }

    public List<Society> searchSocietiesByCriteria(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("searchSocietiesByCriteria"), params);
    }

    public List<Society> findAllByParam(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAllByParam"), params);
    }

    public Long countFindAllByParam(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countFindAllByParam"), params);
    }

    public List<Society> findAllPendingUserStateCode(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAllPendingUserStateCode"), params);
    }

    public long countFindAllPendingUserStateCode(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countFindAllPendingUserStateCode"), params);
    }

    public List<Long> findSocietyIdListByIdentificationNo(String identificationNo){
            return this.sqlSession.selectList(this.sqlId("findSocietyIdListByIdentificationNo"), identificationNo);
    }

    public List<Long> findSocietyIdListByIdentificationNo(String identificationNo, boolean checkSecretaryOwnershipRule){
        Map<String, Object> params = Map.of(
                "identificationNo", identificationNo,
                "checkSecretaryOwnershipRule", checkSecretaryOwnershipRule,
                "secretaryPositionCodes", Position.getAllSecretaryPositions().stream()
                        .map(Position::getCode)
                        .map(String::valueOf)
                        .toList()
        );
        return this.sqlSession.selectList(this.sqlId("findSocietyIdListByIdentificationNoWithSecretaryCheck"), params);
    }

    public int countBySocietyName(String societyName){
        return this.sqlSession.selectOne(this.sqlId("countBySocietyName"), societyName);
    }

    public int countStatusCode(String statusCode){
        return this.sqlSession.selectOne(this.sqlId("countStatusCode"), statusCode);
    }

    public int countApplicationStatusCode(int applicationStatusCode){
        return this.sqlSession.selectOne(this.sqlId("countApplicationStatusCode"), applicationStatusCode);
    }

    public int countApplicationStatusCodeAndPaymentDate(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countApplicationStatusCodeAndPaymentDate"), params);
    }

    public Optional<Society> findSocietyByIdAndStatusCode(Map<String, Object> paging){
        return this.sqlSession.selectOne(this.sqlId("findSocietyByIdAndStatusCode"), paging);
    }

    public List<Society> getSocietyByNameAndStatusAndApplicationStatusCode(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("getSocietyByNameAndStatusAndApplicationStatusCode"), params);
    }

    public Long countSocietyByNameAndStatusAndApplicationStatusCode(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countSocietyByNameAndStatusAndApplicationStatusCode"), params);
    }
    public List<GetUserSocietiesForAppealResponse> getUserSocietiesForAppeal(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getUserSocietiesForAppeal"), params);
    }

    public Long countUserSocietiesForAppeal(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countUserSocietiesForAppeal"), params);
    }

    public Integer countAllPendingByCriteria(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllPendingByCriteria"), params);
    }

    public List<SocietyGetOneResponse> getUserSocieties(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getUserSocieties"), params);
    }

    public Long countGetUserSocieties(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countGetUserSocieties"), params);
    }

    public List<Society> findByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByCriteria"), params);
    }

    public Boolean resubmitSocietyApplication(Map<String, Object> params){
        return this.sqlSession.update(this.sqlId("resubmitSocietyApplication"), params) > 0;
    }

    public List<GetUserSocietiesForAppealResponse> findSocietyAmendmentAndNonCitizenForAppeal(Map<String, Object> param) {
        return this.sqlSession.selectList(this.sqlId("findSocietyAmendmentAndNonCitizenForAppeal"), param);
    }

    public List<GetUserSocietiesForAppealResponse> findSocietyForAppeal(Map<String, Object> param) {
        return this.sqlSession.selectList(this.sqlId("findSocietyForAppeal"), param);
    }

    public List<Long> getAllExpiredSocietyApplicationId(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllExpiredSocietyApplicationId"), params);
    }

    public Boolean updateExpiredSociety(Map<String, Object> params){
        return this.sqlSession.update(this.sqlId("updateExpiredSociety"), params) > 0;
    }

    public List<Society> getAllSoonToExpireSocietyApplication(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllSoonToExpireSocietyApplication"), params);
    }

    public List<Society> getSocietiesByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getSocietiesByCriteria"), params);
    }

    public Long countGetSocietiesByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countGetSocietiesByCriteria"), params);
    }

    public List<Society> getSocietiesByCriteriaAndExcludingActiveSocietyCancellationSocieties(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getSocietiesByCriteriaAndExcludingActiveSocietyCancellationSocieties"), params);
    }

    public Long countSocietiesByCriteriaAndExcludingActiveSocietyCancellationSocieties(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countSocietiesByCriteriaAndExcludingActiveSocietyCancellationSocieties"), params);
    }

    public List<Society> findByCategoryCodeJppm(String categoryCodeJppm) {
        return this.sqlSession.selectList(this.sqlId("findByCategoryCodeJppm"), categoryCodeJppm);
    }

    public List<Society> findByCategoriesCodeJppm(List<Long> categoriesCodeJppm) {
        return this.sqlSession.selectList(this.sqlId("findByCategoriesCodeJppm"), categoriesCodeJppm);
    }

    public List<Society> findBySocietyLevel(String societyLevel) {
        return this.sqlSession.selectList(this.sqlId("findBySocietyLevel"), societyLevel);
    }

    public List<Society> findByCategoryAndLevel(String categoryCodeJppm, String societyLevel) {
        Map<String, Object> params = Map.of(
                "categoryCodeJppm", categoryCodeJppm,
                "societyLevel", societyLevel
        );
        return this.sqlSession.selectList(this.sqlId("findByCategoryAndLevel"), params);
    }
    public List<Society> findByCities(List<String> cities) {
        return this.sqlSession.selectList(this.sqlId("findByCities"), cities);
    }
    public List<Society> findByDistrictCodes(List<Long> districtCodes) {
        return this.sqlSession.selectList(this.sqlId("findByDistrictCodes"), districtCodes);
    }
    public List<Society> findByStateCodes(List<Long> stateCodes) {
        return this.sqlSession.selectList(this.sqlId("findByStateCodes"), stateCodes);
    }

    public List<Society> getAllSocietyPendingApproval(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllSocietyPendingApproval"), params);
    }
}
