package com.eroses.external.society.mappers;

import com.eroses.external.society.model.EventFeedbackQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface EventFeedbackQuestionDao {
    List<EventFeedbackQuestion> findAll();
    List<EventFeedbackQuestion> findByEventId(Long eventId);
    EventFeedbackQuestion findById(Long id);
    int create(List<EventFeedbackQuestion> eventFeedbackQuestion);
    boolean update(EventFeedbackQuestion eventFeedbackQuestion);
    void deleteByEventAndQuestionId(List<EventFeedbackQuestion> eventFeedbackQuestions);
    void deleteByEventId(Long eventId);
}
