package com.eroses.external.society.mappers.grant;

import com.eroses.external.society.model.grant.GrantTemplate;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class GrantTemplateDao extends MyBatisDao<GrantTemplate> {
    
    public List<GrantTemplate> findAll(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAll"), params);
    }
    
    public Long countAll(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAll"), params);
    }
    
    public List<GrantTemplate> findAllDrafts() {
        return this.sqlSession.selectList(this.sqlId("findAllDrafts"));
    }
    
    public List<GrantTemplate> findAllPublished() {
        return this.sqlSession.selectList(this.sqlId("findAllPublished"));
    }
    
    public GrantTemplate findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }
    
    public List<GrantTemplate> findBySocietyCategory(Long societyCategoryId) {
        return this.sqlSession.selectList(this.sqlId("findBySocietyCategory"), societyCategoryId);
    }
    
    public List<GrantTemplate> findAvailableForSociety(Long societyId) {
        return this.sqlSession.selectList(this.sqlId("findAvailableForSociety"), societyId);
    }
}
