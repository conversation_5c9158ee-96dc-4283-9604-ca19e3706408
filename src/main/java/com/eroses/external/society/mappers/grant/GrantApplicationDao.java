package com.eroses.external.society.mappers.grant;

import com.eroses.external.society.model.grant.GrantApplication;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class GrantApplicationDao extends MyBatisDao<GrantApplication> {
    
    public List<GrantApplication> findAll(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAll"), params);
    }
    
    public Long countAll(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAll"), params);
    }
    
    public GrantApplication findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }
    
    public List<GrantApplication> findBySocietyId(Long societyId) {
        return this.sqlSession.selectList(this.sqlId("findBySocietyId"), societyId);
    }
    
    public List<GrantApplication> findByGrantTemplateId(Long grantTemplateId) {
        return this.sqlSession.selectList(this.sqlId("findByGrantTemplateId"), grantTemplateId);
    }
    
    public List<GrantApplication> findByStatus(String status) {
        return this.sqlSession.selectList(this.sqlId("findByStatus"), status);
    }
    
    public List<GrantApplication> findPendingStateApproval() {
        return this.sqlSession.selectList(this.sqlId("findPendingStateApproval"));
    }
    
    public List<GrantApplication> findPendingHQApproval() {
        return this.sqlSession.selectList(this.sqlId("findPendingHQApproval"));
    }
}
