package com.eroses.external.society.mappers;

import com.eroses.external.society.model.SocietyDocumentTemplate;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class SocietyDocumentTemplateDao extends MyBatisDao<SocietyDocumentTemplate> {

//    Long create(SocietyDocumentTemplate societyDocumentTemplate);

    public List<SocietyDocumentTemplate> getAllBySocietyId(Long societyId){
        return this.sqlSession.selectList(this.sqlId("getAllBySocietyId"), societyId);
    }

    public SocietyDocumentTemplate getBySocietyIdAndTemplateCode (Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("getBySocietyIdAndTemplateCode"), params);
    }
}