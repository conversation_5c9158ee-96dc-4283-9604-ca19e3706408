package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.statement.StatementGetOneResponse;
import com.eroses.external.society.dto.response.statement.StatementInfoForInternalResponse;
import com.eroses.external.society.model.Statement;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class StatementDao extends MyBatisDao<Statement> {
//    boolean create(Statement statement);

//    boolean update(Statement statement);

//    Statement findById(Long id);

    public List<Statement> findAll(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAll"), params);
    }

    public long countFindAll(){
        return this.sqlSession.selectOne(this.sqlId("countFindAll"));
    }

    public List<StatementGetOneResponse> listStatements(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("listStatements"), params);
    }

    public long countListStatements(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countListStatements"), params);
    }

    public boolean existsBySocietyIdAndStatementYear(Map<String, Object> params){
        Integer count = this.sqlSession.selectOne(this.sqlId("existsBySocietyIdAndStatementYear"), params);
        return count != null && count > 0;
    }

    public Statement findByStatementId(Long statementId){
        return this.sqlSession.selectOne(this.sqlId("findByStatementId"), statementId);
    }

    public List<Statement> admSearchStatement(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("admSearchStatement"), params);
    }

    public List<Statement> searchStatement(String searchQuery){
        return this.sqlSession.selectList(this.sqlId("searchStatement"), searchQuery);
    }

    public long countAdmSearchStatement(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAdmSearchStatement"), params);
    }

   public Boolean updateGeneralStatement(Statement statement){
       return this.sqlSession.update(this.sqlId("updateGeneralStatement"), statement) > 0;
   }

    public StatementGetOneResponse getGeneralStatementInfo(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("getGeneralStatementInfo"), params);
    }

    public Boolean deleteStatement(Map<String, Object> params) {
        return this.sqlSession.update(this.sqlId("deleteStatement"), params) > 0;
    }

    public Statement findBySocietyIdOrBranchIdAndStatementYear(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findBySocietyIdOrBranchIdAndStatementYear"), params);
    }

    public Statement findByParam(Map<String, Object> param) {
        return this.sqlSession.selectOne(this.sqlId("findByParam"), param);
    }

    public List<StatementInfoForInternalResponse> getStatementListForInternal(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getStatementListForInternal"), params);
    }

    public Long countGetStatementListForInternal(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countGetStatementListForInternal"), params);
    }
}
