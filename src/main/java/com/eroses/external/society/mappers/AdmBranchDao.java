package com.eroses.external.society.mappers;

import com.eroses.external.society.model.AdmBranch;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class AdmBranchDao extends MyBatisDao<AdmBranch> {

    public List<AdmBranch> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

    public List<AdmBranch> findAllByCodes(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAllByCodes"), params);
    }

    public AdmBranch findByCode(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findByCode"), params);
    }

    public List<AdmBranch> findAdmBranchByJppmBranchId(Long jppmBranchId) {
        return this.sqlSession.selectList(this.sqlId("findAdmBranchByJppmBranchId"), jppmBranchId);
    }

    public String findStateCodeByJppmBranchId(Long jppmBranchId) {
        return this.sqlSession.selectOne(this.sqlId("findStateCodeByJppmBranchId"), jppmBranchId);
    }

    public AdmBranch findByStateCode(String stateCode) {
        return this.sqlSession.selectOne(this.sqlId("findByStateCode"), stateCode);
    }

    public boolean existsByCode(String code) {
        return this.sqlSession.selectOne(this.sqlId("existsByCode"), code);
    }

    public boolean existsByCodeExcludingId(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsByCodeExcludingId"), params);
    }

    public List<AdmBranch> getAllActive() {
        return this.sqlSession.selectList(this.sqlId("getAllActive"));
    }

    public List<AdmBranch> list() {
        return this.sqlSession.selectList(this.sqlId("list"));
    }

    public List<AdmBranch> getAll(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAll"), params);
    }

    public Long countAll(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAll"), params);
    }

    public List<AdmBranch> getAllByStateCode(String stateCode) {
        return this.sqlSession.selectList(this.sqlId("getAllByStateCode"), stateCode);
    }
}

