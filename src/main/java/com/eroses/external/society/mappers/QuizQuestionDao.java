package com.eroses.external.society.mappers;

import com.eroses.external.society.model.QuizQuestion;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class QuizQuestionDao extends MyBatisDao<QuizQuestion> {

//    public boolean create(QuizQuestion quizQuestion) {
//        return this.sqlSession.insert(this.sqlId("create"), quizQuestion) == 1;
//    }
//
//    public boolean update(QuizQuestion quizQuestion) {
//        return this.sqlSession.update(this.sqlId("update"), quizQuestion) == 1;
//    }
//
//    public boolean delete(Long id) {
//        return this.sqlSession.delete(this.sqlId("delete"), id) == 1;
//    }

    public QuizQuestion findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }

    public List<QuizQuestion> findAllByQuizId(Long trainingQuizId) {
        return this.sqlSession.selectList(this.sqlId("findAllByQuizId"), trainingQuizId);
    }

    public boolean updateSequenceOrders(List<QuizQuestion> questions) {
        Map<String, Object> params = new HashMap<>();
        params.put("questions", questions);
        return this.sqlSession.update(this.sqlId("updateSequenceOrders"), params) > 0;
    }
}
