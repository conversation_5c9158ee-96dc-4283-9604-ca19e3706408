package com.eroses.external.society.mappers;

import com.eroses.external.society.model.PublicOfficer;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class PublicOfficerDao extends MyBatisDao<PublicOfficer> {
    public List<PublicOfficer> getAll(Map<String, Object> parameters){
        return this.sqlSession.selectList(this.sqlId("getAll"), parameters);
    }

    public long countAll(Map<String, Object> parameters){
        return this.sqlSession.selectOne(this.sqlId("countAll"), parameters);
    }

    public PublicOfficer findByPaymentId(Long paymentId){
        return this.sqlSession.selectOne(this.sqlId("findByPaymentId"), paymentId);
    }

    public long countAllBranchPending(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllBranchPending"), params);
    }

    public long countAllBranchPendingWithCriteria(Map<String,Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllBranchPendingWithCriteria"), params);
    }

    public long countAllPending(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllPending"), params);
    }

    public List<PublicOfficer> listAllBranchPending(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("listAllBranchPending"), params);
    }

    public List<PublicOfficer> listAllSocietyPending(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("listAllSocietyPending"), params);
    }

    public Long countAllSocietyPendingWithCriteria(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllSocietyPendingWithCriteria"), params);
    }

    public List<PublicOfficer> getAllPublicOfficerPendingApproval(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("getAllPublicOfficerPendingApproval"), params);
    }

    public boolean existsOnGoingSocietyPublicOfficerApplication(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsOnGoingSocietyPublicOfficerApplication"), params);
    }

    public boolean existsOnGoingBranchPublicOfficerApplication(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsOnGoingBranchPublicOfficerApplication"), params);
    }
}
