package com.eroses.external.society.mappers;

import com.eroses.external.society.model.ExtensionTime;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class ExtensionTimeDao extends MyBatisDao<ExtensionTime> {

    //    public Long createAndGetId(Feedback feedback){
//        this.sqlSession.insert(this.sqlId("create"), feedback);
//        return feedback.getId();
//    }

//    public Boolean update(ExtensionTime extensionTime) {
//        return this.sqlSession.update(this.sqlId("update"), extensionTime) == 1;
//    }

    public ExtensionTime findByIdJoin(Long id){
        return this.sqlSession.selectOne(this.sqlId("findByIdJoin"), id);
    }

    public List<ExtensionTime> findByBranchId(Long branchId){
        return this.sqlSession.selectList(this.sqlId("findByBranchId"), branchId);
    }

    public List<ExtensionTime> getAllPendingBranchExtensionTimeByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllPendingBranchExtensionTimeByCriteria"), params);
    }

    public Integer countAllPendingBranchExtensionTimeByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllPendingBranchExtensionTimeByCriteria"), params);
    }

    public ExtensionTime findExisting(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findExisting"), params);
    }
}
