package com.eroses.external.society.mappers;

import com.eroses.external.society.model.SearchInformationDocumentTemplate;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class SearchInformationDocumentTemplateDao extends MyBatisDao<SearchInformationDocumentTemplate> {

//    Long create(SearchInformationDocumentTemplate searchInformationDocumentTemplate);

    public SearchInformationDocumentTemplate getByCode(String code){
        return this.sqlSession.selectOne(this.sqlId("getByCode"), code);
    }

    public List<SearchInformationDocumentTemplate> getAll(){
        return this.sqlSession.selectList(this.sqlId("getAll"));
    }
}