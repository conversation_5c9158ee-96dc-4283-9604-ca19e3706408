package com.eroses.external.society.mappers;

import com.eroses.external.society.model.TrainingEnrollment;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class TrainingEnrollmentDao extends MyBatisDao<TrainingEnrollment> {

//    public boolean create(TrainingEnrollment trainingEnrollment) {
//        return this.sqlSession.insert(this.sqlId("create"), trainingEnrollment) == 1;
//    }
//
//    public boolean update(TrainingEnrollment trainingEnrollment) {
//        return this.sqlSession.update(this.sqlId("update"), trainingEnrollment) == 1;
//    }

    public TrainingEnrollment findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }

    public TrainingEnrollment findByUserIdAndCourseId(Long userId, Long trainingCourseId) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("trainingCourseId", trainingCourseId);
        return this.sqlSession.selectOne(this.sqlId("findByUserIdAndCourseId"), params);
    }

    public List<TrainingEnrollment> findAllByUserId(Long userId) {
        return this.sqlSession.selectList(this.sqlId("findAllByUserId"), userId);
    }

    public List<TrainingEnrollment> findAllByCourseId(Long trainingCourseId) {
        return this.sqlSession.selectList(this.sqlId("findAllByCourseId"), trainingCourseId);
    }

    public List<TrainingEnrollment> findAllCompletedByUserId(Long userId) {
        return this.sqlSession.selectList(this.sqlId("findAllCompletedByUserId"), userId);
    }

    public boolean updateCompletionStatus(Long id, String completionStatus) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("completionStatus", completionStatus);
        return this.sqlSession.update(this.sqlId("updateCompletionStatus"), params) == 1;
    }

    public boolean updateCertificateId(Long id, String certificateId) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("certificateId", certificateId);
        return this.sqlSession.update(this.sqlId("updateCertificateId"), params) == 1;
    }
}
