package com.eroses.external.society.mappers;

import com.eroses.external.society.model.QuizAttempt;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class QuizAttemptDao extends MyBatisDao<QuizAttempt> {

//    public boolean create(QuizAttempt quizAttempt) {
//        return this.sqlSession.insert(this.sqlId("create"), quizAttempt) == 1;
//    }
//
//    public boolean update(QuizAttempt quizAttempt) {
//        return this.sqlSession.update(this.sqlId("update"), quizAttempt) == 1;
//    }

    public QuizAttempt findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }

    public List<QuizAttempt> findAllByEnrollmentId(Long trainingEnrollmentId) {
        return this.sqlSession.selectList(this.sqlId("findAllByEnrollmentId"), trainingEnrollmentId);
    }

    public List<QuizAttempt> findAllByQuizId(Long trainingQuizId) {
        return this.sqlSession.selectList(this.sqlId("findAllByQuizId"), trainingQuizId);
    }

    public QuizAttempt findLatestByEnrollmentIdAndQuizId(Long trainingEnrollmentId, Long trainingQuizId) {
        Map<String, Object> params = new HashMap<>();
        params.put("trainingEnrollmentId", trainingEnrollmentId);
        params.put("trainingQuizId", trainingQuizId);
        return this.sqlSession.selectOne(this.sqlId("findLatestByEnrollmentIdAndQuizId"), params);
    }

    public int countAttemptsByEnrollmentIdAndQuizId(Long trainingEnrollmentId, Long trainingQuizId) {
        Map<String, Object> params = new HashMap<>();
        params.put("trainingEnrollmentId", trainingEnrollmentId);
        params.put("trainingQuizId", trainingQuizId);
        return this.sqlSession.selectOne(this.sqlId("countAttemptsByEnrollmentIdAndQuizId"), params);
    }

    public boolean updateScoreAndPassed(Long id, Integer score, Boolean passed) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("score", score);
        params.put("passed", passed);
        return this.sqlSession.update(this.sqlId("updateScoreAndPassed"), params) == 1;
    }
}
