package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.meeting.BranchMeetingResponse;
import com.eroses.external.society.dto.response.meeting.MeetingBasicListResponse;
import com.eroses.external.society.dto.response.meeting.MeetingPagingResponse;
import com.eroses.external.society.dto.response.meeting.MeetingResponse;
import com.eroses.external.society.model.Meeting;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class MeetingDao extends MyBatisDao<Meeting> {
    //Boolean create(Meeting meeting);
    //int update(Meeting meeting);

    public List<Meeting> getAll(){
        return this.sqlSession.selectList(this.sqlId("getAll"));
    }

    public Meeting findByBranchNo(String branchNo){
        return this.sqlSession.selectOne(this.sqlId("findByBranchNo"), branchNo);
    }

    public List<MeetingPagingResponse> search(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("search"), params);
    }

    public Long findMeetingsTotalCount(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("findMeetingsTotalCount"), params);
    }

    public List<Meeting> findBySocietyId(Long societyId){
        return this.sqlSession.selectList(this.sqlId("findBySocietyId"), societyId);
    }

    public Boolean isExistByCriteria(Map<String, Object> params) {
        Integer count = this.sqlSession.selectOne(this.sqlId("countByCriteria"), params);
        return count != null && count > 0;
    }

    public List<Meeting> findByBranchId(Long branchId){
        return this.sqlSession.selectList(this.sqlId("findByBranchId"), branchId);
    }

    public List<BranchMeetingResponse> findByBranchIdAndMeetingTypes(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findByBranchIdAndMeetingTypes"), params);
    }

    public List<Meeting> findBySocietyIdAndMeetingTypes(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findBySocietyIdAndMeetingTypes"), params);
    }

    public List<MeetingBasicListResponse> findBySocietyIdAndBranchId(
        Long societyId,
        Long branchId,
        Integer meetingType,
        LocalDate meetingDate
    ){
        Map<String, Object> params = new HashMap<>();
        params.put("societyId", societyId);
        params.put("branchId", branchId);
        params.put("meetingType", meetingType);
        params.put("meetingDate", meetingDate);
        return this.sqlSession.selectList(this.sqlId("findBySocietyIdAndBranchId"), params);
    }

    public MeetingResponse findMeetingAndMembers(Long meetingId) {
        return this.sqlSession.selectOne(this.sqlId("findMeetingAndMembers"), meetingId);
    }

    public Meeting findStatementMeeting(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("findStatementMeeting"), params);
    }

    public Meeting findByStatementId(Long statementId){
        return this.sqlSession.selectOne(this.sqlId("findByStatementId"), statementId);
    }

    public Meeting findBySocietyIdAndMeetingType(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findBySocietyIdAndMeetingType"), params);
    }

    public List<MeetingResponse> getMeetingInfoForStatement(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getMeetingInfoForStatement"), params);
    }

    public List<Meeting> findByDateAndTypeAndSocietyId(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByDateAndTypeAndSocietyId"), params);
    }
  
    public Meeting findByBranchIdAndMeetingType(Long id, Integer code) {
        return this.sqlSession.selectOne(this.sqlId("findByBranchIdAndMeetingType"), Map.of("id", id, "code", code));

    }
}
