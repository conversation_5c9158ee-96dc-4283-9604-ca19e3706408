package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.societyLiquidation.LiquidationBasicResponse;
import com.eroses.external.society.model.societyLiquidation.SocietyLiquidation;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class SocietyLiquidationDao extends MyBatisDao<SocietyLiquidation> {

    public List<SocietyLiquidation> findAllByCriteria(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAllByCriteria"), params);
    }

    public Long countAllByCriteria(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllByCriteria"), params);
    }

    public LiquidationBasicResponse findById(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("findById"), params);
    }
    
    public SocietyLiquidation findBasic(Long liquidationId){
        return this.sqlSession.selectOne(this.sqlId("findBasic"), liquidationId);
    }

    public SocietyLiquidation getById(Long id){
        return this.sqlSession.selectOne(this.sqlId("getById"), id);
    }

    public int updateVote(Map<String, Object> params){
        return this.sqlSession.update(this.sqlId("updateVote"), params);
    }

    public int assignRO(SocietyLiquidation societyLiquidation){
        return this.sqlSession.update(this.sqlId("assignRO"), societyLiquidation);
    }

    public List<SocietyLiquidation> getAllPendingSocietyLiquidationByCriteria(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("getAllPendingSocietyLiquidationByCriteria"), params);
    }

    public Long countAllPendingSocietyLiquidationByCriteria(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllPendingSocietyLiquidationByCriteria"), params);
    }

    public List<SocietyLiquidation> getAllPendingBranchLiquidationByCriteria(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("getAllPendingBranchLiquidationByCriteria"), params);
    }

    public Long countAllPendingBranchLiquidationByCriteria(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllPendingBranchLiquidationByCriteria"), params);
    }

    public List<String> findDistinctCreatedYears(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findDistinctCreatedYears"), params);
    }

    public List<String> findDistinctSubmissionYears(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findDistinctSubmissionYears"), params);
    }

    public List<String> findDistinctDecisionYears(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findDistinctDecisionYears"), params);
    }
}
