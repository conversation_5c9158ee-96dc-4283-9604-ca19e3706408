package com.eroses.external.society.mappers;

import com.eroses.external.society.model.EventAdmin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface EventAdminDao {
    List<EventAdmin> findAll();
    EventAdmin findById(Long id);
    void create(EventAdmin eventAdmin);
    EventAdmin findByIdentificationNo(@Param("identificationNo") String identificationNo);
    int update(@Param("identificationNo") String identificationNo,
                      @Param("eventadmin") EventAdmin eventadmin);

//    EventAdmin findById(Long id);
}


