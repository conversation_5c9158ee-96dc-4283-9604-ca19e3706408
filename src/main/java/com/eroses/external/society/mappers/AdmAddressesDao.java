package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.database.lookup.DistrictWithState;
import com.eroses.external.society.model.AdmAddresses;
import java.util.List;
import java.util.Map;

import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

//@Slf4j
//@Repository
@Repository
public class AdmAddressesDao extends MyBatisDao<AdmAddresses> {

    public List<AdmAddresses> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

    public List<AdmAddresses> getAll(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAll"), params);
    }

    public List<DistrictWithState> getAllDistricts(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllDistricts"), params);
    }

    public Long countAll(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAll"), params);
    }

    public boolean existsByCode(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsByCode"), params);
    }

    public boolean existsByCodeExcludingId(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsByCodeExcludingId"), params);
    }

    public List<AdmAddresses> getAllActive(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllActive"), params);
    }
}


