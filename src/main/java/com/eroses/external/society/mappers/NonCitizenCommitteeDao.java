package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.appeal.GetUserSocietiesForAppealResponse;
import com.eroses.external.society.model.NonCitizenCommittee;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class NonCitizenCommitteeDao extends MyBatisDao<NonCitizenCommittee> {
//    boolean create(NonCitizenCommittee nonCitizenCommittee);

//    NonCitizenCommittee findById(Long id);

//    Boolean update(NonCitizenCommittee nonCitizenCommittee);

    public int updateAll(List<NonCitizenCommittee> nonCitizenCommittees){
        return this.sqlSession.update(this.sqlId("updateAll"), nonCitizenCommittees);
    }

    public Long deleteAllBySocietyId(Long societyId){
        this.sqlSession.delete(this.sqlId("deleteAllBySocietyId"), societyId);
        return societyId;
    }

    public List<NonCitizenCommittee> findAll(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAll"), params);
    }
    public List<NonCitizenCommittee> findAllByParams(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAllByParams"), params);
    }
    public Long countAllByParams(Map<String,Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllByParams"), params);
    }

    public Long countFindAll(Map<String,Object> params){
        return this.sqlSession.selectOne(this.sqlId("countFindAll"), params);
    }

    public List<NonCitizenCommittee> findBySocietyId(Long societyId){
        return this.sqlSession.selectList(this.sqlId("findBySocietyId"), societyId);
    }

    public List<NonCitizenCommittee> findByBranchId(Long branchId){
        return this.sqlSession.selectList(this.sqlId("findByBranchId"), branchId);
    }

    public List<Map<Long, String>> findSocietyIdAndDesignationCodeByIdentificationNo(String identificationNo){
        return this.sqlSession.selectList(this.sqlId("findSocietyIdAndDesignationCodeByIdentificationNo"), identificationNo);
    }

    public boolean existsByIdentityNoAndSocietyId(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("existsByIdentityNoAndSocietyId"), params);
    }

    public List<NonCitizenCommittee> findBySocietyIdAndApplicationStatusCode(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findBySocietyIdAndApplicationStatusCode"), params);
    }

    public long countBySocietyIdAndApplicationStatusCode(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countBySocietyIdAndApplicationStatusCode"), params);
    }

    public List<NonCitizenCommittee> findBySocietyIdAndApplicationStatusCodeAndStatus(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findBySocietyIdAndApplicationStatusCodeAndStatus"), params);
    }

    public long countBySocietyIdAndApplicationStatusCodeAndStatus(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countBySocietyIdAndApplicationStatusCodeAndStatus"), params);
    }

    public NonCitizenCommittee findBySocietyIdAndIdentificationNoAndApplicationStatusCodeAndStatus(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("findBySocietyIdAndIdentificationNoAndApplicationStatusCodeAndStatus"), params);
    }

    public NonCitizenCommittee findBySocietyIdAndIdentificationNo(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("findBySocietyIdAndIdentificationNo"), params);
    }

    public List<NonCitizenCommittee> findAllBySocietyIdAndIdentificationNo(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAllBySocietyIdAndIdentificationNo"), params);
    }

    public List<NonCitizenCommittee> findAllBySocietyIdAndApplicationStatusCodeAndStatus(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAllBySocietyIdAndApplicationStatusCodeAndStatus"), params);
    }

    public List<NonCitizenCommittee> getAllPendingSocietyNonCitizenByCriteria(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("getAllPendingSocietyNonCitizenByCriteria"), params);
    }

    public List<NonCitizenCommittee> getAllPendingBranchNonCitizenByCriteria(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("getAllPendingBranchNonCitizenByCriteria"), params);
    }

    public Integer countAllPendingSocietyNonCitizenByCriteria(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllPendingSocietyNonCitizenByCriteria"), params);
    }

    public Integer countAllPendingBranchNonCitizenByCriteria(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllPendingBranchNonCitizenByCriteria"), params);
    }

    public Long countBySocietyIdAndPositionAndApplicationStatusCodeAndStatus(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countBySocietyIdAndPositionAndApplicationStatusCodeAndStatus"), params);
    }

    public NonCitizenCommittee findByPaymentId(Long paymentId){
        return this.sqlSession.selectOne(this.sqlId("findByPaymentId"), paymentId);
    }

    public List<GetUserSocietiesForAppealResponse> findNonCitizenCommitteeForAppeal(Map<String, Object> param) {
        return this.sqlSession.selectList(this.sqlId("findNonCitizenCommitteeForAppeal"), param);
    }
}
