package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.posting.PostingReportResponse;
import com.eroses.external.society.model.posting.PostingReport;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class PostingReportDao extends MyBatisDao<PostingReport> {

    public List<PostingReport> findByPostingId(Long postingId) {
        return this.sqlSession.selectList(this.sqlId("findByPostingId"), postingId);
    }

    public List<PostingReport> findByUserId(Long userId) {
        return this.sqlSession.selectList(this.sqlId("findByUserId"), userId);
    }

    public List<PostingReport> findByStatus(String status) {
        return this.sqlSession.selectList(this.sqlId("findByStatus"), status);
    }

    public List<PostingReportResponse> findReportedPostings(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findReportedPostings"), params);
    }

    public Long countReportedPostings(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countReportedPostings"), params);
    }

    public Boolean updateStatus(Long id, String status) {
        Map<String, Object> params = Map.of("id", id, "status", status);
        return this.sqlSession.update(this.sqlId("updateStatus"), params) > 0;
    }

    public PostingReport findByUserIdAndPostingIdAndDate(Long userId, Long postingId) {
        Map<String, Object> params = Map.of("userId", userId, "postingId", postingId);
        return this.sqlSession.selectOne(this.sqlId("findByUserIdAndPostingIdAndDate"), params);
    }
}
