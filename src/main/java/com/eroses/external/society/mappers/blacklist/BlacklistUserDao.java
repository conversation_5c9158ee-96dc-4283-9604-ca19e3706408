package com.eroses.external.society.mappers.blacklist;

import com.eroses.external.society.model.blacklist.BlacklistUser;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class BlacklistUserDao extends MyBatisDao<BlacklistUser> {

    public BlacklistUser findByIdentificationNo(String identificationNo) {
        return this.sqlSession.selectOne(this.sqlId("findByIdentificationNo"), identificationNo);
    }

    public boolean existsActiveBlacklistUserByIdentificationNo(String identificationNo) {
        return this.sqlSession.selectOne(this.sqlId("existsActiveBlacklistUserByIdentificationNo"), identificationNo) != null;
    }

    public List<BlacklistUser> search(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("search"), params);
    }

    public Long countSearch(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countSearch"), params);
    }

    public List<Long> getAllExpiredBlacklistUserId() {
        return this.sqlSession.selectList(this.sqlId("getAllExpiredBlacklistUserId"));
    }
}