package com.eroses.external.society.mappers.grant;

import com.eroses.external.society.model.grant.GrantQuery;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class GrantQueryDao extends MyBatisDao<GrantQuery> {
    
    public List<GrantQuery> findByGrantApplicationId(Long grantApplicationId) {
        return this.sqlSession.selectList(this.sqlId("findByGrantApplicationId"), grantApplicationId);
    }
    
    public GrantQuery findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }
    
    public List<GrantQuery> findPendingQueries() {
        return this.sqlSession.selectList(this.sqlId("findPendingQueries"));
    }
}
