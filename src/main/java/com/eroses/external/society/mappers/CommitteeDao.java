package com.eroses.external.society.mappers;

import com.eroses.external.society.model.Committee;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Repository
public class CommitteeDao extends MyBatisDao<Committee> {

    public List<Committee> findAll(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAll"), params);
    }

    public List<Committee> getAllCommitteesByCriteriaAndSort(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllCommitteesByCriteriaAndSort"), params);
    }

    public List<Committee> findByParams(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByParams"), params);
    }

    public Long countFindAll(Long societyId) {
        return this.sqlSession.selectOne(this.sqlId("countFindAll"), societyId);
    }

    public Boolean removeSecretary(Map<String, Object> map){
        return this.sqlSession.update(this.sqlId("removeSecretary"), map) == 1;
    }

    public Long deleteAllBySocietyId(Long societyId) {
        return (long) this.sqlSession.delete(this.sqlId("deleteAllBySocietyId"), societyId);
    }

    public List<Committee> findBySocietyId(Long societyId) {
        return this.sqlSession.selectList(this.sqlId("findBySocietyId"), societyId);
    }

    public List<Committee> findByPositionAndSocietyIdsAndApplicationStatusCodeAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByPositionAndSocietyIdsAndApplicationStatusCodeAndStatus"), params);
    }

    public List<Long> findSocietyIdListByIdentificationNo(String identificationNo) {
        return this.sqlSession.selectList(this.sqlId("findSocietyIdListByIdentificationNo"), identificationNo);
    }

    public List<Long> findUniqueSocietyIdListByIdentificationNo(String identificationNo) {
        return this.sqlSession.selectList(this.sqlId("findUniqueSocietyIdListByIdentificationNo"), identificationNo);
    }

    public boolean exist(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("exist"), params);
    }

    public boolean existsByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus"), params);
    }

    public boolean existsByIdentityNoAndSocietyIdAndNotEqualApplicationStatusCodesAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsByIdentityNoAndSocietyIdAndNotEqualApplicationStatusCodesAndStatus"), params);
    }

    public List<Committee> findBySocietyIdAndApplicationStatusCode(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findBySocietyIdAndApplicationStatusCode"), params);
    }

    public long countBySocietyIdAndApplicationStatusCode(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countBySocietyIdAndApplicationStatusCode"), params);
    }

    public List<Committee> findBySocietyIdAndApplicationStatusCodeAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findBySocietyIdAndApplicationStatusCodeAndStatus"), params);
    }

    public long countBySocietyIdAndApplicationStatusCodeAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countBySocietyIdAndApplicationStatusCodeAndStatus"), params);
    }

    public Committee findBySocietyIdAndIdentificationNoAndApplicationStatusCodeAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findBySocietyIdAndIdentificationNoAndApplicationStatusCodeAndStatus"), params);
    }

    public Committee findBySocietyIdAndIdentificationNoAndNotEqualApplicationStatusCodeAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findBySocietyIdAndIdentificationNoAndNotEqualApplicationStatusCodeAndStatus"), params);
    }

    public Committee findBySocietyIdAndIdentificationNo(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findBySocietyIdAndIdentificationNo"), params);
    }

    public Committee findNotDeletedBySocietyIdAndIdentificationNo(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findNotDeletedBySocietyIdAndIdentificationNo"), params);
    }

    public List<Committee> findAllBySocietyIdAndIdentificationNo(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAllBySocietyIdAndIdentificationNo"), params);
    }

    public List<Committee> findAllBySocietyIdAndApplicationStatusCodeAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAllBySocietyIdAndApplicationStatusCodeAndStatus"), params);
    }

    public List<Committee> findBySocietyIdAndPositionAndApplicationStatusCodeAndStatusPaging(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findBySocietyIdAndPositionAndApplicationStatusCodeAndStatusPaging"), params);
    }

    public Long countBySocietyIdAndPositionAndApplicationStatusCodeAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countBySocietyIdAndPositionAndApplicationStatusCodeAndStatus"), params);
    }

    public Committee findOneByIdentificationNoAndSocietyIdAndPositionAndApplicationStatusCodeAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findOneByIdentificationNoAndSocietyIdAndPositionAndApplicationStatusCodeAndStatus"), params);
    }

    public Committee findOneBySocietyIdAndIdentificationNoAndApplicationStatusCodeAndStatusAndPosition(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findOneBySocietyIdAndIdentificationNoAndApplicationStatusCodeAndStatusAndPosition"), params);
    }

    public List<Long> findCommitteeIdListByIdentificationNo(String identificationNo) {
        return this.sqlSession.selectList(this.sqlId("findCommitteeIdListByIdentificationNo"), identificationNo);
    }

    public Long deleteByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus(Map<String, Object> params) {
        return (long) this.sqlSession.delete(this.sqlId("deleteByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus"), params);
    }

    public List<Map<Long, String>> findSocietyIdAndDesignationCodeByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findSocietyIdAndDesignationCodeByCriteria"), params);
    }

    public Long countRoleNoInSociety(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countRoleNoInSociety"), params);
    }

    public List<Committee> listRoleInSociety(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("listRoleInSociety"), params);
    }

    public List<Committee> listRoleInSocietyStatus(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("listRoleInSocietyStatus"), params);
    }

    public Long countRoleInSocietyStatus(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countRoleInSocietyStatus"), params);
    }

    public Committee findByStatementId(Long statementId) {
        return this.sqlSession.selectOne(this.sqlId("findByStatementId"), statementId);
    }

    public Committee findACommitteeInSocietyWithRole(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findACommitteeInSocietyWithRole"), params);
    }

    public Committee findActiveCommitteeInSocietyWithRoles(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findActiveCommitteeInSocietyWithRoles"), params);
    }

    public List<Committee> findActiveCommitteesInSocietyWithRoles(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findActiveCommitteesInSocietyWithRoles"), params);
    }

    public List<Committee> findActiveByIdentificationNoAndDesignationCode(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findActiveByIdentificationNoAndDesignationCode"), params);
    }

    public List<Committee> findCommitteeByIdentificationNoIn(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findCommitteeByIdentificationNoIn"), params);
    }

    public List<Committee> findByIds(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByIds"), params);
    }

    public List<Committee> findByIdentificationNo(String identificationNo) {
        return this.sqlSession.selectList(this.sqlId("findByIdentificationNo"), identificationNo);
    }

    public void updateBlacklistStatusByIdentificationNo(Map<String, Object> params) {
        this.sqlSession.update(this.sqlId("updateBlacklistStatusByIdentificationNo"), params);
    }

    public boolean existsByParam(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsByParam"), params);
    }

    public List<Committee> findByParam(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByParam"), params);
    }

    public List<Committee> findByIdentificationNoAndActiveUserAndActiveSociety(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByIdentificationNoAndActiveUserAndActiveSociety"), params);
    }

    public Committee findBySocietyIdAndPositionAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findBySocietyIdAndPositionAndStatus"), params);
    }

    public Boolean updateStatus(List<Long> committeeIds, String status, int applicationStatusCode) {
        return this.sqlSession.update(this.sqlId("updateStatus"), Map.of(
                "committeeIds", committeeIds,
                "status", status,
                "applicationStatusCode", applicationStatusCode
        )) > 0;
    }

    public Committee findOneByParams(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findOneByParams"), params);
    }

    public List<LocalDate> findAllAppointedDates(Long societyId) {
        return this.sqlSession.selectList(this.sqlId("findAllAppointedDates"), societyId);
    }

    public void updateByOldIdAndAppointedDate(Committee updateCommittee) {
        this.sqlSession.update(this.sqlId("updateByOldIdAndAppointedDate"), updateCommittee);
    }

    public void saveAll(List<Committee> currentCommitteeList) {
        this.sqlSession.update(this.sqlId("saveAll"), currentCommitteeList);
    }
}
