package com.eroses.external.society.mappers;

import com.eroses.external.society.model.StatementBankInfo;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class StatementBankInfoDao extends MyBatisDao<StatementBankInfo> {
//    StatementBankInfo findById(Long id);

    public List<StatementBankInfo> listStatementBankInfo(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("listStatementBankInfo"), params);
    }

    public Long countListStatementBankInfo(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countListStatementBankInfo"), params);
    }

//    boolean create(StatementBankInfo statementBankInfo);

//    boolean update(StatementBankInfo statementBankInfo);

    public StatementBankInfo findByStatementId(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("findByStatementId"), params);
    }

    public StatementBankInfo getByParam(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("getByParam"), params);
    }

    public Boolean deleteStatement(Map<String, Object> params) {
        return this.sqlSession.update(this.sqlId("deleteStatement"), params) > 0;
    }

    public Boolean hardDelete(Map<String, Object> params) {
        return this.sqlSession.delete(this.sqlId("delete"), params) > 0;
    }

    public Boolean deleteBankInfo(Long id) {
        return this.sqlSession.delete(this.sqlId("deleteBankInfo"), id) > 0;
    }
}
