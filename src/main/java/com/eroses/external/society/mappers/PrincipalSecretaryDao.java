package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.PrincipalSecretarySearchResponse;
import com.eroses.external.society.dto.response.societySecretaryReplacement.PrincipleSecretaryResponse;
import com.eroses.external.society.model.PrincipalSecretary;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class PrincipalSecretaryDao extends MyBatisDao<PrincipalSecretary> {

    public void delete(PrincipalSecretary principalSecretary){
        this.sqlSession.delete(this.sqlId("delete"), principalSecretary);
    }

    public PrincipleSecretaryResponse findResponseById(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("findById"), params);
    }

    public List<PrincipalSecretary> findAll(){
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

    public List<PrincipalSecretarySearchResponse> searchPrincipalSecretaryExternal(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("searchPrincipalSecretaryExternal"), params);
    }

    public Long countPrincipalSecretaryExternal(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countPrincipalSecretaryExternal"), params);
    }

    public PrincipalSecretary getById(Long id){
        return this.sqlSession.selectOne(this.sqlId("getById"), id);
    }

    public boolean existsById(Long id){
        return this.sqlSession.selectOne(this.sqlId("existsById"), id);
    }

    public List<PrincipalSecretary> getAllPendingByCriteria(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("getAllPendingByCriteria"), params);
    }

    public Long countAllPendingByCriteria(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllPendingByCriteria"), params);
    }
}
