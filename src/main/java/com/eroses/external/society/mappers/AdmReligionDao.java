package com.eroses.external.society.mappers;

import com.eroses.external.society.model.lookup.AdmReligion;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class AdmReligionDao extends MyBatisDao<AdmReligion> {

    public List<AdmReligion> getAll(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAll"), params);
    }

    public Long countAll(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAll"), params);
    }

    public List<AdmReligion> getAllActive() {
        return this.sqlSession.selectList(this.sqlId("getAllActive"));
    }

    @Override
    public Boolean delete(Long id) {
        return this.sqlSession.update(this.sqlId("delete"), id) == 1;
    }

    public boolean existsByCode(String code) {
        return this.sqlSession.selectOne(this.sqlId("existsByCode"), code);
    }

    public boolean existsByCodeExcludingId(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsByCodeExcludingId"), params);
    }
}
