package com.eroses.external.society.mappers;

import com.eroses.external.society.model.Auditor;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Map;

@Repository
public class AuditorDao extends MyBatisDao<Auditor> {

    // Old methods, now commented out
    // void create(Auditor auditor);
    // List<Auditor> listAuditor(Map<String, Object> params);
    // Long countListAuditor(Map<String, Object> params);
    // Auditor findById(Long id);
    // boolean update(Auditor auditor);
    // Auditor findByStatementId(Long statementId);

//    public void create(Auditor auditor) {
//        this.sqlSession.insert(this.sqlId("create"), auditor);
//    }

    public List<Auditor> listAuditor(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("listAuditor"), params);
    }

    public Long countListAuditor(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countListAuditor"), params);
    }

    public Auditor findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }

//    public boolean update(Auditor auditor) {
//        return this.sqlSession.update(this.sqlId("update"), auditor) > 0;
//    }

    public Auditor findByStatementId(Long statementId) {
        return this.sqlSession.selectOne(this.sqlId("findByStatementId"), statementId);
    }
}

