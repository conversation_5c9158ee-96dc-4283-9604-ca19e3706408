package com.eroses.external.society.mappers;

import com.eroses.external.society.model.Event;
import com.eroses.external.society.model.EventOrganiser;
import com.eroses.external.society.model.Organiser;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface EventOrganiserDao {
    List<EventOrganiser> findAll();
    List<EventOrganiser> findByEventId(Long eventId);
    List<EventOrganiser> findAllForEvents();

    List<Organiser> findByEventIdJointTable(Long eventId);
    List<EventOrganiser> findByOrganiserId(Long orgId);
    EventOrganiser findOneByOrgIdAndEventId(Long orgId, Long eventId );
    int create(EventOrganiser eventOrganisers);
    Boolean update(EventOrganiser eo);
//    Booelan update

}
