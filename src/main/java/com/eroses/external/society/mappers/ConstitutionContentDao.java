package com.eroses.external.society.mappers;

import com.eroses.external.society.model.ConstitutionContent;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class ConstitutionContentDao extends MyBatisDao<ConstitutionContent> {
    // Old methods, now commented out
    // Boolean create(ConstitutionContent constitutioncontent);
    // int update(ConstitutionContent constitutioncontent);
    // ConstitutionContent findById(Long id);
    // List<ConstitutionContent> findBySocietyId(Long societyId);
    // List<ConstitutionContent> findAll(Map<String, Object> params);
    // List<ConstitutionContent> getAllBySocietyId(Map<String, Object> params);
    // long countFindAll(Map<String, Object> params);
    // ConstitutionContent findForAmendment(Long societyId, Long clauseContentId, Integer applicationStatusCode);
    // void updateStatus(List<Long> idList, String status, Integer appStatusCode, long userId);
    // long countBySocietyIdAndConstitutionTypeId(Map<String, Object> params);

//    public Boolean create(ConstitutionContent constitutioncontent) {
//        return this.sqlSession.insert(this.sqlId("create"), constitutioncontent) > 0;
//    }
//
//    public int update(ConstitutionContent constitutioncontent) {
//        return this.sqlSession.update(this.sqlId("update"), constitutioncontent);
//    }
//
//    public ConstitutionContent findBy(Long id) {
//        return this.sqlSession.selectOne(this.sqlId("findBy"), id);
//    }

    public List<ConstitutionContent> findBySocietyId(Long societyId) {
        return this.sqlSession.selectList(this.sqlId("findBySocietyId"), societyId);
    }

    public List<ConstitutionContent> findAll(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAll"), params);
    }

    public List<ConstitutionContent> getAllBySocietyId(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllBySocietyId"), params);
    }

    public long countFindAll(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countFindAll"), params);
    }

    public ConstitutionContent findForAmendment(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findForAmendment"), params);
    }

    public void updateStatus(List<Long> idList, String status, Integer appStatusCode, long userId) {
        this.sqlSession.update(this.sqlId("updateStatus"), Map.of("idList", idList, "status", status, "appStatusCode", appStatusCode, "userId", userId));
    }

    public long countBySocietyIdAndConstitutionTypeId(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countBySocietyIdAndConstitutionTypeId"), params);
    }

    public Boolean hardDelete(Long id) {
        return this.sqlSession.delete(this.sqlId("hardDelete"), id) > 0;
    }

    public ConstitutionContent findBySocietyIdAndClauseContentId(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findBySocietyIdAndClauseContentId"), params);
    }

    public List<ConstitutionContent> findAllByIdList(List<Long> oldConstitutionContentIds) {
        return this.sqlSession.selectList(this.sqlId("findAllByIdList"), oldConstitutionContentIds);
    }

    public ConstitutionContent findBySocietyIdAndClauseContentIdAndApplicationStatusCode(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findBySocietyIdAndClauseContentIdAndApplicationStatusCode"), params);
    }
}
