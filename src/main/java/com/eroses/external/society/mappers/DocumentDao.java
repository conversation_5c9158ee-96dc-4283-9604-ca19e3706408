package com.eroses.external.society.mappers;

import com.eroses.external.society.model.Document;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class DocumentDao extends MyBatisDao<Document> {
    // Old methods, now commented out
    // List<Document> findAll();
    // Document findById(Long id);
    // List<Document> findByParam(Map<String, Object> param);
    // List<Document> findByBranchId(Long branchId);
    // Long registerFileInDb(Document document);
    // Boolean updateDocument(Document document);
    // Boolean deleteDocument(Long id);
    // Boolean updateDocumentUrl(Document document);
    // void unregisterFromDb(String url);
    // Boolean submitDocument(Long id);

    public List<Document> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

//    public Document findById(Long id) {
//        return this.sqlSession.selectOne(this.sqlId("findById"), id);
//    }

    public List<Document> findByParam(Map<String, Object> param) {
        return this.sqlSession.selectList(this.sqlId("findByParam"), param);
    }

    public Boolean updateDocumentStatusToInactive(List<Long> ids) {
        return this.sqlSession.update(this.sqlId("updateDocumentStatusToInactive"), ids) > 0;
    }

    public List<Document> findByBranchId(Long branchId) {
        return this.sqlSession.selectList(this.sqlId("findByBranchId"), branchId);
    }

    public Long registerFileInDb(Document document) {
        return (long) this.sqlSession.insert(this.sqlId("registerFileInDb"), document);
    }

    public Boolean updateDocument(Document document) {
        return this.sqlSession.update(this.sqlId("updateDocument"), document) > 0;
    }

    public Boolean deleteDocument(Long id) {
        return this.sqlSession.delete(this.sqlId("deleteDocument"), id) > 0;
    }

    public Boolean updateDocumentUrl(Document document) {
        return this.sqlSession.update(this.sqlId("updateDocumentUrl"), document) > 0;
    }

    public void unregisterFromDb(String url) {
        this.sqlSession.delete(this.sqlId("unregisterFromDb"), url);
    }

    public Boolean submitDocument(Long id) {
        return this.sqlSession.update(this.sqlId("submitDocument"), id) > 0;
    }

    public Document findByParamSingle(Map<String, Object> param) {
        return this.sqlSession.selectOne(this.sqlId("findByParamSingle"), param);
    }

    public List<Document> findDocumentByIdListAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findDocumentByIdListAndStatus"), params);
    }

    public Long countFindDocumentByParam(Map<String, Object> param) {
        return this.sqlSession.selectOne(this.sqlId("countFindDocumentByParam"), param);
    }
}