package com.eroses.external.society.mappers;

import com.eroses.external.society.model.ForbiddenKeyword;
import com.eroses.external.society.model.ForbiddenLogo;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class ForbiddenLogoDao extends MyBatisDao<ForbiddenLogo> {
    public List<ForbiddenLogo> findAllLaranganLogo() {
        return this.sqlSession.selectList(this.sqlId("findAllLaranganLogo"));
    }

    public ForbiddenLogo createLaranganLogo(ForbiddenLogo forbiddenLogo) {
        this.sqlSession.insert(this.sqlId("createLaranganLogo"), forbiddenLogo);
        return forbiddenLogo;
    }

    public ForbiddenLogo findLaranganLogoById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findLaranganLogoById"), id);
    }

    public List<String> findAllLaranganLogoImageOnly() {
        return this.sqlSession.selectList(this.sqlId("findAllLaranganLogoImageOnly"));
    }
    public List<String> findAllLaranganLogoVectorOnly() {
        return this.sqlSession.selectList(this.sqlId("findAllLaranganLogoVectorOnly"));
    }

    public Boolean updateLaranganLogo(ForbiddenLogo forbiddenLogo) {
        return this.sqlSession.update(this.sqlId("updateLaranganLogo"), forbiddenLogo) == 1;
    }
    public List<ForbiddenLogo> searchLaranganLogo(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("searchLaranganLogo"), params);
    }
    public Long countSearchLaranganLogo(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countSearchLaranganLogo"), params);
    }
}
