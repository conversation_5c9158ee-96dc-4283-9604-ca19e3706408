package com.eroses.external.society.mappers;

import com.eroses.external.society.model.SocietyCommitteeArchive;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class SocietyCommitteeArchiveDao extends MyBatisDao<SocietyCommitteeArchive> {

    public List<SocietyCommitteeArchive> getCommitteeArchive(Map<String, Object> param) {
        return this.sqlSession.selectList(this.sqlId("getCommitteeArchive"), param);
    }

    public Long countCommitteeArchive(Map<String, Object> param) {
        return this.sqlSession.selectOne(this.sqlId("countCommitteeArchive"), param);
    }

    public List<SocietyCommitteeArchive> findByMeetingDate(LocalDate meetingDate) {
        return this.sqlSession.selectList(this.sqlId("findByMeetingDate"), meetingDate);
    }

    public SocietyCommitteeArchive findByCommitteeOldId(Long committeeTableOldId) {
        return this.sqlSession.selectOne(this.sqlId("findByCommitteeOldId"), committeeTableOldId);
    }

    public SocietyCommitteeArchive findOneByParams(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findOneByParams"), params);
    }

    public List<LocalDate> findAllAppointedDates(Long societyId) {
        return this.sqlSession.selectList(this.sqlId("findAllAppointedDates"), societyId);
    }

    public List<SocietyCommitteeArchive> findByParams(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByParams"), params);
    }

    public List<SocietyCommitteeArchive> findActiveCommitteesInSocietyArchiveWithRoles(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findActiveCommitteesInSocietyArchiveWithRoles"), params);
    }
}
