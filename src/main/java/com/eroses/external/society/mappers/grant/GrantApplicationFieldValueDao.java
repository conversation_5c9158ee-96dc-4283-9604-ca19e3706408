package com.eroses.external.society.mappers.grant;

import com.eroses.external.society.model.grant.GrantApplicationFieldValue;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class GrantApplicationFieldValueDao extends MyBatisDao<GrantApplicationFieldValue> {

    public List<GrantApplicationFieldValue> findByGrantApplicationId(Long grantApplicationId) {
        return this.sqlSession.selectList(this.sqlId("findByGrantApplicationId"), grantApplicationId);
    }

    public GrantApplicationFieldValue findByGrantApplicationIdAndFieldId(Long grantApplicationId, Long grantTemplateFieldId) {
        return this.sqlSession.selectOne(this.sqlId("findByGrantApplicationIdAndFieldId"),
                Map.of("grantApplicationId", grantApplicationId, "grantTemplateFieldId", grantTemplateFieldId));
    }

    public void deleteByGrantApplicationId(Long grantApplicationId) {
        this.sqlSession.delete(this.sqlId("deleteByGrantApplicationId"), grantApplicationId);
    }
}
