package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.statement.StatementSocietyInfoGetOneResponse;
import com.eroses.external.society.model.StatementSocietyInfo;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class StatementSocietyInfoDao extends MyBatisDao<StatementSocietyInfo> {
//    StatementSocietyInfo findById(Long id);

    public List<StatementSocietyInfo> findAll(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAll"), params);
    }

    public Long countFindAll(){
        return this.sqlSession.selectOne(this.sqlId("countFindAll"));
    }

//    boolean create(StatementSocietyInfo statementSocietyInfo);

//    boolean update(StatementSocietyInfo statementSocietyInfo);

    public StatementSocietyInfo getStatementSocietyInfo(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("getStatementSocietyInfo"), params);
    }

    public StatementSocietyInfo findByStatementId(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("findByStatementId"), params);
    }

    public boolean updateByStatementId(StatementSocietyInfo statementSocietyInfo){
        return this.sqlSession.update(this.sqlId("updateByStatementId"), statementSocietyInfo) > 0;
    }

    public StatementSocietyInfoGetOneResponse findByParam(Map<String, Object> param) {
        return this.sqlSession.selectOne(this.sqlId("findByParam"), param);
    }

    public Boolean deleteStatement(Long statementId, Long societyId, Long branchId) {
        Map<String, Object> params = new HashMap<>();
        params.put("statementId", statementId);
        if (societyId != null) params.put("societyId", societyId);
        if (branchId != null) params.put("branchId", branchId);
        return this.sqlSession.update(this.sqlId("deleteStatement"), params) > 0;
    }
}
