package com.eroses.external.society.mappers;

import com.eroses.external.society.model.BranchAmendment;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Map;

@Repository
public class BranchAmendmentDao extends MyBatisDao<BranchAmendment> {

    // Old methods, now commented out
    // void create(BranchAmendment branchAmendment);
    // void update(BranchAmendment branchAmendment);
    // BranchAmendment findById(Long id);
    // BranchAmendment getByPaymentId(Long paymentId);
    // List<BranchAmendment> getAllByCriteria(Map<String, Object> params);
    // List<BranchAmendment> getAllPendingBranchAmendmentByCriteria(Map<String, Object> params);
    // Integer countAllPendingBranchAmendmentByCriteria(Map<String, Object> params);

//    public void create(BranchAmendment branchAmendment) {
//        this.sqlSession.insert(this.sqlId("create"), branchAmendment);
//    }
//
//    public void update(BranchAmendment branchAmendment) {
//        this.sqlSession.update(this.sqlId("update"), branchAmendment);
//    }
//
//    public BranchAmendment findById(Long id) {
//        return this.sqlSession.selectOne(this.sqlId("findById"), id);
//    }

    public BranchAmendment getByPaymentId(Long paymentId) {
        return this.sqlSession.selectOne(this.sqlId("getByPaymentId"), paymentId);
    }

    public List<BranchAmendment> getAllByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllByCriteria"), params);
    }

    public List<BranchAmendment> getAllPendingBranchAmendmentByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllPendingBranchAmendmentByCriteria"), params);
    }

    public Integer countAllPendingBranchAmendmentByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllPendingBranchAmendmentByCriteria"), params);
    }
}
