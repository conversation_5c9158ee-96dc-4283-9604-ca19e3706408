package com.eroses.external.society.mappers.newSocietySecretary;

import com.eroses.external.society.dto.response.principalSecretaryInternal.PrincipalSecretaryInternalResponse;
import com.eroses.external.society.model.PrincipalSecretary;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class PrincipleSecretaryInternalDao extends MyBatisDao<PrincipalSecretaryInternalResponse> {

    public PrincipalSecretaryInternalResponse findPrincipalSecretaryInternal(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findPrincipalSecretaryInternal"), id);
    }

    public List<PrincipalSecretary> getAllByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllByCriteria"), params);
    }

    public Long countAllByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllByCriteria"), params);
    }
}


