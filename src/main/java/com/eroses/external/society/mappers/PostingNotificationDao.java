package com.eroses.external.society.mappers;

import com.eroses.external.society.model.posting.PostingNotification;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class PostingNotificationDao extends MyBatisDao<PostingNotification> {

    public List<PostingNotification> findByPostingId(Long postingId) {
        return this.sqlSession.selectList(this.sqlId("findByPostingId"), postingId);
    }

    public List<PostingNotification> findByRecipientId(Long recipientId) {
        return this.sqlSession.selectList(this.sqlId("findByRecipientId"), recipientId);
    }

    public List<PostingNotification> findByRecipientIdAndIsRead(Long recipientId, Boolean isRead) {
        Map<String, Object> params = Map.of("recipientId", recipientId, "isRead", isRead);
        return this.sqlSession.selectList(this.sqlId("findByRecipientIdAndIsRead"), params);
    }

    public Boolean markAsRead(Long id) {
        return this.sqlSession.update(this.sqlId("markAsRead"), id) > 0;
    }

    public Boolean markAllAsRead(Long recipientId) {
        return this.sqlSession.update(this.sqlId("markAllAsRead"), recipientId) > 0;
    }
}
