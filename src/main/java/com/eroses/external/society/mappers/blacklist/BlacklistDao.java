package com.eroses.external.society.mappers.blacklist;

import com.eroses.external.society.model.blacklist.Blacklist;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class BlacklistDao extends MyBatisDao<Blacklist> {
    
    public List<Blacklist> findByBlacklistUserId(Long blacklistUserId) {
        return this.sqlSession.selectList(this.sqlId("findByBlacklistUserId"), blacklistUserId);
    }
    
    public List<Blacklist> findBySocietyId(Integer societyId) {
        return this.sqlSession.selectList(this.sqlId("findBySocietyId"), societyId);
    }
    
    public Blacklist findByIdentificationNo(String identificationNo) {
        return this.sqlSession.selectOne(this.sqlId("findByIdentificationNo"), identificationNo);
    }
    
    public List<Blacklist> search(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("search"), params);
    }
    
    public Long countSearch(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countSearch"), params);
    }

    public Long countGetByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countGetByCriteria"), params);
    }

    public Boolean updateRemovalStatus(Long id, Boolean removalStatus) {
        Map<String, Object> params = Map.of(
            "id", id,
            "removalStatus", removalStatus
        );
        return this.sqlSession.update(this.sqlId("updateRemovalStatus"), params) == 1;
    }

    public List<Blacklist> findBySocietyCancellationIdAndIsCompletedAndRemovalStatus(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findBySocietyCancellationIdAndIsCompletedAndRemovalStatus"), params);
    }

    public List<Blacklist> findByIdentificationNoAndIsCompletedAndRemovalStatus(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByIdentificationNoAndIsCompletedAndRemovalStatus"), params);
    }

    public List<Blacklist> findByIdentificationNoAndIsCompletedAndRemovalStatusExcludingId(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByIdentificationNoAndIsCompletedAndRemovalStatusExcludingId"), params);
    }

    public List<Blacklist> findByParam(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getByCriteria"), params);
    }

    public Long countByParam(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countGetByCriteria"), params);
    }

    public List<Long> getAllPendingToProcessBlacklistId(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllPendingToProcessBlacklistId"), params);
    }

    public Boolean updateRemovalStatusByIds(Map<String, Object> params) {
        return this.sqlSession.update(this.sqlId("updateRemovalStatusByIds"), params) > 0;
    }
}
