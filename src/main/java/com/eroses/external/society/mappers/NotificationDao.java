package com.eroses.external.society.mappers;

import com.eroses.external.society.model.EmailTemplate;
import com.eroses.external.society.model.Notification;
import com.eroses.external.society.model.Society;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class NotificationDao extends MyBatisDao<Notification> {
//    Boolean create(Notification notification);
    public Boolean edit(Notification notification){
        return this.sqlSession.update(this.sqlId("edit"), notification) == 1;
    }
    public List<Notification> getAll(){
        return this.sqlSession.selectList(this.sqlId("getAll"));
    }

//    Notification findById(Long id);
    public Notification findByTemplateCode(@Param("templateCode") String templateCode){
        return this.sqlSession.selectOne(this.sqlId("findByTemplateCode"), templateCode);
    }
}
