package com.eroses.external.society.mappers;

import com.eroses.external.society.model.ConstitutionValue;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class ConstitutionValueDao extends MyBatisDao<ConstitutionValue> {
    // Old methods, now commented out
    // Boolean create(ConstitutionValue constitutionvalue);
    // Boolean update(ConstitutionValue constitutionvalue);
    // ConstitutionValue findById(Long id);
    // List<ConstitutionValue> findAll(Map<String, Object> params);
    // List<ConstitutionValue> findByConstitutionContentId(Long constitutionContentId);
    // long countFindAll(Map<String, Object> params);
    // void updateStatus(List<Long> idList, String status, Integer appStatusCode, long userId);

//    public Boolean create(ConstitutionValue constitutionvalue) {
//        return this.sqlSession.insert(this.sqlId("create"), constitutionvalue) > 0;
//    }
//
//    public Boolean update(ConstitutionValue constitutionvalue) {
//        return this.sqlSession.update(this.sqlId("update"), constitutionvalue) > 0;
//    }
//
//    public ConstitutionValue findById(Long id) {
//        return this.sqlSession.selectOne(this.sqlId("findById"), id);
//    }

    public List<ConstitutionValue> findAll(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAll"), params);
    }

    public List<ConstitutionValue> findByConstitutionContentId(Long constitutionContentId) {
        return this.sqlSession.selectList(this.sqlId("findByConstitutionContentId"), constitutionContentId);
    }

    public long countFindAll(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countFindAll"), params);
    }

    public void updateStatus(List<Long> idList, String status, Integer appStatusCode, long userId) {
        this.sqlSession.update(this.sqlId("updateStatus"), Map.of(
                "idList", idList,
                "status", status,
                "appStatusCode", appStatusCode,
                "userId", userId
        ));
    }

    public void hardDelete(List<Long> idList) {
        this.sqlSession.delete(this.sqlId("hardDelete"), idList);
    }

    public ConstitutionValue findByParam(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findByParam"), params);
    }

    public Long updateById(ConstitutionValue constitutionValue) {
        return (long) this.sqlSession.update(this.sqlId("updateById"), constitutionValue);
    }
}

