package com.eroses.external.society.mappers;

import com.eroses.external.society.model.DocumentTemplate;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public class DocumentTemplateDao extends MyBatisDao<DocumentTemplate> {

    public DocumentTemplate findByCode(String templateCode) {
        return this.sqlSession.selectOne(this.sqlId("findByCode"), templateCode);
    }
    // Old methods, now commented out
    // void create(DocumentTemplate documentTemplate);
    // DocumentTemplate findById(Long id);

//    public void create(DocumentTemplate documentTemplate) {
//        this.sqlSession.insert(this.sqlId("create"), documentTemplate);
//    }
//
//    public DocumentTemplate findById(Long id) {
//        return this.sqlSession.selectOne(this.sqlId("findById"), id);
//    }

    public DocumentTemplate findByCode(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findByCode"), params);
    }
}

