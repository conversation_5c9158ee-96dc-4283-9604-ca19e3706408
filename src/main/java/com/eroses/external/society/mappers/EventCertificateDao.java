package com.eroses.external.society.mappers;

import com.eroses.external.society.model.EventCertificate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

@Mapper
public interface EventCertificateDao {
    @Select("SELECT e.event_name as eventName, " +
            "e.event_start_date as startDate, " +
            "e.event_end_date as endDate, " +
            "ea.full_name AS name, " +
            "ea.identification_no as identificationNo " +
            "FROM event e " +
            "INNER JOIN event_attendees ea ON e.id = ea.event_id " +
            "WHERE e.id = #{eventId} AND ea.identification_no = #{identificationNo} LIMIT 1")
    EventCertificate getEventAttendeeShow(@Param("eventId") Long eventId, @Param("identificationNo") String identificationNo);
}
