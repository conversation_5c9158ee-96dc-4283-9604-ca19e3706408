package com.eroses.external.society.mappers;

import com.eroses.external.society.model.lookup.AdmCalendar;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class AdmCalendarDao extends MyBatisDao<AdmCalendar> {

    public List<AdmCalendar> getAll() {
        return this.sqlSession.selectList(this.sqlId("getAll"));
    }

    public List<AdmCalendar> getAllActive() {
        return this.sqlSession.selectList(this.sqlId("getAllActive"));
    }

    public List<AdmCalendar> getAll(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAll"), params);
    }

    public Long countAll(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAll"), params);
    }

    public boolean hasOverlappingHoliday(String holidayType, LocalDate startDate, LocalDate endDate, Long excludeId) {
        Map<String, Object> params = new HashMap<>();
        params.put("holidayType", holidayType);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("excludeId", excludeId);
        
        return this.sqlSession.selectOne(this.sqlId("hasOverlappingHoliday"), params);
    }
}