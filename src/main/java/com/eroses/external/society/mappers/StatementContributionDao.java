package com.eroses.external.society.mappers;

import com.eroses.external.society.model.StatementContribution;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class StatementContributionDao extends MyBatisDao<StatementContribution> {
//    StatementContribution findById(Long id);

//    void create(StatementContribution statementContribution);

//    boolean update(StatementContribution statementContribution);

    public List<StatementContribution> listStatementContributions(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("listStatementContributions"), params);
    }

    public Long countListStatementContributions(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countListStatementContributions"), params);
    }

    public StatementContribution findByStatementId(Long statementId){
        return this.sqlSession.selectOne(this.sqlId("findByStatementId"), statementId);
    }

    public Boolean deleteStatement(Map<String, Object> params) {
        return this.sqlSession.update(this.sqlId("deleteStatement"), params) > 0;
    }

    public StatementContribution findByParam(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findByParam"), params);
    }

    public List<StatementContribution> findAllByParam(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAllByParam"), params);
    }
}
