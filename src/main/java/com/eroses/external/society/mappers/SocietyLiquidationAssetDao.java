package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.societyLiquidation.LiquidationAssetResponse;
import com.eroses.external.society.model.societyLiquidation.SocietyLiquidationAsset;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class SocietyLiquidationAssetDao extends MyBatisDao<SocietyLiquidationAsset> {
    public Long insertMany(List<SocietyLiquidationAsset> assets){
        return (long) this.sqlSession.insert(this.sqlId("insertMany"), assets);
    }
    public List<LiquidationAssetResponse> findByLiquidationId(Long id){
        return this.sqlSession.selectList(this.sqlId("findByLiquidationId"), id);
    }
}