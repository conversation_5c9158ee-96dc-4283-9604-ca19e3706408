package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.request.appeal.AppealSearchRequest;
import com.eroses.external.society.dto.response.AppealCountByType;
import com.eroses.external.society.dto.response.appeal.*;
import com.eroses.external.society.dto.response.roDecision.GetAllPendingAppealResponse;
import com.eroses.external.society.model.Appeal;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class AppealDao extends MyBatisDao<Appeal> {

    // Old methods, now commented out
    // List<Appeal> getAll(Integer offset, Integer limit);
    // Long countAll();
    // Appeal getById(Long id);
    // List<Appeal> getAppealByParam(Map<String, Integer> params);
    // Boolean create(Appeal appeal);
    // Boolean update(Appeal appeal);
    // Appeal getByPaymentId(Long paymentId);
    // List<AppealCountByType> getCountByType();
    // Long countAppealByParams(Map<String, Integer> params);
    // List<GetSearchAppealResponse> searchByName(@Param("request") AppealSearchRequest request, @Param("offset") Integer offset, @Param("limit") Integer limit);
    // Long countSearchAppeal(@Param("request") AppealSearchRequest request);
    // List<GetAllPendingAppealResponse> getAllPendingAppeal(Map<String, Object> params);
    // Integer countAllPendingAppeal(Map<String, Object> params);

    public List<Appeal> getAll(Integer offset, Integer limit) {
        return this.sqlSession.selectList(this.sqlId("getAll"), Map.of("offset", offset, "limit", limit));
    }

    public Long countAll() {
        return this.sqlSession.selectOne(this.sqlId("countAll"));
    }

    public Appeal getById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("getById"), id);
    }

    public List<GetByParamAppealResponse> getAppealByParam(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAppealByParam"), params);
    }
    public List<AppealAdminRecordsResponse> getAllByParams(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllByParams"), params);
    }
    public Long countAllByParams(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllByParams"), params);
    }


//    public Boolean create(Appeal appeal) {
//        return this.sqlSession.insert(this.sqlId("create"), appeal) > 0;
//    }
//
//    public Boolean update(Appeal appeal) {
//        return this.sqlSession.update(this.sqlId("update"), appeal) > 0;
//    }

    public Appeal getByPaymentId(Long paymentId) {
        return this.sqlSession.selectOne(this.sqlId("getByPaymentId"), paymentId);
    }

    public List<AppealCountByType> getCountByType() {
        return this.sqlSession.selectList(this.sqlId("getCountByType"));
    }

    public Long countAppealByParams(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAppealByParams"), params);
    }

    public List<GetSearchAppealResponse> searchByName(AppealSearchRequest request, Integer offset, Integer limit) {
        Map<String, Object> params = Map.of("request", request, "offset", offset, "limit", limit);
        return this.sqlSession.selectList(this.sqlId("searchByName"), params);
    }

    public Long countSearchAppeal(AppealSearchRequest request) {
        return this.sqlSession.selectOne(this.sqlId("countSearchAppeal"), request);
    }

    public List<GetAllPendingAppealResponse> getAllPendingAppeal(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllPendingAppeal"), params);
    }

    public Integer countAllPendingAppeal(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllPendingAppeal"), params);
    }

    public List<GetUserSocietiesForAppealResponse> getAppealsByUser(Map<String, Object> param) {
        return this.sqlSession.selectList(this.sqlId("getAppealsByUser"), param);
    }

    public Long countAppealsByUser(Map<String, Object> param) {
        return this.sqlSession.selectOne(this.sqlId("countAppealsByUser"), param);
    }

    public Boolean removeAppealApplication(Long id) {
        return this.sqlSession.delete(this.sqlId("removeAppealApplication"), id) > 0;
    }

    public List<Appeal> findByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByCriteria"), params);
    }

    public List<Long> getAllExpiredUnpaidSocietyAppealId(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllExpiredUnpaidSocietyAppealId"), params);
    }

    public Boolean updateAppealStatusByAppealIds(Map<String, Object> params){
        return this.sqlSession.update(this.sqlId("updateAppealStatusByAppealIds"), params) > 0;
    }

    public List<GetAllDecisionRecordAppealResponse> getAllDecisionRecordAppeal(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllDecisionRecordAppeal"), params);
    }

    public Long countAllDecisionRecordAppeal(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllDecisionRecordAppeal"), params);
    }

    public AppealAdminRecordByIdResponse getAdminRecord(Long appealId) {
        return this.sqlSession.selectOne(this.sqlId("getAdminRecord"), appealId);
    }

    public List<GetByParamAppealResponse> getAppealsByParamEnhanced(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAppealsByParamEnhanced"), params);
    }

    public Long countAppealsByParamEnhanced(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAppealsByParamEnhanced"), params);
    }
}

