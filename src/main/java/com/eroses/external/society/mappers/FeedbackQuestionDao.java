package com.eroses.external.society.mappers;

import com.eroses.external.society.model.FeedbackQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface FeedbackQuestionDao {
    List<FeedbackQuestion> findAll();
    List<FeedbackQuestion> findByIds(List<Long> ids);
//    List<FeedbackQuestion> findByEventId(Long id);
    FeedbackQuestion findById(Long id);
    int create(FeedbackQuestion question);
    Boolean update(FeedbackQuestion question);

}
