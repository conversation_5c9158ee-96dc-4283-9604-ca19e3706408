package com.eroses.external.society.mappers;

import com.eroses.external.society.model.TrainingCertificate;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class TrainingCertificateDao extends MyBatisDao<TrainingCertificate> {

//    public boolean create(TrainingCertificate trainingCertificate) {
//        return this.sqlSession.insert(this.sqlId("create"), trainingCertificate) == 1;
//    }
//
//    public boolean update(TrainingCertificate trainingCertificate) {
//        return this.sqlSession.update(this.sqlId("update"), trainingCertificate) == 1;
//    }

    public TrainingCertificate findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }

    public TrainingCertificate findByEnrollmentId(Long trainingEnrollmentId) {
        return this.sqlSession.selectOne(this.sqlId("findByEnrollmentId"), trainingEnrollmentId);
    }

    public TrainingCertificate findByVerificationCode(String verificationCode) {
        return this.sqlSession.selectOne(this.sqlId("findByVerificationCode"), verificationCode);
    }

    public List<TrainingCertificate> findAllByUserId(Long userId) {
        return this.sqlSession.selectList(this.sqlId("findAllByUserId"), userId);
    }
}
