package com.eroses.external.society.mappers;

import com.eroses.external.society.model.RoAmendmentApproval;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class RoAmendmentApprovalDao extends MyBatisDao<RoAmendmentApproval> {
    public List<RoAmendmentApproval> get(Map<String, Object> param){
        return this.sqlSession.selectList(this.sqlId("get"), param);
    }

    public RoAmendmentApproval getById(Long id){
        return this.sqlSession.selectOne(this.sqlId("getById"), id);
    }

//    Long create(RoAmendmentApproval roAmendmentApproval);

//    RoAmendmentApproval update(RoAmendmentApproval roAmendmentApproval);
}
