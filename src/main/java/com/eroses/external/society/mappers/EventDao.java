package com.eroses.external.society.mappers;

import com.eroses.external.society.model.Event;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Mapper
@Repository
public interface EventDao {
    List<Event> findAll(Integer year);
    Event findById(Long id);
    Event findByEventNo(String eventNo);

    Long findIdByEventNo(String eventNo);
    <PERSON>olean create(Event event);
    int update(Event event);
    Optional<Integer> getNextSequence(String yearMont);

    Boolean insertSequence(String nextSequence);

    int updateSequence(String yearMonth);

    List<Event> findAllPublished(Integer year);
    List<Event> findPastEvents(Integer year);
    Boolean delete(Long id);
}
