package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.request.auditTrail.UserAuditTrailReadRequest;
import com.eroses.external.society.dto.response.auditTrail.AuditTrailPagingResponse;
import com.eroses.external.society.model.AuditTrail;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Map;

@Repository
public class AuditTrailDao extends MyBatisDao<AuditTrail> {

    // Old methods, now commented out
    // void create(AuditTrail auditTrail);
    // int update(AuditTrail auditTrail);
    // List<AuditTrailPagingResponse> findAllByParams(Map<String, Object> params);
    // long countByIdNo(Map<String, Object> params);

//    public void create(AuditTrail auditTrail) {
//        this.sqlSession.insert(this.sqlId("create"), auditTrail);
//    }
//
//    public int update(AuditTrail auditTrail) {
//        return this.sqlSession.update(this.sqlId("update"), auditTrail);
//    }

    public List<AuditTrailPagingResponse> findAllByParams(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAllByParams"), params);
    }

    public long countByIdNo(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countByIdNo"), params);
    }

    public List<AuditTrail> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

    public AuditTrail findAuditTrailForUser(UserAuditTrailReadRequest request) {
        return this.sqlSession.selectOne(this.sqlId("findAuditTrailForUser"), request);
    }
}

