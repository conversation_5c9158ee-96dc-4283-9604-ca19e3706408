package com.eroses.external.society.mappers;

import com.eroses.external.society.model.EventFeedbackScale;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface EventFeedbackScaleDao {
    List<EventFeedbackScale> findAll();
    EventFeedbackScale findById(Long id);
    EventFeedbackScale findByCode(String code);
    Boolean create(EventFeedbackScale eventFeedbackScale);
    int update(EventFeedbackScale eventFeedbackScale);
    int delete(Long id);
}