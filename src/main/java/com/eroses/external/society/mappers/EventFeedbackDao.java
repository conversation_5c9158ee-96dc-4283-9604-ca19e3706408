package com.eroses.external.society.mappers;

import com.eroses.external.society.model.EventFeedback;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class EventFeedbackDao extends MyBatisDao<EventFeedback> {
    public int insert(List<EventFeedback> feedback) {
        return this.sqlSession.insert(this.sqlId("insert"), feedback);

    }

    public List<EventFeedback> findByAttendeesId(Long attendeesId){
        return this.sqlSession.selectList(this.sqlId("findByAttendeesId"), attendeesId);
    }
}
