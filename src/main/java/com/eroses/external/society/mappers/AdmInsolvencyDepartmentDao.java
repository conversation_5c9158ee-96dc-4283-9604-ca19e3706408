package com.eroses.external.society.mappers;

import com.eroses.external.society.model.lookup.AdmInsolvencyDepartment;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class AdmInsolvencyDepartmentDao extends MyBatisDao<AdmInsolvencyDepartment> {

    public List<AdmInsolvencyDepartment> getAll() {
        return this.sqlSession.selectList(this.sqlId("getAll"));
    }

    public List<AdmInsolvencyDepartment> getAllActive() {
        return this.sqlSession.selectList(this.sqlId("getAllActive"));
    }

    public List<AdmInsolvencyDepartment> getAll(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAll"), params);
    }

    public Long countAll(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAll"), params);
    }

    @Override
    public Boolean delete(Long id) {
        return this.sqlSession.update(this.sqlId("delete"), id) == 1;
    }

    public boolean existsByCode(String code) {
        return this.sqlSession.selectOne(this.sqlId("existsByCode"), code);
    }

    public boolean existsByCodeExcludingId(String code, Long id) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("id", id);
        return this.sqlSession.selectOne(this.sqlId("existsByCodeExcludingId"), params);
    }

    public AdmInsolvencyDepartment findByStateCode(String stateCode) {
        return this.sqlSession.selectOne(this.sqlId("findByStateCode"), stateCode);
    }
}