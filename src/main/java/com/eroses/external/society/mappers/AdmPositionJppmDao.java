package com.eroses.external.society.mappers;

import com.eroses.external.society.model.AdmPositionJppm;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class AdmPositionJppmDao extends MyBatisDao<AdmPositionJppm> {

    public List<AdmPositionJppm> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

    public List<AdmPositionJppm> getAll() {
        return this.sqlSession.selectList(this.sqlId("getAll"));
    }

    public List<AdmPositionJppm> getAllActive() {
        return this.sqlSession.selectList(this.sqlId("getAllActive"));
    }

    public List<AdmPositionJppm> getAll(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAll"), params);
    }

    public Long countAll(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAll"), params);
    }

    public boolean existsByGradeAndName(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsByGradeAndName"), params);
    }

    public boolean existsByGradeAndNameExcludingId(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsByGradeAndNameExcludingId"), params);
    }
}
