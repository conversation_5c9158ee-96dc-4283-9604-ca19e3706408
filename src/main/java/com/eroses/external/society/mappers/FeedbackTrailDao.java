package com.eroses.external.society.mappers;

import com.eroses.external.society.model.FeedbackTrail;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class FeedbackTrailDao extends MyBatisDao<FeedbackTrail> {
    public List<FeedbackTrail> getAllByCriteria(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("getAllByCriteria"), params);
    }
}
