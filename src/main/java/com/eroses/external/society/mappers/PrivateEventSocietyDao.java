package com.eroses.external.society.mappers;

import com.eroses.external.society.model.PrivateEventSociety;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface PrivateEventSocietyDao {
    List<PrivateEventSociety> findAll();
    PrivateEventSociety findById(Long id);
    List<PrivateEventSociety> findByEventId(Long eventId);
    List<PrivateEventSociety> findBySocietyId(Long societyId);
    int create(PrivateEventSociety privateEventSociety);
    int createMultiples(List<PrivateEventSociety> privateEventSocieties);
    Boolean update(PrivateEventSociety privateEventSociety);
    void deleteBySocietyIdAndEventId(List<PrivateEventSociety> privateEventSocieties);
    void deleteByEventIdAndManualSelection(Long eventId, Boolean manualSelection);
    void deleteByEventId(Long eventId);

}



