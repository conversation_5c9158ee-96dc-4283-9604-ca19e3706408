package com.eroses.external.society.mappers;

import com.eroses.external.society.model.PrincipalSecretaryRoApproval;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;


import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class PrincipalSecretaryRoApprovalDao extends MyBatisDao<PrincipalSecretaryRoApproval> {
    public Boolean updatePrincipalSecretaryRoApproval(PrincipalSecretaryRoApproval principalSecretaryRoApproval){
        return this.sqlSession.update(this.sqlId("updatePrincipalSecretaryRoApproval"), principalSecretaryRoApproval) == 1;
    }
    public PrincipalSecretaryRoApproval updatePrincipalSecretaryApprovalDecision(PrincipalSecretaryRoApproval principalSecretaryRoApproval){
        return this.sqlSession.update(this.sqlId("updatePrincipalSecretaryApprovalDecision"), principalSecretaryRoApproval) == 1 ? principalSecretaryRoApproval : null;
    }
    public List<PrincipalSecretaryRoApproval> getAll(){
        return this.sqlSession.selectList(this.sqlId("getAll"));
    }
    public List<PrincipalSecretaryRoApproval> search(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("search"), params);
    }
    public boolean existsById(Long id){
        return this.sqlSession.selectOne(this.sqlId("existsById"), id) != null;
    }
}
