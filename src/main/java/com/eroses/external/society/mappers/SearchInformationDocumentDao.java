package com.eroses.external.society.mappers;

import com.eroses.external.society.model.SearchInformationDocument;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class SearchInformationDocumentDao extends MyBatisDao<SearchInformationDocument> {

    public List<SearchInformationDocument> getAllBySearchInformationId(Long searchInformationId){
        return this.sqlSession.selectList(this.sqlId("getAllBySearchInformationId"), searchInformationId);
    }

    public List<Long> getAllExpiredSearchInformationDocumentId(int applicationStatusCode){
        return this.sqlSession.selectList(this.sqlId("getAllExpiredSearchInformationDocumentId"), applicationStatusCode);
    }

    public Boolean updateExpiredSearchInformationDocument(Map<String, Object> params){
        return this.sqlSession.update(this.sqlId("updateExpiredSearchInformationDocument"), params) > 0;
    }
}