package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.appeal.GetUserSocietiesForAppealResponse;
import com.eroses.external.society.dto.response.branch.BranchBasicInfoResponse;
import com.eroses.external.society.dto.response.branchCommittee.BranchSecretaryResponse;
import com.eroses.external.society.model.Branch;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class BranchDao extends MyBatisDao<Branch> {
//    Boolean create(Branch branchDetail);

    public boolean existByBranchName(String name, Long societyId) {
        Map<String, Object> params = new HashMap<>();
        params.put("name", name);
        if (societyId != null) {
            params.put("societyId", societyId);
        }
        return this.sqlSession.selectOne(this.sqlId("existByBranchName"), params);
    }

//    Boolean update(Branch branch);

    public List<Branch> findAll(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAll"), params);
    }

    public List<Branch> findAllByParams(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAllByParams"), params);
    }

    public Long countAllByParams(Map<String, Object> param){
        return this.sqlSession.selectOne(this.sqlId("countAllByParams"), param);
    }

    public Branch findBranchNo(Long id){
        return this.sqlSession.selectOne(this.sqlId("findAllByParams"), id);
    }

    public Branch getByApplicationNo(String applicationNo){
        return this.sqlSession.selectOne(this.sqlId("getByApplicationNo"), applicationNo);
    }

    public Branch getByPaymentId(Long paymentId){
        return this.sqlSession.selectOne(this.sqlId("getByPaymentId"), paymentId);
    }

//    Branch findById(Long id);

    public List<BranchBasicInfoResponse> findBasicInfo(Long societyId){
        return this.sqlSession.selectList(this.sqlId("findBasicInfo"), societyId);
    }

    public List<BranchSecretaryResponse> getBranchSecretaries(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("getBranchSecretaries"), params);
    }

    public Long getBranchSecretariesCount(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("getBranchSecretariesCount"), params);
    }

    public Long countAllBranch(Map<String, Object> param){
        return this.sqlSession.selectOne(this.sqlId("countAllBranch"), param);
    }

    public Boolean updateBranchApprovalRo(Branch branch){
        return this.sqlSession.update(this.sqlId("updateBranchApprovalRo"), branch) == 1;
    }

    public List<Branch> findAllExtendingBranch(){
        return this.sqlSession.selectList(this.sqlId("findAllExtendingBranch"));
    }

    public Boolean updateBranchApprovalDecision(Branch branch){
        return this.sqlSession.update(this.sqlId("updateBranchApprovalDecision"), branch) == 1;
    }

    public Boolean updateBranchExtension(Branch branch){
        return this.sqlSession.update(this.sqlId("updateBranchExtension"), branch) == 1;
    }

    public String findLastBranchNo(String societyNo){
        return this.sqlSession.selectOne(this.sqlId("findLastBranchNo"), societyNo);
    }

    public List<Branch> findBySocietyId(Long societyId){
        return this.sqlSession.selectList(this.sqlId("findBySocietyId"), societyId);
    }

    public List<Branch> findAllPending(Map<String, Integer> params){
        return this.sqlSession.selectList(this.sqlId("findAllPending"), params);
    }

    public Long countAllPending(int applicationStatusCode){
        return this.sqlSession.selectOne(this.sqlId("countAllPending"), applicationStatusCode);
    }

    public List<Branch> findAllPendingUserStateCode(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAllPendingUserStateCode"), params);
    }

    public Long countAllPendingUserStateCode(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllPendingUserStateCode"), params);
    }

    public int countBranchRegisteredToday(String currDate){
        return this.sqlSession.selectOne(this.sqlId("countBranchRegisteredToday"), currDate);
    }

    public int countStatusCode(String statusCode){
        return this.sqlSession.selectOne(this.sqlId("countStatusCode"), statusCode);
    }

    public int countApplicationStatusCode(int applicationStatusCode){
        return this.sqlSession.selectOne(this.sqlId("countApplicationStatusCode"), applicationStatusCode);
    }

    public int countApplicationStatusCodeAndPaymentDate(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countApplicationStatusCodeAndPaymentDate"), params);
    }

    public List<Branch> findByBranchIdOrStatusCode(Map<String, Integer> param){
        return this.sqlSession.selectList(this.sqlId("findByBranchIdOrStatusCode"), param);
    }

    public Long countBranchStatusInSociety(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countBranchStatusInSociety"), params);
    }

    public List<Branch> getAllPendingByCriteria(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("getAllPendingByCriteria"), params);
    }

    public Integer countAllPendingByCriteria(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("countAllPendingByCriteria"), params);
    }

    public Boolean isBranchNameExist(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("isBranchNameExist"), params);
    }

    public List<Long> getAllExpiredBranchApplicationId(int applicationStatusCode){
        return this.sqlSession.selectList(this.sqlId("getAllExpiredBranchApplicationId"), applicationStatusCode);
    }

    public Boolean updateApplicationStatusCodeByIds(Map<String, Object> params){
        return this.sqlSession.update(this.sqlId("updateApplicationStatusCodeByIds"), params) > 0;
    }

    public Boolean updateStatusCodeAndSubStatusCodeByIds(Map<String, Object> params){
        return this.sqlSession.update(this.sqlId("updateStatusCodeAndSubStatusCodeByIds"), params) > 0;
    }

    public List<GetUserSocietiesForAppealResponse> findBranchForAppeal(Map<String, Object> param) {
        return this.sqlSession.selectList(this.sqlId("findBranchForAppeal"), param);
    }

    public List<Branch> findBranchesByDetailedParam(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findBranchesByDetailedParam"), params);
    }

    public Long countBranchesByDetailedParam(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countBranchesByDetailedParam"), params);
    }

    public List<Branch> findAllBySocietyIdAndApplicationStatusCodeAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAllBySocietyIdAndApplicationStatusCodeAndStatus"), params);
    }

    public List<Branch> findAllBySocietyIdByStatusAndSubStatusCode(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAllBySocietyIdByStatusAndSubStatusCode"), params);
    }

    public List<Branch> findAllByIds(List<Long> ids) {
        return this.sqlSession.selectList(this.sqlId("findAllByIds"), ids);
    }

    public List<Branch> getAllBranchPendingApproval (Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getAllBranchPendingApproval"), params);
    }

    public List<Long> findBranchIdListByIdentificationNo(String identificationNo) {
        return this.sqlSession.selectList(this.sqlId("findBranchIdListByIdentificationNo"), identificationNo);
    }

    public List<Branch> getBranchesByCriteriaAndExcludingActiveSocietyCancellationBranches(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("getBranchesByCriteriaAndExcludingActiveSocietyCancellationBranches"), params);
    }

    public Long countBranchesByCriteriaAndExcludingActiveSocietyCancellationBranches(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countBranchesByCriteriaAndExcludingActiveSocietyCancellationBranches"), params);
    }
}
