package com.eroses.external.society.mappers;

import com.eroses.external.society.model.StatementFinancial;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class StatementFinancialDao extends MyBatisDao<StatementFinancial> {
//    boolean create(StatementFinancial statementFinancial);

//    boolean update(StatementFinancial statementFinancial);

//    StatementFinancial findById(Long id);

    public List<StatementFinancial> findAll(Map<String, Object> params){
        return this.sqlSession.selectList(this.sqlId("findAll"), params);
    }

    public long countFindAll(){
        return this.sqlSession.selectOne(this.sqlId("countFindAll"));
    }

    public StatementFinancial findOneBySocietyIdAndStatementId(Map<String, Object> params){
        return this.sqlSession.selectOne(this.sqlId("findOneBySocietyIdAndStatementId"), params);
    }

    public StatementFinancial findByStatementId(Long statementId){
        return this.sqlSession.selectOne(this.sqlId("findByStatementId"), statementId);
    }

    public Boolean deleteStatement(Map<String, Object> params) {
        return this.sqlSession.update(this.sqlId("deleteStatement"), params) > 0;
    }

    public boolean isExistsByStatementId(Long id) {
        return this.sqlSession.selectOne(this.sqlId("isExistsByStatementId"), id);
    }

    public StatementFinancial findByParam(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findByParam"), params);
    }
}
