package com.eroses.external.society.mappers;

import com.eroses.external.society.model.ConstitutionType;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ConstitutionTypeDao extends MyBatisDao<ConstitutionType> {
    // Old methods, now commented out
    // List<ConstitutionType> findAll();
    // ConstitutionType findByName(String name);

    public List<ConstitutionType> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

    public ConstitutionType findByName(String name) {
        return this.sqlSession.selectOne(this.sqlId("findByName"), name);
    }
}
