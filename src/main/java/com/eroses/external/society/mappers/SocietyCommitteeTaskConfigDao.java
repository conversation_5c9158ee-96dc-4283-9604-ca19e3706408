package com.eroses.external.society.mappers;

import com.eroses.external.society.model.SocietyCommitteeTaskConfig;
import com.eroses.external.society.model.enums.CommitteeTaskModule;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public class SocietyCommitteeTaskConfigDao extends MyBatisDao<SocietyCommitteeTaskConfig> {

    public int enableCommitteeTaskForModule(Long societyId, CommitteeTaskModule module) {
        return this.sqlSession.insert(this.sqlId("insertCommitteeTaskForModule"), Map.of("societyId", societyId, "module", module.getCode()));
    }

    public int disableCommitteeTaskForModule(Long societyId, CommitteeTaskModule module) {
        return this.sqlSession.delete(this.sqlId("removeCommitteeTaskForModule"), Map.of("societyId", societyId, "module", module.getCode()));
    }

    public boolean isCommitteeTaskEnabled(Long societyId, CommitteeTaskModule module) {
        return this.sqlSession.selectOne(this.sqlId("findCommitteeTaskForModule"), Map.of("societyId", societyId, "module", module.getCode())) != null;

    }

}
