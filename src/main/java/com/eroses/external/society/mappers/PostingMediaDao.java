package com.eroses.external.society.mappers;

import com.eroses.external.society.model.posting.PostingMedia;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class PostingMediaDao extends MyBatisDao<PostingMedia> {

    public List<PostingMedia> findByPostingId(Long postingId) {
        return this.sqlSession.selectList(this.sqlId("findByPostingId"), postingId);
    }

    public Boolean deleteByPostingId(Long postingId) {
        int result = this.sqlSession.delete(this.sqlId("deleteByPostingId"), postingId);
        // Return true even if no records were deleted (result == 0)
        // This is because we want the operation to be considered successful
        // even if there were no media records to delete
        return true;
    }
}
