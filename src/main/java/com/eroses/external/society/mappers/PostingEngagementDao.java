package com.eroses.external.society.mappers;

import com.eroses.external.society.model.posting.PostingEngagement;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class PostingEngagementDao extends MyBatisDao<PostingEngagement> {

    public List<PostingEngagement> findByPostingId(Long postingId) {
        return this.sqlSession.selectList(this.sqlId("findByPostingId"), postingId);
    }

    public List<PostingEngagement> findByUserId(Long userId) {
        return this.sqlSession.selectList(this.sqlId("findByUserId"), userId);
    }

    public List<PostingEngagement> findByUserIdAndPostingId(Long userId, Long postingId) {
        Map<String, Object> params = Map.of("userId", userId, "postingId", postingId);
        return this.sqlSession.selectList(this.sqlId("findByUserIdAndPostingId"), params);
    }

    public List<PostingEngagement> findByEngagementType(String engagementType) {
        return this.sqlSession.selectList(this.sqlId("findByEngagementType"), engagementType);
    }

    public Long countByPostingIdAndEngagementType(Long postingId, String engagementType) {
        Map<String, Object> params = Map.of("postingId", postingId, "engagementType", engagementType);
        return this.sqlSession.selectOne(this.sqlId("countByPostingIdAndEngagementType"), params);
    }

    public Map<String, Long> getViewsByState() {
        return this.sqlSession.selectMap(this.sqlId("getViewsByState"), "state", "count");
    }

    public Map<String, Long> getViewsByDate(Map<String, Object> params) {
        List<Map<String, Object>> results = this.sqlSession.selectList(this.sqlId("getViewsByDate"), params);
        Map<String, Long> viewsByDate = new HashMap<>();
        for (Map<String, Object> result : results) {
            String date = (String) result.get("date");
            Long count = ((Number) result.get("count")).longValue();
            viewsByDate.put(date, count);
        }
        return viewsByDate;
    }
}
