package com.eroses.external.society.mappers;

import com.eroses.external.society.model.EventAttendees;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface EventAttendeesDao {
    List<EventAttendees> findAll();
    EventAttendees findById(Long id);
    List<EventAttendees> findByIdentificationNo(String idNo);
    List<EventAttendees> findByEventId (Long eventId);
    List<EventAttendees> findAttendeesSubmittedFeedbackByEventId (Long eventId);
    List<EventAttendees> findCanceledByEventId (Long eventId);
    EventAttendees findByEventIdAndIdentificationNo (String identificationNo, Long eventId);
    EventAttendees findByEventIdAndIdentificationNoIncludeCancelled (String identificationNo, Long eventId);
    EventAttendees findByAttendanceNo (String attendanceNo);
    int create(EventAttendees eventAttendees);
    Boolean update(EventAttendees eventAttendees);
    Boolean delete(Long id);
    boolean cancelAttendance(Long id);
    boolean deleteByEventId(Long eventId);
}
