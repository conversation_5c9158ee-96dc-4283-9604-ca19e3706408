package com.eroses.external.society.mappers;

import com.eroses.external.society.model.EmailTemplate;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class EmailTemplateDao extends MyBatisDao<EmailTemplate> {
    // Old methods, now commented out
    // Boolean create(EmailTemplate emailTemplate);
    // Boolean edit(EmailTemplate emailTemplate);
    // EmailTemplate findById(Long id);
    // List<EmailTemplate> getAll();
    // EmailTemplate findByTemplateCode(String templateCode);

//    public Boolean create(EmailTemplate emailTemplate) {
//        return this.sqlSession.insert(this.sqlId("create"), emailTemplate) > 0;
//    }

    public Boolean edit(EmailTemplate emailTemplate) {
        return this.sqlSession.update(this.sqlId("edit"), emailTemplate) > 0;
    }

//    public EmailTemplate findById(Long id) {
//        return this.sqlSession.selectOne(this.sqlId("findById"), id);
//    }

    public List<EmailTemplate> getAll() {
        return this.sqlSession.selectList(this.sqlId("getAll"));
    }

    public EmailTemplate findByTemplateCode(String templateCode) {
        return this.sqlSession.selectOne(this.sqlId("findByTemplateCode"), templateCode);
    }
}

