package com.eroses.external.society.mappers;

import com.eroses.external.society.dto.response.branchCommittee.ActiveBranchSecretaryResponse;
import com.eroses.external.society.dto.response.branchCommittee.BranchCommitteeBasicResponse;
import com.eroses.external.society.dto.response.branchCommittee.BranchCommitteeDetailResponse;
import com.eroses.external.society.model.BranchCommittee;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Repository
public class BranchCommitteeDao extends MyBatisDao<BranchCommittee> {

    public void insert(List<BranchCommittee> list) {
        this.sqlSession.insert(this.sqlId("insert"), list);
    }

    public BranchCommittee findByBranchId(Long branchId) {
        return this.sqlSession.selectOne(this.sqlId("findByBranchId"), branchId);
    }

    public ActiveBranchSecretaryResponse findActiveBranchCommittee(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findActiveBranchCommittee"), params);
    }

    public List<BranchCommittee> findAllByBranchId(Long branchId) {
        return this.sqlSession.selectList(this.sqlId("findAllByBranchId"), branchId);
    }

    public List<BranchCommittee> findByIcNo(String icNo) {
        return this.sqlSession.selectList(this.sqlId("findByIcNo"), icNo);
    }

    public List<BranchCommittee> findByIds(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByIds"), params);
    }

    public List<BranchCommittee> findAllByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAllByCriteria"), params);
    }

    public Long countAllByCriteria(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countAllByCriteria"), params);
    }

    public List<Long> findBranchIdListByIdentificationNo(String identificationNo) {
        return this.sqlSession.selectList(this.sqlId("findBranchIdListByIdentificationNo"), identificationNo);
    }

    public List<BranchCommittee> findByIdentificationNo(String identificationNo) {
        return this.sqlSession.selectList(this.sqlId("findByIdentificationNo"), identificationNo);
    }

    public List<BranchCommittee> findByBranchIdAndApplicationStatusCodeAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByBranchIdAndApplicationStatusCodeAndStatus"), params);
    }

    public BranchCommittee findByBranchIdAndIdentificationNoAndApplicationStatusCodeAndStatus(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findByBranchIdAndIdentificationNoAndApplicationStatusCodeAndStatus"), params);
    }

    public BranchCommittee findByBranchIdAndIdentificationNo(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findByBranchIdAndIdentificationNo"), params);
    }

    public List<BranchCommitteeBasicResponse> findAll(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findAll"), params);
    }
    public long countFindAll(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("countFindAll"), params);
    }

    public BranchCommitteeDetailResponse findDetail(long id) {
        return this.sqlSession.selectOne(this.sqlId("findDetail"), id);
    }

    public Long countByBranchId(Long branchId) {
        return this.sqlSession.selectOne(this.sqlId("countByBranchId"), branchId);
    }

    public List<BranchCommittee> listBranchMember(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("listBranchMember"), params);
    }

    public void updateBlacklistStatusByIdentificationNo(Map<String, Object> params) {
        this.sqlSession.update(this.sqlId("updateBlacklistStatusByIdentificationNo"), params);
    }

    public boolean existsByParam(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("existsByParam"), params);
    }

    public List<BranchCommittee> findByIdentificationNoAndActiveUserAndActiveBranch(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByIdentificationNoAndActiveUserAndActiveBranch"), params);
    }

    public List<BranchCommittee> findByParam(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findByParam"), params);
    }

    public BranchCommittee findActiveCommitteeInBranchWithRoles(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findActiveCommitteeInBranchWithRoles"), params);
    }

    public List<BranchCommittee> findActiveCommitteesInBranchWithRoles(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findActiveCommitteesInBranchWithRoles"), params);
    }

    public List<BranchCommittee> findActiveCommitteesInBranchWithRolesByBranchIds(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findActiveCommitteesInBranchWithRolesByBranchIds"), params);
    }

    public Boolean updateStatus(List<Long> committeeIds, String status, int applicationStatusCode) {
        return this.sqlSession.update(this.sqlId("updateStatus"), Map.of(
                "committeeIds", committeeIds,
                "status", status,
                "applicationStatusCode", applicationStatusCode
        )) > 0;
    }

    public BranchCommittee findOneByParams(Map<String, Object> params) {
        return this.sqlSession.selectOne(this.sqlId("findOneByParams"), params);
    }

    public List<LocalDate> findAllAppointedDates(Long branchId) {
        return this.sqlSession.selectList(this.sqlId("findAllAppointedDates"), branchId);
    }

    public void updateOldIdAndAppointedDate(BranchCommittee updateCommittee) {
        this.sqlSession.update(this.sqlId("updateOldIdAndAppointedDate"), updateCommittee);
    }

    public List<BranchCommittee> findActiveByIdentificationNo(Map<String, Object> params) {
        return this.sqlSession.selectList(this.sqlId("findActiveByIdentificationNo"), params);
    }

    public void saveAll(List<BranchCommittee> currentCommitteeList) {
        this.sqlSession.update(this.sqlId("saveAll"), currentCommitteeList);
    }
}
