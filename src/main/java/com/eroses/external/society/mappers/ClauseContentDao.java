package com.eroses.external.society.mappers;

import com.eroses.external.society.model.ClauseContent;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class ClauseContentDao extends MyBatisDao<ClauseContent> {

    // Old methods, now commented out
    // List<ClauseContent> findAll();
    // List<ClauseContent> findByConstitutionTypeId(Long constitutionTypeId);
    // ClauseContent getByConsTypeAndClauseNo(Long constitutionType, Long clauseNo);
    // Long countAllByConstitutionTypeId(Long constitutionTypeId);

    public List<ClauseContent> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }

    public List<ClauseContent> findByConstitutionTypeId(Long constitutionTypeId) {
        return this.sqlSession.selectList(this.sqlId("findByConstitutionTypeId"), constitutionTypeId);
    }

    public ClauseContent getByConsTypeAndClauseNo(Long constitutionType, Long clauseNo) {
        return this.sqlSession.selectOne(this.sqlId("getByConsTypeAndClauseNo"),
                Map.of("constitutionType", constitutionType, "clauseNo", clauseNo));
    }

    public Long countAllByConstitutionTypeId(Long constitutionTypeId) {
        return this.sqlSession.selectOne(this.sqlId("countAllByConstitutionTypeId"), constitutionTypeId);
    }

    public ClauseContent getByConsTypeAndClauseName(Long constitutionType, String clauseName) {
        return this.sqlSession.selectOne(this.sqlId("getByConsTypeAndClauseName"), Map.of("constitutionType", constitutionType, "clauseName", clauseName));
    }
}




