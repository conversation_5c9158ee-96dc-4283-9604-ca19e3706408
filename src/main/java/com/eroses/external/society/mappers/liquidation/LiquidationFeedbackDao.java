package com.eroses.external.society.mappers.liquidation;

import com.eroses.external.society.dto.response.societyLiquidation.LiquidationFeedbackResponse;
import com.eroses.external.society.model.societyLiquidation.LiquidationFeedback;
import com.eroses.mysql.dao.MyBatisDao;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class LiquidationFeedbackDao extends MyBatisDao<LiquidationFeedback> {
//    void create(LiquidationFeedback liquidationFeedback);
//    void update(LiquidationFeedback liquidationFeedback);

    // Old method, now commented out
    // List<LiquidationFeedbackResponse> findByLiquidationId(Long liquidationId);
    public List<LiquidationFeedbackResponse> findByLiquidationId(Long liquidationId) {
        return this.sqlSession.selectList(this.sqlId("findByLiquidationId"), liquidationId);
    }
}

