package com.eroses.external.society.mappers;

import java.util.List;

import com.eroses.external.society.model.AdmCategory;
import com.eroses.mysql.dao.MyBatisDao;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

@Repository
public class AdmCategoryDao extends MyBatisDao<AdmCategory> {

    // Old method, now commented out
    // List<AdmCategory> findAll();
    public List<AdmCategory> findAll() {
        return this.sqlSession.selectList(this.sqlId("findAll"));
    }
}



