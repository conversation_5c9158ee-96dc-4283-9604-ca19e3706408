package com.eroses.external.society.mappers;

import com.eroses.external.society.model.payment.PaymentCallback;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class PaymentCallbackDao extends MyBatisDao<PaymentCallback> {
    public List<PaymentCallback> select(){
        return this.sqlSession.selectList(this.sqlId("select"));
    }
    public int save(PaymentCallback paymentCallback){
        return this.sqlSession.insert(this.sqlId("save"), paymentCallback);
    }
    public PaymentCallback findByCriteria(PaymentCallback paymentCallback){
        return this.sqlSession.selectOne(this.sqlId("findByCriteria"), paymentCallback);
    }
}
