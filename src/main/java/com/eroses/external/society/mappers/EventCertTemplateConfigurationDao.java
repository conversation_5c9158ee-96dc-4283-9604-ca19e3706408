package com.eroses.external.society.mappers;

import com.eroses.external.society.model.EventCertTemplateConfiguration;
import com.eroses.mysql.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

@Repository
public class EventCertTemplateConfigurationDao extends MyBatisDao<EventCertTemplateConfiguration> {
    public EventCertTemplateConfiguration findByTemplateCode(String templateCode) {
        return this.sqlSession.selectOne(this.sqlId("findByTemplateCode"), templateCode);
    }
}
