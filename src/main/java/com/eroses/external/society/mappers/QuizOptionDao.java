package com.eroses.external.society.mappers;

import com.eroses.external.society.model.QuizOption;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class QuizOptionDao extends MyBatisDao<QuizOption> {

//    public boolean create(QuizOption quizOption) {
//        return this.sqlSession.insert(this.sqlId("create"), quizOption) == 1;
//    }

    public boolean createBatch(List<QuizOption> quizOptions) {
        Map<String, Object> params = new HashMap<>();
        params.put("options", quizOptions);
        return this.sqlSession.insert(this.sqlId("createBatch"), params) > 0;
    }

//    public boolean update(QuizOption quizOption) {
//        return this.sqlSession.update(this.sqlId("update"), quizOption) == 1;
//    }
//
//    public boolean delete(Long id) {
//        return this.sqlSession.delete(this.sqlId("delete"), id) == 1;
//    }

    public boolean deleteAllByQuestionId(Long quizQuestionId) {
        return this.sqlSession.delete(this.sqlId("deleteAllByQuestionId"), quizQuestionId) >= 0;
    }

    public QuizOption findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }

    public List<QuizOption> findAllByQuestionId(Long quizQuestionId) {
        return this.sqlSession.selectList(this.sqlId("findAllByQuestionId"), quizQuestionId);
    }

    public QuizOption findCorrectOptionByQuestionId(Long quizQuestionId) {
        return this.sqlSession.selectOne(this.sqlId("findCorrectOptionByQuestionId"), quizQuestionId);
    }

    public boolean updateSequenceOrders(List<QuizOption> options) {
        Map<String, Object> params = new HashMap<>();
        params.put("options", options);
        return this.sqlSession.update(this.sqlId("updateSequenceOrders"), params) > 0;
    }
}
