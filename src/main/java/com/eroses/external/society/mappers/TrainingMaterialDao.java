package com.eroses.external.society.mappers;

import com.eroses.external.society.model.TrainingMaterial;
import com.eroses.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class TrainingMaterialDao extends MyBatisDao<TrainingMaterial> {

//    public boolean create(TrainingMaterial trainingMaterial) {
//        return this.sqlSession.insert(this.sqlId("create"), trainingMaterial) == 1;
//    }
//
//    public boolean update(TrainingMaterial trainingMaterial) {
//        return this.sqlSession.update(this.sqlId("update"), trainingMaterial) == 1;
//    }
//
//    public boolean delete(Long id) {
//        return this.sqlSession.delete(this.sqlId("delete"), id) == 1;
//    }

    public TrainingMaterial findById(Long id) {
        return this.sqlSession.selectOne(this.sqlId("findById"), id);
    }

    public List<TrainingMaterial> findAllByCourseId(Long trainingCourseId) {
        return this.sqlSession.selectList(this.sqlId("findAllByCourseId"), trainingCourseId);
    }

    public List<TrainingMaterial> findAllByCourseIdAndType(Long trainingCourseId, String materialType) {
        Map<String, Object> params = new HashMap<>();
        params.put("trainingCourseId", trainingCourseId);
        params.put("materialType", materialType);
        return this.sqlSession.selectList(this.sqlId("findAllByCourseIdAndType"), params);
    }

    public boolean updateSequenceOrders(List<TrainingMaterial> materials) {
        Map<String, Object> params = new HashMap<>();
        params.put("materials", materials);
        return this.sqlSession.update(this.sqlId("updateSequenceOrders"), params) > 0;
    }
}
