package com.eroses.external.society.mappers;

import com.eroses.external.society.model.Organiser;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface OrganiserDao {
    List<Organiser> findAll();
    Organiser findByIdentificationNo(String identificationNo);
    int create(Organiser org);
    Boolean update(Organiser org);
    Organiser findById(Long id);
}
