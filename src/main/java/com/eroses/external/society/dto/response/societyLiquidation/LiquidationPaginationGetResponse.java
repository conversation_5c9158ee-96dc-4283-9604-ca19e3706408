package com.eroses.external.society.dto.response.societyLiquidation;

import com.eroses.external.society.model.Paging;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiquidationPaginationGetResponse {
    private Paging<LiquidationGetResponse> liquidationGetResponseList;
    private List<String> availableCreatedYears;
    private List<String> availableSubmissionYears;
    private List<String> availableDecisionYears;
}
