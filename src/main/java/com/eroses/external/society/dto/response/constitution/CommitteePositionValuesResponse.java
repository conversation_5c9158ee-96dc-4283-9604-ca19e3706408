package com.eroses.external.society.dto.response.constitution;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommitteePositionValuesResponse {
    private List<String> societyCommitteePositionCodes;
    private List<String> branchCommitteePositionCodes;
}
