package com.eroses.external.society.dto.response.constitution;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConstitutionContentGetOnePendingResponse {
    private Long id;
    private Long amendmentId;
    private Long societyId;
    private String societyNo;
    private Long clauseContentId;
    private String description;
    private String applicationStatusCode;
    private Long status;
    private String statusCode;
    private List<ConstitutionValueGetOnePendingResponse> constitutionValueList;

    private String createdBy;
    private LocalDate createdDate;
    private String modifiedBy;
    private LocalDate modifiedDate;
}
