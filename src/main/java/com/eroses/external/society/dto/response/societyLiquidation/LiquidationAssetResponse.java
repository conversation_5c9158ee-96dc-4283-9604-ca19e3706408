package com.eroses.external.society.dto.response.societyLiquidation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiquidationAssetResponse {
    private Long id;
    private String assetType;
    private BigDecimal assetValue;
    private Integer donation;
    private Integer liability;
    private Integer balance;
    private Integer other;
    private String otherReason;
    private Integer status;
}