package com.eroses.external.society.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingMinutesDto {
    private String societyNo;
    private String societyName;
    private String address;
    private String meetingTime;
    private String meetingPlace;
    private String meetingType;
    private int totalAttendees;
    private String meetingContent;
    private String providedBy;
    private String confirmBy;
    private List<Committee> committees;
    private String base64data; // Add this field to store the base64 PDF data

    // Getters and setters
    public String getBase64data() {
        return base64data;
    }

    public void setBase64data(String base64data) {
        this.base64data = base64data;
    }

    public static class Committee {
        private String name;
        private String occupation;

        // Getters and setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getOccupation() {
            return occupation;
        }

        public void setOccupation(String occupation) {
            this.occupation = occupation;
        }
    }
}

