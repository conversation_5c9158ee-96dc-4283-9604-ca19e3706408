package com.eroses.external.society.dto.response.searchInformation;

import com.eroses.external.society.model.SearchInformationDocumentTemplate;
import com.eroses.external.society.model.Society;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SocietyDocumentTemplateGetAllBySocietyIdResponse {

    private Long societyId;

    private String societyName;

    private String societyNo;

    private List<DocumentTemplate> documentTemplates;

    private List<Integer> statementYears;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocumentTemplate {
        private Long documentTemplateId;

        private String documentTemplateCode;

        private String documentTemplateName;

        private Float amount;
    }

    public SocietyDocumentTemplateGetAllBySocietyIdResponse convertToResponse(
            List<SearchInformationDocumentTemplate> searchInformationDocumentTemplates,
            Society society,
            List<Integer> statementYears) {

        // Map the document templates dynamically
        List<SocietyDocumentTemplateGetAllBySocietyIdResponse.DocumentTemplate> documentTemplates =
                searchInformationDocumentTemplates.stream()
                        .map(template -> SocietyDocumentTemplateGetAllBySocietyIdResponse.DocumentTemplate.builder()
                                .documentTemplateId(template.getId())
                                .documentTemplateCode(template.getCode())
                                .documentTemplateName(template.getName())
                                .amount(template.getAmount())
                                .build()
                        )
                        .collect(Collectors.toList());

        return SocietyDocumentTemplateGetAllBySocietyIdResponse.builder()
                .societyId(society.getId())
                .societyName(society.getSocietyName())
                .societyNo(society.getSocietyNo())
                .documentTemplates(documentTemplates)
                .statementYears(statementYears)
                .build();
    }

//    public SocietyDocumentTemplateGetAllBySocietyIdResponse convertToResponse(List<SocietyDocumentTemplate> societyDocumentTemplateList) {
//        // Extract common society details from the first element
//        SocietyDocumentTemplate firstTemplate = societyDocumentTemplateList.getFirst();
//
//        // Map the document templates dynamically
//        List<SocietyDocumentTemplateGetAllBySocietyIdResponse.DocumentTemplate> documentTemplates =
//                societyDocumentTemplateList.stream()
//                        .map(template -> SocietyDocumentTemplateGetAllBySocietyIdResponse.DocumentTemplate.builder()
//                                .documentTemplateId(template.getSearchInformationDocumentTemplate().getId())
//                                .documentTemplateCode(template.getSearchInformationDocumentTemplate().getCode())
//                                .documentTemplateName(template.getSearchInformationDocumentTemplate().getName())
//                                .amount(template.getSearchInformationDocumentTemplate().getAmount())
//                                .build()
//                        )
//                        .collect(Collectors.toList());
//
//        return SocietyDocumentTemplateGetAllBySocietyIdResponse.builder()
//                .societyId(firstTemplate.getSocietyId())
//                .societyName(firstTemplate.getSociety().getSocietyName())
//                .societyNo(firstTemplate.getSociety().getSocietyNo())
//                .documentTemplates(documentTemplates)
//                .build();
//    }

}
