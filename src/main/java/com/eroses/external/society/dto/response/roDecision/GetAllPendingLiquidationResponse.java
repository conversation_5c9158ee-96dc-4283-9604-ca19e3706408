package com.eroses.external.society.dto.response.roDecision;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetAllPendingLiquidationResponse {
    private Long id;
    private Long societyId;
    private String societyName;
    private String societyNo;
    private Long branchId;
    private String branchName;
    private String branchNo;
    private Long ro;
    private String roName;
    @JsonFormat(pattern="yyyy-MM-dd")
    private LocalDate transferDate;
    @JsonFormat(pattern="yyyy-MM-dd")
    private LocalDate submissionDate;
    private String stateCode;
    private Integer applicationStatusCode;
}
