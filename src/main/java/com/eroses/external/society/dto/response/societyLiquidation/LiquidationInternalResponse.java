package com.eroses.external.society.dto.response.societyLiquidation;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiquidationInternalResponse {
    private Long id;
    private Long societyId;
    private Long meetingId;
    @JsonFormat(pattern="dd-MM-yyyy")
    private LocalDate transferDate;
    private int committeeVoteCount;
    private String ro;
    private String roName;
    private String noteRo;
    private String notePpp;
    private Long createdBy;
    private String secretaryName;
    private String secretaryContactNo;
    private boolean userRo;
    private List<LiquidationAssetResponse> assets;
    private List<LiquidationApprovalResponse> reviews;
    private List<LiquidationFeedbackResponse> feedbacks;
}
