package com.eroses.external.society.dto.response.roDecision;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.time.LocalDate;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class GetAllPendingBranchAmendmentResponse {
    private Long id;
    private Long societyId;
    private String societyName;
    private String branchName;
    private String branchNo;
    private String roName;
    private String stateName;
    @JsonFormat(pattern="dd-MM-yyyy")
    private LocalDate paymentDate;
}