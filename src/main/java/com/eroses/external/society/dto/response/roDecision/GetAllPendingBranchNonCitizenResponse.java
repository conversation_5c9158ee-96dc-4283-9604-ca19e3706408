package com.eroses.external.society.dto.response.roDecision;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.time.LocalDate;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class GetAllPendingBranchNonCitizenResponse {
    private Long id;
    private String name;
    private String societyName;
    private String branchName;
    private String branchNo;
    private String branchApplicationNo;
    private String roName;
    private String stateName;
    @JsonFormat(pattern="dd/MM/yyyy")
    private LocalDate createdDate;
}