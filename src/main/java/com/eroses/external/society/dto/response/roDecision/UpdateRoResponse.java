package com.eroses.external.society.dto.response.roDecision;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateRoResponse {
    private Long societyId;
    private Long branchId;
    private Long amendmentId;
    private Long liquidationId;
    private Long appealId;
    private Long societyNonCitizenId;
    private Long branchNonCitizenId;
    private Long branchAmendmentId;
    private Long principalSecretaryId;
}