package com.eroses.external.society.dto.response.publicofficer;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
public class SocietyPublicOfficerGetResponse {
    private Long id;
    private Long societyId;
    private String societyNo;
    private String societyApplicationNo;
    private String societyName;
    private List<Long> publicOfficerCaseOfficerUserId;
    private Integer daysAfterSubmission;
}
