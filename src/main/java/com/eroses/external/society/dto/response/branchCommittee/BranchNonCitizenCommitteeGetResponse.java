
package com.eroses.external.society.dto.response.branchCommittee;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class BranchNonCitizenCommitteeGetResponse {
    private String id;
    private String name;
    private String citizenshipStatus;
    private String identificationType;
    private String identificationNo;
    private String applicantCountryCode;
    @JsonFormat(pattern="yyyy-MM-dd")
    private LocalDate visaExpirationDate;
    @JsonFormat(pattern="yyyy-MM-dd")
    private LocalDate permitExpirationDate;
    private String visaNo;
    private String permitNo;
    private String visaPermitNo;
    private Integer stayDurationDigit;
    private String stayDurationUnit;
    private String tujuanDMalaysia;
    private String designationCode;
    private String summary;
    private String applicationStatusCode;
}
