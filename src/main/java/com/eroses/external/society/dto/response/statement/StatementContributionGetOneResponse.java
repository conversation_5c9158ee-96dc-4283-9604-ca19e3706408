package com.eroses.external.society.dto.response.statement;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public record StatementContributionGetOneResponse(
        Long id,
        Long statementId,
        Long societyId,
        String societyNo,
        Long branchId,
        String branchNo,
        String contributionCode,
        String contribution,
        String countryOrigin,
        BigDecimal value,
        String applicationStatusCode,
        String createdBy,
        LocalDateTime createdDate,
        String modifiedBy,
        LocalDateTime modifiedDate
) {
}
