package com.eroses.external.society.dto.response.constitution;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConstitutionValueGetOnePendingResponse {
    private Long id;
    private Long amendmentId;
    private Long societyId;
    private String societyNo;
    private Long clauseContentId;
    private Long constitutionContentId;
    private String societyName;
    private String definitionName;
    private String societyAddress;
    private String societyCity;
    private String societyDistrictCode;
    private String societyPostcode;
    private String societyParlimentCode;
    private String societyState;
    private String mailingAddress;
    private String mailingAddressCity;
    private String mailingAddressDistrictCode;
    private String mailingAddressPostcode;
    private String mailingAddressParlimentCode;
    private String mailingAddressState;
    private String meetingFrequency;
    private String parentBeginningFiscalYear;
    private String numberOfPrincipalAuditors;
    private String branchMeetingFrequency;
    private String financialYearBeginningBranch;
    private String numberOfBranchAuditors;
    private String parentPosition;
    private String branchPosition;
    private String applicationStatusCode;
    private String statusCode;

    private String createdBy;
    private LocalDate createdDate;
    private String modifiedBy;
    private LocalDate modifiedDate;
}

