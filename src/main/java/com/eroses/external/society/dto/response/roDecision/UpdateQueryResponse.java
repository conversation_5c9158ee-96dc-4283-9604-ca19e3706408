package com.eroses.external.society.dto.response.roDecision;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateQueryResponse {
    private Long societyId;
    private Long branchId;
    private Long appealId;
    private Long amendmentId;
    private Long liquidationId;
    private Long newSecretaryId;
}