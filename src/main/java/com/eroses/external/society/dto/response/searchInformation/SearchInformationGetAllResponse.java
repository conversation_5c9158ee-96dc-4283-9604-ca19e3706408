package com.eroses.external.society.dto.response.searchInformation;

import com.eroses.external.society.model.SearchInformation;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchInformationGetAllResponse {
    private Long searchInformationId;
    private Long societyId;
    private String societyName;
    private String societyNo;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate paymentDate;
    private Integer applicationStatusCode;

    public List<SearchInformationGetAllResponse> convertToSearchInformationGetAllResponse (List<SearchInformation> searchInformationList){

        return searchInformationList.stream()
                .map(searchInformation -> new SearchInformationGetAllResponse(
                        searchInformation.getId(),
                        searchInformation.getSocietyId(),
                        searchInformation.getSociety().getSocietyName(),
                        searchInformation.getSocietyNo(),
                        searchInformation.getPaymentDate() != null ? searchInformation.getPaymentDate().toLocalDate() : null,
                        searchInformation.getApplicationStatusCode()
                ))
                .collect(Collectors.toList());
    }
}
