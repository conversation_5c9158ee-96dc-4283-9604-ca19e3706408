//package com.eroses.external.society.dto.response;
//
//import com.eroses.service.notification.NotificationSendStatus;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.util.List;
//
//@Data
//@Builder
//@NoArgsConstructor
//@AllArgsConstructor
//public class NotificationCreateResponse {
//    private Long id;
//    private NotificationSendStatus sendStatus;
//    private int totalRecipients;
//    private int successCount;
//    private int failureCount;
//    private List<RecipientEmailResult> recipientResults;
//
//    @Data
//    @Builder
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public static class RecipientEmailResult {
//        private String username;
//        private String emailAddress;
//        private String status;
//        private String message;
//    }
//}
