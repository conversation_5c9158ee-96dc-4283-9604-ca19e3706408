package com.eroses.external.society.dto.response.propertyofficer;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
public class PropertyOfficerResponse {
    private Long id;
    private String societyNo;
    private Long societyId;
    private String branchNo;
    private Long branchId;
    private String name;
    private String email;
    private String identificationNo;
    private LocalDate appointmentDate;
    private Boolean agreementAcknowledgment;
    private LocalDate acknowledgmentDate;
    private LocalDate submissionDate;
    private String paymentMethod;
    private Long paymentId;
    private String receiptNumber;
    private LocalDate paymentDate;
    private Long ePaymentId;
    private String bankName;
    private String bankReferenceNumber;
    private String applicationStatusCode;
    private Integer status;
    private String receiptStatus;
    private String branchOfficer;
    private String ro;
    private LocalDate flowDate;
    private String approver;
    private LocalDate reconcileDate;
    private String roNote;
    private Long societyCommitteeId;
    private Long branchCommitteeId;
    private Long propertyOfficerApplicationId;
}
