package com.eroses.external.society.dto.response.societySecretaryReplacement;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PrincipleSecretaryResponse {
    private Long id;
    @JsonProperty("isUserApplicant")
    private boolean isUserApplicant;
    private int applicationStatusCode;
    private Long societyId;
    private String societyNo;
    private String titleCode;
    private String committeeName;
    private String gender;
    private String committeePosition;
    private String jobCode;
    private String citizenshipStatus;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate dateOfBirth;
    private String placeOfBirth;
    private String identificationType;
    private String identificationNo;
    private String email;
    private String residenceAddress;
    private String residencePostcode;
    private String residenceAddressStatus;
    private String residenceCountryCode;
    private String residenceStateCode;
    private String residenceDistrictCode;
    private String residenceCityCode;
    private String residenceCity;
    private String homeTelNo;
    private String hpNo;
    private String workTelNo;
    private String employerName;
    private String employerAddress;
    private String employerPostcode;
    private String employerAddressStatus;
    private String employerCountryCode;
    private String employerStateCode;
    private String employerDistrictCode;
    private String employerCityCode;
    private String employerCity;
    private String oldSecretaryName;
    private String oldSecretaryIdentificationNumber;
    private Long meetingId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate meetingDate;
    private String meetingType;
    private String reasonOfChange;
    private String queryNote;
    private Long queryId;
}
