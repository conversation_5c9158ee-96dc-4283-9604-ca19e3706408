package com.eroses.external.society.dto.response.principalSecretaryInternal;

import com.eroses.external.society.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PrincipalSecretaryInternalResponse extends BaseEntity {
    private Long id;
    private Long societyId;
    private String societyNo;
    private String societyName;
    private String societyCategory;
    private String societySubCategory;
    private boolean hasBranch;
    private String identificationNo;
    private String identificationType;
    private String newSecretaryName;
    private String gender;
    private String citizenshipStatus;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate dateOfBirth;
    private String placeOfBirth;
    private String residenceAddress;
    private String residencePostcode;
    private String residenceAddressStatus;
    private String residenceCountryCode;
    private String residenceStateCode;
    private String residenceDistrictCode;
    private String residenceCityCode;
    private String residenceCity;
    private String homeTelNo;
    private String hpNo;
    private String workTelNo;
    private String employerName;
    private String employerAddress;
    private String employerPostcode;
    private String employerCountryCode;
    private String employerStateCode;
    private String employerDistrictCode;
    private String employerCityCode;
    private String employerCity;
    private String titleCode;
    private String committeePosition;
    private String jobCode;
    private String email;
    private String oldSecretaryNameKeyIn;
    private String oldSecretaryIdentificationNumberKeyIn;
    private String oldSecretaryName;
    private String oldSecretaryIdentificationNumber;
    private String oldSecretaryResidentialAddress;
    private String oldSecretaryResidentialPostcode;
    private String oldSecretaryResidentialCity;
    private String oldSecretaryTelephoneNumber;
    private Long meetingId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate meetingDate;
    private String meetingType;
    private String ro;
    private String reasonOfChange;
    private String roName;
    private String noteRo;
    private boolean userRO;
    private List<PrincipalSecreatryFeedbackInternalResponse> feedbacks;
    private List<RoApprovalResponse> reviews;
    private List<RoQueryResponse> queries;
}
