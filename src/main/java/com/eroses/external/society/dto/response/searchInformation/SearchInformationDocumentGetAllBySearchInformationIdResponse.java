package com.eroses.external.society.dto.response.searchInformation;

import com.eroses.external.society.model.SearchInformationDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchInformationDocumentGetAllBySearchInformationIdResponse {
    private Long searchInformationDocumentId;
    private String searchInformationDocumentName;
    private Integer downloadDaysLeft;
    private Integer applicationStatusCode;

    public SearchInformationDocumentGetAllBySearchInformationIdResponse convertToResponse (SearchInformationDocument searchInformationDocument, Integer downloadDaysLeft){

        return new SearchInformationDocumentGetAllBySearchInformationIdResponse(
                        searchInformationDocument.getId(),
                        searchInformationDocument.getName(),
                        downloadDaysLeft,
                        searchInformationDocument.getApplicationStatusCode()
        );
    }
}
