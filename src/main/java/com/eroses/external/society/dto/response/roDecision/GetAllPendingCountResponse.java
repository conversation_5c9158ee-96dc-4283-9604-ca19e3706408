package com.eroses.external.society.dto.response.roDecision;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetAllPendingCountResponse {
    private Integer societyRegistrationPendingCount;
    private long societyLiquidationPendingCount;
    private Integer societyNonCitizenPendingCount;
    private Long societyPublicOfficerPendingCount;
    private Long societyPropertyOfficerPendingCount;
    private Integer societyExternalAgencyReviewPendingCount;
    private Integer societyAmendmentPendingCount;

    private Integer branchRegistrationPendingCount;
    private Long branchLiquidationPendingCount;
    private Integer branchNonCitizenPendingCount;
    private Integer branchExtensionTimePendingCount;
    private Long branchPublicOfficerPendingCount;
    private Long branchPropertyOfficerPendingCount;
    private Integer branchAmendmentPendingCount;


    private Integer societyRegistrationQueryPendingCount;
    private Integer branchRegistrationQueryPendingCount;
    private Integer societyAmendmentQueryPendingCount;
    private Integer societyAppealQueryPendingCount;
    private Long societyPrincipalSecretaryPendingCount;
}
