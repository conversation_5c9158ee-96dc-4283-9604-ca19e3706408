package com.eroses.external.society.dto.response.roDecision;

import lombok.*;

import java.time.LocalDate;

@Getter
@Setter
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetAllDecisionRecordAmendmentResponse {
    //from Amendment
    private Long id;
    private String amendedConstitutionRemarks;
    private Long societyId;
    private String societyNo;
    private String societyLevel;
    private String categoryCodeJppm;
    private String subCategoryCode;
    private String constitutionType;
    private String amendmentType;
    private LocalDate paymentDate;
    private LocalDate submissionDate;
    private LocalDate approvedDate;
    private String ro;
    private String applicationStatusCode;

    //from Society
    private String societyName;
    private String societyApplicationStatusCode;
    private String stateCode;
}
