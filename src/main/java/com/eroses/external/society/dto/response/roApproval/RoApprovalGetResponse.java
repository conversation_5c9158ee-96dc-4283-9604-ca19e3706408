package com.eroses.external.society.dto.response.roApproval;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoApprovalGetResponse {
    private Long id;
    private String type;
    private String applicationNo;
    private String societyNo;
    private Long societyId;
    private Long branchId;
    private String branchNo;
    private Long societyNonCitizenCommitteeId;
    private Long extensionTimeId;
    private Long branchAmendmentId;
    private Integer decision;
    private String akta1;
    private String akta2;
    private Integer extensionDays;
    private String rejectReason;
    private String note;
    private Long approvedBy;
    private String officerName;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate decisionDate;
    
    private Long amendmentId;
    private Long appealId;
    private Long principalSecretaryId;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate createdDate;
    
    private Long createdBy;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate modifiedDate;
    
    private Long modifiedBy;
}
