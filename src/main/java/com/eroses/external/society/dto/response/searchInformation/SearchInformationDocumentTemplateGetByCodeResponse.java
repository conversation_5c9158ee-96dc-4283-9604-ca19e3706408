package com.eroses.external.society.dto.response.searchInformation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchInformationDocumentTemplateGetByCodeResponse {

    private Long id;

    private String code;

    private String name;

    private String description;

    private String type;

    private Integer downloadPeriod;

    private String content;

    private Integer status;

    private String format;

    private String language;

    private String s3Url;

    private Boolean isGenerated;
}
