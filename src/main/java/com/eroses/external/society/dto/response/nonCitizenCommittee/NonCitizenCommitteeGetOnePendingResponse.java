package com.eroses.external.society.dto.response.nonCitizenCommittee;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NonCitizenCommitteeGetOnePendingResponse {
    private Long id;
    private String name;
    private String citizenshipStatus;
    private String identificationType;
    private String identificationNo;
    private String applicantCountryCode;
    private LocalDate visaExpirationDate;
    private LocalDate permitExpirationDate;
    private String visaNo;
    private String permitNo;
    private String positionCode;
    private String visaPermitNo;
    private String tujuanDMalaysia;
    private String tempohDMalaysia;
    private String durationType;
    private String summary;
    private String societyName;
    private Integer societyId;
    private String societyNo;
    private Integer branchId;
    private String branchNo;
    private String applicationStatusCode;
    private String status;
    private String ro;
    private String pembaharuanSu;
    private String pemCaw;
    private String otherPosition;
    private LocalDate transferDate;
    private String noteRo;

    private String createdBy;
    private LocalDate createdDate;
    private String modifiedBy;
    private LocalDate modifiedDate;
}
