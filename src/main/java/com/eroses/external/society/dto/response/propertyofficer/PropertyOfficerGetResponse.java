package com.eroses.external.society.dto.response.propertyofficer;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
public class PropertyOfficerGetResponse {
    private String name;
    private String identificationNo;
    private Long propertyOfficerApplicationId;
    private Long societyCommitteeId;
    private Long branchCommitteeId;
    private String email;
}
