package com.eroses.external.society.dto.response.branchSecretary;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BranchSecretarySlipResponse {
    private Long id;
    private Long applicantUserId;
    private String applicantName;
    private String applicantIdNo;
    private Long societyId;
    private String societyName;
    private String societyNo;
    private Long branchId;
    private String branchName;
    private String branchNo;
    private String branchSecretaryName;
    private String branchSecretaryIdNo;
    private LocalDateTime applicationDateTime;
}
