package com.eroses.external.society.dto.response;

import com.eroses.external.society.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtensionTimeGetResponse implements Serializable {

    @ApiModelProperty
    private Long id;

    @ApiModelProperty
    private Long societyId;

    @ApiModelProperty
    private String societyNo;

    @ApiModelProperty
    private Long branchId;

    @ApiModelProperty
    private String branchNo;

    @ApiModelProperty
    private Integer extensionDays;

    @ApiModelProperty
    private String note;

    @ApiModelProperty
    private Boolean acknowledgement;

    @ApiModelProperty
    private Integer applicationStatusCode;

    @ApiModelProperty
    private Long createdBy;

    @ApiModelProperty
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate createdDate;

    @ApiModelProperty
    private Long modifiedBy;

    @ApiModelProperty
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate modifiedDate;
}
