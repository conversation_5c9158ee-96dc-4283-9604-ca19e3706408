package com.eroses.external.society.dto.response.auditTrail;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditTrailPagingResponse {
    private Long id;
    private String module;
    private String actionType;
    private Integer userGroup;
    private String userName;
    private String identificationNo;
    @JsonFormat(pattern="HH:mm dd:MM:yyyy")
    private LocalDateTime createdDate;
}
