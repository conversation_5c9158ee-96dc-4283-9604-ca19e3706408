package com.eroses.external.society.dto.response.propertyofficer;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
public class SocietyPropertyOfficerApplicationGetResponse {
    private Long id;
    private Long societyId;
    private String societyNo;
    private String societyApplicationNo;
    private String societyName;
    private List<Long> propertyOfficerApplicationCaseOfficerUserId;
    private Integer daysAfterSubmission;
}
