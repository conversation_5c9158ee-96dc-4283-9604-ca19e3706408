package com.eroses.external.society.dto.response.roDecision;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.time.LocalDate;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class GetAllPendingSocietyPublicOfficerResponse {
    private Long id;
    private String creatorName;
    private String name;
    private String identificationNo;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate appointmentDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate paymentDate;
    private String roName;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate transferDate;
    private String stateName;
    private String societyName;
    private String societyNo;
}
