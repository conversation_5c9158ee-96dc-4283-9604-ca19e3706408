package com.eroses.external.society.dto.response.propertyofficer;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PropertyOfficerApplicationResponse {
    private Long id;
    private String societyNo;
    private Long societyId;
    private String branchNo;
    private Long branchId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate appointmentDate;
    private Boolean agreementAcknowledgment;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate acknowledgmentDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate submissionDate;
    private String paymentMethod;
    private Long paymentId;
    private String receiptNumber;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate paymentDate;
    private Long ePaymentId;
    private String bankName;
    private String bankReferenceNumber;
    private String applicationStatusCode;
    private Integer status;
    private String receiptStatus;
    private String branchOfficer;
    private String ro;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate flowDate;
    private String approver;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate reconcileDate;
    private String roNote;
    private List<PropertyOfficerGetResponse> propertyOfficers;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdDate;
}
