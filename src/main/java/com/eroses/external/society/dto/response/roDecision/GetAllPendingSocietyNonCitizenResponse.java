package com.eroses.external.society.dto.response.roDecision;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.time.LocalDate;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class GetAllPendingSocietyNonCitizenResponse {
    private Long id;
    private String name;
    private String designationCode;
    private String roName;
    @JsonFormat(pattern="dd/MM/yyyy")
    private LocalDate transferDate;
    private String stateName;
    @JsonFormat(pattern="dd/MM/yyyy")
    private LocalDate createdDate;
    private String societyNo;
    private String societyApplicationNo;
}