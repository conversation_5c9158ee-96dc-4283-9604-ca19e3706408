package com.eroses.external.society.dto.response.branchSecretary;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class BranchSecretaryHistoryDetailsResponse {
    private Long id;
    private Long societyId;
    private String societyNo;
    private String societyName;
    private Long branchId;
    private String branchNo;
    private String branchName;
    private String secretaryName;
    private String secretaryIdNo;
    private Long applicantUserId;
    private String applicantName;
    private String applicantIdNo;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applicationDateTime;
}
