package com.eroses.external.society.dto.response.roDecision;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.time.LocalDate;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class GetAllPendingBranchRegistrationResponse {
    private Long id; //branchId
    private String branchApplicationNo;
    private String name; //branchName
    private Long societyId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate paymentDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate submissionDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate transferDate;

    //Extra fields required mapping
    private String branchCreatorName;
    private String societyName;
    private String roName;
    private String stateName;
}