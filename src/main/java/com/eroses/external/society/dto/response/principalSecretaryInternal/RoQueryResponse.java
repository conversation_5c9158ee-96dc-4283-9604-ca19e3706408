package com.eroses.external.society.dto.response.principalSecretaryInternal;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RoQueryResponse {
    private Long id;
    private String note;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yy")
    private LocalDateTime createdDate;
}
