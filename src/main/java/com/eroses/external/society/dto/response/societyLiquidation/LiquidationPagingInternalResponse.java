package com.eroses.external.society.dto.response.societyLiquidation;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiquidationPagingInternalResponse {
    private Long liquidationId;
    private String societyName;
    private String societyNo;
    private Long branchId;
    private Long ro;
    private String roName;
    @JsonFormat(pattern="dd-MM-yyyy")
    private LocalDate transferDate;
    @JsonFormat(pattern="dd-MM-yyyy")
    private LocalDate submissionDate;
    private String stateCode;
    private Integer applicationStatusCode;
}
