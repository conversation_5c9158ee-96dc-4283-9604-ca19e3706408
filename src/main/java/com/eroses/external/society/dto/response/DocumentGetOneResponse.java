package com.eroses.external.society.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentGetOneResponse {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("type")
    private String type;
    @ApiModelProperty("societyId")
    private Long societyId;
    @ApiModelProperty("societyNo")
    private String societyNo;
    @ApiModelProperty("branchId")
    private Long branchId;
    @ApiModelProperty("branchNo")
    private String branchNo;
    @ApiModelProperty("meetingId")
    private Long meetingId;
    @ApiModelProperty("societyCommitteeId")
    private Long societyCommitteeId;
    @ApiModelProperty("icNo")
    private String icNo;
    @ApiModelProperty("name")
    private String name;
    @ApiModelProperty("note")
    private String note;
    @ApiModelProperty("url")
    private String url;
    @ApiModelProperty("doc")
    private byte[] doc;
    @ApiModelProperty("status")
    private String status;

    private String createdBy;
    private LocalDate createdDate;
    private String modifiedBy;
    private LocalDate modifiedDate;
}
