package com.eroses.external.society.dto.response.societyLiquidation;

import com.eroses.external.society.model.enums.Position;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiquidationFeedbackResponse {
    private Long id;
    private String icNo;
    private String feedback;
    private Integer nonCommittee;
    private Integer falseStatement;
    private Integer other;
    private String otherReason;
    private int positionCode;
    private Position position;
}
