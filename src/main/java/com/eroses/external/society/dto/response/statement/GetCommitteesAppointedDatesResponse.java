package com.eroses.external.society.dto.response.statement;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.util.List;

@Data
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GetCommitteesAppointedDatesResponse {
    private Long statementId;
    private Long societyId;
    private String societyNo;
    private Long branchId;
    private String branchNo;
    private String ajkType;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private List<Object> ajkAppointedDates;
}
