package com.eroses.external.society.dto.response.societyLiquidation;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiquidationApprovalResponse {
    private Long id;
    private String decision;
    private Integer decisionCode;
    private String decisionDate;
    private String note;
    private String rejectReason;
    @Value("${isPPP:false}")
    @JsonProperty("isPPP")
    private boolean isPPP;
    private String approvedBy;
}