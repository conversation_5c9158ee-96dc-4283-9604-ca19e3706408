package com.eroses.external.society.dto.response.societyLiquidation;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiquidationGetResponse {
    private Long id;
    private Long societyId;
    private String societyNo;
    private Long branchId;
    private String branchNo;
    private Integer liquidationDocumentType;
    private Long meetingId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate meetingDate;
    private Integer committeeVoteCount;
    private Integer committeeAttendCount;
    private Integer committeeAgreeCount;
    private BigDecimal totalFixedAssets;
    private BigDecimal cashInHand;
    private BigDecimal cashInBank;
    private BigDecimal totalAsset;
    private BigDecimal shortTermLiability;
    private BigDecimal longTermLiability;
    private BigDecimal totalLiability;
    private BigDecimal totalFixedAssetsFinance;
    private BigDecimal cashInHandFinance;
    private BigDecimal cashInBankFinance;
    private BigDecimal totalAssetFinance;
    private BigDecimal shortTermLiabilityFinance;
    private BigDecimal longTermLiabilityFinance;
    private BigDecimal totalLiabilityFinance;
    private String applicantName;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate submissionDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate decisionDate;
    private Integer applicationStatusCode;
    private String ro;
    private Integer branchLiquidation;
    private String notePpp;
    private LocalDate transferDate;
    private String noteRo;
    private Integer status;
    private Integer roDecisionCode;
    private String roDecisionNote;
    private Long createdBy;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate createdDate;
    private Long modifiedBy;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDateTime modifiedDate;
    private Long statementId;

    private String secretaryName;
    private String secretaryContactNo;
    private String roName;
    private List<LiquidationAssetResponse> assets;
    private List<LiquidationFeedbackResponse> feedbacks;
}
