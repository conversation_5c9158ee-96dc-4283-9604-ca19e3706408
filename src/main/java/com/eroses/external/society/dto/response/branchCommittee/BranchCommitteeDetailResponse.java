package com.eroses.external.society.dto.response.branchCommittee;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BranchCommitteeDetailResponse {
    private long branchCommitteeId;
    private Integer designationCode;
    private String identityType;
    private String committeeIcNo;
    private String titleCode;
    private String committeeName;
    private String gender;
    private String dateOfBirth;
    private String placeOfBirth;
    private String jobCode;
    private String committeeAddress;
    private String committeeStateCode;
    private String committeeDistrict;
    private String postcode;
    private String email;
    private String phoneNumber;
    private String homePhoneNumber;
    private String officePhoneNumber;
    private String committeeEmployerName;
    private String committeeEmployerAddress;
    private String committeeEmployerCountryCode;
}
