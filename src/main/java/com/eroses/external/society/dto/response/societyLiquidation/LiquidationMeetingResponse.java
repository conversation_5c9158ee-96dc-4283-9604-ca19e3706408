package com.eroses.external.society.dto.response.societyLiquidation;

import com.eroses.external.society.dto.response.meeting.MeetingMemberAttendanceResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiquidationMeetingResponse {
    private Long id;
    private List<LiquidationAssetResponse> assets;
    private Long meetingId;
    private String meetingType;
    private String meetingPurpose;
    private String meetingPlace;
    private String meetingMethod;
    private String platformType;
    private LocalDate meetingDate;
    private LocalTime meetingTime;
    private String GISInformation;
    private String meetingAddress;
    private String state;
    private String district;
    private String city;
    private String postcode;
    private int totalAttendees;
    private String meetingContent;
    private String providedBy;
    private String confirmBy;
    private String meetingMinute;
    private List<MeetingMemberAttendanceResponse> memberAttendances;
}