package com.eroses.external.society.dto.response.organization;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationRegisterResponse {
    private Long meetingId;
    private List<Long> constitutionIdList;
    private List<Long> committeeIdList;
    private List<Long> nonCitizenCommitteeIdList;
    private Long documentId;
}
