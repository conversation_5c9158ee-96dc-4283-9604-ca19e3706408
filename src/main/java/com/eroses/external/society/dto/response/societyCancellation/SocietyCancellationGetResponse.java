package com.eroses.external.society.dto.response.societyCancellation;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SocietyCancellationGetResponse {
    private Long id;
    private Long societyId;
    private String societyNo;
    private String societyName;
    private String societyStatusCode;
    private Long branchId;
    private String branchNo;
    private String branchName;
    private String branchStatusCode;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate cancelledDate;
    
    private String section;
    private String reason;
}