package com.eroses.external.society.dto.response.roDecision;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoQueryGetResponse {

    private Long id;
    private String type;
    private String societyNo;
    private Long societyId;
    private Integer branchId;
    private String branchNo;
    private Long appealId;
    private String queryReceiver;
    private Long amendmentId;
    private Long nonCitizenCommitteeId;
    private String note;
    private Boolean finished;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    LocalDate createdDate;
    private Long createdBy;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    LocalDate modifiedDate;
    private String officerName;
}