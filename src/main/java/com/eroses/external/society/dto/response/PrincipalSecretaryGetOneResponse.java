package com.eroses.external.society.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrincipalSecretaryGetOneResponse {
    private Long societyCommitteeId;
    private String jobCode;
    private String societyNo;
    private String titleCode;
    private String committeeName;
    private String gender;
    private String citizenshipStatus;
    private String identificationType;
    private String identificationNo;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate dateOfBirth;
    private String placeOfBirth;
    private String committeePosition;
    private String addressStatusM;
    private String employerName;
    private String employerAddress;
    private String employerPostcode;
    private String countryCodeM;
    private String stateCodeM;
    private String districtCodeM;
    private String cityCodeM;
    private String cityM;
    private String residentialAddress;
    private String residentialPostcode;
    private String addressStatusK;
    private String countryCodeK;
    private String stateCodeK;
    private String districtCodeK;
    private String cityCodeK;
    private String cityK;
    private String email;
    private String telNoR;
    private String hpNo;
    private String telNoP;
    private String deleteStatus;
    private String applicationStatusCode;
    private String reasonOfChange;
    private String oldSecretaryName;
    private String oldSecretaryIdentificationNumber;
    private String acknowledgement1;
    private String acknowledgement2;
    private String ro;
    private String activeStatus;
    private String migrateStatSecretary;
    private LocalDate meetingDate;
    private String meetingType;
    private Byte[] pdfDocument;
    private LocalDate submitDate;
    private String noteRo;
    private LocalDate transferDate;
}
