package com.eroses.external.society.dto.response.organization;

import com.eroses.external.society.dto.response.committee.CommitteeGetOnePendingResponse;
import com.eroses.external.society.dto.response.constitution.ConstitutionContentGetOnePendingResponse;
import com.eroses.external.society.dto.response.meeting.MeetingGetOnePendingResponse;
import com.eroses.external.society.dto.response.nonCitizenCommittee.NonCitizenCommitteeGetOnePendingResponse;
import com.eroses.external.society.dto.response.society.SocietyGetOnePendingResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationGetOnePendingResponse {
    SocietyGetOnePendingResponse societyPendingResponse;
    List<MeetingGetOnePendingResponse> meetingGetAllPendingResponses;
    List<ConstitutionContentGetOnePendingResponse> constitutionContentGetOnePendingResponses;
    List<CommitteeGetOnePendingResponse> committeeGetAllPendingResponses;
    List<NonCitizenCommitteeGetOnePendingResponse> nonCitizenCommitteeGetAllPendingResponses;
}
