package com.eroses.external.society.dto.response;

import com.eroses.external.society.model.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetRoAmendmentResponse extends BaseEntity implements Serializable {
    private Long id;
    private Long oldAmendmentId;
    private Long societyId;
    private String societyNo;
    private String clauseType;
    private String meetingType;
    private LocalDate meetingDate;
    private Long meetingId;
    private Byte[] pdfDocument;
    private String amendmentClause;
    private String goal;
    private String agreement;
    private LocalDate agreementDate;
    private String paymentMethod;
    private Long paymentId;
    private String receiptNo;
    private Integer receiptStatus;
    private LocalDate paymentDate;
    private LocalDate submissionDate;
    private String referenceNo;
    private String bankName;
    private String bankReferenceNo;
    private String ro;
    private Integer appealActive;
    private LocalDate transferDate;
    private String noteRo;
    private String kuiri;
    private String status;
    private String applicationStatusCode;
    private String societyLevel;
    private String hasBranch;
    private String categoryCodeJppm;
    private String subCategoryCode;
    private String constitutionType;
    private String amendmentType;
    private Integer isQueried;
    private LocalDate approvedDate;
//    private Long societyId;
    private String societyName;
    private String societyNoPpm;
    private Long societyStateCode;
}
