package com.eroses.external.society.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BranchAmendmentGetResponse {

    private Long id;
    private Long societyId;
    private String societyNo;
    private Long branchId;
    private String branchNo;
    private String amendmentType;
    private Long meetingId;
    private String meetingType;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate meetingDate;

    private String meetingPlace;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss")
    private LocalTime meetingTime;

    private Integer totalAttendees;
    private String branchName;
    private String address;
    private String countryCode;
    private String stateCode;
    private String districtCode;
    private String smallDistrictCode;
    private String cityCode;
    private String city;
    private String mailingAddress;
    private String mailingStateId;
    private String mailingDistrictId;
    private String mailingCity;
    private String mailingPostcode;
    private String postcode;
    private Integer acknowledge;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate acknowledgeDate;

    private Integer paymentId;
    private String paymentMethod;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime paymentDate;

    private String bankReferenceNo;
    private String bankName;
    private String receiptNo;
    private Integer receiptStatus;

    private Long createdBy;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate createdDate;

    private Long modifiedBy;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate modifiedDate;

    private String applicationStatusCode;
    private String ro;
    private String noteRo;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate transferDate;
    private String originalBranchName;
    private String originalAddress;
    private String originalCountryCode;
    private String originalStateCode;
    private String originalDistrictCode;
    private String originalSmallDistrictCode;
    private String originalCityCode;
    private String originalCity;
    private String originalPostcode;
    private String originalMailingAddress;
    private String originalMailingStateId;
    private String originalMailingDistrictId;
    private String originalMailingCity;
    private String originalMailingPostcode;
    private String epaymentId;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate reconcileDate;

    private String currentBranchName;
    private String currentBranchAddress;
}