package com.eroses.external.society.dto.response.roDecision;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.time.LocalDate;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class GetAllPendingAmendmentResponse {
    //from Amendments
    private Long id;
    private Long oldAmendmentId;
    private String amendedConstitutionRemarks;
    private Long societyId;
    private String roName;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate transferDate;
    private Long meetingId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate meetingDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy")
    private LocalDate paymentDate;
    private String amendmentType;
    private Long applicationStatusCode;
    private Integer isQueried;
    private LocalDate approvedDate;

    //from Society
    private String societyName;
    private String societyNo;
    private Long stateCode;
    private Long categoryCodeJppm;
    private Long subCategoryCode;
    private String constitutionType;
    private Long societyApplicationStatusCode;

    //extra info
    private String applicantName;
    private String applicantEmail;
    private Long createdBy;
    private Long modifiedBy;
}
