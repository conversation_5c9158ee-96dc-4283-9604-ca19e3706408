package com.eroses.external.society.dto.response.societyLiquidation;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiquidationBasicResponse {
    private Long id;
    private Integer liquidationDocumentType;
    private Long meetingId;
    private String societyName;
    private Long societyId;
    private String societyNo;
    private int committeeVoteCount;
    private int committeeAttendCount;
    private int committeeAgreeCount;
    private Integer applicationStatusCode;
    private String committeeName;
    private String committeeDesignationCode;
    private String committeeIdentificationNo;
    private String committeeNationalityStatus;
    @JsonProperty("isSecretary")
    private boolean isSecretary;
    private List<LiquidationAssetResponse> assets;
}
