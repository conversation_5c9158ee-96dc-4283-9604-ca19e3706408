package com.eroses.external.society.dto.response.roDecision;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.time.LocalDate;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class GetAllPendingAppealResponse {
    //from Appeal
    private Long id;
    private String appealNo;
    private Long idSebab;
    @JsonFormat(pattern="dd/MM/yyyy")
    private LocalDate submitDate;
    private String roName;
    private Long applicationStatusCode;
    private Long createdBy;
    @JsonFormat(pattern="dd/MM/yyyy")
    private LocalDate createdDate;
    private Long modifiedBy;
    @JsonFormat(pattern="dd/MM/yyyy")
    private LocalDate modifiedDate;
    private Integer isQueried;
    @JsonFormat(pattern="dd/MM/yyyy")
    private LocalDate approvedDate;

    //from Society
    private String societyId;
    private String societyName;
    private String societyNo;
    private String societyApplicationNo;
    private Long societyApplicationStatusCode;
    private Long categoryCodeJppm;
    private Long subCategoryCode;
    private Long stateCode;

    //from User
    private String applicantName;
    private String applicantEmail;
}
