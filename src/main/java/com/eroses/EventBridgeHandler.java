package com.eroses;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.ScheduledEvent;
import com.eroses.external.society.service.SchedulerTaskService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;

public class EventBridgeHandler implements RequestHandler<ScheduledEvent, String> {

    private final SchedulerTaskService schedulerTaskService;

    @Autowired
    public EventBridgeHandler(SchedulerTaskService schedulerTaskService) {
        this.schedulerTaskService = schedulerTaskService;
    }

    @Override
    public String handleRequest(ScheduledEvent event, Context context) {
        try {
            context.getLogger().log("Starting daily record status update");

            ObjectMapper objectMapper = new ObjectMapper();
            String eventJson = objectMapper.writeValueAsString(event);
            context.getLogger().log("Event received: " + eventJson);

            //UpdateResult result = schedulerTaskService.updateOverdueQuery();

            //String message = String.format("Successfully processed %d records", result.getProcessedCount());
            //context.getLogger().log(message);
            return null;
        } catch (Exception e) {
            String errorMessage = "Error in daily update: " + e.getMessage();
            context.getLogger().log(errorMessage);
            throw new RuntimeException(errorMessage, e);
        }
    }
}
