spring:
  cache:
    redis:
      host: your-elasticache-endpoint.amazonaws.com
      port: 6379
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    #    hibernate:
    #      ddl-auto: update
    show-sql: true
  servlet:
    multipart:
      enabled: true
      max-file-size: 25MB
      max-request-size: 30MB
  server:
    tomcat:
      max-http-post-size: 31457280
#  server:
#    port: 8081

mybatis:
  mapper-locations: classpath:/mapper/*.xml

logging:
  level:
    root: info

api:
  jpn:
    base-url: https://staging.ros.gov.my/api_eroses/chxMI.PHP
    key: ?k=O*)3B@nM6vrtaI^9qD*1hkZm~kN
  payment:
    url: https://epayment.eroses.gov.my/eps/process
    urlHost: https://epayment.eroses.gov.my/eps
    exchangeId: **********
    environment: production
    urlRequest: https://epayment.eroses.gov.my/
    osolCode : 72499
    department : "JABATAN PENDAFTARAN PERTUBUHAN"
    ptj : "IBU PEJABAT JAB. PENDAFTARAN PERTUBUHAN"
    paymentMode : fpx
    requestStatus : 3

aws:
  region: ap-southeast-5
  secrets-manager:
    database: db_credentials_prod
    cache: cache_credentials_prod
    map: map_credentials_prod
  s3:
    region: ap-southeast-5
    secret-name: aws_credentials_prod
    bucket-name: eroses-backend-docs-prod
    domain-name: doc.ros.gov.my
    old-domain-name: eroses-backend-docs-prod.s3.ap-southeast-5.amazonaws.com
    template:
      meeting-minutes: https://eroses-backend-docs-dev.s3.ap-southeast-5.amazonaws.com/asset/MeetingMinutes.docx
      payment-counter: https://eroses-backend-docs-dev.s3.ap-southeast-5.amazonaws.com/asset/Slip+pengesahan+pembayaran+kaunter.docx
      payment-online: https://eroses-backend-docs-dev.s3.ap-southeast-5.amazonaws.com/asset/Slip+pengesahan+pembayaran+kaunter.docx
  quicksight:
    region: ap-southeast-1
    secret-name: aws_credentials_prod
    account-id: "************"
    user-arn: arn:aws:quicksight:ap-southeast-1:************:user/default/<EMAIL>
    session-timeout-minutes: 60
    undo-redo-disabled: false
    reset-disabled: false
    access-key: ********************
    secret-key: 9vyuqt8vTVwQqQVqYaM9FPAFHTTPhoNyQxXJU8qi

jwt:
  secret: I8xKJ4TLOORtjtXotP3tW4RJmcqgn5+ag9VGzvS5x2zeWhgw8qs0RI4uXHfhNtnLsJjDJizYIWUUPyYzzbScGQ==
  expiration-time: **********

awsS3:
  bucket-name: eroses-backend-docs-prod
  region: ap-southeast-5
  secret-name: aws_credentials_prod

userClient:
  url: https://a-erosesv2.ros.gov.my
  loginUrl: https://a-erosesv2.ros.gov.my/user/auth/login
  notification_url: https://a-erosesv2.ros.gov.my/notification
  document_url: https://a-erosesv2.ros.gov.my/document
  username: SOCIETY_SERVICE
  password: society4883#68567@8302948
