-- Training Course Table
CREATE TABLE training_course (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    difficulty_level ENUM('<PERSON>as', '<PERSON><PERSON><PERSON>', '<PERSON>hir') NOT NULL,
    status TINYINT NOT NULL DEFAULT 0 COMMENT '0: Draft, 1: Published, 2: Hidden',
    passing_criteria INT NOT NULL DEFAULT 70,
    created_by <PERSON><PERSON><PERSON><PERSON>(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100),
    updated_date TIMESTAMP NULL
);

-- Training Material Table
CREATE TABLE training_material (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    training_course_id BIGINT NOT NULL,
    material_type ENUM('VIDEO', 'PDF', 'TEXT', 'IMAGE') NOT NULL,
    content_path VARCHAR(255),
    content_text TEXT,
    sequence_order INT NOT NULL,
    created_by <PERSON><PERSON><PERSON><PERSON>(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (training_course_id) REFERENCES training_course(id) ON DELETE CASCADE
);

-- Training Quiz Table
CREATE TABLE training_quiz (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    training_course_id BIGINT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    time_limit_minutes INT NOT NULL DEFAULT 60,
    is_mandatory BOOLEAN NOT NULL DEFAULT TRUE,
    created_by VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (training_course_id) REFERENCES training_course(id) ON DELETE CASCADE
);

-- Quiz Question Table
CREATE TABLE quiz_question (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    training_quiz_id BIGINT NOT NULL,
    question_text TEXT NOT NULL,
    question_type VARCHAR(50) NOT NULL DEFAULT 'MULTIPLE_CHOICE',
    sequence_order INT NOT NULL,
    created_by VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (training_quiz_id) REFERENCES training_quiz(id) ON DELETE CASCADE
);

-- Quiz Option Table
CREATE TABLE quiz_option (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    quiz_question_id BIGINT NOT NULL,
    option_text TEXT NOT NULL,
    is_correct BOOLEAN NOT NULL DEFAULT FALSE,
    sequence_order INT NOT NULL,
    created_by VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quiz_question_id) REFERENCES quiz_question(id) ON DELETE CASCADE
);

-- Training Enrollment Table
CREATE TABLE training_enrollment (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    training_course_id BIGINT NOT NULL,
    user_id VARCHAR(100) NOT NULL,
    enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completion_date TIMESTAMP NULL,
    completion_status ENUM('IN_PROGRESS', 'COMPLETED', 'FAILED') NOT NULL DEFAULT 'IN_PROGRESS',
    certificate_id VARCHAR(100),
    FOREIGN KEY (training_course_id) REFERENCES training_course(id)
);

-- Quiz Attempt Table
CREATE TABLE quiz_attempt (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    training_enrollment_id BIGINT NOT NULL,
    training_quiz_id BIGINT NOT NULL,
    attempt_number INT NOT NULL,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP NULL,
    score INT DEFAULT 0,
    passed BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (training_enrollment_id) REFERENCES training_enrollment(id),
    FOREIGN KEY (training_quiz_id) REFERENCES training_quiz(id)
);

-- Quiz Answer Table
CREATE TABLE quiz_answer (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    quiz_attempt_id BIGINT NOT NULL,
    quiz_question_id BIGINT NOT NULL,
    quiz_option_id BIGINT NOT NULL,
    is_correct BOOLEAN NOT NULL DEFAULT FALSE,
    FOREIGN KEY (quiz_attempt_id) REFERENCES quiz_attempt(id),
    FOREIGN KEY (quiz_question_id) REFERENCES quiz_question(id),
    FOREIGN KEY (quiz_option_id) REFERENCES quiz_option(id)
);

-- Training Certificate Table
CREATE TABLE training_certificate (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    training_enrollment_id BIGINT NOT NULL,
    certificate_path VARCHAR(255) NOT NULL,
    issue_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verification_code VARCHAR(100) NOT NULL,
    FOREIGN KEY (training_enrollment_id) REFERENCES training_enrollment(id)
);

-- Index for performance
CREATE INDEX idx_training_course_status ON training_course(status);
CREATE INDEX idx_training_enrollment_user ON training_enrollment(user_id);
CREATE INDEX idx_training_enrollment_course ON training_enrollment(training_course_id);
CREATE INDEX idx_quiz_attempt_enrollment ON quiz_attempt(training_enrollment_id);
