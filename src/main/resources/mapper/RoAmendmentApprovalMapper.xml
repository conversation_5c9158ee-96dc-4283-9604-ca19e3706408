<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="RoAmendmentApproval">
    <resultMap id="RoAmendmentApprovalMap" type="com.eroses.external.society.model.RoAmendmentApproval">
        <id property="id" column="id" />
        <result column="id_minit" property="idMinit"/>
        <result column="amendment_id" property="amendmentId"/>
        <result column="society_no" property="societyNo"/>
        <result column="decision" property="decision"/>
        <result column="notes" property="notes"/>
        <result column="decision_date" property="decisionDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <sql id="tb">
        `ro_amendment_approval`
    </sql>

    <sql id="cols">
        `id_minit`,
        `amendment_id`,
        `society_no`,
        `decision`,
        `notes`,
        `decision_date`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="vals">
        #{idMinit},
        #{amendmentId},
        #{societyNo},
        #{decision},
        #{notes},
        #{decisionDate},
        #{createdBy},
        now(),
        #{modifiedBy},
        now()
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.RoAmendmentApproval" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.RoAmendmentApproval">
        UPDATE <include refid="tb"/>
        <set>
            <if test="idMinit != null">`id_minit` = #{idMinit},</if>
            <if test="amendmentId != null">`amendment_id` = #{amendmentId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="decision != null">`decision` = #{decision},</if>
            <if test="notes != null">`notes` = #{notes},</if>
            <if test="decisionDate != null">`decision_date` = #{decisionDate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            modified_date=NOW()
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="get" parameterType="map" resultMap="RoAmendmentApprovalMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="idMinit != null">
            AND `id_minit` = #{idMinit}
        </if>
        <if test="amendmentId != null">
            AND `amendment_id` = #{amendmentId}
        </if>
        <if test="societyNo != null">
            AND `society_no` = #{societyNo}
        </if>
        <if test="decision != null">
            AND `decision` = #{decision}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="getById" parameterType="java.lang.Long" resultMap="RoAmendmentApprovalMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `id` = #{id}
    </select>

</mapper>