<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="ConstitutionValue">
    <resultMap id="ConstitutionValueMap" type="com.eroses.external.society.model.ConstitutionValue">
        <id column="id" property="id"/>
        <result column="amendment_id" property="amendmentId"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="constitution_type_id" property="constitutionTypeId"/>
        <result column="clause_content_id" property="clauseContentId"/>
        <result column="clause_no" property="clauseNo"/>
        <result column="clause_name" property="clauseName"/>
        <result column="constitution_content_id" property="constitutionContentId"/>
<!--        <result column="application_no" property="applicationNo"/>-->
        <result column="society_name" property="societyName"/>
        <result column="title_name" property="titleName"/>
        <result column="definition_name" property="definitionName"/>
        <result column="society_address" property="societyAddress"/>
        <result column="society_city" property="societyCity"/>
        <result column="society_district_code" property="societyDistrictCode"/>
        <result column="society_postcode" property="societyPostcode"/>
        <result column="society_parliment_code" property="societyParlimentCode"/>
        <result column="society_state" property="societyState"/>
        <result column="mailing_address" property="mailingAddress"/>
        <result column="mailing_address_city" property="mailingAddressCity"/>
        <result column="mailing_address_district_code" property="mailingAddressDistrictCode"/>
        <result column="mailing_address_postcode" property="mailingAddressPostcode"/>
        <result column="mailing_address_parliment_code" property="mailingAddressParlimentCode"/>
        <result column="mailing_address_state" property="mailingAddressState"/>
        <result column="meeting_frequency" property="meetingFrequency"/>
        <result column="parent_beginning_fiscal_year" property="parentBeginningFiscalYear"/>
        <result column="number_of_principal_auditors" property="numberOfPrincipalAuditors"/>
        <result column="branch_meeting_frequency" property="branchMeetingFrequency"/>
        <result column="financial_year_beginning_branch" property="financialYearBeginningBranch"/>
        <result column="number_of_branch_auditors" property="numberOfBranchAuditors"/>
        <result column="parent_position" property="parentPosition"/>
        <result column="branch_position" property="branchPosition"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="custom_jawatan" property="customJawatan"/>
<!--        <result column="status_code" property="statusCode"/>-->
    </resultMap>

    <sql id="tb">
        `constitution_value`
    </sql>

    <sql id="cols">
        `amendment_id`,`society_id`, `society_no`, `constitution_type_id`, `clause_content_id`, `clause_no`, `clause_name`,
        `constitution_content_id`, `society_name`, `title_name`, `definition_name`,
        `society_address`, `society_city`, `society_district_code`, `society_postcode`,
        `society_parliment_code`, `society_state`, `mailing_address`, `mailing_address_city`,
        `mailing_address_district_code`, `mailing_address_postcode`, `mailing_address_parliment_code`, `mailing_address_state`,
        `meeting_frequency`, `parent_beginning_fiscal_year`, `number_of_principal_auditors`,
        `branch_meeting_frequency`, `financial_year_beginning_branch`, `number_of_branch_auditors`, `parent_position`,
        `branch_position`, `created_by`, `created_date`, `modified_by`,
        `modified_date`, `application_status_code`, `custom_jawatan`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.ConstitutionValue" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{amendmentId}, #{societyId}, #{societyNo}, #{constitutionTypeId}, #{clauseContentId}, #{clauseNo}, #{clauseName},
        #{constitutionContentId}, #{societyName}, #{titleName},
        #{definitionName}, #{societyAddress}, #{societyCity},
        #{societyDistrictCode}, #{societyPostcode}, #{societyParlimentCode}, #{societyState},
        #{mailingAddress}, #{mailingAddressCity}, #{mailingAddressDistrictCode}, #{mailingAddressPostcode}, #{mailingAddressParlimentCode},
        #{mailingAddressState}, #{meetingFrequency}, #{parentBeginningFiscalYear},
        #{numberOfPrincipalAuditors}, #{branchMeetingFrequency}, #{financialYearBeginningBranch}, #{numberOfBranchAuditors},
        #{parentPosition}, #{branchPosition}, #{createdBy}, NOW(), #{modifiedBy}, NOW(), #{applicationStatusCode}, #{customJawatan})
    </insert>

    <select id="findById" parameterType="java.lang.Long" resultMap="ConstitutionValueMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.ConstitutionValue">
        UPDATE <include refid="tb"/>
        <set>
            <trim suffixOverrides=",">
                <if test="amendmentId != null">`amendment_id` = #{amendmentId},</if>
                <if test="societyId != null">`society_id` = #{societyId},</if>
                <if test="societyNo != null">`society_no` = #{societyNo},</if>
                <if test="constitutionTypeId != null">`constitution_type_id` = #{constitutionTypeId},</if>
                <if test="clauseContentId != null">`clause_content_id` = #{clauseContentId},</if>
                <if test="clauseNo != null">`clause_no` = #{clauseNo},</if>
                <if test="clauseName != null">`clause_name` = #{clauseName},</if>
                <if test="constitutionContentId != null">`constitution_content_id` = #{constitutionContentId},</if>
                <if test="societyName != null">`society_name` = #{societyName},</if>
                <if test="titleName != null">`title_name` = #{titleName},</if>
                <if test="definitionName != null">`definition_name` = #{definitionName},</if>
                <if test="societyAddress != null">`society_address` = #{societyAddress},</if>
                <if test="societyCity != null">`society_city` = #{societyCity},</if>
                <if test="societyDistrictCode != null">`society_district_code` = #{societyDistrictCode},</if>
                <if test="societyPostcode != null">`society_postcode` = #{societyPostcode},</if>
                <if test="societyParlimentCode != null">`society_parliment_code` = #{societyParlimentCode},</if>
                <if test="societyState != null">`society_state` = #{societyState},</if>
                <if test="mailingAddress != null">`mailing_address` = #{mailingAddress},</if>
                <if test="mailingAddressCity != null">`mailing_address_city` = #{mailingAddressCity},</if>
                <if test="mailingAddressDistrictCode != null">`mailing_address_district_code` = #{mailingAddressDistrictCode},</if>
                <if test="mailingAddressPostcode != null">`mailing_address_postcode` = #{mailingAddressPostcode},</if>
                <if test="mailingAddressParlimentCode != null">`mailing_address_parliment_code` = #{mailingAddressParlimentCode},</if>
                <if test="mailingAddressState != null">`mailing_address_state` = #{mailingAddressState},</if>
                <if test="meetingFrequency != null">`meeting_frequency` = #{meetingFrequency},</if>
                <if test="parentBeginningFiscalYear != null">`parent_beginning_fiscal_year` = #{parentBeginningFiscalYear},</if>
                <if test="numberOfPrincipalAuditors != null">`number_of_principal_auditors` = #{numberOfPrincipalAuditors},</if>
                <if test="branchMeetingFrequency != null">`branch_meeting_frequency` = #{branchMeetingFrequency},</if>
                <if test="financialYearBeginningBranch != null">`financial_year_beginning_branch` = #{financialYearBeginningBranch},</if>
                <if test="numberOfBranchAuditors != null">`number_of_branch_auditors` = #{numberOfBranchAuditors},</if>
                <if test="parentPosition != null">`parent_position` = #{parentPosition},</if>
                <if test="branchPosition != null">`branch_position` = #{branchPosition},</if>
                <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
                <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
                <if test="customJawatan != null">`custom_jawatan` = #{customJawatan},</if>
                modified_date=NOW()
            </trim>
        </set>
        <where>
            <if test="constitutionContentId != null">`constitution_content_id` = #{constitutionContentId}</if>
            <if test="titleName != null">AND `title_name` = #{titleName}</if>
        </where>
    </update>

    <update id="updateById" parameterType="com.eroses.external.society.model.ConstitutionValue">
    UPDATE <include refid="tb"/>
    <set>
        <trim suffixOverrides=",">
            <if test="amendmentId != null">`amendment_id` = #{amendmentId},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="constitutionTypeId != null">`constitution_type_id` = #{constitutionTypeId},</if>
            <if test="clauseContentId != null">`clause_content_id` = #{clauseContentId},</if>
            <if test="clauseNo != null">`clause_no` = #{clauseNo},</if>
            <if test="clauseName != null">`clause_name` = #{clauseName},</if>
            <if test="constitutionContentId != null">`constitution_content_id` = #{constitutionContentId},</if>
            <if test="societyName != null">`society_name` = #{societyName},</if>
            <if test="titleName != null">`title_name` = #{titleName},</if>
            <if test="definitionName != null">`definition_name` = #{definitionName},</if>
            <if test="societyAddress != null">`society_address` = #{societyAddress},</if>
            <if test="societyCity != null">`society_city` = #{societyCity},</if>
            <if test="societyDistrictCode != null">`society_district_code` = #{societyDistrictCode},</if>
            <if test="societyPostcode != null">`society_postcode` = #{societyPostcode},</if>
            <if test="societyParlimentCode != null">`society_parliment_code` = #{societyParlimentCode},</if>
            <if test="societyState != null">`society_state` = #{societyState},</if>
            <if test="mailingAddress != null">`mailing_address` = #{mailingAddress},</if>
            <if test="mailingAddressCity != null">`mailing_address_city` = #{mailingAddressCity},</if>
            <if test="mailingAddressDistrictCode != null">`mailing_address_district_code` = #{mailingAddressDistrictCode},</if>
            <if test="mailingAddressPostcode != null">`mailing_address_postcode` = #{mailingAddressPostcode},</if>
            <if test="mailingAddressParlimentCode != null">`mailing_address_parliment_code` = #{mailingAddressParlimentCode},</if>
            <if test="mailingAddressState != null">`mailing_address_state` = #{mailingAddressState},</if>
            <if test="meetingFrequency != null">`meeting_frequency` = #{meetingFrequency},</if>
            <if test="parentBeginningFiscalYear != null">`parent_beginning_fiscal_year` = #{parentBeginningFiscalYear},</if>
            <if test="numberOfPrincipalAuditors != null">`number_of_principal_auditors` = #{numberOfPrincipalAuditors},</if>
            <if test="branchMeetingFrequency != null">`branch_meeting_frequency` = #{branchMeetingFrequency},</if>
            <if test="financialYearBeginningBranch != null">`financial_year_beginning_branch` = #{financialYearBeginningBranch},</if>
            <if test="numberOfBranchAuditors != null">`number_of_branch_auditors` = #{numberOfBranchAuditors},</if>
            <if test="parentPosition != null">`parent_position` = #{parentPosition},</if>
            <if test="branchPosition != null">`branch_position` = #{branchPosition},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="customJawatan != null">`custom_jawatan` = #{customJawatan},</if>
            modified_date=NOW()
        </trim>
    </set>
    <where>
        `id` = #{id}
    </where>
    </update>

    <update id="updateStatus" parameterType="map">
        UPDATE constitution_content
        SET
        `status` = #{status},
        `application_status_code` = #{appStatusCode},
        `modified_by` = #{userId}
        WHERE id IN
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="hardDelete" parameterType="java.util.List">
        DELETE FROM
        <include refid="tb"/>
        <if test="list != null and list.size() > 0">
            WHERE id IN
            <foreach item="id" collection="list" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </delete>


    <select id="findAll" parameterType="map" resultMap="ConstitutionValueMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="amendmentId != null">
            AND `amendment_id` = #{amendmentId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="clauseContentId != null">
            AND `clause_content_id` = #{clauseContentId}
        </if>
        <if test="clauseNo != null">
            AND `clause_no` = #{clauseNo}
        </if>
        <if test="clauseName != null">
            AND `clause_name` = #{clauseName}
        </if>
        <if test="constitutionContentId != null">
            AND `constitution_content_id` = #{constitutionContentId}
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countFindAll" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="amendmentId != null">
            AND amendment_id = #{amendmentId}
        </if>
        <if test="societyId != null">
            AND society_id = #{societyId}
        </if>
        <if test="clauseContentId != null">
            AND clause_content_id = #{clauseContentId}
        </if>
        <if test="clauseNo != null">
            AND `clause_no` = #{clauseNo}
        </if>
        <if test="clauseName != null">
            AND `clause_name` = #{clauseName}
        </if>
        <if test="constitutionContentId != null">
            AND constitution_content_id = #{constitutionContentId}
        </if>
    </select>

    <select id="findByConstitutionContentId" parameterType="java.lang.Long" resultMap="ConstitutionValueMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `constitution_content_id` = #{constitutionContentId}
    </select>

    <select id="findByParam" parameterType="map" resultMap="ConstitutionValueMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="amendmentId != null">
            AND `amendment_id` = #{amendmentId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="clauseContentId != null">
            AND `clause_content_id` = #{clauseContentId}
        </if>
        <if test="clauseNo != null">
            AND `clause_no` = #{clauseNo}
        </if>
        <if test="clauseName != null">
            AND `clause_name` = #{clauseName}
        </if>
        <if test="constitutionContentId != null">
            AND `constitution_content_id` = #{constitutionContentId}
        </if>
        <if test="constitutionTypeId != null">
            AND `constitution_type_id` = #{constitutionTypeId}
        </if>
        <if test="titleName != null">
            AND `title_name` = #{titleName}
        </if>
    </select>

</mapper>