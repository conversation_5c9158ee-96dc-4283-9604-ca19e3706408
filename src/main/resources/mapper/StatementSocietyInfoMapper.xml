<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="StatementSocietyInfo">

    <!-- Result Map -->
    <resultMap id="StatementSocietyInfoMap" type="com.eroses.external.society.model.StatementSocietyInfo">
        <id property="id" column="id"/>
        <result property="societyId" column="society_id"/>
        <result property="societyNo" column="society_no"/>
        <result property="branchId" column="branch_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="statementId" column="statement_id"/>
        <result property="societyName" column="society_name"/>
        <result property="address" column="address"/>
        <result property="category" column="category"/>
        <result property="subcategory" column="subcategory"/>
        <result property="societyPhoneNo" column="society_phone_no"/>
        <result property="societyFaxNo" column="society_fax_no"/>
        <result property="financialYearStart" column="financial_year_start"/>
        <result property="memberCount" column="member_count"/>
        <result property="committeeCount" column="committee_count"/>
        <result property="federation" column="federation"/>
        <result property="meetingFrequency" column="meeting_frequency"/>
        <result property="meetingDate" column="meeting_date"/>
        <result property="regMemberCount" column="reg_member_count"/>
        <result property="votingMemberCount" column="voting_member_count"/>
        <result property="attendanceCount" column="attendance_count"/>
        <result property="approvalStatement" column="approval_statement"/>
        <result property="statementDate" column="statement_date"/>
        <result property="submissionDate" column="submission_date"/>
        <result property="acknowledgeAjk" column="acknowledge_ajk"/>
        <result property="applicationStatusCode" column="application_status_code"/>
        <result property="ro" column="ro"/>
        <result property="meetingId" column="meeting_id"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>

    <resultMap id="StatementSocietyInfoGetOneMap" type="com.eroses.external.society.dto.response.statement.StatementSocietyInfoGetOneResponse">
        <result column="ssi_society_id" property="societyId"/>
        <result column="ssi_society_no" property="societyNo"/>
        <result column="ssi_branch_id" property="branchId"/>
        <result column="ssi_branch_no" property="branchNo"/>
        <result column="ssi_statement_id" property="statementId"/>
        <result column="ssi_society_name" property="societyName"/>
        <result column="ssi_address" property="address"/>
        <result column="ssi_category" property="categoryCodeJppm"/>
        <result column="ssi_subcategory" property="subCategoryCode"/>
        <result column="ssi_society_phone_no" property="societyPhoneNo"/>
        <result column="ssi_society_fax_no" property="societyFaxNo"/>
        <result column="ssi_financial_year_start" property="financialYearStart"/>
        <result column="ssi_member_count" property="memberCount"/>
        <result column="ssi_committee_count" property="committeeCount"/>
        <result column="ssi_federation" property="federation"/>
        <result column="ssi_meeting_id" property="meetingId"/>
        <result column="ssi_meeting_frequency" property="meetingFrequency"/>
        <result column="ssi_meeting_date" property="meetingDate"/>
        <result column="ssi_reg_member_count" property="regMemberCount"/>
        <result column="ssi_voting_member_count" property="votingMemberCount"/>
        <result column="ssi_attendance_count" property="attendanceCount"/>
        <result column="ssi_approval_statement" property="approvalStatement"/>
        <result column="ssi_statement_date" property="statementDate"/>
        <result column="ssi_submission_date" property="submissionDate"/>
        <result column="ssi_acknowledge_ajk" property="acknowledgeAjk"/>
        <result column="ssi_application_status_code" property="applicationStatusCode"/>
        <result column="ssi_ro" property="ro"/>

<!--        from statement-->
        <result column="stg_akuan_setuju" property="akuanSetuju"/>
        <result column="stg_akuan_setuju_induk" property="akuanSetujuInduk"/>
        <result column="stg_akuan_papar" property="akuanPapar"/>

<!--        audit-->
        <result column="ssi_created_by" property="createdBy"/>
        <result column="ssi_created_date" property="createdDate"/>
        <result column="ssi_modified_by" property="modifiedBy"/>
        <result column="ssi_modified_date" property="modifiedDate"/>
    </resultMap>

    <!-- Table Name -->
    <sql id="tb">
        `statement_society_info`
    </sql>

    <sql id="selectTb">
        `statement_society_info` ssi
    </sql>

    <!-- Columns without ID -->
    <sql id="cols">
        `society_id`, `society_no`, `branch_id`, `branch_no`, `statement_id`,
        `society_name`, `address`, `category`, `subcategory`, `society_phone_no`,
        `society_fax_no`, `financial_year_start`, `member_count`, `committee_count`,
        `federation`, `meeting_id`, `meeting_frequency`, `meeting_date`, `reg_member_count`,
        `voting_member_count`, `attendance_count`, `approval_statement`, `statement_date`,
        `submission_date`, `acknowledge_ajk`, `application_status_code`, `ro`,
        `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="selectCols">
        ssi.`society_id` AS ssi_society_id,
        ssi.`society_no` AS ssi_society_no,
        ssi.`branch_id` AS ssi_branch_id,
        ssi.`branch_no` AS ssi_branch_no,
        ssi.`statement_id` AS ssi_statement_id,
        ssi.`society_name` AS ssi_society_name,
        ssi.`address` AS ssi_address,
        ssi.`category` AS ssi_category,
        ssi.`subcategory` AS ssi_subcategory,
        ssi.`society_phone_no` AS ssi_society_phone_no,
        ssi.`society_fax_no` AS ssi_society_fax_no,
        ssi.`financial_year_start` AS ssi_financial_year_start,
        ssi.`member_count` AS ssi_member_count,
        ssi.`committee_count` AS ssi_committee_count,
        ssi.`federation` AS ssi_federation,
        ssi.`meeting_id` AS ssi_meeting_id,
        ssi.`meeting_frequency` AS ssi_meeting_frequency,
        ssi.`meeting_date` AS ssi_meeting_date,
        ssi.`reg_member_count` AS ssi_reg_member_count,
        ssi.`voting_member_count` AS ssi_voting_member_count,
        ssi.`attendance_count` AS ssi_attendance_count,
        ssi.`approval_statement` AS ssi_approval_statement,
        ssi.`statement_date` AS ssi_statement_date,
        ssi.`submission_date` AS ssi_submission_date,
        ssi.`acknowledge_ajk` AS ssi_acknowledge_ajk,
        ssi.`application_status_code` AS ssi_application_status_code,
        ssi.`ro` AS ssi_ro,
        ssi.`created_by` AS ssi_created_by,
        ssi.`created_date` AS ssi_created_date,
        ssi.`modified_by` AS ssi_modified_by,
        ssi.`modified_date` AS ssi_modified_date
    </sql>

    <!-- Columns with ID -->
    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <!-- Insert Statement -->
    <insert id="create" parameterType="com.eroses.external.society.model.StatementSocietyInfo" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{statementId},
        #{societyName},
        #{address},
        #{category},
        #{subcategory},
        #{societyPhoneNo},
        #{societyFaxNo},
        #{financialYearStart},
        #{memberCount},
        #{committeeCount},
        #{federation},
        #{meetingId},
        #{meetingFrequency},
        #{meetingDate},
        #{regMemberCount},
        #{votingMemberCount},
        #{attendanceCount},
        #{approvalStatement},
        #{statementDate},
        #{submissionDate},
        #{acknowledgeAjk},
        #{applicationStatusCode},
        #{ro},
        #{createdBy},
        NOW(),
        #{modifiedBy},
        NOW()
        )
    </insert>

    <!-- Update Statement -->
    <update id="update" parameterType="com.eroses.external.society.model.StatementSocietyInfo">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="societyName != null">`society_name` = #{societyName},</if>
            <if test="address != null">`address` = #{address},</if>
            <if test="category != null">`category` = #{category},</if>
            <if test="subcategory != null">`subcategory` = #{subcategory},</if>
            <if test="societyPhoneNo != null">`society_phone_no` = #{societyPhoneNo},</if>
            <if test="societyFaxNo != null">`society_fax_no` = #{societyFaxNo},</if>
            <if test="financialYearStart != null">`financial_year_start` = #{financialYearStart},</if>
            <if test="memberCount != null">`member_count` = #{memberCount},</if>
            <if test="committeeCount != null">`committee_count` = #{committeeCount},</if>
            <if test="federation != null">`federation` = #{federation},</if>
            <if test="meetingId != null">`meeting_id` = #{meetingId},</if>
            <if test="meetingFrequency != null">`meeting_frequency` = #{meetingFrequency},</if>
            <if test="meetingDate != null">`meeting_date` = #{meetingDate},</if>
            <if test="regMemberCount != null">`reg_member_count` = #{regMemberCount},</if>
            <if test="votingMemberCount != null">`voting_member_count` = #{votingMemberCount},</if>
            <if test="attendanceCount != null">`attendance_count` = #{attendanceCount},</if>
            <if test="approvalStatement != null">`approval_statement` = #{approvalStatement},</if>
            <if test="statementDate != null">`statement_date` = #{statementDate},</if>
            <if test="submissionDate != null">`submission_date` = #{submissionDate},</if>
            <if test="acknowledgeAjk != null">`acknowledge_ajk` = #{acknowledgeAjk},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="ro != null">`ro` = #{ro},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            `modified_date` = now()
        </set>
        WHERE `statement_id` = #{statementId}
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </update>

    <update id="updateByStatementId" parameterType="com.eroses.external.society.model.StatementSocietyInfo">
        UPDATE
        <include refid="tb"/>
        <set>
<!--            <if test="societyId != null">`society_id` = #{societyId},</if>-->
<!--            <if test="societyNo != null">`society_no` = #{societyNo},</if>-->
<!--            <if test="branchId != null">`branch_id` = #{branchId},</if>-->
<!--            <if test="branchNo != null">`branch_no` = #{branchNo},</if>-->
<!--            <if test="statementId != null">`statement_id` = #{statementId},</if>-->
            <if test="societyName != null">`society_name` = #{societyName},</if>
            <if test="address != null">`address` = #{address},</if>
            <if test="category != null">`category` = #{category},</if>
            <if test="subcategory != null">`subcategory` = #{subcategory},</if>
            <if test="societyPhoneNo != null">`society_phone_no` = #{societyPhoneNo},</if>
            <if test="societyFaxNo != null">`society_fax_no` = #{societyFaxNo},</if>
            <if test="financialYearStart != null">`financial_year_start` = #{financialYearStart},</if>
            <if test="memberCount != null">`member_count` = #{memberCount},</if>
            <if test="committeeCount != null">`committee_count` = #{committeeCount},</if>
            <if test="federation != null">`federation` = #{federation},</if>
            <if test="meetingId != null">`meeting_id` = #{meetingId},</if>
            <if test="meetingFrequency != null">`meeting_frequency` = #{meetingFrequency},</if>
            <if test="meetingDate != null">`meeting_date` = #{meetingDate},</if>
            <if test="regMemberCount != null">`reg_member_count` = #{regMemberCount},</if>
            <if test="votingMemberCount != null">`voting_member_count` = #{votingMemberCount},</if>
            <if test="attendanceCount != null">`attendance_count` = #{attendanceCount},</if>
            <if test="approvalStatement != null">`approval_statement` = #{approvalStatement},</if>
            <if test="statementDate != null">`statement_date` = #{statementDate},</if>
            <if test="submissionDate != null">`submission_date` = #{submissionDate},</if>
            <if test="acknowledgeAjk != null">`acknowledge_ajk` = #{acknowledgeAjk},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="ro != null">`ro` = #{ro},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            `modified_date` = now()
        </set>
        WHERE `statement_id` = #{statementId}
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </update>

    <!-- Select All -->
    <select id="findAll" parameterType="map" resultMap="StatementSocietyInfoMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- Select By ID -->
    <select id="findById" parameterType="java.lang.Long" resultMap="StatementSocietyInfoMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <!-- Count All -->
    <select id="countFindAll" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
    </select>

    <select id="getStatementSocietyInfo" parameterType="map" resultMap="StatementSocietyInfoMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
    </select>

    <select id="findByStatementId" parameterType="map" resultMap="StatementSocietyInfoMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        LIMIT 1
    </select>

    <select id="findByParam" resultMap="StatementSocietyInfoGetOneMap">
        SELECT <include refid="selectCols"/>,
        <include refid="Statement.selectCols"/>
        FROM
        <include refid="selectTb"/>
        LEFT JOIN <include refid="Statement.selectTb"/>
        ON ssi.`statement_id` = stg.`statement_id` AND ssi.`society_id` = stg.`society_id`
        WHERE 1=1
        <if test="societyId != null">
            AND ssi.`society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND ssi.`branch_id` = #{branchId}
        </if>
        <if test="statementId != null">
            AND ssi.`statement_id` = #{statementId}
        </if>
        <if test="year != null">
            AND stg.`statement_year` = #{year}
        </if>
    </select>

    <update id="deleteStatement" parameterType="map">
        UPDATE <include refid="tb"/>
        <set>
            `application_status_code` = -1
        </set>
        WHERE
        `statement_id` = #{statementId}
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </update>
</mapper>
