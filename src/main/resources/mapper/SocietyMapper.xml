<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Society">
    <resultMap id="SocietyMap" type="com.eroses.external.society.model.Society">
        <id property="id" column="id"/>
        <result column="society_no" property="societyNo"/>
        <result column="application_no" property="applicationNo"/>
        <result column="society_name" property="societyName"/>
        <result column="short_name" property="shortName"/>
        <result column="society_level" property="societyLevel"/>
        <result column="constitution_type" property="constitutionType"/>
        <result column="has_branch" property="hasBranch"/>
        <result column="registered_date" property="registeredDate"/>
        <result column="approved_date" property="approvedDate"/>
        <result column="submission_date" property="submissionDate"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="category_code_jppm" property="categoryCodeJppm"/>
        <result column="sub_category_code" property="subCategoryCode"/>
        <result column="country_code" property="countryCode"/>
        <result column="state_id" property="stateCode"/>
        <result column="district_id" property="districtCode"/>
        <result column="small_district_code" property="smallDistrictCode"/>
        <result column="city_code" property="cityCode"/>
        <result column="city" property="city"/>
        <result column="postcode" property="postcode"/>
        <result column="address" property="address"/>
        <result column="address_latitude" property="addressLatitude"/>
        <result column="address_longitude" property="addressLongitude"/>
        <result column="mailing_country_code" property="mailingCountryCode"/>
        <result column="mailing_state_code" property="mailingStateCode"/>
        <result column="mailing_district_code" property="mailingDistrictCode"/>
        <result column="mailing_small_district_code" property="mailingSmallDistrictCode"/>
        <result column="mailing_city_code" property="mailingCityCode"/>
        <result column="mailing_city" property="mailingCity"/>
        <result column="mailing_postcode" property="mailingPostcode"/>
        <result column="mailing_address" property="mailingAddress"/>
        <result column="mailing_address_latitude" property="mailingAddressLatitude"/>
        <result column="mailing_address_longitude" property="mailingAddressLongitude"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="fax_number" property="faxNumber"/>
        <result column="email" property="email"/>
        <result column="status_code" property="statusCode"/>
        <result column="sub_status_code" property="subStatusCode"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="statement" property="statement"/>
        <result column="statement_date" property="statementDate"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="payment_id" property="paymentId"/>
        <result column="id_osol" property="idOsol"/>
        <result column="kod_ptj" property="kodPtj"/>
        <result column="receipt_no" property="receiptNo"/>
        <result column="payment_date" property="paymentDate"/>
        <result column="inquire" property="inquire"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="receipt_status" property="receiptStatus"/>
        <result column="NoPPM_lama" property="noPPMLama"/>
        <result column="NoPPP_lama" property="noPPPLama"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_reference_no" property="bankReferenceNo"/>
        <result column="ro" property="ro"/>
        <result column="ro_batal" property="roBatal"/>
        <result column="ro_update" property="roUpdate"/>
        <result column="akui_ajk" property="akuiAjk"/>
        <result column="bubar" property="bubar"/>
        <result column="appeal_status" property="appealStatus"/>
        <result column="origin_name" property="originName"/>
        <result column="origin_address" property="originAddress"/>
        <result column="origin_state_code" property="originStateCode"/>
        <result column="origin_district_code" property="originDistrictCode"/>
        <result column="origin_small_district_code" property="originSmallDistrictCode"/>
        <result column="origin_postcode" property="originPostcode"/>
        <result column="origin_city" property="originCity"/>
        <result column="notis_1" property="notis1"/>
        <result column="migrate_stat" property="migrateStat"/>
        <result column="migrate_ajk" property="migrateAjk"/>
        <result column="migrate_undang" property="migrateUndang"/>
        <result column="tarikh_alih" property="tarikhAlih"/>
        <result column="reply_notis" property="replyNotis"/>
        <result column="note_ro" property="noteRo"/>
        <result column="stat_bebas" property="statBebas"/>
        <result column="stat_pindakecaw" property="statPindaKecaw"/>
        <result column="appeal_statement" property="appealStatement"/>
        <result column="reconcile_date" property="reconcileDate"/>
        <result column="flat_padam" property="flatPadam"/>
        <result column="rujuk" property="rujuk"/>
        <result column="benar_ajk" property="benarAjk"/>
        <result column="is_queried" property="isQueried"/>
        <result column="approval_reviewed" property="approvalReviewed"/>
    </resultMap>

    <resultMap id="SocietySelectMap" type="com.eroses.external.society.model.Society">
        <id column="society_id" property="id"/>
        <result column="society_society_no" property="societyNo"/>
        <result column="society_application_no" property="applicationNo"/>
        <result column="society_society_name" property="societyName"/>
        <result column="society_short_name" property="shortName"/>
        <result column="society_society_level" property="societyLevel"/>
        <result column="society_constitution_type" property="constitutionType"/>
        <result column="society_has_branch" property="hasBranch"/>
        <result column="society_registered_date" property="registeredDate"/>
        <result column="society_approved_date" property="approvedDate"/>
        <result column="society_submission_date" property="submissionDate"/>
        <result column="society_identification_no" property="identificationNo"/>
        <result column="society_category_code_jppm" property="categoryCodeJppm"/>
        <result column="society_sub_category_code" property="subCategoryCode"/>
        <result column="society_country_code" property="countryCode"/>
        <result column="society_state_code" property="stateCode"/>
        <result column="society_district_code" property="districtCode"/>
        <result column="society_small_district_code" property="smallDistrictCode"/>
        <result column="society_city_code" property="cityCode"/>
        <result column="society_city" property="city"/>
        <result column="society_postcode" property="postcode"/>
        <result column="society_address" property="address"/>
        <result column="society_address_latitude" property="addressLatitude"/>
        <result column="society_address_longitude" property="addressLongitude"/>
        <result column="society_mailing_country_code" property="mailingCountryCode"/>
        <result column="society_mailing_state_code" property="mailingStateCode"/>
        <result column="society_mailing_district_code" property="mailingDistrictCode"/>
        <result column="society_mailing_small_district_code" property="mailingSmallDistrictCode"/>
        <result column="society_mailing_city_code" property="mailingCityCode"/>
        <result column="society_mailing_city" property="mailingCity"/>
        <result column="society_mailing_postcode" property="mailingPostcode"/>
        <result column="society_mailing_address" property="mailingAddress"/>
        <result column="society_mailing_address_latitude" property="mailingAddressLatitude"/>
        <result column="society_mailing_address_longitude" property="mailingAddressLongitude"/>
        <result column="society_phone_number" property="phoneNumber"/>
        <result column="society_fax_number" property="faxNumber"/>
        <result column="society_email" property="email"/>
        <result column="society_status_code" property="statusCode"/>
        <result column="society_sub_status_code" property="subStatusCode"/>
        <result column="society_created_by" property="createdBy"/>
        <result column="society_created_date" property="createdDate"/>
        <result column="society_modified_by" property="modifiedBy"/>
        <result column="society_modified_date" property="modifiedDate"/>
        <result column="society_statement" property="statement"/>
        <result column="society_statement_date" property="statementDate"/>
        <result column="society_payment_method" property="paymentMethod"/>
        <result column="society_payment_id" property="paymentId"/>
        <result column="society_id_osol" property="idOsol"/>
        <result column="society_kod_ptj" property="kodPtj"/>
        <result column="society_receipt_no" property="receiptNo"/>
        <result column="society_payment_date" property="paymentDate"/>
        <result column="society_inquire" property="inquire"/>
        <result column="society_application_status_code" property="applicationStatusCode"/>
        <result column="society_receipt_status" property="receiptStatus"/>
        <result column="society_NoPPM_lama" property="noPPMLama"/>
        <result column="society_NoPPP_lama" property="noPPPLama"/>
        <result column="society_bank_name" property="bankName"/>
        <result column="society_bank_reference_no" property="bankReferenceNo"/>
        <result column="society_ro" property="ro"/>
        <result column="society_ro_batal" property="roBatal"/>
        <result column="society_ro_update" property="roUpdate"/>
        <result column="society_akui_ajk" property="akuiAjk"/>
        <result column="society_bubar" property="bubar"/>
        <result column="society_appeal_status" property="appealStatus"/>
        <result column="society_origin_name" property="originName"/>
        <result column="society_origin_address" property="originAddress"/>
        <result column="society_origin_state_code" property="originStateCode"/>
        <result column="society_origin_district_code" property="originDistrictCode"/>
        <result column="society_origin_small_district_code" property="originSmallDistrictCode"/>
        <result column="society_origin_postcode" property="originPostcode"/>
        <result column="society_origin_city" property="originCity"/>
        <result column="society_notis_1" property="notis1"/>
        <result column="society_migrate_stat" property="migrateStat"/>
        <result column="society_migrate_ajk" property="migrateAjk"/>
        <result column="society_migrate_undang" property="migrateUndang"/>
        <result column="society_tarikh_alih" property="tarikhAlih"/>
        <result column="society_reply_notis" property="replyNotis"/>
        <result column="society_note_ro" property="noteRo"/>
        <result column="society_stat_bebas" property="statBebas"/>
        <result column="society_stat_pindakecaw" property="statPindaKecaw"/>
        <result column="society_appeal_statement" property="appealStatement"/>
        <result column="society_reconcile_date" property="reconcileDate"/>
        <result column="society_flat_padam" property="flatPadam"/>
        <result column="society_rujuk" property="rujuk"/>
        <result column="society_benar_ajk" property="benarAjk"/>
        <result column="society_is_queried" property="isQueried"/>
        <result column="society_approval_reviewed" property="approvalReviewed"/>

        <collection property="branches" resultMap="Branch.BranchSelectMap" ofType="com.eroses.external.society.model.Branch"/>
    </resultMap>

    <resultMap id="SocietyNoMap" type="com.eroses.external.society.model.Society">
        <result column="society_no" property="societyNo"/>
    </resultMap>

    <resultMap id="SocietyBasicMap" type="com.eroses.external.society.dto.response.society.SocietyBasicResponse">
        <id property="id" column="id"/>
        <result column="society_no" property="societyNo"/>
        <result column="society_name" property="societyName"/>
        <result column="constitution_type" property="constitutionType"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="society_level" property="societyLevel"/>
        <result column="application_no" property="applicationNo"/>
        <result column="email" property="email"/>
    </resultMap>

    <resultMap id="SocietyGetOneResponseMap" type="com.eroses.external.society.dto.response.society.SocietyGetOneResponse">
        <id column="society_id" property="id"/>
        <result column="society_society_no" property="societyNo"/>
        <result column="society_application_no" property="applicationNo"/>
        <result column="society_society_name" property="societyName"/>
        <result column="society_short_name" property="shortName"/>
        <result column="society_society_level" property="societyLevel"/>
        <result column="society_registered_date" property="registeredDate"/>
        <result column="society_approved_date" property="approvedDate"/>
        <result column="society_identification_no" property="identificationNo"/>
        <result column="society_category_code_jppm" property="categoryCodeJppm"/>
        <result column="society_sub_category_code" property="subCategoryCode"/>
        <result column="society_country_code" property="countryCode"/>
        <result column="society_state_code" property="stateCode"/>
        <result column="society_district_code" property="districtCode"/>
        <result column="society_small_district_code" property="smallDistrictCode"/>
        <result column="society_city_code" property="cityCode"/>
        <result column="society_city" property="city"/>
        <result column="society_postcode" property="postcode"/>
        <result column="society_address" property="address"/>
        <result column="society_address_latitude" property="addressLatitude"/>
        <result column="society_address_longitude" property="addressLongitude"/>
        <result column="society_mailing_country_code" property="mailingCountryCode"/>
        <result column="society_mailing_state_code" property="mailingStateCode"/>
        <result column="society_mailing_district_code" property="mailingDistrictCode"/>
        <result column="society_mailing_small_district_code" property="mailingSmallDistrictCode"/>
        <result column="society_mailing_city_code" property="mailingCityCode"/>
        <result column="society_mailing_city" property="mailingCity"/>
        <result column="society_mailing_postcode" property="mailingPostcode"/>
        <result column="society_mailing_address" property="mailingAddress"/>
        <result column="society_mailing_address_latitude" property="mailingAddressLatitude"/>
        <result column="society_mailing_address_longitude" property="mailingAddressLongitude"/>
        <result column="society_phone_number" property="phoneNumber"/>
        <result column="society_fax_number" property="faxNumber"/>
        <result column="society_email" property="email"/>
        <result column="society_status_code" property="statusCode"/>
        <result column="society_sub_status_code" property="subStatusCode"/>
        <result column="society_statement" property="statement"/>
        <result column="society_statement_date" property="statementDate"/>
        <result column="society_payment_method" property="paymentMethod"/>
        <result column="society_payment_id" property="paymentId"/>
        <result column="society_id_osol" property="idOsol"/>
        <result column="society_kod_ptj" property="kodPtj"/>
        <result column="society_receipt_no" property="receiptNo"/>
        <result column="society_payment_date" property="paymentDate"/>
        <result column="society_inquire" property="inquire"/>
        <result column="society_application_status_code" property="applicationStatusCode"/>
        <result column="society_receipt_status" property="receiptStatus"/>
        <result column="society_no_ppm_lama" property="noPPMLama"/>
        <result column="society_no_ppp_lama" property="noPPPLama"/>
        <result column="society_bank_name" property="bankName"/>
        <result column="society_bank_reference_no" property="bankReferenceNo"/>
        <result column="society_ro" property="ro"/>
        <result column="society_ro_batal" property="roBatal"/>
        <result column="society_ro_update" property="roUpdate"/>
        <result column="society_akui_ajk" property="akuiAjk"/>
        <result column="society_bubar" property="bubar"/>
        <result column="society_appeal_status" property="appealStatus"/>
        <result column="society_origin_name" property="originName"/>
        <result column="society_origin_address" property="originAddress"/>
        <result column="society_origin_state_code" property="originStateCode"/>
        <result column="society_origin_district_code" property="originDistrictCode"/>
        <result column="society_origin_small_district_code" property="originSmallDistrictCode"/>
        <result column="society_origin_postcode" property="originPostcode"/>
        <result column="society_origin_city" property="originCity"/>
        <result column="society_notis_1" property="notis1"/>
        <result column="society_migrate_stat" property="migrateStat"/>
        <result column="society_migrate_ajk" property="migrateAjk"/>
        <result column="society_migrate_undang" property="migrateUndang"/>
        <result column="society_tarikh_alih" property="tarikhAlih"/>
        <result column="society_reply_notis" property="replyNotis"/>
        <result column="society_note_ro" property="noteRo"/>
        <result column="society_stat_bebas" property="statBebas"/>
        <result column="society_stat_pindakecaw" property="statPindaKecaw"/>
        <result column="society_appeal_statement" property="appealStatement"/>
        <result column="society_reconcile_date" property="reconcileDate"/>
        <result column="society_flat_padam" property="flatPadam"/>
        <result column="society_rujuk" property="rujuk"/>
        <result column="society_benar_ajk" property="benarAjk"/>
        <result column="society_has_branch" property="hasBranch"/>
        <result column="society_constitution_type" property="constitutionType"/>
        <result column="society_is_queried" property="isQueried"/>
        <result column="society_is_manage_authorized" property="isManageAuthorized"/>
        <result column="society_created_by" property="createdBy"/>
        <result column="society_created_date" property="createdDate"/>
        <result column="society_modified_by" property="modifiedBy"/>
        <result column="society_modified_date" property="modifiedDate"/>
        <result column="designation_code" property="designationCode"/>
        <result column="society_approval_reviewed" property="approvalReviewed"/>
    </resultMap>

    <sql id="tb">
        `society`
    </sql>

    <sql id="selectTb">
        `society` s
    </sql>

    <sql id="selectColsForAppeal">
        ap.`id` AS ap_id,
        ap.`appeal_no` AS ap_appeal_no,
        ap.`id_sebab` AS ap_id_sebab,
        ap.`appeal_date` AS ap_appeal_date,
        ap.`application_status_code` AS ap_application_status_code,
        ap.`approved_date` AS ap_approved_date,
        s.`id` AS s_society_id,
        s.`society_name` AS s_society_name,
        s.`application_status_code` AS s_application_status_code
    </sql>

    <sql id="cols">
        `society_no`, `application_no`, `society_name`, `short_name`, `society_level`,`constitution_type`,`has_branch`,
        `registered_date`, `approved_date`, `submission_date`, `identification_no`, `category_code_jppm`,
        `sub_category_code`, `country_code`, `state_id`, `district_id`,
        `small_district_code`, `city_code`, `city`, `postcode`, `address`,
        `address_latitude`, `address_longitude`,
        `mailing_country_code`, `mailing_state_code`, `mailing_district_code`,
        `mailing_small_district_code`, `mailing_city_code`, `mailing_city`, `mailing_postcode`,
        `mailing_address`, `mailing_address_latitude`, `mailing_address_longitude`,
        `phone_number`, `fax_number`, `email`,
        `status_code`, `sub_status_code`, `created_by`, `created_date`, `modified_by`,
        `modified_date`, `statement`, `statement_date`, `payment_method`,
        `payment_id`, `id_osol`, `kod_ptj`, `receipt_no`,
        `payment_date`, `inquire`, `application_status_code`, `receipt_status`,
        `NoPPM_lama`, `NoPPP_lama`, `bank_name`, `bank_reference_no`,
        `ro`, `ro_batal`, `ro_update`, `akui_ajk`,
        `bubar`, `appeal_status`, `origin_name`, `origin_address`,
        `origin_state_code`, `origin_district_code`, `origin_small_district_code`,
        `origin_postcode`, `origin_city`, `notis_1`, `migrate_stat`,
        `migrate_ajk`, `migrate_undang`, `tarikh_alih`, `reply_notis`,
        `note_ro`, `stat_bebas`, `stat_pindakecaw`, `appeal_statement`,
        `reconcile_date`, `flat_padam`, `rujuk`, `benar_ajk`, `is_queried`, `approval_reviewed`
    </sql>

    <sql id="selectCols">
        s.`id` as society_id,
        s.`society_no` as society_society_no,
        s.`application_no` as society_application_no,
        s.`society_name` as society_society_name,
        s.`short_name` as society_short_name,
        s.`society_level` as society_society_level,
        s.`constitution_type` as society_constitution_type,
        s.`has_branch` as society_has_branch,
        s.`registered_date` as society_registered_date,
        s.`approved_date` as society_approved_date,
        s.`submission_date` as society_submission_date,
        s.`identification_no` as society_identification_no,
        s.`category_code_jppm` as society_category_code_jppm,
        s.`sub_category_code` as society_sub_category_code,
        s.`country_code` as society_country_code,
        s.`state_id` as society_state_code,
        s.`district_id` as society_district_code,
        s.`small_district_code` as society_small_district_code,
        s.`city_code` as society_city_code,
        s.`city` as society_city,
        s.`postcode` as society_postcode,
        s.`address` as society_address,
        s.`address_latitude` as society_address_latitude,
        s.`address_longitude` as society_address_longitude,
        s.`mailing_country_code` as society_mailing_country_code,
        s.`mailing_state_code` as society_mailing_state_code,
        s.`mailing_district_code` as society_mailing_district_code,
        s.`mailing_small_district_code` as society_mailing_small_district_code,
        s.`mailing_city_code` as society_mailing_city_code,
        s.`mailing_city` as society_mailing_city,
        s.`mailing_postcode` as society_mailing_postcode,
        s.`mailing_address` as society_mailing_address,
        s.`mailing_address_latitude` as society_mailing_address_latitude,
        s.`mailing_address_longitude` as society_mailing_address_longitude,
        s.`phone_number` as society_phone_number,
        s.`fax_number` as society_fax_number,
        s.`email` as society_email,
        s.`status_code` as society_status_code,
        s.`sub_status_code` as society_sub_status_code,
        s.`created_by` as society_created_by,
        s.`created_date` as society_created_date,
        s.`modified_by` as society_modified_by,
        s.`modified_date` as society_modified_date,
        s.`statement` as society_statement,
        s.`statement_date` as society_statement_date,
        s.`payment_method` as society_payment_method,
        s.`payment_id` as society_payment_id,
        s.`id_osol` as society_id_osol,
        s.`kod_ptj` as society_kod_ptj,
        s.`receipt_no` as society_receipt_no,
        s.`payment_date` as society_payment_date,
        s.`inquire` as society_inquire,
        s.`application_status_code` as society_application_status_code,
        s.`receipt_status` as society_receipt_status,
        s.`NoPPM_lama` as society_NoPPM_lama,
        s.`NoPPP_lama` as society_NoPPP_lama,
        s.`bank_name` as society_bank_name,
        s.`bank_reference_no` as society_bank_reference_no,
        s.`ro` as society_ro,
        s.`ro_batal` as society_ro_batal,
        s.`ro_update` as society_ro_update,
        s.`akui_ajk` as society_akui_ajk,
        s.`bubar` as society_bubar,
        s.`appeal_status` as society_appeal_status,
        s.`origin_name` as society_origin_name,
        s.`origin_address` as society_origin_address,
        s.`origin_state_code` as society_origin_state_code,
        s.`origin_district_code` as society_origin_district_code,
        s.`origin_small_district_code` as society_origin_small_district_code,
        s.`origin_postcode` as society_origin_postcode,
        s.`origin_city` as society_origin_city,
        s.`notis_1` as society_notis_1,
        s.`migrate_stat` as society_migrate_stat,
        s.`migrate_ajk` as society_migrate_ajk,
        s.`migrate_undang` as society_migrate_undang,
        s.`tarikh_alih` as society_tarikh_alih,
        s.`reply_notis` as society_reply_notis,
        s.`note_ro` as society_note_ro,
        s.`stat_bebas` as society_stat_bebas,
        s.`stat_pindakecaw` as society_stat_pindakecaw,
        s.`appeal_statement` as society_appeal_statement,
        s.`reconcile_date` as society_reconcile_date,
        s.`flat_padam` as society_flat_padam,
        s.`rujuk` as society_rujuk,
        s.`benar_ajk` as society_benar_ajk,
        s.`is_queried` as society_is_queried,
        s.`approval_reviewed` as society_approval_reviewed
    </sql>

    <sql id="PagingBranchCountCols">
        id,
        society_name
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.Society" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{societyNo}, #{applicationNo}, #{societyName}, #{shortName},
        #{societyLevel}, #{constitutionType}, #{hasBranch}, CURDATE(), #{approvedDate}, #{submissionDate}, #{identificationNo},
        #{categoryCodeJppm},
        #{subCategoryCode}, #{countryCode}, #{stateCode}, #{districtCode},
        #{smallDistrictCode}, #{cityCode}, #{city}, #{postcode}, #{address},
        #{addressLatitude}, #{addressLongitude},
        #{mailingCountryCode}, #{mailingStateCode}, #{mailingDistrictCode},
        #{mailingSmallDistrictCode}, #{mailingCityCode}, #{mailingCity}, #{mailingPostcode},
        #{mailingAddress}, #{mailingAddressLatitude}, #{mailingAddressLongitude},
        #{phoneNumber}, #{faxNumber}, #{email},
        #{statusCode}, #{subStatusCode}, #{createdBy}, NOW(), #{modifiedBy},
        NOW(), #{statement}, #{statementDate}, #{paymentMethod},
        #{paymentId}, #{idOsol}, #{kodPtj}, #{receiptNo},
        #{paymentDate}, #{inquire}, #{applicationStatusCode}, #{receiptStatus},
        #{noPPMLama}, #{noPPPLama}, #{bankName}, #{bankReferenceNo},
        #{ro}, #{roBatal}, #{roUpdate}, #{akuiAjk},
        #{bubar}, #{appealStatus}, #{originName}, #{originAddress},
        #{originStateCode}, #{originDistrictCode}, #{originSmallDistrictCode},
        #{originPostcode}, #{originCity}, #{notis1}, #{migrateStat},
        #{migrateAjk}, #{migrateUndang}, #{tarikhAlih}, #{replyNotis},
        #{noteRo}, #{statBebas}, #{statPindaKecaw}, #{appealStatement},
        #{reconcileDate}, #{flatPadam}, #{rujuk}, #{benarAjk}, #{isQueried}, #{approvalReviewed})
    </insert>

    <select id="getAllCurrentUserSocietyBranchList" parameterType="map" resultMap="SocietySelectMap">
        SELECT <include refid="selectCols"/>,
        <include refid="Branch.selectCols"/>
        FROM (
            SELECT s.*
            FROM society s
            WHERE s.id IN (
                <foreach item="item" collection="societyIds" separator=",">
                    #{item}
                </foreach>
            )
            AND s.application_status_code = #{lulusApplicationStatusCode}
            AND s.status_code = #{activeStatusCode}
            AND s.constitution_type IN (
            <foreach item="item" collection="constitutionType" separator=",">
                #{item}
            </foreach>
            )
            <if test="societyName != null">
                <bind name="wildcardName" value="'%' + societyName + '%'" />
                AND s.society_name LIKE #{wildcardName}
            </if>
            ORDER BY s.approved_date DESC
            LIMIT #{offset}, #{limit}
        ) s
        LEFT JOIN branch b ON s.id = b.society_id
        ORDER BY s.approved_date DESC, b.created_date DESC
    </select>

    <select id="countAllCurrentUserSocietyBranchList" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM society s
        WHERE s.id IN (
            <foreach item="item" collection="societyIds" separator=",">
                #{item}
            </foreach>
        )
        AND s.application_status_code = #{lulusApplicationStatusCode}
        AND s.status_code = #{activeStatusCode}
        AND s.constitution_type IN (
            <foreach item="item" collection="constitutionType" separator=",">
                #{item}
            </foreach>
        )
        <if test="societyName != null">
            <bind name="wildcardName" value="'%' + societyName + '%'" />
            AND s.society_name LIKE #{wildcardName}
        </if>
    </select>

    <select id="countRegisteredToday" resultType="int">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE registered_date = CURDATE()
    </select>

    <select id="countApprovedToday" resultType="int">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE approved_date = CURDATE()
    </select>

    <select id="findById" parameterType="java.lang.Long" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="findBasicById" parameterType="java.lang.Long" resultMap="SocietyBasicMap">
        SELECT
        id,
        society_no,
        society_name,
        constitution_type,
        identification_no,
        society_level,
        application_no,
        email
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
    </select>

    <select id="findSocietyNo" parameterType="java.lang.Long" resultMap="SocietyNoMap">
        SELECT
        society_no
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>
    <select id="findByIds" parameterType="list" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        AND `id` IN
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
    <update id="update" parameterType="com.eroses.external.society.model.Society">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="applicationNo != null">`application_no` = #{applicationNo},</if>
            <if test="societyName != null">`society_name` = #{societyName},</if>
            <if test="shortName != null">`short_name` = #{shortName},</if>
            <if test="societyLevel != null">`society_level` = #{societyLevel},</if>
            <if test="constitutionType != null">`constitution_type` = #{constitutionType},</if>
            <if test="hasBranch != null">`has_branch` = #{hasBranch},</if>
            <if test="registeredDate != null">`registered_date` = #{registeredDate},</if>
            <if test="approvedDate != null">`approved_date` = #{approvedDate},</if>
            <if test="submissionDate != null">`submission_date` = #{submissionDate},</if>
            <if test="identificationNo != null">`identification_no` = #{identificationNo},</if>
            <if test="categoryCodeJppm != null">`category_code_jppm` = #{categoryCodeJppm},</if>
            <if test="subCategoryCode != null">`sub_category_code` = #{subCategoryCode},</if>
            <if test="countryCode != null">`country_code` = #{countryCode},</if>
            <if test="stateCode != null">`state_id` = #{stateCode},</if>
            <if test="districtCode != null">`district_id` = #{districtCode},</if>
            <if test="smallDistrictCode != null">`small_district_code` = #{smallDistrictCode},</if>
            <if test="cityCode != null">`city_code` = #{cityCode},</if>
            <if test="city != null">`city` = #{city},</if>
            <if test="postcode != null">`postcode` = #{postcode},</if>
            <if test="address != null">`address` = #{address},</if>
            <if test="addressLatitude != null">`address_latitude` = #{addressLatitude},</if>
            <if test="addressLongitude != null">`address_longitude` = #{addressLongitude},</if>
            <if test="mailingCountryCode != null">`mailing_country_code` = #{mailingCountryCode},</if>
            <if test="mailingStateCode != null">`mailing_state_code` = #{mailingStateCode},</if>
            <if test="mailingDistrictCode != null">`mailing_district_code` = #{mailingDistrictCode},</if>
            <if test="mailingSmallDistrictCode != null">`mailing_small_district_code` = #{mailingSmallDistrictCode},
            </if>
            <if test="mailingCityCode != null">`mailing_city_code` = #{mailingCityCode},</if>
            <if test="mailingCity != null">`mailing_city` = #{mailingCity},</if>
            <if test="mailingPostcode != null">`mailing_postcode` = #{mailingPostcode},</if>
            <if test="mailingAddress != null">`mailing_address` = #{mailingAddress},</if>
            <if test="mailingAddressLatitude != null">`mailing_address_latitude` = #{mailingAddressLatitude},</if>
            <if test="mailingAddressLongitude != null">`mailing_address_longitude` = #{mailingAddressLongitude},</if>
            <if test="phoneNumber != null">`phone_number` = #{phoneNumber},</if>
            <if test="faxNumber != null">`fax_number` = #{faxNumber},</if>
            <if test="email != null">`email` = #{email},</if>
            <if test="statusCode != null">`status_code` = #{statusCode},</if>
            <if test="subStatusCode != null">`sub_status_code` = #{subStatusCode},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="statement != null">`statement` = #{statement},</if>
            <if test="statementDate != null">`statement_date` = #{statementDate},</if>
            <if test="paymentMethod != null">`payment_method` = #{paymentMethod},</if>
            <if test="paymentId != null">`payment_id` = #{paymentId},</if>
            <if test="idOsol != null">`id_osol` = #{idOsol},</if>
            <if test="kodPtj != null">`kod_ptj` = #{kodPtj},</if>
            <if test="receiptNo != null">`receipt_no` = #{receiptNo},</if>
            <if test="paymentDate != null">`payment_date` = #{paymentDate},</if>
            <if test="inquire != null">`inquire` = #{inquire},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="receiptStatus != null">`receipt_status` = #{receiptStatus},</if>
            <if test="noPPMLama != null">`NoPPM_lama` = #{noPPMLama},</if>
            <if test="noPPPLama != null">`NoPPP_lama` = #{noPPPLama},</if>
            <if test="bankName != null">`bank_name` = #{bankName},</if>
            <if test="bankReferenceNo != null">`bank_reference_no` = #{bankReferenceNo},</if>
            <if test="ro != null">`ro` = #{ro},</if>
            <if test="roBatal != null">`ro_batal` = #{roBatal},</if>
            <if test="roUpdate != null">`ro_update` = #{roUpdate},</if>
            <if test="akuiAjk != null">`akui_ajk` = #{akuiAjk},</if>
            <if test="bubar != null">`bubar` = #{bubar},</if>
            <if test="appealStatus != null">`appeal_status` = #{appealStatus},</if>
            <if test="originName != null">`origin_name` = #{originName},</if>
            <if test="originAddress != null">`origin_address` = #{originAddress},</if>
            <if test="originStateCode != null">`origin_state_code` = #{originStateCode},</if>
            <if test="originDistrictCode != null">`origin_district_code` = #{originDistrictCode},</if>
            <if test="originSmallDistrictCode != null">`origin_small_district_code` = #{originSmallDistrictCode},</if>
            <if test="originPostcode != null">`origin_postcode` = #{originPostcode},</if>
            <if test="originCity != null">`origin_city` = #{originCity},</if>
            <if test="notis1 != null">`notis_1` = #{notis1},</if>
            <if test="migrateStat != null">`migrate_stat` = #{migrateStat},</if>
            <if test="migrateAjk != null">`migrate_ajk` = #{migrateAjk},</if>
            <if test="migrateUndang != null">`migrate_undang` = #{migrateUndang},</if>
            <if test="tarikhAlih != null">`tarikh_alih` = #{tarikhAlih},</if>
            <if test="replyNotis != null">`reply_notis` = #{replyNotis},</if>
            <if test="noteRo != null">`note_ro` = #{noteRo},</if>
            <if test="statBebas != null">`stat_bebas` = #{statBebas},</if>
            <if test="statPindaKecaw != null">`stat_pindakecaw` = #{statPindaKecaw},</if>
            <if test="appealStatement != null">`appeal_statement` = #{appealStatement},</if>
            <if test="reconcileDate != null">`reconcile_date` = #{reconcileDate},</if>
            <if test="flatPadam != null">`flat_padam` = #{flatPadam},</if>
            <if test="rujuk != null">`rujuk` = #{rujuk},</if>
            <if test="benarAjk != null">`benar_ajk` = #{benarAjk},</if>
            <if test="isQueried != null">`is_queried` = #{isQueried},</if>
            <if test="approvalReviewed != null">`approval_reviewed` = #{approvalReviewed},</if>
            modified_date=NOW()
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="findAll" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
    </select>

    <select id="searchSocieties" parameterType="map" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyNo != null">
            AND society_no = #{societyNo}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            society_name LIKE CONCAT('%', #{searchQuery}, '%')
            OR society_no LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="statusCode != null and statusCode !=''">
            AND status_code = #{statusCode}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countSearchedSocieties" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            society_name LIKE CONCAT('%', #{searchQuery}, '%')
            OR society_no LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="statusCode != null and statusCode !=''">
            AND status_code = #{statusCode}
        </if>
    </select>

    <select id="findAllPending" parameterType="map" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND application_status_code IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND ro = #{ro}
        </if>
        <if test="isQueried == 0"> <!-- Cater for NULL data -->
            AND (is_queried IS NULL OR is_queried = 0)
        </if>
        <if test="isQueried != null and isQueried != 0">
            AND is_queried = #{isQueried}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND category_code_jppm = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND sub_category_code = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND society_name LIKE CONCAT('%', #{societyName}, '%')
        </if>
        ORDER BY payment_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countFindAllPending" resultType="long">
        SELECT COUNT(*)
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
    </select>

    <select id="findAllPendingUserStateCode" parameterType="map" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND `state_id` = #{stateCode}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countFindAllPendingUserStateCode" parameterType="map" resultType="long">
        SELECT COUNT(*)
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND `state_id` = #{stateCode}
        </if>
    </select>

    <select id="findPendingById" parameterType="map" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        AND
        `application_status_code` = #{applicationStatusCode}
        LIMIT 1
    </select>

    <select id="searchSocietiesByCriteria" parameterType="map" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyIdList != null and societyIdList.size > 0">
            AND id IN
            <foreach item="id" index="index" collection="societyIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="societyIdList == null or societyIdList.size == 0">
            AND 1 = 0
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
                society_name LIKE CONCAT('%', #{searchQuery}, '%')
                OR society_no LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="statusCode != null and statusCode != ''">
            AND `status_code` = #{statusCode}
        </if>
        <if test="registeredYear != null and registeredYear != 0">
            AND YEAR(`registered_date`) = #{registeredYear}
        </if>
        AND application_status_code NOT IN (43, -1)
        ORDER BY registered_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countSearchedSocietiesByCriteria" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyIdList != null and societyIdList.size > 0">
            AND id IN
            <foreach item="id" index="index" collection="societyIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="societyIdList == null or societyIdList.size == 0">
            AND 1 = 0
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
                society_name LIKE CONCAT('%', #{searchQuery}, '%')
                OR society_no LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="statusCode != null and statusCode != ''">
            AND `status_code` = #{statusCode}
        </if>
        <if test="registeredYear != null and registeredYear != 0">
            AND YEAR(`registered_date`) = #{registeredYear}
        </if>
        AND application_status_code NOT IN (43, -1)
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="findAllByParam" parameterType="map" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            society_name LIKE CONCAT('%', #{searchQuery}, '%')
            OR society_no LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="statusCode != null and statusCode != ''">
            AND `status_code` = #{statusCode}
        </if>
        <if test="state != null and state != 0">
            AND YEAR(`state_id`) = #{state}
        </if>
        ORDER BY registered_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countFindAllByParam" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            society_name LIKE CONCAT('%', #{searchQuery}, '%')
            OR society_no LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="statusCode != null and statusCode != ''">
            AND `status_code` = #{statusCode}
        </if>
        <if test="state != null and state != 0">
            AND YEAR(`state_id`) = #{state}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="findSocietyIdListByIdentificationNo" resultType="java.lang.Long">
        SELECT id
        FROM
        <include refid="tb"/>
        WHERE identification_no = #{identificationNo}
    </select>

    <select id="findSocietyIdListByIdentificationNoWithSecretaryCheck" resultType="java.lang.Long">
        SELECT s.id
        FROM society s
        WHERE s.identification_no = #{identificationNo}
        <if test="checkSecretaryOwnershipRule">
            AND (
                -- Case 1: Society has no secretary (no active secretary positions)
                NOT EXISTS (
                    SELECT 1
                    FROM society_committee sc
                    WHERE sc.society_id = s.id
                    AND sc.designation_code IN
                    <foreach collection="secretaryPositionCodes" item="code" open="(" separator="," close=")">
                        #{code}
                    </foreach>
                    AND sc.status = '001' -- AKTIF
                    AND sc.application_status_code = '11' -- AKTIF
                )
                OR
                -- Case 2: Society has secretary but user is also a committee member
                EXISTS (
                    SELECT 1
                    FROM society_committee sc2
                    WHERE sc2.society_id = s.id
                    AND sc2.identification_no = #{identificationNo}
                    AND sc2.status = '001' -- AKTIF
                    AND sc2.application_status_code = '11' -- AKTIF
                )
            )
        </if>
    </select>

    <select id="countBySocietyName" resultType="int" parameterType="java.lang.String">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE society_name = #{societyName}
        AND application_status_code != -1
    </select>

    <select id="findBySocietyNo" parameterType="java.lang.String" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_no` = #{societyNo}
    </select>

    <select id="countStatusCode" resultType="int">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE `status_code` = #{statusCode}
    </select>

    <select id="countApplicationStatusCode" resultType="int">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE `application_status_code` = #{applicationStatusCode}
    </select>

    <select id="countApplicationStatusCodeAndPaymentDate" parameterType="map" resultType="int">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE `application_status_code` = #{applicationStatusCode}
        AND YEAR(`payment_date`) = #{year}
    </select>

    <select id="findSocietyByIdAndStatusCode" parameterType="map" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `application_status_code` = #{status}
        AND `id` = #{societyId}
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="getSocietyByNameAndStatusAndApplicationStatusCode" parameterType="map" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            society_name LIKE CONCAT('%', #{searchQuery}, '%')
            OR society_no LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="statusCode != null and statusCode !=''">
            AND status_code = #{statusCode}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countSocietyByNameAndStatusAndApplicationStatusCode" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            society_name LIKE CONCAT('%', #{searchQuery}, '%')
            OR society_no LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="statusCode != null and statusCode !=''">
            AND status_code = #{statusCode}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND application_status_code = #{applicationStatusCode}
        </if>
    </select>

    <select id="findByPaymentId" parameterType="java.lang.Long" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `payment_id` = #{paymentId}
    </select>

    <select id="findByApplicationNo" parameterType="java.lang.String" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `application_no` = #{applicationNo}
    </select>

    <select id="countAllPendingByCriteria" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND application_status_code IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND ro = #{ro}
        </if>
        <if test="isQueried == 0"> <!-- Cater for NULL data -->
            AND (is_queried IS NULL OR is_queried = 0)
        </if>
        <if test="isQueried != null and isQueried != 0">
            AND is_queried = #{isQueried}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND category_code_jppm = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND sub_category_code = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND society_name LIKE CONCAT('%', #{societyName}, '%')
        </if>
    </select>

<!--    Get All Society if -->
<!--    1. the user registers the society-->
<!--    2. the user is society committee-->
<!--    3. the user is soicety non citizen approval for committee-->
    <select id="getUserSocieties" parameterType="map" resultMap="SocietyGetOneResponseMap">
        SELECT DISTINCT
        <include refid="selectCols"/>,
        sc.`designation_code`
        FROM <include refid="selectTb"/>
        LEFT JOIN (
            SELECT *
            FROM <include refid="Committee.selectTb"/>
            WHERE identification_no = #{identificationNo}
            AND application_status_code = #{committeeApplicationStatusCode}
            AND status = #{committeeStatusCode}
        ) sc ON s.`id` = sc.`society_id`
        LEFT JOIN (
            SELECT *
            FROM <include refid="NonCitizenCommittee.selectTb"/>
            WHERE identification_no = #{identificationNo}
        ) sncc ON s.`id` = sncc.`society_id`
        WHERE
        (
            s.`identification_no` = #{identificationNo}
            OR sc.`identification_no` = #{identificationNo}
            OR sncc.`identification_no` = #{identificationNo}
        )
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
            OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND s.`application_status_code` = #{applicationStatusCode}
        </if>
        <if test="statusCode != null and statusCode != ''">
            AND s.`status_code` = #{statusCode}
        </if>
        <if test="registeredYear != null and registeredYear != 0">
            AND YEAR(s.`registered_date`) = #{registeredYear}
        </if>
        <if test="designationCode != null and designationCode != ''">
            AND sc.`designation_code` = #{designationCode}
        </if>
        ORDER BY s.registered_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

<!--    <select id="getUserSocieties" parameterType="map" resultMap="SocietyGetOneResponseMap">-->
<!--        SELECT DISTINCT-->
<!--        <include refid="selectCols"/>,-->
<!--        sc.`designation_code`-->
<!--        FROM <include refid="selectTb"/>-->
<!--        LEFT JOIN <include refid="com.eroses.external.society.mappers.CommitteeDao.selectTb"/> ON s.`id` = sc.`society_id`-->
<!--            AND sc.`identification_no` = #{identificationNo}-->
<!--            AND sc.`application_status_code` = #{committeeApplicationStatusCode}-->
<!--            AND sc.`status` = #{committeeStatusCode}-->
<!--        LEFT JOIN <include refid="NonCitizenCommittee.selectTb"/> ON s.`id` = sncc.`society_id`-->
<!--            AND sncc.`identification_no` = #{identificationNo}-->
<!--        WHERE-->
<!--        (-->
<!--            s.`identification_no` = #{identificationNo}-->
<!--            OR sc.`identification_no` = #{identificationNo}-->
<!--            OR sncc.`identification_no` = #{identificationNo}-->
<!--        )-->
<!--        <if test="searchQuery != null and searchQuery != ''">-->
<!--            AND (-->
<!--            s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')-->
<!--            OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')-->
<!--            )-->
<!--        </if>-->
<!--        <if test="applicationStatusCode != null and applicationStatusCode != 0">-->
<!--            AND s.`application_status_code` = #{applicationStatusCode}-->
<!--        </if>-->
<!--        <if test="statusCode != null and statusCode != ''">-->
<!--            AND s.`status_code` = #{statusCode}-->
<!--        </if>-->
<!--        <if test="registeredYear != null and registeredYear != 0">-->
<!--            AND YEAR(s.`registered_date`) = #{registeredYear}-->
<!--        </if>-->
<!--        <if test="designationCode != null and designationCode != ''">-->
<!--            AND sc.`designation_code` = #{designationCode}-->
<!--        </if>-->
<!--        ORDER BY s.registered_date DESC-->
<!--        <if test="offset != null and limit != null">-->
<!--            LIMIT #{offset}, #{limit}-->
<!--        </if>-->
<!--    </select>-->

    <select id="countGetUserSocieties" parameterType="map" resultType="long">
        SELECT COUNT(*) FROM (
            SELECT DISTINCT s.id
            FROM <include refid="selectTb"/>
            LEFT JOIN (
                SELECT *
                FROM <include refid="Committee.selectTb"/>
                WHERE identification_no = #{identificationNo}
                AND application_status_code = #{committeeApplicationStatusCode}
                AND status = #{committeeStatusCode}
            ) sc ON s.`id` = sc.`society_id`
            LEFT JOIN (
                SELECT *
                FROM <include refid="NonCitizenCommittee.selectTb"/>
                WHERE identification_no = #{identificationNo}
            ) sncc ON s.`id` = sncc.`society_id`
            WHERE
            (
                s.`identification_no` = #{identificationNo}
                OR sc.`identification_no` = #{identificationNo}
                OR sncc.`identification_no` = #{identificationNo}
        )
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
            OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND s.`application_status_code` = #{applicationStatusCode}
        </if>
        <if test="statusCode != null and statusCode != ''">
            AND s.`status_code` = #{statusCode}
        </if>
        <if test="registeredYear != null and registeredYear != 0">
            AND YEAR(s.`registered_date`) = #{registeredYear}
        </if>
        <if test="designationCode != null and designationCode != ''">
            AND sc.`designation_code` = #{designationCode}
        </if>
        ) AS count_query
    </select>

<!--    <select id="countGetUserSocieties" parameterType="map" resultType="long">-->
<!--        SELECT COUNT(*) FROM (-->
<!--            SELECT DISTINCT s.id-->
<!--            FROM <include refid="selectTb"/>-->
<!--            LEFT JOIN <include refid="com.eroses.external.society.mappers.CommitteeDao.selectTb"/> ON s.`id` = sc.`society_id`-->
<!--                AND sc.`identification_no` = #{identificationNo}-->
<!--                AND sc.`application_status_code` = #{committeeApplicationStatusCode}-->
<!--                AND sc.`status` = #{committeeStatusCode}-->
<!--            LEFT JOIN <include refid="NonCitizenCommittee.selectTb"/> ON s.`id` = sncc.`society_id`-->
<!--                AND sncc.`identification_no` = #{identificationNo}-->
<!--            WHERE-->
<!--            (-->
<!--                s.`identification_no` = #{identificationNo}-->
<!--                OR sc.`identification_no` = #{identificationNo}-->
<!--                OR sncc.`identification_no` = #{identificationNo}-->
<!--            )-->
<!--            <if test="searchQuery != null and searchQuery != ''">-->
<!--                AND (-->
<!--                    s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')-->
<!--                    OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')-->
<!--                )-->
<!--            </if>-->
<!--            <if test="applicationStatusCode != null and applicationStatusCode != 0">-->
<!--                AND s.`application_status_code` = #{applicationStatusCode}-->
<!--            </if>-->
<!--            <if test="statusCode != null and statusCode != ''">-->
<!--                AND s.`status_code` = #{statusCode}-->
<!--            </if>-->
<!--            <if test="registeredYear != null and registeredYear != 0">-->
<!--                AND YEAR(s.`registered_date`) = #{registeredYear}-->
<!--            </if>-->
<!--            <if test="designationCode != null and designationCode != ''">-->
<!--                AND sc.`designation_code` = #{designationCode}-->
<!--            </if>-->
<!--        ) AS count_query-->
<!--    </select>-->

    <select id="findByCriteria" parameterType="map" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
    </select>

    <update id="resubmitSocietyApplication" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            application_status_code = #{applicationStatusCode}
        </set>
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="findSocietyAmendmentAndNonCitizenForAppeal" parameterType="map" resultMap="Appeal.AppealByUserMap">
        SELECT <include refid="Appeal.societyAppealCols"/>
        FROM <include refid="selectTb"/>
        LEFT JOIN <include refid="Amendment.selectTb"/> ON s.`id` = a.`society_id`
        LEFT JOIN <include refid="NonCitizenCommittee.selectTb"/> ON s.`id` = sncc.`society_id`
        WHERE
        (
        (s.`application_status_code` IN (4, 20))
        OR (a.`application_status_code` IN (4))
        OR (sncc.`application_status_code` IN (4))
        )
        <if test="societyIdList != null and societyIdList.size() > 0">
            AND s.`id` IN
            <foreach item="id" collection="societyIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="findSocietyForAppeal" parameterType="map" resultMap="Appeal.AppealByUserMap">
        SELECT
        s.`id` AS s_id,
        s.`society_name` AS s_society_name,
        s.`society_no` AS s_society_no,
        s.`application_status_code` AS s_application_status_code,
        s.`appeal_status` AS s_appeal_status
        FROM <include refid="selectTb"/>
        WHERE 1=1
        AND s.`application_status_code` IN (4, 20)
        AND (s.`appeal_status` = 0 OR s.`appeal_status` IS NULL)
        <if test="societyIdList != null and societyIdList.size() > 0">
            AND s.`id` IN
            <foreach item="id" collection="societyIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
            OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
    </select>

    <select id="getAllExpiredSocietyApplicationId" parameterType="map" resultType="java.lang.Long">
        SELECT id
        FROM
        <include refid="tb"/>
        WHERE
        application_status_code = #{applicationStatusCode}
        AND status_code = #{statusCode}
        AND NOW() > DATE_ADD(registered_date, INTERVAL #{registrationPeriodDays} DAY)
    </select>

    <update id="updateExpiredSociety" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            application_status_code = #{applicationStatusCode},
            status_code = #{statusCode}
        </set>
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getAllSoonToExpireSocietyApplication" parameterType="map" resultMap="SocietyMap">
        SELECT <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        application_status_code = #{applicationStatusCode}
        AND status_code = #{statusCode}
        AND DATE(DATE_ADD(registered_date, INTERVAL #{registrationPeriodDays} DAY)) = DATE(NOW() + INTERVAL #{remainingDaysToExpire} DAY)
    </select>

    <select id="getSocietiesByCriteria" parameterType="map" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyName != null and societyName != ''">
            AND society_name LIKE CONCAT('%', #{societyName}, '%')
        </if>
        <if test="societyName != null and societyName != ''">
            AND society_no LIKE CONCAT('%', #{societyNo}, '%')
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="statusCode != null and statusCode != ''">
            AND `status_code` = #{statusCode}
        </if>
        <if test="stateCode != null and stateCode != 0">
            AND `state_id` = #{stateCode}
        </if>
        ORDER BY registered_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countGetSocietiesByCriteria" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyName != null and societyName != ''">
            AND society_name LIKE CONCAT('%', #{societyName}, '%')
        </if>
        <if test="societyName != null and societyName != ''">
            AND society_no LIKE CONCAT('%', #{societyNo}, '%')
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="statusCode != null and statusCode != ''">
            AND `status_code` = #{statusCode}
        </if>
        <if test="stateCode != null and stateCode != 0">
            AND `state_id` = #{stateCode}
        </if>
    </select>

    <select id="getSocietiesByCriteriaAndExcludingActiveSocietyCancellationSocieties" parameterType="map" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` NOT IN (
            SELECT DISTINCT `society_id`
            FROM
            <include refid="SocietyCancellation.tb"/>
            WHERE `is_reverted` = 0
            AND `branch_id` IS NULL
        )
        <if test="societyName != null and societyName != ''">
            AND `society_name` LIKE CONCAT('%', #{societyName}, '%')
        </if>
        <if test="societyNo != null and societyNo != ''">
            AND `society_no` LIKE CONCAT('%', #{societyNo}, '%')
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="statusCode != null and statusCode != ''">
            AND `status_code` = #{statusCode}
        </if>
        <if test="stateCodes != null and stateCodes.size() > 0">
            AND `state_id` IN
            <foreach collection="stateCodes" item="stateCode" open="(" close=")" separator=",">
                #{stateCode}
            </foreach>
        </if>
        <if test="societyCategoryCode != null and societyCategoryCode != 0">
            AND `category_code_jppm` = #{societyCategoryCode}
        </if>
        ORDER BY registered_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countSocietiesByCriteriaAndExcludingActiveSocietyCancellationSocieties" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE `id` NOT IN (
            SELECT DISTINCT `society_id`
            FROM
            <include refid="SocietyCancellation.tb"/>
            WHERE `is_reverted` = 0
            AND `branch_id` IS NULL
        )
        <if test="societyName != null and societyName != ''">
            AND `society_name` LIKE CONCAT('%', #{societyName}, '%')
        </if>
        <if test="societyNo != null and societyNo != ''">
            AND `society_no` LIKE CONCAT('%', #{societyNo}, '%')
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="statusCode != null and statusCode != ''">
            AND `status_code` = #{statusCode}
        </if>
        <if test="stateCodes != null and stateCodes.size() > 0">
            AND `state_id` IN
            <foreach collection="stateCodes" item="stateCode" open="(" close=")" separator=",">
                #{stateCode}
            </foreach>
        </if>
        <if test="societyCategoryCode != null and societyCategoryCode != 0">
            AND `category_code_jppm` = #{societyCategoryCode}
        </if>
    </select>

    <select id="findByCategoryCodeJppm" parameterType="java.lang.String" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `category_code_jppm` = #{categoryCodeJppm}
    </select>

    <select id="findByCategoriesCodeJppm" parameterType="java.lang.String" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1 AND
        `category_code_jppm` IN
        <foreach collection="list" item="categoriesCodeJppm" open="(" close=")" separator=",">
            #{categoriesCodeJppm}
        </foreach>
    </select>

    <select id="findBySocietyLevel" parameterType="java.lang.String" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_level` = #{societyLevel}
    </select>

    <select id="findByCategoryAndLevel" parameterType="map" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `category_code_jppm` = #{categoryCodeJppm}
        AND `society_level` = #{societyLevel}
    </select>

    <select id="findByCities" parameterType="list" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `city` IN
        <foreach item="city" collection="list" open="(" separator="," close=")">
            #{city}
        </foreach>
    </select>

    <select id="findByDistrictCodes" parameterType="list" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `district_id` IN
        <foreach item="districtCode" collection="list" open="(" separator="," close=")">
            #{districtCode}
        </foreach>
    </select>

    <select id="findByStateCodes" parameterType="list" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `state_id` IN
        <foreach item="stateCode" collection="list" open="(" separator="," close=")">
            #{stateCode}
        </foreach>
    </select>

    <select id="getAllSocietyPendingApproval" parameterType="map" resultMap="SocietyMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE application_status_code = #{applicationStatusCode}
        AND status_code = #{statusCode}
        AND submission_date IS NOT NULL
        AND submission_date = DATE_SUB(CURDATE(), INTERVAL #{daysAfterSubmission} DAY)
    </select>

    <select id="getAllPendingExternalAgencyReviewByCriteria" parameterType="map" resultMap="SocietyMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND application_status_code IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND ro = #{ro}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND category_code_jppm = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND sub_category_code = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND society_name LIKE CONCAT('%', #{societyName}, '%')
        </if>
        ORDER BY payment_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllPendingExternalAgencyReviewByCriteria" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND application_status_code IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND ro = #{ro}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND category_code_jppm = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND sub_category_code = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND society_name LIKE CONCAT('%', #{societyName}, '%')
        </if>
    </select>
</mapper>
