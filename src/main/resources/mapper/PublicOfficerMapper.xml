<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="PublicOfficer">
    <resultMap id="PublicOfficerResultMap" type="com.eroses.external.society.model.PublicOfficer">
        <id property="id" column="id"/>
        <result property="societyNo" column="society_no"/>
        <result property="societyId" column="society_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="branchId" column="branch_id"/>
        <result property="titleCode" column="title_code"/>
        <result property="name" column="name"/>
        <result property="gender" column="gender"/>
        <result property="citizenshipStatus" column="citizenship_status"/>
        <result property="identificationType" column="identification_type"/>
        <result property="identificationNo" column="identification_no"/>
        <result property="dateOfBirth" column="date_of_birth"/>
        <result property="placeOfBirth" column="place_of_birth"/>
        <result property="occupationCode" column="occupation_code"/>
        <result property="addressStatus" column="address_status"/>
        <result property="address" column="address"/>
        <result property="countryCode" column="country_code"/>
        <result property="stateCode" column="state_id"/>
        <result property="districtCode" column="district_id"/>
        <result property="subDistrictCode" column="sub_district_code"/>
        <result property="city" column="city"/>
        <result property="postalCode" column="postal_code"/>
        <result property="email" column="email"/>
        <result property="homePhoneNumber" column="home_phone_number"/>
        <result property="mobilePhoneNumber" column="mobile_phone_number"/>
        <result property="officePhoneNumber" column="office_phone_number"/>
        <result property="agreed" column="agreed"/>
        <result property="acknowledgment" column="acknowledgment"/>
        <result property="acknowledgmentDate" column="acknowledgment_date"/>
        <result property="submissionDate" column="submission_date"/>
        <result property="paymentId" column="payment_id"/>
        <result property="paymentMethod" column="payment_method"/>
        <result property="paymentDate" column="payment_date"/>
        <result property="receiptNumber" column="receipt_number"/>
        <result property="ePaymentId" column="epayment_id"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankReferenceNumber" column="bank_reference_number"/>
        <result property="receiptStatus" column="receipt_status"/>
        <result property="ro" column="ro"/>
        <result property="appointmentDate" column="appointment_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
        <result property="applicationStatusCode" column="application_status_code"/>
        <result property="status" column="status"/>
        <result property="branchOfficer" column="branch_officer"/>
        <result property="flowDate" column="flow_date"/>
        <result property="approver" column="approver"/>
        <result property="reconcileDate" column="reconcile_date"/>
        <result property="roNote" column="ro_note"/>
    </resultMap>
    <resultMap id="SelectSocietyPublicOfficerResultMap" type="com.eroses.external.society.model.PublicOfficer">
        <id property="id" column="po_id"/>
        <result property="societyNo" column="po_society_no"/>
        <result property="societyId" column="po_society_id"/>
        <result property="branchNo" column="po_branch_no"/>
        <result property="branchId" column="po_branch_id"/>
        <result property="titleCode" column="po_title_code"/>
        <result property="name" column="po_name"/>
        <result property="gender" column="po_gender"/>
        <result property="citizenshipStatus" column="po_citizenship_status"/>
        <result property="identificationType" column="po_identification_type"/>
        <result property="identificationNo" column="po_identification_no"/>
        <result property="dateOfBirth" column="po_date_of_birth"/>
        <result property="placeOfBirth" column="po_place_of_birth"/>
        <result property="occupationCode" column="po_occupation_code"/>
        <result property="addressStatus" column="po_address_status"/>
        <result property="address" column="po_address"/>
        <result property="countryCode" column="po_country_code"/>
        <result property="stateCode" column="po_state_id"/>
        <result property="districtCode" column="po_district_id"/>
        <result property="subDistrictCode" column="po_sub_district_code"/>
        <result property="city" column="po_city"/>
        <result property="postalCode" column="po_postal_code"/>
        <result property="email" column="po_email"/>
        <result property="homePhoneNumber" column="po_home_phone_number"/>
        <result property="mobilePhoneNumber" column="po_mobile_phone_number"/>
        <result property="officePhoneNumber" column="po_office_phone_number"/>
        <result property="agreed" column="po_agreed"/>
        <result property="acknowledgment" column="po_acknowledgment"/>
        <result property="acknowledgmentDate" column="po_acknowledgment_date"/>
        <result property="submissionDate" column="po_submission_date"/>
        <result property="paymentId" column="po_payment_id"/>
        <result property="paymentMethod" column="po_payment_method"/>
        <result property="paymentDate" column="po_payment_date"/>
        <result property="receiptNumber" column="po_receipt_number"/>
        <result property="epaymentId" column="po_epayment_id"/>
        <result property="bankName" column="po_bank_name"/>
        <result property="bankReferenceNumber" column="po_bank_reference_number"/>
        <result property="receiptStatus" column="po_receipt_status"/>
        <result property="ro" column="po_ro"/>
        <result property="appointmentDate" column="po_appointment_date"/>
        <result property="createdBy" column="po_created_by"/>
        <result property="createdDate" column="po_created_date"/>
        <result property="modifiedBy" column="po_modified_by"/>
        <result property="modifiedDate" column="po_modified_date"/>
        <result property="applicationStatusCode" column="po_application_status_code"/>
        <result property="status" column="po_status"/>
        <result property="branchOfficer" column="po_branch_officer"/>
        <result property="flowDate" column="po_flow_date"/>
        <result property="approver" column="po_approver"/>
        <result property="reconcileDate" column="po_reconcile_date"/>
        <result property="roNote" column="po_ro_note"/>
        <association property="society" javaType="com.eroses.external.society.model.Society"
                     resultMap="Society.SocietySelectMap"/>
    </resultMap>
    <resultMap id="SelectBranchPublicOfficerResultMap" type="com.eroses.external.society.model.PublicOfficer">
        <id property="id" column="po_id"/>
        <result property="societyNo" column="po_society_no"/>
        <result property="societyId" column="po_society_id"/>
        <result property="branchNo" column="po_branch_no"/>
        <result property="branchId" column="po_branch_id"/>
        <result property="titleCode" column="po_title_code"/>
        <result property="name" column="po_name"/>
        <result property="gender" column="po_gender"/>
        <result property="citizenshipStatus" column="po_citizenship_status"/>
        <result property="identificationType" column="po_identification_type"/>
        <result property="identificationNo" column="po_identification_no"/>
        <result property="dateOfBirth" column="po_date_of_birth"/>
        <result property="placeOfBirth" column="po_place_of_birth"/>
        <result property="occupationCode" column="po_occupation_code"/>
        <result property="addressStatus" column="po_address_status"/>
        <result property="address" column="po_address"/>
        <result property="countryCode" column="po_country_code"/>
        <result property="stateCode" column="po_state_id"/>
        <result property="districtCode" column="po_district_id"/>
        <result property="subDistrictCode" column="po_sub_district_code"/>
        <result property="city" column="po_city"/>
        <result property="postalCode" column="po_postal_code"/>
        <result property="email" column="po_email"/>
        <result property="homePhoneNumber" column="po_home_phone_number"/>
        <result property="mobilePhoneNumber" column="po_mobile_phone_number"/>
        <result property="officePhoneNumber" column="po_office_phone_number"/>
        <result property="agreed" column="po_agreed"/>
        <result property="acknowledgment" column="po_acknowledgment"/>
        <result property="acknowledgmentDate" column="po_acknowledgment_date"/>
        <result property="submissionDate" column="po_submission_date"/>
        <result property="paymentId" column="po_payment_id"/>
        <result property="paymentMethod" column="po_payment_method"/>
        <result property="paymentDate" column="po_payment_date"/>
        <result property="receiptNumber" column="po_receipt_number"/>
        <result property="epaymentId" column="po_epayment_id"/>
        <result property="bankName" column="po_bank_name"/>
        <result property="bankReferenceNumber" column="po_bank_reference_number"/>
        <result property="receiptStatus" column="po_receipt_status"/>
        <result property="ro" column="po_ro"/>
        <result property="appointmentDate" column="po_appointment_date"/>
        <result property="createdBy" column="po_created_by"/>
        <result property="createdDate" column="po_created_date"/>
        <result property="modifiedBy" column="po_modified_by"/>
        <result property="modifiedDate" column="po_modified_date"/>
        <result property="applicationStatusCode" column="po_application_status_code"/>
        <result property="status" column="po_status"/>
        <result property="branchOfficer" column="po_branch_officer"/>
        <result property="flowDate" column="po_flow_date"/>
        <result property="approver" column="po_approver"/>
        <result property="reconcileDate" column="po_reconcile_date"/>
        <result property="roNote" column="po_ro_note"/>

        <association property="society" javaType="com.eroses.external.society.model.Society"
                     resultMap="Society.SocietySelectMap"/>
        <association property="branch" javaType="com.eroses.external.society.model.Branch"
                     resultMap="Branch.BranchSelectMap"/>
    </resultMap>

    <sql id="tbl">`public_officer`</sql>
    <sql id="selectTb">`public_officer` `po`</sql>
    <sql id="cols">`society_no`, `society_id`, `branch_no`, `branch_id`,
        `title_code`, `name`, `gender`, `citizenship_status`,
        `identification_type`, `identification_no`, `date_of_birth`, `place_of_birth`,
        `occupation_code`, `address_status`, `address`, `country_code`,
        `state_id`, `district_id`, `sub_district_code`, `city`,
        `postal_code`, `email`, `home_phone_number`, `mobile_phone_number`,
        `office_phone_number`, `agreed`, `acknowledgment`, `acknowledgment_date`, `submission_date`,
        `payment_id`, `payment_method`, `payment_date`, `receipt_number`,
        `epayment_id`, `bank_name`, `bank_reference_number`, `receipt_status`,
        `ro`, `appointment_date`, `created_by`, `created_date`,
        `modified_by`, `modified_date`, `application_status_code`, `status`,
        `branch_officer`, `flow_date`, `approver`, `reconcile_date`, `ro_note`
    </sql>
    <sql id="selectCols">`po`.`id` AS `po_id`,`po`.`society_no` AS `po_society_no`,
        `po`.`society_id` AS `po_society_id`,
        `po`.`branch_no` AS `po_branch_no`,
        `po`.`branch_id` AS `po_branch_id`,
        `po`.`title_code` AS `po_title_code`,
        `po`.`name` AS `po_name`,
        `po`.`gender` AS `po_gender`,
        `po`.`citizenship_status` AS `po_citizenship_status`,
        `po`.`identification_type` AS `po_identification_type`,
        `po`.`identification_no` AS `po_identification_no`,
        `po`.`date_of_birth` AS `po_date_of_birth`,
        `po`.`place_of_birth` AS `po_place_of_birth`,
        `po`.`occupation_code` AS `po_occupation_code`,
        `po`.`address_status` AS `po_address_status`,
        `po`.`address` AS `po_address`,
        `po`.`country_code` AS `po_country_code`,
        `po`.`state_id` AS `po_state_id`,
        `po`.`district_id` AS `po_district_id`,
        `po`.`sub_district_code` AS `po_sub_district_code`,
        `po`.`city` AS `po_city`,
        `po`.`postal_code` AS `po_postal_code`,
        `po`.`email` AS `po_email`,
        `po`.`home_phone_number` AS `po_home_phone_number`,
        `po`.`mobile_phone_number` AS `po_mobile_phone_number`,
        `po`.`office_phone_number` AS `po_office_phone_number`,
        `po`.`agreed` AS `po_agreed`,
        `po`.`acknowledgment` AS `po_acknowledgment`,
        `po`.`acknowledgment_date` AS `po_acknowledgment_date`,
        `po`.`submission_date` AS `po_submission_date`,
        `po`.`payment_id` AS `po_payment_id`,
        `po`.`payment_method` AS `po_payment_method`,
        `po`.`payment_date` AS `po_payment_date`,
        `po`.`receipt_number` AS `po_receipt_number`,
        `po`.`epayment_id` AS `po_epayment_id`,
        `po`.`bank_name` AS `po_bank_name`,
        `po`.`bank_reference_number` AS `po_bank_reference_number`,
        `po`.`receipt_status` AS `po_receipt_status`,
        `po`.`ro` AS `po_ro`,
        `po`.`appointment_date` AS `po_appointment_date`,
        `po`.`created_by` AS `po_created_by`,
        `po`.`created_date` AS `po_created_date`,
        `po`.`modified_by` AS `po_modified_by`,
        `po`.`modified_date` AS `po_modified_date`,
        `po`.`application_status_code` AS `po_application_status_code`,
        `po`.`status` AS `po_status`,
        `po`.`branch_officer` AS `po_branch_officer`,
        `po`.`flow_date` AS `po_flow_date`,
        `po`.`approver` AS `po_approver`,
        `po`.`reconcile_date` AS `po_reconcile_date`,
        `po`.`ro_note` AS `po_ro_note`
    </sql>

    <sql id="cols_without_audit_fields">
        `society_no`, `society_id`, `branch_no`, `branch_id`,
        `title_code`, `name`, `gender`, `citizenship_status`,
        `identification_type`, `identification_no`, `date_of_birth`, `place_of_birth`,
        `occupation_code`, `address_status`, `address`, `country_code`,
        `state_id`, `district_id`, `sub_district_code`, `city`,
        `postal_code`, `email`, `home_phone_number`, `mobile_phone_number`,
        `office_phone_number`, `agreed`, `acknowledgment`, `acknowledgment_date`, `submission_date`,
        `payment_id`, `payment_method`, `payment_date`, `receipt_number`,
        `epayment_id`, `bank_name`, `bank_reference_number`, `receipt_status`,
        `ro`, `appointment_date`, `created_by`,
        `modified_by`,  `application_status_code`, `status`,
        `branch_officer`, `flow_date`, `approver`, `reconcile_date`, `ro_note`
    </sql>
    <sql id="cols_with_id">
        `id`, <include refid="cols"/>
    </sql>
    <select id="getAll" resultMap="PublicOfficerResultMap" parameterType="map">
        SELECT
        <include refid="cols_with_id"/>
        FROM <include refid="tbl"/>
        WHERE 1=1
        AND `society_id` = #{societyId}
        <choose>
            <when test="branchId == null">
                AND `branch_id` IS NULL
            </when>
            <otherwise>
                AND `branch_id` = #{branchId}
            </otherwise>
        </choose>
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND `application_status_code` != #{applicationStatusCode}
        </if>
        LIMIT #{limit} OFFSET #{offset}
    </select>
    <select id="countAll" parameterType="map" resultType="long">
        SELECT count(*)
        FROM
        <include refid="tbl"/>
        WHERE 1=1
        AND `society_id` = #{societyId}
        <choose>
            <when test="branchId == null">
                AND `branch_id` IS NULL
            </when>
            <otherwise>
                AND `branch_id` = #{branchId}
            </otherwise>
        </choose>
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND `application_status_code` != #{applicationStatusCode}
        </if>
    </select>
    <select id="countAllPending" parameterType="map" resultType="long">
        SELECT count(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN
        <include refid="Society.selectTb"/>
        ON `po`.`society_id` = s.`id`
        WHERE 1=1
        AND `po`.`branch_id` IS NULL
        AND `po`.`application_status_code` = #{applicationStatusCode}
        <if test="stateCode != null and stateCode != ''">
            AND s.`state_id` = #{stateCode}
        </if>
        <if test="stateCodeFilter != null and stateCodeFilter != ''">
            AND b.`state_id` = #{stateCodeFilter}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            po.name LIKE CONCAT('%', #{searchQuery}, '%')
            OR b.name LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="ro != null and ro != ''">
            AND `po`.`ro` = #{ro}
        </if>
    </select>

    <select id="listAllSocietyPending" parameterType="map" resultMap="SelectSocietyPublicOfficerResultMap">
        SELECT <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>
        FROM <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/>
        ON `po`.`society_id` = s.`id`
        WHERE 1=1
        AND `po`.`branch_id` IS NULL
        AND `po`.`application_status_code` = #{applicationStatusCode}
        <if test="stateCode != null and stateCode != ''">
            AND s.`state_id` = #{stateCode}
        </if>
        <if test="ro != null and ro != ''">
            AND `po`.`ro` = #{ro}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND s.category_code_jppm = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND s.sub_category_code = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND s.society_name LIKE CONCAT('%', #{societyName}, '%')
        </if>
        ORDER BY po.payment_date DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="countAllSocietyPendingWithCriteria" parameterType="map" resultType="java.lang.Long">
        SELECT count(*)
        FROM <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/>
        ON `po`.`society_id` = s.`id`
        WHERE 1=1
        AND `po`.`branch_id` IS NULL
        AND `po`.`application_status_code` = #{applicationStatusCode}
        <if test="stateCode != null and stateCode != ''">
            AND s.`state_id` = #{stateCode}
        </if>
        <if test="ro != null and ro != ''">
            AND `po`.`ro` = #{ro}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND s.category_code_jppm = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND s.sub_category_code = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND s.society_name LIKE CONCAT('%', #{societyName}, '%')
        </if>
    </select>

    <select id="listAllBranchPending" parameterType="map" resultMap="SelectBranchPublicOfficerResultMap">
        SELECT <include refid="selectCols"/>,
        <include refid="Branch.selectCols"/>,
        <include refid="Society.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Branch.selectTb"/> ON `po`.`branch_id` = b.`id`
        INNER JOIN <include refid="Society.selectTb"/> ON `po`.`society_id` = s.`id`
        WHERE 1=1
        AND `po`.`application_status_code` = #{applicationStatusCode}
        <if test="stateCode != null and stateCode != ''">
            AND b.`state_id` = #{stateCode}
        </if>
        <if test="stateCodeFilter != null and stateCodeFilter != ''">
            AND b.`state_id` = #{stateCodeFilter}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            po.name LIKE CONCAT('%', #{searchQuery}, '%')
            OR b.name LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="ro != null and ro != ''">
            AND `po`.`ro` = #{ro}
        </if>
        ORDER BY po.payment_date DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="countAllBranchPendingWithCriteria" parameterType="map" resultType="long">
        SELECT count(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN
        <include refid="Branch.selectTb"/>
        ON `po`.`branch_id` = b.`id`
        WHERE 1=1
        AND `po`.`application_status_code` = #{applicationStatusCode}
        <if test="stateCode != null and stateCode != ''">
            AND b.`state_id` = #{stateCode}
        </if>
        <if test="ro != null and ro != ''">
            AND `po`.`ro` = #{ro}
        </if>
    </select>

    <select id="countAllBranchPending" parameterType="map" resultType="long">
        SELECT count(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN
        <include refid="Branch.selectTb"/>
        ON `po`.`branch_id` = b.`id`
        WHERE 1=1
        AND `po`.`application_status_code` = #{applicationStatusCode}
        <if test="stateCode != null and stateCode != ''">
            AND b.`state_id` = #{stateCode}
        </if>
        <if test="ro != null and ro != ''">
            AND `po`.`ro` = #{ro}
        </if>

    </select>

    <select id="findByPaymentId" resultMap="PublicOfficerResultMap" parameterType="long">
        SELECT  <include refid="cols_with_id"/>
        FROM <include refid="tbl"/>
        WHERE `payment_id` = #{paymentId}
    </select>
    <select id="findById" resultMap="PublicOfficerResultMap" parameterType="long">
        SELECT  <include refid="cols_with_id"/>
        FROM <include refid="tbl"/>
        WHERE `id` = #{id}
    </select>
    <update id="update" parameterType="com.eroses.external.society.model.PublicOfficer">
        UPDATE <include refid="tbl"/>
        <set>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="titleCode != null">`title_code` = #{titleCode},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="gender != null">`gender` = #{gender},</if>
            <if test="citizenshipStatus != null">`citizenship_status` = #{citizenshipStatus},</if>
            <if test="identificationType != null">`identification_type` = #{identificationType},</if>
            <if test="identificationNo != null">`identification_no` = #{identificationNo},</if>
            <if test="dateOfBirth != null">`date_of_birth` = #{dateOfBirth},</if>
            <if test="placeOfBirth != null">`place_of_birth` = #{placeOfBirth},</if>
            <if test="occupationCode != null">`occupation_code` = #{occupationCode},</if>
            <if test="addressStatus != null">`address_status` = #{addressStatus},</if>
            <if test="address != null">`address` = #{address},</if>
            <if test="countryCode != null">`country_code` = #{countryCode},</if>
            <if test="stateCode != null">`state_id` = #{stateCode},</if>
            <if test="districtCode != null">`district_id` = #{districtCode},</if>
            <if test="subDistrictCode != null">`sub_district_code` = #{subDistrictCode},</if>
            <if test="city != null">`city` = #{city},</if>
            <if test="postalCode != null">`postal_code` = #{postalCode},</if>
            <if test="email != null">`email` = #{email},</if>
            <if test="homePhoneNumber != null">`home_phone_number` = #{homePhoneNumber},</if>
            <if test="mobilePhoneNumber != null">`mobile_phone_number` = #{mobilePhoneNumber},</if>
            <if test="officePhoneNumber != null">`office_phone_number` = #{officePhoneNumber},</if>
            <if test="agreed != null">`agreed` = #{agreed},</if>
            <if test="acknowledgment != null">`acknowledgment` = #{acknowledgment},</if>
            <if test="acknowledgmentDate != null">`acknowledgment_date` = #{acknowledgmentDate},</if>
            <if test="submissionDate != null">`submission_date` = #{submissionDate},</if>
            <if test="paymentId != null">`payment_id` = #{paymentId},</if>
            <if test="paymentMethod != null">`payment_method` = #{paymentMethod},</if>
            <if test="paymentDate != null">`payment_date` = #{paymentDate},</if>
            <if test="receiptNumber != null">`receipt_number` = #{receiptNumber},</if>
            <if test="ePaymentId != null">`epayment_id` = #{ePaymentId},</if>
            <if test="bankName != null">`bank_name` = #{bankName},</if>
            <if test="bankReferenceNumber != null">`bank_reference_number` = #{bankReferenceNumber},</if>
            <if test="receiptStatus != null">`receipt_status` = #{receiptStatus},</if>
            <if test="ro != null">`ro` = #{ro},</if>
            <if test="appointmentDate != null">`appointment_date` = #{appointmentDate},</if>
            <if test="createdBy != null">`created_by` = #{createdBy},</if>
            <if test="createdDate != null">`created_date` = #{createdDate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="modifiedDate != null">`modified_date` = #{modifiedDate},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="branchOfficer != null">`branch_officer` = #{branchOfficer},</if>
            <if test="flowDate != null">`flow_date` = #{flowDate},</if>
            <if test="approver != null">`approver` = #{approver},</if>
            <if test="reconcileDate != null">`reconcile_date` = #{reconcileDate},</if>
            <if test="roNote != null">`ro_note` = #{roNote}</if>
        </set>
        WHERE `id` = #{id}
    </update>
    <insert id="create" parameterType="com.eroses.external.society.model.PublicOfficer" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tbl"/> (
        <include refid="cols_without_audit_fields"/>
        ) VALUES (
        #{societyNo},
        #{societyId},
        #{branchNo},
        #{branchId},
        #{titleCode},
        #{name},
        #{gender},
        #{citizenshipStatus},
        #{identificationType},
        #{identificationNo},
        #{dateOfBirth},
        #{placeOfBirth},
        #{occupationCode},
        #{addressStatus},
        #{address},
        #{countryCode},
        #{stateCode},
        #{districtCode},
        #{subDistrictCode},
        #{city},
        #{postalCode},
        #{email},
        #{homePhoneNumber},
        #{mobilePhoneNumber},
        #{officePhoneNumber},
        #{agreed},
        #{acknowledgment},
        #{acknowledgmentDate},
        #{submissionDate},
        #{paymentId},
        #{paymentMethod},
        #{paymentDate},
        #{receiptNumber},
        #{ePaymentId},
        #{bankName},
        #{bankReferenceNumber},
        #{receiptStatus},
        #{ro},
        #{appointmentDate},
        #{createdBy},
        #{modifiedBy},
        #{applicationStatusCode},
        #{status},
        #{branchOfficer},
        #{flowDate},
        #{approver},
        #{reconcileDate},
        #{roNote}
        )
    </insert>

    <select id="getAllPublicOfficerPendingApproval" parameterType="map" resultMap="SelectSocietyPublicOfficerResultMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>
        FROM <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.id = po.society_id
        WHERE
        po.`application_status_code` = #{applicationStatusCode}
        AND po.`submission_date` IS NOT NULL
        AND (
        <foreach item="days" collection="daysAfterSubmissionList" separator=" OR ">
            po.`submission_date` = DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        </foreach>
        )
    </select>

    <select id="existsOnGoingSocietyPublicOfficerApplication" parameterType="map" resultType="boolean">
        SELECT EXISTS(
            SELECT 1
            FROM <include refid="tbl"/>
            WHERE 1=1
            AND `branch_id` IS NULL
            <if test="applicationStatusCodes != null and applicationStatusCodes.size() > 0">
                AND `application_status_code` IN
                <foreach item="applicationStatusCode" collection="applicationStatusCodes" open="(" separator="," close=")">
                #{applicationStatusCode}
            </foreach>
            </if>
            <if test ="societyId != null">
                AND `society_id` = #{societyId}
            </if>
        )
    </select>

    <select id="existsOnGoingBranchPublicOfficerApplication" parameterType="map" resultType="boolean">
        SELECT EXISTS(
        SELECT 1
        FROM <include refid="tbl"/>
        WHERE 1=1
        <if test="applicationStatusCodes != null and applicationStatusCodes.size() > 0">
            AND `application_status_code` IN
            <foreach item="applicationStatusCode" collection="applicationStatusCodes" open="(" separator="," close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test ="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        )
    </select>
</mapper>
