<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="RoLiquidationApproval">
    <resultMap id="map" type="com.eroses.external.society.model.societyLiquidation.RoLiquidationApproval">
        <id property="id" column="id" />
        <result column="liquidation_id" property="liquidationId"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="decision" property="decision"/>
        <result column="reject_reason" property="rejectReason"/>
        <result column="note" property="note"/>
        <result column="approved_by" property="approvedBy"/>
        <result column="decision_date" property="decisionDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <resultMap id="LiquidationDetailsMap" type="com.eroses.external.society.dto.response.societyLiquidation.LiquidationInternalResponse">
        <id property="id" column="id" />
        <result column="society_id" property="societyId"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="transfer_date" property="transferDate"/>
        <result column="ro" property="ro"/>
        <result column="note_ro" property="noteRo"/>
        <result column="note_ppp" property="notePpp"/>
        <result column="committee_vote_count" property="committeeVoteCount"/>
        <result column="created_by" property="createdBy"/>
    </resultMap>

    <resultMap id="ApprovalListMap" type="com.eroses.external.society.dto.response.societyLiquidation.LiquidationApprovalResponse">
        <id property="id" column="id" />
        <result column="decision" property="decision"/>
        <result column="decision_date" property="decisionDate"/>
        <result column="note" property="note"/>
        <result column="reject_reason" property="rejectReason"/>
        <result column="approved_by" property="approvedBy"/>
    </resultMap>

    <resultMap id="PagingMap" type="com.eroses.external.society.dto.response.societyLiquidation.LiquidationPagingInternalResponse">
        <id property="liquidationId" column="id" />
        <result column="society_name" property="societyName"/>
        <result column="ro" property="ro"/>
        <result column="branch_id" property="branchId"/>
        <result column="ro_name" property="roName"/>
        <result column="transfer_date" property="transferDate"/>
        <result column="submission_date" property="submissionDate"/>
        <result column="society_no" property="societyNo"/>
        <result column="state_id" property="stateCode"/>
        <result column="application_status_code" property="applicationStatusCode"/>
    </resultMap>


    <sql id="tb">
        ro_liquidation_approval
    </sql>

    <sql id="cols">
        liquidation_id,
        society_id,
        society_no,
        branch_id,
        branch_no,
        decision,
        reject_reason,
        note,
        approved_by,
        decision_date,
        created_by,
        created_date,
        modified_by,
        modified_date
    </sql>

    <sql id="vals">
        #{liquidationId},
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{decision},
        #{rejectReason},
        #{note},
        #{approvedBy},
        #{decisionDate},
        #{createdBy},
        now(),
        #{modifiedBy},
        now()
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.societyLiquidation.RoLiquidationApproval" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
            liquidation_id,
            society_id,
            society_no,
            decision,
            reject_reason,
            note,
            approved_by,
            decision_date,
            created_by,
            created_date
        )
        VALUES
        (
            #{liquidationId},
            #{societyId},
            #{societyNo},
            #{decision},
            #{rejectReason},
            #{note},
            #{approvedBy},
            NOW(),
            #{createdBy},
            NOW()
        )
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.societyLiquidation.RoLiquidationApproval">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date=NOW(),
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="findLiquidationById" parameterType="java.lang.Long" resultMap="LiquidationDetailsMap">
        SELECT
        l.id,
        l.society_id,
        l.meeting_id,
        l.transfer_date,
        l.committee_vote_count,
        l.ro,
        l.note_ro,
        l.note_ppp,
        l.created_by
        FROM liquidation l
        WHERE l.id = #{liquidationId}
    </select>

    <select id="findApprovalById" parameterType="java.lang.Long" resultMap="ApprovalListMap">
        SELECT
        a.id,
        a.decision,
        a.reject_reason,
        a.note,
        a.decision_date,
        a.approved_by
        FROM ro_liquidation_approval a
        WHERE a.liquidation_id = #{liquidationId};
    </select>

    <!-- Approval -->
    <select id="getPaging" parameterType="map" resultMap="PagingMap">
        SELECT
        l.id,
        s.society_name,
        l.ro,
        l.branch_id,
        <!-- u.name AS ro_name, -->
        l.transfer_date,
        l.submission_date,
        l.society_no,
        s.state_id,
        l.application_status_code
        FROM liquidation l
        JOIN society s ON l.society_id = s.id
        WHERE
        l.application_status_code IN
        <if test="appStatusCode != null">
            <foreach collection="appStatusCode" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="stateCode != null">
            AND s.state_id = #{stateCode}
        </if>
        <if test="identificationNo != null">
            AND l.ro = #{identificationNo}
        </if>
        <if test="categoryCode != null">
            AND s.category_code_jppm = #{categoryCode}
        </if>
        <if test="subCategoryCode != null">
            AND s.sub_category_code = #{subCategoryCode}
        </if>
        <if test="societyName != null">
            <bind name="wildcardName" value="'%' + societyName + '%'" />
            AND s.society_name LIKE #{wildcardName}
        </if>
        <if test="branchLiquidation != null">
            AND l.branch_liquidation = #{branchLiquidation}
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="getTotalCount" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM liquidation l
        JOIN society s ON s.id = l.society_id
        WHERE
        l.application_status_code IN
        <if test="appStatusCode != null">
            <foreach collection="appStatusCode" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="stateCode != null">
            AND s.state_id = #{stateCode}
        </if>
        <if test="identificationNo != null">
            AND l.ro = #{identificationNo}
        </if>
        <if test="categoryCode != null">
            AND s.category_code_jppm = #{categoryCode}
        </if>
        <if test="subCategoryCode != null">
            AND s.sub_category_code = #{subCategoryCode}
        </if>
        <if test="societyName != null">
            <bind name="wildcardName" value="'%' + societyName + '%'" />
            AND s.society_name LIKE #{wildcardName}
        </if>
        <if test="branchLiquidation != null">
            AND l.branch_liquidation = #{branchLiquidation}
        </if>
    </select>
</mapper>
