<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="RoApproval">
    <resultMap id="RoApprovalResultMap" type="com.eroses.external.society.model.RoApproval">
        <!-- ID -->
        <id property="id" column="id"/>

        <!-- Columns -->
        <result property="type" column="type"/>
        <result property="applicationNo" column="application_no"/>
        <result property="societyNo" column="society_no"/>
        <result property="societyId" column="society_id"/>
        <result property="branchId" column="branch_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="amendmentId" column="amendment_id"/>
        <result property="appealId" column="appeal_id"/>
        <result property="societyNonCitizenCommitteeId" column="society_non_citizen_committee_id"/>
        <result property="extensionTimeId" column="extension_time_id"/>
        <result property="branchAmendmentId" column="branch_amendment_id"/>
        <result property="decision" column="decision"/>
        <result property="akta1" column="akta_1"/>
        <result property="akta2" column="akta_2"/>
        <result property="extensionDays" column="extension_days"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="note" column="note"/>
        <result property="approvedBy" column="approved_by"/>
        <result property="decisionDate" column="decision_date"/>
        <result property="principalSecretaryId" column="principal_secretary_id"/>
        <result property="liquidationId" column="liquidation_id"/>
        <result property="propertyOfficerApplicationId" column="property_officer_application_id"/>
        <result property="publicOfficerId" column="public_officer_id"/>

        <!-- Audit Columns -->
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>

    <sql id="tb">
        `ro_approval`
    </sql>

    <sql id="cols">
        `type`, `application_no`, `society_no`, `amendment_id`, `appeal_id`,
        `society_id`, `branch_id`, `branch_no`, `society_non_citizen_committee_id`,
        `extension_time_id`, `branch_amendment_id`, `decision`, `akta_1`, `akta_2`, `extension_days`, `reject_reason`, `note`,
        `approved_by`, `decision_date`, `created_by`, `created_date`,
        `modified_by`, `modified_date`, `principal_secretary_id`, `liquidation_id`, `property_officer_application_id`, `public_officer_id`
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.RoApproval" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{type}, #{applicationNo}, #{societyNo}, #{amendmentId}, #{appealId},
        #{societyId}, #{branchId}, #{branchNo}, #{societyNonCitizenCommitteeId},
        #{extensionTimeId}, #{branchAmendmentId}, #{decision}, #{akta1}, #{akta2}, #{extensionDays},
        #{rejectReason}, #{note}, #{approvedBy},
        #{decisionDate}, #{createdBy}, NOW(), #{modifiedBy}, NOW(), #{principalSecretaryId}, #{liquidationId},
        #{propertyOfficerApplicationId}, #{publicOfficerId})
    </insert>

    <select id="findById" parameterType="java.lang.Long" resultMap="RoApprovalResultMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="findByBranchId" parameterType="java.lang.Long" resultMap="RoApprovalResultMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `branch_id` = #{id}
        LIMIT 1
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.RoApproval">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="type != null">`type` = #{type},</if>
            <if test="applicationNo != null">`application_no` = #{applicationNo},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="societyNonCitizenCommitteeId != null">`society_non_citizen_committee_id` = #{societyNonCitizenCommitteeId},</if>
            <if test="extensionTimeId != null">`extension_time_id` = #{extensionTimeId},</if>
            <if test="branchAmendmentId != null">`branch_amendment_id` = #{branchAmendmentId},</if>
            <if test="decision != null">`decision` = #{decision},</if>
            <if test="akta1 != null">`akta_1` = #{akta1},</if>
            <if test="akta2 != null">`akta_2` = #{akta2},</if>
            <if test="extensionDays != null">`extension_days` = #{extensionDays},</if>
            <if test="rejectReason != null">`reject_reason` = #{rejectReason},</if>
            <if test="note != null">`note` = #{note},</if>
            <if test="approvedBy != null">`approved_by` = #{approvedBy},</if>
            <if test="decisionDate != null">`decision_date` = #{decisionDate},</if>
            <if test="amendmentId != null">`amendment_id` = #{amendmentId},</if>
            <if test="appealId != null">`appeal_id` = #{appealId},</if>
            <if test="principalSecretaryId != null">`principal_secretary_id` = #{principalSecretaryId},</if>
            <if test="liquidationId != null">`liquidation_id` = #{liquidationId},</if>
            <if test="propertyOfficerApplicationId != null">`property_officer_application_id` = #{propertyOfficerApplicationId},</if>
            <if test="publicOfficerId != null">`public_officer_id` = #{publicOfficerId},</if>

            <!-- Audit Columns -->
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            `modified_date` = NOW()
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="findAllByCriteria" parameterType="map" resultMap="RoApprovalResultMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId !=''">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null and branchId !=''">
            AND `branch_id` = #{branchId}
        </if>
        <if test="amendmentId != null and amendmentId !=''">
            AND `amendment_id` = #{amendmentId}
        </if>
        <if test="appealId != null and appealId !=''">
            AND `appeal_id` = #{appealId}
        </if>
        <if test="societyNonCitizenCommitteeId != null and societyNonCitizenCommitteeId !=''">
            AND `society_non_citizen_committee_id` = #{societyNonCitizenCommitteeId}
        </if>
        <if test="extensionTimeId != null and extensionTimeId !=''">
            AND `extension_time_id` = #{extensionTimeId}
        </if>
        <if test="branchAmendmentId != null and branchAmendmentId !=''">
            AND `branch_amendment_id` = #{branchAmendmentId}
        </if>
        <if test="principalSecretaryId != null and principalSecretaryId !=''">
            AND `principal_secretary_id` = #{principalSecretaryId}
        </if>
        <if test="liquidationId != null and liquidationId !=''">
            AND `liquidation_id` = #{liquidationId}
        </if>
        <if test="propertyOfficerApplicationId != null and propertyOfficerApplicationId !=''">
            AND `property_officer_application_id` = #{propertyOfficerApplicationId}
        </if>
        <if test="publicOfficerId != null and publicOfficerId !=''">
            AND `public_officer_id` = #{publicOfficerId}
        </if>
        <if test="type != null and type !=''">
            AND `type` = #{type}
        </if>
        <if test="decision != null and decision !=''">
            AND `decision` = #{decision}
        </if>
        ORDER BY `created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countFindAll" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId !=''">
            AND `society_id` = #{societyId}
        </if>
    </select>

    <!-- Society secretary -->
    <insert id="createSocietySecretaryApproval" parameterType="com.eroses.external.society.model.RoApproval" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/> (
            type,
            society_no,
            society_id,
            decision,
            reject_reason,
            note,
            principal_secretary_id,
            approved_by,
            decision_date,
            created_by,
            created_date
        ) VALUES (
            #{type},
            #{societyNo},
            #{societyId},
            #{decision},
            #{rejectReason},
            #{note},
            #{principalSecretaryId},
            #{approvedBy},
            NOW(),
            #{createdBy},
            NOW()
        )
    </insert>

    <select id="findByParamForAppeal" parameterType="map" resultMap="RoApprovalResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="amendmentId != null">
            AND `amendment_id` = #{amendmentId}
        </if>
        <if test="nonCitizenId != null">
            AND `society_non_citizen_committee_id` = #{nonCitizenId}
        </if>
        <choose>
            <when test="decision != null">
                AND `decision` = #{decision}
            </when>
            <otherwise>
                AND `decision` = 4
            </otherwise>
        </choose>
        ORDER BY `created_date`
    </select>

    <select id="findRoApprovalByModuleId" parameterType="map" resultMap="RoApprovalResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="amendmentId != null">
            AND `amendment_id` = #{amendmentId}
        </if>
        <if test="nonCitizenId != null">
            AND `society_non_citizen_committee_id` = #{nonCitizenId}
        </if>
        <if test="appealId != null">
            AND `appeal_id` = #{appealId}
        </if>
        <if test="extensionTimeId != null">
            AND `extension_time_id` = #{extensionTimeId}
        </if>
        <if test="branchAmendmentId != null">
            AND `branch_amendment_id` = #{branchAmendmentId}
        </if>
        <if test="principalSecretaryId != null">
            AND `principal_secretary_id` = #{principalSecretaryId}
        </if>
        <if test="liquidationId != null">
            AND `liquidation_id` = #{liquidationId}
        </if>
        <if test="type != null">
            AND `type` = #{type}
        </if>
        <if test="decision != null">
            AND `decision` = #{decision}
        </if>
        ORDER BY `created_date` DESC
        LIMIT 1
    </select>
</mapper>
