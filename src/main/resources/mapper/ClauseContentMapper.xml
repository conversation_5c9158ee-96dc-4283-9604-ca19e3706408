<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ClauseContent">

    <resultMap id="ClauseContentMap" type="com.eroses.external.society.model.ClauseContent">
        <id property="id" column="id" />
        <result column="constitution_type_id" property="constitutionTypeId"/>
        <result column="clause_no" property="clauseNo"/>
        <result column="name" property="name"/>
        <result column="content" property="content"/>
        <result column="key_value" property="keyValue"/>
        <result column="status" property="status"/>
    </resultMap>

    <resultMap id="ClauseContentSelectMap" type="com.eroses.external.society.model.ClauseContent">
        <id column="clause_id" property="id"/>
        <result column="clause_constitution_type_id" property="constitutionTypeId"/>
        <result column="clause_clause_no" property="clauseNo"/>
        <result column="clause_name" property="name"/>
        <result column="clause_content" property="content"/>
        <result column="clause_key_value" property="keyValue"/>
        <result column="clause_status" property="status"/>
    </resultMap>

    <sql id="tb">
        clause_content
    </sql>

    <sql id="selectTb">
        `clause_content` clause
    </sql>

    <sql id="cols">
        `id`,
        `constitution_type_id`,
        `clause_no`,
        `name`,
        `content`,
        `key_value`,
        `status`
    </sql>

    <sql id="cols1">
        `id`,
        `constitution_type_id`,
        `clause_no`,
        `name`,
        `content`,
        `key_value`,
        `status`
    </sql>

    <sql id="selectCols">
        clause.`id` AS clause_id,
        clause.`constitution_type_id` AS clause_constitution_type_id,
        clause.`clause_no` AS clause_clause_no,
        clause.`name` AS clause_name,
        clause.`content` AS clause_content,
        clause.`key_value` AS clause_key_value,
        clause.`status` AS clause_status
    </sql>

    <sql id="vals">
        #{constitutionTypeId},
        #{clauseNo},
        #{name},
        #{content},
        #{keyValue},
        #{status}
    </sql>



    <select id="findAll" resultType="list" resultMap="ClauseContentMap">
        SELECT
        <include refid="cols1"/>
        FROM
        <include refid="tb"/>
        WHERE `status` != '-1'
    </select>

    <select id="findByConstitutionTypeId" resultType="list" resultMap="ClauseContentMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `constitution_type_id` = #{constitutionTypeId}
        AND `status` = 1
    </select>

    <select id="getByConsTypeAndClauseNo" parameterType="java.lang.Long" resultMap="ClauseContentMap">
        SELECT
        <include refid="cols1"/>
        FROM
        <include refid="tb"/>
        WHERE `constitution_type_id` = #{constitutionType}
        AND `clause_no` = #{clauseNo}
    </select>

    <select id="countAllByConstitutionTypeId" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE `constitution_type_id` = #{constitutionTypeId}
        AND `status` = 1
    </select>

    <select id="getByConsTypeAndClauseName" parameterType="map" resultMap="ClauseContentMap">
        SELECT
        <include refid="cols1"/>
        FROM
        <include refid="tb"/>
        WHERE `constitution_type_id` = #{constitutionType}
        AND `name` = #{clauseName}
    </select>

</mapper>
