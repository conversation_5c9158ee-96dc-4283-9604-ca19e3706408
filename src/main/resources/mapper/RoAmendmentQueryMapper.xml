<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="RoAmendmentQuery">
    <resultMap id="RoAmendmentQueryMap" type="com.eroses.external.society.model.RoAmendmentQuery">
        <id property="id" column="id" />
        <result column="id_minit" property="idMinit"/>
        <result column="amendment_id" property="amendmentId"/>
        <result column="society_no" property="societyNo"/>
        <result column="notes" property="notes"/>
        <result column="done" property="done"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <sql id="tb">
        `ro_amendment_query`
    </sql>

    <sql id="cols">
        `id_minit`,
        `amendment_id`,
        `society_no`,
        `notes`,
        `done`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="vals">
        #{idMinit},
        #{amendmentId},
        #{societyNo},
        #{notes},
        #{done},
        #{createdBy},
        now(),
        #{modifiedBy},
        now()
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.RoAmendmentQuery" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        <include refid="vals"/>
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.RoAmendmentQuery">
        UPDATE <include refid="tb"/>
        <set>
            <if test="idMinit != null">`id_minit` = #{idMinit},</if>
            <if test="amendmentId != null">`amendment_id` = #{amendmentId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="notes != null">`notes` = #{notes},</if>
            <if test="done != null">`done` = #{done},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            modified_date=NOW(),
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="get" parameterType="map" resultMap="RoAmendmentQueryMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="idMinit != null">
            AND `id_minit` = #{idMinit}
        </if>
        <if test="amendmentId != null">
            AND `amendment_id` = #{amendmentId}
        </if>
        <if test="done != null">
            AND `done` = #{done}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="getById" parameterType="java.lang.Long" resultMap="RoAmendmentQueryMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `id` = #{id}
    </select>

</mapper>