<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="SearchInformationDocumentTemplate">

    <resultMap id="SearchInformationDocumentTemplateResultMap" type="com.eroses.external.society.model.SearchInformationDocumentTemplate">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="type" property="type" />
        <result column="html_content" property="htmlContent" />
        <result column="status" property="status" />
        <result column="format" property="format" />
        <result column="language" property="language" />
        <result column="download_period" property="downloadPeriod" />
        <result column="amount" property="amount" />
        <result column="s3_url" property="s3Url" />
        <result column="is_generated" property="isGenerated" />
        <result column="created_by" property="createdBy" />
        <result column="created_date" property="createdDate" />
        <result column="modified_by" property="modifiedBy" />
        <result column="modified_date" property="modifiedDate" />
    </resultMap>

    <resultMap id="SearchInformationDocumentTemplateSelectResultMap" type="com.eroses.external.society.model.SearchInformationDocumentTemplate">
        <id column="sidt_id" property="id" />
        <result column="sidt_code" property="code" />
        <result column="sidt_name" property="name" />
        <result column="sidt_description" property="description" />
        <result column="sidt_type" property="type" />
        <result column="sidt_html_content" property="htmlContent" />
        <result column="sidt_status" property="status" />
        <result column="sidt_format" property="format" />
        <result column="sidt_language" property="language" />
        <result column="sidt_download_period" property="downloadPeriod" />
        <result column="sidt_amount" property="amount" />
        <result column="sidt_s3_url" property="s3Url" />
        <result column="sidt_is_generated" property="isGenerated" />
        <result column="sidt_created_by" property="createdBy" />
        <result column="sidt_created_date" property="createdDate" />
        <result column="sidt_modified_by" property="modifiedBy" />
        <result column="sidt_modified_date" property="modifiedDate" />
    </resultMap>

    <!-- SQL Fragments -->
    <sql id="tb">
        `search_information_document_template`
    </sql>

    <sql id="selectTb">
        `search_information_document_template` sidt
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols" />
    </sql>

    <sql id="cols">
        `code`,
        `name`,
        `description`,
        `type`,
        `html_content`,
        `status`,
        `format`,
        `language`,
        `download_period`,
        `amount`,
        `s3_url`,
        `is_generated`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`
    </sql>

    <sql id="selectCols">
        sidt.`id` AS sidt_id,
        sidt.`code` AS sidt_code,
        sidt.`name` AS sidt_name,
        sidt.`description` AS sidt_description,
        sidt.`type` AS sidt_type,
        sidt.`html_content` AS sidt_html_content,
        sidt.`status` AS sidt_status,
        sidt.`format` AS sidt_format,
        sidt.`language` AS sidt_language,
        sidt.`download_period` AS sidt_download_period,
        sidt.`amount` AS sidt_amount,
        sidt.`s3_url` AS sidt_s3_url,
        sidt.`is_generated` AS sidt_is_generated,
        sidt.`created_by` AS sidt_created_by,
        sidt.`created_date` AS sidt_created_date,
        sidt.`modified_by` AS sidt_modified_by,
        sidt.`modified_date` AS sidt_modified_date
    </sql>

    <!-- Select all records -->
    <select id="getAll" resultMap="SearchInformationDocumentTemplateResultMap">
        SELECT
        <include refid="colsWithId" />
        FROM
        <include refid="tb" />
    </select>

    <!-- Find by ID -->
    <select id="findById" parameterType="long" resultMap="SearchInformationDocumentTemplateResultMap">
        SELECT
        <include refid="colsWithId" />
        FROM
        <include refid="tb" />
        WHERE `id` = #{id}
    </select>

    <!-- Insert a new record -->
    <insert id="create" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb" />
        (<include refid="cols" />)
        VALUES (
        #{code},
        #{name},
        #{description},
        #{type},
        #{htmlContent},
        #{status},
        #{format},
        #{language},
        #{downloadPeriod},
        #{amount},
        #{s3Url},
        #{isGenerated},
        #{createdBy},
        #{createdDate},
        #{modifiedBy},
        #{modifiedDate}
        )
    </insert>

    <select id="getByCode" parameterType="String" resultMap="SearchInformationDocumentTemplateResultMap">
        SELECT
        <include refid="colsWithId" />
        FROM
        <include refid="tb" />
        WHERE `code` = #{code}
    </select>

    <!-- Update an existing record -->
    <update id="update">
        UPDATE <include refid="tb" />
        <set>
            <if test="code != null">`code` = #{code},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="description != null">`description` = #{description},</if>
            <if test="type != null">`type` = #{type},</if>
            <if test="html_content != null">`htmlContent` = #{htmlContent},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="format != null">`format` = #{format},</if>
            <if test="language != null">`language` = #{language},</if>
            <if test="downloadPeriod != null">`download_period` = #{downloadPeriod},</if>
            <if test="amount != null">`amount` = #{amount},</if>
            <if test="s3Url != null">`s3_url` = #{s3Url},</if>
            <if test="isGenerated != null">`is_generated` = #{isGenerated},</if>
            <if test="createdBy != null">`created_by` = #{createdBy},</if>
            <if test="createdDate != null">`created_date` = #{createdDate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="modifiedDate != null">`modified_date` = #{modifiedDate},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- Delete a record by ID -->
    <delete id="deleteById">
        DELETE FROM <include refid="tb" />
        WHERE `id` = #{id}
    </delete>

</mapper>