<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="StatementContribution">

    <!-- Result Map -->
    <resultMap id="StatementContributionMap" type="com.eroses.external.society.model.StatementContribution">
        <id property="id" column="id"/>
        <result property="statementId" column="statement_id"/>
        <result property="societyId" column="society_id"/>
        <result property="societyNo" column="society_no"/>
        <result property="branchId" column="branch_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="contributionCode" column="contribution_code"/>
        <result property="contribution" column="contribution"/>
        <result property="countryOrigin" column="country_origin"/>
        <result property="value" column="value"/>
        <result property="applicationStatusCode" column="application_status_code"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>

    <!-- Table Name -->
    <sql id="tb">
        `statement_contribution`
    </sql>

    <!-- Columns without ID -->
    <sql id="cols">
        `statement_id`,
        `society_id`,
        `society_no`,
        `branch_id`,
        `branch_no`,
        `contribution_code`,
        `contribution`,
        `country_origin`,
        `value`,
        `application_status_code`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`
    </sql>

    <!-- Columns with ID -->
    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <!-- Insert Statement -->
    <insert id="create" parameterType="com.eroses.external.society.model.StatementContribution" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (
        #{statementId},
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{contributionCode},
        #{contribution},
        #{countryOrigin},
        #{value},
        #{applicationStatusCode},
        #{createdBy},
        NOW(),
        #{modifiedBy},
        NOW()
        )
    </insert>

    <!-- Update Statement -->
    <update id="update" parameterType="com.eroses.external.society.model.StatementContribution">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="statementId != null">`statement_id` = #{statementId},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="contributionCode != null">`contribution_code` = #{contributionCode},</if>
            <if test="contribution != null">`contribution` = #{contribution},</if>
            <if test="countryOrigin != null">`country_origin` = #{countryOrigin},</if>
            <if test="value != null">`value` = #{value},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="modifiedDate != null">`modified_date` = #{modifiedDate},</if>
        </set>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </update>

    <!-- Select By ID -->
    <select id="findById" parameterType="java.lang.Long" resultMap="StatementContributionMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="listStatementContributions" resultMap="StatementContributionMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        AND `application_status_code` != -1  <!-- filter out deleted -->
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="contributionCode != null and contributionCode != ''">
            AND `contribution_code` = #{contributionCode}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countListStatementContributions" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        AND `application_status_code` != -1 <!-- filter out deleted -->
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="contributionCode != null and contributionCode != ''">
            AND `contribution_code` = #{contributionCode}
        </if>
    </select>

    <select id="findByStatementId" resultMap="StatementContributionMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `statement_id` = #{statementId}
        LIMIT 1
    </select>

    <select id="findByParam" parameterType="map" resultMap="StatementContributionMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        AND `application_status_code` != -1
        LIMIT 1
    </select>

    <select id="findAllByParam" parameterType="map" resultMap="StatementContributionMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="contributionCode != null and contributionCode != ''">
            AND `contribution_code` = #{contributionCode}
        </if>
        <choose>
            <when test="applicationStatusCode != null and applicationStatusCode != ''">
                AND `application_status_code` = #{applicationStatusCode}
            </when>
            <otherwise>
                AND `application_status_code` != -1
            </otherwise>
        </choose>
    </select>

    <update id="deleteStatement" parameterType="map">
        UPDATE <include refid="tb"/>
        <set>
            `application_status_code` = -1
        </set>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </update>
</mapper>
