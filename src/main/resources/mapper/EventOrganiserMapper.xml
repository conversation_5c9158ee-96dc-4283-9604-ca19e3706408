<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eroses.external.society.mappers.EventOrganiserDao">

    <resultMap id="EventOrganiserMap" type="com.eroses.external.society.model.EventOrganiser">
        <id property="id" column="id"/>
        <result property="eventId" column="event_id"/>
        <result property="organiserId" column="organiser_id"/>
        <result property="active" column="active"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        event_organiser
    </sql>

    <sql id="tb2">
        organiser
    </sql>


    <sql id="getCols">
        id,
        event_id,
        organiser_id,
        active,
        created_date,
        modified_by,
        created_by
    </sql>

    <sql id="colsCallWithEvents">
        id,
        event_id,
        organiser_id,
    </sql>
    <sql id="cols">
        event_id,
        organiser_id,
        active,
        created_date
    </sql>

    <sql id="vals">
        #{eventId},
        #{organiserId},
        #{active},
        now()
    </sql>


    <insert id="create" parameterType="com.eroses.external.society.model.EventOrganiser" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES (<include refid="vals"/>)
    </insert>

    <select id="findAll" resultType="list" resultMap="EventOrganiserMap">
        <!--        SELECT * FROM event_admin-->
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
    </select>
    <select id="findAllForEvents" resultType="list" resultMap="EventOrganiserMap">
        <!--        SELECT * FROM event_admin-->
        SELECT
        <include refid="colsCallWithEvents"/>
        FROM
        <include refid="tb"/>
    </select>

    <select id="findByEventIdJointTable" resultType="list" resultMap="EventOrganiserMap">
        SELECT *
        FROM
        organiser o
        JOIN
        event_organiser eo
        ON o.id = eo.organiser_id
        WHERE
        eo.event_id = #{eventId}
    </select>

    <select id="findByEventId" resultType="list" resultMap="EventOrganiserMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        event_id = #{eventId}
    </select>
    <!--        SELECT * FROM event_organiser WHERE event_id = #{eventId}-->
    <!--    <select id="findById" resultType="list" resultMap="EventMap">-->
    <!--        SELECT-->
    <!--        <include refid="cols"/>-->
    <!--        FROM-->
    <!--        <include refid="tb"/>-->
    <!--        WHERE-->
    <!--        id = #{id}-->
    <!--    </select>-->

    <!--    <update id="update" parameterType="com.eroses.external.society.model.Event">-->
    <!--        UPDATE-->
    <!--        <include refid="tb"/>-->
    <!--        <set>-->
    <!--            <if test="visibility != null">visibility = #{visibility},</if>-->
    <!--            <if test="eventName != null">event_name = #{eventName},</if>-->
    <!--            <if test="hasMax != null">has_max = #{hasMax},</if>-->
    <!--            <if test="published != null">published = #{published},</if>-->
    <!--            <if test="status != null">status = #{status},</if>-->
    <!--            <if test="regStartDate != null">reg_start_date = #{regStartDate},</if>-->
    <!--            <if test="regEndDate != null">reg_end_date = #{regEndDate},</if>-->
    <!--            <if test="maxParticipants != null">max_participants = #{maxParticipants},</if>-->
    <!--            <if test="description != null">description = #{description},</if>-->
    <!--            <if test="address1 != null">address_1 = #{address1},</if>-->
    <!--            <if test="address2 != null">address_2 = #{address2},</if>-->
    <!--            <if test="state != null">state = #{state},</if>-->
    <!--            <if test="postcode != null">postcode = #{postcode},</if>-->
    <!--            <if test="eventStartDate != null">event_start_date = #{eventStartDate},</if>-->
    <!--            <if test="eventEndDate != null">event_end_date = #{eventEndDate},</if>-->
    <!--            &lt;!&ndash;            <if test="1 == 1">modified_date = now()</if>&ndash;&gt;-->
    <!--            modified_date = now()-->
    <!--        </set>-->
    <!--        WHERE-->
    <!--        id = #{id}-->
    <!--    </update>-->
</mapper>
