<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ForbiddenKeyword">
    <resultMap id="ForbiddenKeywordMap" type="com.eroses.external.society.model.ForbiddenKeyword">
        <id property="id" column="id"/>
        <result property="keyword" column="keyword"/>
        <result property="remarks" column="remarks"/>
        <result property="forbiddenType" column="forbidden_type"/>
        <result property="active" column="active"/>
        <result property="activeRemarks" column="active_remarks"/>
        <result property="status" column="status"/>
        <result property="createdDate" column="created_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>

    <sql id="tb">
        `forbidden_keyword`
    </sql>

    <sql id="cols">
        `id`, `keyword`, `remarks`, `forbidden_type`, `active`,
        `active_remarks`, `status`, `created_date`, `created_by`,
        `modified_by`, `modified_date`
    </sql>

    <sql id="insertCols">
        `keyword`, `remarks`, `forbidden_type`, `active`,
        `active_remarks`, `status`, `created_by`, `modified_by`
    </sql>

    <insert id="create" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.eroses.external.society.model.ForbiddenKeyword">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="insertCols"/>)
        VALUES (#{keyword}, #{remarks}, #{forbiddenType}, #{active},
        #{activeRemarks}, #{status}, #{createdBy}, #{modifiedBy})
    </insert>

    <delete id="delete">
        UPDATE
        <include refid="tb"/>
        SET `status` = -1
        WHERE `id` = #{id}
    </delete>

    <update id="update" parameterType="com.eroses.external.society.model.ForbiddenKeyword">
        UPDATE
        <include refid="tb"/>
        SET `keyword` = #{keyword}, `remarks` = #{remarks},
        `forbidden_type` = #{forbiddenType}, `active` = #{active},
        `active_remarks` = #{activeRemarks},
        `modified_by` = #{modifiedBy}, `modified_date` = NOW()
        WHERE `id` = #{id}
    </update>

    <select id="findAll" resultMap="ForbiddenKeywordMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `status` = 11
        ORDER BY `created_date` DESC
    </select>

    <select id="findByType" resultMap="ForbiddenKeywordMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `forbidden_type` = #{type} AND `status` = 11
        ORDER BY `created_date` DESC
    </select>

    <select id="findByTypePagination" resultMap="ForbiddenKeywordMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `forbidden_type` = #{type} AND `status` = 11
        ORDER BY `created_date` DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>
    <select id="countByForbiddenType" resultType="Long">
        SELECT COUNT(*) FROM
        <include refid="tb"/>
        WHERE `forbidden_type` = #{type} AND `status` = 11
    </select>

    <select id="findOnlyKeywordByType" resultType="String">
        SELECT `keyword` FROM
        <include refid="tb"/>
        WHERE `forbidden_type` = #{type} AND `status` = 11
    </select>

    <select id="findById" resultMap="ForbiddenKeywordMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        AND `status` = 11
    </select>

    <select id="checkKeyWord" resultMap="ForbiddenKeywordMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `keyword` LIKE CONCAT('%', #{keyword}, '%')
        AND `forbidden_type` = #{type}
        AND `status` = 11
    </select>

    <select id="searchLaranganOrKelabu" resultMap="ForbiddenKeywordMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="searchQuery != null and searchQuery != ''">
            AND (`keyword` LIKE CONCAT('%', #{searchQuery}, '%')
            OR `remarks` LIKE CONCAT('%', #{searchQuery}, '%'))
        </if>
        <if test="forbiddenType != null and forbiddenType != ''">
            AND `forbidden_type` = #{forbiddenType}
        </if>
        AND `status` = 11
        <if test="activeStatus != null">
            AND `active` = #{activeStatus}
        </if>
        ORDER BY `created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{limit} OFFSET #{offset}
        </if>
    </select>

    <select id="countSearchedForbiddens" resultType="Long">
        SELECT COUNT(*) FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="searchQuery != null and searchQuery != ''">
            AND (`keyword` LIKE CONCAT('%', #{searchQuery}, '%')
            OR `remarks` LIKE CONCAT('%', #{searchQuery}, '%'))
        </if>
        AND `status` = 11
        <if test="activeStatus != null">
            AND `active` = #{activeStatus}
        </if>
        <if test="forbiddenType != null and forbiddenType != ''">
            AND `forbidden_type` = #{forbiddenType}
        </if>
    </select>
</mapper>