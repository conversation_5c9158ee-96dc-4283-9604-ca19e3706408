<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AdmPositionJppm">

    <!-- Result Map Definition -->
    <resultMap id="AdmPositionJppmMap" type="com.eroses.external.society.model.AdmPositionJppm">
        <id property="id" column="id" />
        <result column="grade" property="grade"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="status" property="status"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        `adm_position_jppm`
    </sql>

    <sql id="selectTb">
        `adm_position_jppm` apj
    </sql>

    <sql id="selectCols">
        apj.`id` as apj_id,
        apj.`grade` as apj_grade,
        apj.`name` as apj_name,
        apj.`description` as apj_description,
        apj.`status` as apj_status,
        apj.`created_by` as apj_created_by,
        apj.`created_date` as apj_created_date,
        apj.`modified_by` as apj_modified_by,
        apj.`modified_date` as apj_modified_date
    </sql>

    <sql id="cols">
        `grade`, `name`, `description`, `status`,
        `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="vals">
        #{grade},
        #{name},
        #{description},
        #{status},
        #{createdBy},
        now(),
        #{modifiedBy},
        now()
    </sql>

    <!-- Select Queries -->
    <select id="findAll" resultType="list" resultMap="AdmPositionJppmMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `status` != '-1'
        AND `status` != '0'
    </select>

    <insert id="create" parameterType="com.eroses.external.society.model.AdmPositionJppm" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.AdmPositionJppm">
        UPDATE <include refid="tb"/>
        <set>
            <if test="grade != null">`grade` = #{grade},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="description != null">`description` = #{description},</if>
            <if test="status != null">`status` = #{status},</if>
            `modified_date` = now(),
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="findById" parameterType="java.lang.Long" resultMap="AdmPositionJppmMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="getAll" resultMap="AdmPositionJppmMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE 1=1
            <if test="nameQuery != null and nameQuery != ''">
                AND `name` LIKE CONCAT('%', #{nameQuery}, '%')
            </if>
        ORDER BY `grade` ASC, `name` ASC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAll" resultType="Long">
        SELECT COUNT(*)
        FROM <include refid="tb"/>
        WHERE 1=1
        <if test="nameQuery != null and nameQuery != ''">
            AND `name` LIKE CONCAT('%', #{nameQuery}, '%')
        </if>
    </select>

    <select id="getAllActive" resultMap="AdmPositionJppmMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `status` = '1'
    </select>

    <select id="existsByGradeAndName" resultType="boolean">
        SELECT EXISTS (
        SELECT 1
        FROM <include refid="tb"/>
        WHERE `grade` = #{grade}
        AND `name` = #{name}
        )
    </select>

    <select id="existsByGradeAndNameExcludingId" resultType="boolean">
        SELECT EXISTS (
        SELECT 1
        FROM <include refid="tb"/>
        WHERE `grade` = #{grade}
        AND `name` = #{name}
        AND `id` != #{id}
        )
    </select>

    <delete id="delete" parameterType="long">
        DELETE FROM <include refid="tb"/>
        WHERE id = #{id}
    </delete>
</mapper>
