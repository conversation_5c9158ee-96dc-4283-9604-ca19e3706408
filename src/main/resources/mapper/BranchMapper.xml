<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Branch">
    <resultMap id="BranchMap" type="com.eroses.external.society.model.Branch">
        <id property="id" column="id" />
        <result column="branch_application_no" property="branchApplicationNo"/>
        <result column="branch_no" property="branchNo"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="ic_no" property="icNo"/>
        <result column="name" property="name"/>
        <result column="branch_level" property="branchLevel"/>
        <result column="request_date" property="requestDate"/>
        <result column="address" property="address"/>
        <result column="country_code" property="countryCode"/>
        <result column="state_id" property="stateCode"/>
        <result column="district_id" property="districtCode"/>
        <result column="small_district_code" property="smallDistrictCode"/>
        <result column="city_code" property="cityCode"/>
        <result column="city" property="city"/>
        <result column="postcode" property="postcode"/>
        <result column="mailing_address" property="mailingAddress"/>
        <result column="mailing_country_code" property="mailingCountryCode"/>
        <result column="mailing_state_code" property="mailingStateCode"/>
        <result column="mailing_district_code" property="mailingDistrictCode"/>
        <result column="mailing_small_district_code" property="mailingSmallDistrictCode"/>
        <result column="mailing_city_code" property="mailingCityCode"/>
        <result column="mailing_city" property="mailingCity"/>
        <result column="mailing_postcode" property="mailingPostcode"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="fax_number" property="faxNumber"/>
        <result column="email" property="email"/>
        <result column="branch_status_code" property="branchStatusCode"/>
        <result column="confession" property="confession"/>
        <result column="confession_date" property="confessionDate"/>
        <result column="payment_id" property="paymentId"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="payment_date" property="paymentDate"/>
        <result column="submission_date" property="submissionDate"/>
        <result column="body_date" property="bodyDate"/>
        <result column="receipt_number" property="receiptNumber"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="status" property="status"/>
        <result column="sub_status_code" property="subStatusCode"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="NoPPM_induk" property="noPPMInduk"/>
        <result column="NoPPM_cawangan" property="noPPMCawangan"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_reference_no" property="bankReferenceNo"/>
        <result column="receipt_status" property="receiptStatus"/>
        <result column="migrate_stat" property="migrateStat"/>
        <result column="migrate_ajk" property="migrateAjk"/>
        <result column="ro" property="ro"/>
        <result column="appeal_status" property="appealStatus"/>
        <result column="transfer_date" property="transferDate"/>
        <result column="reconcile_date" property="reconcileDate"/>
        <result column="cancel_society" property="cancelSociety"/>
        <result column="query" property="query"/>
        <result column="note_ro" property="noteRo"/>
        <result column="is_queried" property="isQueried"/>
        <result column="application_expiration_date" property="applicationExpirationDate"/>
        <result column="is_extended" property="isExtended"/>
        <result column="migrate" property="migrate"/>
    </resultMap>

    <resultMap id="BranchSelectMap" type="com.eroses.external.society.model.Branch">
        <id property="id" column="b_id" />
        <result column="b_branch_application_no" property="branchApplicationNo"/>
        <result column="b_branch_no" property="branchNo"/>
        <result column="b_society_id" property="societyId"/>
        <result column="b_society_no" property="societyNo"/>
        <result column="b_ic_no" property="icNo"/>
        <result column="b_name" property="name"/>
        <result column="b_branch_level" property="branchLevel"/>
        <result column="b_request_date" property="requestDate"/>
        <result column="b_address" property="address"/>
        <result column="b_country_code" property="countryCode"/>
        <result column="b_state_id" property="stateCode"/>
        <result column="b_district_id" property="districtCode"/>
        <result column="b_small_district_code" property="smallDistrictCode"/>
        <result column="b_city_code" property="cityCode"/>
        <result column="b_city" property="city"/>
        <result column="b_postcode" property="postcode"/>
        <result column="b_mailing_address" property="mailingAddress"/>
        <result column="b_mailing_country_code" property="mailingCountryCode"/>
        <result column="b_mailing_state_code" property="mailingStateCode"/>
        <result column="b_mailing_district_code" property="mailingDistrictCode"/>
        <result column="b_mailing_small_district_code" property="mailingSmallDistrictCode"/>
        <result column="b_mailing_city_code" property="mailingCityCode"/>
        <result column="b_mailing_city" property="mailingCity"/>
        <result column="b_mailing_postcode" property="mailingPostcode"/>
        <result column="b_phone_number" property="phoneNumber"/>
        <result column="b_fax_number" property="faxNumber"/>
        <result column="b_email" property="email"/>
        <result column="b_branch_status_code" property="branchStatusCode"/>
        <result column="b_confession" property="confession"/>
        <result column="b_confession_date" property="confessionDate"/>
        <result column="b_payment_id" property="paymentId"/>
        <result column="b_payment_method" property="paymentMethod"/>
        <result column="b_payment_date" property="paymentDate"/>
        <result column="b_submission_date" property="submissionDate"/>
        <result column="b_body_date" property="bodyDate"/>
        <result column="b_receipt_number" property="receiptNumber"/>
        <result column="b_created_by" property="createdBy"/>
        <result column="b_created_date" property="createdDate"/>
        <result column="b_modified_by" property="modifiedBy"/>
        <result column="b_modified_date" property="modifiedDate"/>
        <result column="b_status" property="status"/>
        <result column="b_sub_status_code" property="subStatusCode"/>
        <result column="b_application_status_code" property="applicationStatusCode"/>
        <result column="b_NoPPM_induk" property="noPPMInduk"/>
        <result column="b_NoPPM_cawangan" property="noPPMCawangan"/>
        <result column="b_bank_name" property="bankName"/>
        <result column="b_bank_reference_no" property="bankReferenceNo"/>
        <result column="b_receipt_status" property="receiptStatus"/>
        <result column="b_migrate_stat" property="migrateStat"/>
        <result column="b_migrate_ajk" property="migrateAjk"/>
        <result column="b_ro" property="ro"/>
        <result column="b_appeal_status" property="appealStatus"/>
        <result column="b_transfer_date" property="transferDate"/>
        <result column="b_reconcile_date" property="reconcileDate"/>
        <result column="b_cancel_society" property="cancelSociety"/>
        <result column="b_query" property="query"/>
        <result column="b_note_ro" property="noteRo"/>
        <result column="b_is_queried" property="isQueried"/>
        <result column="b_application_expiration_date" property="applicationExpirationDate"/>
        <result column="b_is_extended" property="isExtended"/>
        <result column="b_migrate" property="migrate"/>

        <association property="society"
                     resultMap="Society.SocietySelectMap"/>
    </resultMap>

    <resultMap id="BranchAndCommitteeSelectMap" type="com.eroses.external.society.model.Branch">
        <id property="id" column="b_id" />
        <result column="b_branch_application_no" property="branchApplicationNo"/>
        <result column="b_branch_no" property="branchNo"/>
        <result column="b_society_id" property="societyId"/>
        <result column="b_society_no" property="societyNo"/>
        <result column="b_ic_no" property="icNo"/>
        <result column="b_name" property="name"/>
        <result column="b_branch_level" property="branchLevel"/>
        <result column="b_request_date" property="requestDate"/>
        <result column="b_address" property="address"/>
        <result column="b_country_code" property="countryCode"/>
        <result column="b_state_id" property="stateCode"/>
        <result column="b_district_id" property="districtCode"/>
        <result column="b_small_district_code" property="smallDistrictCode"/>
        <result column="b_city_code" property="cityCode"/>
        <result column="b_city" property="city"/>
        <result column="b_postcode" property="postcode"/>
        <result column="b_mailing_address" property="mailingAddress"/>
        <result column="b_mailing_country_code" property="mailingCountryCode"/>
        <result column="b_mailing_state_code" property="mailingStateCode"/>
        <result column="b_mailing_district_code" property="mailingDistrictCode"/>
        <result column="b_mailing_small_district_code" property="mailingSmallDistrictCode"/>
        <result column="b_mailing_city_code" property="mailingCityCode"/>
        <result column="b_mailing_city" property="mailingCity"/>
        <result column="b_mailing_postcode" property="mailingPostcode"/>
        <result column="b_phone_number" property="phoneNumber"/>
        <result column="b_fax_number" property="faxNumber"/>
        <result column="b_email" property="email"/>
        <result column="b_branch_status_code" property="branchStatusCode"/>
        <result column="b_confession" property="confession"/>
        <result column="b_confession_date" property="confessionDate"/>
        <result column="b_payment_id" property="paymentId"/>
        <result column="b_payment_method" property="paymentMethod"/>
        <result column="b_payment_date" property="paymentDate"/>
        <result column="b_submission_date" property="submissionDate"/>
        <result column="b_body_date" property="bodyDate"/>
        <result column="b_receipt_number" property="receiptNumber"/>
        <result column="b_created_by" property="createdBy"/>
        <result column="b_created_date" property="createdDate"/>
        <result column="b_modified_by" property="modifiedBy"/>
        <result column="b_modified_date" property="modifiedDate"/>
        <result column="b_status" property="status"/>
        <result column="b_sub_status_code" property="subStatusCode"/>
        <result column="b_application_status_code" property="applicationStatusCode"/>
        <result column="b_NoPPM_induk" property="noPPMInduk"/>
        <result column="b_NoPPM_cawangan" property="noPPMCawangan"/>
        <result column="b_bank_name" property="bankName"/>
        <result column="b_bank_reference_no" property="bankReferenceNo"/>
        <result column="b_receipt_status" property="receiptStatus"/>
        <result column="b_migrate_stat" property="migrateStat"/>
        <result column="b_migrate_ajk" property="migrateAjk"/>
        <result column="b_ro" property="ro"/>
        <result column="b_appeal_status" property="appealStatus"/>
        <result column="b_transfer_date" property="transferDate"/>
        <result column="b_reconcile_date" property="reconcileDate"/>
        <result column="b_cancel_society" property="cancelSociety"/>
        <result column="b_query" property="query"/>
        <result column="b_note_ro" property="noteRo"/>
        <result column="b_is_queried" property="isQueried"/>
        <result column="b_application_expiration_date" property="applicationExpirationDate"/>
        <result column="b_is_extended" property="isExtended"/>
        <result column="b_migrate" property="migrate"/>

        <collection property="branchNonCitizenCommittees" ofType="com.eroses.external.society.dto.response.branchCommittee.BranchNonCitizenCommitteeGetResponse">
            <id column="non_citizen_id" property="id"/>
            <result column="non_citizen_identification_no" property="identificationNo"/>
            <result column="non_citizen_name" property="name"/>
            <result column="non_citizen_applicant_country_code" property="applicantCountryCode"/>
            <result column="non_citizen_citizenship_status" property="citizenshipStatus"/>
            <result column="non_citizen_designation_code" property="designationCode"/>
            <result column="non_citizen_identification_type" property="identificationType"/>
            <result column="non_citizen_permit_expiration_date" property="permitExpirationDate"/>
            <result column="non_citizen_permit_no" property="permitNo"/>
            <result column="non_citizen_visa_expiration_date" property="visaExpirationDate"/>
            <result column="non_citizen_visa_permit_no" property="visaPermitNo"/>
            <result column="non_citizen_visa_no" property="visaNo"/>
            <result column="non_citizen_summary" property="summary"/>
            <result column="non_citizen_tujuanD_malaysia" property="tujuanDMalaysia"/>
            <result column="non_citizen_stay_duration_digit" property="stayDurationDigit"/>
            <result column="non_citizen_stay_duration_unit" property="stayDurationUnit"/>
            <result column="non_citizen_application_status_code" property="applicationStatusCode"/>
        </collection>
    </resultMap>

    <resultMap id="BranchNoMap" type="com.eroses.external.society.model.Branch">
        <result column="branch_no" property="branchNo"/>
    </resultMap>

    <resultMap id="BasicInfoMap" type="com.eroses.external.society.dto.response.branch.BranchBasicInfoResponse">
        <id property="id" column="id" />
        <result column="branch_no" property="branchNo"/>
        <result column="name" property="name"/>
    </resultMap>

    <resultMap id="BranchSecretariesMap" type="com.eroses.external.society.dto.response.branchCommittee.BranchSecretaryResponse">
        <id property="newSecretaryBranchId" column="new_secretary_branch_id" />
        <result column="branch_name" property="branchName"/>
        <result column="secretary_name" property="secretaryName"/>
        <result column="replacement_date" property="replacementDate"/>
    </resultMap>

    <sql id="tb">
        `branch`
    </sql>

    <sql id="selectTb">
        `branch` b
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="valsWithId">
        `id`, <include refid="vals"/>
    </sql>

    <sql id="cols">
        `branch_application_no`,
        `branch_no`,
        `society_id`,
        `society_no`,
        `ic_no`,
        `name`,
        `branch_level`,
        `request_date`,
        `address`,
        `country_code`,
        `state_id`,
        `district_id`,
        `small_district_code`,
        `city_code`,
        `city`,
        `postcode`,
        `mailing_address`,
        `mailing_country_code`,
        `mailing_state_code`,
        `mailing_district_code`,
        `mailing_small_district_code`,
        `mailing_city_code`,
        `mailing_city`,
        `mailing_postcode`,
        `phone_number`,
        `fax_number`,
        `email`,
        `branch_status_code`,
        `confession`,
        `confession_date`,
        `payment_id`,
        `payment_method`,
        `payment_date`,
        `submission_date`,
        `body_date`,
        `receipt_number`,
        `status`,
        `sub_status_code`,
        `application_status_code`,
        `NoPPM_induk`,
        `NoPPM_cawangan`,
        `bank_name`,
        `bank_reference_no`,
        `receipt_status`,
        `migrate_stat`,
        `migrate_ajk`,
        `ro`,
        `appeal_status`,
        `transfer_date`,
        `reconcile_date`,
        `cancel_society`,
        `query`,
        `note_ro`,
        `is_queried`,
        `application_expiration_date`,
        `is_extended`,
        `migrate`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`
    </sql>
    <sql id="selectCols">
        `b`.`id` AS `b_id`,
        `b`.`branch_application_no` AS `b_branch_application_no`,
        `b`.`branch_no` AS `b_branch_no`,
        `b`.`society_id` AS `b_society_id`,
        `b`.`society_no` AS `b_society_no`,
        `b`.`ic_no` AS `b_ic_no`,
        `b`.`name` AS `b_name`,
        `b`.`branch_level` AS `b_branch_level`,
        `b`.`request_date` AS `b_request_date`,
        `b`.`address` AS `b_address`,
        `b`.`country_code` AS `b_country_code`,
        `b`.`state_id` AS `b_state_id`,
        `b`.`district_id` AS `b_district_id`,
        `b`.`small_district_code` AS `b_small_district_code`,
        `b`.`city_code` AS `b_city_code`,
        `b`.`city` AS `b_city`,
        `b`.`postcode` AS `b_postcode`,
        `b`.`mailing_address` AS `b_mailing_address`,
        `b`.`mailing_country_code` AS `b_mailing_country_code`,
        `b`.`mailing_state_code` AS `b_mailing_state_code`,
        `b`.`mailing_district_code` AS `b_mailing_district_code`,
        `b`.`mailing_small_district_code` AS `b_mailing_small_district_code`,
        `b`.`mailing_city_code` AS `b_mailing_city_code`,
        `b`.`mailing_city` AS `b_mailing_city`,
        `b`.`mailing_postcode` AS `b_mailing_postcode`,
        `b`.`phone_number` AS `b_phone_number`,
        `b`.`fax_number` AS `b_fax_number`,
        `b`.`email` AS `b_email`,
        `b`.`branch_status_code` AS `b_branch_status_code`,
        `b`.`confession` AS `b_confession`,
        `b`.`confession_date` AS `b_confession_date`,
        `b`.`payment_id` AS `b_payment_id`,
        `b`.`payment_method` AS `b_payment_method`,
        `b`.`payment_date` AS `b_payment_date`,
        `b`.`submission_date` AS `b_submission_date`,
        `b`.`body_date` AS `b_body_date`,
        `b`.`receipt_number` AS `b_receipt_number`,
        `b`.`status` AS `b_status`,
        `b`.`sub_status_code` AS `b_sub_status_code`,
        `b`.`application_status_code` AS `b_application_status_code`,
        `b`.`NoPPM_induk` AS `b_NoPPM_induk`,
        `b`.`NoPPM_cawangan` AS `b_NoPPM_cawangan`,
        `b`.`bank_name` AS `b_bank_name`,
        `b`.`bank_reference_no` AS `b_bank_reference_no`,
        `b`.`receipt_status` AS `b_receipt_status`,
        `b`.`migrate_stat` AS `b_migrate_stat`,
        `b`.`migrate_ajk` AS `b_migrate_ajk`,
        `b`.`ro` AS `b_ro`,
        `b`.`appeal_status` AS `b_appeal_status`,
        `b`.`transfer_date` AS `b_transfer_date`,
        `b`.`reconcile_date` AS `b_reconcile_date`,
        `b`.`cancel_society` AS `b_cancel_society`,
        `b`.`query` AS `b_query`,
        `b`.`note_ro` AS `b_note_ro`,
        `b`.`is_queried` AS `b_is_queried`,
        `b`.`application_expiration_date` AS `b_application_expiration_date`,
        `b`.`is_extended` AS `b_is_extended`,
        `b`.`migrate` AS `b_migrate`,
        `b`.`created_by` AS `b_created_by`,
        `b`.`created_date` AS `b_created_date`,
        `b`.`modified_by` AS `b_modified_by`,
        `b`.`modified_date` AS `b_modified_date`
    </sql>

    <sql id="selectNonCitizenCols">
        sncc.id AS non_citizen_id,
        sncc.name AS non_citizen_name,
        sncc.citizenship_status AS non_citizen_citizenship_status,
        sncc.identification_type AS non_citizen_identification_type,
        sncc.identification_no AS non_citizen_identification_no,
        sncc.applicant_country_code AS non_citizen_applicant_country_code,
        sncc.visa_expiration_date AS non_citizen_visa_expiration_date,
        sncc.permit_expiration_date AS non_citizen_permit_expiration_date,
        sncc.visa_no AS non_citizen_visa_no,
        sncc.permit_no AS non_citizen_permit_no,
        sncc.visa_permit_no AS non_citizen_visa_permit_no,
        sncc.tujuanD_malaysia AS non_citizen_tujuanD_malaysia,
        sncc.stay_duration_digit AS non_citizen_stay_duration_digit,
        sncc.stay_duration_unit AS non_citizen_stay_duration_unit,
        sncc.designation_code AS non_citizen_designation_code,
        sncc.application_status_code AS non_citizen_application_status_code,
        sncc.summary AS non_citizen_summary
    </sql>

    <sql id="vals">
        #{branchApplicationNo},
        #{branchNo},
        #{societyId},
        #{societyNo},
        #{icNo},
        #{name},
        #{branchLevel},
        #{requestDate},
        #{address},
        #{countryCode},
        #{stateCode},
        #{districtCode},
        #{smallDistrictCode},
        #{cityCode},
        #{city},
        #{postcode},
        #{mailingAddress},
        #{mailingCountryCode},
        #{mailingStateCode},
        #{mailingDistrictCode},
        #{mailingSmallDistrictCode},
        #{mailingCityCode},
        #{mailingCity},
        #{mailingPostcode},
        #{phoneNumber},
        #{faxNumber},
        #{email},
        #{branchStatusCode},
        #{confession},
        #{confessionDate},
        #{paymentId},
        #{paymentMethod},
        #{paymentDate},
        #{submissionDate},
        #{bodyDate},
        #{receiptNumber},
        #{status},
        #{subStatusCode},
        #{applicationStatusCode},
        #{noPPMInduk},
        #{noPPMCawangan},
        #{bankName},
        #{bankReferenceNo},
        #{receiptStatus},
        #{migrateStat},
        #{migrateAjk},
        #{ro},
        #{appealStatus},
        #{transferDate},
        #{reconcileDate},
        #{cancelSociety},
        #{query},
        #{noteRo},
        #{isQueried},
        #{applicationExpirationDate},
        #{isExtended},
        #{migrate},
        #{createdBy},
        now(),
        #{modifiedBy},
        now()
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.Branch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.Branch">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="branchApplicationNo != null">branch_application_no = #{branchApplicationNo},</if>
            <if test="branchNo != null">branch_no = #{branchNo},</if>
            <if test="societyId != null">society_id = #{societyId},</if>
            <if test="societyNo != null">society_no = #{societyNo},</if>
            <if test="icNo != null">ic_no = #{icNo},</if>
            <if test="name != null">name = #{name},</if>
            <if test="branchLevel != null">branch_level = #{branchLevel},</if>
            <if test="requestDate != null">request_date = #{requestDate},</if>
            <if test="address != null">address = #{address},</if>
            <if test="countryCode != null">country_code = #{countryCode},</if>
            <if test="stateCode != null">state_id = #{stateCode},</if>
            <if test="districtCode != null">district_id = #{districtCode},</if>
            <if test="smallDistrictCode != null">small_district_code = #{smallDistrictCode},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="city != null">city = #{city},</if>
            <if test="postcode != null">postcode = #{postcode},</if>
            <if test="mailingAddress != null">mailing_address = #{mailingAddress},</if>
            <if test="mailingCountryCode != null">mailing_country_code = #{mailingCountryCode},</if>
            <if test="mailingStateCode != null">mailing_state_code = #{mailingStateCode},</if>
            <if test="mailingDistrictCode != null">mailing_district_code = #{mailingDistrictCode},</if>
            <if test="mailingSmallDistrictCode != null">mailing_small_district_code = #{mailingSmallDistrictCode},</if>
            <if test="mailingCityCode != null">mailing_city_code = #{mailingCityCode},</if>
            <if test="mailingCity != null">mailing_city = #{mailingCity},</if>
            <if test="mailingPostcode != null">mailing_postcode = #{mailingPostcode},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="faxNumber != null">fax_number = #{faxNumber},</if>
            <if test="email != null">email = #{email},</if>
            <if test="branchStatusCode != null">branch_status_code = #{branchStatusCode},</if>
            <if test="confession != null">confession = #{confession},</if>
            <if test="confessionDate != null">confession_date = #{confessionDate},</if>
            <if test="paymentId != null">payment_id = #{paymentId},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="paymentDate != null">payment_date = #{paymentDate},</if>
            <if test="submissionDate != null">submission_date = #{submissionDate},</if>
            <if test="bodyDate != null">body_date = #{bodyDate},</if>
            <if test="receiptNumber != null">receipt_number = #{receiptNumber},</if>
            <if test="status != null">status = #{status},</if>
            <if test="subStatusCode != null">sub_status_code = #{subStatusCode},</if>
            <if test="applicationStatusCode != null">application_status_code = #{applicationStatusCode},</if>
            <if test="noPPMInduk != null">NoPPM_induk = #{noPPMInduk},</if>
            <if test="noPPMCawangan != null">NoPPM_cawangan = #{noPPMCawangan},</if>
            <if test="migrateStat != null">migrate_stat = #{migrateStat},</if>
            <if test="migrateAjk != null">migrate_ajk = #{migrateAjk},</if>
            <if test="ro != null">ro = #{ro},</if>
            <if test="appealStatus != null">appeal_status = #{appealStatus},</if>
            <if test="transferDate != null">transfer_date = #{transferDate},</if>
            <if test="reconcileDate != null">reconcile_date = #{reconcileDate},</if>
            <if test="cancelSociety != null">cancel_society = #{cancelSociety},</if>
            <if test="query != null">query = #{query},</if>
            <if test="noteRo != null">note_ro = #{noteRo},</if>
            <if test="isQueried != null">is_queried = #{isQueried},</if>
            <if test="applicationExpirationDate != null">application_expiration_date = #{applicationExpirationDate},</if>
            <if test="isExtended != null">is_extended = #{isExtended},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date = now()
        </set>
        WHERE
        id = #{id}
    </update>

    <select id="findAll" parameterType="map" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="branchId != null">
            AND `id` = #{branchId}
        </if>
        <if test="branchNo != null and branchNo != ''">
            AND `branch_no` LIKE CONCAT('%', LOWER(#{branchNo}), '%')
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="name != null and name != ''">
            AND `name` LIKE CONCAT('%', LOWER(#{name}), '%')
        </if>
        <choose>
            <when test="applicationStatusCode != null and applicationStatusCode != '' and applicationStatusCode != -1">
                AND `application_status_code` = #{applicationStatusCode}
            </when>
            <otherwise>
                AND `application_status_code` IN (3,11)
            </otherwise>
        </choose>
        <if test="status != null">
            AND `status` = #{status}
        </if>
        <if test="statusCode != null">
            AND `application_status_code` = #{statusCode}
        </if>
        <if test="icNo != null">
            AND `icNo` = #{icNo}
        </if>

        <!-- Sorting Logic -->
        ORDER BY
        <choose>
            <when test="sortByName == 1"> `name` ASC, </when>
            <when test="sortByName == 2"> `name` DESC, </when>
        </choose>
        <choose>
            <when test="sortByApplicationStatus == 1"> `application_status_code` ASC, </when>
            <when test="sortByApplicationStatus == 2"> `application_status_code` DESC, </when>
        </choose>
        `created_date` DESC

        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="existByBranchName" parameterType="map" resultType="java.lang.Boolean">
        SELECT CASE
            WHEN COUNT(*) > 0 THEN false
            ELSE true
        END
        FROM branch
        WHERE LOWER(TRIM(name)) = LOWER(TRIM(#{name}))
        <if test="societyId != null">
            AND society_id = #{societyId}
        </if>
    </select>

    <select id="findBranchIdListByIdentificationNo" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT id
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="identificationNo != null and identificationNo != ''">
            AND `ic_no` = #{identificationNo}
        </if>
    </select>

    <select id="findBranchNo" parameterType="java.lang.Long" resultMap="BranchNoMap">
        SELECT
        branch_no
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="getByApplicationNo" parameterType="java.lang.String" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `branch_application_no` = #{applicationNo}
    </select>

    <select id="getByPaymentId" parameterType="java.lang.Long" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `payment_id` = #{paymentId}
    </select>

    <select id="findById" parameterType="java.lang.Long" resultMap="BranchAndCommitteeSelectMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="selectNonCitizenCols"/>
        FROM <include refid="tb"/> b
        LEFT JOIN society_non_citizen_committee sncc ON (sncc.branch_id = b.id AND sncc.application_status_code != "-1")
        WHERE b.id = #{id}
    </select>

    <select id="findBasicInfo" parameterType="java.lang.Long" resultMap="BasicInfoMap">
        SELECT
            id,
            branch_no,
            name
        FROM <include refid="tb"/>
        WHERE
            society_id = #{societyId}
            AND status = "001"
    </select>

    <select id="getBranchSecretaries" parameterType="map" resultMap="BranchSecretariesMap">
        SELECT
            nsb.id AS new_secretary_branch_id,
            b.name AS branch_name,
            bc.committee_name AS secretary_name,
            bc.created_date AS replacement_date
        FROM <include refid="tb"/> b
        JOIN new_secretary_branch nsb ON nsb.branch_id = b.id
        JOIN branch_committee bc ON bc.branch_id = nsb.branch_id
        WHERE
            b.society_id = #{societyId}
            <if test="branchName != null">
                AND b.name = #{branchName}
            </if>
            <if test="secretaryName != null">
                <bind name="wildcardSecretaryName" value="'%' + secretaryName + '%'" />
                AND bc.committee_name LIKE #{wildcardSecretaryName}
            </if>
        ORDER BY bc.created_date DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="getBranchSecretariesCount" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM <include refid="tb"/> b
        JOIN new_secretary_branch nsb ON nsb.branch_id = b.id
        JOIN branch_committee bc ON bc.branch_id = nsb.branch_id
        WHERE
            b.society_id = #{societyId}
            <if test="branchName != null">
                AND b.name = #{branchName}
            </if>
            <if test="secretaryName != null">
                <bind name="wildcardSecretaryName" value="'%' + secretaryName + '%'" />
                AND bc.committee_name LIKE #{wildcardSecretaryName}
            </if>
    </select>

    <select id="findAllByParams" parameterType="map" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="searchQuery != null and searchQuery != ''">
                AND (
                branch_no LIKE CONCAT('%', #{searchQuery}, '%')
                )
            </if>
            <if test="state != null">
                AND state_id = #{state}
            </if>
            <if test="branchId != null">
                AND id = #{branchId}
            </if>
            <if test="societyId != null">
                AND society_id = #{societyId}
            </if>
            <if test="applicationStatusCode != null">
                AND application_status_code = #{applicationStatusCode}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countAllByParams"  resultType="java.lang.Long">
        SELECT COUNT(*)
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="searchQuery != null and searchQuery != ''">
                AND (
                branch_no LIKE CONCAT('%', #{searchQuery}, '%')
                )
            </if>
            <if test="state != null">
                AND state_id = #{state}
            </if>
            <if test="branchId != null">
                AND id = #{branchId}
            </if>
            <if test="societyId != null">
                AND society_id = #{societyId}
            </if>
            <if test="applicationStatusCode != null">
                AND application_status_code = #{applicationStatusCode}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

    <select id="findByStatusCode" parameterType="java.lang.Long" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `status` = #{statusCode}
    </select>

    <select id="findBranchApprovalInfo" parameterType="java.lang.Long" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `id` = #{branchId}
    </select>

    <update id="updateBranchApprovalRo" parameterType="com.eroses.external.society.model.Branch">
        UPDATE
        <include refid="tb"/>
        SET
        `name` = #{name},
        `ro` = #{ro},
        `note_ro` = #{noteRo},
        `modified_by` = #{modifiedBy},
        `modified_date` = now()
        WHERE `id` = #{id}
    </update>

    <select id="countBranchRegisteredToday" resultType="int">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE `created_date` LIKE CONCAT(#{currDate}, '%')
    </select>

    <update id="updateBranchApprovalDecision" parameterType="com.eroses.external.society.model.Branch">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="branchNo != null">
                `branch_no` = #{branchNo},
            </if>
            `status` = #{status},
            `application_status_code` = #{applicationStatusCode},
            <if test="cancelSociety != null">
                `cancel_society` = #{cancelSociety},
            </if>
            `note_ro` = #{noteRo},
            `modified_by` = #{modifiedBy},
            `modified_date` = now()
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="findAllExtendingBranch" resultMap="BranchMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE
        `appeal_status` IS NOT NULL
        AND `appeal_status` != ''
    </select>

    <update id="updateBranchExtension" parameterType="com.eroses.external.society.model.Branch">
        UPDATE
        <include refid="tb"/>
        SET
        `appeal_status` = #{appealStatus},
        `application_status_code` = #{applicationStatusCode},
        `note_ro` = #{noteRo},
        `modified_by` = #{modifiedBy},
        `modified_date` = now()
        WHERE `id` = #{id}
    </update>

    <select id="findBySocietyId" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
    </select>

    <select id="findLastBranchNo" parameterType="String" resultType="String">
        SELECT `branch_no`
        FROM
        <include refid="tb"/>
        WHERE `society_no` = #{societyNo}
        ORDER BY `branch_no` DESC
        LIMIT 1
    </select>

    <select id="findAllPending" parameterType="map" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `application_status_code` = #{applicationStatusCode}
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countAllPending" resultType="java.lang.Long">
        SELECT COUNT(*)
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `application_status_code` = #{applicationStatusCode}
    </select>

    <select id="countAllBranch" resultType="java.lang.Long">
        SELECT COUNT(*)
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="branchId != null">
            AND `id` = #{branchId}
        </if>
        <if test="branchNo != null and branchNo != ''">
            AND `branch_no` LIKE CONCAT('%', LOWER(#{branchNo}), '%')
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="name != null and name != ''">
            AND `name` LIKE CONCAT('%', LOWER(#{name}), '%')
        </if>
        <choose>
            <when test="applicationStatusCode != null and applicationStatusCode != '' and applicationStatusCode != -1">
                AND `application_status_code` = #{applicationStatusCode}
            </when>
            <otherwise>
                AND `application_status_code` IN (3, 11)
            </otherwise>
        </choose>
        <if test="status != null">
            AND `status` = #{status}
        </if>
        <if test="statusCode != null">
            AND `application_status_code` = #{statusCode}
        </if>
        <if test="icNo != null">
            AND `icNo` = #{icNo}
        </if>
    </select>

    <select id="findAllPendingUserStateCode" parameterType="map" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `application_status_code` = #{applicationStatusCode}
        AND
        `state_id` = #{stateCode}
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countAllPendingUserStateCode" resultType="java.lang.Long">
        SELECT COUNT(*)
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `application_status_code` = #{applicationStatusCode}
        AND
        `state_id` = #{stateCode}
    </select>

<!--    DEPRECATED-->
    <select id="findByBranchIdOrStatusCode" parameterType="map" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <where>
            status != #{deletedCode}
            <if test="societyId != null">
                AND `society_id` = #{societyId}
            </if>
            <if test="statusCode != null">
                AND `status` = #{statusCode}
            </if>
            <if test="branchId != null">
                AND `id` = #{branchId}
            </if>
        </where>
        ORDER BY created_date DESC
        LIMIT #{offset}, #{limit}
    </select>
<!--    DEPRECATED-->

    <select id="countStatusCode" resultType="int">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE `status` = #{statusCode}
    </select>

    <select id="countApplicationStatusCode" resultType="int">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE `application_status_code` = #{applicationStatusCode}
    </select>

    <select id="countApplicationStatusCodeAndPaymentDate" resultType="int">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE `application_status_code` = #{applicationStatusCode}
        AND YEAR(`payment_date`) = #{year}
    </select>

    <select id="countBranchStatusInSociety" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE
        `society_id` = #{societyId}
        AND
        `status` = #{status}
    </select>

    <select id="countAllPendingByCriteria" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND application_status_code IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND ro = #{ro}
        </if>
        <if test="isQueried == 0"> <!-- Cater for NULL data -->
            AND (is_queried IS NULL OR is_queried = 0)
        </if>
        <if test="isQueried != null and isQueried != 0">
            AND is_queried = #{isQueried}
        </if>
        <if test="search != null and search != ''">
            AND (
            branch_no LIKE CONCAT('%', #{search}, '%')
            OR name LIKE CONCAT('%', #{search}, '%')
            )
        </if>
    </select>

    <select id="getAllPendingByCriteria" parameterType="map" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND application_status_code IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND ro = #{ro}
        </if>
        <if test="isQueried == 0"> <!-- Cater for NULL data -->
            AND (is_queried IS NULL OR is_queried = 0)
        </if>
        <if test="isQueried != null and isQueried != 0">
            AND is_queried = #{isQueried}
        </if>
        <if test="search != null and search != ''">
            AND (
            branch_no LIKE CONCAT('%', #{search}, '%')
            OR name LIKE CONCAT('%', #{search}, '%')
            )
        </if>
        ORDER BY payment_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="isBranchNameExist" parameterType="map" resultType="Boolean">
        SELECT CASE
            WHEN COUNT(1) > 0 THEN TRUE
            ELSE FALSE
        END
        FROM
        <include refid="tb"/>
        WHERE
        society_id = #{societyId}
        AND name = #{branchName}
        AND application_status_code != #{applicationStatusCode}
    </select>

    <select id="getAllExpiredBranchApplicationId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        SELECT id
        FROM
        <include refid="tb"/>
        WHERE
        application_status_code = #{applicationStatusCode}
        AND NOW() > application_expiration_date
    </select>

    <update id="updateApplicationStatusCodeByIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            application_status_code = #{applicationStatusCode}
        </set>
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateStatusCodeAndSubStatusCodeByIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            branch_status_code = #{status},
            status = #{status},
            sub_status_code = #{subStatusCode}
        </set>
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="findBranchForAppeal" parameterType="map" resultMap="Appeal.AppealByUserMap">
        SELECT <include refid="Appeal.branchAppealCols"/>
        FROM <include refid="selectTb"/>
        LEFT JOIN <include refid="Society.selectTb"/> ON b.`society_id` = s.`id`
        WHERE b.`application_status_code` IN (4)
        AND (b.`appeal_status` = 0 OR b.`appeal_status` IS NULL)
        <if test="branchIdList != null and branchIdList.size() > 0">
            AND b.`id` IN
            <foreach item="id" collection="branchIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
            OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
    </select>

    <!-- New query for getBranchesByDetailedParam -->
    <select id="findBranchesByDetailedParam" parameterType="map" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="branchId != null">
            AND `id` = #{branchId}
        </if>
        <if test="branchNo != null and branchNo != ''">
            AND `branch_no` LIKE CONCAT('%', LOWER(#{branchNo}), '%')
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="name != null and name != ''">
            AND `name` LIKE CONCAT('%', LOWER(#{name}), '%')
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="applicationStatusCode == null">
            AND `application_status_code` != -1
        </if>
        <if test="status != null">
            AND `status` = #{status}
        </if>
        <if test="status == null">
            AND `status` != '-001'
        </if>
        <if test="icNo != null and icNo != ''">
            AND `ic_no` = #{icNo}
        </if>

        <!-- Sorting Logic -->
        ORDER BY
        <choose>
            <when test="sortByName == 1"> `name` ASC, </when>
            <when test="sortByName == 2"> `name` DESC, </when>
            <otherwise></otherwise>
        </choose>
        <choose>
            <when test="sortByApplicationStatus == 1"> `application_status_code` ASC, </when>
            <when test="sortByApplicationStatus == 2"> `application_status_code` DESC, </when>
            <otherwise></otherwise>
        </choose>
        `created_date` DESC

        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- Count query for getBranchesByDetailedParam -->
    <select id="countBranchesByDetailedParam" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="branchId != null">
            AND `id` = #{branchId}
        </if>
        <if test="branchNo != null and branchNo != ''">
            AND `branch_no` LIKE CONCAT('%', LOWER(#{branchNo}), '%')
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="name != null and name != ''">
            AND `name` LIKE CONCAT('%', LOWER(#{name}), '%')
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="applicationStatusCode == null">
            AND `application_status_code` != -1
        </if>
        <if test="status != null">
            AND `status` = #{status}
        </if>
        <if test="status == null">
            AND `status` != '-001'
        </if>
        <if test="icNo != null and icNo != ''">
            AND `ic_no` = #{icNo}
        </if>
    </select>

    <select id="findAllBySocietyIdAndApplicationStatusCodeAndStatus" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
        AND `application_status_code` = #{applicationStatusCode}
        AND `status` = #{status}
    </select>

    <select id="findAllBySocietyIdByStatusAndSubStatusCode" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
        AND `status` = #{status}
        AND `sub_status_code` = #{subStatusCode}
    </select>

    <select id="findByIds" parameterType="list" resultMap="BranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        AND `id` IN
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="getAllBranchPendingApproval" parameterType="map" resultMap="BranchSelectMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>
        FROM <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON b.society_id = s.id
        WHERE b.`application_status_code` = #{applicationStatusCode}
        AND b.`status` = #{statusCode}
        AND b.`submission_date` IS NOT NULL
        AND b.`submission_date` = DATE_SUB(CURDATE(), INTERVAL #{daysAfterSubmission} DAY)
    </select>

    <select id="getBranchesByCriteriaAndExcludingActiveSocietyCancellationBranches" parameterType="map" resultMap="BranchSelectMap">
        SELECT
        <include refid="selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.`id` = b.`society_id`
        WHERE b.`id` NOT IN (
            SELECT DISTINCT `branch_id`
            FROM
            <include refid="SocietyCancellation.tb"/>
            WHERE `is_reverted` = 0
            AND `branch_id` IS NOT NULL
        )
        <if test="branchName != null and branchName != ''">
            AND b.`name` LIKE CONCAT('%', #{branchName}, '%')
        </if>
        <if test="branchNo != null and branchNo != ''">
            AND b.`branch_no` LIKE CONCAT('%', #{branchNo}, '%')
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND b.`application_status_code` = #{applicationStatusCode}
        </if>
        <if test="statusCode != null and statusCode != ''">
            AND b.`status` = #{statusCode}
        </if>
        <if test="stateCodes != null and stateCodes.size() > 0">
            AND b.`state_id` IN
            <foreach collection="stateCodes" item="stateCode" open="(" close=")" separator=",">
                #{stateCode}
            </foreach>
        </if>
        <if test="societyCategoryCode != null and societyCategoryCode != 0">
            AND s.`category_code_jppm` = #{societyCategoryCode}
        </if>
        ORDER BY registered_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countBranchesByCriteriaAndExcludingActiveSocietyCancellationBranches" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.`id` = b.`society_id`
        WHERE b.`id` NOT IN (
            SELECT DISTINCT `branch_id`
            FROM
            <include refid="SocietyCancellation.tb"/>
            WHERE `is_reverted` = 0
            AND `branch_id` IS NOT NULL
        )
        <if test="branchName != null and branchName != ''">
            AND b.`name` LIKE CONCAT('%', #{branchName}, '%')
        </if>
        <if test="branchNo != null and branchNo != ''">
            AND b.`branch_no` LIKE CONCAT('%', #{branchNo}, '%')
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND b.`application_status_code` = #{applicationStatusCode}
        </if>
        <if test="statusCode != null and statusCode != ''">
            AND b.`status` = #{statusCode}
        </if>
        <if test="stateCodes != null and stateCodes.size() > 0">
            AND b.`state_id` IN
            <foreach collection="stateCodes" item="stateCode" open="(" close=")" separator=",">
                #{stateCode}
            </foreach>
        </if>
        <if test="societyCategoryCode != null and societyCategoryCode != 0">
            AND s.`category_code_jppm` = #{societyCategoryCode}
        </if>
    </select>

</mapper>
