<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="Whitelist">
    <!-- Result mapping -->
    <resultMap id="WhitelistMap" type="com.eroses.external.society.model.blacklist.Whitelist">
        <id property="id" column="id"/>
        <result column="blacklist_id" property="blacklistId"/>
        <result column="reference_no" property="referenceNo"/>
        <result column="whitelist_date" property="whitelistDate"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="notice_reference_no" property="noticeReferenceNo"/>
        <result column="notes" property="notes"/>
        <result column="is_completed" property="isCompleted"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <resultMap id="WhitelistSelectMap" type="com.eroses.external.society.model.blacklist.Whitelist">
        <id property="id" column="w_id"/>
        <result column="w_blacklist_id" property="blacklistId"/>
        <result column="w_reference_no" property="referenceNo"/>
        <result column="w_whitelist_date" property="whitelistDate"/>
        <result column="w_society_id" property="societyId"/>
        <result column="w_society_no" property="societyNo"/>
        <result column="w_branch_id" property="branchId"/>
        <result column="w_branch_no" property="branchNo"/>
        <result column="w_notice_reference_no" property="noticeReferenceNo"/>
        <result column="w_notes" property="notes"/>
        <result column="w_is_completed" property="isCompleted"/>
        <result column="w_created_by" property="createdBy"/>
        <result column="w_created_date" property="createdDate"/>
        <result column="w_modified_by" property="modifiedBy"/>
        <result column="w_modified_date" property="modifiedDate"/>
    </resultMap>

    <!-- SQL snippets -->
    <sql id="tb">
        `whitelist`
    </sql>

    <sql id="selectTb">
        `whitelist` w
    </sql>

    <sql id="cols">
        `blacklist_id`,
        `reference_no`,
        `whitelist_date`,
        `society_id`,
        `society_no`,
        `branch_id`,
        `branch_no`,
        `notice_reference_no`,
        `notes`,
        `is_completed`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`
    </sql>

    <sql id="selectCols">
        w.`id` as w_id,
        w.`blacklist_id` as w_blacklist_id,
        w.`reference_no` as w_reference_no,
        w.`whitelist_date` as w_whitelist_date,
        w.`society_id` as w_society_id,
        w.`society_no` as w_society_no,
        w.`branch_id` as w_branch_id,
        w.`branch_no` as w_branch_no,
        w.`notice_reference_no` as w_notice_reference_no,
        w.`notes` as w_notes,
        w.`is_completed` as w_is_completed,
        w.`created_by` as w_created_by,
        w.`created_date` as w_created_date,
        w.`modified_by` as w_modified_by,
        w.`modified_date` as w_modified_date
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="insertCols">
        `blacklist_id`,
        `reference_no`,
        `whitelist_date`,
        `society_id`,
        `society_no`,
        `branch_id`,
        `branch_no`,
        `notice_reference_no`,
        `notes`,
        `is_completed`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`
    </sql>

    <sql id="vals">
        #{blacklistId},
        #{referenceNo},
        #{whitelistDate},
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{noticeReferenceNo},
        #{notes},
        #{isCompleted},
        #{createdBy},
        NOW(),
        #{modifiedBy},
        NOW()
    </sql>

    <!-- Select operations -->
    <select id="findById" parameterType="java.lang.Long" resultMap="WhitelistMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="findByBlacklistId" parameterType="java.lang.Long" resultMap="WhitelistMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `blacklist_id` = #{blacklistId}
        ORDER BY `id` DESC
    </select>

    <select id="findByReferenceNo" parameterType="java.lang.String" resultMap="WhitelistMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `reference_no` = #{referenceNo}
        LIMIT 1
    </select>

    <select id="search" parameterType="map" resultMap="WhitelistMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        <where>
            <if test="blacklistId != null">
                AND `blacklist_id` = #{blacklistId}
            </if>
            <if test="referenceNo != null and referenceNo != ''">
                AND `reference_no` LIKE CONCAT('%', #{referenceNo}, '%')
            </if>
            <if test="whitelistDateFrom != null">
                AND `whitelist_date` &gt;= #{whitelistDateFrom}
            </if>
            <if test="whitelistDateTo != null">
                AND `whitelist_date` &lt;= #{whitelistDateTo}
            </if>
            <if test="societyId != null">
                AND `society_id` = #{societyId}
            </if>
            <if test="societyNo != null and societyNo != ''">
                AND `society_no` LIKE CONCAT('%', #{societyNo}, '%')
            </if>
            <if test="branchId != null">
                AND `branch_id` = #{branchId}
            </if>
            <if test="branchNo != null and branchNo != ''">
                AND `branch_no` LIKE CONCAT('%', #{branchNo}, '%')
            </if>
            <if test="noticeReferenceNo != null and noticeReferenceNo != ''">
                AND `notice_reference_no` LIKE CONCAT('%', #{noticeReferenceNo}, '%')
            </if>
            <if test="isCompleted != null">
                AND `is_completed` = #{isCompleted}
            </if>
        </where>
        ORDER BY `created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countSearch" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM <include refid="tb"/>
        <where>
            <if test="blacklistId != null">
                AND `blacklist_id` = #{blacklistId}
            </if>
            <if test="referenceNo != null and referenceNo != ''">
                AND `reference_no` LIKE CONCAT('%', #{referenceNo}, '%')
            </if>
            <if test="whitelistDateFrom != null">
                AND `whitelist_date` &gt;= #{whitelistDateFrom}
            </if>
            <if test="whitelistDateTo != null">
                AND `whitelist_date` &lt;= #{whitelistDateTo}
            </if>
            <if test="societyId != null">
                AND `society_id` = #{societyId}
            </if>
            <if test="societyNo != null and societyNo != ''">
                AND `society_no` LIKE CONCAT('%', #{societyNo}, '%')
            </if>
            <if test="branchId != null">
                AND `branch_id` = #{branchId}
            </if>
            <if test="branchNo != null and branchNo != ''">
                AND `branch_no` LIKE CONCAT('%', #{branchNo}, '%')
            </if>
            <if test="noticeReferenceNo != null and noticeReferenceNo != ''">
                AND `notice_reference_no` LIKE CONCAT('%', #{noticeReferenceNo}, '%')
            </if>
            <if test="isCompleted != null">
                AND `is_completed` = #{isCompleted}
            </if>
        </where>
    </select>

    <!-- Insert operation -->
    <insert id="create" parameterType="com.eroses.external.society.model.blacklist.Whitelist" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
        (<include refid="insertCols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <!-- Update operation -->
    <update id="update" parameterType="com.eroses.external.society.model.blacklist.Whitelist">
        UPDATE <include refid="tb"/>
        <set>
            <if test="blacklistId != null">`blacklist_id` = #{blacklistId},</if>
            <if test="referenceNo != null">`reference_no` = #{referenceNo},</if>
            <if test="whitelistDate != null">`whitelist_date` = #{whitelistDate},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="noticeReferenceNo != null">`notice_reference_no` = #{noticeReferenceNo},</if>
            <if test="notes != null">`notes` = #{notes},</if>
            <if test="isCompleted != null">`is_completed` = #{isCompleted},</if>
            `modified_date` = NOW(),
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- Delete operation -->
    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM <include refid="tb"/>
        WHERE `id` = #{id}
    </delete>

    <select id="getAllPendingToProcessWhitelistId" parameterType="map" resultType="java.lang.Long">
        SELECT id
        FROM
        <include refid="tb" />
        WHERE `is_completed` = #{isCompleted}
        AND DATE(whitelist_date) &lt;= CURDATE()
    </select>
</mapper>