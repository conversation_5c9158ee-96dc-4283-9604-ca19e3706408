<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="Blacklist">
    <!-- Result mapping -->
    <resultMap id="BlacklistMap" type="com.eroses.external.society.model.blacklist.Blacklist">
        <id property="id" column="id"/>
        <result column="blacklist_user_id" property="blacklistUserId"/>
        <result column="name" property="name"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="society_cancellation_id" property="societyCancellationId"/>
        <result column="notice_reference_no" property="noticeReferenceNo"/>
        <result column="section" property="section"/>
        <result column="effective_date" property="effectiveDate"/>
        <result column="notes" property="notes"/>
        <result column="whitelist_date" property="whitelistDate"/>
        <result column="notice" property="notice"/>
        <result column="committee_update" property="committeeUpdate"/>
        <result column="ro" property="ro"/>
        <result column="assign_date" property="assignDate"/>
        <result column="notice_date" property="noticeDate"/>
        <result column="notice_by" property="noticeBy"/>
        <result column="is_completed" property="isCompleted"/>
        <result column="removal_status" property="removalStatus"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <resultMap id="BlacklistSelectMap" type="com.eroses.external.society.model.blacklist.Blacklist">
        <id property="id" column="bl_id"/>
        <result column="bl_blacklist_user_id" property="blacklistUserId"/>
        <result column="bl_name" property="name"/>
        <result column="bl_identification_no" property="identificationNo"/>
        <result column="bl_society_id" property="societyId"/>
        <result column="bl_society_no" property="societyNo"/>
        <result column="bl_branch_id" property="branchId"/>
        <result column="bl_branch_no" property="branchNo"/>
        <result column="bl_society_cancellation_id" property="societyCancellationId"/>
        <result column="bl_notice_reference_no" property="noticeReferenceNo"/>
        <result column="bl_section" property="section"/>
        <result column="bl_effective_date" property="effectiveDate"/>
        <result column="bl_notes" property="notes"/>
        <result column="bl_whitelist_date" property="whitelistDate"/>
        <result column="bl_notice" property="notice"/>
        <result column="bl_committee_update" property="committeeUpdate"/>
        <result column="bl_ro" property="ro"/>
        <result column="bl_assign_date" property="assignDate"/>
        <result column="bl_notice_date" property="noticeDate"/>
        <result column="bl_notice_by" property="noticeBy"/>
        <result column="bl_is_completed" property="isCompleted"/>
        <result column="bl_removal_status" property="removalStatus"/>
        <result column="bl_created_by" property="createdBy"/>
        <result column="bl_created_date" property="createdDate"/>
        <result column="bl_modified_by" property="modifiedBy"/>
        <result column="bl_modified_date" property="modifiedDate"/>

        <association property="society" resultMap="Society.SocietySelectMap"/>
        <association property="branch" resultMap="Branch.BranchSelectMap"/>
        <collection property="whitelists" resultMap="Whitelist.WhitelistSelectMap"/>
    </resultMap>

    <!-- SQL snippets -->
    <sql id="tb">
        `blacklist`
    </sql>

    <sql id="selectTb">
        `blacklist` bl
    </sql>

    <sql id="cols">
        `blacklist_user_id`,
        `name`,
        `identification_no`,
        `society_id`,
        `society_no`,
        `branch_id`,
        `branch_no`,
        `society_cancellation_id`,
        `notice_reference_no`,
        `section`,
        `effective_date`,
        `notes`,
        `whitelist_date`,
        `notice`,
        `committee_update`,
        `ro`,
        `assign_date`,
        `notice_date`,
        `notice_by`,
        `is_completed`,
        `removal_status`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="selectCols">
        bl.`id` as bl_id,
        bl.`blacklist_user_id` as bl_blacklist_user_id,
        bl.`name` as bl_name,
        bl.`identification_no` as bl_identification_no,
        bl.`society_id` as bl_society_id,
        bl.`society_no` as bl_society_no,
        bl.`branch_id` as bl_branch_id,
        bl.`branch_no` as bl_branch_no,
        bl.`society_cancellation_id` as bl_society_cancellation_id,
        bl.`notice_reference_no` as bl_notice_reference_no,
        bl.`section` as bl_section,
        bl.`effective_date` as bl_effective_date,
        bl.`notes` as bl_notes,
        bl.`whitelist_date` as bl_whitelist_date,
        bl.`notice` as bl_notice,
        bl.`committee_update` as bl_committee_update,
        bl.`ro` as bl_ro,
        bl.`assign_date` as bl_assign_date,
        bl.`notice_date` as bl_notice_date,
        bl.`notice_by` as bl_notice_by,
        bl.`is_completed` as bl_is_completed,
        bl.`removal_status` as bl_removal_status,
        bl.`created_by` as bl_created_by,
        bl.`created_date` as bl_created_date,
        bl.`modified_by` as bl_modified_by,
        bl.`modified_date` as bl_modified_date
    </sql>

    <sql id="insertCols">
        `blacklist_user_id`,
        `name`,
        `identification_no`,
        `society_id`,
        `society_no`,
        `branch_id`,
        `branch_no`,
        `society_cancellation_id`,
        `notice_reference_no`,
        `section`,
        `effective_date`,
        `notes`,
        `whitelist_date`,
        `notice`,
        `committee_update`,
        `ro`,
        `assign_date`,
        `notice_date`,
        `notice_by`,
        `is_completed`,
        `removal_status`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`
    </sql>

    <sql id="vals">
        #{blacklistUserId},
        #{name},
        #{identificationNo},
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{societyCancellationId},
        #{noticeReferenceNo},
        #{section},
        #{effectiveDate},
        #{notes},
        #{whitelistDate},
        #{notice},
        #{committeeUpdate},
        #{ro},
        #{assignDate},
        #{noticeDate},
        #{noticeBy},
        #{isCompleted},
        #{removalStatus},
        #{createdBy},
        NOW(),
        #{modifiedBy},
        NOW()
    </sql>

    <!-- Select operations -->
    <select id="findById" parameterType="java.lang.Long" resultMap="BlacklistMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="findByBlacklistUserId" parameterType="java.lang.Long" resultMap="BlacklistMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `blacklist_user_id` = #{blacklistUserId}
        ORDER BY `id` DESC
    </select>

    <select id="findBySocietyId" parameterType="java.lang.Integer" resultMap="BlacklistMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `society_id` = #{societyId}
        ORDER BY `id` DESC
    </select>

    <select id="findByIdentificationNo" parameterType="java.lang.String" resultMap="BlacklistMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `identification_no` = #{identificationNo}
        AND `removal_status` = 0
        LIMIT 1
    </select>

    <select id="search" parameterType="map" resultMap="BlacklistSelectMap">
        SELECT filtered_blacklist.*,
        <include refid="Whitelist.selectCols"/>
        FROM (
        SELECT <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>
        FROM <include refid="selectTb"/>
        LEFT JOIN <include refid="Society.selectTb"/> ON bl.society_id = s.id
        LEFT JOIN <include refid="Branch.selectTb"/> ON bl.branch_id = b.id
        <where>
            <if test="searchQuery != null and searchQuery != ''">
                AND (
                bl.`name` LIKE CONCAT('%', #{searchQuery}, '%')
                OR bl.`identification_no` LIKE CONCAT('%', #{searchQuery}, '%')
                OR s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
                OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
                OR b.`name` LIKE CONCAT('%', #{searchQuery}, '%')
                OR b.`branch_no` LIKE CONCAT('%', #{searchQuery}, '%')
                )
            </if>
            <if test="societyName != null and societyName != ''">
                AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
            </if>
            <if test="stateCode != null and stateCode != ''">
                AND (
                s.`state_id` = #{stateCode}
                OR b.`state_id` = #{stateCode}
                )
            </if>
            <if test="blacklistUserId != null">
                AND bl.`blacklist_user_id` = #{blacklistUserId}
            </if>
            <if test="name != null and name != ''">
                AND bl.`name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="identificationNo != null and identificationNo != ''">
                AND bl.`identification_no` LIKE CONCAT('%', #{identificationNo}, '%')
            </if>
            <if test="societyId != null">
                AND bl.`society_id` = #{societyId}
            </if>
            <if test="societyNo != null and societyNo != ''">
                AND bl.`society_no` LIKE CONCAT('%', #{societyNo}, '%')
            </if>
            <if test="branchId != null">
                AND bl.`branch_id` = #{branchId}
            </if>
            <if test="branchNo != null and branchNo != ''">
                AND bl.`branch_no` LIKE CONCAT('%', #{branchNo}, '%')
            </if>
            <if test="societyCancellationId != null">
                AND bl.`society_cancellation_id` = #{societyCancellationId}
            </if>
            <if test="noticeReferenceNo != null and noticeReferenceNo != ''">
                AND bl.`notice_reference_no` LIKE CONCAT('%', #{noticeReferenceNo}, '%')
            </if>
            <if test="section != null and section != ''">
                AND bl.`section` = #{section}
            </if>
            <if test="effectiveDateFrom != null">
                AND bl.`effective_date` &gt;= #{effectiveDateFrom}
            </if>
            <if test="effectiveDateTo != null">
                AND bl.`effective_date` &lt;= #{effectiveDateTo}
            </if>
            <if test="isCompleted != null">
                AND bl.`is_completed` = #{isCompleted}
            </if>
            <if test="removalStatus != null">
                AND bl.`removal_status` = #{removalStatus}
            </if>
        </where>
        ORDER BY bl.`created_date` DESC, bl.`id` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
        ) AS filtered_blacklist
        LEFT JOIN <include refid="Whitelist.selectTb"/> ON w.blacklist_id = filtered_blacklist.bl_id
        ORDER BY filtered_blacklist.bl_created_date DESC, filtered_blacklist.bl_id DESC
    </select>

    <select id="countSearch" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM <include refid="selectTb"/>
        LEFT JOIN <include refid="Society.selectTb"/> ON bl.society_id = s.id
        LEFT JOIN <include refid="Branch.selectTb"/> ON bl.branch_id = b.id
        <where>
            <if test="searchQuery != null and searchQuery != ''">
                AND (
                bl.`name` LIKE CONCAT('%', #{searchQuery}, '%')
                OR bl.`identification_no` LIKE CONCAT('%', #{searchQuery}, '%')
                OR s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
                OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
                OR b.`name` LIKE CONCAT('%', #{searchQuery}, '%')
                OR b.`branch_no` LIKE CONCAT('%', #{searchQuery}, '%')
                )
            </if>
            <if test="societyName != null and societyName != ''">
                AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
            </if>
            <if test="stateCode != null and stateCode != ''">
                AND (
                s.`state_id` = #{stateCode}
                OR b.`state_id` = #{stateCode}
                )
            </if>
            <if test="blacklistUserId != null">
                AND bl.`blacklist_user_id` = #{blacklistUserId}
            </if>
            <if test="name != null and name != ''">
                AND bl.`name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="identificationNo != null and identificationNo != ''">
                AND bl.`identification_no` LIKE CONCAT('%', #{identificationNo}, '%')
            </if>
            <if test="societyId != null">
                AND bl.`society_id` = #{societyId}
            </if>
            <if test="societyNo != null and societyNo != ''">
                AND bl.`society_no` LIKE CONCAT('%', #{societyNo}, '%')
            </if>
            <if test="branchId != null">
                AND bl.`branch_id` = #{branchId}
            </if>
            <if test="branchNo != null and branchNo != ''">
                AND bl.`branch_no` LIKE CONCAT('%', #{branchNo}, '%')
            </if>
            <if test="societyCancellationId != null">
                AND bl.`society_cancellation_id` = #{societyCancellationId}
            </if>
            <if test="noticeReferenceNo != null and noticeReferenceNo != ''">
                AND bl.`notice_reference_no` LIKE CONCAT('%', #{noticeReferenceNo}, '%')
            </if>
            <if test="section != null and section != ''">
                AND bl.`section` = #{section}
            </if>
            <if test="effectiveDateFrom != null">
                AND bl.`effective_date` &gt;= #{effectiveDateFrom}
            </if>
            <if test="effectiveDateTo != null">
                AND bl.`effective_date` &lt;= #{effectiveDateTo}
            </if>
            <if test="isCompleted != null">
                AND bl.`is_completed` = #{isCompleted}
            </if>
            <if test="removalStatus != null">
                AND bl.`removal_status` = #{removalStatus}
            </if>
        </where>
    </select>

    <select id="getByCriteria" parameterType="map" resultMap="BlacklistMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        <where>
            <if test="blacklistUserId != null">
                AND `blacklist_user_id` = #{blacklistUserId}
            </if>
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="identificationNo != null and identificationNo != ''">
                AND `identification_no` = #{identificationNo}
            </if>
            <if test="societyId != null">
                AND `society_id` = #{societyId}
            </if>
            <if test="societyNo != null and societyNo != ''">
                AND `society_no` = #{societyNo}
            </if>
            <if test="branchId != null">
                AND `branch_id` = #{branchId}
            </if>
            <if test="branchNo != null and branchNo != ''">
                AND `branch_no` = #{branchNo}
            </if>
            <if test="societyCancellationId != null">
                AND `society_cancellation_id` = #{societyCancellationId}
            </if>
            <if test="noticeReferenceNo != null and noticeReferenceNo != ''">
                AND `notice_reference_no` = #{noticeReferenceNo}
            </if>
            <if test="section != null and section != ''">
                AND `section` = #{section}
            </if>
            <if test="effectiveDateFrom != null">
                AND `effective_date` &gt;= #{effectiveDateFrom}
            </if>
            <if test="effectiveDateTo != null">
                AND `effective_date` &lt;= #{effectiveDateTo}
            </if>
            <if test="isCompleted != null">
                AND `is_completed` = #{isCompleted}
            </if>
            <if test="removalStatus != null">
                AND `removal_status` = #{removalStatus}
            </if>
        </where>
        ORDER BY `id` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countGetByCriteria" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM <include refid="tb"/>
        <where>
            <if test="blacklistUserId != null">
                AND `blacklist_user_id` = #{blacklistUserId}
            </if>
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="identificationNo != null and identificationNo != ''">
                AND `identification_no` = #{identificationNo}
            </if>
            <if test="societyId != null">
                AND `society_id` = #{societyId}
            </if>
            <if test="societyNo != null and societyNo != ''">
                AND `society_no` = #{societyNo}
            </if>
            <if test="branchId != null">
                AND `branch_id` = #{branchId}
            </if>
            <if test="branchNo != null and branchNo != ''">
                AND `branch_no` = #{branchNo}
            </if>
            <if test="societyCancellationId != null">
                AND `society_cancellation_id` = #{societyCancellationId}
            </if>
            <if test="noticeReferenceNo != null and noticeReferenceNo != ''">
                AND `notice_reference_no` = #{noticeReferenceNo}
            </if>
            <if test="section != null and section != ''">
                AND `section` = #{section}
            </if>
            <if test="effectiveDateFrom != null">
                AND `effective_date` &gt;= #{effectiveDateFrom}
            </if>
            <if test="effectiveDateTo != null">
                AND `effective_date` &lt;= #{effectiveDateTo}
            </if>
            <if test="isCompleted != null">
                AND `is_completed` = #{isCompleted}
            </if>
            <if test="removalStatus != null">
                AND `removal_status` = #{removalStatus}
            </if>
        </where>
    </select>

    <!-- Insert operation -->
    <insert id="create" parameterType="com.eroses.external.society.model.blacklist.Blacklist" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
        (<include refid="insertCols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <!-- Update operations -->
    <update id="update" parameterType="com.eroses.external.society.model.blacklist.Blacklist">
        UPDATE <include refid="tb"/>
        <set>
            <if test="blacklistUserId != null">`blacklist_user_id` = #{blacklistUserId},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="identificationNo != null">`identification_no` = #{identificationNo},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="societyCancellationId != null">`society_cancellation_id` = #{societyCancellationId},</if>
            <if test="noticeReferenceNo != null">`notice_reference_no` = #{noticeReferenceNo},</if>
            <if test="section != null">`section` = #{section},</if>
            <if test="effectiveDate != null">`effective_date` = #{effectiveDate},</if>
            <if test="notes != null">`notes` = #{notes},</if>
            <if test="whitelistDate != null">`whitelist_date` = #{whitelistDate},</if>
            <if test="notice != null">`notice` = #{notice},</if>
            <if test="committeeUpdate != null">`committee_update` = #{committeeUpdate},</if>
            <if test="ro != null">`ro` = #{ro},</if>
            <if test="assignDate != null">`assign_date` = #{assignDate},</if>
            <if test="noticeDate != null">`notice_date` = #{noticeDate},</if>
            <if test="noticeBy != null">`notice_by` = #{noticeBy},</if>
            <if test="isCompleted != null">`is_completed` = #{isCompleted},</if>
            <if test="removalStatus != null">`removal_status` = #{removalStatus},</if>
            `modified_date` = NOW(),
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <update id="updateRemovalStatus" parameterType="map">
        UPDATE <include refid="tb"/>
        SET `removal_status` = #{removalStatus},
            `modified_date` = NOW()
        WHERE `id` = #{id}
    </update>

    <!-- Delete operation -->
    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM <include refid="tb"/>
        WHERE `id` = #{id}
    </delete>

    <select id="findBySocietyCancellationIdAndIsCompletedAndRemovalStatus" parameterType="map" resultMap="BlacklistMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `society_cancellation_id` = #{societyCancellationId}
        AND `is_completed` = #{isCompleted}
        AND `removal_status` = #{removalStatus}
    </select>

    <select id="findByIdentificationNoAndIsCompletedAndRemovalStatus" parameterType="map" resultMap="BlacklistMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `identification_no` = #{identificationNo}
        AND `is_completed` = #{isCompleted}
        AND `removal_status` = #{removalStatus}
    </select>

    <select id="findByIdentificationNoAndIsCompletedAndRemovalStatusExcludingId" parameterType="map" resultMap="BlacklistMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `identification_no` = #{identificationNo}
        AND `is_completed` = #{isCompleted}
        AND `removal_status` = #{removalStatus}
        AND `id` != #{id}
    </select>

    <select id="getAllPendingToProcessBlacklistId" parameterType="map" resultType="java.lang.Long">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `is_completed` = #{isCompleted}
        AND `removal_status` = #{removalStatus}
        AND DATE(effective_date) &lt;= CURDATE()
    </select>

    <update id="updateRemovalStatusByIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            removal_status = #{removalStatus}
        </set>
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>