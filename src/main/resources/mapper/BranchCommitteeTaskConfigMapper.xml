<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="BranchCommitteeTaskConfig">
    <resultMap id="BranchCommitteeTaskConfigMap" type="com.eroses.external.society.model.BranchCommitteeTaskConfig">
        <result column="id" property="id"/>
        <result column="branch_id" property="branchId"/>
        <result column="module" property="module"/>
    </resultMap>
    <sql id="tb">`branch_committee_task_config`</sql>
    <sql id="col">`branch_id`,`module`</sql>
    <sql id="col_with_id">`id`, <include refid="col"/></sql>
    <insert id="insertCommitteeTaskForModule">
        INSERT INTO <include refid="tb"/> (<include refid="col"/>)
        VALUES (#{branchId}, #{module});
    </insert>
    <delete id="removeCommitteeTaskForModule">
        DELETE FROM <include refid="tb"/>
        <where>
            1=1 AND `branch_id`=#{branchId} AND `module`=#{module}
        </where>
    </delete>
    <select id="findCommitteeTaskForModule" resultMap="BranchCommitteeTaskConfigMap">
        SELECT <include refid="col_with_id"/> FROM <include refid="tb"/>
        <where>
            1=1 AND `branch_id`=#{branchId} AND `module`=#{module}
        </where>
    </select>
</mapper>
