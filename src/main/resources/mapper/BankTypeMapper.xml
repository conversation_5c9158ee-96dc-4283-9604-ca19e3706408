<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="BankType">
    <resultMap id="BankTypeResultMap" type="com.eroses.external.society.model.payment.BankType">
        <id property="id" column="id" />
        <result property="bankCode" column="bank_code" />
        <result property="bankName" column="bank_name" />
        <result property="createdDate" column="created_date" />
        <result property="createdBy" column="created_by" />
        <result property="status" column="status" />
        <result property="modifiedDate" column="modified_date" />
        <result property="modifiedBy" column="modified_by" />
    </resultMap>

    <sql id="tb">
        `bank_type`
    </sql>

    <sql id="cols">
        `id`,
        `bank_code`,
        `bank_name`,
        `created_date`,
        `created_by`,
        `status`,
        `modified_date`,
        `modified_by`
    </sql>

    <sql id="insertCols">
        `bank_code`,
        `bank_name`,
        `created_date`,
        `created_by`,
        `status`,
        `modified_date`,
        `modified_by`
    </sql>

    <sql id="vals">
        #{bankCode},
        #{bankName},
        #{createdDate},
        #{createdBy},
        #{status},
        #{modifiedDate},
        #{modifiedBy}
    </sql>

    <select id="findAll" resultMap="BankTypeResultMap">
        SELECT
        <include refid="cols" />
        FROM
        <include refid="tb" />
    </select>

    <select id="findByBankCode" parameterType="string" resultMap="BankTypeResultMap">
        SELECT
        <include refid="cols" />
        FROM
        <include refid="tb" />
        WHERE
        bank_code = #{bankCode}
    </select>

    <select id="findById" parameterType="long" resultMap="BankTypeResultMap">
        SELECT
        <include refid="cols" />
        FROM
        <include refid="tb" />
        WHERE
        id = #{id}
    </select>

    <insert id="save" parameterType="com.eroses.external.society.model.payment.BankType">
        INSERT INTO <include refid="tb" />
            (<include refid="insertCols" />)
        VALUES
            (<include refid="vals"/> )
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.payment.BankType">
        UPDATE
        <include refid="tb" />
        <set>
            <if test="bankCode != null"> bank_code = #{bankCode}, </if>
            <if test="bankName != null"> bank_name = #{bankName}, </if>
            <if test="createdDate != null"> created_date = #{createdDate}, </if>
            <if test="createdBy != null"> created_by = #{createdBy}, </if>
            <if test="status != null"> status = #{status}, </if>
            <if test="modifiedDate != null"> modified_date = #{modifiedDate}, </if>
            <if test="modifiedBy != null"> modified_by = #{modifiedBy}, </if>
        </set>
        WHERE id = #{id}
    </update>



</mapper>