<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="EventCertTemplateConfiguration">

    <resultMap id="EventCertTemplateConfigurationMap" type="com.eroses.external.society.model.EventCertTemplateConfiguration">
        <id property="id" column="id"/>
        <result property="templateCode" column="template_code"/>
        <result property="templateName" column="template_name"/>
        <result property="outputFileName" column="output_file_name"/>
        <result property="description" column="description"/>
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        event_cert_template_configuration
    </sql>

    <sql id="cols">
        event_id,
        feedback_question_id,
        created_by,
        modified_by
    </sql>

    <sql id="getCols">
        id,
        template_code,
        template_name,
        output_file_name,
        description,
        created_date,
        modified_by,
        created_by,
        modified_date
    </sql>

    <sql id="vals">
        #{templateCode},
        #{templateName},
        #{outputFileName},
        #{description},
        #{createdBy},
        #{modifiedBy}
    </sql>




<!--    <insert id="create" parameterType="java.util.List" keyProperty="id" useGeneratedKeys="true">-->
<!--        INSERT INTO <include refid="tb"/>-->
<!--        (<include refid="cols"/>, created_date)-->
<!--        VALUES-->
<!--        <foreach collection="list" item="item" separator=",">-->
<!--            (<include refid="createVals"/>, NOW())-->
<!--        </foreach>-->
<!--    </insert>-->

    <select id="findByTemplateCode"  resultMap="EventCertTemplateConfigurationMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        template_code = #{templateCode}
    </select>




</mapper>
