<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--<mapper namespace="com.eroses.external.society.mappers.ExtensionTimeDao">-->
<mapper namespace="ExtensionTime">
    <resultMap id="ExtensionTimeResultMap" type="com.eroses.external.society.model.ExtensionTime">
        <id property="id" column="id"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="application_date" property="applicationDate"/>
        <result column="decision" property="decision"/>
        <result column="extension_days" property="extensionDays"/>
        <result column="approved_extension_days" property="approvedExtensionDays"/>
        <result column="note" property="note"/>
        <result column="acknowledgement" property="acknowledgement"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="ro" property="ro"/>
    </resultMap>

    <resultMap id="ExtensionTimeSelectResultMap" type="com.eroses.external.society.model.ExtensionTime">
        <id column="et_id" property="id"/>
        <result column="et_society_id" property="societyId"/>
        <result column="et_society_no" property="societyNo"/>
        <result column="et_branch_id" property="branchId"/>
        <result column="et_branch_no" property="branchNo"/>
        <result column="et_application_date" property="applicationDate"/>
        <result column="et_decision" property="decision"/>
        <result column="et_extension_days" property="extensionDays"/>
        <result column="et_approved_extension_days" property="approvedExtensionDays"/>
        <result column="et_note" property="note"/>
        <result column="et_acknowledgement" property="acknowledgement"/>
        <result column="et_created_by" property="createdBy"/>
        <result column="et_created_date" property="createdDate"/>
        <result column="et_modified_by" property="modifiedBy"/>
        <result column="et_modified_date" property="modifiedDate"/>
        <result column="et_application_status_code" property="applicationStatusCode"/>
        <result column="et_ro" property="ro"/>

        <association property="society"
                     resultMap="Society.SocietySelectMap"/>
        <association property="branch"
                     resultMap="Branch.BranchSelectMap"/>
    </resultMap>

    <sql id="tb">
        `extension_time`
    </sql>

    <sql id="selectTb">
        `extension_time` et
    </sql>

    <sql id="selectCols">
        et.`id` as et_id,
        et.`society_id` as et_society_id,
        et.`society_no` as et_society_no,
        et.`branch_id` as et_branch_id,
        et.`branch_no` as et_branch_no,
        et.`application_date` as et_application_date,
        et.`decision` as et_decision,
        et.`extension_days` as et_extension_days,
        et.`approved_extension_days` as et_approved_extension_days,
        et.`note` as et_note,
        et.`acknowledgement` as et_acknowledgement,
        et.`created_by` as et_created_by,
        et.`created_date` as et_created_date,
        et.`modified_by` as et_modified_by,
        et.`modified_date` as et_modified_date,
        et.`application_status_code` as et_application_status_code,
        et.`ro` as et_ro
    </sql>

    <sql id="cols">
        `society_id`, `society_no`, `branch_id`, `branch_no`, `application_date`, `decision`, `extension_days`, `approved_extension_days`, `note`, `acknowledgement`,
        `created_by`, `created_date`, `modified_by`, `modified_date`, `application_status_code`, `ro`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <select id="findById" parameterType="java.lang.Long" resultMap="ExtensionTimeResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="findByBranchId" parameterType="java.lang.Long" resultMap="ExtensionTimeResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `branch_id` = #{branchId}
    </select>

    <select id="findByIdJoin" parameterType="java.lang.Long" resultMap="ExtensionTimeSelectResultMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>,
        <include refid="Branch.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON et.society_id = s.id
        INNER JOIN <include refid="Branch.selectTb"/> ON et.branch_id = b.id
        WHERE et.`id` = #{id}
        LIMIT 1
    </select>

    <insert id="create" parameterType="com.eroses.external.society.model.ExtensionTime" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{societyId}, #{societyNo}, #{branchId}, #{branchNo}, #{applicationDate}, #{decision}, #{extensionDays}, #{approvedExtensionDays}, #{note}, #{acknowledgement},
        #{createdBy}, now(), #{modifiedBy}, now(), #{applicationStatusCode}, #{ro})
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.ExtensionTime">
        UPDATE <include refid="tb"/>
        <set>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="applicationDate != null">`application_date` = #{applicationDate},</if>
            <if test="decision != null">`decision` = #{decision},</if>
            <if test="extensionDays != null">`extension_days` = #{extensionDays},</if>
            <if test="approvedExtensionDays != null">`approved_extension_days` = #{approvedExtensionDays},</if>
            <if test="note != null">`note` = #{note},</if>
            <if test="acknowledgement != null">`acknowledgement` = #{acknowledgement},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="ro != null">`ro` = #{ro},</if>
            modified_date = now(),
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="getAll" resultMap="ExtensionTimeResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
    </select>

    <select id="getAllPendingBranchExtensionTimeByCriteria" parameterType="map" resultMap="ExtensionTimeSelectResultMap">
        SELECT
        <include refid="selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Branch.selectTb"/> ON et.branch_id = b.id
        WHERE 1=1
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND et.application_status_code = #{applicationStatusCode}
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND b.state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND et.ro = #{ro}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            b.branch_no LIKE CONCAT('%', #{searchQuery}, '%')
            OR b.name LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        ORDER BY et.application_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllPendingBranchExtensionTimeByCriteria" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Branch.selectTb"/> ON et.branch_id = b.id
        WHERE 1=1
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND et.application_status_code = #{applicationStatusCode}
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND b.state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND et.ro = #{ro}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            b.branch_no LIKE CONCAT('%', #{searchQuery}, '%')
            OR b.name LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
    </select>

    <select id="findExisting" parameterType="map" resultMap="ExtensionTimeResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE
        `branch_id` = #{branchId}
        AND `application_status_code` = #{applicationStatusCode}
        ORDER BY created_date DESC
        LIMIT 1
    </select>

</mapper>
