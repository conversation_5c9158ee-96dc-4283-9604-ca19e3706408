<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="PostingReport">
    <sql id="tb">posting_report</sql>

    <resultMap id="PostingReviewMap" type="com.eroses.external.society.model.posting.Posting">
        <id property="id" column="id" />
        <result property="postingId" column="posting_id" />
        <result property="userId" column="user_id" />
        <result property="username" column="username" />
        <result property="reportReason" column="report_reason" />
        <result property="reportDate" column="report_date" />
        <result property="status" column="status" />
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date" />
    </resultMap>

    <sql id="cols">
        posting_id,
        user_id,
        username,
        report_reason,
        report_date,
        status,
        created_by,
        created_date
    </sql>

    <sql id="colsWithId">
        id,
        <include refid="cols"/>
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.posting.PostingReport" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
            posting_id,
            user_id,
            username,
            report_reason,
            report_date,
            status,
            created_by,
            created_date
        )
        VALUES (
            #{postingId},
            #{userId},
            #{username},
            #{reportReason},
            #{reportDate},
            #{status},
            #{createdBy},
            #{createdDate}
        )
    </insert>

    <!-- Update Status -->
    <update id="updateStatus">
        UPDATE
        <include refid="tb"/>
        SET
            status = #{status}
        WHERE
            id = #{id}
    </update>

    <!-- Find By ID -->
    <select id="findById" resultType="com.eroses.external.society.model.posting.PostingReport">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            id = #{id}
    </select>

    <!-- Find By Posting ID -->
    <select id="findByPostingId" resultType="com.eroses.external.society.model.posting.PostingReport">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            posting_id = #{postingId}
    </select>

    <!-- Find By User ID -->
    <select id="findByUserId" resultType="com.eroses.external.society.model.posting.PostingReport">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            user_id = #{userId}
    </select>

    <!-- Find By Status -->
    <select id="findByStatus" resultType="com.eroses.external.society.model.posting.PostingReport">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            status = #{status}
    </select>

    <!-- Find Reported Postings -->
    <select id="findReportedPostings" resultType="com.eroses.external.society.dto.response.posting.PostingReportResponse">
        SELECT
            pr.id,
            pr.posting_id as postingId,
            p.title as postingTitle,
            pr.user_id as userId,
            pr.username as userName,
            pr.report_reason as reportReason,
            pr.report_date as reportDate,
            pr.status
        FROM
        <include refid="tb"/> pr
        JOIN posting p ON pr.posting_id = p.id
        WHERE
            pr.status = 'PENDING'
        ORDER BY
            pr.report_date DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- Count Reported Postings -->
    <select id="countReportedPostings" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE
            status = 'PENDING'
    </select>

    <!-- Find By User ID And Posting ID And Date -->
    <select id="findByUserIdAndPostingIdAndDate" resultType="com.eroses.external.society.model.posting.PostingReport">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            user_id = #{userId}
            AND posting_id = #{postingId}
            AND report_date >= DATE_SUB(NOW(), INTERVAL 1 DAY)
    </select>
</mapper>
