<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ExternalAgencyReview">

    <resultMap id="ExternalAgencyReviewResultMap" type="com.eroses.external.society.model.ExternalAgencyReview">
        <id property="id" column="id"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="status" property="status"/>
        <result column="submission_date" property="submissionDate"/>
        <result column="submitted_by" property="submittedBy"/>
        <result column="note" property="note"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <sql id="tb">external_agency_review</sql>

    <sql id="cols">
        `society_id`, `society_no`, `status`, `submission_date`, `submitted_by`, `note`,
        `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <select id="findById" parameterType="java.lang.Long" resultMap="ExternalAgencyReviewResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE id = #{id}
        LIMIT 1
    </select>

    <select id="getAllBySocietyId" parameterType="map" resultMap="ExternalAgencyReviewResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE society_id = #{societyId}
        AND status = 1
        ORDER BY created_date DESC
    </select>

    <select id="getLatestBySocietyId" parameterType="java.lang.Long" resultMap="ExternalAgencyReviewResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE society_id = #{societyId}
        AND status = 0
        ORDER BY created_date DESC
        LIMIT 1
    </select>

    <select id="getAll" resultMap="ExternalAgencyReviewResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
    </select>

    <insert id="create" parameterType="com.eroses.external.society.model.ExternalAgencyReview" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{societyId}, #{societyNo}, #{status}, #{submissionDate}, #{submittedBy}, #{note}, #{createdBy}, now(), #{modifiedBy}, now())
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.ExternalAgencyReview">
        UPDATE <include refid="tb"/>
        <set>
            <if test="societyId != null">society_id = #{societyId},</if>
            <if test="societyNo != null">society_no = #{societyNo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="submissionDate != null">submission_date = #{submissionDate},</if>
            <if test="submittedBy != null">submitted_by = #{submittedBy},</if>
            <if test="note != null">note = #{note},</if>
            modified_date = now(),
            <if test="modifiedBy != null">modified_by = #{modifiedBy}</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM <include refid="tb"/>
        WHERE id = #{id}
    </delete>
</mapper>
