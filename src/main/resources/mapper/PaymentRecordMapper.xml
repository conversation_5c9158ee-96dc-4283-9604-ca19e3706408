<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="PaymentRecord">
    <resultMap id="PaymentResultMap" type="com.eroses.external.society.model.payment.PaymentRecord">
        <id column="id" property="id"/>
        <result column="payment_callback_id" property="paymentCallbackId"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="ic_no" property="icNo"/>
        <result column="email" property="email"/>
        <result column="society_id" property="societyId"/>
        <result column="society_name" property="societyName"/>
        <result column="society_no" property="societyNo"/>
        <result column="society_address" property="societyAddress"/>
        <result column="branch_id" property="branchId"/>
        <result column="appeal_id" property="appealId"/>
        <result column="amendment_id" property="amendmentId"/>
        <result column="amount" property="amount"/>
        <result column="payment_type" property="paymentType"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="payment_code" property="paymentCode"/>
        <result column="payment_status" property="paymentStatus"/>
        <result column="payment_date" property="paymentDate"/>
        <result column="officer_id" property="officerId"/>
        <result column="officer_name" property="officerName"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="doc_code" property="docCode"/>
        <result column="reference_no" property="referenceNo"/>
        <result column="receipt_no" property="receiptNo"/>
        <result column="state_id" property="stateCode"/>
        <result column="status" property="status"/>
        <result column="bank_code" property="bankCode"/>
        <result property="createdDate" column="created_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="modifiedDate" column="updated_date"/>
        <result property="modifiedBy" column="updated_by"/>
    </resultMap>



    <sql id="tb">
        `payment_record`
    </sql>

    <sql id="insertCols">
        `payment_callback_id`,
        `user_id`,
        `user_name`,
        `ic_no`,
        `email`,
        `society_id`,
        `society_name`,
        `society_no`,
        `society_address`,
        `branch_id`,
        `appeal_id`,
        `amount`,
        `payment_type`,
        `payment_method`,
        `payment_code`,
        `payment_status`,
        `payment_date`,
        `officer_id`,
        `officer_name`,
        `transaction_id`,
        `doc_code`,
        `reference_no`,
        `receipt_no`,
        `state_id`,
        `status`,
        `bank_code`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`
    </sql>

    <sql id="cols">
        `id`,
        <include refid="insertCols"/>
    </sql>

    <sql id="vals">
        #{paymentCallbackId},
        #{userId},
        #{userName},
        #{icNo},
        #{email},
        #{societyId},
        #{societyName},
        #{societyNo},
        #{societyAddress},
        #{branchId},
        #{appealId},
        #{amount},
        #{paymentType},
        #{paymentMethod},
        #{paymentCode},
        #{paymentStatus},
        #{paymentDate},
        #{officerId},
        #{officerName},
        #{transactionId},
        #{docCode},
        #{referenceNo},
        #{receiptNo},
        #{stateCode},
        #{status},
        #{bankCode},
        #{createdBy},
        NOW(),
        #{modifiedBy},
        NOW()
    </sql>


    <select id="getAllPaymentRecordsByCriteria" parameterType="map" resultMap="PaymentResultMap" >
        SELECT
            <include refid="cols"/>
        FROM
            <include refid="tb"/>
        WHERE
        1 = 1
        <!-- Ensure that this record is the latest for the same RefNo and DocCode -->
        AND `id` IN (
            SELECT MAX(`id`) <!-- Assuming `id` is unique; use created_date if no id -->
            FROM <include refid="tb"/>
            GROUP BY `reference_no`, `doc_code`
        )
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            LOWER(`society_name`) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
            OR LOWER(`ic_no`) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
            OR LOWER(`society_no`) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
            )
        </if>
        <if test="paymentMethod != null and paymentMethod != ''">
            AND `payment_method` = #{paymentMethod}
        </if>
        <if test="paymentType != null and paymentType != ''">
            AND `payment_type` = #{paymentType}
        </if>
        <if test="paymentStatus != null and paymentStatus != ''">
            AND `payment_status` = #{paymentStatus}
        </if>
        <if test="stateCode != null and stateCode != ''">
            AND `state_id` = #{stateCode}
        </if>
        ORDER BY `created_date` DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countGetAllPaymentRecordsByCriteria" resultType="Long" >
        SELECT
        count(*)
        FROM
        <include refid="tb"/>
        WHERE
        1 = 1
        <!-- Ensure that this record is the latest for the same RefNo and DocCode -->
        AND `id` IN (
        SELECT MAX(`id`) <!-- Assuming `id` is unique; use created_date if no id -->
        FROM <include refid="tb"/>
        GROUP BY `reference_no`, `doc_code`
        )
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            LOWER(`society_name`) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
            OR LOWER(`ic_no`) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
            OR LOWER(`society_no`) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
            )
        </if>
        <if test="paymentMethod != null and paymentMethod != ''">
            AND `payment_method` = #{paymentMethod}
        </if>
        <if test="paymentType != null and paymentType != ''">
            AND `payment_type` = #{paymentType}
        </if>
        <if test="paymentStatus != null and paymentStatus != ''">
            AND `payment_status` = #{paymentStatus}
        </if>
        <if test="stateCode != null and stateCode != ''">
            AND `state_id` = #{stateCode}
        </if>
    </select>

    <select id="getLatestPaymentRecordByDocCodeAndReferenceNo" parameterType="map" resultMap="PaymentResultMap">
        SELECT <include refid="cols"/>
        FROM <include refid="tb"/>
        WHERE id = (
            SELECT MAX(id)
            FROM <include refid="tb"/>
            WHERE 1=1
            <if test="docCode != null">AND `doc_code` = #{docCode} </if>
            <if test="referenceNo != null">AND `reference_no` = #{referenceNo}</if>
        )
    </select>

    <select id="selectById" resultMap="PaymentResultMap">
        SELECT <include refid="cols"/>
        FROM <include refid="tb"/>
        WHERE   `id` = #{id}
        LIMIT 1
    </select>

    <select id="count">
        SELECT count(*) from <include refid="tb"/>
    </select>

    <insert id="save" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
            (<include refid="insertCols"/>)
        VALUES
            (<include refid="vals"/>)
    </insert>

    <update id="update">
        UPDATE <include refid="tb"/>
        <set>
            <if test="paymentCallbackId != null and paymentCallbackId != 0">`payment_callback_id` = #{paymentCallbackId},</if>
            <if test="userId != null and userId != 0">`user_id` = #{userId},</if>
            <if test="userName != null">`user_name` = #{userName},</if>
            <if test="icNo != null">`ic_no` = #{icNo},</if>
            <if test="email != null">`email` = #{email},</if>
            <if test="societyId != null and societyId != 0">`society_id` = #{societyId},</if>
            <if test="societyName != null">`society_name` = #{societyName},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="societyAddress != null">`society_address` = #{societyAddress},</if>
            <if test="branchId != null and branchId != 0">`branch_id` = #{branchId},</if>
            <if test="appealId != null and appealId != 0">`appeal_id` = #{appealId},</if>
            <if test="amount != null and amount != 0.0">`amount` = #{amount},</if>
            <if test="paymentType != null">`payment_type` = #{paymentType},</if>
            <if test="paymentMethod != null">`payment_method` = #{paymentMethod},</if>
            <if test="paymentCode != null">`payment_code` = #{paymentCode},</if>
            <if test="paymentStatus != null">`payment_status` = #{paymentStatus},</if>
            <if test="paymentDate != null">`payment_date` = #{paymentDate},</if>
            <if test="officerId != null">`officer_id` = #{officerId},</if>
            <if test="officerName != null">`officer_name` = #{officerName},</if>
            <if test="transactionId != null">`transaction_id` = #{transactionId},</if>
            <if test="docCode != null">`doc_code` = #{docCode},</if>
            <if test="referenceNo != null">`reference_no` = #{referenceNo},</if>
            <if test="receiptNo != null">`receipt_no` = #{receiptNo},</if>
            <if test="stateCode != null">`state_id` = #{stateCode},</if>
            <if test="status != null and status != 0">`status` = #{status},</if>
            <if test="bankCode != null">`bank_code` = #{bankCode},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="modifiedDate != null">`modified_date` = NOW(),</if>
        </set>
        WHERE `id` = #{id}
    </update>

</mapper>