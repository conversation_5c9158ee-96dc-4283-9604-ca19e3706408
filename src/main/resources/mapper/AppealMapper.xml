<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Appeal">
    <resultMap id="AppealMap" type="com.eroses.external.society.model.Appeal">
        <id property="id" column="id"/>
        <result column="appeal_no" property="appealNo"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="amendment_id" property="amendmentId"/>
        <result column="society_non_citizen_committee_id" property="societyNonCitizenCommitteeId"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="id_sebab" property="idSebab"/>
        <result column="sebab_lain" property="sebabLain"/>
        <result column="pdf_file" property="pdfFile"/>
        <result column="confession" property="confession"/>
        <result column="confession_date" property="confessionDate"/>
        <result column="payment_record_id" property="paymentRecordId"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="payment_date" property="paymentDate"/>
        <result column="receipt_no" property="receiptNo"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_reference_no" property="bankReferenceNo"/>
        <result column="receipt_status" property="receiptStatus"/>
        <result column="letter_reference_no" property="letterReferenceNo"/>
        <result column="ro" property="ro"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="id_epayment" property="idEpayment"/>
        <result column="pem_caw" property="pemCaw"/>
        <result column="appeal_date" property="appealDate"/>
        <result column="date_132" property="date132"/>
        <result column="date_142" property="date142"/>
        <result column="date_145" property="date145"/>
        <result column="reconcile_date" property="reconcileDate"/>
        <result column="sent_epp" property="sentEpp"/>
        <result column="email_id" property="emailId"/>
        <result column="approved_date" property="approvedDate"/>
        <result column="is_queried" property="isQueried"/>
        <result column="register_date" property="registerDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>
    
    <resultMap id="SearchAppealMap" type="com.eroses.external.society.dto.response.appeal.GetSearchAppealResponse">
        <id column="ap_id" property="id"/>
        <result column="ap_appeal_no" property="appealNo"/>
        <result column="ap_id_sebab" property="idSebab"/>
        <result column="ap_appeal_date" property="appealDate"/>
        <result column="ap_application_status_code" property="appealStatus"/>
        <result column="ap_approved_date" property="approvedDate"/>
        <result column="ap_created_by" property="createdBy"/>
        <result column="ap_created_date" property="createdDate"/>
        <result column="ap_modified_by" property="modifiedBy"/>
        <result column="ap_modified_date" property="modifiedDate"/>
        <result column="s_id" property="societyId"/>
        <result column="s_society_name" property="societyName"/>
        <result column="s_society_no" property="societyNo"/>
        <result column="s_application_no" property="societyApplicationNo"/>
        <result column="s_state_id" property="societyStateCode"/>
        <result column="s_category_code_jppm" property="categoryCodeJppm"/>
        <result column="s_sub_category_code" property="subCategoryCode"/>
        <result column="s_application_status_code" property="societyStatus"/>
    </resultMap>

    <resultMap id="PendingAppealMap" type="com.eroses.external.society.dto.response.roDecision.GetAllPendingAppealResponse">
        <id column="ap_id" property="id"/>
        <result column="ap_appeal_no" property="appealNo"/>
        <result column="ap_id_sebab" property="idSebab"/>
        <result column="ap_appeal_date" property="submitDate"/>
        <result column="ap_application_status_code" property="applicationStatusCode"/>
        <result column="ap_ro" property="roName"/>
        <result column="ap_approved_date" property="approvedDate"/>
        <result column="ap_created_by" property="createdBy"/>
        <result column="ap_created_date" property="createdDate"/>
        <result column="ap_modified_by" property="modifiedBy"/>
        <result column="ap_modified_date" property="modifiedDate"/>
        <result column="ap_is_queried" property="isQueried"/>
        <result column="s_id" property="societyId"/>
        <result column="s_application_no" property="societyApplicationNo"/>
        <result column="s_society_name" property="societyName"/>
        <result column="s_society_no" property="societyNo"/>
        <result column="s_category_code_jppm" property="categoryCodeJppm"/>
        <result column="s_sub_category_code" property="subCategoryCode"/>
        <result column="s_state_id" property="stateCode"/>
        <result column="s_application_status_code" property="societyApplicationStatusCode"/>
    </resultMap>

    <resultMap id="AppealByUserMap" type="com.eroses.external.society.dto.response.appeal.GetUserSocietiesForAppealResponse">
        <result column="s_id" property="societyId"/>
        <result column="s_society_name" property="societyName"/>
        <result column="s_society_no" property="societyNo"/>
        <result column="s_application_status_code" property="societyApplicationStatusCode"/>
        <result column="b_id" property="branchId"/>
        <result column="b_branch_name" property="branchName"/>
        <result column="b_application_status_code" property="branchApplicationStatusCode"/>
        <result column="a_id" property="amendmentId"/>
        <result column="sncc_id" property="nonCitizenId"/>
    </resultMap>

    <resultMap id="AppealByParamMap" type="com.eroses.external.society.dto.response.appeal.GetByParamAppealResponse">
        <id column="ap_id" property="id"/>
        <result column="ap_appeal_no" property="appealNo"/>
        <result column="ap_id_sebab" property="idSebab"/>
        <result column="ap_appeal_date" property="appealDate"/>
        <result column="ap_application_status_code" property="appealApplicationStatusCode"/>
        <result column="ap_approved_date" property="approvedDate"/>
        <result column="ap_amendment_id" property="amendmentId"/>
        <result column="s_id" property="societyId"/>
        <result column="s_society_name" property="societyName"/>
        <result column="s_society_no" property="societyNo"/>
        <result column="s_application_no" property="societyApplicationNo"/>
        <result column="s_state_id" property="societyStateCode"/>
        <result column="s_application_status_code" property="societyApplicationStatusCode"/>
        <result column="b_id" property="branchId"/>
        <result column="b_name" property="branchName"/>
        <result column="b_application_status_code" property="branchApplicationStatusCode"/>
        <result column="sncc_id" property="nonCitizenCommitteeId"/>
        <result column="sncc_name" property="nonCitizenName"/>
        <result column="sncc_application_status_code" property="nonCitizenApplicationStatusCode"/>
    </resultMap>

    <resultMap id="DecisionRecordAppeal" type="com.eroses.external.society.dto.response.appeal.GetAllDecisionRecordAppealResponse">
        <id column="ap_id" property="id"/>
        <result column="s_society_name" property="societyName"/>
        <result column="s_society_no" property="societyNo"/>
        <result column="ap_id_sebab" property="idSebab"/>
        <result column="ap_application_status_code" property="applicationStatusCode"/>
        <result column="s_state_id" property="stateCode"/>
    </resultMap>

    <resultMap id="AppealAdminRecordsMap" type="com.eroses.external.society.dto.response.appeal.AppealAdminRecordsResponse">
        <id column="ap_id" property="id"/>
        <result column="ap_id_sebab" property="idSebab"/>
        <result column="ap_application_status_code" property="applicationStatusCode"/>
        <result column="s_id" property="societyId"/>
        <result column="s_society_no" property="societyNo"/>
        <result column="s_society_name" property="societyName"/>
        <result column="s_state_id" property="stateCode"/>
        <result column="ap_created_by" property="createdBy"/>
        <result column="ap_created_date" property="createdDate"/>
        <result column="ap_modified_by" property="modifiedBy"/>
        <result column="ap_modified_date" property="modifiedDate"/>
    </resultMap>

    <resultMap id="AppealAdminRecordByIdMap" type="com.eroses.external.society.dto.response.appeal.AppealAdminRecordByIdResponse">
        <id column="ap_id" property="id"/>
        <result column="ap_id_sebab" property="idSebab"/>
        <result column="ap_application_status_code" property="applicationStatusCode"/>
        <result column="ap_ppp_name" property="pppName"/>
        <result column="ap_kpp_name" property="kppName"/>
        <result column="ap_registrar_name" property="registrarName"/>
        <result column="s_society_name" property="societyName"/>
        <result column="s_society_no" property="societyNo"/>
        <result column="sc_secretary_name" property="secretaryName"/>
        <result column="sc_secretary_identification_no" property="secretaryIdentificationNo"/>
        <result column="approved_by" property="approvedBy"/>
        <result column="decision" property="decision"/>
        <result column="decision_date" property="decisionDate"/>
    </resultMap>

    <sql id="tb">
        `appeal`
    </sql>

    <sql id="selectTb">
        `appeal` ap
    </sql>

    <sql id="cols">
        `appeal_no`, `society_id`, `society_no`, `branch_id`, `branch_no`, `amendment_id`, `identification_no`, `id_sebab`,
        `sebab_lain`, `pdf_file`, `confession`, `confession_date`, `payment_record_id`, `payment_method`, `payment_date`,
        `receipt_no`, `bank_name`, `bank_reference_no`, `receipt_status`, `letter_reference_no`, `ro`, `application_status_code`, `id_epayment`,
        `pem_caw`, `appeal_date`, `date_132`, `date_142`, `date_145`, `reconcile_date`, `sent_epp`, `email_id`, `is_queried`,
        `approved_date`, `register_date`, `created_by`, `created_date`, `modified_by`, `modified_date`, `society_non_citizen_committee_id`
    </sql>

    <sql id="userAppealCols">
        ap.`id` AS ap_id,
        ap.`appeal_no` AS ap_appeal_no,
        ap.`id_sebab` AS ap_id_sebab,
        ap.`sebab_lain` AS ap_sebab_lain,
        ap.`appeal_date` AS ap_appeal_date,
        ap.`application_status_code` AS ap_application_status_code,
        ap.`approved_date` AS ap_approved_date,
        s.`id` AS s_id,
        s.`society_name` AS s_society_name,
        s.`society_no` AS s_society_no,
        s.`state_id` AS s_state_id,
        s.`application_status_code` AS s_application_status_code
    </sql>

    <sql id="societyAppealCols">
        s.`id` AS s_id,
        s.`society_name` AS s_society_name,
        s.`society_no` AS s_society_no,
        s.`application_status_code` AS s_application_status_code,
        a.`id` AS a_id,
        a.`application_status_code` AS amendment_application_status_code,
        sncc.`id` AS sncc_id,
        sncc.`application_status_code` AS sncc_application_status_code
    </sql>

    <sql id="branchAppealCols">
        s.`id` AS s_id,
        s.`society_name` AS s_society_name,
        s.`society_no` AS s_society_no,
        s.`application_status_code` AS s_application_status_code,
        b.`id` AS b_id,
        b.`name` AS b_branch_name,
        b.`application_status_code` AS b_application_status_code,
        b.`appeal_status` AS b_appeal_status
    </sql>

    <sql id="searchCols">
        ap.`id` AS ap_id,
        ap.`appeal_no` AS ap_appeal_no,
        ap.`id_sebab` AS ap_id_sebab,
        ap.`appeal_date` AS ap_appeal_date,
        ap.`application_status_code` AS ap_application_status_code,
        ap.`ro` AS ap_ro,
        ap.`is_queried` AS ap_is_queried,
        ap.`approved_date` AS ap_approved_date,
        ap.`created_by` AS ap_created_by,
        ap.`created_date` AS ap_created_date,
        ap.`modified_by` AS ap_modified_by,
        ap.`modified_date` AS ap_modified_date,
        s.`id` AS s_id,
        s.`society_name` AS s_society_name,
        s.`society_no` AS s_society_no,
        s.`application_status_code` AS s_application_status_code,
        s.`application_no` AS s_application_no,
        s.`state_id` AS s_state_id,
        s.`category_code_jppm` AS s_category_code_jppm,
        s.`sub_category_code` AS s_sub_category_code
    </sql>

    <sql id="appealByParamCols">
        ap.`id` AS ap_id,
        ap.`appeal_no` AS ap_appeal_no,
        ap.`id_sebab` AS ap_id_sebab,
        ap.`appeal_date` AS ap_appeal_date,
        ap.`application_status_code` AS ap_application_status_code,
        ap.`approved_date` AS ap_approved_date,
        ap.`amendment_id` AS ap_amendment_id,
        s.`id` AS s_id,
        s.`society_name` AS s_society_name,
        s.`society_no` AS s_society_no,
        s.`application_no` AS s_application_no,
        s.`state_id` AS s_state_id,
        s.`application_status_code` AS s_application_status_code,
        b.`id` AS b_id,
        b.`branch_no` as b_branch_no,
        b.`name` AS b_branch_name,
        b.`application_status_code` AS b_application_status_code,
        sncc.`id` AS sncc_id,
        sncc.`name` AS sncc_name,
        sncc.`application_status_code` AS sncc_application_status_code,
        ap.`created_date` AS ap_created_date
    </sql>

    <!-- SQL Fragment: Society-only Appeal Columns -->
    <sql id="appealByParamSocietyCols">
        ap.id AS ap_id,
        ap.appeal_no AS ap_appeal_no,
        ap.id_sebab AS ap_id_sebab,
        ap.appeal_date AS ap_appeal_date,
        ap.application_status_code AS ap_application_status_code,
        ap.approved_date AS ap_approved_date,
        ap.amendment_id AS ap_amendment_id,
        s.id AS s_id,
        s.society_name AS s_society_name,
        s.society_no AS s_society_no,
        s.application_no AS s_application_no,
        s.state_id AS s_state_id,
        s.application_status_code AS s_application_status_code,
        NULL AS b_id,
        NULL AS b_name,
        NULL AS b_application_status_code,
        NULL AS sncc_id,
        NULL AS sncc_name,
        NULL AS sncc_application_status_code,
        ap.created_date AS ap_created_date
    </sql>

    <!-- SQL Fragment: Branch Appeal Columns -->
    <sql id="appealByParamBranchCols">
        ap.id AS ap_id,
        ap.appeal_no AS ap_appeal_no,
        ap.id_sebab AS ap_id_sebab,
        ap.appeal_date AS ap_appeal_date,
        ap.application_status_code AS ap_application_status_code,
        ap.approved_date AS ap_approved_date,
        ap.amendment_id AS ap_amendment_id,
        s.id AS s_id,
        s.society_name AS s_society_name,
        s.society_no AS s_society_no,
        s.application_no AS s_application_no,
        s.state_id AS s_state_id,
        s.application_status_code AS s_application_status_code,
        b.id AS b_id,
        b.name AS b_name,
        b.application_status_code AS b_application_status_code,
        NULL AS sncc_id,
        NULL AS sncc_name,
        NULL AS sncc_application_status_code,
        ap.created_date AS ap_created_date
    </sql>

    <!-- SQL Fragment: Non-Citizen Committee Appeal Columns -->
    <sql id="appealByParamNonCitizenCols">
        ap.id AS ap_id,
        ap.appeal_no AS ap_appeal_no,
        ap.id_sebab AS ap_id_sebab,
        ap.appeal_date AS ap_appeal_date,
        ap.application_status_code AS ap_application_status_code,
        ap.approved_date AS ap_approved_date,
        ap.amendment_id AS ap_amendment_id,
        s.id AS s_id,
        s.society_name AS s_society_name,
        s.society_no AS s_society_no,
        s.application_no AS s_application_no,
        s.state_id AS s_state_id,
        s.application_status_code AS s_application_status_code,
        b.id AS b_id,
        b.name AS b_name,
        b.application_status_code AS b_application_status_code,
        sncc.id AS sncc_id,
        sncc.name AS sncc_name,
        sncc.application_status_code AS sncc_application_status_code,
        ap.created_date AS ap_created_date
    </sql>

    <sql id="appealAdminRecordCols">
        ap.`id` AS ap_id,
        ap.`id_sebab` AS ap_id_sebab,
        ap.`application_status_code` AS ap_application_status_code,
        s.`id` AS s_id,
        s.`society_no` AS s_society_no,
        s.`society_name` AS s_society_name,
        s.`state_id` AS s_state_id,
        ap.`created_by` AS ap_created_by,
        ap.`created_date` AS ap_created_date,
        ap.`modified_by` AS ap_modified_by,
        ap.`modified_date` AS ap_modified_date
    </sql>

    <sql id="AppealAdminRecordByIdCols">
        ap.`id` AS ap_id,
        ap.`id_sebab` AS ap_id_sebab,
        ap.`application_status_code` AS ap_application_status_code,
<!--        ap.`ppp_name` AS ap_ppp_name,-->
<!--        ap.`kpp_name` AS ap_kpp_name,-->
<!--        ap.`registrar_name` AS ap_registrar_name,-->
        s.`society_name` AS s_society_name,
        s.`society_no` AS s_society_no
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.Appeal" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{appealNo}, #{societyId}, #{societyNo}, #{branchId}, #{branchNo},
        #{amendmentId}, #{identificationNo}, #{idSebab}, #{sebabLain},
        #{pdfFile}, #{confession}, #{confessionDate}, #{paymentRecordId},
        #{paymentMethod}, #{paymentDate}, #{receiptNo}, #{bankName},
        #{bankReferenceNo}, #{receiptStatus}, #{ro}, #{applicationStatusCode},
        #{letterReferenceNo}, #{idEpayment}, #{pemCaw}, #{appealDate}, #{date132},
        #{date142}, #{date145}, #{reconcileDate}, #{sentEpp},
        #{emailId}, #{isQueried}, #{approvedDate}, #{registerDate}, #{createdBy}, NOW(), #{modifiedBy},
        NOW(), #{societyNonCitizenCommitteeId})
    </insert>

    <select id="getAll" parameterType="java.lang.Integer" resultMap="AppealMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countAll" resultType="java.lang.Long">
        SELECT COUNT(*)
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.Appeal">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="appealNo != null">appeal_no = #{appealNo},</if>
            <if test="societyId != null">society_id = #{societyId},</if>
            <if test="societyNo != null">society_no = #{societyNo},</if>
            <if test="branchId != null">branch_id = #{branchId},</if>
            <if test="branchNo != null">branch_no = #{branchNo},</if>
            <if test="amendmentId != null">amendment_id = #{amendmentId},</if>
            <if test="identificationNo != null">identification_no = #{identificationNo},</if>
            <if test="idSebab != null">id_sebab = #{idSebab},</if>
            <if test="sebabLain != null">sebab_lain = #{sebabLain},</if>
            <if test="pdfFile != null">pdf_file = #{pdfFile},</if>
            <if test="confession != null">confession = #{confession},</if>
            <if test="confessionDate != null">confession_date = #{confessionDate},</if>
            <if test="paymentRecordId != null">payment_record_id = #{paymentRecordId},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="paymentDate != null">payment_date = #{paymentDate},</if>
            <if test="receiptNo != null">receipt_no = #{receiptNo},</if>
            <if test="bankName != null">bank_name = #{bankName},</if>
            <if test="bankReferenceNo != null">bank_reference_no = #{bankReferenceNo},</if>
            <if test="receiptStatus != null">receipt_status = #{receiptStatus},</if>
            <if test="letterReferenceNo != null">letter_reference_no = #{letterReferenceNo},</if>
            <if test="ro != null">ro = #{ro},</if>
            <if test="applicationStatusCode != null">application_status_code = #{applicationStatusCode},</if>
            <if test="idEpayment != null">id_epayment = #{idEpayment},</if>
            <if test="pemCaw != null">pem_caw = #{pemCaw},</if>
            <if test="appealDate != null">appeal_date = #{appealDate},</if>
            <if test="date132 != null">date_132 = #{date132},</if>
            <if test="date142 != null">date_142 = #{date142},</if>
            <if test="date145 != null">date_145 = #{date145},</if>
            <if test="reconcileDate != null">reconcile_date = #{reconcileDate},</if>
            <if test="sentEpp != null">sent_epp = #{sentEpp},</if>
            <if test="emailId != null">email_id = #{emailId},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            <if test="isQueried != null">is_queried = #{isQueried},</if>
            <if test="approvedDate != null">approved_date = #{approvedDate},</if>
            <if test="registerDate != null">register_Date = #{registerDate},</if>
            <if test="societyNonCitizenCommitteeId != null">society_non_citizen_committee_id = #{societyNonCitizenCommitteeId},</if>
            modified_date = NOW()
        </set>
        WHERE
        `id` = #{id}
    </update>

    <select id="getById" parameterType="java.lang.Long" resultMap="AppealMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `id` = #{id}
    </select>

    <select id="getAllByParams" parameterType="map" resultMap="AppealAdminRecordsMap">
        SELECT <include refid="appealAdminRecordCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.`id` = ap.`society_id`
        <where>
            <if test="searchQuery != null and searchQuery != ''">
                AND (
                s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
                OR s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
                )
            </if>
            <if test="state != null">
                AND s.`state_id` = #{state}
            </if>
        </where>
        ORDER BY ap_created_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllByParams" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.`id` = ap.`society_id`
        <where>
            <if test="searchQuery != null and searchQuery != ''">
                AND (
                s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
                OR s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
                )
            </if>
            <if test="state != null">
                AND s.`state_id` = #{state}
            </if>
        </where>
    </select>

    <select id="getByPaymentId" parameterType="java.lang.Long" resultMap="AppealMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `payment_record_id` = #{paymentId}
    </select>

    <select id="getCountByType" resultType="com.eroses.external.society.dto.response.AppealCountByType">
        SELECT
        `id_sebab` AS idSebab,
        COUNT(*) AS count
        FROM
        <include refid="tb"/>
        GROUP BY
        `id_sebab`
        ORDER BY
        count DESC;
    </select>

    <select id="getAppealByParam" parameterType="map" resultMap="AppealByParamMap">
        SELECT
        <include refid="appealByParamCols"/>
        FROM <include refid="selectTb"/>
        LEFT JOIN <include refid="Society.selectTb"/>
        ON s.`id` = ap.`society_id`
        LEFT JOIN <include refid="Branch.selectTb"/>
        ON b.`id` = ap.`branch_id`
        <if test="isIndividualAppeal != null">
            LEFT JOIN <include refid="NonCitizenCommittee.selectTb"/>
            ON sncc.`id` = ap.`society_non_citizen_committee_id`
        </if>
        WHERE 1 = 1
        AND ap.`application_status_code` != -1
        <if test="societyIdList != null and societyIdList.size() > 0">
            AND s.`id` IN
            <foreach item="id" collection="societyIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND ap.`branch_id` IS NULL
        </if>
        <if test="branchIdList != null and branchIdList.size() > 0">
            AND b.`id` IN
            <foreach item="id" collection="branchIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND b.`application_status_code` != -1
        </if>
        <if test="isIndividualAppeal != null">
            <choose>
                <when test="isIndividualAppeal == true">
                    AND ap.`society_non_citizen_committee_id` IS NOT NULL
                </when>
                <otherwise>
                    AND ap.`society_non_citizen_committee_id` IS NULL
                </otherwise>
            </choose>
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND LOWER(s.`society_name`) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
        </if>
        <choose>
            <when test="appealApplicationStatus != null">
                    AND ap.`application_status_code` = #{appealApplicationStatus}
            </when>
            <otherwise>
                    AND ap.`application_status_code` != -1
            </otherwise>
        </choose>
        <if test="idSebab != null">
            AND ap.`id_sebab` = #{idSebab}
        </if>
        <if test="appealDate != null">
            AND DATE(ap.`appeal_date`) = #{appealDate}
        </if>
        <choose>
            <when test="societyApplicationStatus != null">
                AND s.`application_status_code` = #{societyApplicationStatus}
            </when>
            <otherwise>
                AND s.`application_status_code` != -1
            </otherwise>
        </choose>
        ORDER BY ap.`created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAppealByParams" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM <include refid="selectTb"/>
        LEFT JOIN <include refid="Society.selectTb"/>
        ON s.`id` = ap.`society_id`
        WHERE 1 = 1
        AND ap.`application_status_code` != -1
        <trim prefix="AND (" suffix=")" suffixOverrides="OR">
            <if test="createdBy != null">
                ap.`created_by` = #{createdBy} OR
            </if>
            <if test="societyIdList != null and societyIdList.size() > 0">
                s.`id` IN
                <foreach item="id" collection="societyIdList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </trim>
        <if test="isIndividualAppeal != null and isIndividualAppeal == true">
            AND ap.`society_non_citizen_committee_id` IS NOT NULL
        </if>
        <if test="isIndividualAppeal != null and isIndividualAppeal == false">
            AND ap.`society_non_citizen_committee_id` IS NULL
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND LOWER(s.`society_name`) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
        </if>
        <if test="appealApplicationStatus != null">
            AND ap.`application_status_code` = #{appealApplicationStatus}
        </if>
        <if test="idSebab != null">
            AND ap.`id_sebab` = #{idSebab}
        </if>
        <if test="appealDate != null">
            AND DATE(ap.`appeal_date`) = #{appealDate}
        </if>
        <if test="societyApplicationStatus != null">
            AND s.`application_status_code` = #{societyApplicationStatus}
        </if>
    </select>

    <select id="searchByName" parameterType="map" resultMap="SearchAppealMap">
        SELECT
        <include refid="searchCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/>
        ON s.`id` = ap.`society_id`
        WHERE 1=1
        <if test="request.searchQuery != null and request.searchQuery != ''">
            AND LOWER(s.`society_name`) LIKE CONCAT('%', LOWER(#{request.searchQuery}), '%')
        </if>
        <if test="request.id != null">
            AND ap.`id` = #{request.id}
        </if>
        <if test="request.idSebab != null">
            AND ap.`id_sebab` = #{request.idSebab}
        </if>
        <if test="request.societyId != null">
            AND ap.`society_id` = #{request.societyId}
        </if>
        <if test="request.branchId != null">
            AND ap.`branch_id` = #{request.branchId}
        </if>
        <if test="request.categoryCodeJppm != null">
            AND s.`category_code_jppm` = #{request.categoryCodeJppm}
        </if>
        <if test="request.subCategoryCode != null">
            AND s.`sub_category_code` = #{request.subCategoryCode}
        </if>
        <if test="request.userId != null">
            AND ap.`created_by` = #{request.userId}
        </if>
        <if test="request.identificationNo != null and request.identificationNo != ''">
            AND ap.`identification_no` = #{request.identificationNo}
        </if>
        ORDER BY ap.`created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countSearchAppeal" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/>
        ON s.`id` = ap.`society_id`
        WHERE 1=1
        <if test="searchQuery != null and searchQuery != ''">
            AND LOWER(s.`society_name`) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
        </if>
        <if test="id != null">
            AND ap.`id` = #{id}
        </if>
        <if test="idSebab != null">
            AND ap.`id_sebab` = #{idSebab}
        </if>
        <if test="societyId != null">
            AND ap.`society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND ap.`branch_id` = #{branchId}
        </if>
        <if test="categoryCodeJppm != null">
            AND s.`category_code_jppm` = #{categoryCodeJppm}
        </if>
        <if test="subCategoryCode != null">
            AND s.`sub_category_code` = #{subCategoryCode}
        </if>
        <if test="userId != null">
            AND s.`created_by` = #{userId}
        </if>
        <if test="identificationNo != null and identificationNo != ''">
            AND ap.`identification_no` = #{identificationNo}
        </if>
    </select>

    <select id="getAllPendingAppeal" parameterType="map" resultMap="PendingAppealMap">
        SELECT
        <include refid="searchCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.`id` = ap.`society_id`
        WHERE 1=1
        <if test="isQuery == 0"> <!-- Cater for NULL data -->
            AND (ap.`is_queried` IS NULL OR ap.`is_queried` = 0)
        </if>
        <if test="isQuery != null and isQuery != 0">
            AND ap.`is_queried` = #{isQuery}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND LOWER(s.`society_name`) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
        </if>
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND ap.`application_status_code` IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null and stateCodeQuery != ''">
            AND s.`state_id` = #{stateCode}
        </if>
        <if test="ro != null and ro != 0">
            AND ap.`ro` = #{ro}
        </if>
        <if test="categoryCodeJppm != null">
            AND s.`category_code_jppm` = #{categoryCodeJppm}
        </if>
        <if test="subCategoryCode != null">
            AND s.`sub_category_code` = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
        </if>
        <if test="idSebab != null">
            AND ap.`id_sebab` = #{idSebab}
        </if>
        ORDER BY ap.`created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllPendingAppeal" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.`id` = ap.`society_id`
        WHERE 1=1
        <if test="isQuery == 0"> <!-- Cater for NULL data -->
            AND (ap.`is_queried` IS NULL OR ap.`is_queried` = 0)
        </if>
        <if test="isQuery != null and isQuery != 0">
            AND ap.`is_queried` = #{isQuery}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND LOWER(s.`society_name`) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
        </if>
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND ap.`application_status_code` IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null and stateCode != ''">
            AND s.`state_id` = #{stateCode}
        </if>
        <if test="ro != null and ro != 0">
            AND ap.`ro` = #{ro}
        </if>
        <if test="categoryCodeJppm != null">
            AND s.`category_code_jppm` = #{categoryCodeJppm}
        </if>
        <if test="subCategoryCode != null">
            AND s.`sub_category_code` = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
        </if>
        <if test="idSebab != null">
            AND ap.`id_sebab` = #{idSebab}
        </if>
    </select>

    <select id="getAppealsByUser" parameterType="map" resultMap="AppealByUserMap">
        SELECT <include refid="userAppealCols"/>
        FROM <include refid="Society.selectTb"/>
        LEFT JOIN <include refid="Society.selectTb"/>
        ON ap.`society_id` = s.`id`
        WHERE 1=1
        <if test="searchQuery != null and searchQuery != ''">
            AND (s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
            OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%'))
        </if>
        <if test="filter != null and !filter.isEmpty()">
            ORDER BY
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="filter['filterAppealApplicationStatus'] != null and filter['filterAppealApplicationStatus'] == 1">
                    ap.`application_status_code` ASC,
                </if>
                <if test="filter['filterIdSebab'] != null and filter['filterIdSebab'] == 1">
                    ap.`id_sebab` ASC,
                </if>
                <if test="filter['filterAppealDate'] != null and filter['filterAppealDate'] == 1">
                    ap.`appeal_date` DESC,
                </if>
                <if test="filter['filterSocietyStatus'] != null and filter['filterSocietyStatus'] == 1">
                    s.`application_status_code` ASC,
                </if>
            </trim>
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAppealsByUser" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM <include refid="selectTb"/>
        LEFT JOIN <include refid="Society.selectTb"/>
        ON ap.`society_id` = s.`id`
        WHERE 1=1
        <if test="searchQuery != null and searchQuery != ''">
            AND (s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
            OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%'))
        </if>
        <if test="filter != null and !filter.isEmpty()">
            ORDER BY
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="filter['filterAppealApplicationStatus'] != null and filter['filterAppealApplicationStatus'] == 1">
                    ap.`application_status_code` ASC,
                </if>
                <if test="filter['filterIdSebab'] != null and filter['filterIdSebab'] == 1">
                    ap.`id_sebab` ASC,
                </if>
                <if test="filter['filterAppealDate'] != null and filter['filterAppealDate'] == 1">
                    ap.`appeal_date` DESC,
                </if>
                <if test="filter['filterSocietyStatus'] != null and filter['filterSocietyStatus'] == 1">
                    s.`application_status_code` ASC,
                </if>
            </trim>
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <delete id="removeAppealApplication" parameterType="java.lang.Long">
        DELETE FROM <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <select id="findByCriteria" parameterType="map" resultMap="AppealMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
    </select>

    <select id="getAllExpiredUnpaidSocietyAppealId" parameterType="map" resultType="java.lang.Long">
        SELECT id
        FROM
        <include refid="tb"/>
        WHERE
        (
            application_status_code = #{pendingCounterPaymentStatusCode}
            OR application_status_code = #{pendingOnlinePaymentStatusCode}
        )
        AND CURDATE() > DATE(DATE_ADD(register_date, INTERVAL #{paymentPeriodDays} DAY))
    </select>

    <update id="updateAppealStatusByAppealIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            application_status_code = #{applicationStatusCode}
        </set>
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getAllDecisionRecordAppeal" parameterType="map" resultMap="DecisionRecordAppeal">
        SELECT <include refid="userAppealCols"/>
        FROM <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/>
        ON s.`id` = ap.`society_id`
        WHERE 1=1
<!--        AND a.`application_status_code` IN (3, 4, 36)-->
        <if test="stateCode != null and stateCode != ''">
            AND s.`state_id` = #{stateCode}
        </if>
        <if test="decision != null">
            AND ap.`application_status_code` = #{decision}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
        </if>
        ORDER BY ap.`created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllDecisionRecordAppeal" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/>
        ON s.`id` = ap.`society_id`
        WHERE 1=1
        <!--        AND a.`application_status_code` IN (3, 4, 36)-->
        <if test="stateCode != null and stateCode != ''">
            AND s.`state_id` = #{stateCode}
        </if>
        <if test="decision != null">
            AND ap.`application_status_code` = #{decision}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
        </if>
    </select>

    <select id="getAdminRecord" parameterType="java.lang.Long" resultMap="AppealAdminRecordByIdMap">
        SELECT <include refid="AppealAdminRecordByIdCols"/>
        FROM <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/>
        ON s.`id` = ap.`society_id`
        WHERE ap.`id` = #{appealId}
    </select>

    <select id="getAppealsByParamEnhanced" parameterType="map" resultMap="AppealByParamMap">
        SELECT *
        FROM (
        <choose>
            <when test="isIndividualAppeal == false">
                <!-- Society-only appeals -->
                SELECT
                <include refid="appealByParamSocietyCols"/>
                FROM
                <include refid="selectTb"/>
                INNER JOIN
                <include refid="Society.selectTb"/> ON s.id = ap.society_id
                WHERE
                ap.application_status_code != -1
                AND ap.branch_id IS NULL
                AND ap.society_non_citizen_committee_id IS NULL
                <if test="societyIdList != null and societyIdList.size() > 0">
                    AND s.id IN
                    <foreach item="id" collection="societyIdList" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>

                <!-- Branch appeals (if branch list provided) -->
                <if test="branchIdList != null and branchIdList.size() > 0">
                    UNION ALL
                    SELECT
                    <include refid="appealByParamBranchCols"/>
                    FROM
                    <include refid="selectTb"/>
                    INNER JOIN
                    <include refid="Society.selectTb"/> ON s.id = ap.society_id
                    INNER JOIN
                    <include refid="Branch.selectTb"/> ON b.id = ap.branch_id
                    WHERE
                    ap.application_status_code != -1
                    AND ap.society_non_citizen_committee_id IS NULL
                    AND b.id IN
                    <foreach item="id" collection="branchIdList" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <!-- Non-citizen committee appeals -->
                SELECT
                <include refid="appealByParamNonCitizenCols"/>
                FROM
                <include refid="selectTb"/>
                INNER JOIN
                <include refid="Society.selectTb"/> ON s.id = ap.society_id
                LEFT JOIN
                <include refid="Branch.selectTb"/> ON b.id = ap.branch_id
                INNER JOIN
                <include refid="NonCitizenCommittee.selectTb"/> ON sncc.id = ap.society_non_citizen_committee_id
                WHERE
                ap.application_status_code != -1
                <if test="societyIdList != null and societyIdList.size() > 0">
                        AND s.id IN
                        <foreach item="id" collection="societyIdList" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                </if>
                <if test="branchIdList != null and branchIdList.size() > 0">
                        AND b.id IN
                        <foreach item="id" collection="branchIdList" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                </if>
            </otherwise>
        </choose>
        ) AS combined
        <where>
            <if test="searchQuery != null and searchQuery != ''">
                AND (
                LOWER(combined.s_society_name) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
                OR LOWER(combined.b_name) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
                OR LOWER(combined.sncc_name) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
                )
            </if>
            <if test="appealApplicationStatus != null">
                AND combined.ap_application_status_code = #{appealApplicationStatus}
            </if>
            <if test="idSebab != null">
                AND combined.ap_id_sebab = #{idSebab}
            </if>
            <if test="appealDate != null">
                AND DATE(combined.ap_appeal_date) = #{appealDate}
            </if>
            <if test="societyApplicationStatus != null">
                AND combined.s_application_status_code = #{societyApplicationStatus}
            </if>
        </where>
        ORDER BY combined.ap_created_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAppealsByParamEnhanced" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM (
        <choose>
            <when test="isIndividualAppeal == false">
                <!-- Society-only appeals -->
                SELECT
                <include refid="appealByParamSocietyCols"/>
                FROM
                <include refid="selectTb"/>
                INNER JOIN
                <include refid="Society.selectTb"/> ON s.id = ap.society_id
                WHERE
                ap.application_status_code != -1
                AND ap.branch_id IS NULL
                AND ap.society_non_citizen_committee_id IS NULL
                <if test="societyIdList != null and societyIdList.size() > 0">
                    AND s.id IN
                    <foreach item="id" collection="societyIdList" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>

                <!-- Branch appeals (if branch list provided) -->
                <if test="branchIdList != null and branchIdList.size() > 0">
                    UNION ALL
                    SELECT
                    <include refid="appealByParamBranchCols"/>
                    FROM
                    <include refid="selectTb"/>
                    INNER JOIN
                    <include refid="Society.selectTb"/> ON s.id = ap.society_id
                    INNER JOIN
                    <include refid="Branch.selectTb"/> ON b.id = ap.branch_id
                    WHERE
                    ap.application_status_code != -1
                    AND ap.society_non_citizen_committee_id IS NULL
                    AND b.id IN
                    <foreach item="id" collection="branchIdList" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <!-- Non-citizen committee appeals -->
                SELECT
                <include refid="appealByParamNonCitizenCols"/>
                FROM
                <include refid="selectTb"/>
                INNER JOIN
                <include refid="Society.selectTb"/> ON s.id = ap.society_id
                LEFT JOIN
                <include refid="Branch.selectTb"/> ON b.id = ap.branch_id
                INNER JOIN
                <include refid="NonCitizenCommittee.selectTb"/> ON sncc.id = ap.society_non_citizen_committee_id
                WHERE
                ap.application_status_code != -1
                <if test="societyIdList != null and societyIdList.size() > 0">
                    AND s.id IN
                    <foreach item="id" collection="societyIdList" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="branchIdList != null and branchIdList.size() > 0">
                    AND b.id IN
                    <foreach item="id" collection="branchIdList" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        ) AS combined
        <where>
            <if test="searchQuery != null and searchQuery != ''">
                AND (
                LOWER(combined.s_society_name) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
                OR LOWER(combined.b_name) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
                OR LOWER(combined.sncc_name) LIKE CONCAT('%', LOWER(#{searchQuery}), '%')
                )
            </if>
            <if test="appealApplicationStatus != null">
                AND combined.ap_application_status_code = #{appealApplicationStatus}
            </if>
            <if test="idSebab != null">
                AND combined.ap_id_sebab = #{idSebab}
            </if>
            <if test="appealDate != null">
                AND DATE(combined.ap_appeal_date) = #{appealDate}
            </if>
            <if test="societyApplicationStatus != null">
                AND combined.s_application_status_code = #{societyApplicationStatus}
            </if>
        </where>
    </select>

</mapper>