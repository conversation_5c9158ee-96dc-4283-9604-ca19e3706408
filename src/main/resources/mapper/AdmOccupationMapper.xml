<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AdmOccupation">
    <resultMap id="AdmOccupationResultMap" type="com.eroses.external.society.model.lookup.AdmOccupation">
        <id property="id" column="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="code" property="code"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <sql id="tb">
        `adm_occupation`
    </sql>

    <sql id="selectTb">
        `adm_occupation` ao
    </sql>

    <sql id="selectCols">
        ao.`id` as ao_id,
        ao.`name` as ao_name,
        ao.`description` as ao_description,
        ao.`code` as ao_code,
        ao.`status` as ao_status,
        ao.`created_by` as ao_created_by,
        ao.`created_date` as ao_created_date,
        ao.`modified_by` as ao_modified_by,
        ao.`modified_date` as ao_modified_date
    </sql>

    <sql id="cols">
        `name`, `description`, `code`, `status`, `is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <select id="findById" parameterType="java.lang.Long" resultMap="AdmOccupationResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="getAll" resultMap="AdmOccupationResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `is_deleted` = 0
        <if test="nameQuery != null and nameQuery != ''">
            AND `name` LIKE CONCAT('%', #{nameQuery}, '%')
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countAll" resultType="Long">
        SELECT COUNT(*)
        FROM <include refid="tb"/>
        WHERE `is_deleted` = 0
        <if test="nameQuery != null and nameQuery != ''">
            AND `name` LIKE CONCAT('%', #{nameQuery}, '%')
        </if>
    </select>

    <select id="getAllActive" resultMap="AdmOccupationResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `is_deleted` = 0
        AND `status` = 1
    </select>

    <insert id="create" parameterType="com.eroses.external.society.model.lookup.AdmOccupation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{name}, #{description}, #{code}, #{status}, 0, #{createdBy}, now(), #{modifiedBy}, now())
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.lookup.AdmOccupation">
        UPDATE <include refid="tb"/>
        <set>
            <if test="name != null">`name` = #{name},</if>
            <if test="description != null">`description` = #{description},</if>
            <if test="code != null">`code` = #{code},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="isDeleted != null">`is_deleted` = #{isDeleted},</if>
            modified_date = now(),
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <update id="delete" parameterType="java.lang.Long">
        UPDATE <include refid="tb"/>
        <set>
            `is_deleted` = 1
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="existsByCode" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM <include refid="tb"/>
            WHERE `code` = #{code}
            AND `is_deleted` = 0
        )
    </select>

    <select id="existsByCodeExcludingId" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM <include refid="tb"/>
            WHERE `code` = #{code}
            AND `id` != #{id}
            AND `is_deleted` = 0
        )
    </select>
</mapper>