<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="TrusteeHolder">
    <resultMap id="TrusteeHolderMap" type="com.eroses.external.society.model.TrusteeHolder">
        <!-- ID -->
        <result property="societyNo" column="society_no"/>
        <result property="societyId" column="society_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="branchId" column="branch_id"/>
        <result property="titleCode" column="title_code"/>
        <result property="name" column="name"/>
        <result property="gender" column="gender"/>
        <result property="citizenshipStatus" column="citizenship_status"/>
        <result property="identificationType" column="identification_type"/>
        <result property="identificationNo" column="identification_no"/>
        <result property="dateOfBirth" column="date_of_birth"/>
        <result property="placeOfBirth" column="place_of_birth"/>
        <result property="occupationCode" column="occupation_code"/>
        <result property="address" column="address"/>
        <result property="countryCode" column="country_code"/>
        <result property="stateCode" column="state_id"/>
        <result property="districtCode" column="district_id"/>
        <result property="subDistrictCode" column="sub_district_code"/>
        <result property="city" column="city"/>
        <result property="postalCode" column="postal_code"/>
        <result property="email" column="email"/>
        <result property="homePhoneNumber" column="home_phone_number"/>
        <result property="mobilePhoneNumber" column="mobile_phone_number"/>
        <result property="appointmentDate" column="appointment_date"/>
        <result property="status" column="status"/>
        <result property="branchTrustee" column="branch_trustee"/>
        <result property="visaExpirationDate" column="visa_expiration_date"/>
        <result property="permitExpirationDate" column="permit_expiration_date"/>
        <result property="visaNo" column="visa_no"/>
        <result property="permitNo" column="permit_no"/>
        <result property="officePhoneNumber" column="office_phone_number"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>
    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>
    <sql id="cols">
        `society_no`, `society_id`, `branch_no`, `branch_id`, `title_code`, `name`, `gender`, `citizenship_status`,
        `identification_type`, `identification_no`, `date_of_birth`, `place_of_birth`, `occupation_code`, `address`,
        `country_code`, `state_id`, `district_id`, `sub_district_code`, `city`, `postal_code`, `email`,`office_phone_number`,
        `home_phone_number`, `mobile_phone_number`, `appointment_date`, `created_by`, `modified_by`,`status`, `branch_trustee`
        ,`visa_expiration_date`, `permit_expiration_date`, `visa_no`,`permit_no`
    </sql>

    <sql id="tbl">`trustee_holder`</sql>

    <select id="findBySocietyId" parameterType="map" resultMap="TrusteeHolderMap" resultType="java.util.List">
        SELECT
        <include refid="colsWithId"/>
        FROM <include refid="tbl"/>
        WHERE `society_id` = #{societyId}
        <choose>
            <when test="branchId != null">
                AND `branch_id` = #{branchId}
            </when>
            <otherwise>
                AND `branch_id` IS NULL
            </otherwise>
        </choose>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countBySocietyId" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT COUNT(*) FROM <include refid="tbl"/>
        WHERE `society_id` = #{societyId}
        <choose>
            <when test="branchId != null">
                AND `branch_id` = #{branchId}
            </when>
            <otherwise>
                AND `branch_id` IS NULL
            </otherwise>
        </choose>
    </select>

    <select id="findById" parameterType="java.lang.Long" resultMap="TrusteeHolderMap">
        SELECT <include refid="colsWithId"/> FROM <include refid="tbl"/> WHERE `id` = #{id}
    </select>

    <insert id="create" parameterType="com.eroses.external.society.model.TrusteeHolder" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tbl"/>
        (
        <include refid="cols"/>
        ) VALUES (
        #{societyNo}, #{societyId}, #{branchNo}, #{branchId}, #{titleCode}, #{name}, #{gender}, #{citizenshipStatus},
        #{identificationType}, #{identificationNo}, #{dateOfBirth}, #{placeOfBirth}, #{occupationCode}, #{address},
        #{countryCode}, #{stateCode}, #{districtCode}, #{subDistrictCode}, #{city}, #{postalCode}, #{email},#{officePhoneNumber},
        #{homePhoneNumber}, #{mobilePhoneNumber}, #{appointmentDate}, #{createdBy},  #{modifiedBy}, #{status}, #{branchTrustee},
        #{visaExpirationDate} , #{permitExpirationDate} , #{visaNo} , #{permitNo}
        )
    </insert>
    <update id="update">
        UPDATE
        <include refid="tbl"/>
        <set>
            <if test="societyNo != null">
                `society_no` = #{societyNo},
            </if>
            <if test="societyId != null">
                `society_id` = #{societyId},
            </if>
            <if test="branchNo != null">
                `branch_no` = #{branchNo},
            </if>
            <if test="branchId != null">
                `branch_id` = #{branchId},
            </if>
            <if test="titleCode != null">
                `title_code` = #{titleCode},
            </if>
            <if test="name != null">
                `name` = #{name},
            </if>
            <if test="gender != null">
                `gender` = #{gender},
            </if>
            <if test="citizenshipStatus != null">
                `citizenship_status` = #{citizenshipStatus},
            </if>
            <if test="identificationType != null">
                `identification_type` = #{identificationType},
            </if>
            <if test="identificationNo != null">
                `identification_no` = #{identificationNo},
            </if>
            <if test="dateOfBirth != null">
                `date_of_birth` = #{dateOfBirth},
            </if>
            <if test="placeOfBirth != null">
                `place_of_birth` = #{placeOfBirth},
            </if>
            <if test="occupationCode != null">
                `occupation_code` = #{occupationCode},
            </if>
            <if test="address != null">
                `address` = #{address},
            </if>
            <if test="countryCode != null">
                `country_code` = #{countryCode},
            </if>
            <if test="stateCode != null">
                `state_id` = #{stateCode},
            </if>
            <if test="districtCode != null">
                `district_id` = #{districtCode},
            </if>
            <if test="subDistrictCode != null">
                `sub_district_code` = #{subDistrictCode},
            </if>
            <if test="city != null">
                `city` = #{city},
            </if>
            <if test="postalCode != null">
                `postal_code` = #{postalCode},
            </if>
            <if test="email != null">
                `email` = #{email},
            </if>
            <if test="officePhoneNumber != null">
                `office_phone_number` = #{officePhoneNumber},
            </if>

            <if test="homePhoneNumber != null">
                `home_phone_number` = #{homePhoneNumber},
            </if>
            <if test="mobilePhoneNumber != null">
                `mobile_phone_number` = #{mobilePhoneNumber},
            </if>
            <if test="appointmentDate != null">
                `appointment_date` = #{appointmentDate},
            </if>
            <if test="modifiedBy != null">
                `modified_by` = #{modifiedBy},
            </if>
            <if test="modifiedDate != null">
                `modified_date` = #{modifiedDate},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="branchTrustee != null">
                `branch_trustee` = #{branchTrustee},
            </if>
            <if test="visaExpirationDate != null">
                `visa_expiration_date` = #{visaExpirationDate},
            </if>
            <if test="permitExpirationDate != null">
                `permit_expiration_date` = #{permitExpirationDate},
            </if>
            <if test="visaNo != null">
                `visa_no` = #{visaNo},
            </if>
            <if test="permitNo != null">
                `permit_no` = #{permitNo}
            </if>
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="existsBySocietyIdAndIdentificationNumber" resultType="boolean" parameterType="map">
        SELECT count(*) FROM <include refid="tbl"/> WHERE `society_id` = #{societyId}
        AND `identification_type` = #{identificationType} AND `identification_no` = #{identificationNo}
        AND `status` = #{status}
    </select>

    <select id="findActiveTrusteesByParam" parameterType="map" resultMap="TrusteeHolderMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tbl"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="status != null">
            AND `status` = #{status}
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
    </select>
</mapper>