<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AdmAddresses">

    <resultMap id="AdmAddressesMap" type="com.eroses.external.society.model.AdmAddresses">
        <id property="id" column="id" />
        <result column="pid" property="pid"/>
        <result column="name" property="name"/>
        <result column="level" property="level"/>
        <result column="short_code" property="shortCode"/>
        <result column="code" property="code"/>
        <result column="status" property="status"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="migrate" property="migrate"/>
    </resultMap>

    <resultMap id="DistrictWithStateMap" type="com.eroses.external.society.dto.database.lookup.DistrictWithState">
        <id property="id" column="district_id" />
        <result column="district_name" property="name"/>
        <result column="district_code" property="code"/>
        <result column="district_level" property="level"/>
        <result column="district_short_code" property="shortCode"/>
        <result column="district_status" property="status"/>
        <result column="district_created_by" property="createdBy"/>
        <result column="district_created_date" property="createdDate"/>
        <result column="district_modified_by" property="modifiedBy"/>
        <result column="district_modified_date" property="modifiedDate"/>
        <result column="district_migrate" property="migrate"/>
        <result column="state_id" property="stateId"/>
        <result column="state_name" property="stateName"/>
        <result column="state_code" property="stateCode"/>
    </resultMap>

    <sql id="tb">adm_addresses</sql>

    <sql id="selectTb">
        adm_addresses aa
    </sql>

    <sql id="cols">
        id, pid, name, level, short_code, code, status, created_by, created_date, modified_by, modified_date, migrate
    </sql>

    <sql id="colsWithoutId">
        pid, name, level, short_code, code, status, created_by, created_date, modified_by, modified_date, migrate
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.AdmAddresses" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="colsWithoutId"/>)
        VALUES
        (#{pid}, #{name}, #{level}, #{shortCode}, #{code}, #{status}, #{createdBy}, now(), #{modifiedBy}, now(), 0)
    </insert>

    <select id="findAll" resultType="list" resultMap="AdmAddressesMap">
        SELECT
            <include refid="cols"/>
        FROM
            <include refid="tb"/>
        WHERE `status` != '-1'
        AND `status` != '0'
    </select>

    <select id="findById" parameterType="long" resultMap="AdmAddressesMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
    </select>

    <select id="paging" parameterType="map" resultMap="AdmAddressesMap">
        select
        <include refid="cols"/>
        from
        <include refid="tb"/>
        where status != '-1'
        AND `status` != '0'
        LIMIT #{offset}, #{limit}
    </select>

    <select id="getAll" resultMap="AdmAddressesMap">
        SELECT <include refid="cols"/>
        FROM <include refid="tb"/>
        WHERE level = #{level}
        AND status != 0
        <if test="nameQuery != null and nameQuery != ''">
            AND name LIKE CONCAT('%', #{nameQuery}, '%')
        </if>
        <if test="pid != null and pid != ''">
            AND pid = #{pid}
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="getAllDistricts" parameterType="map" resultMap="DistrictWithStateMap">
        SELECT
            d.id as district_id,
            d.name as district_name,
            d.code as district_code,
            d.level as district_level,
            d.short_code as district_short_code,
            d.status as district_status,
            d.created_by as district_created_by,
            d.created_date as district_created_date,
            d.modified_by as district_modified_by,
            d.modified_date as district_modified_date,
            d.migrate as district_migrate,
            s.id as state_id,
            s.name as state_name,
            s.code as state_code
        FROM
            adm_addresses d
        LEFT JOIN
            adm_addresses s ON d.pid = s.id
        WHERE
            d.level = 2
            AND d.status != 0
        <if test="nameQuery != null and nameQuery != ''">
            AND d.name LIKE CONCAT('%', #{nameQuery}, '%')
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAll" resultType="Long">
        SELECT COUNT(*)
        FROM <include refid="tb"/>
        WHERE level = #{level}
        AND status != 0
        <if test="nameQuery != null and nameQuery != ''">
            AND name LIKE CONCAT('%', #{nameQuery}, '%')
        </if>
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.AdmAddresses">
        UPDATE <include refid="tb"/>
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="shortCode != null">short_code = #{shortCode},</if>
            <if test="code != null">code = #{code},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date = now()
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <select id="existsByCode" resultType="boolean">
        SELECT EXISTS (
        SELECT 1
        FROM <include refid="tb"/>
        WHERE code = #{code}
        AND level = #{level}
        )
    </select>

    <select id="existsByCodeExcludingId" resultType="boolean">
        SELECT EXISTS (
        SELECT 1
        FROM <include refid="tb"/>
        WHERE code = #{code}
        AND id != #{id}
        AND level = #{level}
        )
    </select>

    <select id="getAllActive" resultMap="AdmAddressesMap">
        SELECT <include refid="cols"/>
        FROM <include refid="tb"/>
        WHERE `status` = 1
        AND `level` = #{level}
    </select>
</mapper>
