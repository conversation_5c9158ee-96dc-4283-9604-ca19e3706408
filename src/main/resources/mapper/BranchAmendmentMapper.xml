<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="BranchAmendment">

    <resultMap id="BranchAmendmentResultMap" type="com.eroses.external.society.model.BranchAmendment">
        <id property="id" column="id"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="amendment_type" property="amendmentType"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="meeting_type" property="meetingType"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="meeting_place" property="meetingPlace"/>
        <result column="meeting_time" property="meetingTime"/>
        <result column="total_attendees" property="totalAttendees"/>
        <result column="branch_name" property="branchName"/>
        <result column="address" property="address"/>
        <result column="country_code" property="countryCode"/>
        <result column="state_id" property="stateCode"/>
        <result column="district_id" property="districtCode"/>
        <result column="small_district_code" property="smallDistrictCode"/>
        <result column="city_code" property="cityCode"/>
        <result column="city" property="city"/>
        <result column="postcode" property="postcode"/>
        <result column="mailing_address" property="mailingAddress"/>
        <result column="mailing_state_id" property="mailingStateId"/>
        <result column="mailing_district_id" property="mailingDistrictId"/>
        <result column="mailing_city" property="mailingCity"/>
        <result column="mailing_postcode" property="mailingPostcode"/>
        <result column="acknowledge" property="acknowledge"/>
        <result column="acknowledge_date" property="acknowledgeDate"/>
        <result column="payment_id" property="paymentId"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="payment_date" property="paymentDate"/>
        <result column="bank_reference_no" property="bankReferenceNo"/>
        <result column="bank_name" property="bankName"/>
        <result column="receipt_no" property="receiptNo"/>
        <result column="receipt_status" property="receiptStatus"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="ro" property="ro"/>
        <result column="note_ro" property="noteRo"/>
        <result column="transfer_date" property="transferDate"/>
        <result column="original_branch_name" property="originalBranchName"/>
        <result column="original_address" property="originalAddress"/>
        <result column="original_country_code" property="originalCountryCode"/>
        <result column="original_state_code" property="originalStateCode"/>
        <result column="original_district_code" property="originalDistrictCode"/>
        <result column="original_small_district_code" property="originalSmallDistrictCode"/>
        <result column="original_city_code" property="originalCityCode"/>
        <result column="original_city" property="originalCity"/>
        <result column="original_postcode" property="originalPostcode"/>
        <result column="original_mailing_address" property="originalMailingAddress"/>
        <result column="original_mailing_state_id" property="originalMailingStateId"/>
        <result column="original_mailing_district_id" property="originalMailingDistrictId"/>
        <result column="original_mailing_city" property="originalMailingCity"/>
        <result column="original_mailing_postcode" property="originalMailingPostcode"/>
        <result column="epayment_id" property="epaymentId"/>
        <result column="reconcile_date" property="reconcileDate"/>
    </resultMap>

    <resultMap id="BranchAmendmentSelectResultMap" type="com.eroses.external.society.model.BranchAmendment">
        <id column="ba_id" property="id" />
        <result column="ba_society_id" property="societyId"/>
        <result column="ba_society_no" property="societyNo"/>
        <result column="ba_branch_id" property="branchId"/>
        <result column="ba_branch_no" property="branchNo"/>
        <result column="ba_amendment_type" property="amendmentType"/>
        <result column="ba_meeting_id" property="meetingId"/>
        <result column="ba_meeting_type" property="meetingType"/>
        <result column="ba_meeting_date" property="meetingDate"/>
        <result column="ba_meeting_place" property="meetingPlace"/>
        <result column="ba_meeting_time" property="meetingTime"/>
        <result column="ba_total_attendees" property="totalAttendees"/>
        <result column="ba_branch_name" property="branchName"/>
        <result column="ba_address" property="address"/>
        <result column="ba_country_code" property="countryCode"/>
        <result column="ba_state_id" property="stateCode"/>
        <result column="ba_district_id" property="districtCode"/>
        <result column="ba_small_district_code" property="smallDistrictCode"/>
        <result column="ba_city_code" property="cityCode"/>
        <result column="ba_city" property="city"/>
        <result column="ba_postcode" property="postcode"/>
        <result column="ba_mailing_address" property="mailingAddress"/>
        <result column="ba_mailing_state_id" property="mailingStateId"/>
        <result column="ba_mailing_district_id" property="mailingDistrictId"/>
        <result column="ba_mailing_city" property="mailingCity"/>
        <result column="ba_mailing_postcode" property="mailingPostcode"/>
        <result column="ba_acknowledge" property="acknowledge"/>
        <result column="ba_acknowledge_date" property="acknowledgeDate"/>
        <result column="ba_payment_id" property="paymentId"/>
        <result column="ba_payment_method" property="paymentMethod"/>
        <result column="ba_payment_date" property="paymentDate"/>
        <result column="ba_bank_reference_no" property="bankReferenceNo"/>
        <result column="ba_bank_name" property="bankName"/>
        <result column="ba_receipt_no" property="receiptNo"/>
        <result column="ba_receipt_status" property="receiptStatus"/>
        <result column="ba_created_by" property="createdBy"/>
        <result column="ba_created_date" property="createdDate"/>
        <result column="ba_modified_by" property="modifiedBy"/>
        <result column="ba_modified_date" property="modifiedDate"/>
        <result column="ba_application_status_code" property="applicationStatusCode"/>
        <result column="ba_ro" property="ro"/>
        <result column="ba_note_ro" property="noteRo"/>
        <result column="ba_transfer_date" property="transferDate"/>
        <result column="ba_original_branch_name" property="originalBranchName"/>
        <result column="ba_original_address" property="originalAddress"/>
        <result column="ba_original_country_code" property="originalCountryCode"/>
        <result column="ba_original_state_code" property="originalStateCode"/>
        <result column="ba_original_district_code" property="originalDistrictCode"/>
        <result column="ba_original_small_district_code" property="originalSmallDistrictCode"/>
        <result column="ba_original_city_code" property="originalCityCode"/>
        <result column="ba_original_city" property="originalCity"/>
        <result column="ba_original_postcode" property="originalPostcode"/>
        <result column="ba_original_mailing_address" property="originalMailingAddress"/>
        <result column="ba_original_mailing_state_id" property="originalMailingStateId"/>
        <result column="ba_original_mailing_district_id" property="originalMailingDistrictId"/>
        <result column="ba_original_mailing_city" property="originalMailingCity"/>
        <result column="ba_original_mailing_postcode" property="originalMailingPostcode"/>
        <result column="ba_epayment_id" property="epaymentId"/>
        <result column="ba_reconcile_date" property="reconcileDate"/>

        <association property="branch"
                     resultMap="Branch.BranchSelectMap"/>
    </resultMap>

    <sql id="tb">
        branch_amendment
    </sql>

    <sql id="selectTb">
        branch_amendment ba
    </sql>

    <sql id="selectCols">
        ba.`id` AS ba_id,
        ba.`society_id` AS ba_society_id,
        ba.`society_no` AS ba_society_no,
        ba.`branch_id` AS ba_branch_id,
        ba.`branch_no` AS ba_branch_no,
        ba.`amendment_type` AS ba_amendment_type,
        ba.`meeting_id` AS ba_meeting_id,
        ba.`meeting_type` AS ba_meeting_type,
        ba.`meeting_date` AS ba_meeting_date,
        ba.`meeting_place` AS ba_meeting_place,
        ba.`meeting_time` AS ba_meeting_time,
        ba.`total_attendees` AS ba_total_attendees,
        ba.`branch_name` AS ba_branch_name,
        ba.`address` AS ba_address,
        ba.`country_code` AS ba_country_code,
        ba.`state_id` AS ba_state_id,
        ba.`district_id` AS ba_district_id,
        ba.`small_district_code` AS ba_small_district_code,
        ba.`city_code` AS ba_city_code,
        ba.`city` AS ba_city,
        ba.`postcode` AS ba_postcode,
        ba.`mailing_address` AS ba_mailing_address,
        ba.`mailing_state_id` AS ba_mailing_state_id,
        ba.`mailing_district_id` AS ba_mailing_district_id,
        ba.`mailing_city` AS ba_mailing_city,
        ba.`mailing_postcode` AS ba_mailing_postcode,
        ba.`acknowledge` AS ba_acknowledge,
        ba.`acknowledge_date` AS ba_acknowledge_date,
        ba.`payment_id` AS ba_payment_id,
        ba.`payment_method` AS ba_payment_method,
        ba.`payment_date` AS ba_payment_date,
        ba.`bank_reference_no` AS ba_bank_reference_no,
        ba.`bank_name` AS ba_bank_name,
        ba.`receipt_no` AS ba_receipt_no,
        ba.`receipt_status` AS ba_receipt_status,
        ba.`created_by` AS ba_created_by,
        ba.`created_date` AS ba_created_date,
        ba.`modified_by` AS ba_modified_by,
        ba.`modified_date` AS ba_modified_date,
        ba.`application_status_code` AS ba_application_status_code,
        ba.`ro` AS ba_ro,
        ba.`note_ro` AS ba_note_ro,
        ba.`transfer_date` AS ba_transfer_date,
        ba.`original_branch_name` AS ba_original_branch_name,
        ba.`original_address` AS ba_original_address,
        ba.`original_country_code` AS ba_original_country_code,
        ba.`original_state_code` AS ba_original_state_code,
        ba.`original_district_code` AS ba_original_district_code,
        ba.`original_small_district_code` AS ba_original_small_district_code,
        ba.`original_city_code` AS ba_original_city_code,
        ba.`original_city` AS ba_original_city,
        ba.`original_postcode` AS ba_original_postcode,
        ba.`original_mailing_address` AS ba_original_mailing_address,
        ba.`original_mailing_state_id` AS ba_original_mailing_state_id,
        ba.`original_mailing_district_id` AS ba_original_mailing_district_id,
        ba.`original_mailing_city` AS ba_original_mailing_city,
        ba.`original_mailing_postcode` AS ba_original_mailing_postcode,
        ba.`epayment_id` AS ba_epayment_id,
        ba.`reconcile_date` AS ba_reconcile_date
    </sql>

    <sql id="cols">
        society_id, society_no, branch_id, branch_no, amendment_type, meeting_id, meeting_type, meeting_date,
        meeting_place, meeting_time, total_attendees, branch_name, address, country_code, state_id, district_id,
        small_district_code, city_code, city, postcode, mailing_address, mailing_state_id, mailing_district_id,
        mailing_city, mailing_postcode, acknowledge, acknowledge_date, payment_id, payment_method,
        payment_date, bank_reference_no, bank_name, receipt_no, receipt_status, created_by, created_date,
        modified_by, modified_date, application_status_code, ro, note_ro, transfer_date, original_branch_name, original_address,
        original_country_code, original_state_code, original_district_code, original_small_district_code,
        original_city_code, original_city, original_postcode, original_mailing_address, original_mailing_state_id,
        original_mailing_district_id, original_mailing_city, original_mailing_postcode, epayment_id, reconcile_date
    </sql>

    <sql id="colsWithId">
        id, <include refid="cols"/>
    </sql>

    <select id="findById" parameterType="java.lang.Long" resultMap="BranchAmendmentResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE id = #{id}
        LIMIT 1
    </select>

    <select id="getByPaymentId" parameterType="java.lang.Long" resultMap="BranchAmendmentResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE payment_id = #{paymentId}
        LIMIT 1
    </select>

    <insert id="create" parameterType="com.eroses.external.society.model.BranchAmendment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{societyId}, #{societyNo}, #{branchId}, #{branchNo}, #{amendmentType}, #{meetingId}, #{meetingType},
        #{meetingDate}, #{meetingPlace}, #{meetingTime}, #{totalAttendees}, #{branchName}, #{address}, #{countryCode},
        #{stateCode}, #{districtCode}, #{smallDistrictCode}, #{cityCode}, #{city}, #{postcode}, #{mailingAddress},
        #{mailingStateId}, #{mailingDistrictId}, #{mailingCity}, #{mailingPostcode}, #{acknowledge},
        #{acknowledgeDate}, #{paymentId}, #{paymentMethod}, #{paymentDate}, #{bankReferenceNo}, #{bankName}, #{receiptNo},
        #{receiptStatus}, #{createdBy}, now(), #{modifiedBy}, now(), #{applicationStatusCode}, #{ro}, #{noteRo}, #{transferDate},
        #{originalBranchName}, #{originalAddress}, #{originalCountryCode}, #{originalStateCode},
        #{originalDistrictCode}, #{originalSmallDistrictCode}, #{originalCityCode}, #{originalCity}, #{originalPostcode},
        #{originalMailingAddress}, #{originalMailingStateId}, #{originalMailingDistrictId}, #{originalMailingCity},
        #{originalMailingPostcode}, #{epaymentId}, #{reconcileDate})
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.BranchAmendment">
        UPDATE <include refid="tb"/>
        <set>
            <if test="societyId != null">society_id = #{societyId},</if>
            <if test="societyNo != null">society_no = #{societyNo},</if>
            <if test="branchId != null">branch_id = #{branchId},</if>
            <if test="branchNo != null">branch_no = #{branchNo},</if>
            <if test="amendmentType != null">amendment_type = #{amendmentType},</if>
            <if test="meetingId != null">meeting_id = #{meetingId},</if>
            <if test="meetingType != null">meeting_type = #{meetingType},</if>
            <if test="meetingDate != null">meeting_date = #{meetingDate},</if>
            <if test="meetingPlace != null">meeting_place = #{meetingPlace},</if>
            <if test="meetingTime != null">meeting_time = #{meetingTime},</if>
            <if test="totalAttendees != null">total_attendees = #{totalAttendees},</if>
            branch_name = #{branchName},
            address = #{address},
            country_code = #{countryCode},
            state_id = #{stateCode},
            district_id = #{districtCode},
            small_district_code = #{smallDistrictCode},
            city_code = #{cityCode},
            city = #{city},
            postcode = #{postcode},
            mailing_address = #{mailingAddress},
            mailing_state_id = #{mailingStateId},
            mailing_district_id = #{mailingDistrictId},
            mailing_city = #{mailingCity},
            mailing_postcode = #{mailingPostcode},
            <if test="acknowledge != null">acknowledge = #{acknowledge},</if>
            <if test="acknowledgeDate != null">acknowledge_date = #{acknowledgeDate},</if>
            <if test="paymentId != null">payment_id = #{paymentId},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="paymentDate != null">payment_date = #{paymentDate},</if>
            <if test="bankReferenceNo != null">bank_reference_no = #{bankReferenceNo},</if>
            <if test="bankName != null">bank_name = #{bankName},</if>
            <if test="receiptNo != null">receipt_no = #{receiptNo},</if>
            <if test="receiptStatus != null">receipt_status = #{receiptStatus},</if>
            <if test="applicationStatusCode != null">application_status_code = #{applicationStatusCode},</if>
            <if test="ro != null">ro = #{ro},</if>
            <if test="noteRo != null">note_ro = #{noteRo},</if>
            <if test="transferDate != null">transfer_date = #{transferDate},</if>
            original_branch_name = #{originalBranchName},
            original_address = #{originalAddress},
            original_country_code = #{originalCountryCode},
            original_state_code = #{originalStateCode},
            original_district_code = #{originalDistrictCode},
            original_small_district_code = #{originalSmallDistrictCode},
            original_city_code = #{originalCityCode},
            original_city = #{originalCity},
            original_postcode = #{originalPostcode},
            original_mailing_address = #{originalMailingAddress},
            original_mailing_state_id = #{originalMailingStateId},
            original_mailing_district_id = #{originalMailingDistrictId},
            original_mailing_city = #{originalMailingCity},
            original_mailing_postcode = #{originalMailingPostcode},
            <if test="epaymentId != null">epayment_id = #{epaymentId},</if>
            <if test="reconcileDate != null">reconcile_date = #{reconcileDate},</if>
            modified_date = NOW(),
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="getAll" resultMap="BranchAmendmentResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
    </select>

    <select id="getAllByCriteria" parameterType="map" resultMap="BranchAmendmentSelectResultMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Branch.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Branch.selectTb"/> ON ba.branch_id = b.id
        WHERE 1=1
        <if test="societyId != null and societyId != 0">
            AND ba.society_id = #{societyId}
        </if>
        <if test="societyNo != null and societyNo !=''">
            AND ba.society_no = #{societyNo}
        </if>
        <if test="branchId != 0 and branchId != null">
            AND ba.branch_id = #{branchId}
        </if>
        <if test="branchNo != null and branchNo !=''">
            AND ba.branch_no = #{branchNo}
        </if>
        <if test="amendmentType != null and amendmentType !=''">
            AND ba.amendment_type = #{amendmentType}
        </if>
        <if test="meetingId != 0 and meetingId != null">
            AND ba.meeting_id = #{meetingId}
        </if>
        <if test="paymentId != 0 and paymentId != null">
            AND ba.payment_id = #{paymentId}
        </if>
        <if test="createdBy != null and createdBy !=''">
            AND ba.created_by = #{createdBy}
        </if>
        <if test="modifiedBy != null and modifiedBy !=''">
            AND ba.modified_by = #{modifiedBy}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != 0">
            AND ba.application_status_code = #{applicationStatusCode}
        </if>
        <if test="ro != null and ro !=''">
            AND ba.ro = #{ro}
        </if>
        <if test="branchNameQuery != null and branchNameQuery !=''">
            AND b.name LIKE CONCAT('%', #{branchNameQuery}, '%')
        </if>
    </select>

    <select id="getAllPendingBranchAmendmentByCriteria" parameterType="map" resultMap="BranchAmendmentSelectResultMap">
        SELECT
        <include refid="selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Branch.selectTb"/> ON ba.branch_id = b.id
        WHERE 1=1
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND ba.application_status_code = #{applicationStatusCode}
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND b.state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND ba.ro = #{ro}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            b.branch_no LIKE CONCAT('%', #{searchQuery}, '%')
            OR b.name LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        ORDER BY ba.payment_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllPendingBranchAmendmentByCriteria" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Branch.selectTb"/> ON ba.branch_id = b.id
        WHERE 1=1
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND ba.application_status_code = #{applicationStatusCode}
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND b.state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND ba.ro = #{ro}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            b.branch_no LIKE CONCAT('%', #{searchQuery}, '%')
            OR b.name LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
    </select>

    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM <include refid="tb"/>
        WHERE id = #{id}
    </delete>

</mapper>