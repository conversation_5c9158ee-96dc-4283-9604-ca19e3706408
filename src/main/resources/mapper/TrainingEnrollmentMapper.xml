<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="TrainingEnrollment">

    <resultMap id="TrainingEnrollmentMap" type="com.eroses.external.society.model.TrainingEnrollment">
        <id property="id" column="id" />
        <result property="trainingCourseId" column="training_course_id" />
        <result property="userId" column="user_id" />
        <result property="enrollmentDate" column="enrollment_date" />
        <result property="completionDate" column="completion_date" />
        <result property="completionStatus" column="completion_status" />
        <result property="certificateId" column="certificate_id" />
        <result property="totalStep" column="total_step" />
        <result property="currentStep" column="current_step" />
    </resultMap>

    <sql id="tb">
        training_enrollment
    </sql>

    <sql id="cols">
        training_course_id,
        user_id,
        enrollment_date,
        completion_date,
        completion_status,
        certificate_id,
        total_step,
        current_step
    </sql>

    <sql id="colsWithId">
        id, <include refid="cols" />
    </sql>

    <!-- Create -->
    <insert id="create" parameterType="com.eroses.external.society.model.TrainingEnrollment" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb" /> (
            training_course_id,
            user_id,
            enrollment_date,
            completion_status,
            total_step
        ) VALUES (
            #{trainingCourseId},
            #{userId},
            NOW(),
            'IN_PROGRESS',
            #{totalStep}
        )
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.TrainingEnrollment">
        UPDATE <include refid="tb" />
        SET
            completion_date = #{completionDate},
            completion_status = #{completionStatus},
            certificate_id = #{certificateId}
        WHERE id = #{id}
    </update>

    <!-- Find By ID -->
    <select id="findById" resultMap="TrainingEnrollmentMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE id = #{id}
    </select>

    <!-- Find By User ID and Course ID -->
    <select id="findByUserIdAndCourseId" resultMap="TrainingEnrollmentMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE user_id = #{userId}
        AND training_course_id = #{trainingCourseId}
    </select>

    <!-- Find All By User ID -->
    <select id="findAllByUserId" resultMap="TrainingEnrollmentMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE user_id = #{userId}
        ORDER BY enrollment_date DESC
    </select>

    <!-- Find All By Course ID -->
    <select id="findAllByCourseId" resultMap="TrainingEnrollmentMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE training_course_id = #{trainingCourseId}
        ORDER BY enrollment_date DESC
    </select>

    <!-- Find All Completed By User ID -->
    <select id="findAllCompletedByUserId" resultMap="TrainingEnrollmentMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE user_id = #{userId}
        AND completion_status = 'COMPLETED'
        ORDER BY completion_date DESC
    </select>

    <!-- Update Completion Status -->
    <update id="updateCompletionStatus">
        UPDATE <include refid="tb" />
        SET
            completion_status = #{completionStatus},
            <if test="completionStatus == 'COMPLETED'">
                completion_date = NOW(),
            </if>
        WHERE id = #{id}
    </update>

    <!-- Update Certificate ID -->
    <update id="updateCertificateId">
        UPDATE <include refid="tb" />
        SET
            certificate_id = #{certificateId}
        WHERE id = #{id}
    </update>

    <!-- Update Current Chapter -->
    <update id="updateEnrollmentStep" parameterType="com.eroses.external.society.model.TrainingEnrollment">
        UPDATE <include refid="tb" />
        SET
            current_step = #{currentStep}
        WHERE id = #{id}
    </update>

</mapper>
