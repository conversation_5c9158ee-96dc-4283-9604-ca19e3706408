<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="SocietyCancellation">
    <resultMap id="SocietyCancellationMap" type="com.eroses.external.society.model.societyCancellation.SocietyCancellation">
        <id property="id" column="id"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="cancelled_date" property="cancelledDate"/>
        <result column="section" property="section"/>
        <result column="reason" property="reason"/>
        <result column="is_cancelled" property="isCancelled"/>
        <result column="is_reverted" property="isReverted"/>
        <result column="is_liquidation" property="isLiquidation"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <resultMap id="SocietyCancellationSelectMap" type="com.eroses.external.society.model.societyCancellation.SocietyCancellation">
        <id property="id" column="sc_id"/>
        <result column="sc_society_id" property="societyId"/>
        <result column="sc_society_no" property="societyNo"/>
        <result column="sc_branch_id" property="branchId"/>
        <result column="sc_branch_no" property="branchNo"/>
        <result column="sc_cancelled_date" property="cancelledDate"/>
        <result column="sc_section" property="section"/>
        <result column="sc_reason" property="reason"/>
        <result column="sc_is_cancelled" property="isCancelled"/>
        <result column="sc_is_reverted" property="isReverted"/>
        <result column="sc_is_liquidation" property="isLiquidation"/>
        <result column="sc_created_by" property="createdBy"/>
        <result column="sc_created_date" property="createdDate"/>
        <result column="sc_modified_by" property="modifiedBy"/>
        <result column="sc_modified_date" property="modifiedDate"/>

        <association property="society" resultMap="Society.SocietySelectMap"/>
        <association property="branch" resultMap="Branch.BranchSelectMap"/>
    </resultMap>

    <sql id="tb">
        society_cancellation
    </sql>

    <sql id="selectTb">
        society_cancellation sc
    </sql>

    <sql id="cols">
        `society_id`, `society_no`, `branch_id`, `branch_no`, `cancelled_date`, `section`, `reason`, `is_cancelled`, `is_reverted`, `is_liquidation`,
        `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="selectCols">
        sc.`id` as sc_id,
        sc.`society_id` as sc_society_id,
        sc.`society_no` as sc_society_no,
        sc.`branch_id` as sc_branch_id,
        sc.`branch_no` as sc_branch_no,
        sc.`cancelled_date` as sc_cancelled_date,
        sc.`section` as sc_section,
        sc.`reason` as sc_reason,
        sc.`is_cancelled` as sc_is_cancelled,
        sc.`is_reverted` as sc_is_reverted,
        sc.`is_liquidation` as sc_is_liquidation,
        sc.`created_by` as sc_created_by,
        sc.`created_date` as sc_created_date,
        sc.`modified_by` as sc_modified_by,
        sc.`modified_date` as sc_modified_date
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.societyCancellation.SocietyCancellation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (`society_id`, `society_no`, `branch_id`, `branch_no`, `cancelled_date`, `section`, `reason`, `is_cancelled`, `is_reverted`, `is_liquidation`,
        `created_by`, `created_date`, `modified_by`, `modified_date`)
        VALUES
        (#{societyId}, #{societyNo}, #{branchId}, #{branchNo}, #{cancelledDate}, #{section}, #{reason}, #{isCancelled}, #{isReverted}, #{isLiquidation},
        #{createdBy}, now(), #{modifiedBy}, now())
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.societyCancellation.SocietyCancellation">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="cancelledDate != null">`cancelled_date` = #{cancelledDate},</if>
            <if test="section != null">`section` = #{section},</if>
            <if test="reason != null">`reason` = #{reason},</if>
            <if test="isCancelled != null">`is_cancelled` = #{isCancelled},</if>
            <if test="isReverted != null">`is_reverted` = #{isReverted},</if>
            <if test="isLiquidation != null">`is_liquidation` = #{isLiquidation},</if>
            `modified_by` = #{modifiedBy},
            `modified_date` = now()
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="findById" parameterType="java.lang.Long" resultMap="SocietyCancellationMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
    </select>

    <select id="findAll" resultMap="SocietyCancellationMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
    </select>

    <select id="findBySocietyId" parameterType="java.lang.Long" resultMap="SocietyCancellationMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
    </select>

    <select id="findBySocietyNo" parameterType="java.lang.String" resultMap="SocietyCancellationMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_no` = #{societyNo}
    </select>

    <select id="findByParam" parameterType="map" resultMap="SocietyCancellationSelectMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON sc.society_id = s.id
        <where>
            <if test="societyName != null and societyName != ''">
                AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
            </if>
            <if test="societyNo != null and societyNo != ''">
                AND s.`society_no` LIKE CONCAT('%', #{societyNo}, '%')
            </if>
            <if test="cancelledDateFrom != null">
                AND sc.`cancelled_date` >= #{cancelledDateFrom}
            </if>
            <if test="cancelledDateTo != null">
                AND sc.`cancelled_date` &lt;= #{cancelledDateTo}
            </if>
            <if test="section != null">
                AND sc.`section` = #{section}
            </if>
            <if test="isCancelled != null">
                AND sc.`is_cancelled` = #{isCancelled}
            </if>
            <if test="isReverted != null">
                AND sc.`is_reverted` = #{isReverted}
            </if>
            <if test="isLiquidation != null">
                AND sc.`is_liquidation` = #{isLiquidation}
            </if>
        </where>
        ORDER BY sc.`created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countByParam" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON sc.society_id = s.id
        <where>
            <if test="societyName != null and societyName != ''">
                AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
            </if>
            <if test="societyNo != null and societyNo != ''">
                AND s.`society_no` LIKE CONCAT('%', #{societyNo}, '%')
            </if>
            <if test="cancelledDateFrom != null">
                AND sc.`cancelled_date` >= #{cancelledDateFrom}
            </if>
            <if test="cancelledDateTo != null">
                AND sc.`cancelled_date` &lt;= #{cancelledDateTo}
            </if>
            <if test="section != null">
                AND sc.`section` = #{section}
            </if>
            <if test="isCancelled != null">
                AND sc.`is_cancelled` = #{isCancelled}
            </if>
            <if test="isReverted != null">
                AND sc.`is_reverted` = #{isReverted}
            </if>
            <if test="isLiquidation != null">
                AND sc.`is_liquidation` = #{isLiquidation}
            </if>
        </where>
    </select>

    <select id="findActiveSocietyCancellationsByParam" parameterType="map" resultMap="SocietyCancellationSelectMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>,
        <include refid="Branch.selectCols"/>
        FROM
        <include refid="selectTb"/>
        LEFT JOIN <include refid="Society.selectTb"/> ON sc.society_id = s.id
        LEFT JOIN <include refid="Branch.selectTb"/> ON sc.branch_id = b.id
        <where>
            <if test="societyName != null and societyName != ''">
                AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
            </if>
            <if test="societyNo != null and societyNo != ''">
                AND s.`society_no` LIKE CONCAT('%', #{societyNo}, '%')
            </if>
            <if test="stateCodes != null and stateCodes.size() > 0">
                AND s.`state_id` IN
                <foreach collection="stateCodes" item="stateCode" open="(" close=")" separator=",">
                    #{stateCode}
                </foreach>
            </if>
            <if test="societyCategoryCode != null and societyCategoryCode != 0">
                AND s.`category_code_jppm` = #{societyCategoryCode}
            </if>
            <if test="cancelledDateFrom != null">
                AND sc.`cancelled_date` >= #{cancelledDateFrom}
            </if>
            <if test="cancelledDateTo != null">
                AND sc.`cancelled_date` &lt;= #{cancelledDateTo}
            </if>
            <if test="section != null">
                AND sc.`section` = #{section}
            </if>
            <if test="isCancelled != null">
                AND sc.`is_cancelled` = #{isCancelled}
            </if>
            <if test="isReverted != null">
                AND sc.`is_reverted` = #{isReverted}
            </if>
            <if test="isLiquidation != null">
                AND sc.`is_liquidation` = #{isLiquidation}
            </if>
        </where>
        ORDER BY sc.`created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countActiveSocietyCancellationsByParam" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        LEFT JOIN <include refid="Society.selectTb"/> ON sc.society_id = s.id
        LEFT JOIN <include refid="Branch.selectTb"/> ON sc.branch_id = b.id
        <where>
            <if test="societyName != null and societyName != ''">
                AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
            </if>
            <if test="societyNo != null and societyNo != ''">
                AND s.`society_no` LIKE CONCAT('%', #{societyNo}, '%')
            </if>
            <if test="stateCodes != null and stateCodes.size() > 0">
                AND s.`state_id` IN
                <foreach collection="stateCodes" item="stateCode" open="(" close=")" separator=",">
                    #{stateCode}
                </foreach>
            </if>
            <if test="societyCategoryCode != null and societyCategoryCode != 0">
                AND s.`category_code_jppm` = #{societyCategoryCode}
            </if>
            <if test="cancelledDateFrom != null">
                AND sc.`cancelled_date` >= #{cancelledDateFrom}
            </if>
            <if test="cancelledDateTo != null">
                AND sc.`cancelled_date` &lt;= #{cancelledDateTo}
            </if>
            <if test="section != null">
                AND sc.`section` = #{section}
            </if>
            <if test="isCancelled != null">
                AND sc.`is_cancelled` = #{isCancelled}
            </if>
            <if test="isReverted != null">
                AND sc.`is_reverted` = #{isReverted}
            </if>
            <if test="isLiquidation != null">
                AND sc.`is_liquidation` = #{isLiquidation}
            </if>
        </where>
    </select>

    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
    </delete>

    <select id="getAllPendingToProcessSocietyCancellationId" parameterType="map" resultType="java.lang.Long">
        SELECT id
        FROM
        <include refid="tb" />
        WHERE `is_cancelled` = #{isCancelled}
        AND `is_reverted` = #{isReverted}
        AND DATE(cancelled_date) &lt;= CURDATE()
    </select>

</mapper>
