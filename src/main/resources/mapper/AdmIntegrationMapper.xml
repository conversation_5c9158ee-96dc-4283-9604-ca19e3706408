<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="Integration">
    <resultMap id="AdmIntegrationMap" type="com.eroses.external.society.model.Integration">
        <!-- ID -->
        <id column="id" property="id"/>

        <!-- Columns -->
        <result property="name" column="name" />
        <result property="type" column="type" />
        <result property="description" column="description"/>
        <result property="status" column="status" />

        <!-- Audit Columns -->
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <sql id="tb">
        `integration`
    </sql>

    <sql id="cols">
        `name`, `type`, `description`, `status`,
        `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <select id="findAll" resultMap="AdmIntegrationMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
    </select>

    <!-- Update -->
    <insert id="update" parameterType="com.eroses.external.society.model.Integration">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date = now()

        </set>
        WHERE
        id = #{id}
    </insert>

</mapper>
