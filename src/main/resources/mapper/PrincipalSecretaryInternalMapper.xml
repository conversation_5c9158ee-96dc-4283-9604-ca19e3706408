<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="PrincipalSecretaryInternalResponse">
    <resultMap id="PrincipalSecretaryMap"
               type="com.eroses.external.society.dto.response.principalSecretaryInternal.PrincipalSecretaryInternalResponse">
        <id property="id" column="id"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="title_code" property="titleCode"/>
        <result column="new_secretary_name" property="newSecretaryName"/>
        <result column="gender" property="gender"/>
        <result column="committee_position" property="committeePosition"/>
        <result column="job_code" property="jobCode"/>
        <result column="citizenship_status" property="citizenshipStatus"/>
        <result column="date_of_birth" property="dateOfBirth"/>
        <result column="place_of_birth" property="placeOfBirth"/>
        <result column="identification_type" property="identificationType"/>
        <result column="new_secretary_identification_no" property="identificationNo"/>
        <result column="email" property="email"/>
        <result column="residence_address" property="residenceAddress"/>
        <result column="residence_postcode" property="residencePostcode"/>
        <result column="residence_address_status" property="residenceAddressStatus"/>
        <result column="residence_country_code" property="residenceCountryCode"/>
        <result column="residence_state_code" property="residenceStateCode"/>
        <result column="residence_district_code" property="residenceDistrictCode"/>
        <result column="residence_city_code" property="residenceCityCode"/>
        <result column="residence_city" property="residenceCity"/>
        <result column="home_tel_no" property="homeTelNo"/>
        <result column="hp_no" property="hpNo"/>
        <result column="work_tel_no" property="workTelNo"/>
        <result column="employer_name" property="employerName"/>
        <result column="employer_address" property="employerAddress"/>
        <result column="employer_postcode" property="employerPostcode"/>
        <result column="employer_country_code" property="employerCountryCode"/>
        <result column="employer_state_code" property="employerStateCode"/>
        <result column="employer_district_code" property="employerDistrictCode"/>
        <result column="employer_city_code" property="employerCityCode"/>
        <result column="employer_city" property="employerCity"/>
        <result column="old_secretary_name_key_in" property="oldSecretaryNameKeyIn"/>
        <result column="old_secretary_identification_number_key_in" property="oldSecretaryIdentificationNumberKeyIn"/>
        <result column="old_secretary_name" property="oldSecretaryName"/>
        <result column="old_secretary_identification_number" property="oldSecretaryIdentificationNumber"/>
        <result column="old_secretary_residential_address" property="oldSecretaryResidentialAddress"/>
        <result column="old_secretary_residential_city" property="oldSecretaryResidentialCity"/>
        <result column="old_secretary_residential_postcode" property="oldSecretaryResidentialPostcode"/>
        <result column="old_secretary_telephone_number" property="oldSecretaryTelephoneNumber"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="meeting_type" property="meetingType"/>
        <result column="ro" property="ro"/>
        <result column="reason_of_change" property="reasonOfChange"/>
        <result column="ro_name" property="roName"/>
        <result column="note_ro" property="noteRo"/>
        <collection property="feedbacks"
                    ofType="com.eroses.external.society.dto.response.principalSecretaryInternal.PrincipalSecreatryFeedbackInternalResponse">
            <id column="feedback_id" property="feedbackId"/>
            <result column="feedback_name" property="name"/>
            <result column="feedback_identification_no" property="identificationNo"/>
            <result column="feedback_position_code" property="positionCode"/>
            <result column="feedback" property="feedback"/>
            <result column="feedback_other_reason" property="otherReason"/>
        </collection>
        <collection property="reviews"
                    ofType="com.eroses.external.society.dto.response.principalSecretaryInternal.RoApprovalResponse">
            <id column="ro_approval_id" property="id"/>
            <result column="ro_approval_decision" property="decision"/>
            <result column="ro_approval_note" property="note"/>
        </collection>
        <collection property="queries"
                    ofType="com.eroses.external.society.dto.response.principalSecretaryInternal.RoQueryResponse">
            <id column="ro_query_id" property="id"/>
            <result column="ro_query_note" property="note"/>
            <result column="ro_query_created_date" property="createdDate"/>
        </collection>
    </resultMap>

    <resultMap id="PrincipalSecretarySelectMap" type="com.eroses.external.society.model.PrincipalSecretary">
        <id property="id" column="ps_id"/>
        <result property="oldId" column="ps_old_id"/>
        <result property="societyCommitteeId" column="ps_society_committee_id"/>
        <result property="jobCode" column="ps_job_code"/>
        <result property="societyId" column="ps_society_id"/>
        <result property="societyNo" column="ps_society_no"/>
        <result property="titleCode" column="ps_title_code"/>
        <result property="committeeName" column="ps_committee_name"/>
        <result property="gender" column="ps_gender"/>
        <result property="citizenshipStatus" column="ps_citizenship_status"/>
        <result property="identificationType" column="ps_identification_type"/>
        <result property="identificationNo" column="ps_identification_no"/>
        <result property="dateOfBirth" column="ps_date_of_birth"/>
        <result property="placeOfBirth" column="ps_place_of_birth"/>
        <result property="committeePosition" column="ps_committee_position"/>
        <result property="employerAddressStatus" column="ps_employer_address_status"/>
        <result property="employerName" column="ps_employer_name"/>
        <result property="employerAddress" column="ps_employer_address"/>
        <result property="employerPostcode" column="ps_employer_postcode"/>
        <result property="employerCountryCode" column="ps_employer_country_code"/>
        <result property="employerStateCode" column="ps_employer_state_code"/>
        <result property="employerDistrictCode" column="ps_employer_district_code"/>
        <result property="employerCityCode" column="ps_employer_city_code"/>
        <result property="employerCity" column="ps_employer_city"/>
        <result property="residenceAddress" column="ps_residence_address"/>
        <result property="residencePostcode" column="ps_residence_postcode"/>
        <result property="residenceAddressStatus" column="ps_residence_address_status"/>
        <result property="residenceCountryCode" column="ps_residence_country_code"/>
        <result property="residenceStateCode" column="ps_residence_state_code"/>
        <result property="residenceDistrictCode" column="ps_residence_district_code"/>
        <result property="residenceCityCode" column="ps_residence_city_code"/>
        <result property="residenceCity" column="ps_residence_city"/>
        <result property="email" column="ps_email"/>
        <result property="homeTelNo" column="ps_home_tel_no"/>
        <result property="hpNo" column="ps_hp_no"/>
        <result property="workTelNo" column="ps_work_tel_no"/>
        <result property="deleteStatus" column="ps_delete_status"/>
        <result property="applicationStatusCode" column="ps_application_status_code"/>
        <result property="reasonOfChange" column="ps_reason_of_change"/>
        <result property="oldSecretaryName" column="ps_old_secretary_name"/>
        <result property="oldSecretaryIdentificationNumber" column="ps_old_secretary_identification_number"/>
        <result property="acknowledgement1" column="ps_acknowledgement_1"/>
        <result property="acknowledgement2" column="ps_acknowledgement_2"/>
        <result property="ro" column="ps_ro"/>
        <result property="activeStatus" column="ps_active_status"/>
        <result property="migrateStatSecretary" column="ps_migrate_stat_secretary"/>
        <result property="meetingDate" column="ps_meeting_date"/>
        <result property="meetingType" column="ps_meeting_type"/>
        <result property="pdfDocument" column="ps_pdf_document"/>
        <result property="submitDate" column="ps_submit_date"/>
        <result property="noteRo" column="ps_note_ro"/>
        <result property="transferDate" column="ps_transfer_date"/>
        <result property="isQueried" column="ps_is_queried"/>
        <result property="createdBy" column="ps_created_by"/>
        <result property="createdByName" column="ps_created_by_name"/>
        <result property="createdDate" column="ps_created_date"/>
        <result property="modifiedBy" column="ps_modified_by"/>
        <result property="modifiedDate" column="ps_modified_date"/>
        <result property="migrate" column="ps_migrate"/>
        <result property="meetingId" column="ps_meeting_id"/>
        <result property="roName" column="ps_ro_name"/>

        <!-- Association for society object if still applicable -->
        <association property="society" resultMap="Society.SocietySelectMap"/>
    </resultMap>

    <sql id="tb">
        principal_secretary
    </sql>

    <sql id="cols">
        old_id,
        society_committee_id,
        job_code,
        society_id,
        society_no,
        title_code,
        committee_name,
        gender,
        citizenship_status,
        identification_type,
        identification_no,
        date_of_birth,
        place_of_birth,
        committee_position,
        employer_address_status,
        employer_name,
        employer_address,
        employer_postcode,
        employer_country_code,
        employer_state_code,
        employer_district_code,
        employer_city_code,
        employer_city,
        residence_address,
        residence_postcode,
        residence_address_status,
        residence_country_code,
        residence_state_code,
        residence_district_code,
        residence_city_code,
        residence_city,
        email,
        home_tel_no,
        hp_no,
        work_tel_no,
        delete_status,
        application_status_code,
        reason_of_change,
        old_secretary_name,
        old_secretary_identification_number,
        acknowledgement_1,
        acknowledgement_2,
        ro,
        active_status,
        migrate_stat_secretary,
        meeting_date,
        meeting_type,
        pdf_document,
        submit_date,
        note_ro,
        transfer_date,
        is_queried,
        created_by,
        created_by_name,
        created_date,
        modified_by,
        modified_date
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <sql id="selectTb">
        principal_secretary ps
    </sql>

    <sql id="selectCols">
        ps.`id` as ps_id,
        ps.`society_committee_id` as ps_society_committee_id,
        ps.`society_id` as ps_society_id,
        ps.`society_no` as ps_society_no,
        ps.`title_code` as ps_title_code,
        ps.`committee_name` as ps_committee_name,
        ps.`gender` as ps_gender,
        ps.`committee_position` as ps_committee_position,
        ps.`job_code` as ps_job_code,
        ps.`citizenship_status` as ps_citizenship_status,
        ps.`date_of_birth` as ps_date_of_birth,
        ps.`place_of_birth` as ps_place_of_birth,
        ps.`identification_type` as ps_identification_type,
        ps.`identification_no` as ps_identification_no,
        ps.`email` as ps_email,
        ps.`residence_address` as ps_residence_address,
        ps.`residence_postcode` as ps_residence_postcode,
        ps.`residence_address_status` as ps_residence_address_status,
        ps.`residence_country_code` as ps_residence_country_code,
        ps.`residence_state_code` as ps_residence_state_code,
        ps.`residence_district_code` as ps_residence_district_code,
        ps.`residence_city_code` as ps_residence_city_code,
        ps.`residence_city` as ps_residence_city,
        ps.`home_tel_no` as ps_home_tel_no,
        ps.`hp_no` as ps_hp_no,
        ps.`work_tel_no` as ps_work_tel_no,
        ps.`employer_name` as ps_employer_name,
        ps.`employer_address` as ps_employer_address,
        ps.`employer_postcode` as ps_employer_postcode,
        ps.`employer_country_code` as ps_employer_country_code,
        ps.`employer_state_code` as ps_employer_state_code,
        ps.`employer_district_code` as ps_employer_district_code,
        ps.`employer_city_code` as ps_employer_city_code,
        ps.`employer_city` as ps_employer_city,
        ps.`old_secretary_name` as ps_old_secretary_name,
        ps.`old_secretary_identification_number` as ps_old_secretary_identification_number,
        ps.`meeting_id` as ps_meeting_id,
        ps.`meeting_date` as ps_meeting_date,
        ps.`meeting_type` as ps_meeting_type,
        ps.`reason_of_change` as ps_reason_of_change,
        ps.`application_status_code` as ps_application_status_code,
        ps.`acknowledgement_1` as ps_acknowledgement_1,
        ps.`acknowledgement_2` as ps_acknowledgement_2,
        ps.`active_status` as ps_active_status,
        ps.`migrate_stat_secretary` as ps_migrate_stat_secretary,
        ps.`pdf_document` as ps_pdf_document,
        ps.`submit_date` as ps_submit_date,
        ps.`transfer_date` as ps_transfer_date,
        ps.`is_queried` as ps_is_queried,
        ps.`ro` as ps_ro,
        ps.`ro_name` as ps_ro_name,
        ps.`note_ro` as ps_note_ro,
        ps.`created_by` as ps_created_by,
        ps.`created_by_name` as ps_created_by_name,
        ps.`created_date` as ps_created_date,
        ps.`modified_by` as ps_modified_by,
        ps.`modified_date` as ps_modified_date
    </sql>

    <select id="findPrincipalSecretaryInternal" parameterType="java.lang.Long" resultMap="PrincipalSecretaryMap">
        SELECT
        ps.id,
        ps.society_id,
        ps.society_no,
        ps.title_code,
        ps.committee_name AS new_secretary_name,
        ps.gender,
        ps.committee_position,
        ps.job_code,
        ps.citizenship_status,
        ps.date_of_birth,
        ps.place_of_birth,
        ps.identification_type,
        ps.identification_no AS new_secretary_identification_no,
        ps.email,
        ps.residence_address,
        ps.residence_postcode,
        ps.residence_address_status,
        ps.residence_country_code,
        ps.residence_state_code,
        ps.residence_district_code,
        ps.residence_city_code,
        ps.residence_city,
        ps.home_tel_no,
        ps.hp_no,
        ps.work_tel_no,
        ps.employer_name,
        ps.employer_address,
        ps.employer_postcode,
        ps.employer_country_code,
        ps.employer_state_code,
        ps.employer_district_code,
        ps.employer_city_code,
        ps.employer_city,
        ps.old_secretary_name AS old_secretary_name_key_in,
        ps.old_secretary_identification_number AS old_secretary_identification_number_key_in,
        os.residential_address AS old_secretary_residential_address,
        os.residential_postcode AS old_secretary_residential_postcode,
        os.residential_city AS old_secretary_residential_city,
        os.telephone_number AS old_secretary_telephone_number,
        os.name AS old_secretary_name,
        os.identification_no AS old_secretary_identification_number,
        ps.meeting_id,
        ps.meeting_date,
        ps.meeting_type,
        ps.ro,
        ps.ro_name,
        ps.note_ro,
        ps.reason_of_change,
        s.society_name,
        nsf.id AS feedback_id,
        sc.name AS feedback_name,
        nsf.identification_no AS feedback_identification_no,
        sc.designation_code AS feedback_position_code,
        nsf.feedback,
        nsf.other_reason AS feedback_other_reason,
        ra.id AS ro_approval_id,
        ra.decision AS ro_approval_decision,
        ra.note AS ro_approval_note,
        rq.id AS ro_query_id,
        rq.note AS ro_query_note,
        rq.created_date AS ro_query_created_date
        FROM
        <include refid="tb"/>
        ps
        JOIN society s ON s.id = ps.society_id
        LEFT JOIN society_committee os ON (
        os.designation_code = 3
        AND os.society_id = ps.society_id
        AND os.status = "001"
        )
        LEFT JOIN new_secretary_feedback nsf ON nsf.secretary_id = ps.id
        LEFT JOIN society_committee sc ON sc.id = nsf.society_committee_id
        LEFT JOIN ro_approval ra ON ra.principal_secretary_id = ps.id
        LEFT JOIN ro_query rq ON rq.principal_secretary_id = ps.id
        WHERE ps.id = #{id}
    </select>

    <select id="getAllByCriteria" parameterType="map" resultMap="PrincipalSecretarySelectMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.id = ps.society_id
        <where>
            <if test="applicationStatusCode != null">
                AND ps.`application_status_code` = #{applicationStatusCode}
            </if>
            <if test="stateCode != null">
                AND s.`state_id` = #{stateCode}
            </if>
            <if test="searchQuery != null and searchQuery != ''">
                AND (
                s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
                OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
                OR ps.`committee_name` LIKE CONCAT('%', #{searchQuery}, '%')
                OR ps.`identification_no` LIKE CONCAT('%', #{searchQuery}, '%')
                )
            </if>
        </where>
        ORDER BY ps.`created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllByCriteria" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.id = ps.society_id
        <where>
            <if test="applicationStatusCode != null">
                AND ps.`application_status_code` = #{applicationStatusCode}
            </if>
            <if test="stateCode != null">
                AND s.`state_id` = #{stateCode}
            </if>
            <if test="searchQuery != null and searchQuery != ''">
                AND (
                s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
                OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
                OR ps.`committee_name` LIKE CONCAT('%', #{searchQuery}, '%')
                OR ps.`identification_no` LIKE CONCAT('%', #{searchQuery}, '%')
                )
            </if>
        </where>
    </select>

</mapper>
