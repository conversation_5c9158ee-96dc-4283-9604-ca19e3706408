<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="SocietyCommitteeArchive">
    <resultMap id="SocietyCommitteeArchiveMap" type="com.eroses.external.society.model.SocietyCommitteeArchive">
        <!-- ID -->
        <id column="id" property="id"/>
        <result column="committee_table_old_id" property="committeeTableOldId"/>

        <!-- Columns -->
        <result column="job_code" property="jobCode"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="title_code" property="titleCode"/>
        <result column="name" property="name"/>
        <result column="gender" property="gender"/>
        <result column="nationality_status" property="nationalityStatus"/>
        <result column="identification_type" property="identificationType"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="date_of_birth" property="dateOfBirth"/>
        <result column="place_of_birth" property="placeOfBirth"/>
        <result column="designation_code" property="designationCode"/>
        <result column="other_designation_code" property="otherDesignationCode"/>
        <result column="employer_address_status" property="employerAddressStatus"/>
        <result column="employer_name" property="employerName"/>
        <result column="employer_address" property="employerAddress"/>
        <result column="employer_postcode" property="employerPostcode"/>
        <result column="employer_country_code" property="employerCountryCode"/>
        <result column="employer_state_code" property="employerStateCode"/>
        <result column="employer_city" property="employerCity"/>
        <result column="employer_district" property="employerDistrict"/>
        <result column="residential_address" property="residentialAddress"/>
        <result column="residential_postcode" property="residentialPostcode"/>
        <result column="residential_address_status" property="residentialAddressStatus"/>
        <result column="residential_country_code" property="residentialCountryCode"/>
        <result column="residential_state_code" property="residentialStateCode"/>
        <result column="residential_district_code" property="residentialDistrictCode"/>
        <result column="residential_city" property="residentialCity"/>
        <result column="email" property="email"/>
        <result column="telephone_number" property="telephoneNumber"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="no_tel_p" property="noTelP"/>
        <result column="membership_no" property="membershipNo"/>
        <result column="status" property="status"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="peg_harta" property="pegHarta"/>
        <result column="tarikh_tukar_su" property="tarikhTukarSu"/>
        <result column="other_position" property="otherPosition"/>
        <result column="batal_flat" property="batalFlat"/>
        <result column="blacklist_notice" property="blacklistNotice"/>
        <result column="benar_ajk" property="benarAjk"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="document_id" property="documentId"/>
        <result column="document_type" property="documentType"/>
        <result column="appointed_date" property="appointedDate"/>
        <result column="membership_registration_date" property="membershipRegistrationDate"/>
        <result column="marked_date" property="markedDate"/>
        <!-- Audit Columns -->
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <sql id="tb">
        `society_committee_archive`
    </sql>

    <sql id="selectTb">
        `society_committee_archive` sac
    </sql>

    <sql id="cols">
        `committee_table_old_id`, `job_code`, `society_id`, `society_no`, `title_code`, `name`, `gender`,
        `nationality_status`, `identification_type`, `identification_no`, `date_of_birth`, `place_of_birth`,
        `designation_code`, `other_designation_code`, `employer_address_status`, `employer_name`,
        `employer_address`, `employer_postcode`, `employer_country_code`, `employer_state_code`,
        `employer_city`, `employer_district`, `residential_address`, `residential_postcode`,
        `residential_address_status`, `residential_country_code`, `residential_state_code`,
        `residential_district_code`, `residential_city`, `email`, `telephone_number`, `phone_number`,
        `no_tel_p`, `membership_no`, `status`, `application_status_code`, `peg_harta`, `tarikh_tukar_su`,
        `other_position`, `batal_flat`, `blacklist_notice`, `benar_ajk`, `meeting_id`, `meeting_date`,
        `document_id`, `document_type`,
        `appointed_date`, `membership_registration_date`, `marked_date`,
        `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="vals">
        #{committeeTableOldId}, #{jobCode}, #{societyId}, #{societyNo}, #{titleCode}, #{name}, #{gender},
        #{nationalityStatus}, #{identificationType}, #{identificationNo}, #{dateOfBirth}, #{placeOfBirth},
        #{designationCode}, #{otherDesignationCode}, #{employerAddressStatus}, #{employerName},
        #{employerAddress}, #{employerPostcode}, #{employerCountryCode}, #{employerStateCode},
        #{employerCity}, #{employerDistrict}, #{residentialAddress}, #{residentialPostcode},
        #{residentialAddressStatus}, #{residentialCountryCode}, #{residentialStateCode},
        #{residentialDistrictCode}, #{residentialCity}, #{email}, #{telephoneNumber}, #{phoneNumber},
        #{noTelP}, #{membershipNo}, #{status}, #{applicationStatusCode}, #{pegHarta}, #{tarikhTukarSu},
        #{otherPosition}, #{batalFlat}, #{blacklistNotice}, #{benarAjk}, #{meetingId}, #{meetingDate},
        #{documentId}, #{documentType},
        #{appointedDate}, #{membershipRegistrationDate}, #{markedDate},
        #{createdBy}, #{createdDate}, #{modifiedBy}, #{modifiedDate}
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.SocietyCommitteeArchive" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="committeeTableOldId != null">committee_table_old_id,</if>
            <if test="jobCode != null">job_code,</if>
            <if test="societyId != null">society_id,</if>
            <if test="societyNo != null">society_no,</if>
            <if test="titleCode != null">title_code,</if>
            <if test="name != null">name,</if>
            <if test="gender != null">gender,</if>
            <if test="nationalityStatus != null">nationality_status,</if>
            <if test="identificationType != null">identification_type,</if>
            <if test="identificationNo != null">identification_no,</if>
            <if test="dateOfBirth != null">date_of_birth,</if>
            <if test="placeOfBirth != null">place_of_birth,</if>
            <if test="designationCode != null">designation_code,</if>
            <if test="otherDesignationCode != null">other_designation_code,</if>
            <if test="employerAddressStatus != null">employer_address_status,</if>
            <if test="employerName != null">employer_name,</if>
            <if test="employerAddress != null">employer_address,</if>
            <if test="employerPostcode != null">employer_postcode,</if>
            <if test="employerCountryCode != null">employer_country_code,</if>
            <if test="employerStateCode != null">employer_state_code,</if>
            <if test="employerCity != null">employer_city,</if>
            <if test="employerDistrict != null">employer_district,</if>
            <if test="residentialAddress != null">residential_address,</if>
            <if test="residentialPostcode != null">residential_postcode,</if>
            <if test="residentialAddressStatus != null">residential_address_status,</if>
            <if test="residentialCountryCode != null">residential_country_code,</if>
            <if test="residentialStateCode != null">residential_state_code,</if>
            <if test="residentialDistrictCode != null">residential_district_code,</if>
            <if test="residentialCity != null">residential_city,</if>
            <if test="email != null">email,</if>
            <if test="telephoneNumber != null">telephone_number,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="noTelP != null">no_tel_p,</if>
            <if test="status != null">status,</if>
            <if test="applicationStatusCode != null">application_status_code,</if>
            <if test="pegHarta != null">peg_harta,</if>
            <if test="tarikhTukarSu != null">tarikh_tukar_su,</if>
            <if test="batalFlat != null">batal_flat,</if>
            <if test="benarAjk != null">benar_ajk,</if>
            <if test="meetingId != null">meeting_id,</if>
            <if test="documentId != null">document_id,</if>
            <if test="documentType != null">document_type,</if>
            <if test="meetingDate != null">meeting_date,</if>
            <if test="markedDate != null">marked_date,</if>
            <if test="appointedDate != null">appointed_date,</if>
            <if test="membershipRegistrationDate != null">membership_registration_date,</if>
            <if test="blacklistNotice != null">blacklist_notice,</if>
            <if test="otherPosition != null">other_position,</if>
            <if test="membershipNo != null">membership_no,</if>
            <if test="createdBy != null">created_by,</if>
            created_date,
            <if test="modifiedBy != null">modified_by,</if>
            modified_date,
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="committeeTableOldId != null">#{committeeTableOldId},</if>
            <if test="jobCode != null">#{jobCode},</if>
            <if test="societyId != null">#{societyId},</if>
            <if test="societyNo != null">#{societyNo},</if>
            <if test="titleCode != null">#{titleCode},</if>
            <if test="name != null">#{name},</if>
            <if test="gender != null">#{gender},</if>
            <if test="nationalityStatus != null">#{nationalityStatus},</if>
            <if test="identificationType != null">#{identificationType},</if>
            <if test="identificationNo != null">#{identificationNo},</if>
            <if test="dateOfBirth != null">#{dateOfBirth},</if>
            <if test="placeOfBirth != null">#{placeOfBirth},</if>
            <if test="designationCode != null">#{designationCode},</if>
            <if test="otherDesignationCode != null">#{otherDesignationCode},</if>
            <if test="employerAddressStatus != null">#{employerAddressStatus},</if>
            <if test="employerName != null">#{employerName},</if>
            <if test="employerAddress != null">#{employerAddress},</if>
            <if test="employerPostcode != null">#{employerPostcode},</if>
            <if test="employerCountryCode != null">#{employerCountryCode},</if>
            <if test="employerStateCode != null">#{employerStateCode},</if>
            <if test="employerCity != null">#{employerCity},</if>
            <if test="employerDistrict != null">#{employerDistrict},</if>
            <if test="residentialAddress != null">#{residentialAddress},</if>
            <if test="residentialPostcode != null">#{residentialPostcode},</if>
            <if test="residentialAddressStatus != null">#{residentialAddressStatus},</if>
            <if test="residentialCountryCode != null">#{residentialCountryCode},</if>
            <if test="residentialStateCode != null">#{residentialStateCode},</if>
            <if test="residentialDistrictCode != null">#{residentialDistrictCode},</if>
            <if test="residentialCity != null">#{residentialCity},</if>
            <if test="email != null">#{email},</if>
            <if test="telephoneNumber != null">#{telephoneNumber},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="noTelP != null">#{noTelP},</if>
            <if test="status != null">#{status},</if>
            <if test="applicationStatusCode != null">#{applicationStatusCode},</if>
            <if test="pegHarta != null">#{pegHarta},</if>
            <if test="tarikhTukarSu != null">#{tarikhTukarSu},</if>
            <if test="batalFlat != null">#{batalFlat},</if>
            <if test="benarAjk != null">#{benarAjk},</if>
            <if test="meetingId != null">#{meetingId},</if>
            <if test="documentId != null">#{documentId},</if>
            <if test="documentType != null">#{documentType},</if>
            <if test="meetingDate != null">#{meetingDate},</if>
            <if test="markedDate != null">#{markedDate},</if>
            <if test="appointedDate != null">#{appointedDate},</if>
            <if test="membershipRegistrationDate != null">#{membershipRegistrationDate},</if>
            <if test="blacklistNotice != null">#{blacklistNotice},</if>
            <if test="otherPosition != null">#{otherPosition},</if>
            <if test="membershipNo != null">#{membershipNo},</if>
            <if test="createdBy != null">#{createdBy},</if>
            now(),
            <if test="modifiedBy != null">#{modifiedBy},</if>
            now(),
        </trim>
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.SocietyCommitteeArchive">
        UPDATE <include refid="tb"/>
        <set>
            <if test="committeeTableOldId != null">`committee_table_old_id` = #{committeeTableOldId},</if>
            <if test="jobCode != null">`job_code` = #{jobCode},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="titleCode != null">`title_code` = #{titleCode},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="gender != null">`gender` = #{gender},</if>
            <if test="nationalityStatus != null">`nationality_status` = #{nationalityStatus},</if>
            <if test="identificationType != null">`identification_type` = #{identificationType},</if>
            <if test="identificationNo != null">`identification_no` = #{identificationNo},</if>
            <if test="dateOfBirth != null">`date_of_birth` = #{dateOfBirth},</if>
            <if test="placeOfBirth != null">`place_of_birth` = #{placeOfBirth},</if>
            <if test="designationCode != null">`designation_code` = #{designationCode},</if>
            <if test="otherDesignationCode != null">`other_designation_code` = #{otherDesignationCode},</if>
            <if test="employerAddressStatus != null">`employer_address_status` = #{employerAddressStatus},</if>
            <if test="employerName != null">`employer_name` = #{employerName},</if>
            <if test="employerAddress != null">`employer_address` = #{employerAddress},</if>
            <if test="employerPostcode != null">`employer_postcode` = #{employerPostcode},</if>
            <if test="employerCountryCode != null">`employer_country_code` = #{employerCountryCode},</if>
            <if test="employerStateCode != null">`employer_state_code` = #{employerStateCode},</if>
            <if test="employerCity != null">`employer_city` = #{employerCity},</if>
            <if test="employerDistrict != null">`employer_district` = #{employerDistrict},</if>
            <if test="residentialAddress != null">`residential_address` = #{residentialAddress},</if>
            <if test="residentialPostcode != null">`residential_postcode` = #{residentialPostcode},</if>
            <if test="residentialAddressStatus != null">`residential_address_status` = #{residentialAddressStatus},</if>
            <if test="residentialCountryCode != null">`residential_country_code` = #{residentialCountryCode},</if>
            <if test="residentialStateCode != null">`residential_state_code` = #{residentialStateCode},</if>
            <if test="residentialDistrictCode != null">`residential_district_code` = #{residentialDistrictCode},</if>
            <if test="residentialCity != null">`residential_city` = #{residentialCity},</if>
            <if test="email != null">`email` = #{email},</if>
            <if test="telephoneNumber != null">`telephone_number` = #{telephoneNumber},</if>
            <if test="phoneNumber != null">`phone_number` = #{phoneNumber},</if>
            <if test="noTelP != null">`no_tel_p` = #{noTelP},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="pegHarta != null">`peg_harta` = #{pegHarta},</if>
            <if test="tarikhTukarSu != null">`tarikh_tukar_su` = #{tarikhTukarSu},</if>
            <if test="batalFlat != null">`batal_flat` = #{batalFlat},</if>
            <if test="benarAjk != null">`benar_ajk` = #{benarAjk},</if>
            <if test="meetingId != null">`meeting_id` = #{meetingId},</if>
            <if test="documentId != null">`document_id` = #{documentId},</if>
            <if test="documentType != null">`document_type` = #{documentType},</if>
            <if test="meetingDate != null">`meeting_date` = #{meetingDate},</if>
            <if test="markedDate != null">`marked_date` = #{markedDate},</if>
            <if test="appointedDate != null">`appointed_date` = #{appointedDate},</if>
            <if test="membershipRegistrationDate != null">`membership_registration_date` = #{membershipRegistrationDate},</if>
            <if test="blacklistNotice != null">`blacklist_notice` = #{blacklistNotice},</if>
            <if test="otherPosition != null">`other_position` = #{otherPosition},</if>
            <if test="membershipNo != null">`membership_no` = #{membershipNo},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            `modified_date` = NOW()
        </set>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="committeeTableOldId != null">
            AND `committee_table_old_id` = #{committeeTableOldId}
        </if>
    </update>

    <update id="updateCommitteeArchive" parameterType="com.eroses.external.society.model.SocietyCommitteeArchive">
        UPDATE <include refid="tb"/>
        <set>
            <if test="committeeTableOldId != null">`committee_table_old_id` = #{committeeTableOldId},</if>
            <if test="jobCode != null">`job_code` = #{jobCode},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="titleCode != null">`title_code` = #{titleCode},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="gender != null">`gender` = #{gender},</if>
            <if test="nationalityStatus != null">`nationality_status` = #{nationalityStatus},</if>
            <if test="identificationType != null">`identification_type` = #{identificationType},</if>
            <if test="identificationNo != null">`identification_no` = #{identificationNo},</if>
            <if test="dateOfBirth != null">`date_of_birth` = #{dateOfBirth},</if>
            <if test="placeOfBirth != null">`place_of_birth` = #{placeOfBirth},</if>
            <if test="designationCode != null">`designation_code` = #{designationCode},</if>
            <if test="otherDesignationCode != null">`other_designation_code` = #{otherDesignationCode},</if>
            <if test="employerAddressStatus != null">`employer_address_status` = #{employerAddressStatus},</if>
            <if test="employerName != null">`employer_name` = #{employerName},</if>
            <if test="employerAddress != null">`employer_address` = #{employerAddress},</if>
            <if test="employerPostcode != null">`employer_postcode` = #{employerPostcode},</if>
            <if test="employerCountryCode != null">`employer_country_code` = #{employerCountryCode},</if>
            <if test="employerStateCode != null">`employer_state_code` = #{employerStateCode},</if>
            <if test="employerCity != null">`employer_city` = #{employerCity},</if>
            <if test="employerDistrict != null">`employer_district` = #{employerDistrict},</if>
            <if test="residentialAddress != null">`residential_address` = #{residentialAddress},</if>
            <if test="residentialPostcode != null">`residential_postcode` = #{residentialPostcode},</if>
            <if test="residentialAddressStatus != null">`residential_address_status` = #{residentialAddressStatus},</if>
            <if test="residentialCountryCode != null">`residential_country_code` = #{residentialCountryCode},</if>
            <if test="residentialStateCode != null">`residential_state_code` = #{residentialStateCode},</if>
            <if test="residentialDistrictCode != null">`residential_district_code` = #{residentialDistrictCode},</if>
            <if test="residentialCity != null">`residential_city` = #{residentialCity},</if>
            <if test="email != null">`email` = #{email},</if>
            <if test="telephoneNumber != null">`telephone_number` = #{telephoneNumber},</if>
            <if test="phoneNumber != null">`phone_number` = #{phoneNumber},</if>
            <if test="noTelP != null">`no_tel_p` = #{noTelP},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="pegHarta != null">`peg_harta` = #{pegHarta},</if>
            <if test="tarikhTukarSu != null">`tarikh_tukar_su` = #{tarikhTukarSu},</if>
            <if test="batalFlat != null">`batal_flat` = #{batalFlat},</if>
            <if test="benarAjk != null">`benar_ajk` = #{benarAjk},</if>
            <if test="meetingId != null">`meeting_id` = #{meetingId},</if>
            <if test="documentId != null">`document_id` = #{documentId},</if>
            <if test="documentType != null">`document_type` = #{documentType},</if>
            <if test="meetingDate != null">`meeting_date` = #{meetingDate},</if>
            <if test="markedDate != null">`marked_date` = #{markedDate},</if>
            <if test="appointedDate != null">`appointed_date` = #{appointedDate},</if>
            <if test="membershipRegistrationDate != null">`membership_registration_date` = #{membershipRegistrationDate},</if>
            <if test="blacklistNotice != null">`blacklist_notice` = #{blacklistNotice},</if>
            <if test="otherPosition != null">`other_position` = #{otherPosition},</if>
            <if test="membershipNo != null">`membership_no` = #{membershipNo},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            `modified_date` = NOW()
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="getCommitteeArchive" parameterType="map" resultMap="SocietyCommitteeArchiveMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="name != null and name != ''">
            AND `name` = #{name}
        </if>
        <if test="identificationNo != null and identificationNo != ''">
            AND `identification_no` = #{identificationNo}
        </if>
        <if test="meetingId != null">
            AND `meeting_id` = #{meetingId}
        </if>
        <if test="meetingDate != null">
            AND `meeting_date` = #{meetingDate}
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="markedDate != null">
            AND `marked_date` = #{markedDate}
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        <if test="status != null">
            AND `status` = #{status}
        </if>
        ORDER BY meeting_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="findByCommitteeOldId" parameterType="java.lang.Long" resultMap="SocietyCommitteeArchiveMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `committee_table_old_id` = #{committeeTableOldId}
    </select>

    <select id="findByParams" parameterType="map" resultMap="SocietyCommitteeArchiveMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="roleIds != null">
            AND `designation_code` IN (
            <foreach item="value" index="key" collection="roleIds" separator=",">
                #{value}
            </foreach>
            )
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="position != null">
            AND `designation_code` = #{position}
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
    </select>

    <select id="findOneByParams" parameterType="map" resultMap="SocietyCommitteeArchiveMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="committeeTableOldId != null">
            AND `committee_table_old_id` = #{committeeTableOldId}
        </if>
        <if test="societyId != null and societyId !=''">
            AND `society_id` = #{societyId}
        </if>
        <if test="position != null and position !=''">
            AND `designation_code` = #{position}
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        <if test="markedDate != null">
            AND `marked_date` = #{markedDate}
        </if>
        <if test="identificationNo != null and identificationNo !=''">
            AND `identification_no` = #{identificationNo}
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
        LIMIT 1
    </select>

    <select id="findAllAppointedDates" resultType="java.time.LocalDate">
        SELECT DISTINCT `appointed_date`
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
        AND `application_status_code` = 11
    </select>

    <select id="findByParam" parameterType="map" resultMap="SocietyCommitteeArchiveMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="meetingId != null">
            AND `meeting_id` = #{meetingId}
        </if>
        <if test="documentId != null">
            AND `document_id` = #{documentId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
    </select>

    <select id="findActiveCommitteesInSocietyArchiveWithRoles" parameterType="map" resultMap="SocietyCommitteeArchiveMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
        AND `status` = #{status}
        AND `application_status_code` = #{applicationStatusCode}
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        AND `designation_code` IN
        <foreach separator="," collection="positions" item="position" open="(" close=")">
            #{position}
        </foreach>
    </select>

<!--    <select id="findClosestMeetingDate" parameterType="java.time.LocalDate" resultMap="SocietyCommitteeArchiveMap">-->
<!--        SELECT <include refid="colsWithId"/>-->
<!--        FROM <include refid="tb"/>-->
<!--        WHERE `meeting_date` <= #{meetingDate}-->
<!--        ORDER BY meeting_date DESC-->
<!--        LIMIT 1-->
<!--    </select>-->

</mapper>