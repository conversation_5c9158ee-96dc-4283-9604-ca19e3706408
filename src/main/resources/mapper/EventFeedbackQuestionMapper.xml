<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eroses.external.society.mappers.EventFeedbackQuestionDao">

    <resultMap id="EventFeedbackQuestionMap" type="com.eroses.external.society.model.EventFeedbackQuestion">
        <id property="id" column="id"/>
        <result property="eventId" column="event_id"/>
        <result property="feedbackQuestionId" column="feedback_question_id"/>
        <result property="createdBy" column="created_by" />
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        event_feedback_question
    </sql>

    <sql id="cols">
        event_id,
        feedback_question_id,
        created_by,
        modified_by
    </sql>

    <sql id="getCols">
        id,
        event_id,
        feedback_question_id,
        created_date,
        modified_by,
        created_by,
        modified_date
    </sql>

    <sql id="vals">
        #{eventId},
        #{feedbackQuestionId},
        #{createdBy},
        #{modifiedBy}
    </sql>
    <sql id="createVals">
        #{item.eventId},
        #{item.feedbackQuestionId},
        #{item.createdBy},
        #{item.modifiedBy}
    </sql>


<!--    <insert id="create" parameterType="com.eroses.external.society.model.EventFeedbackQuestion" keyProperty="id"-->
<!--            useGeneratedKeys="true">-->
<!--        INSERT INTO-->
<!--        <include refid="tb"/>-->
<!--        (<include refid="cols"/>, created_date)-->
<!--        VALUES (<include refid="vals"/>, NOW())-->
<!--    </insert>-->
        <insert id="create" parameterType="java.util.List" keyProperty="id" useGeneratedKeys="true">
            INSERT INTO <include refid="tb"/>
            (<include refid="cols"/>, created_date)
            VALUES
            <foreach collection="list" item="item" separator=",">
                (<include refid="createVals"/>, NOW())
            </foreach>
        </insert>

    <delete id="deleteByEventAndQuestionId" parameterType="java.util.List">
        DELETE FROM <include refid="tb"/>
        WHERE (event_id, feedback_question_id) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.eventId}, #{item.feedbackQuestionId})
        </foreach>
    </delete>


    <select id="findByEventId" resultType="list" resultMap="EventFeedbackQuestionMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        event_id = #{eventId}
    </select>
    <select id="findById" resultType="list" resultMap="EventFeedbackQuestionMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        id = #{id}
    </select>

    <delete id="deleteByEventId">
        DELETE FROM <include refid="tb"/>
        WHERE event_id = #{eventId}
    </delete>




</mapper>
