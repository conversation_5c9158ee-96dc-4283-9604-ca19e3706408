<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="PropertyOfficer">

    <resultMap id="PropertyOfficerItemResultMap" type="com.eroses.external.society.model.PropertyOfficer">
        <id property="id" column="po_id"/>
        <result property="createdBy" column="po_created_by"/>
        <result property="createdDate" column="po_created_date"/>
        <result property="modifiedBy" column="po_modified_by"/>
        <result property="modifiedDate" column="po_modified_date"/>
        <result property="societyCommitteeId" column="po_society_committee_id"/>
        <result property="name" column="po_name"/>
        <result property="email" column="po_email"/>
        <result property="identificationNo" column="po_identification_no"/>
        <result property="branchCommitteeId" column="po_branch_committee_id"/>
        <result property="propertyOfficerApplicationId" column="po_property_officer_application_id"/>
    </resultMap>

    <resultMap id="SinglePropertyOfficerApplicationSelectResultMap" type="com.eroses.external.society.model.PropertyOfficerApplication">
        <id property="id" column="poa_id"/>
        <result property="societyId" column="poa_society_id"/>
        <result property="societyNo" column="poa_society_no"/>
        <result property="branchId" column="poa_branch_id"/>
        <result property="branchNo" column="poa_branch_no"/>
        <result property="appointmentDate" column="poa_appointment_date"/>
        <result property="agreementAcknowledgment" column="poa_agreement_acknowledgment"/>
        <result property="acknowledgmentDate" column="poa_acknowledgment_date"/>
        <result property="submissionDate" column="poa_submission_date"/>
        <result property="paymentMethod" column="poa_payment_method"/>
        <result property="paymentId" column="poa_payment_id"/>
        <result property="paymentDate" column="poa_payment_date"/>
        <result property="receiptNumber" column="poa_receipt_number"/>
        <result property="ePaymentId" column="poa_epayment_id"/>
        <result property="bankName" column="poa_bank_name"/>
        <result property="bankReferenceNumber" column="poa_bank_reference_number"/>
        <result property="applicationStatusCode" column="poa_application_status_code"/>
        <result property="status" column="poa_status"/>
        <result property="branchOfficer" column="poa_branch_officer"/>
        <result property="ro" column="poa_ro"/>
        <result property="flowDate" column="poa_flow_date"/>
        <result property="approver" column="poa_approver"/>
        <result property="reconcileDate" column="poa_reconcile_date"/>
        <result property="roNote" column="poa_ro_note"/>

        <result property="createdBy" column="poa_created_by"/>
        <result property="createdDate" column="poa_created_date"/>
        <result property="modifiedBy" column="poa_modified_by"/>
        <result property="modifiedDate" column="poa_modified_date"/>

        <association property="society" javaType="com.eroses.external.society.model.Society"
                     resultMap="Society.SocietySelectMap"/>
    </resultMap>

    <resultMap id="PropertyOfficerApplicationSelectResultMap" type="com.eroses.external.society.model.PropertyOfficerApplication">
        <id property="id" column="poa_id"/>
        <result property="societyId" column="poa_society_id"/>
        <result property="societyNo" column="poa_society_no"/>
        <result property="branchId" column="poa_branch_id"/>
        <result property="branchNo" column="poa_branch_no"/>
        <result property="appointmentDate" column="poa_appointment_date"/>
        <result property="agreementAcknowledgment" column="poa_agreement_acknowledgment"/>
        <result property="acknowledgmentDate" column="poa_acknowledgment_date"/>
        <result property="submissionDate" column="poa_submission_date"/>
        <result property="paymentMethod" column="poa_payment_method"/>
        <result property="paymentId" column="poa_payment_id"/>
        <result property="paymentDate" column="poa_payment_date"/>
        <result property="receiptNumber" column="poa_receipt_number"/>
        <result property="ePaymentId" column="poa_epayment_id"/>
        <result property="bankName" column="poa_bank_name"/>
        <result property="bankReferenceNumber" column="poa_bank_reference_number"/>
        <result property="applicationStatusCode" column="poa_application_status_code"/>
        <result property="status" column="poa_status"/>
        <result property="branchOfficer" column="poa_branch_officer"/>
        <result property="ro" column="poa_ro"/>
        <result property="flowDate" column="poa_flow_date"/>
        <result property="approver" column="poa_approver"/>
        <result property="reconcileDate" column="poa_reconcile_date"/>
        <result property="roNote" column="poa_ro_note"/>

        <result property="createdBy" column="poa_created_by"/>
        <result property="createdDate" column="poa_created_date"/>
        <result property="modifiedBy" column="poa_modified_by"/>
        <result property="modifiedDate" column="poa_modified_date"/>

        <collection property="propertyOfficers" resultMap="PropertyOfficerItemResultMap" ofType="com.eroses.external.society.model.PropertyOfficer"/>

    </resultMap>

    <resultMap id="PropertyOfficerApplicationMap" type="com.eroses.external.society.model.PropertyOfficerApplication">
        <id property="id" column="id"/>
        <result property="societyNo" column="society_no"/>
        <result property="societyId" column="society_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="branchId" column="branch_id"/>
        <result property="appointmentDate" column="appointment_date"/>
        <result property="agreementAcknowledgment" column="agreement_acknowledgment"/>
        <result property="acknowledgmentDate" column="acknowledgment_date"/>
        <result property="submissionDate" column="submission_date"/>
        <result property="paymentMethod" column="payment_method"/>
        <result property="paymentId" column="payment_id"/>
        <result property="receiptNumber" column="receipt_number"/>
        <result property="paymentDate" column="payment_date"/>
        <result property="ePaymentId" column="epayment_id"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankReferenceNumber" column="bank_reference_number"/>
        <result property="applicationStatusCode" column="application_status_code"/>
        <result property="status" column="status"/>
        <result property="receiptStatus" column="receipt_status"/>
        <result property="branchOfficer" column="branch_officer"/>
        <result property="ro" column="ro"/>
        <result property="flowDate" column="flow_date"/>
        <result property="approver" column="approver"/>
        <result property="reconcileDate" column="reconcile_date"/>
        <result property="roNote" column="ro_note"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>

    <resultMap id="PropertyOfficerResultMap" type="com.eroses.external.society.model.PropertyOfficer">
        <id property="id" column="id"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
        <result property="societyCommitteeId" column="society_committee_id"/>
        <result property="name" column="name"/>
        <result property="email" column="email"/>
        <result property="identificationNo" column="identification_no"/>
        <result property="branchCommitteeId" column="branch_committee_id"/>
        <result property="propertyOfficerApplicationId" column="property_officer_application_id"/>
    </resultMap>

    <resultMap id="BranchPendingPropertyOfficerApplicationResultMap" type="com.eroses.external.society.model.PropertyOfficerApplication">
        <id property="id" column="poa_id"/>
        <result property="societyId" column="poa_society_id"/>
        <result property="societyNo" column="poa_society_no"/>
        <result property="branchId" column="poa_branch_id"/>
        <result property="branchNo" column="poa_branch_no"/>
        <result property="appointmentDate" column="poa_appointment_date"/>
        <result property="agreementAcknowledgment" column="poa_agreement_acknowledgment"/>
        <result property="acknowledgmentDate" column="poa_acknowledgment_date"/>
        <result property="submissionDate" column="poa_submission_date"/>
        <result property="paymentMethod" column="poa_payment_method"/>
        <result property="paymentId" column="poa_payment_id"/>
        <result property="paymentDate" column="poa_payment_date"/>
        <result property="receiptNumber" column="poa_receipt_number"/>
        <result property="ePaymentId" column="poa_epayment_id"/>
        <result property="bankName" column="poa_bank_name"/>
        <result property="bankReferenceNumber" column="poa_bank_reference_number"/>
        <result property="applicationStatusCode" column="poa_application_status_code"/>
        <result property="status" column="poa_status"/>
        <result property="branchOfficer" column="poa_branch_officer"/>
        <result property="ro" column="poa_ro"/>
        <result property="flowDate" column="poa_flow_date"/>
        <result property="approver" column="poa_approver"/>
        <result property="reconcileDate" column="poa_reconcile_date"/>
        <result property="roNote" column="poa_ro_note"/>
        <result property="createdBy" column="poa_created_by"/>
        <result property="createdDate" column="poa_created_date"/>
        <result property="modifiedBy" column="poa_modified_by"/>
        <result property="modifiedDate" column="poa_modified_date"/>
        <association property="branch" resultMap="Branch.BranchSelectMap"/>

        <collection property="propertyOfficers" resultMap="PropertyOfficerItemResultMap" ofType="com.eroses.external.society.model.PropertyOfficer"/>
    </resultMap>

    <resultMap id="PropertyOfficerSelectResultMap" type="com.eroses.external.society.model.PropertyOfficer">
        <id property="id" column="po_id"/>
        <result property="createdBy" column="po_created_by"/>
        <result property="createdDate" column="po_created_date"/>
        <result property="modifiedBy" column="po_modified_by"/>
        <result property="modifiedDate" column="po_modified_date"/>
        <result property="name" column="po_name"/>
        <result property="identificationNo" column="po_identification_no"/>
        <result property="societyCommitteeId" column="po_society_committee_id"/>
        <result property="propertyOfficerApplicationId" column="po_property_officer_application_id"/>
        <result property="email" column="po_email"/>
        <result property="branchCommitteeId" column="po_branch_committee_id"/>
        <!-- Association with PropertyOfficerApplication -->
        <association property="propertyOfficerApplication" javaType="com.eroses.external.society.model.PropertyOfficerApplication">
            <id property="id" column="poa_id"/>
            <result property="societyId" column="poa_society_id"/>
            <result property="societyNo" column="poa_society_no"/>
            <result property="branchId" column="poa_branch_id"/>
            <result property="branchNo" column="poa_branch_no"/>
            <result property="appointmentDate" column="poa_appointment_date"/>
            <result property="agreementAcknowledgment" column="poa_agreement_acknowledgment"/>
            <result property="acknowledgmentDate" column="poa_acknowledgment_date"/>
            <result property="submissionDate" column="poa_submission_date"/>
            <result property="paymentMethod" column="poa_payment_method"/>
            <result property="paymentDate" column="poa_payment_date"/>
            <result property="receiptStatus" column="poa_receipt_status"/>
            <result property="paymentId" column="poa_payment_id"/>
            <result property="receiptNumber" column="poa_receipt_number"/>
            <result property="ePaymentId" column="poa_epayment_id"/>
            <result property="bankName" column="poa_bank_name"/>
            <result property="bankReferenceNumber" column="poa_bank_reference_number"/>
            <result property="applicationStatusCode" column="poa_application_status_code"/>
            <result property="status" column="poa_status"/>
            <result property="branchOfficer" column="poa_branch_officer"/>
            <result property="ro" column="poa_ro"/>
            <result property="flowDate" column="poa_flow_date"/>
            <result property="approver" column="poa_approver"/>
            <result property="reconcileDate" column="poa_reconcile_date"/>
            <result property="roNote" column="poa_ro_note"/>
            <result property="createdBy" column="poa_created_by"/>
            <result property="createdDate" column="poa_created_date"/>
            <result property="modifiedBy" column="poa_modified_by"/>
            <result property="modifiedDate" column="poa_modified_date"/>
        </association>
    </resultMap>

    <update id="updatePropertyOfficer" parameterType="com.eroses.external.society.model.PropertyOfficer">
        UPDATE `property_officer`
        <set>
            <if test="createdBy != null">`created_by` = #{createdBy},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="email != null and email != ''">`email` = #{email},</if>
            <if test="identificationNo != null">`identification_no` = #{identificationNo},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <sql id="tbl">`property_officer`</sql>
    <sql id="poaTb">`property_officer_application`</sql>
    <sql id="poaSelectTb">`property_officer_application` `poa`</sql>
    <sql id="poaSelectCols">
        `poa`.`id` AS `poa_id`,
        `poa`.`society_id` AS `poa_society_id`,
        `poa`.`society_no` AS `poa_society_no`,
        `poa`.`branch_id` AS `poa_branch_id`,
        `poa`.`branch_no` AS `poa_branch_no`,
        `poa`.`appointment_date` AS `poa_appointment_date`,
        `poa`.`agreement_acknowledgment` AS `poa_agreement_acknowledgment`,
        `poa`.`acknowledgment_date` AS `poa_acknowledgment_date`,
        `poa`.`submission_date` AS `poa_submission_date`,
        `poa`.`payment_method` AS `poa_payment_method`,
        `poa`.`payment_id` AS `poa_payment_id`,
        `poa`.`payment_date` AS `poa_payment_date`,
        `poa`.`receipt_number` AS `poa_receipt_number`,
        `poa`.`epayment_id` AS `poa_epayment_id`,
        `poa`.`bank_name` AS `poa_bank_name`,
        `poa`.`bank_reference_number` AS `poa_bank_reference_number`,
        `poa`.`application_status_code` AS `poa_application_status_code`,
        `poa`.`status` AS `poa_status`,
        `poa`.`created_by` AS `poa_created_by`,
        `poa`.`created_date` AS `poa_created_date`,
        `poa`.`modified_by` AS `poa_modified_by`,
        `poa`.`modified_date` AS `poa_modified_date`,
        `poa`.`branch_officer` AS `poa_branch_officer`,
        `poa`.`ro` AS `poa_ro`,
        `poa`.`flow_date` AS `poa_flow_date`,
        `poa`.`approver` AS `poa_approver`,
        `poa`.`reconcile_date` AS `poa_reconcile_date`,
        `poa`.`ro_note` AS `poa_ro_note`
    </sql>

    <sql id="poSelectTb">`property_officer` `po`</sql>
    <sql id="poSelectCols">
        `po`.`id` AS `po_id`,
        `po`.`created_by` AS `po_created_by`,
        `po`.`created_date` AS `po_created_date`,
        `po`.`modified_by` AS `po_modified_by`,
        `po`.`modified_date` AS `po_modified_date`,
        `po`.`name` AS `po_name`,
        `po`.`identification_no` AS `po_identification_no`,
        `po`.`society_committee_id` AS `po_society_committee_id`,
        `po`.`branch_committee_id` AS `po_branch_committee_id`,
        `po`.`email` as `po_email`,
        `po`.`property_officer_application_id` AS `po_property_officer_application_id`
    </sql>

    <select id="getAllSocietyPending" parameterType="map" resultMap="PropertyOfficerApplicationSelectResultMap">
        SELECT <include refid="poSelectCols"/>,
        <include refid="poaSelectCols"/>
        FROM (
            SELECT poa.*
            FROM <include refid="poaSelectTb"/>
            INNER JOIN <include refid="Society.selectTb"/> ON s.id = poa.society_id
            WHERE 1=1
            AND poa.application_status_code = #{applicationStatusCode}
            AND poa.branch_id IS NULL
            <if test="stateCode != null and stateCode !=''">
                AND s.state_id = #{stateCode}
            </if>
            <if test="ro != 0 and ro != ''">
                AND poa.ro = #{ro}
            </if>
            <if test="categoryCode != null and categoryCode !=''">
                AND s.category_code_jppm = #{categoryCode}
            </if>
            <if test="subCategoryCode != null and subCategoryCode !=''">
                AND s.sub_category_code = #{subCategoryCode}
            </if>
            <if test="societyName != null and societyName != ''">
                AND s.society_name LIKE CONCAT('%', #{societyName}, '%')
            </if>
            <if test="deleteApplicationStatusCode != null">
                AND poa.application_status_code != #{deleteApplicationStatusCode}
            </if>
            ORDER BY poa.payment_date DESC
            <if test="offset != null and limit != null">
                LIMIT #{offset}, #{limit}
            </if>
        ) poa
        INNER JOIN <include refid="poSelectTb"/> ON po.property_officer_application_id = poa.id
        INNER JOIN <include refid="Society.selectTb"/> ON s.id = poa.society_id
        ORDER BY poa.payment_date DESC
    </select>

    <select id="getAllBranchPending" parameterType="map" resultMap="BranchPendingPropertyOfficerApplicationResultMap">
        SELECT  <include refid="poSelectCols"/>,
        <include refid="poaSelectCols"/>,
        <include refid="Branch.selectCols"/>
        FROM <include refid="poSelectTb"/>
        INNER JOIN <include refid="poaSelectTb"/> ON `po`.`property_officer_application_id` = `poa`.`id`
        INNER JOIN <include refid="Branch.selectTb"/> ON `b`.`id` = `poa`.`branch_id`
        WHERE 1=1
        AND `poa`.`application_status_code` = #{applicationStatusCode}
        <if test="stateCode != null and stateCode !=''">
            AND `b`.`state_id` = #{stateCode}
        </if>
        <if test="stateCodeFilter != null and stateCodeFilter !=''">
            AND `b`.`state_id` = #{stateCodeFilter}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND b.name LIKE CONCAT('%', #{searchQuery}, '%')
        </if>
        <if test="ro != 0 and ro != ''">
            AND `poa`.`ro` = #{ro}
        </if>
        <if test="deleteApplicationStatusCode != null">
            AND `poa`.`application_status_code` != #{deleteApplicationStatusCode}
        </if>
        ORDER BY poa.payment_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllSocietyPending" parameterType="map" resultType="long">
        SELECT COUNT(DISTINCT poa.id)
        FROM (
        SELECT poa.*
        FROM <include refid="poaSelectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.id = poa.society_id
        WHERE 1=1
        AND poa.application_status_code = #{applicationStatusCode}
        AND poa.branch_id IS NULL
        <if test="stateCode != null and stateCode !=''">
            AND s.state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND poa.ro = #{ro}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND s.category_code_jppm = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND s.sub_category_code = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND s.society_name LIKE CONCAT('%', #{societyName}, '%')
        </if>
        <if test="deleteApplicationStatusCode != null">
            AND poa.application_status_code != #{deleteApplicationStatusCode}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
        ) poa
        INNER JOIN <include refid="poSelectTb"/> ON po.property_officer_application_id = poa.id
        INNER JOIN <include refid="Society.selectTb"/> ON s.id = poa.society_id
    </select>

    <select id="countAllBranchPending" parameterType="map" resultType="long">
        SELECT COUNT(DISTINCT poa.id)
        FROM <include refid="poSelectTb"/>
        INNER JOIN <include refid="poaSelectTb"/> ON `po`.`property_officer_application_id` = `poa`.`id`
        INNER JOIN <include refid="Branch.selectTb"/> ON `b`.`id` = `poa`.`branch_id`
        WHERE 1=1
        AND `poa`.`application_status_code` = #{applicationStatusCode}
        <if test="stateCode != null and stateCode !=''">
            AND `b`.`state_id` = #{stateCode}
        </if>
        <if test="stateCodeFilter != null and stateCodeFilter !=''">
            AND `b`.`state_id` = #{stateCodeFilter}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND b.name LIKE CONCAT('%', #{searchQuery}, '%')
        </if>
        <if test="ro != 0 and ro != ''">
            AND `poa`.`ro` = #{ro}
        </if>
        <if test="deleteApplicationStatusCode != null">
            AND `poa`.`application_status_code` != #{deleteApplicationStatusCode}
        </if>
    </select>

    <select id="getAll" parameterType="map" resultMap="PropertyOfficerSelectResultMap">
        SELECT
        <include refid="poSelectCols"/>,
        <include refid="poaSelectCols"/>
        FROM
        <include refid="poSelectTb"/>
        INNER JOIN
        <include refid="poaSelectTb"/>
        ON `po`.`property_officer_application_id` = `poa`.`id`
        WHERE 1=1
        AND `poa`.`society_id` = #{societyId}
        <choose>
            <when test="branchId==null">
                `poa`.`branch_id` IS NULL
            </when>
            <otherwise>
                `poa`.`branch_id` = #{branchId}
            </otherwise>
        </choose>
        <if test="applicationStatusCode != null and applicationStatusCode != ''">
            AND `poa`.`application_status_code` != #{applicationStatusCode}
        </if>
        LIMIT #{limit} OFFSET #{offset}
    </select>
    <select id="countAll" parameterType="long" resultType="long">
        SELECT count(*)
        FROM
        <include refid="poSelectTb"/>
        INNER JOIN
        <include refid="poaSelectTb"/>
        ON `po`.`property_officer_application_id` = `poa`.`id`
        WHERE 1=1
        AND `poa`.`society_id` = #{societyId}
        <choose>
            <when test="branchId==null">
                `poa`.`branch_id` IS NULL
            </when>
            <otherwise>
                `poa`.`branch_id` = #{branchId}
            </otherwise>
        </choose>
        <if test="applicationStatusCode != null and applicationStatusCode != ''">
            AND `poa`.`application_status_code` != #{applicationStatusCode}
        </if>
    </select>

    <select id="findByPaymentId" parameterType="long" resultMap="PropertyOfficerApplicationSelectResultMap">
        SELECT
        <include refid="poSelectCols"/>,
        <include refid="poaSelectCols"/>
        FROM
        <include refid="poSelectTb"/>
        INNER JOIN
        <include refid="poaSelectTb"/>
        ON `po`.`property_officer_application_id` = `poa`.`id`
        WHERE 1=1
        AND `poa`.`payment_id` = #{paymentId}
    </select>
    <insert id="createPropertyOfficerApplication" parameterType="com.eroses.external.society.model.PropertyOfficerApplication" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `property_officer_application` (
        `society_id`,
        `society_no`,
        `branch_id`,
        `branch_no`,
        `appointment_date`,
        `agreement_acknowledgment`,
        `acknowledgment_date`,
        `submission_date`,
        `payment_method`,
        `payment_id`,
        `receipt_number`,
        `epayment_id`,
        `bank_name`,
        `bank_reference_number`,
        `application_status_code`,
        `status`,
        `created_by`,
        `modified_by`,
        `branch_officer`,
        `ro`,
        `flow_date`,
        `approver`,
        `reconcile_date`,
        `ro_note`
        ) VALUES (
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{appointmentDate},
        #{agreementAcknowledgment},
        #{acknowledgmentDate},
        #{submissionDate},
        #{paymentMethod},
        #{paymentId},
        #{receiptNumber},
        #{ePaymentId},
        #{bankName},
        #{bankReferenceNumber},
        #{applicationStatusCode},
        #{status},
        #{createdBy},
        #{modifiedBy},
        #{branchOfficer},
        #{ro},
        #{flowDate},
        #{approver},
        #{reconcileDate},
        #{roNote}
        )
    </insert>

    <insert id="createPropertyOfficers" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `property_officer` (
        `created_by`,
        `modified_by`,
        `name`,
        `email`,
        `identification_no`,
        `property_officer_application_id`,
        `society_committee_id`,
        `branch_committee_id`
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.createdBy},
            #{item.modifiedBy},
            #{item.name},
            #{item.email},
            #{item.identificationNo},
            #{item.propertyOfficerApplicationId},
            #{item.societyCommitteeId},
            #{item.branchCommitteeId}
            )
        </foreach>
    </insert>

    <select id="findApplicationById" parameterType="long" resultMap="PropertyOfficerApplicationSelectResultMap">
        SELECT <include refid="poaSelectCols"/>
        FROM <include refid="poaSelectTb"/>
        WHERE `poa`.`id`=#{id}
    </select>

    <update id="updatePropertyOfficerApplication">
        UPDATE <include refid="poaTb"/>
        <set>
            <if test="societyNo != null and societyNo != ''">society_no = #{societyNo},</if>
            <if test="societyId != null and societyId != ''">society_id = #{societyId},</if>
            <if test="branchNo != null and branchNo != ''">branch_no = #{branchNo},</if>
            <if test="branchId != null and branchId != ''">branch_id = #{branchId},</if>
            <if test="appointmentDate != null">appointment_date = #{appointmentDate},</if>
            <if test="agreementAcknowledgment != null and agreementAcknowledgment != ''">agreement_acknowledgment = #{agreementAcknowledgment},</if>
            <if test="acknowledgmentDate != null">acknowledgment_date = #{acknowledgmentDate},</if>
            <if test="submissionDate != null">submission_date = #{submissionDate},</if>
            <if test="paymentMethod != null and paymentMethod != ''">payment_method = #{paymentMethod},</if>
            <if test="paymentId != null and paymentId != ''">payment_id = #{paymentId},</if>
            <if test="receiptNumber != null and receiptNumber != ''">receipt_number = #{receiptNumber},</if>
            <if test="paymentDate != null">payment_date = #{paymentDate},</if>
            <if test="ePaymentId != null and ePaymentId != ''">epayment_id = #{ePaymentId},</if>
            <if test="bankName != null and bankName != ''">bank_name = #{bankName},</if>
            <if test="bankReferenceNumber != null and bankReferenceNumber != ''">bank_reference_number = #{bankReferenceNumber},</if>
            <if test="applicationStatusCode != null and applicationStatusCode != ''">application_status_code = #{applicationStatusCode},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createdBy != null and createdBy != ''">created_by = #{createdBy},</if>
            <if test="receiptStatus != null and receiptStatus != ''">receipt_status = #{receiptStatus},</if>
            <if test="branchOfficer != null and branchOfficer != ''">branch_officer = #{branchOfficer},</if>
            <if test="ro != null and ro != ''">ro = #{ro},</if>
            <if test="flowDate != null">flow_date = #{flowDate},</if>
            <if test="approver != null and approver != ''">approver = #{approver},</if>
            <if test="reconcileDate != null">reconcile_date = #{reconcileDate},</if>
            <if test="roNote != null and roNote != ''">ro_note = #{roNote},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="findApplicationByCriteria" parameterType="map" resultMap="PropertyOfficerApplicationSelectResultMap">
        SELECT <include refid="poaSelectCols"/>
        FROM <include refid="poaSelectTb"/>
        <where>
            <if test="societyId!=null">
                AND society_id = #{societyId}
            </if>
            <choose>
                <when test="branchId!=null">
                    AND branch_id = #{branchId}
                </when>
                <otherwise>
                    AND branch_id IS NULL
                </otherwise>
            </choose>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="applicationStatusCode != null and applicationStatusCode != ''">
                AND application_status_code = #{applicationStatusCode}
            </if>
        </where>
    </select>

    <select id="findAllApplicationByCriteria" parameterType="map" resultMap="PropertyOfficerApplicationSelectResultMap">
        SELECT
        <include refid="poaSelectCols"/>,
        <include refid="poSelectCols"/>
        FROM <include refid="poaSelectTb"/>
        LEFT JOIN <include refid="poSelectTb"/> ON `po`.`property_officer_application_id` = `poa`.`id`
        <where>
            <if test="societyId!=null">
                AND poa.`society_id` = #{societyId}
            </if>
            <choose>
                <when test="branchId!=null">
                    AND poa.`branch_id` = #{branchId}
                </when>
                <otherwise>
                    AND poa.`branch_id` IS NULL
                </otherwise>
            </choose>
            <if test="deleteApplicationStatusCode != null and deleteApplicationStatusCode != ''">
                AND poa.`application_status_code` != #{deleteApplicationStatusCode}
            </if>
        </where>
        ORDER BY poa.`created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllApplicationByCriteria" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM <include refid="poaTb"/>
        <where>
            <if test="societyId != null">
                AND society_id = #{societyId}
            </if>
            <choose>
                <when test="branchId != null">
                    AND branch_id = #{branchId}
                </when>
                <otherwise>
                    AND branch_id IS NULL
                </otherwise>
            </choose>
            <if test="deleteApplicationStatusCode != null and deleteApplicationStatusCode != ''">
                AND application_status_code != #{deleteApplicationStatusCode}
            </if>
        </where>
    </select>

    <select id="countPropertyOfficer" parameterType="map" resultType="long">
        SELECT count(*) FROM <include refid="poSelectTb"/>
        INNER JOIN <include refid="poaSelectTb"/>
        ON `po`.`property_officer_application_id`=`poa`.`id`
        WHERE 1=1
        <if test="societyId!=null">
           AND `poa`.`society_id`=#{societyId}
        </if>
        <choose>
            <when test="branchId!=null">
              AND  `poa`.`branch_id`=#{branchId}
            </when>
            <otherwise>
               AND `poa`.`branch_id` IS NULL
            </otherwise>
        </choose>
    </select>

    <select id="findLatestPropertyOfficerApplication" parameterType="map" resultMap="SinglePropertyOfficerApplicationSelectResultMap">
        SELECT <include refid="poaSelectCols"/> FROM <include refid="poaSelectTb"/>
        WHERE 1=1
        <if test="societyId!=null">
            AND `poa`.`society_id`=#{societyId}
        </if>
        <choose>
            <when test="branchId!=null">
                AND  `poa`.`branch_id`=#{branchId}
            </when>
            <otherwise>
                AND `poa`.`branch_id` IS NULL
            </otherwise>
        </choose>
        <if test="deleteApplicationStatusCode != null">
            AND `poa`.`application_status_code` != #{deleteApplicationStatusCode}
        </if>
        ORDER BY `poa`.`created_date` DESC
        LIMIT 1
    </select>
    <select id="findAllActivePropertyOfficers" parameterType="map" resultMap="PropertyOfficerSelectResultMap">
        SELECT <include refid="poSelectCols"/>, <include refid="poaSelectCols"/>
        FROM <include refid="poSelectTb"/>
        JOIN <include refid="poaSelectTb"/>
        ON `po`.`property_officer_application_id` = `poa`.`id`
        WHERE 1=1
        <if test="societyId!=null">
            AND `poa`.`society_id`=#{societyId}
        </if>
        <choose>
            <when test="branchId!=null">
                AND  `poa`.`branch_id`=#{branchId}
            </when>
            <otherwise>
                AND `poa`.`branch_id` IS NULL
            </otherwise>
        </choose>
        AND `poa`.`application_status_code`=#{applicationStatusCode}
        AND `poa`.`status` = #{status}
    </select>
    <select id="findAllPropertyOfficer" parameterType="map" resultMap="PropertyOfficerSelectResultMap">
        SELECT <include refid="poSelectCols"/>, <include refid="poaSelectCols"/>
        FROM <include refid="poSelectTb"/>
        JOIN <include refid="poaSelectTb"/>
        ON `po`.`property_officer_application_id` = `poa`.`id`
        WHERE 1=1
        <if test="societyId!=null">
            AND `poa`.`society_id`=#{societyId}
        </if>
        <choose>
            <when test="branchId!=null">
                AND  `poa`.`branch_id`=#{branchId}
            </when>
            <otherwise>
                AND `poa`.`branch_id` IS NULL
            </otherwise>
        </choose>
        <if test="deleteApplicationStatusCode != null">
            AND `poa`.`application_status_code` != #{deleteApplicationStatusCode}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <delete id="removePropertyOfficers" parameterType="long" >
        DELETE FROM <include refid="tbl"/> WHERE  `property_officer_application_id` = #{propertyOfficerApplicationId}
    </delete>

    <select id="findOfficerByApplicationId" parameterType="long" resultMap="PropertyOfficerSelectResultMap">
        SELECT <include refid="poSelectCols"/>, <include refid="poaSelectCols"/>
        FROM <include refid="poSelectTb"/>
        JOIN <include refid="poaSelectTb"/>
        ON `po`.`property_officer_application_id` = `poa`.`id`
        WHERE 1=1
        AND `poa`.`id`=#{propertyOfficerApplicationId}
    </select>

    <select id="getAllPropertyOfficerApplicationPendingApproval" parameterType="map" resultMap="SinglePropertyOfficerApplicationSelectResultMap">
        SELECT
        <include refid="poaSelectCols"/>,
        <include refid="Society.selectCols"/>
        FROM <include refid="poaSelectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.id = poa.society_id
        WHERE
        poa.`application_status_code` = #{applicationStatusCode}
        AND poa.`submission_date` IS NOT NULL
        AND (
        <foreach item="days" collection="daysAfterSubmissionList" separator=" OR ">
            poa.`submission_date` = DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        </foreach>
        )
    </select>

    <select id="existsOnGoingSocietyPropertyOfficerApplication" parameterType="map" resultType="boolean">
        SELECT EXISTS(
        SELECT 1
        FROM <include refid="poaTb"/>
        WHERE 1=1
        AND `branch_id` IS NULL
        <if test="applicationStatusCodes != null and applicationStatusCodes.size() > 0">
            AND `application_status_code` IN
            <foreach item="applicationStatusCode" collection="applicationStatusCodes" open="(" separator="," close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test ="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        )
    </select>

    <select id="existsOnGoingBranchPropertyOfficerApplication" parameterType="map" resultType="boolean">
        SELECT EXISTS(
        SELECT 1
        FROM <include refid="poaTb"/>
        WHERE 1=1
        <if test="applicationStatusCodes != null and applicationStatusCodes.size() > 0">
            AND `application_status_code` IN
            <foreach item="applicationStatusCode" collection="applicationStatusCodes" open="(" separator="," close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test ="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        )
    </select>
</mapper>