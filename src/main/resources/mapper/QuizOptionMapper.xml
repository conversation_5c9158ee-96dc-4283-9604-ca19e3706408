<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="QuizOption">

    <resultMap id="QuizOptionMap" type="com.eroses.external.society.model.QuizOption">
        <id property="id" column="id" />
        <result property="quizQuestionId" column="quiz_question_id" />
        <result property="optionText" column="option_text" />
        <result property="isCorrect" column="is_correct" />
        <result property="sequenceOrder" column="sequence_order" />
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date" />
    </resultMap>

    <sql id="tb">
        quiz_option
    </sql>

    <sql id="cols">
        quiz_question_id,
        option_text,
        is_correct,
        sequence_order,
        created_by,
        created_date
    </sql>

    <sql id="colsWithId">
        id, <include refid="cols" />
    </sql>

    <!-- Create -->
    <insert id="create" parameterType="com.eroses.external.society.model.QuizOption" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb" /> (
            quiz_question_id,
            option_text,
            is_correct,
            sequence_order,
            created_by,
            created_date
        ) VALUES (
            #{quizQuestionId},
            #{optionText},
            #{isCorrect},
            #{sequenceOrder},
            #{createdBy},
            NOW()
        )
    </insert>

    <!-- Create Batch -->
    <insert id="createBatch" parameterType="java.util.List">
        INSERT INTO <include refid="tb" /> (
            quiz_question_id,
            option_text,
            is_correct,
            sequence_order,
            created_by,
            created_date
        ) VALUES 
        <foreach collection="options" item="option" separator=",">
            (
                #{option.quizQuestionId},
                #{option.optionText},
                #{option.isCorrect},
                #{option.sequenceOrder},
                #{option.createdBy},
                NOW()
            )
        </foreach>
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.QuizOption">
        UPDATE <include refid="tb" />
        SET
            option_text = #{optionText},
            is_correct = #{isCorrect},
            sequence_order = #{sequenceOrder}
        WHERE id = #{id}
    </update>

    <!-- Delete -->
    <delete id="delete">
        DELETE FROM <include refid="tb" />
        WHERE id = #{id}
    </delete>

    <!-- Delete All By Question ID -->
    <delete id="deleteAllByQuestionId">
        DELETE FROM <include refid="tb" />
        WHERE quiz_question_id = #{quizQuestionId}
    </delete>

    <!-- Find By ID -->
    <select id="findById" resultMap="QuizOptionMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE id = #{id}
    </select>

    <!-- Find All By Question ID -->
    <select id="findAllByQuestionId" resultMap="QuizOptionMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE quiz_question_id = #{quizQuestionId}
        ORDER BY sequence_order ASC
    </select>

    <!-- Find Correct Option By Question ID -->
    <select id="findCorrectOptionByQuestionId" resultMap="QuizOptionMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE quiz_question_id = #{quizQuestionId}
        AND is_correct = TRUE
        LIMIT 1
    </select>

    <!-- Update Sequence Orders -->
    <update id="updateSequenceOrders">
        <foreach collection="options" item="option" separator=";">
            UPDATE <include refid="tb" />
            SET sequence_order = #{option.sequenceOrder}
            WHERE id = #{option.id}
        </foreach>
    </update>

</mapper>
