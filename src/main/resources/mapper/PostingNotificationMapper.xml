<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="PostingNotification">
    <sql id="tb">posting_notification</sql>

    <resultMap id="PostingNotificationMap" type="com.eroses.external.society.model.posting.Posting">
        <id property="id" column="id" />
        <result property="postingId" column="posting_id" />
        <result property="recipientId" column="recipient_id" />
        <result property="notificationType" column="notification_type" />
        <result property="notificationDate" column="notification_date" />
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date" />
    </resultMap>
    <sql id="cols">
        posting_id,
        recipient_id,
        notification_type,
        is_read,
        notification_date,
        created_by,
        created_date
    </sql>

    <sql id="colsWithId">
        id,
        <include refid="cols"/>
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.posting.PostingNotification" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
            posting_id,
            recipient_id,
            notification_type,
            is_read,
            notification_date,
            created_by,
            created_date
        )
        VALUES (
            #{postingId},
            #{recipientId},
            #{notificationType},
            #{isRead},
            #{notificationDate},
            #{createdBy},
            #{createdDate}
        )
    </insert>

    <!-- Mark As Read -->
    <update id="markAsRead">
        UPDATE
        <include refid="tb"/>
        SET
            is_read = TRUE
        WHERE
            id = #{id}
    </update>

    <!-- Mark All As Read -->
    <update id="markAllAsRead">
        UPDATE
        <include refid="tb"/>
        SET
            is_read = TRUE
        WHERE
            recipient_id = #{recipientId}
            AND is_read = FALSE
    </update>

    <!-- Find By ID -->
    <select id="findById" resultType="com.eroses.external.society.model.posting.PostingNotification">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            id = #{id}
    </select>

    <!-- Find By Posting ID -->
    <select id="findByPostingId" resultType="com.eroses.external.society.model.posting.PostingNotification">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            posting_id = #{postingId}
    </select>

    <!-- Find By Recipient ID -->
    <select id="findByRecipientId" resultType="com.eroses.external.society.model.posting.PostingNotification">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            recipient_id = #{recipientId}
        ORDER BY
            notification_date DESC
    </select>

    <!-- Find By Recipient ID And Is Read -->
    <select id="findByRecipientIdAndIsRead" resultType="com.eroses.external.society.model.posting.PostingNotification">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            recipient_id = #{recipientId}
            AND is_read = #{isRead}
        ORDER BY
            notification_date DESC
    </select>
</mapper>
