<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="FeedbackTrail">

    <resultMap id="FeedbackTrailMap" type="com.eroses.external.society.model.FeedbackTrail">
        <id property="id" column="id" />
        <result column="feedback_Id" property="feedbackId"/>
        <result column="feedback_Recipient_Id" property="feedbackRecipientId"/>
        <result column="feedback_Recipient_Name" property="feedbackRecipientName"/>
        <result column="feedback_Recipient_Branch_Id" property="feedbackRecipientBranchId"/>
        <result column="jppm_Branch_Id" property="jppmBranchId"/>
        <result column="feedback_Action" property="feedbackAction"/>
        <result column="complaint_Level" property="complaintLevel"/>
        <result column="feedback_Note" property="feedbackNote"/>
        <result column="status" property="status"/>
        <result column="created_date" property="createdDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="modified_by" property="modifiedBy"/>
    </resultMap>

    <resultMap id="PagingMap" type="com.eroses.external.society.model.FeedbackTrail">
        <id property="id" column="id" />
        <result column="feedback_id" property="feedbackId"/>
        <result column="feedback_recipient_id" property="feedbackRecipientId"/>
        <result column="feedback_recipient_name" property="feedbackRecipientName"/>
        <result column="feedback_recipient_branch_id" property="feedbackRecipientBranchId"/>
        <result column="jppm_branch_id" property="jppmBranchId"/>
        <result column="feedback_action" property="feedbackAction"/>
        <result column="complaint_level" property="complaintLevel"/>
        <result column="feedback_note" property="feedbackNote"/>
        <result column="status" property="status"/>
        <result column="created_date" property="createdDate"/>
    </resultMap>

    <sql id="tb">
        feedback_trail
    </sql>

    <sql id="cols">
        feedback_id,
        feedback_recipient_id,
        feedback_recipient_name,
        feedback_recipient_branch_id,
        jppm_branch_id,
        feedback_action,
        complaint_level,
        feedback_note,
        status,
        created_date,
        created_by,
        modified_date,
        modified_by
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="vals">
        #{id},
        #{feedbackId},
        #{feedbackRecipientId},
        #{feedbackRecipientName},
        #{feedbackRecipientBranchId},
        #{jppmBranchId},
        #{feedbackAction},
        #{complaintLevel},
        #{feedbackNote},
        #{status},
        now(),
        #{createdBy},
        #{modifiedDate},
        #{modifiedBy}
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.FeedbackTrail" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="colsWithId"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <!-- Update -->
    <insert id="update" parameterType="com.eroses.external.society.model.FeedbackTrail">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="jppmBranchId != null">jppm_branch_id = #{jppmBranchId},</if>
            <if test="feedbackAction != null">feedback_action = #{feedbackAction},</if>
            <if test="complaintLevel != null">complaint_level = #{complaintLevel},</if>
            <if test="feedbackNote != null">feedback_note = #{feedbackNote},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="modifiedDate != null">modified_date = #{modifiedDate},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy}</if>
        </set>
        WHERE
        id = #{id}
    </insert>

    <select id="getAllByCriteria" parameterType="map" resultMap="FeedbackTrailMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="feedbackId != null">
                AND feedback_id = #{feedbackId}
            </if>
        </where>
        ORDER BY created_date DESC
    </select>

    <select id="findById" parameterType="java.lang.Long" resultMap="FeedbackTrailMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
    </select>
</mapper>
