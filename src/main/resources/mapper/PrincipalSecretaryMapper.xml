<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="PrincipalSecretary">

    <resultMap id="PrincipalSecretaryResultMap" type="com.eroses.external.society.model.PrincipalSecretary">
        <id property="id" column="id"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="society_committee_id" property="societyCommitteeId"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="title_code" property="titleCode"/>
        <result column="committee_name" property="committeeName"/>
        <result column="gender" property="gender"/>
        <result column="committee_position" property="committeePosition"/>
        <result column="job_code" property="jobCode"/>
        <result column="citizenship_status" property="citizenshipStatus"/>
        <result column="date_of_birth" property="dateOfBirth"/>
        <result column="place_of_birth" property="placeOfBirth"/>
        <result column="identification_type" property="identificationType"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="email" property="email"/>
        <result column="residence_address" property="residenceAddress"/>
        <result column="residence_postcode" property="residencePostcode"/>
        <result column="residence_address_status" property="residenceAddressStatus"/>
        <result column="residence_country_code" property="residenceCountryCode"/>
        <result column="residence_state_code" property="residenceStateCode"/>
        <result column="residence_district_code" property="residenceDistrictCode"/>
        <result column="residence_city_code" property="residenceCityCode"/>
        <result column="residence_city" property="residenceCity"/>
        <result column="home_tel_no" property="homeTelNo"/>
        <result column="hp_no" property="hpNo"/>
        <result column="work_tel_no" property="workTelNo"/>
        <result column="employer_name" property="employerName"/>
        <result column="employer_address" property="employerAddress"/>
        <result column="employer_postcode" property="employerPostcode"/>
        <result column="employer_country_code" property="employerCountryCode"/>
        <result column="employer_state_code" property="employerStateCode"/>
        <result column="employer_district_code" property="employerDistrictCode"/>
        <result column="employer_city_code" property="employerCityCode"/>
        <result column="employer_city" property="employerCity"/>
        <result column="employer_address_status" property="employerAddressStatus"/>
        <result column="old_secretary_name" property="oldSecretaryName"/>
        <result column="old_secretary_identification_number" property="oldSecretaryIdentificationNumber"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="meeting_type" property="meetingType"/>
        <result column="reason_of_change" property="reasonOfChange"/>
        <result column="acknowledgement_1" property="acknowledgement1"/>
        <result column="acknowledgement_2" property="acknowledgement2"/>
        <result column="active_status" property="activeStatus"/>
        <result column="migrate_stat_secretary" property="migrateStatSecretary"/>
        <result column="pdf_document" property="pdfDocument"/>
        <result column="submit_date" property="submitDate"/>
        <result column="transfer_date" property="transferDate"/>
        <result column="is_queried" property="isQueried"/>
        <result column="ro" property="ro"/>
        <result column="ro_name" property="roName"/>
        <result column="note_ro" property="noteRO"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <resultMap id="PrincipalSecretarySelectResultMap" type="com.eroses.external.society.model.PrincipalSecretary">
        <id property="id" column="ps_id"/>
        <result column="ps_application_status_code" property="applicationStatusCode"/>
        <result column="ps_society_committee_id" property="societyCommitteeId"/>
        <result column="ps_society_id" property="societyId"/>
        <result column="ps_society_no" property="societyNo"/>
        <result column="ps_title_code" property="titleCode"/>
        <result column="ps_committee_name" property="committeeName"/>
        <result column="ps_gender" property="gender"/>
        <result column="ps_committee_position" property="committeePosition"/>
        <result column="ps_job_code" property="jobCode"/>
        <result column="ps_citizenship_status" property="citizenshipStatus"/>
        <result column="ps_date_of_birth" property="dateOfBirth"/>
        <result column="ps_place_of_birth" property="placeOfBirth"/>
        <result column="ps_identification_type" property="identificationType"/>
        <result column="ps_identification_no" property="identificationNo"/>
        <result column="ps_email" property="email"/>
        <result column="ps_residence_address" property="residenceAddress"/>
        <result column="ps_residence_postcode" property="residencePostcode"/>
        <result column="ps_residence_address_status" property="residenceAddressStatus"/>
        <result column="ps_residence_country_code" property="residenceCountryCode"/>
        <result column="ps_residence_state_code" property="residenceStateCode"/>
        <result column="ps_residence_district_code" property="residenceDistrictCode"/>
        <result column="ps_residence_city_code" property="residenceCityCode"/>
        <result column="ps_residence_city" property="residenceCity"/>
        <result column="ps_home_tel_no" property="homeTelNo"/>
        <result column="ps_hp_no" property="hpNo"/>
        <result column="ps_work_tel_no" property="workTelNo"/>
        <result column="ps_employer_name" property="employerName"/>
        <result column="ps_employer_address" property="employerAddress"/>
        <result column="ps_employer_postcode" property="employerPostcode"/>
        <result column="ps_employer_address_status" property="employerAddressStatus"/>
        <result column="ps_employer_country_code" property="employerCountryCode"/>
        <result column="ps_employer_state_code" property="employerStateCode"/>
        <result column="ps_employer_district_code" property="employerDistrictCode"/>
        <result column="ps_employer_city_code" property="employerCityCode"/>
        <result column="ps_employer_city" property="employerCity"/>
        <result column="ps_old_secretary_name" property="oldSecretaryName"/>
        <result column="ps_old_secretary_identification_number" property="oldSecretaryIdentificationNumber"/>
        <result column="ps_meeting_id" property="meetingId"/>
        <result column="ps_meeting_date" property="meetingDate"/>
        <result column="ps_meeting_type" property="meetingType"/>
        <result column="ps_reason_of_change" property="reasonOfChange"/>
        <result column="ps_acknowledgement_1" property="acknowledgement1"/>
        <result column="ps_acknowledgement_2" property="acknowledgement2"/>
        <result column="ps_active_status" property="activeStatus"/>
        <result column="ps_migrate_stat_secretary" property="migrateStatSecretary"/>
        <result column="ps_pdf_document" property="pdfDocument"/>
        <result column="ps_submit_date" property="submitDate"/>
        <result column="ps_transfer_date" property="transferDate"/>
        <result column="ps_is_queried" property="isQueried"/>
        <result column="ps_ro" property="ro"/>
        <result column="ps_ro_name" property="roName"/>
        <result column="ps_note_ro" property="noteRO"/>
        <result column="ps_created_by" property="createdBy"/>
        <result column="ps_created_date" property="createdDate"/>
        <result column="ps_modified_by" property="modifiedBy"/>
        <result column="ps_modified_date" property="modifiedDate"/>

        <association property="society" resultMap="Society.SocietySelectMap"/>
    </resultMap>

    <resultMap id="PrincipalSecretaryMap" type="com.eroses.external.society.dto.response.societySecretaryReplacement.PrincipleSecretaryResponse">
        <id property="id" column="id" />
        <result column="is_user_applicant" property="isUserApplicant"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="title_code" property="titleCode"/>
        <result column="committee_name" property="committeeName"/>
        <result column="gender" property="gender"/>
        <result column="committee_position" property="committeePosition"/>
        <result column="job_code" property="jobCode"/>
        <result column="citizenship_status" property="citizenshipStatus"/>
        <result column="date_of_birth" property="dateOfBirth"/>
        <result column="place_of_birth" property="placeOfBirth"/>
        <result column="identification_type" property="identificationType"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="email" property="email"/>
        <result column="residence_address" property="residenceAddress"/>
        <result column="residence_postcode" property="residencePostcode"/>
        <result column="residence_address_status" property="residenceAddressStatus"/>
        <result column="residence_country_code" property="residenceCountryCode"/>
        <result column="residence_state_code" property="residenceStateCode"/>
        <result column="residence_district_code" property="residenceDistrictCode"/>
        <result column="residence_city_code" property="residenceCityCode"/>
        <result column="residence_city" property="residenceCity"/>
        <result column="home_tel_no" property="homeTelNo"/>
        <result column="hp_no" property="hpNo"/>
        <result column="work_tel_no" property="workTelNo"/>
        <result column="employer_name" property="employerName"/>
        <result column="employer_address" property="employerAddress"/>
        <result column="employer_postcode" property="employerPostcode"/>
        <result column="employer_address_status" property="employerAddressStatus"/>
        <result column="employer_country_code" property="employerCountryCode"/>
        <result column="employer_state_code" property="employerStateCode"/>
        <result column="employer_district_code" property="employerDistrictCode"/>
        <result column="employer_city_code" property="employerCityCode"/>
        <result column="employer_city" property="employerCity"/>
        <result column="old_secretary_name" property="oldSecretaryName"/>
        <result column="old_secretary_identification_number" property="oldSecretaryIdentificationNumber"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="meeting_type" property="meetingType"/>
        <result column="reason_of_change" property="reasonOfChange"/>
        <result column="query_note" property="queryNote"/>
        <result column="query_id" property="queryId"/>
    </resultMap>

    <resultMap id="PagingExternalMap" type="com.eroses.external.society.dto.response.PrincipalSecretarySearchResponse">
        <id property="id" column="id" />
        <result column="society_name" property="societyName"/>
        <result column="application_status_code" property="applicationStatusCode"/>
    </resultMap>

    <sql id="tb">
        principal_secretary
    </sql>

    <sql id="selectTb">
        principal_secretary ps
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <sql id="cols">
        `id`, `society_committee_id`, `society_id`, `society_no`, `title_code`, `committee_name`,
        `gender`, `committee_position`, `job_code`, `citizenship_status`, `date_of_birth`, `place_of_birth`,
        `identification_type`, `identification_no`, `email`, `residence_address`, `residence_postcode`,
        `residence_address_status`, `residence_country_code`, `residence_state_code`, `residence_district_code`,
        `residence_city_code`, `residence_city`, `home_tel_no`, `hp_no`, `work_tel_no`,
        `employer_name`, `employer_address`, `employer_postcode`, `employer_country_code`,
        `employer_state_code`, `employer_district_code`, `employer_city_code`, `employer_city`,
        `employer_address_status`, `old_secretary_name`, `old_secretary_identification_number`,
        `meeting_id`, `meeting_date`, `meeting_type`, `reason_of_change`,
        `acknowledgement_1`, `acknowledgement_2`, `active_status`, `migrate_stat_secretary`,
        `pdf_document`, `submit_date`, `transfer_date`, `is_queried`, `ro`, `ro_name`, `note_ro`, `created_by`,
        `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="selectCols">
        ps.`id` as ps_id,
        ps.`society_committee_id`,
        ps.`society_id` as ps_society_id,
        ps.`society_no` as ps_society_no,
        ps.`title_code` as ps_title_code,
        ps.`committee_name` as ps_committee_name,
        ps.`gender` as ps_gender,
        ps.`committee_position` as ps_committee_position,
        ps.`job_code` as ps_job_code,
        ps.`citizenship_status` as ps_citizenship_status,
        ps.`date_of_birth` as ps_date_of_birth,
        ps.`place_of_birth` as ps_place_of_birth,
        ps.`identification_type` as ps_identification_type,
        ps.`identification_no` as ps_identification_no,
        ps.`email` as ps_email,
        ps.`residence_address` as ps_residence_address,
        ps.`residence_postcode` as ps_residence_postcode,
        ps.`residence_address_status` as ps_residence_address_status,
        ps.`residence_country_code` as ps_residence_country_code,
        ps.`residence_state_code` as ps_residence_state_code,
        ps.`residence_district_code` as ps_residence_district_code,
        ps.`residence_city_code` as ps_residence_city_code,
        ps.`residence_city` as ps_residence_city,
        ps.`home_tel_no` as ps_home_tel_no,
        ps.`hp_no` as ps_hp_no,
        ps.`work_tel_no` as ps_work_tel_no,
        ps.`employer_name` as ps_employer_name,
        ps.`employer_address` as ps_employer_address,
        ps.`employer_postcode` as ps_employer_postcode,
        ps.`employer_country_code` as ps_employer_country_code,
        ps.`employer_state_code` as ps_employer_state_code,
        ps.`employer_district_code` as ps_employer_district_code,
        ps.`employer_city_code` as ps_employer_city_code,
        ps.`employer_city` as ps_employer_city,
        ps.`old_secretary_name` as ps_old_secretary_name,
        ps.`old_secretary_identification_number` as ps_old_secretary_identification_number,
        ps.`meeting_id` as ps_meeting_id,
        ps.`meeting_date` as ps_meeting_date,
        ps.`meeting_type` as ps_meeting_type,
        ps.`reason_of_change` as ps_reason_of_change,
        ps.`application_status_code` as ps_application_status_code,
        ps.`acknowledgement_1` as ps_acknowledgement_1,
        ps.`acknowledgement_2` as ps_acknowledgement_2,
        ps.`active_status` as ps_active_status,
        ps.`migrate_stat_secretary` as ps_migrate_stat_secretary,
        ps.`pdf_document` as ps_pdf_document,
        ps.`submit_date` as ps_submit_date,
        ps.`transfer_date` as ps_transfer_date,
        ps.`is_queried` as ps_is_queried,
        ps.`ro` as ps_ro,
        ps.`ro_name` as ps_ro_name,
        ps.`note_ro` as ps_note_ro,
        ps.`created_by` as ps_created_by,
        ps.`created_date` as ps_created_date,
        ps.`modified_by` as ps_modified_by,
        ps.`modified_date` as ps_modified_date
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.PrincipalSecretary" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
        society_committee_id, job_code, society_id, society_no, title_code,
        committee_name, gender, citizenship_status, identification_type, identification_no,
        date_of_birth, place_of_birth, committee_position, employer_address_status, employer_name,
        employer_address, employer_postcode, employer_country_code, employer_state_code, employer_district_code,
        employer_city_code, employer_city, residence_address, residence_postcode, residence_address_status,
        residence_country_code, residence_state_code, residence_district_code, residence_city_code, residence_city,
        email, home_tel_no, hp_no, work_tel_no, application_status_code,
        old_secretary_name, old_secretary_identification_number, reason_of_change,
        meeting_id, meeting_date, meeting_type,
        submit_date, transfer_date, is_queried, created_by, created_by_name, created_date
        )
        VALUES
        (
        #{societyCommitteeId}, #{jobCode}, #{societyId}, #{societyNo}, #{titleCode},
        #{committeeName}, #{gender}, #{citizenshipStatus}, #{identificationType}, #{identificationNo},
        #{dateOfBirth}, #{placeOfBirth}, #{committeePosition}, #{employerAddressStatus}, #{employerName},
        #{employerAddress}, #{employerPostcode}, #{employerCountryCode}, #{employerStateCode}, #{employerDistrictCode},
        #{employerCityCode}, #{employerCity}, #{residenceAddress}, #{residencePostcode}, #{residenceAddressStatus},
        #{residenceCountryCode}, #{residenceStateCode}, #{residenceDistrictCode}, #{residenceCityCode}, #{residenceCity},
        #{email}, #{homeTelNo}, #{hpNo}, #{workTelNo}, #{applicationStatusCode},
        #{oldSecretaryName}, #{oldSecretaryIdentificationNumber}, #{reasonOfChange},
        #{meetingId}, #{meetingDate}, #{meetingType}, #{submitDate}, #{transferDate},
        #{isQueried}, #{createdBy}, #{createdByName}, NOW()
        )
    </insert>

    <select id="findById" parameterType="map" resultMap="PrincipalSecretaryMap">
        SELECT
            ps.id,
            CASE
            WHEN ps.created_by = #{userId}
                THEN 1
                ELSE 0
            END AS is_user_applicant,
            ps.society_id,
            ps.society_no,
            ps.application_status_code,
            ps.title_code,
            ps.committee_name,
            ps.gender,
            ps.committee_position,
            ps.job_code,
            ps.citizenship_status,
            ps.date_of_birth,
            ps.place_of_birth,
            ps.identification_type,
            ps.identification_no,
            ps.email,
            ps.residence_address,
            ps.residence_postcode,
            ps.residence_address_status,
            ps.residence_country_code,
            ps.residence_state_code,
            ps.residence_district_code,
            ps.residence_city_code,
            ps.residence_city,
            ps.home_tel_no,
            ps.hp_no,
            ps.work_tel_no,
            ps.employer_name,
            ps.employer_address,
            ps.employer_postcode,
            ps.employer_address_status,
            ps.employer_country_code,
            ps.employer_state_code,
            ps.employer_district_code,
            ps.employer_city_code,
            ps.employer_city,
            ps.old_secretary_name,
            ps.old_secretary_identification_number,
            ps.meeting_id,
            ps.meeting_date,
            ps.meeting_type,
            ps.reason_of_change,
            q.id AS query_id,
            q.note AS query_note
        FROM <include refid="tb"/> ps
        LEFT JOIN ro_query q ON q.principal_secretary_id = ps.id
        WHERE ps.id = #{psid}
        ORDER BY q.created_date DESC
        LIMIT 1
    </select>

    <select id="getById" parameterType="java.lang.Long" resultMap="PrincipalSecretaryResultMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="findAll" resultMap="PrincipalSecretaryResultMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
    </select>

    <select id="searchPrincipalSecretaryExternal" parameterType="map" resultMap="PagingExternalMap">
        SELECT
            ps.id,
            s.society_name,
            ps.application_status_code
        FROM <include refid="tb"/> ps
        JOIN society s ON s.id = ps.society_id
        <where>
            ps.identification_no = #{identificationNo}
            <choose>
                <when test="applicationStatusCode != null">
                    AND ps.application_status_code = #{applicationStatusCode}
                </when>
                <otherwise>
                    AND ps.application_status_code != -1
                </otherwise>
            </choose>
            <if test="societyName != null and societyName != ''">
                <bind name="wildcardSocietyName" value="'%' + societyName + '%'" />
                AND s.society_name LIKE #{wildcardSocietyName}
            </if>
        </where>
        ORDER BY ps.created_date DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countPrincipalSecretaryExternal" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM <include refid="tb"/> ps
        JOIN society s ON s.id = ps.society_id
        <where>
            ps.identification_no = #{identificationNo}
            <choose>
                <when test="applicationStatusCode != null">
                    AND ps.application_status_code = #{applicationStatusCode}
                </when>
                <otherwise>
                    AND ps.application_status_code != -1
                </otherwise>
            </choose>
            <if test="societyName != null and societyName != ''">
                <bind name="wildcardSocietyName" value="'%' + societyName + '%'" />
                AND s.society_name LIKE #{wildcardSocietyName}
            </if>
        </where>
    </select>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.PrincipalSecretary">
        UPDATE
            <include refid="tb"/>
        <set>
            <if test="titleCode != null">title_code = #{titleCode}, </if>
            <if test="committeeName != null">committee_name = #{committeeName},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="committeePosition != null">committee_position = #{committeePosition},</if>
            <if test="jobCode != null">job_code = #{jobCode},</if>
            <if test="citizenshipStatus != null">citizenship_status = #{citizenshipStatus},</if>
            <if test="dateOfBirth != null">date_of_birth = #{dateOfBirth},</if>
            <if test="placeOfBirth != null">place_of_birth = #{placeOfBirth},</if>
            <if test="identificationType != null">identification_type = #{identificationType},</if>
            <if test="identificationNo != null">identification_no = #{identificationNo},</if>
            <if test="email != null">email = #{email},</if>
            <if test="residenceAddress != null">residence_address = #{residenceAddress},</if>
            <if test="residencePostcode != null">residence_postcode = #{residencePostcode},</if>
            <if test="residenceAddressStatus != null">residence_address_status = #{residenceAddressStatus},</if>
            <if test="residenceCountryCode != null">residence_country_code = #{residenceCountryCode},</if>
            <if test="residenceStateCode != null">residence_state_code = #{residenceStateCode},</if>
            <if test="residenceDistrictCode != null">residence_district_code = #{residenceDistrictCode},</if>
            <if test="residenceCityCode != null">residence_city_code = #{residenceCityCode},</if>
            <if test="residenceCity != null">residence_city = #{residenceCity},</if>
            <if test="homeTelNo != null">home_tel_no = #{homeTelNo},</if>
            <if test="hpNo != null">hp_no = #{hpNo},</if>
            <if test="workTelNo != null">work_tel_no = #{workTelNo},</if>
            <if test="employerName != null">employer_name = #{employerName},</if>
            <if test="employerAddress != null">employer_address = #{employerAddress},</if>
            <if test="employerPostcode != null">employer_postcode = #{employerPostcode},</if>
            <if test="employerCountryCode != null">employer_country_code = #{employerCountryCode},</if>
            <if test="employerStateCode != null">employer_state_code = #{employerStateCode},</if>
            <if test="employerDistrictCode != null">employer_district_code = #{employerDistrictCode},</if>
            <if test="employerCityCode != null">employer_city_code = #{employerCityCode},</if>
            <if test="employerCity != null">employer_city = #{employerCity},</if>
            <if test="employerAddressStatus != null">employer_address_status = #{employerAddressStatus},</if>
            <if test="oldSecretaryName != null">old_secretary_name = #{oldSecretaryName},</if>
            <if test="oldSecretaryIdentificationNumber != null">old_secretary_identification_number = #{oldSecretaryIdentificationNumber},</if>
            <if test="ro != null">ro = #{ro},</if>
            <if test="roName != null">ro_name = #{roName},</if>
            <if test="noteRO != null">note_ro = #{noteRO},</if>
            <if test="applicationStatusCode != null">application_status_code = #{applicationStatusCode},</if>
            <if test="reasonOfChange != null">reason_of_change = #{reasonOfChange},</if>
            <if test="meetingId != null">meeting_id = #{meetingId},</if>
            <if test="meetingDate != null">meeting_date = #{meetingDate},</if>
            <if test="meetingType != null">meeting_type = #{meetingType},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            <if test="ro != null">transfer_date = NOW(),</if>
            <if test="isQueried != null">is_queried = #{isQueried},</if>
            <if test="submitDate != null">submit_date = #{submitDate},</if>
            <if test="transferDate != null">transfer_date = #{transferDate},</if>
            modified_date = NOW()
        </set>
        WHERE
        id = #{id}
    </update>

    <update id="delete" parameterType="com.eroses.external.society.model.PrincipalSecretary">
        UPDATE <include refid="tb"/>
        <set>
            <if test="applicationStatusCode != null">application_status_code = #{applicationStatusCode},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date = NOW()
        </set>
        WHERE
        id = #{id}
    </update>

    <select id="existsById" parameterType="java.lang.Long" resultType="boolean">
        SELECT EXISTS(
            SELECT 1
            FROM <include refid="tb"/>
            WHERE id = #{id}
        )
    </select>

    <select id="getAllPendingByCriteria" parameterType="map" resultMap="PrincipalSecretarySelectResultMap">
        SELECT
            <include refid="selectCols"/>,
            <include refid="Society.selectCols"/>
        FROM
            <include refid="selectTb"/>
            INNER JOIN <include refid="Society.selectTb"/> ON s.id = ps.society_id
        WHERE 1=1
            <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
                AND ps.`application_status_code` IN
                <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                    #{applicationStatusCode}
                </foreach>
            </if>
            <if test="stateCode != null and stateCode !=''">
                AND s.`state_id` = #{stateCode}
            </if>
            <if test="ro != 0 and ro != ''">
                AND ps.`ro` = #{ro}
            </if>
            <if test="isQueried == 0"> <!-- Cater for NULL data -->
                AND (ps.`is_queried` IS NULL OR ps.`is_queried` = 0)
            </if>
            <if test="isQueried != null and isQueried != 0">
                AND ps.`is_queried` = #{isQueried}
            </if>
            <if test="categoryCode != null and categoryCode !=''">
                AND s.`category_code_jppm` = #{categoryCode}
            </if>
            <if test="subCategoryCode != null and subCategoryCode !=''">
                AND s.`sub_category_code` = #{subCategoryCode}
            </if>
            <if test="societyName != null and societyName != ''">
                AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
            </if>
        ORDER BY submit_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllPendingByCriteria" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.id = ps.society_id
        WHERE 1=1
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND ps.`application_status_code` IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND s.`state_id` = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND ps.`ro` = #{ro}
        </if>
        <if test="isQueried == 0"> <!-- Cater for NULL data -->
            AND (ps.`is_queried` IS NULL OR ps.`is_queried` = 0)
        </if>
        <if test="isQueried != null and isQueried != 0">
            AND ps.`is_queried` = #{isQueried}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND s.`category_code_jppm` = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND s.`sub_category_code` = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
        </if>
    </select>
</mapper>
