<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.eroses.user.mappers.TokenDao">
    <resultMap id="TokenResponseMapping" type="com.eroses.user.model.Token">
        <id property="id" column="id"/>
        <result property="token" column="token"/>
        <result property="isLogout" column="is_logout"/>
        <result property="identificationNo" column="identification_no"/>
    </resultMap>

    <sql id="tb">
        `token`
    </sql>
    <sql id="cols">
        `id`,
        `token`,
        `is_logout`,
        `identification_no`
    </sql>
    <sql id="insertCols">
        `token`,
        `is_logout`,
        `identification_no`
    </sql>
    <sql id="vals">
        #{token},
        #{isLogout},
        #{identificationNo}
    </sql>
    
    <select id="findByToken" resultMap="TokenResponseMapping">
        SELECT
            <include refid="cols"/>
        FROM
            <include refid="tb"/>
        WHERE
            `token` = #{token}
        LIMIT 1
    </select>

    <select id="findByIdentificationNo" resultMap="TokenResponseMapping">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE
        `identification_no` = #{identificationNo}
    </select>

    <select id="findByIdentificationNoAndStatus" resultMap="TokenResponseMapping">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE
        `identification_no` = #{identificationNo} AND `is_logout` = #{isLogout}
    </select>

    <insert id="save">
        INSERT INTO
            <include refid="tb"/>
            (<include refid="insertCols"/>)
        VALUES
            (<include refid="vals"/>)
    </insert>

    <update id="update">
        UPDATE
            <include refid="tb"/>
        <set>
            `is_logout` = #{isLogout}
        </set>
        WHERE
            `id` = #{id}
    </update>

    <update id="revokeAllToken">
        UPDATE
        <include refid="tb"/>
        <set>
            `is_logout` = 1
        </set>
        WHERE `identification_no` = #{identificationNo}
    </update>
</mapper>