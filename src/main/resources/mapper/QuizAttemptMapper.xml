<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="QuizAttempt">

    <resultMap id="QuizAttemptMap" type="com.eroses.external.society.model.QuizAttempt">
        <id property="id" column="id" />
        <result property="trainingEnrollmentId" column="training_enrollment_id" />
        <result property="trainingQuizId" column="training_quiz_id" />
        <result property="attemptNumber" column="attempt_number" />
        <result property="startTime" column="start_time" />
        <result property="endTime" column="end_time" />
        <result property="score" column="score" />
        <result property="passed" column="passed" />
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date" />
        <result property="modifiedBy" column="modified_by" />
        <result property="modifiedDate" column="modified_date" />
    </resultMap>

    <sql id="tb">
        quiz_attempt
    </sql>

    <sql id="cols">
        training_enrollment_id,
        training_quiz_id,
        attempt_number,
        start_time,
        end_time,
        score,
        passed,
        created_by,
        created_date,
        modified_by,
        modified_date
    </sql>

    <sql id="colsWithId">
        id, <include refid="cols" />
    </sql>

    <!-- Create -->
    <insert id="create" parameterType="com.eroses.external.society.model.QuizAttempt" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb" /> (
            training_enrollment_id,
            training_quiz_id,
            attempt_number,
            start_time,
            score,
            passed,
            created_by,
            created_date
        ) VALUES (
            #{trainingEnrollmentId},
            #{trainingQuizId},
            #{attemptNumber},
            NOW(),
            0,
            FALSE,
            #{createdBy},
            NOW()
        )
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.QuizAttempt">
        UPDATE <include refid="tb" />
        SET
            end_time = #{endTime},
            score = #{score},
            passed = #{passed},
            modified_by = #{modifiedBy},
            modified_date = NOW()
        WHERE id = #{id}
    </update>

    <!-- Find By ID -->
    <select id="findById" resultMap="QuizAttemptMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE id = #{id}
    </select>

    <!-- Find All By Enrollment ID -->
    <select id="findAllByEnrollmentId" resultMap="QuizAttemptMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE training_enrollment_id = #{trainingEnrollmentId}
        ORDER BY attempt_number DESC
    </select>

    <!-- Find All By Quiz ID -->
    <select id="findAllByQuizId" resultMap="QuizAttemptMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE training_quiz_id = #{trainingQuizId}
        ORDER BY start_time DESC
    </select>

    <!-- Find Latest By Enrollment ID and Quiz ID -->
    <select id="findLatestByEnrollmentIdAndQuizId" resultMap="QuizAttemptMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE training_enrollment_id = #{trainingEnrollmentId}
        AND training_quiz_id = #{trainingQuizId}
        ORDER BY attempt_number DESC
        LIMIT 1
    </select>

    <!-- Count Attempts By Enrollment ID and Quiz ID -->
    <select id="countAttemptsByEnrollmentIdAndQuizId" resultType="int">
        SELECT COUNT(*)
        FROM <include refid="tb" />
        WHERE training_enrollment_id = #{trainingEnrollmentId}
        AND training_quiz_id = #{trainingQuizId}
    </select>

    <!-- Update Score and Passed -->
    <update id="updateScoreAndPassed">
        UPDATE <include refid="tb" />
        SET
            score = #{score},
            passed = #{passed},
            end_time = NOW(),
            modified_by = #{modifiedBy},
            modified_date = NOW()
        WHERE id = #{id}
    </update>

</mapper>
