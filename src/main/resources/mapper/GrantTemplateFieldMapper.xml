<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.eroses.external.society.mappers.grant.GrantTemplateFieldDao">
    
    <resultMap id="grantTemplateFieldResultMap" type="com.eroses.external.society.model.grant.GrantTemplateField">
        <id property="id" column="id"/>
        <result property="grantTemplateId" column="grant_template_id"/>
        <result property="fieldName" column="field_name"/>
        <result property="fieldType" column="field_type"/>
        <result property="isRequired" column="is_required"/>
        <result property="sequenceOrder" column="sequence_order"/>
        <result property="options" column="options"/>
        <result property="sectionName" column="section_name"/>
        <result property="pageNumber" column="page_number"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>
    
    <insert id="create" parameterType="com.eroses.external.society.model.grant.GrantTemplateField" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO grant_template_field (
            grant_template_id,
            field_name,
            field_type,
            is_required,
            sequence_order,
            options,
            section_name,
            page_number,
            created_by,
            created_date,
            modified_by,
            modified_date
        ) VALUES (
            #{grantTemplateId},
            #{fieldName},
            #{fieldType},
            #{isRequired},
            #{sequenceOrder},
            #{options},
            #{sectionName},
            #{pageNumber},
            #{createdBy},
            #{createdDate},
            #{modifiedBy},
            #{modifiedDate}
        )
    </insert>
    
    <update id="update" parameterType="com.eroses.external.society.model.grant.GrantTemplateField">
        UPDATE grant_template_field
        SET 
            field_name = #{fieldName},
            field_type = #{fieldType},
            is_required = #{isRequired},
            sequence_order = #{sequenceOrder},
            options = #{options},
            section_name = #{sectionName},
            page_number = #{pageNumber},
            modified_by = #{modifiedBy},
            modified_date = #{modifiedDate}
        WHERE id = #{id}
    </update>
    
    <select id="findByGrantTemplateId" parameterType="long" resultMap="grantTemplateFieldResultMap">
        SELECT * FROM grant_template_field
        WHERE grant_template_id = #{grantTemplateId}
        ORDER BY sequence_order ASC
    </select>
    
    <select id="findById" parameterType="long" resultMap="grantTemplateFieldResultMap">
        SELECT * FROM grant_template_field
        WHERE id = #{id}
    </select>
    
    <delete id="deleteByGrantTemplateId" parameterType="long">
        DELETE FROM grant_template_field
        WHERE grant_template_id = #{grantTemplateId}
    </delete>
    
    <delete id="delete" parameterType="long">
        DELETE FROM grant_template_field
        WHERE id = #{id}
    </delete>
    
</mapper>
