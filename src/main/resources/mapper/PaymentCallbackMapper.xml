<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="PaymentCallback">
    <resultMap id="PaymentCallbackResultMap" type="com.eroses.external.society.model.payment.PaymentCallback">
        <id property="id" column="id" />
        <result property="paymentRecordId" column="payment_record_id" />
        <result property="amount" column="amount" />
        <result property="paySuccess" column="pay_success" />
        <result property="transactionId" column="transaction_id" />
        <result property="documentCode" column="document_code" />
        <result property="referenceNo" column="reference_no" />
        <result property="content" column="content" />
        <result property="createdDate" column="created_date" />
        <result property="createdBy" column="created_by" />
        <result property="status" column="status" />
        <result property="modifiedDate" column="modified_date" />
        <result property="modifiedBy" column="modified_by" />
    </resultMap>

    <sql  id="tb">
        `payment_callback`
    </sql>

    <sql id="cols">
        `id`,
        `payment_record_id`,
        `amount`,
        `pay_success`,
        `transaction_id`,
        `document_code`,
        `reference_no`,
        `content`,
        `created_date`,
        `created_by`,
        `status`,
        `modified_date`,
        `modified_by`
    </sql>

    <sql id="insertCols">
        payment_record_id,
        amount,
        pay_success,
        transaction_id,
        document_code,
        reference_no,
        content,
        created_date,
        created_by,
        status,
        modified_date,
        modified_by
    </sql>

    <sql id="vals">
        #{paymentRecordId},
        #{amount},
        #{paySuccess},
        #{transactionId},
        #{documentCode},
        #{referenceNo},
        #{content},
        #{createdDate},
        #{createdBy},
        #{status},
        #{modifiedDate},
        #{modifiedBy}
    </sql>

    <select id="select" resultMap="PaymentCallbackResultMap">
        SELECT <include refid="cols"/>
        FROM <include refid="tb"/>
        ORDER BY `created_date` DESC
    </select>

    <select id="findByCriteria" resultMap="PaymentCallbackResultMap">
        SELECT <include refid="cols"/>
        FROM <include refid="tb"/>
        WHERE 1=1
        <if test="id != 0">
            AND id = #{id}
        </if>
        <if test="paymentRecordId != 0">
            AND payment_record_id = #{paymentRecordId}
        </if>
        <if test="amount != 0.0">
            AND amount = #{amount}
        </if>
        <if test="paySuccess != 0">
            AND pay_success = #{paySuccess}
        </if>
        <if test="transactionId != null">
            AND transaction_id = #{transactionId}
        </if>
        <if test="documentCode != null">
            AND document_code = #{documentCode}
        </if>
        <if test="referenceNo != null">
            AND reference_no = #{referenceNo}
        </if>
        <if test="content != null">
            AND content = #{content}
        </if>
        <if test="createdDate != null">
            AND created_date = #{createdDate}
        </if>
        <if test="createdBy != null">
            AND created_by = #{createdBy}
        </if>
        <if test="status != 0">
            AND status = #{status}
        </if>
        <if test="modifiedDate != null">
            AND modified_date = #{modifiedDate}
        </if>
        <if test="modifiedBy != null">
            AND modified_by = #{modifiedBy}
        </if>
        LIMIT 1
    </select>


    <insert id="save" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
        (<include refid="insertCols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>
</mapper>