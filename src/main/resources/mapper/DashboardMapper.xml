<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="Dashboard">

    <resultMap id="DashboardMap" type="com.eroses.external.society.model.Dashboard">
        <id property="id" column="id" />
        <result column="dashboard_id" property="dashboardId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="module" property="module"/>
        <result column="category" property="category"/>
        <result column="permission" property="permission"/>
        <result column="order_sequence" property="orderSequence"/>
        <result column="created_date" property="createdDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="modified_by" property="modifiedBy"/>
    </resultMap>

    <sql id="tb">
        dashboard
    </sql>

    <sql id="cols">
        dashboard_id,
        code,
        name,
        type,
        module,
        category,
        permission,
        order_sequence,
        created_date,
        created_by,
        modified_date,
        modified_by
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <!-- Find all dashboards with filtering parameters -->
    <select id="findAllByParams" parameterType="map" resultMap="DashboardMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="module != null and module != ''">
                AND module = #{module}
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
        </where>
        ORDER BY order_sequence ASC, name ASC
    </select>

    <!-- Find dashboard by ID -->
    <select id="findById" parameterType="java.lang.Long" resultMap="DashboardMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <!-- Find dashboard by code -->
    <select id="findByCode" parameterType="java.lang.String" resultMap="DashboardMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE code = #{code}
    </select>

</mapper>
