<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="CommitteeDraft">
    <resultMap id="CommitteeDraftMap" type="com.eroses.external.society.model.CommitteeDraft">
        <!-- ID -->
        <id column="id" property="id"/>

        <!-- Committee Draft specific fields -->
        <result column="committee_table_old_id" property="committeeTableOldId"/>

        <!-- Basic Information -->
        <result column="job_code" property="jobCode"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="title_code" property="titleCode"/>
        <result column="name" property="name"/>
        <result column="gender" property="gender"/>
        <result column="nationality_status" property="nationalityStatus"/>
        <result column="identification_type" property="identificationType"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="date_of_birth" property="dateOfBirth"/>
        <result column="place_of_birth" property="placeOfBirth"/>

        <!-- Position Information -->
        <result column="designation_code" property="designationCode"/>
        <result column="other_designation_code" property="otherDesignationCode"/>

        <!-- Employer Information -->
        <result column="employer_address_status" property="employerAddressStatus"/>
        <result column="employer_name" property="employerName"/>
        <result column="employer_address" property="employerAddress"/>
        <result column="employer_postcode" property="employerPostcode"/>
        <result column="employer_country_code" property="employerCountryCode"/>
        <result column="employer_state_code" property="employerStateCode"/>
        <result column="employer_city" property="employerCity"/>
        <result column="employer_district" property="employerDistrict"/>

        <!-- Residential Information -->
        <result column="residential_address" property="residentialAddress"/>
        <result column="residential_postcode" property="residentialPostcode"/>
        <result column="residential_address_status" property="residentialAddressStatus"/>
        <result column="residential_country_code" property="residentialCountryCode"/>
        <result column="residential_state_code" property="residentialStateCode"/>
        <result column="residential_district_code" property="residentialDistrictCode"/>
        <result column="residential_city" property="residentialCity"/>

        <!-- Contact Information -->
        <result column="email" property="email"/>
        <result column="telephone_number" property="telephoneNumber"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="no_tel_p" property="noTelP"/>

        <!-- Membership Information -->
        <result column="membership_no" property="membershipNo"/>
        <result column="status" property="status"/>
        <result column="application_status_code" property="applicationStatusCode"/>

        <!-- Additional Fields -->
        <result column="peg_harta" property="pegHarta"/>
        <result column="tarikh_tukar_su" property="tarikhTukarSu"/>
        <result column="other_position" property="otherPosition"/>
        <result column="batal_flat" property="batalFlat"/>
        <result column="blacklist_notice" property="blacklistNotice"/>
        <result column="benar_ajk" property="benarAjk"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="document_id" property="documentId"/>
        <result column="document_type" property="documentType"/>
        <result column="membership_registration_date" property="membershipRegistrationDate"/>
        <result column="appointed_date" property="appointedDate"/>

        <!-- Audit Columns -->
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <sql id="tb">
        `committee_draft`
    </sql>

    <sql id="selectTb">
        `committee_draft` cd
    </sql>

    <sql id="cols">
        `committee_table_old_id`, `job_code`, `society_id`,
        `society_no`, `branch_id`, `branch_no`, `title_code`,
        `name`, `gender`, `nationality_status`, `identification_type`,
        `identification_no`, `date_of_birth`, `place_of_birth`, `designation_code`,
        `other_designation_code`, `employer_address_status`, `employer_name`, `employer_address`,
        `employer_postcode`, `employer_country_code`, `employer_state_code`, `employer_city`,
        `employer_district`, `residential_address`, `residential_postcode`, `residential_address_status`,
        `residential_country_code`, `residential_state_code`, `residential_district_code`, `residential_city`,
        `email`, `telephone_number`, `phone_number`, `no_tel_p`,
        `membership_no`, `status`, `application_status_code`, `peg_harta`,
        `tarikh_tukar_su`, `other_position`, `batal_flat`, `blacklist_notice`,
        `benar_ajk`, `meeting_id`, `document_id`, `document_type`,
        `membership_registration_date`, `appointed_date`, `created_by`, `created_date`,
        `modified_by`, `modified_date`
    </sql>

    <sql id="vals">
        #{committeeTableOldId}, #{jobCode}, #{societyId},
        #{societyNo}, #{branchId}, #{branchNo}, #{titleCode},
        #{name}, #{gender}, #{nationalityStatus}, #{identificationType},
        #{identificationNo}, #{dateOfBirth}, #{placeOfBirth}, #{designationCode},
        #{otherDesignationCode}, #{employerAddressStatus}, #{employerName}, #{employerAddress},
        #{employerPostcode}, #{employerCountryCode}, #{employerStateCode}, #{employerCity},
        #{employerDistrict}, #{residentialAddress}, #{residentialPostcode}, #{residentialAddressStatus},
        #{residentialCountryCode}, #{residentialStateCode}, #{residentialDistrictCode}, #{residentialCity},
        #{email}, #{telephoneNumber}, #{phoneNumber}, #{noTelP},
        #{membershipNo}, #{status}, #{applicationStatusCode}, #{pegHarta},
        #{tarikhTukarSu}, #{otherPosition}, #{batalFlat}, #{blacklistNotice},
        #{benarAjk}, #{meetingId}, #{documentId}, #{documentType},
        #{membershipRegistrationDate}, #{appointedDate}, #{createdBy}, NOW(),
        #{modifiedBy}, NOW()
    </sql>

    <!-- Column definitions with ID -->
    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <!-- Create operation -->
    <insert id="create" parameterType="com.eroses.external.society.model.CommitteeDraft" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <!-- Find by ID -->
    <select id="findById" parameterType="java.lang.Long" resultMap="CommitteeDraftMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <!-- Update operation -->
    <update id="update" parameterType="com.eroses.external.society.model.CommitteeDraft">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="committeeTableOldId != null">
                `committee_table_old_id` = #{committeeTableOldId},
            </if>
            <if test="jobCode != null">
                `job_code` = #{jobCode},
            </if>
            <if test="societyId != null">
                `society_id` = #{societyId},
            </if>
            <if test="societyNo != null">
                `society_no` = #{societyNo},
            </if>
            <if test="titleCode != null">
                `title_code` = #{titleCode},
            </if>
            <if test="name != null">
                `name` = #{name},
            </if>
            <if test="gender != null">
                `gender` = #{gender},
            </if>
            <if test="nationalityStatus != null">
                `nationality_status` = #{nationalityStatus},
            </if>
            <if test="identificationType != null">
                `identification_type` = #{identificationType},
            </if>
            <if test="identificationNo != null">
                `identification_no` = #{identificationNo},
            </if>
            <if test="dateOfBirth != null">
                `date_of_birth` = #{dateOfBirth},
            </if>
            <if test="placeOfBirth != null">
                `place_of_birth` = #{placeOfBirth},
            </if>
            <if test="designationCode != null">
                `designation_code` = #{designationCode},
            </if>
            <if test="otherDesignationCode != null">
                `other_designation_code` = #{otherDesignationCode},
            </if>
            <if test="employerAddressStatus != null">
                `employer_address_status` = #{employerAddressStatus},
            </if>
            <if test="employerName != null">
                `employer_name` = #{employerName},
            </if>
            <if test="employerAddress != null">
                `employer_address` = #{employerAddress},
            </if>
            <if test="employerPostcode != null">
                `employer_postcode` = #{employerPostcode},
            </if>
            <if test="employerCountryCode != null">
                `employer_country_code` = #{employerCountryCode},
            </if>
            <if test="employerStateCode != null">
                `employer_state_code` = #{employerStateCode},
            </if>
            <if test="employerCity != null">
                `employer_city` = #{employerCity},
            </if>
            <if test="employerDistrict != null">
                `employer_district` = #{employerDistrict},
            </if>
            <if test="residentialAddress != null">
                `residential_address` = #{residentialAddress},
            </if>
            <if test="residentialPostcode != null">
                `residential_postcode` = #{residentialPostcode},
            </if>
            <if test="residentialAddressStatus != null">
                `residential_address_status` = #{residentialAddressStatus},
            </if>
            <if test="residentialCountryCode != null">
                `residential_country_code` = #{residentialCountryCode},
            </if>
            <if test="residentialStateCode != null">
                `residential_state_code` = #{residentialStateCode},
            </if>
            <if test="residentialDistrictCode != null">
                `residential_district_code` = #{residentialDistrictCode},
            </if>
            <if test="residentialCity != null">
                `residential_city` = #{residentialCity},
            </if>
            <if test="email != null">
                `email` = #{email},
            </if>
            <if test="telephoneNumber != null">
                `telephone_number` = #{telephoneNumber},
            </if>
            <if test="phoneNumber != null">
                `phone_number` = #{phoneNumber},
            </if>
            <if test="noTelP != null">
                `no_tel_p` = #{noTelP},
            </if>
            <if test="membershipNo != null">
                `membership_no` = #{membershipNo},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="applicationStatusCode != null">
                `application_status_code` = #{applicationStatusCode},
            </if>
            <if test="pegHarta != null">
                `peg_harta` = #{pegHarta},
            </if>
            <if test="tarikhTukarSu != null">
                `tarikh_tukar_su` = #{tarikhTukarSu},
            </if>
            <if test="otherPosition != null">
                `other_position` = #{otherPosition},
            </if>
            <if test="batalFlat != null">
                `batal_flat` = #{batalFlat},
            </if>
            <if test="blacklistNotice != null">
                `blacklist_notice` = #{blacklistNotice},
            </if>
            <if test="benarAjk != null">
                `benar_ajk` = #{benarAjk},
            </if>
            <if test="meetingId != null">
                `meeting_id` = #{meetingId},
            </if>
            <if test="documentId != null">
                `document_id` = #{documentId},
            </if>
            <if test="documentType != null">
                `document_type` = #{documentType},
            </if>
            <if test="membershipRegistrationDate != null">
                `membership_registration_date` = #{membershipRegistrationDate},
            </if>
            <if test="appointedDate != null">
                `appointed_date` = #{appointedDate},
            </if>
            <if test="modifiedBy != null">
                `modified_by` = #{modifiedBy},
            </if>
            `modified_date` = NOW()
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="findAllByParams" parameterType="map" resultMap="CommitteeDraftMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <choose>
            <when test="applicationStatusCode != null">
                AND `application_status_code` = #{applicationStatusCode}
            </when>
            <otherwise>
                AND `application_status_code` != 43
            </otherwise>
        </choose>
        <if test="offset != null and limit != null">
        LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="findByParam" parameterType="map" resultMap="CommitteeDraftMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="meetingId != null">
            AND `meeting_id` = #{meetingId}
        </if>
<!--        <if test="documentId != null">-->
<!--            AND `document_id` = #{documentId}-->
<!--        </if>-->
        <choose>
            <when test="applicationStatusCode != null">
                AND `application_status_code` = #{applicationStatusCode}
            </when>
            <otherwise>
                AND `application_status_code` != 43
            </otherwise>
        </choose>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        <if test="ids != null">
            AND `id` IN <foreach collection="ids" item="item" separator="," open="(" close=")">#{item}</foreach>
        </if>
        <if test="offset != null and offset >= 0 and limit != null and limit > 0">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countByParam" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="meetingId != null">
            AND `meeting_id` = #{meetingId}
        </if>
        <!--        <if test="documentId != null">-->
        <!--            AND `document_id` = #{documentId}-->
        <!--        </if>-->
        <choose>
            <when test="applicationStatusCode != null">
                AND `application_status_code` = #{applicationStatusCode}
            </when>
            <otherwise>
                AND `application_status_code` != 43
            </otherwise>
        </choose>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        <if test="ids != null">
            AND `id` IN <foreach collection="ids" item="item" separator="," open="(" close=")">#{item}</foreach>
        </if>
    </select>

    <select id="findOneByParams" parameterType="map" resultMap="CommitteeDraftMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="committeeTableOldId != null">
            AND `committee_table_old_id` = #{committeeTableOldId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="position != null">
            AND `designation_code` = #{position}
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        <choose>
            <when test="applicationStatusCode != null">
                AND `application_status_code` = #{applicationStatusCode}
            </when>
            <otherwise>
                AND `application_status_code` != 43
            </otherwise>
        </choose>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
        LIMIT 1
    </select>

    <select id="countAllByParams" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <choose>
            <when test="applicationStatusCode != null">
                AND `application_status_code` = #{applicationStatusCode}
            </when>
            <otherwise>
                AND `application_status_code` != 43
            </otherwise>
        </choose>
    </select>
</mapper>