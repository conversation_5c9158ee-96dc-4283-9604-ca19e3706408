<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="BlacklistUser">
    <!-- Result mapping -->
    <resultMap id="BlacklistUserMap" type="com.eroses.external.society.model.blacklist.BlacklistUser">
        <id property="id" column="id"/>
        <result column="name" property="name"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="email" property="email"/>
        <result column="phone_no" property="phoneNo"/>
        <result column="address" property="address"/>
        <result column="date_of_birth" property="dateOfBirth"/>
        <result column="gender" property="gender"/>
        <result column="nationality" property="nationality"/>
        <result column="is_blacklisted" property="isBlacklisted"/>
        <result column="is_permanent" property="isPermanent"/>
        <result column="whitelist_date" property="whitelistDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <!-- SQL snippets -->
    <sql id="tb">
        `blacklist_user`
    </sql>

    <sql id="cols">
        `name`,
        `identification_no`,
        `email`,
        `phone_no`,
        `address`,
        `date_of_birth`,
        `gender`,
        `nationality`,
        `is_blacklisted`,
        `is_permanent`,
        `whitelist_date`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="insertCols">
        `name`,
        `identification_no`,
        `email`,
        `phone_no`,
        `address`,
        `date_of_birth`,
        `gender`,
        `nationality`,
        `is_blacklisted`,
        `is_permanent`,
        `whitelist_date`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`
    </sql>

    <sql id="vals">
        #{name},
        #{identificationNo},
        #{email},
        #{phoneNo},
        #{address},
        #{dateOfBirth},
        #{gender},
        #{nationality},
        #{isBlacklisted},
        #{isPermanent},
        #{whitelistDate},
        #{createdBy},
        NOW(),
        #{modifiedBy},
        NOW()
    </sql>

    <!-- Select operations -->
    <select id="findById" parameterType="java.lang.Long" resultMap="BlacklistUserMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="findByIdentificationNo" parameterType="java.lang.String" resultMap="BlacklistUserMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `identification_no` = #{identificationNo}
        LIMIT 1
    </select>

    <select id="existsActiveBlacklistUserByIdentificationNo" resultType="int">
        SELECT 1
        FROM <include refid="tb"/>
        WHERE `identification_no` = #{identificationNo}
        AND `is_blacklisted` = 1
        LIMIT 1
    </select>

    <select id="search" parameterType="map" resultMap="BlacklistUserMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        <where>
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="identificationNo != null and identificationNo != ''">
                AND `identification_no` LIKE CONCAT('%', #{identificationNo}, '%')
            </if>
            <if test="email != null and email != ''">
                AND `email` LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="phoneNo != null and phoneNo != ''">
                AND `phone_no` LIKE CONCAT('%', #{phoneNo}, '%')
            </if>
            <if test="gender != null and gender != ''">
                AND `gender` = #{gender}
            </if>
            <if test="nationality != null and nationality != ''">
                AND `nationality` = #{nationality}
            </if>
            <if test="isBlacklisted != null">
                AND `is_blacklisted` = #{isBlacklisted}
            </if>
            <if test="isPermanent != null">
                AND `is_permanent` = #{isPermanent}
            </if>
        </where>
        ORDER BY `id` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countSearch" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM <include refid="tb"/>
        <where>
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="identificationNo != null and identificationNo != ''">
                AND `identification_no` LIKE CONCAT('%', #{identificationNo}, '%')
            </if>
            <if test="email != null and email != ''">
                AND `email` LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="phoneNo != null and phoneNo != ''">
                AND `phone_no` LIKE CONCAT('%', #{phoneNo}, '%')
            </if>
            <if test="gender != null and gender != ''">
                AND `gender` = #{gender}
            </if>
            <if test="nationality != null and nationality != ''">
                AND `nationality` = #{nationality}
            </if>
            <if test="isBlacklisted != null">
                AND `is_blacklisted` = #{isBlacklisted}
            </if>
            <if test="isPermanent != null">
                AND `is_permanent` = #{isPermanent}
            </if>
        </where>
    </select>

    <!-- Insert operation -->
    <insert id="create" parameterType="com.eroses.external.society.model.blacklist.BlacklistUser" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
        (<include refid="insertCols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <!-- Update operation -->
    <update id="update" parameterType="com.eroses.external.society.model.blacklist.BlacklistUser">
        UPDATE <include refid="tb"/>
        <set>
            <if test="name != null">`name` = #{name},</if>
            <if test="identificationNo != null">`identification_no` = #{identificationNo},</if>
            <if test="email != null">`email` = #{email},</if>
            <if test="phoneNo != null">`phone_no` = #{phoneNo},</if>
            <if test="address != null">`address` = #{address},</if>
            <if test="dateOfBirth != null">`date_of_birth` = #{dateOfBirth},</if>
            <if test="gender != null">`gender` = #{gender},</if>
            <if test="nationality != null">`nationality` = #{nationality},</if>
            <if test="isBlacklisted != null">`is_blacklisted` = #{isBlacklisted},</if>
            <if test="isPermanent != null">`is_permanent` = #{isPermanent},</if>
            `whitelist_date` = #{whitelistDate},
            `modified_date` = NOW(),
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- Delete operation -->
    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM <include refid="tb"/>
        WHERE `id` = #{id}
    </delete>

    <!-- Find Expired Blacklist Users -->
    <select id="getAllExpiredBlacklistUserId" resultType="java.lang.Long">
        SELECT id
        FROM <include refid="tb"/>
        WHERE `is_blacklisted` = 1
        AND `is_permanent` = 0
        AND `whitelist_date` IS NOT NULL
        AND DATE(`whitelist_date`) &lt;= CURDATE()
    </select>
</mapper>
