<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AdmInsolvencyDepartment">
    <resultMap id="AdmInsolvencyDepartmentResultMap" type="com.eroses.external.society.model.lookup.AdmInsolvencyDepartment">
        <id property="id" column="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="address" property="address"/>
        <result column="city_code" property="cityCode"/>
        <result column="district_id" property="districtCode"/>
        <result column="state_id" property="stateCode"/>
        <result column="postcode" property="postcode"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="fax_number" property="faxNumber"/>
        <result column="email_address" property="emailAddress"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <sql id="tb">
        `adm_insolvency_department`
    </sql>

    <sql id="selectTb">
        `adm_insolvency_department` aid
    </sql>

    <sql id="selectCols">
        aid.`id` as aid_id,
        aid.`name` as aid_name,
        aid.`code` as aid_code,
        aid.`address` as aid_address,
        aid.`city_code` as aid_city_code,
        aid.`district_id` as aid_district_id,
        aid.`state_id` as aid_state_id,
        aid.`postcode` as aid_postcode,
        aid.`phone_number` as aid_phone_number,
        aid.`fax_number` as aid_fax_number,
        aid.`email_address` as aid_email_address,
        aid.`status` as aid_status,
        aid.`is_deleted` as aid_is_deleted,
        aid.`created_by` as aid_created_by,
        aid.`created_date` as aid_created_date,
        aid.`modified_by` as aid_modified_by,
        aid.`modified_date` as aid_modified_date
    </sql>

    <sql id="cols">
        `name`, `code`, `address`, `city_code`, `district_id`, `state_id`, `postcode`, 
        `phone_number`, `fax_number`, `email_address`, `status`, `is_deleted`,
        `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <select id="findById" parameterType="java.lang.Long" resultMap="AdmInsolvencyDepartmentResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="getAll" resultMap="AdmInsolvencyDepartmentResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `is_deleted` = 0
        <if test="nameQuery != null and nameQuery != ''">
            AND `name` LIKE CONCAT('%', #{nameQuery}, '%')
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countAll" resultType="Long">
        SELECT COUNT(*)
        FROM <include refid="tb"/>
        WHERE `is_deleted` = 0
        <if test="nameQuery != null and nameQuery != ''">
            AND `name` LIKE CONCAT('%', #{nameQuery}, '%')
        </if>
    </select>

    <select id="getAllActive" resultMap="AdmInsolvencyDepartmentResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `is_deleted` = 0
        AND `status` = 1
    </select>

    <insert id="create" parameterType="com.eroses.external.society.model.lookup.AdmInsolvencyDepartment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
        (`name`, `code`, `address`, `city_code`, `district_id`, `state_id`, `postcode`, 
         `phone_number`, `fax_number`, `email_address`, `status`, `is_deleted`,
         `created_by`, `created_date`, `modified_by`, `modified_date`)
        VALUES
        (#{name}, #{code}, #{address}, #{cityCode}, #{districtCode}, #{stateCode}, #{postcode}, 
         #{phoneNumber}, #{faxNumber}, #{emailAddress}, #{status}, 0,
         #{createdBy}, now(), #{modifiedBy}, now())
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.lookup.AdmInsolvencyDepartment">
        UPDATE <include refid="tb"/>
        <set>
            <if test="name != null">`name` = #{name},</if>
            <if test="code != null">`code` = #{code},</if>
            <if test="address != null">`address` = #{address},</if>
            <if test="cityCode != null">`city_code` = #{cityCode},</if>
            <if test="districtCode != null">`district_id` = #{districtCode},</if>
            <if test="stateCode != null">`state_id` = #{stateCode},</if>
            <if test="postcode != null">`postcode` = #{postcode},</if>
            <if test="phoneNumber != null">`phone_number` = #{phoneNumber},</if>
            <if test="faxNumber != null">`fax_number` = #{faxNumber},</if>
            <if test="emailAddress != null">`email_address` = #{emailAddress},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="isDeleted != null">`is_deleted` = #{isDeleted},</if>
            modified_date = now(),
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <update id="delete" parameterType="java.lang.Long">
        UPDATE <include refid="tb"/>
        <set>
            `is_deleted` = 1
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="existsByCode" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM <include refid="tb"/>
            WHERE `code` = #{code}
            AND `is_deleted` = 0
        )
    </select>

    <select id="existsByCodeExcludingId" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM <include refid="tb"/>
            WHERE `code` = #{code}
            AND `id` != #{id}
            AND `is_deleted` = 0
        )
    </select>

    <select id="findByStateCode" parameterType="java.lang.String" resultMap="AdmInsolvencyDepartmentResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `state_id` = #{stateCode}
        AND `is_deleted` = 0
    </select>
</mapper>