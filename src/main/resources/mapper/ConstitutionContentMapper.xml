<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="ConstitutionContent">
    <resultMap id="ConstitutionContentMap" type="com.eroses.external.society.model.ConstitutionContent">
        <id column="id" property="id"/>
        <id column="old_constitution_content_id" property="oldConstitutionContentId"/>
        <result column="amendment_id" property="amendmentId"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="constitution_type_id" property="constitutionTypeId"/>
        <result column="clause_content_id" property="clauseContentId"/>
        <result column="clause_no" property="clauseNo"/>
        <result column="clause_name" property="clauseName"/>
        <result column="description" property="description"/>
        <result column="modified_template" property="modifiedTemplate"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="amendment_toggle" property="amendmentToggle"/>
        <result column="hide_constitution" property="hideConstitution"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="status" property="status"/>
        <result column="check_update" property="checkUpdate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <resultMap id="ConstitutionContentSelectMap" type="com.eroses.external.society.model.ConstitutionContent">
        <id column="const_id" property="id"/>
        <result column="old_constitution_content_id" property="oldConstitutionContentId"/>
        <result column="const_amendment_id" property="amendmentId"/>
        <result column="const_society_id" property="societyId"/>
        <result column="const_society_no" property="societyNo"/>
        <result column="const_constitution_type_id" property="constitutionTypeId"/>
        <result column="const_clause_content_id" property="clauseContentId"/>
        <result column="const_clause_no" property="clauseNo"/>
        <result column="const_clause_name" property="clauseName"/>
        <result column="const_description" property="description"/>
        <result column="const_modified_template" property="modifiedTemplate"/>
        <result column="const_application_status_code" property="applicationStatusCode"/>
        <result column="const_amendment_toggle" property="amendmentToggle"/>
        <result column="const_hide_constitution" property="hideConstitution"/>
        <result column="const_created_by" property="createdBy"/>
        <result column="const_created_date" property="createdDate"/>
        <result column="const_status" property="status"/>
        <result column="const_check_update" property="checkUpdate"/>
        <result column="const_modified_by" property="modifiedBy"/>
        <result column="const_modified_date" property="modifiedDate"/>
        <association property="clauseContent" javaType="com.eroses.external.society.model.ClauseContent"
                     resultMap="ClauseContent.ClauseContentSelectMap"/>
    </resultMap>

    <sql id="tb">
        `constitution_content`
    </sql>

    <sql id="selectTb">
        `constitution_content` const
    </sql>

    <sql id="cols">
        `old_constitution_content_id`, `amendment_id`,`society_id`, `society_no`, `constitution_type_id`, `clause_content_id`,
        `clause_no`, `clause_name`, `description`, `modified_template`, `application_status_code`, `amendment_toggle`, `hide_constitution`,
        `created_by`, `created_date`, `status`, `check_update`, `modified_by`, `modified_date`
    </sql>

    <sql id="selectCols">
        const.`id` AS const_id,
        const.`old_constitution_content_id` AS old_constitution_content_id,
        const.`amendment_id` AS const_amendment_id,
        const.`society_id` AS const_society_id,
        const.`society_no` AS const_society_no,
        const.`constitution_type_id` AS const_constitution_type_id,
        const.`clause_content_id` AS const_clause_content_id,
        const.`clause_no` AS const_clause_no,
        const.`clause_name` AS const_clause_name,
        const.`description` AS const_description,
        const.`modified_template` AS const_modified_template,
        const.`application_status_code` AS const_application_status_code,
        const.`amendment_toggle` AS const_amendment_toggle,
        const.`hide_constitution` AS const_hide_constitution,
        const.`created_by` AS const_created_by,
        const.`created_date` AS const_created_date,
        const.`status` AS const_status,
        const.`check_update` AS const_check_update,
        const.`modified_by` AS const_modified_by,
        const.`modified_date` AS const_modified_date
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.ConstitutionContent" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{oldConstitutionContentId}, #{amendmentId}, #{societyId}, #{societyNo}, #{constitutionTypeId}, #{clauseContentId},
        #{clauseNo}, #{clauseName}, #{description}, #{modifiedTemplate}, #{applicationStatusCode}, #{amendmentToggle}, #{hideConstitution},
        #{createdBy}, NOW(), #{status}, #{checkUpdate},
        #{modifiedBy}, NOW())
    </insert>

    <select id="findById" parameterType="java.lang.Long" resultMap="ConstitutionContentSelectMap">
        SELECT
        <include refid="selectCols"/>
        FROM
        <include refid="selectTb"/>
        WHERE `id` = #{id}
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.ConstitutionContent">
        UPDATE <include refid="tb"/>
        <set>
            <if test="oldConstitutionContentId != null">`old_constitution_content_id` = #{oldConstitutionContentId},</if>
            <if test="amendmentId != null">`amendment_id` = #{amendmentId},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="constitutionTypeId != null">`constitution_type_id` = #{constitutionTypeId},</if>
            <if test="clauseContentId != null">`clause_content_id` = #{clauseContentId},</if>
            <if test="clauseNo != null and clauseNo != ''">`clause_no` = #{clauseNo},</if>
            <if test="clauseName != null and clauseName != ''">`clause_name` = #{clauseName},</if>
            <if test="description != null">`description` = #{description},</if>
            <if test="modifiedTemplate != null">`modified_template` = #{modifiedTemplate},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="amendmentToggle != null">`amendment_toggle` = #{amendmentToggle},</if>
            <if test="hideConstitution != null">`hide_constitution` = #{hideConstitution},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="checkUpdate != null">`check_update` = #{checkUpdate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            modified_date=NOW()
        </set>
        WHERE `id` = #{id}
    </update>

    <update id="updateStatus" parameterType="map">
        UPDATE constitution_content
        SET
        `status` = #{status},
        `application_status_code` = #{appStatusCode},
        `modified_by` = #{userId}
        WHERE id IN
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="findAll" parameterType="map" resultMap="ConstitutionContentSelectMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="ClauseContent.selectCols"/>
        FROM
        <include refid="selectTb" />
        LEFT JOIN <include refid="ClauseContent.selectTb"/> ON const.`clause_content_id` = clause.`id`
        WHERE 1=1
        <if test="oldConstitutionContentId != null">
            AND const.`old_constitution_content_id` = #{oldConstitutionContentId}
        </if>
        <if test="societyId != null and societyId !=''">
            AND const.`society_id` = #{societyId}
        </if>
        <if test="id != null">
            AND const.`id` = #{id}
        </if>
        <if test="amendmentId != null">
            AND const.`amendment_id` = #{amendmentId}
        </if>
        <if test="clauseContentId != null">
            AND const.`clause_content_id` = #{clauseContentId}
        </if>
        <if test="clauseNo != null and clauseNo != ''">
            AND const.`clause_no` = #{clauseNo}
        </if>
        <if test="clauseName != null and clauseName != ''">
            AND const.`clause_name` = #{clauseName}
        </if>
        <if test="status != null and status !=''">
            AND const.`status` = #{status}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode !='' and applicationStatusCode != -1">
            AND const.`application_status_code` = #{applicationStatusCode}
        </if>
        <if test="applicationStatusCode == -1">
            AND const.`application_status_code` != -1
        </if>
        ORDER BY COALESCE(const.`clause_content_id`, CAST(const.`clause_no` AS UNSIGNED))
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countFindAll" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="amendmentId != null">
            AND `amendment_id` = #{amendmentId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="clauseContentId != null">
            AND `clause_content_id` = #{clauseContentId}
        </if>
        <if test="clauseNo != null and clauseNo != ''">
            AND const.`clause_no` = #{clauseNo}
        </if>
        <if test="clauseName != null and clauseName != ''">
            AND const.`clause_name` = #{clauseName}
        </if>
    </select>

    <select id="findBySocietyId" parameterType="java.lang.Long" resultMap="ConstitutionContentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `society_id` = #{societyId}
        AND `application_status_code` IS NOT NULL
    </select>

    <select id="getAllBySocietyId" parameterType="map" resultMap="ConstitutionContentSelectMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="ClauseContent.selectCols"/>
        FROM
        <include refid="selectTb" />
        INNER JOIN <include refid="ClauseContent.selectTb"/> ON const.clause_content_id = clause.id
        WHERE 1=1
        <if test="societyId != null and societyId !=''">
            AND const.`society_id` = #{societyId}
        </if>
        <if test="status != null and status !=''">
            AND const.`status` = #{status}
        </if>
        <choose>
            <when test="applicationStatusCode != null and applicationStatusCode != '' and applicationStatusCode != -1">
                AND const.`application_status_code` = #{applicationStatusCode}
            </when>
            <otherwise>
                AND const.`application_status_code` != -1
            </otherwise>
        </choose>
        ORDER BY COALESCE(const.`clause_content_id`, CAST(const.`clause_no` AS UNSIGNED))
    </select>

    <select id="findForAmendment" parameterType="map" resultMap="ConstitutionContentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="amendmentId != null">
            AND `amendment_id` = #{amendmentId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="clauseContentId != null">
            AND `clause_content_id` = #{clauseContentId}
        </if>
        <if test="clauseNo != null and clauseNo != ''">
            AND `clause_no` = #{clauseNo}
        </if>
        <if test="clauseName != null and clauseName != ''">
            AND `clause_name` = #{clauseName}
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
    </select>

    <select id="findBySocietyIdAndStatus" resultMap="ConstitutionContentMap">

    </select>

    <select id="countBySocietyIdAndConstitutionTypeId" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="ClauseContent.selectTb"/> ON const.clause_content_id = clause.id
        INNER JOIN <include refid="ConstitutionType.selectTb"/> ON clause.constitution_type_id = ct.id
        WHERE 1=1
        AND const.`society_id` = #{societyId}
        AND clause.`constitution_type_id` = #{constitutionTypeId}
    </select>

    <delete id="hardDelete" parameterType="java.lang.Long">
        DELETE FROM
        <include refid="tb"/>
        WHERE 1=1
        AND `id` = #{id}
    </delete>

    <select id="findBySocietyIdAndClauseContentId" parameterType="map" resultMap="ConstitutionContentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="amendmentId != null">
            AND `amendment_id` = #{amendmentId}
        </if>
        AND `society_id` = #{societyId}
        AND `clause_content_id` = #{clauseContentId}
        ORDER BY `created_date` DESC
        LIMIT 1
    </select>

    <select id="findBySocietyIdAndClauseContentIdAndApplicationStatusCode" parameterType="map" resultMap="ConstitutionContentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
                AND `society_id` = #{societyId}
        </if>
        <if test="clauseContentId != null">
            AND `clause_content_id` = #{clauseContentId}
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        ORDER BY `created_date` DESC
        LIMIT 1
    </select>

    <select id="findAllByIdList" parameterType="list" resultMap="ConstitutionContentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>