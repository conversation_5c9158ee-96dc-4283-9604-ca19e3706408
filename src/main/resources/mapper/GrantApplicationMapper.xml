<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.eroses.external.society.mappers.grant.GrantApplicationDao">
    
    <resultMap id="grantApplicationResultMap" type="com.eroses.external.society.model.grant.GrantApplication">
        <id property="id" column="id"/>
        <result property="grantTemplateId" column="grant_template_id"/>
        <result property="societyId" column="society_id"/>
        <result property="status" column="status"/>
        <result property="submissionDate" column="submission_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>
    
    <insert id="create" parameterType="com.eroses.external.society.model.grant.GrantApplication" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO grant_application (
            grant_template_id,
            society_id,
            status,
            submission_date,
            created_by,
            created_date,
            modified_by,
            modified_date
        ) VALUES (
            #{grantTemplateId},
            #{societyId},
            #{status},
            #{submissionDate},
            #{createdBy},
            #{createdDate},
            #{modifiedBy},
            #{modifiedDate}
        )
    </insert>
    
    <update id="update" parameterType="com.eroses.external.society.model.grant.GrantApplication">
        UPDATE grant_application
        SET 
            status = #{status},
            submission_date = #{submissionDate},
            modified_by = #{modifiedBy},
            modified_date = #{modifiedDate}
        WHERE id = #{id}
    </update>
    
    <select id="findAll" parameterType="map" resultMap="grantApplicationResultMap">
        SELECT * FROM grant_application
        <where>
            <if test="societyId != null">
                AND society_id = #{societyId}
            </if>
            <if test="grantTemplateId != null">
                AND grant_template_id = #{grantTemplateId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY created_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>
    
    <select id="countAll" parameterType="map" resultType="long">
        SELECT COUNT(*) FROM grant_application
        <where>
            <if test="societyId != null">
                AND society_id = #{societyId}
            </if>
            <if test="grantTemplateId != null">
                AND grant_template_id = #{grantTemplateId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>
    
    <select id="findById" parameterType="long" resultMap="grantApplicationResultMap">
        SELECT * FROM grant_application
        WHERE id = #{id}
    </select>
    
    <select id="findBySocietyId" parameterType="long" resultMap="grantApplicationResultMap">
        SELECT * FROM grant_application
        WHERE society_id = #{societyId}
        ORDER BY created_date DESC
    </select>
    
    <select id="findByGrantTemplateId" parameterType="long" resultMap="grantApplicationResultMap">
        SELECT * FROM grant_application
        WHERE grant_template_id = #{grantTemplateId}
        ORDER BY created_date DESC
    </select>
    
    <select id="findByStatus" parameterType="string" resultMap="grantApplicationResultMap">
        SELECT * FROM grant_application
        WHERE status = #{status}
        ORDER BY created_date DESC
    </select>
    
    <select id="findPendingStateApproval" resultMap="grantApplicationResultMap">
        SELECT * FROM grant_application
        WHERE status = 'PENDING_STATE'
        ORDER BY submission_date ASC
    </select>
    
    <select id="findPendingHQApproval" resultMap="grantApplicationResultMap">
        SELECT * FROM grant_application
        WHERE status = 'PENDING_HQ'
        ORDER BY submission_date ASC
    </select>
    
    <delete id="delete" parameterType="long">
        DELETE FROM grant_application
        WHERE id = #{id}
    </delete>
    
</mapper>
