<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="TrainingMaterial">

    <resultMap id="TrainingMaterialMap" type="com.eroses.external.society.model.TrainingMaterial">
        <id property="id" column="id" />
        <result property="trainingCourseId" column="training_course_id" />
        <result property="materialType" column="material_type" />
        <result property="contentPath" column="content_path" />
        <result property="contentText" column="content_text" />
        <result property="sequenceOrder" column="sequence_order" />
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date" />
        <result property="title" column="title" />
        <result property="description" column="description" />
        <result property="duration" column="duration" />
        <result property="youtubeLink" column="youtube_link" />
        <result property="media" column="media" />
    </resultMap>

    <sql id="tb">
        training_material
    </sql>

    <sql id="cols">
        training_course_id,
        material_type,
        content_path,
        content_text,
        sequence_order,
        created_by,
        created_date,
        title,
        description,
        duration,
        youtube_link
    </sql>

    <sql id="colsWithId">
        id, <include refid="cols" />
    </sql>

    <sql id="colsJoin">
        a.training_course_id as training_course_id,
        a.material_type as material_type,
        a.content_path as content_path,
        a.content_text as content_text,
        a.sequence_order as sequence_order,
        a.created_by as created_by,
        a.created_date as created_date,
        a.title as title,
        a.description as description,
        a.duration as duration,
        a.youtube_link as youtube_link
    </sql>

    <sql id="colsWithIdJoin">
        a.id, <include refid="colsJoin" />
    </sql>

    <!-- Create -->
    <insert id="create" parameterType="com.eroses.external.society.model.TrainingMaterial" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb" /> (
            training_course_id,
            material_type,
            content_path,
            content_text,
            sequence_order,
            created_by,
            created_date,
            title,
            description,
            duration,
            youtube_link
        ) VALUES (
            #{trainingCourseId},
            #{materialType},
            #{contentPath},
            #{contentText},
            #{sequenceOrder},
            #{createdBy},
            NOW(),
            #{title},
            #{description},
            #{duration},
            #{youtubeLink}
        )
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.TrainingMaterial">
        UPDATE <include refid="tb" />
        SET
            material_type = #{materialType},
            content_path = #{contentPath},
            content_text = #{contentText},
            sequence_order = #{sequenceOrder},
            title = #{title},
            description = #{description},
            duration = #{duration},
            youtube_link = #{youtubeLink}
        WHERE id = #{id}
    </update>

    <!-- Delete -->
    <delete id="delete">
        DELETE FROM <include refid="tb" />
        WHERE id = #{id}
    </delete>

    <!-- Find By ID -->
    <select id="findById" resultMap="TrainingMaterialMap">
        SELECT <include refid="colsWithIdJoin" />, b.url as media
        FROM <include refid="tb" /> as a
        LEFT JOIN document as b ON a.id = b.training_material_id
        WHERE a.id = #{id}
    </select>

    <!-- Find All By Course ID -->
    <select id="findAllByCourseId" resultMap="TrainingMaterialMap">
        SELECT <include refid="colsWithIdJoin" />, b.url as media
        FROM <include refid="tb" /> as a
        LEFT JOIN document as b ON a.id = b.training_material_id
        WHERE a.training_course_id = #{trainingCourseId}
        ORDER BY a.sequence_order ASC
    </select>

    <!-- Find All By Course ID And Type -->
    <select id="findAllByCourseIdAndType" resultMap="TrainingMaterialMap">
        SELECT <include refid="colsWithIdJoin" />, b.url as media
        FROM <include refid="tb" /> as a
        LEFT JOIN document as b ON a.id = b.training_material_id
        WHERE a.training_course_id = #{trainingCourseId}
        AND a.material_type = #{materialType}
        ORDER BY a.sequence_order ASC
    </select>

    <!-- Update Sequence Orders -->
    <update id="updateSequenceOrders">
        <foreach collection="materials" item="material" separator=";">
            UPDATE <include refid="tb" />
            SET sequence_order = #{material.sequenceOrder}
            WHERE id = #{material.id}
        </foreach>
    </update>


</mapper>
