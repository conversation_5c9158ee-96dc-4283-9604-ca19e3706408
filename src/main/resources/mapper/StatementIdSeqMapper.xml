<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.eroses.external.society.mappers.StatementIdSeqDao">
    <!-- Fetch and increment the next_val atomically -->
    <select id="getNextVal" resultType="long">
        SELECT next_val FROM statement_id_seq FOR UPDATE;
    </select>

    <update id="incrementNextVal">
        UPDATE
        statement_id_seq
        SET
        next_val = next_val + 1,
        modified_by = #{userId},
        modified_date = NOW()
    </update>
</mapper>
