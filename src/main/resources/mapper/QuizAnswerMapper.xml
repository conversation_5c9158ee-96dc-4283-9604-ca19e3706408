<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="QuizAnswer">

    <resultMap id="QuizAnswerMap" type="com.eroses.external.society.model.QuizAnswer">
        <id property="id" column="id" />
        <result property="quizAttemptId" column="quiz_attempt_id" />
        <result property="quizQuestionId" column="quiz_question_id" />
        <result property="quizOptionId" column="quiz_option_id" />
        <result property="isCorrect" column="is_correct" />
    </resultMap>

    <sql id="tb">
        quiz_answer
    </sql>

    <sql id="cols">
        quiz_attempt_id,
        quiz_question_id,
        quiz_option_id,
        is_correct
    </sql>

    <sql id="colsWithId">
        id, <include refid="cols" />
    </sql>

    <!-- Create -->
    <insert id="create" parameterType="com.eroses.external.society.model.QuizAnswer" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb" /> (
            quiz_attempt_id,
            quiz_question_id,
            quiz_option_id,
            is_correct
        ) VALUES (
            #{quizAttemptId},
            #{quizQuestionId},
            #{quizOptionId},
            #{isCorrect}
        )
    </insert>

    <!-- Create Batch -->
    <insert id="createBatch" parameterType="java.util.List">
        INSERT INTO <include refid="tb" /> (
            quiz_attempt_id,
            quiz_question_id,
            quiz_option_id,
            is_correct
        ) VALUES 
        <foreach collection="answers" item="answer" separator=",">
            (
                #{answer.quizAttemptId},
                #{answer.quizQuestionId},
                #{answer.quizOptionId},
                #{answer.isCorrect}
            )
        </foreach>
    </insert>

    <!-- Find All By Attempt ID -->
    <select id="findAllByAttemptId" resultMap="QuizAnswerMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE quiz_attempt_id = #{quizAttemptId}
    </select>

    <!-- Find By Attempt ID and Question ID -->
    <select id="findByAttemptIdAndQuestionId" resultMap="QuizAnswerMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE quiz_attempt_id = #{quizAttemptId}
        AND quiz_question_id = #{quizQuestionId}
    </select>

    <!-- Count Correct Answers By Attempt ID -->
    <select id="countCorrectAnswersByAttemptId" resultType="int">
        SELECT COUNT(*)
        FROM <include refid="tb" />
        WHERE quiz_attempt_id = #{quizAttemptId}
        AND is_correct = TRUE
    </select>

    <!-- Delete All By Attempt ID -->
    <delete id="deleteAllByAttemptId">
        DELETE FROM <include refid="tb" />
        WHERE quiz_attempt_id = #{quizAttemptId}
    </delete>

</mapper>
