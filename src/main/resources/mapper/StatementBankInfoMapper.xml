<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="StatementBankInfo">
    <!-- Result Map -->
    <resultMap id="StatementBankInfoMap" type="com.eroses.external.society.model.StatementBankInfo">
        <id property="id" column="id"/>
        <result property="societyId" column="society_id"/>
        <result property="societyNo" column="society_no"/>
        <result property="branchId" column="branch_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="statementId" column="statement_id"/>
        <result property="bankName" column="bank_name"/>
        <result property="accountNo" column="account_no"/>
        <result property="applicationStatusCode" column="application_status_code"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>

    <!-- Table Name -->
    <sql id="tb">
        `statement_bank_info`
    </sql>

    <!-- Columns without ID -->
    <sql id="cols">
        `society_id`,
        `society_no`,
        `branch_id`,
        `branch_no`,
        `statement_id`,
        `bank_name`,
        `account_no`,
        `application_status_code`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`
    </sql>

    <!-- Columns with ID -->
    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <!-- Insert Statement -->
    <insert id="create" parameterType="com.eroses.external.society.model.StatementBankInfo" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{statementId},
        #{bankName},
        #{accountNo},
        #{applicationStatusCode},
        #{createdBy},
        NOW(),
        #{modifiedBy},
        NOW()
        )
    </insert>

    <!-- Update Statement -->
    <update id="update" parameterType="com.eroses.external.society.model.StatementBankInfo">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
<!--            <if test="statementId != null">`statement_id` = #{statementId},</if>-->
            <if test="bankName != null">`bank_name` = #{bankName},</if>
            <if test="accountNo != null">`account_no` = #{accountNo},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            `modified_date` = now()
        </set>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </update>

    <update id="updateByIdAndStatementId" parameterType="com.eroses.external.society.model.StatementBankInfo">
        UPDATE
        <include refid="tb"/>
        `id` = #{id} AND
        `statement_id` = #{statementId}
    </update>

    <select id="getByParam" parameterType="map" resultMap="StatementBankInfoMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </select>

    <!-- Select All -->
    <select id="listStatementBankInfo" parameterType="map" resultMap="StatementBankInfoMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        AND `application_status_code` != -1 <!-- filter out deleted -->
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- Count All -->
    <select id="countListStatementBankInfo" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        AND `application_status_code` != -1 <!-- filter out deleted -->
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </select>

    <!-- Select By ID -->
    <select id="findById" parameterType="java.lang.Long" resultMap="StatementBankInfoMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="findByStatementId" parameterType="map" resultMap="StatementBankInfoMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `statement_id` = #{statementId}
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        LIMIT 1
    </select>

    <delete id="delete" parameterType="map">
        DELETE FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </delete>

    <update id="deleteStatement" parameterType="map">
        UPDATE <include refid="tb"/>
        <set>
            `application_status_code` = -1
        </set>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </update>

    <update id="deleteBankInfo" parameterType="java.lang.Long">
        UPDATE <include refid="tb"/>
        <set>
            `application_status_code` = -1
        </set>
        WHERE `id` = #{id}
    </update>

    <update id="hardDelete" parameterType="map">
        DELETE FROM <include refid="tb"/>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </update>
</mapper>
