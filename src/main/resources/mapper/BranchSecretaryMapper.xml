<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="BranchSecretary">

    <resultMap id="BranchSecretaryMap" type="com.eroses.external.society.model.BranchSecretary">
        <result column="id" property="id"/>
        <result column="secretary_id" property="secretaryId"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="meeting_type" property="meetingType"/>
        <result column="reason_of_change" property="reasonOfChange"/>
        <result column="replacement_date" property="replacementDate"/>
        <result column="created_date" property="createdDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="modified_by" property="modifiedBy"/>
    </resultMap>

    <resultMap id="BranchSecretarySelectMap" type="com.eroses.external.society.model.BranchSecretary">
        <result column="nsb_id" property="id"/>
        <result column="nsb_secretary_id" property="secretaryId"/>
        <result column="nsb_society_id" property="societyId"/>
        <result column="nsb_society_no" property="societyNo"/>
        <result column="nsb_branch_id" property="branchId"/>
        <result column="nsb_branch_no" property="branchNo"/>
        <result column="nsb_meeting_id" property="meetingId"/>
        <result column="nsb_meeting_date" property="meetingDate"/>
        <result column="nsb_meeting_type" property="meetingType"/>
        <result column="nsb_reason_of_change" property="reasonOfChange"/>
        <result column="nsb_replacement_date" property="replacementDate"/>
        <result column="nsb_created_date" property="createdDate"/>
        <result column="nsb_created_by" property="createdBy"/>
        <result column="nsb_modified_date" property="modifiedDate"/>
        <result column="nsb_modified_by" property="modifiedBy"/>

        <association property="branchCommittee"
                     resultMap="BranchCommittee.BranchCommitteeSelectMap"/>
        <association property="branch"
                     resultMap="Branch.BranchSelectMap"/>
    </resultMap>

    <resultMap id="BranchSecretaryHistoryDetailsMap" type="com.eroses.external.society.dto.response.branchSecretary.BranchSecretaryHistoryDetailsResponse">
        <result column="id" property="id"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="society_name" property="societyName"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="branch_name" property="branchName"/>
        <result column="secretary_name" property="secretaryName"/>
        <result column="secretary_ic_no" property="secretaryIdNo"/>
        <result column="applicant_user_id" property="applicantUserId"/>
        <result column="application_date_time" property="applicationDateTime"/>
    </resultMap>

    <resultMap id="BranchSecretarySlipMap" type="com.eroses.external.society.dto.response.branchSecretary.BranchSecretarySlipResponse">
        <result column="application_date_time" property="applicationDateTime"/>
        <result column="applicant_user_id" property="applicantUserId"/>
        <result column="society_id" property="societyId"/>
        <result column="society_name" property="societyName"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_name" property="branchName"/>
        <result column="branch_no" property="branchNo"/>
        <result column="secretary_name" property="branchSecretaryName"/>
        <result column="secretary_ic_no" property="branchSecretaryIdNo"/>
    </resultMap>

    <sql id="tb">
        new_secretary_branch
    </sql>

    <sql id="selectTb">
        new_secretary_branch nsb
    </sql>

    <sql id="cols">
        secretary_id,
        society_id,
        society_no,
        branch_id,
        branch_no,
        meeting_id,
        meeting_date,
        meeting_type,
        reason_of_change,
        replacement_date,
        created_by,
        created_date,
        modified_by,
        modified_date
    </sql>

    <sql id="selectCols">
        nsb.`id` AS nsb_id,
        nsb.`secretary_id` AS nsb_secretary_id,
        nsb.`society_id` AS nsb_society_id,
        nsb.`society_no` AS nsb_society_no,
        nsb.`branch_id` AS nsb_branch_id,
        nsb.`branch_no` AS nsb_branch_no,
        nsb.`meeting_id` AS nsb_meeting_id,
        nsb.`meeting_date` AS nsb_meeting_date,
        nsb.`meeting_type` AS nsb_meeting_type,
        nsb.`reason_of_change` AS nsb_reason_of_change,
        nsb.`replacement_date` AS nsb_replacement_date,
        nsb.`created_by` AS nsb_created_by,
        nsb.`created_date` AS nsb_created_date,
        nsb.`modified_by` AS nsb_modified_by,
        nsb.`modified_date` AS nsb_modified_date
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="vals">
        #{secretaryId},
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{meetingId},
        #{meetingDate},
        #{meetingType},
        #{reasonOfChange},
        #{replacementDate},
        #{createdBy},
        now(),
        #{modifiedBy},
        now()
    </sql>

    <select id="getNewBranchSecretary" parameterType="java.lang.Long" resultMap="BranchSecretaryHistoryDetailsMap">
        SELECT
            nsb.id AS id,
            s.id AS society_id,
            s.society_name,
            s.society_no,
            b.id AS branch_id,
            b.name AS branch_name,
            b.branch_no,
            bc.committee_name AS secretary_name,
            bc.committee_ic_no AS secretary_ic_no,
            nsb.created_by AS applicant_user_id,
            nsb.created_date AS application_date_time
        FROM <include refid="tb"/> nsb
        INNER JOIN society s ON s.id = nsb.society_id
        INNER JOIN branch b ON b.id = nsb.branch_id
        INNER JOIN branch_committee bc ON bc.id = nsb.secretary_id
        WHERE nsb.id = #{newSecretaryBranchId}
    </select>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.BranchSecretary" keyProperty="secretaryId" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
            `secretary_id`,
            `society_id`,
            `society_no`,
            `branch_id`,
            `branch_no`,
            `meeting_id`,
            `meeting_date`,
            `meeting_type`,
            `reason_of_change`,
            `replacement_date`,
            `created_by`,
            `created_date`
        )
        VALUES
        (
            #{secretaryId},
            #{societyId},
            #{societyNo},
            #{branchId},
            #{branchNo},
            #{meetingId},
            #{meetingDate},
            #{meetingType},
            #{reasonOfChange},
            #{replacementDate},
            #{createdBy},
            NOW()
        )
    </insert>

    <!-- Get all -->
    <select id="getAll" resultType="list" resultMap="BranchSecretaryMap">
        SELECT
            <include refid="cols"/>
        FROM
            <include refid="tb"/>
    </select>

    <select id="search" parameterType="map" resultMap="BranchSecretaryMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="society_id != null">
                AND society_id = #{societyId}
            </if>
        </where>
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="getAllByCriteria" parameterType="map" resultMap="BranchSecretarySelectMap">
        SELECT
            <include refid="selectCols"/>,
            <include refid="BranchCommittee.selectCols"/>,
            <include refid="Branch.selectCols"/>
        FROM
            <include refid="selectTb"/>
            LEFT JOIN <include refid="BranchCommittee.selectTb"/> ON nsb.secretary_id = bc.id
            INNER JOIN <include refid="Branch.selectTb"/> ON nsb.branch_id = b.id
        WHERE 1=1
            <if test="societyId != null">
                AND nsb.`society_id` = #{societyId}
            </if>
            <if test="secretaryName != null and secretaryName != ''">
                AND bc.`committee_name` LIKE CONCAT('%', #{secretaryName}, '%')
            </if>
        ORDER BY nsb.`created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllByCriteria" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        LEFT JOIN <include refid="BranchCommittee.selectTb"/> ON nsb.secretary_id = bc.id
        INNER JOIN <include refid="Branch.selectTb"/> ON nsb.branch_id = b.id
        WHERE 1=1
        <if test="societyId != null">
            AND nsb.`society_id` = #{societyId}
        </if>
        <if test="secretaryName != null and secretaryName != ''">
            AND bc.`committee_name` LIKE CONCAT('%', #{secretaryName}, '%')
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.BranchSecretary">
        UPDATE
            <include refid="tb"/>
        <set>
            <if test="societyId != null">society_id = #{societyId},</if>
            <if test="societyNo != null">society_no = #{societyNo},</if>
            <if test="branchId != null">branch_id = #{branchId},</if>
            <if test="branchNo != null">branch_no = #{branchNo},</if>
            <if test="meetingId != null">meeting_id = #{meetingId},</if>
            <if test="meetingType != null">meeting_type = #{meetingType},</if>
            <if test="meetingDate != null">meeting_date = #{meetingDate},</if>
            <if test="reasonOfChange != null">reason_of_change = #{reasonOfChange},</if>
            <if test="replacementDate != null">replacement_date = #{replacementDate},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date = now()
        </set>
        WHERE
            secretary_id = #{secretaryId}
    </update>

    <select id="findBySocietyId" resultMap="BranchSecretaryMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
    </select>

    <select id="getSlipData" parameterType="java.lang.Long" resultMap="BranchSecretarySlipMap">
        SELECT
            s.id AS society_id,
            s.society_name,
            s.society_no,
            b.id AS branch_id,
            b.name AS branch_name,
            b.branch_no,
            bc.committee_name AS secretary_name,
            bc.committee_ic_no AS secretary_ic_no,
            nsb.created_date AS application_date_time,
            nsb.created_by AS applicant_user_id
        FROM <include refid="tb"/> nsb
        JOIN branch b ON b.id = nsb.branch_id
        JOIN branch_committee bc ON bc.id = nsb.secretary_id
        JOIN society s ON s.id = b.society_id
        WHERE nsb.id = #{newBranchSecretaryId}
    </select>
</mapper>
