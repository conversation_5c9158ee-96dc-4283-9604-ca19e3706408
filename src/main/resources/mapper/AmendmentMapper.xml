<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Amendment">
    <resultMap id="AmendmentMap" type="com.eroses.external.society.model.Amendment">
        <id column="id" property="id"/>
        <result column="old_amendment_id" property="oldAmendmentId"/>
        <result column="amended_constitution_remarks" property="amendedConstitutionRemarks"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="clause_type" property="clauseType"/>
        <result column="meeting_type" property="meetingType"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="pdf_document" property="pdfDocument"/>
        <result column="amendment_clause" property="amendmentClause"/>
        <result column="goal" property="goal"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="agreement" property="agreement"/>
        <result column="agreement_date" property="agreementDate"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="payment_id" property="paymentId"/>
        <result column="receipt_no" property="receiptNo"/>
        <result column="receipt_status" property="receiptStatus"/>
        <result column="payment_date" property="paymentDate"/>
        <result column="submission_date" property="submissionDate"/>
        <result column="reference_no" property="referenceNo"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_reference_no" property="bankReferenceNo"/>
        <result column="ro" property="ro"/>
        <result column="appeal_active" property="appealActive"/>
        <result column="transfer_date" property="transferDate"/>
        <result column="note_ro" property="noteRo"/>
        <result column="kuiri" property="kuiri"/>
        <result column="status" property="status"/>
        <result column="society_level" property="societyLevel"/>
        <result column="has_branch" property="hasBranch"/>
        <result column="category_code_jppm" property="categoryCodeJppm"/>
        <result column="sub_category_code" property="subCategoryCode"/>
        <result column="constitution_type" property="constitutionType"/>
        <result column="amendment_type" property="amendmentType"/>
        <result column="approved_date" property="approvedDate"/>
        <result column="is_queried" property="isQueried"/>
        <result column="appeal_status" property="appealStatus"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <resultMap id="AmendmentSelectMap" type="com.eroses.external.society.dto.response.GetRoAmendmentResponse">
        <id column="a_id" property="id"/>
        <result column="a_old_amendment_id" property="oldAmendmentId"/>
        <result column="a_amended_constitution_remarks" property="amendedConstitutionRemarks"/>
        <result column="a_society_id" property="societyId"/>
        <result column="a_society_no" property="societyNo"/>
        <result column="a_society_level" property="societyLevel"/>
        <result column="a_category_code_jppm" property="categoryCodeJppm"/>
        <result column="a_sub_category_code" property="subCategoryCode"/>
        <result column="a_has_branch" property="hasBranch"/>
        <result column="a_constitution_type" property="constitutionType"/>
        <result column="a_clause_type" property="clauseType"/>
        <result column="a_meeting_type" property="meetingType"/>
        <result column="a_meeting_date" property="meetingDate"/>
        <result column="a_meeting_id" property="meetingId"/>
        <result column="a_pdf_document" property="pdfDocument"/>
        <result column="a_amendment_clause" property="amendmentClause"/>
        <result column="a_goal" property="goal"/>
        <result column="a_status" property="status"/>
        <result column="a_application_status_code" property="applicationStatusCode"/>
        <result column="a_agreement" property="agreement"/>
        <result column="a_agreement_date" property="agreementDate"/>
        <result column="a_payment_method" property="paymentMethod"/>
        <result column="a_payment_id" property="paymentId"/>
        <result column="a_receipt_no" property="receiptNo"/>
        <result column="a_receipt_status" property="receiptStatus"/>
        <result column="a_payment_date" property="paymentDate"/>
        <result column="a_submission_date" property="submissionDate"/>
        <result column="a_reference_no" property="referenceNo"/>
        <result column="a_bank_name" property="bankName"/>
        <result column="a_bank_reference_no" property="bankReferenceNo"/>
        <result column="a_ro" property="ro"/>
        <result column="a_appeal_active" property="appealActive"/>
        <result column="a_transfer_date" property="transferDate"/>
        <result column="a_note_ro" property="noteRo"/>
        <result column="a_kuiri" property="kuiri"/>
        <result column="a_amendment_type" property="amendmentType"/>
        <result column="a_approved_date" property="approvedDate"/>
        <result column="a_is_queried" property="isQueried"/>
        <result column="a_created_date" property="createdDate"/>
        <result column="a_created_by" property="createdBy"/>
        <result column="a_modified_by" property="modifiedBy"/>
        <result column="a_modified_date" property="modifiedDate"/>
<!--        <result column="s_id" property="societyId"/>-->
        <result column="s_society_name" property="societyName"/>
        <result column="s_no_ppm" property="societyNoPpm"/>
        <result column="s_state_id" property="societyStateCode"/>
    </resultMap>

    <resultMap id="PendingAmendmentsMap" type="com.eroses.external.society.dto.response.roDecision.GetAllPendingAmendmentResponse">
        <id column="a_id" property="id"/>
        <result column="a_old_amendment_id" property="oldAmendmentId"/>
        <result column="a_amended_constitution_remarks" property="amendedConstitutionRemarks"/>
        <result column="a_society_id" property="societyId"/>
        <result column="a_ro" property="roName"/>
        <result column="a_transfer_date" property="transferDate"/>
        <result column="a_meeting_id" property="meetingId"/>
        <result column="a_meeting_date" property="meetingDate"/>
        <result column="a_payment_date" property="paymentDate"/>
        <result column="a_amendment_type" property="amendmentType"/>
        <result column="a_application_status_code" property="applicationStatusCode"/>
        <result column="a_is_queried" property="isQueried"/>
        <result column="a_approved_date" property="approvedDate"/>
        <result column="s_society_name" property="societyName"/>
        <result column="s_society_no" property="societyNo"/>
        <result column="s_state_id" property="stateCode"/>
        <result column="s_category_code_jppm" property="categoryCodeJppm"/>
        <result column="s_sub_category_code" property="subCategoryCode"/>
        <result column="a_constitution_type" property="constitutionType"/>
        <result column="s_application_status_code" property="societyApplicationStatusCode"/>
        <result column="a_created_by" property="createdBy"/>
        <result column="a_modified_by" property="modifiedBy"/>
    </resultMap>

    <resultMap id="DecisionRecordAmendmentMap" type="com.eroses.external.society.dto.response.roDecision.GetAllDecisionRecordAmendmentResponse">
        <id column="a_id" property="id"/>
        <result column="a_amended_constitution_remarks" property="amendedConstitutionRemarks"/>
        <result column="a_society_id" property="societyId"/>
        <result column="a_society_no" property="societyNo"/>
        <result column="s_society_name" property="societyName"/>
        <result column="a_society_level" property="societyLevel"/>
        <result column="a_category_code_jppm" property="categoryCodeJppm"/>
        <result column="a_sub_category_code" property="subCategoryCode"/>
        <result column="a_constitution_type" property="constitutionType"/>
        <result column="a_amendment_type" property="amendmentType"/>
        <result column="a_payment_date" property="paymentDate"/>
        <result column="a_submission_date" property="submissionDate"/>
        <result column="a_approved_date" property="approvedDate"/>
        <result column="a_ro" property="ro"/>
        <result column="a_application_status_code" property="applicationStatusCode"/>
        <result column="s_application_status_code" property="societyApplicationStatusCode"/>
        <result column="s_state_id" property="stateCode"/>
    </resultMap>

    <resultMap id="AmendmentSelectResultMap" type="com.eroses.external.society.model.Amendment">
        <id column="a_id" property="id"/>
        <result column="a_old_amendment_id" property="oldAmendmentId"/>
        <result column="a_amended_constitution_remarks" property="amendedConstitutionRemarks"/>
        <result column="a_society_id" property="societyId"/>
        <result column="a_society_no" property="societyNo"/>
        <result column="a_society_level" property="societyLevel"/>
        <result column="a_category_code_jppm" property="categoryCodeJppm"/>
        <result column="a_sub_category_code" property="subCategoryCode"/>
        <result column="a_has_branch" property="hasBranch"/>
        <result column="a_constitution_type" property="constitutionType"/>
        <result column="a_clause_type" property="clauseType"/>
        <result column="a_meeting_type" property="meetingType"/>
        <result column="a_meeting_date" property="meetingDate"/>
        <result column="a_meeting_id" property="meetingId"/>
        <result column="a_pdf_document" property="pdfDocument"/>
        <result column="a_amendment_clause" property="amendmentClause"/>
        <result column="a_goal" property="goal"/>
        <result column="a_status" property="status"/>
        <result column="a_application_status_code" property="applicationStatusCode"/>
        <result column="a_agreement" property="agreement"/>
        <result column="a_agreement_date" property="agreementDate"/>
        <result column="a_payment_method" property="paymentMethod"/>
        <result column="a_payment_id" property="paymentId"/>
        <result column="a_receipt_no" property="receiptNo"/>
        <result column="a_receipt_status" property="receiptStatus"/>
        <result column="a_payment_date" property="paymentDate"/>
        <result column="a_submission_date" property="submissionDate"/>
        <result column="a_reference_no" property="referenceNo"/>
        <result column="a_bank_name" property="bankName"/>
        <result column="a_bank_reference_no" property="bankReferenceNo"/>
        <result column="a_ro" property="ro"/>
        <result column="a_appeal_active" property="appealActive"/>
        <result column="a_transfer_date" property="transferDate"/>
        <result column="a_note_ro" property="noteRo"/>
        <result column="a_kuiri" property="kuiri"/>
        <result column="a_amendment_type" property="amendmentType"/>
        <result column="a_approved_date" property="approvedDate"/>
        <result column="a_is_queried" property="isQueried"/>
        <result column="a_created_date" property="createdDate"/>
        <result column="a_created_by" property="createdBy"/>
        <result column="a_modified_by" property="modifiedBy"/>
        <result column="a_modified_date" property="modifiedDate"/>

        <association property="society"
                     resultMap="Society.SocietySelectMap"/>
    </resultMap>

    <sql id="pendingCols">
        a.`id` AS a_id,
        a.`old_amendment_id` AS a_old_amendment_id,
        a.`amended_constitution_remarks` AS a_amended_constitution_remarks,
        a.`society_id` AS a_society_id,
        a.`ro` AS a_ro,
        a.`transfer_date` AS a_transfer_date,
        a.`meeting_id` AS a_meeting_id,
        a.`meeting_date` AS a_meeting_date,
        a.`payment_date` AS a_payment_date,
        a.`amendment_type` AS a_amendment_type,
        a.`application_status_code` AS a_application_status_code,
        a.`is_queried` AS a_is_queried,
        a.`approved_date` AS a_approved_date,
        s.`id` AS s_society_id,
        s.`society_name` AS s_society_name,
        s.`society_no` AS s_society_no,
        s.`state_id` AS s_state_id,
        s.`category_code_jppm` AS s_category_code_jppm,
        s.`sub_category_code` AS s_sub_category_code,
        a.`constitution_type` AS a_constitution_type,
        s.`application_status_code` AS s_application_status_code,
        a.`created_by` AS a_created_by,
        a.`created_date` AS a_created_date,
        a.`modified_by` AS a_modified_by,
        a.`modified_date` AS a_modified_date
    </sql>

    <sql id="decisionRecordAmendment">
        a.`id` AS a_id,
        a.`amended_constitution_remarks` AS a_amended_constitution_remarks,
        a.`society_id` AS a_society_id,
        a.`society_no` AS a_society_no,
        a.`society_level` AS a_society_level,
        a.`category_code_jppm` AS a_category_code_jppm,
        a.`sub_category_code` AS a_sub_category_code,
        a.`constitution_type` AS a_constitution_type,
        a.`amendment_type` AS a_amendment_type,
        a.`payment_date` AS a_payment_date,
        a.`submission_date` AS a_submission_date,
        a.`approved_date` AS a_approved_date,
        a.`ro` AS a_ro,
        a.`application_status_code` AS a_application_status_code,
        s.`society_name` AS s_society_name,
        s.`application_status_code` AS s_application_status_code,
        s.`state_id` AS s_state_id
    </sql>

    <sql id="tb">
        `amendment_list`
    </sql>

    <sql id="selectTb">
        `amendment_list` a
    </sql>

    <sql id="cols">
        `id`, `old_amendment_id`, `amended_constitution_remarks`, `society_id`, `society_no`, `society_level`, `category_code_jppm`,
        `sub_category_code`, `has_branch`, `constitution_type`, `clause_type`, `meeting_type`,
        `meeting_date`, `meeting_id`, `pdf_document`, `amendment_clause`, `goal`, `status`,
        `application_status_code`, `agreement`, `agreement_date`, `payment_method`, `payment_id`,
        `receipt_no`, `receipt_status`, `payment_date`, `submission_date`, `reference_no`, `bank_name`,
        `bank_reference_no`, `ro`, `appeal_active`, `transfer_date`, `note_ro`, `kuiri`, `amendment_type`,
        `approved_date`, `is_queried`, `created_date`, `created_by`, `modified_by`, `modified_date`, `appeal_status`
    </sql>

    <sql id="selectCols">
        a.`id` AS a_id,
        a.`old_amendment_id` AS a_old_amendment_id,
        a.`amended_constitution_remarks` AS a_amended_constitution_remarks,
        a.`society_id` AS a_society_id,
        a.`society_no` AS a_society_no,
        a.`society_level` AS a_society_level,
        a.`category_code_jppm` AS a_category_code_jppm,
        a.`sub_category_code` AS a_sub_category_code,
        a.`has_branch` AS a_has_branch,
        a.`constitution_type` AS a_constitution_type,
        a.`clause_type` AS a_clause_type,
        a.`meeting_type` AS a_meeting_type,
        a.`meeting_date` AS a_meeting_date,
        a.`meeting_id` AS a_meeting_id,
        a.`pdf_document` AS a_pdf_document,
        a.`amendment_clause` AS a_amendment_clause,
        a.`goal` AS a_goal,
        a.`status` AS a_status,
        a.`application_status_code` AS a_application_status_code,
        a.`agreement` AS a_agreement,
        a.`agreement_date` AS a_agreement_date,
        a.`payment_method` AS a_payment_method,
        a.`payment_id` AS a_payment_id,
        a.`receipt_no` AS a_receipt_no,
        a.`receipt_status` AS a_receipt_status,
        a.`payment_date` AS a_payment_date,
        a.`submission_date` AS a_submission_date,
        a.`reference_no` AS a_reference_no,
        a.`bank_name` AS a_bank_name,
        a.`bank_reference_no` AS a_bank_reference_no,
        a.`ro` AS a_ro,
        a.`appeal_active` AS a_appeal_active,
        a.`transfer_date` AS a_transfer_date,
        a.`note_ro` AS a_note_ro,
        a.`kuiri` AS a_kuiri,
        a.`amendment_type` AS a_amendment_type,
        a.`approved_date` AS a_approved_date,
        a.`is_queried` AS a_is_queried,
        a.`created_date` AS a_created_date,
        a.`created_by` AS a_created_by,
        a.`modified_by` AS a_modified_by,
        a.`modified_date` AS a_modified_date,
        a.`appeal_status` AS a_appeal_status
    </sql>

    <sql id="vals">
        #{id}, #{oldAmendmentId}, #{amendedConstitutionRemarks}, #{societyId}, #{societyNo}, #{societyLevel}, #{categoryCodeJppm},
        #{subCategoryCode}, #{hasBranch}, #{constitutionType}, #{clauseType}, #{meetingType},
        #{meetingDate}, #{meetingId}, #{pdfDocument}, #{amendmentClause}, #{goal}, #{status},
        #{applicationStatusCode}, #{agreement}, #{agreementDate}, #{paymentMethod}, #{paymentId},
        #{receiptNo}, #{receiptStatus}, #{paymentDate}, #{submissionDate}, #{referenceNo}, #{bankName},
        #{bankReferenceNo}, #{ro}, #{appealActive}, #{transferDate}, #{noteRo}, #{kuiri}, #{amendmentType},
        #{approvedDate}, #{isQueried}, NOW(), #{createdBy}, #{modifiedBy}, NOW(), #{appealStatus}
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.Amendment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findById" parameterType="java.lang.Long" resultMap="AmendmentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="getByPaymentId" parameterType="java.lang.Long" resultMap="AmendmentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `payment_id` = #{paymentId}
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.Amendment">
        UPDATE <include refid="tb"/>
        <set>
            <if test="oldAmendmentId != null">`old_amendment_id` = #{oldAmendmentId},</if>
            <if test="amendedConstitutionRemarks != null">`amended_constitution_remarks` = #{amendedConstitutionRemarks},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="societyLevel != null">`society_level` = #{societyLevel},</if>
            <if test="categoryCodeJppm != null">`category_code_jppm` = #{categoryCodeJppm},</if>
            <if test="subCategoryCode != null">`sub_category_code` = #{subCategoryCode},</if>
            <if test="hasBranch != null">`has_branch` = #{hasBranch},</if>
            <if test="constitutionType != null">`constitution_type` = #{constitutionType},</if>
            <if test="clauseType != null">`clause_type` = #{clauseType},</if>
            <if test="meetingType != null">`meeting_type` = #{meetingType},</if>
            <if test="meetingDate != null">`meeting_date` = #{meetingDate},</if>
            <if test="meetingId != null">`meeting_id` = #{meetingId},</if>
            <if test="pdfDocument != null">`pdf_document` = #{pdfDocument},</if>
            <if test="amendmentClause != null">`amendment_clause` = #{amendmentClause},</if>
            <if test="goal != null">`goal` = #{goal},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="agreement != null">`agreement` = #{agreement},</if>
            <if test="agreementDate != null">`agreement_date` = #{agreementDate},</if>
            <if test="paymentMethod != null">`payment_method` = #{paymentMethod},</if>
            <if test="paymentId != null">`payment_id` = #{paymentId},</if>
            <if test="receiptNo != null">`receipt_no` = #{receiptNo},</if>
            <if test="receiptStatus != null">`receipt_status` = #{receiptStatus},</if>
            <if test="paymentDate != null">`payment_date` = #{paymentDate},</if>
            <if test="submissionDate != null">`submission_date` = #{submissionDate},</if>
            <if test="referenceNo != null">`reference_no` = #{referenceNo},</if>
            <if test="bankName != null">`bank_name` = #{bankName},</if>
            <if test="bankReferenceNo != null">`bank_reference_no` = #{bankReferenceNo},</if>
            <if test="ro != null">`ro` = #{ro},</if>
            <if test="appealActive != null">`appeal_active` = #{appealActive},</if>
            <if test="transferDate != null">`transfer_date` = #{transferDate},</if>
            <if test="noteRo != null">`note_ro` = #{noteRo},</if>
            <if test="kuiri != null">`kuiri` = #{kuiri},</if>
            <if test="amendmentType != null">`amendment_type` = #{amendmentType},</if>
            <if test="isQueried != null">`is_queried` = #{isQueried},</if>
            <if test="approvedDate != null">`approved_date` = #{approvedDate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="appealStatus != null">`appeal_status` = #{appealStatus},</if>
            `modified_date` = NOW()
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="findAll" resultMap="AmendmentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
    </select>

<!--    <select id="findByUserInvolved" parameterType="map" resultMap="AmendmentMap">-->

<!--    </select>-->

    <select id="findByUserCreated" parameterType="map" resultMap="AmendmentMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `created_by` = #{userId}
        ORDER BY `created_at` DESC
        LIMIT #{pageable.offset}, #{pageable.pageSize}
    </select>

    <select id="findByParam" parameterType="map" resultMap="AmendmentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="id != null and id != 0">
            AND `id` = #{id}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND `goal` LIKE CONCAT('%', #{searchQuery}, '%')
        </if>
        <if test="societyId != null and societyId != 0">
            AND `society_id` = #{societyId}
        </if>
        <if test="clauseType != null and clauseType != ''">
            AND `clause_type` = #{clauseType}
        </if>
        <if test="meetingId != null and meetingId != 0">
            AND `meeting_id` = #{meetingId}
        </if>
        <if test="clauseContentId != null and clauseContentId != 0">
            AND `clause_content_id` = #{clauseContentId}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != '' and applicationStatusCode != -1">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="applicationStatusCode == -1">
            AND `application_status_code` != -1
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="searchAmendments" parameterType="map" resultMap="AmendmentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="amendmentName != null">
            AND `amendment_name` = #{amendmentName}
        </if>
        <if test="statusCode != null">
            AND `status` = #{statusCode}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countSearchedAmendments" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="amendmentName != null">
            AND `amendment_name` = #{amendmentName}
        </if>
        <if test="statusCode != null">
            AND `status` = #{statusCode}
        </if>
    </select>

    <select id="findByClauseType" parameterType="map" resultMap="AmendmentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
        <if test="searchQuery != null || searchQuery != ''">
            AND `goal` LIKE CONCAT('%', #{searchQuery}, '%')
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countAmendments" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="id != null and id != 0">
            AND `id` = #{id}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND `goal` LIKE CONCAT('%', #{searchQuery}, '%')
        </if>
        <if test="societyId != null and societyId != 0">
            AND `society_id` = #{societyId}
        </if>
        <if test="clauseType != null and clauseType != ''">
            AND `clause_type` = #{clauseType}
        </if>
        <if test="meetingId != null and meetingId != 0">
            AND `meeting_id` = #{meetingId}
        </if>
        <if test="clauseContentId != null and clauseContentId != 0">
            AND `clause_content_id` = #{clauseContentId}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != '' and applicationStatusCode != -1">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="applicationStatusCode == -1">
            AND `application_status_code` != -1
        </if>
    </select>

    <select id="getRoAmendmentsByUser" parameterType="map" resultMap="AmendmentSelectMap">
        SELECT
        <include refid="selectCols"/>,
        s.`society_name` as s_society_name, s.`NoPPM_lama` as s_no_ppm, s.`state_id` as s_state_id
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.`id` = a.`society_id`
        <if test="stateCode != null">
            WHERE s.`state_id` = #{stateCode}
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countRoAmendmentsByUser" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.`id` = a.`society_id`
        <if test="stateCode != null">
            WHERE s.`state_id` = #{stateCode}
        </if>
    </select>

    <select id="getAllPendingAmendments" parameterType="map" resultMap="PendingAmendmentsMap">
        SELECT
        <include refid="pendingCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN
        <include refid="Society.selectTb"/>
        ON s.`id` = a.`society_id`
        WHERE 1=1
        <if test="isQuery == 0"> <!-- Cater for NULL data -->
            AND (a.`is_queried` IS NULL OR a.`is_queried` = 0)
        </if>
        <if test="isQuery != null and isQuery != 0">
            AND a.`is_queried` = #{isQuery}
        </if>
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND a.`application_status_code` IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null">
            AND s.`state_id` = #{stateCode}
        </if>
        <if test="ro != null and ro != 0">
            AND a.`ro` = #{ro}
        </if>
        <if test="categoryCodeJppm != null">
            AND s.`category_code_jppm` = #{categoryCodeJppm}
        </if>
        <if test="subCategoryCode != null">
            AND s.`sub_category_code` = #{subCategoryCode}
        </if>
        <if test="societyId != null">
            AND a.`society_id` = #{societyId}
        </if>
        <if test="societyName != null">
            AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
        </if>
        ORDER BY a.`created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllPending" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN
        <include refid="Society.selectTb"/>
        ON s.`id` = a.`society_id`
        WHERE 1=1
        <if test="isQuery == 0"> <!-- Cater for NULL data -->
            AND (a.`is_queried` IS NULL OR a.`is_queried` = 0)
        </if>
        <if test="isQuery != null and isQuery != 0">
            AND a.`is_queried` = #{isQuery}
        </if>
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND a.`application_status_code` IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null">
            AND s.`state_id` = #{stateCode}
        </if>
        <if test="ro != null and ro != 0">
            AND a.`ro` = #{ro}
        </if>
        <if test="categoryCodeJppm != null">
            AND s.`category_code_jppm` = #{categoryCodeJppm}
        </if>
        <if test="subCategoryCode != null">
            AND s.`sub_category_code` = #{subCategoryCode}
        </if>
        <if test="societyId != null">
            AND a.`society_id` = #{societyId}
        </if>
        <if test="societyName != null">
            AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
        </if>
    </select>

    <select id="getAllDecisionRecordAmendment" parameterType="map" resultMap="DecisionRecordAmendmentMap">
        SELECT <include refid="decisionRecordAmendment"/>
        FROM <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/>
        ON s.`id` = a.`society_id`
        WHERE 1=1
        <choose>
            <when test="decision != null">
                AND a.`application_status_code` = #{decision}
            </when>
            <otherwise>
                AND a.`application_status_code` IN (2, 3, 4, 36)
            </otherwise>
        </choose>
        <if test="stateCode != null and stateCode != ''">
            AND s.`state_id` = #{stateCode}
        </if>
        <if test="ro != null and ro != 0">
            AND a.`ro` = #{ro}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
            OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        ORDER BY a.`created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllDecisionRecordAmendment" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/>
        ON s.`id` = a.`society_id`
        WHERE 1=1
        AND a.`application_status_code` IN (2, 3, 4, 36)
        <if test="stateCodeQuery != null and stateCodeQuery != ''">
            AND s.`state_id` = #{stateCode}
        </if>
        <if test="ro != null and ro != 0">
            AND a.`ro` = #{ro}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
        </if>
    </select>

    <select id="findAmendmentForAppeal" parameterType="map" resultMap="Appeal.AppealByUserMap">
        SELECT
        s.`id` AS s_id,
        s.`society_name` AS s_society_name,
        s.`society_no` AS s_society_no,
        s.`application_status_code` AS s_application_status_code,
        a.`id` AS a_id,
        a.`appeal_status` AS a_appeal_status,
        a.`application_status_code` AS amendment_application_status_code
        FROM <include refid="selectTb"/>
        LEFT JOIN <include refid="Society.selectTb"/>
        ON s.`id` = a.`society_id`
        WHERE 1=1
        AND a.`application_status_code` IN (4)
        AND (a.`appeal_status` = 0 OR a.`appeal_status` IS NULL)
        <if test="societyIdList != null and societyIdList.size() > 0">
            AND s.`id` IN
            <foreach item="id" collection="societyIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
            OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
    </select>

    <select id="getAllAmendmentPendingApproval" parameterType="map" resultMap="AmendmentSelectResultMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>
        FROM <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON s.id = a.society_id
        WHERE a.`application_status_code` = #{applicationStatusCode}
        AND a.`submission_date` IS NOT NULL
        AND a.`submission_date` = DATE_SUB(CURDATE(), INTERVAL #{daysAfterSubmission} DAY)
    </select>
</mapper>