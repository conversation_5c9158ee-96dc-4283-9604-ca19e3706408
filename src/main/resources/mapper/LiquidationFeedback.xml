<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="LiquidationFeedback">
    <resultMap id="MeetingMap" type="com.eroses.external.society.model.societyLiquidation.LiquidationFeedback">
        <id property="id" column="id" />
        <result column="society_id" property="societyId"/>
    </resultMap>

    <resultMap id="FeedbackListMap" type="com.eroses.external.society.dto.response.societyLiquidation.LiquidationFeedbackResponse">
        <id property="id" column="id" />
        <result column="other" property="other"/>
        <result column="ic_no" property="icNo"/>
        <result column="position_code" property="positionCode"/>
        <result column="feedback" property="feedback"/>
        <result column="other_reason" property="otherReason"/>
        <result column="false_statement" property="falseStatement"/>
        <result column="non_committee" property="nonCommittee"/>
    </resultMap>

    <sql id="tb">
        liquidation_feedback
    </sql>

    <sql id="cols">
        feedback,
        non_committee,
        non_committee_year,
        false_statement,
        other,
        other_reason
    </sql>

    <sql id="insertCols">
        <include refid="cols"/>
        id,
        liquidation_id,
        ic_no,
        society_id,
        society_no,
        branch_id,
        branch_no,
        created_by,
        created_date
    </sql>

    <sql id="updateCols">
        <include refid="cols"/>
        modified_by,
        modified_date
    </sql>

    <sql id="vals">
        #{feedback},
        #{nonCommittee},
        #{nonCommitteeYear},
        #{falseStatement},
        #{other},
        #{otherReason}
    </sql>

    <sql id="insertVals">
        <include refid="vals"/>
        #{id},
        #{liquidationId},
        #{icNo},
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{createdBy},
        now()
    </sql>

    <sql id="updateVals">
        <include refid="vals"/>
        #{modifiedBy},
        now()
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.societyLiquidation.LiquidationFeedback" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
            feedback,
            other,
            other_reason,
            liquidation_id,
            ic_no,
            society_id,
            society_no,
            branch_id,
            branch_no,
            created_by,
            created_date
        )
        VALUES
        (
            #{feedback},
            #{other},
            #{otherReason},
            #{liquidationId},
            #{icNo},
            #{societyId},
            #{societyNo},
            #{branchId},
            #{branchNo},
            #{createdBy},
            NOW()
        )
    </insert>
    
    <select id="findByLiquidationId" parameterType="com.eroses.external.society.dto.response.societyLiquidation.LiquidationFeedbackResponse" resultMap="FeedbackListMap">
        SELECT
            lf.id,
            lf.ic_no,
            lf.feedback,
            lf.non_committee,
            lf.false_statement,
            lf.other,
            lf.other_reason,
            sc.designation_code AS position_code
        FROM <include refid="tb"/> lf
        JOIN society_committee sc ON (sc.society_id = lf.society_id AND sc.identification_no = lf.ic_no)
        WHERE
            lf.liquidation_id = #{liquidationId}
    </select>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.societyLiquidation.LiquidationFeedback">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date = now()
        </set>
        WHERE
        id = #{id}
    </update>
</mapper>
