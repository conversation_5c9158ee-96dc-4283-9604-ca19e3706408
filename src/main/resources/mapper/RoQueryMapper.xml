<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="RoQuery">
    <resultMap id="RoQueryResultMap" type="com.eroses.external.society.model.RoQuery">
        <!-- ID -->
        <id property="id" column="id"/>

        <!-- Columns -->
        <result property="type" column="type"/>
        <result property="societyNo" column="society_no"/>
        <result property="societyId" column="society_id"/>
        <result property="branchId" column="branch_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="societyNonCitizenCommitteeId" column="society_non_citizen_committee_id"/>
        <result property="appealId" column="appeal_id"/>
        <result property="amendmentId" column="amendment_id"/>
        <result property="note" column="note"/>
        <result property="createdDate" column="created_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="finished" column="finished"/>
        <result property="modifiedDate" column="modified_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="principalSecretaryId" column="principal_secretary_id"/>
        <result property="liquidationId" column="liquidation_id"/>
        <result property="queryReceiver" column="query_receiver"/>
    </resultMap>

    <sql id="tb">
        `ro_query`
    </sql>

    <sql id="cols">
        `type`, `society_no`, `society_id`, `branch_id`, `branch_no`, `society_non_citizen_committee_id`, `appeal_id`,
        `amendment_id`, `note`, `created_date`, `created_by`, `finished`, `modified_date`, `modified_by`, `principal_secretary_id`,
        `liquidation_id`, `query_receiver`
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.RoQuery" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{type}, #{societyNo}, #{societyId}, #{branchId}, #{branchNo}, #{societyNonCitizenCommitteeId}, #{appealId},
        #{amendmentId}, #{note}, NOW(), #{createdBy}, #{finished}, NOW(), #{modifiedBy}, #{principalSecretaryId},
        #{liquidationId}, #{queryReceiver})
    </insert>

    <select id="findById" parameterType="java.lang.Long" resultMap="RoQueryResultMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="findByBranchId" parameterType="java.lang.Long" resultMap="RoQueryResultMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `branch_id` = #{id}
        LIMIT 1
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.RoQuery">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="type != null">`type` = #{type},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="societyNonCitizenCommitteeId != null">`society_non_citizen_committee_id = #{societyNonCitizenCommitteeId},</if>
            <if test="appealId != null">`appeal_id` = #{appealId},</if>
            <if test="amendmentId != null">`amendment_id` = #{amendmentId},</if>
            <if test="principalSecretaryId != null">`principal_secretary_id` = #{principalSecretaryId},</if>
            <if test="liquidationId != null">`liquidation_id` = #{liquidationId},</if>
            <if test="note != null">`note` = #{note},</if>
            <if test="finished != null">`finished` = #{finished},</if>
            <if test="queryReceiver != null">`query_receiver` = #{queryReceiver},</if>

            <!-- Audit Columns -->
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            `modified_date` = NOW()
        </set>
        WHERE `id` = #{id}
    </update>

    <update id="updateAppealRoQuery" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="roQuery.type != null">`type` = #{roQuery.type},</if>
            <if test="roQuery.societyNo != null">`society_no` = #{roQuery.societyNo},</if>
            <if test="roQuery.societyId != null">`society_id` = #{roQuery.societyId},</if>
            <if test="roQuery.branchId != null">`branch_id` = #{roQuery.branchId},</if>
            <if test="roQuery.branchNo != null">`branch_no` = #{roQuery.branchNo},</if>
            <if test="roQuery.appealId != null">`appeal_id` = #{roQuery.appealId},</if>
            <if test="roQuery.amendmentId != null">`amendment_id` = #{roQuery.amendmentId},</if>
            <if test="roQuery.note != null">`note` = #{roQuery.note},</if>
            <if test="roQuery.finished != null">`finished` = #{roQuery.finished},</if>
            <if test="roQuery.queryReceiver != null">`query_receiver` = #{roQuery.queryReceiver},</if>

            <!-- Audit Columns -->
            <if test="roQuery.modifiedBy != null">`modified_by` = #{roQuery.modifiedBy},</if>
            `modified_date` = NOW()
        </set>
        WHERE `appeal_id` = #{id}
    </update>

    <update id="updateLatestByCriteria" parameterType="map">
        UPDATE
            <include refid="tb"/>
        <set>
            <if test="finished != null">`finished` = #{finished},</if>

            <!-- Audit Columns -->
            <if test="modifiedBy != null">`modified_by` = #{userId},</if>
            `modified_date` = NOW()
        </set>
        WHERE `id` = (
            SELECT * FROM (
                SELECT `id`
                FROM <include refid="tb"/>
                WHERE 1 = 1
                <if test="type != null and type != '' ">
                    AND `type` = #{roQueryType}
                </if>
                <if test="societyId != null and societyId != 0 ">
                    AND `society_id` = #{societyId}
                </if>
                <if test="branchId != null and branchId != 0 ">
                    AND `branch_id` = #{branchId}
                </if>
                <if test="appealId != null and appealId != 0 ">
                    AND `appeal_id` = #{appealId}
                </if>
                <if test="amendmentId != null and amendmentId != 0">
                    AND `amendment_id` = #{amendmentId}
                </if>
                <if test="queryReceiver != null and queryReceiver != ''">
                    AND `query_receiver` = #{queryReceiver}
                </if>
                <if test="principalSecretaryId != null and principalSecretaryId != 0">
                    AND `principal_secretary_id` = #{principalSecretaryId}
                </if>
                <if test="liquidationId != null and liquidationId != 0">
                    AND `liquidation_id` = #{liquidationId}
                </if>
                ORDER BY `created_date` DESC
                LIMIT 1
            ) AS temp_table
        )
    </update>

    <select id="findAll" parameterType="map" resultMap="RoQueryResultMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId !=''">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null and branchId !=''">
            AND `branch_id` = #{branchId}
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countFindAll" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId !=''">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null and branchId !=''">
            AND `branch_id` = #{branchId}
        </if>
    </select>

    <select id="findByAppealId" resultMap="RoQueryResultMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `appeal_id` = #{id}
    </select>

    <select id="getByApprovalTypeAndCriteriaOrderByCreatedDateDesc" parameterType="map" resultMap="RoQueryResultMap">
        SELECT
            <include refid="colsWithId"/>
        FROM
            <include refid="tb"/>
        WHERE 1 = 1
        <if test="roQueryType != null and roQueryType != ''">
            AND `type` = #{roQueryType}
        </if>
        <if test="societyId != null and societyId != 0">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null and branchId != 0">
            AND `branch_id` = #{branchId}
        </if>
        <if test="appealId != null and appealId != 0">
            AND `appeal_id` = #{appealId}
        </if>
        <if test="amendmentId != null and amendmentId != 0">
            AND `amendment_id` = #{amendmentId}
        </if>
        <if test="principalSecretaryId != null and principalSecretaryId != 0">
            AND `principal_secretary_id` = #{principalSecretaryId}
        </if>
        <if test="liquidationId != null and liquidationId != 0">
            AND `liquidation_id` = #{liquidationId}
        </if>
        <if test="queryReceiver != null and queryReceiver != ''">
            AND `query_receiver` = #{queryReceiver}
        </if>
        ORDER BY `created_date` DESC
    </select>
</mapper>
