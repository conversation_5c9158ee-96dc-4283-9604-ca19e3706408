<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="NonCitizenCommittee">
    <resultMap id="NonCitizenCommitteeMap" type="com.eroses.external.society.model.NonCitizenCommittee">
        <!-- ID -->
        <id column="id" property="id"/>

        <!-- Columns -->
        <result column="active_committee_id" property="activeCommitteeId"/>
        <result column="name" property="name"/>
        <result column="citizenship_status" property="citizenshipStatus"/>
        <result column="identification_type" property="identificationType"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="applicant_country_code" property="applicantCountryCode"/>
        <result column="visa_expiration_date" property="visaExpirationDate"/>
        <result column="permit_expiration_date" property="permitExpirationDate"/>
        <result column="visa_no" property="visaNo"/>
        <result column="permit_no" property="permitNo"/>
        <result column="designation_code" property="designationCode"/>
        <result column="visa_permit_no" property="visaPermitNo"/>
        <result column="tujuanD_malaysia" property="tujuanDMalaysia"/>
        <result column="stay_duration_digit" property="stayDurationDigit"/>
        <result column="stay_duration_unit" property="stayDurationUnit"/>
        <result column="summary" property="summary"/>
        <result column="society_name" property="societyName"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="status" property="status"/>
        <result column="ro" property="ro"/>
        <result column="pembaharuan_su" property="pembaharuanSu"/>
        <result column="pem_caw" property="pemCaw"/>
        <result column="other_designation_code" property="otherDesignationCode"/>
        <result column="transfer_date" property="transferDate"/>
        <result column="note_ro" property="noteRo"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="payment_id" property="paymentId"/>
        <result column="payment_date" property="paymentDate"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="appeal_status" property="appealStatus"/>
        <result column="appointed_date" property="appointedDate"/>
        <result column="residential_address" property="residentialAddress"/>
        <result column="residential_city" property="residentialCity"/>
        <result column="titleCode" property="titleCode"/>
        <result column="gender" property="gender"/>
        <result column="nationality_status" property="nationalityStatus"/>
        <result column="email" property="email"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="telephone_number" property="telephoneNumber"/>
        <result column="is_from_registration" property="isFromRegistration"/>

        <!-- Audit Columns -->
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <resultMap id="NonCitizenCommitteeSelectResultMap" type="com.eroses.external.society.model.NonCitizenCommittee">
        <id column="sncc_id" property="id"/>
        <result column="sncc_active_committee_id" property="activeCommitteeId"/>
        <result column="sncc_name" property="name"/>
        <result column="sncc_citizenship_status" property="citizenshipStatus"/>
        <result column="sncc_identification_type" property="identificationType"/>
        <result column="sncc_identification_no" property="identificationNo"/>
        <result column="sncc_applicant_country_code" property="applicantCountryCode"/>
        <result column="sncc_visa_expiration_date" property="visaExpirationDate"/>
        <result column="sncc_permit_expiration_date" property="permitExpirationDate"/>
        <result column="sncc_visa_no" property="visaNo"/>
        <result column="sncc_permit_no" property="permitNo"/>
        <result column="sncc_designation_code" property="designationCode"/>
        <result column="sncc_visa_permit_no" property="visaPermitNo"/>
        <result column="sncc_tujuanD_malaysia" property="tujuanDMalaysia"/>
        <result column="sncc_stay_duration_digit" property="stayDurationDigit"/>
        <result column="sncc_stay_duration_unit" property="stayDurationUnit"/>
        <result column="sncc_summary" property="summary"/>
        <result column="sncc_society_name" property="societyName"/>
        <result column="sncc_society_id" property="societyId"/>
        <result column="sncc_society_no" property="societyNo"/>
        <result column="sncc_branch_id" property="branchId"/>
        <result column="sncc_branch_no" property="branchNo"/>
        <result column="sncc_application_status_code" property="applicationStatusCode"/>
        <result column="sncc_status" property="status"/>
        <result column="sncc_ro" property="ro"/>
        <result column="sncc_pembaharuan_su" property="pembaharuanSu"/>
        <result column="sncc_pem_caw" property="pemCaw"/>
        <result column="sncc_other_designation_code" property="otherDesignationCode"/>
        <result column="sncc_transfer_date" property="transferDate"/>
        <result column="sncc_note_ro" property="noteRo"/>
        <result column="sncc_meeting_id" property="meetingId"/>
        <result column="sncc_appeal_status" property="appealStatus"/>
        <result column="sncc_appointed_date" property="appointedDate"/>
        <result column="sncc_title_code" property="titleCode"/>
        <result column="sncc_gender" property="gender"/>
        <result column="sncc_nationality_status" property="nationalityStatus"/>
        <result column="sncc_email" property="email"/>
        <result column="sncc_phone_number" property="phoneNumber"/>
        <result column="sncc_telephone_number" property="telephoneNumber"/>
        <result column="sncc_is_from_registration" property="isFromRegistration"/>
        <result column="sncc_created_by" property="createdBy"/>
        <result column="sncc_created_date" property="createdDate"/>
        <result column="sncc_modified_by" property="modifiedBy"/>
        <result column="sncc_modified_date" property="modifiedDate"/>

        <association property="society" resultMap="Society.SocietySelectMap"/>
        <association property="branch" resultMap="Branch.BranchSelectMap"/>
    </resultMap>

    <sql id="tb">
        `society_non_citizen_committee`
    </sql>

    <sql id="selectTb">
        `society_non_citizen_committee` sncc
    </sql>

    <sql id="cols">
        `active_committee_id`, `name`, `citizenship_status`, `identification_type`, `identification_no`,
        `applicant_country_code`, `visa_expiration_date`, `permit_expiration_date`, `visa_no`,
        `permit_no`, `designation_code`, `visa_permit_no`, `tujuanD_malaysia`,
        `stay_duration_digit`, `stay_duration_unit`, `summary`, `society_name`,
        `society_id`, `society_no`, `branch_id`, `branch_no`,
        `application_status_code`, `created_by`, `created_date`, `modified_by`,
        `modified_date`, `status`, `ro`, `pembaharuan_su`, `appointed_date`,
        `pem_caw`, `other_designation_code`, `transfer_date`, `note_ro`, `meeting_id`,
        `payment_id`, `payment_date`,`payment_method`, `appeal_status`, `residential_address`,
        `residential_city`, `title_code`, `gender`, `nationality_status`, `email`, `telephone_number`, `phone_number`,
        `is_from_registration`
    </sql>

    <sql id="selectCols">
        sncc.`id` as sncc_id,
        sncc.`active_committee_id` as sncc_active_committee_id,
        sncc.`name` as sncc_name,
        sncc.`citizenship_status` as sncc_citizenship_status,
        sncc.`identification_type` as sncc_identification_type,
        sncc.`identification_no` as sncc_identification_no,
        sncc.`applicant_country_code` as sncc_applicant_country_code,
        sncc.`visa_expiration_date` as sncc_visa_expiration_date,
        sncc.`permit_expiration_date` as sncc_permit_expiration_date,
        sncc.`visa_no` as sncc_visa_no,
        sncc.`permit_no` as sncc_permit_no,
        sncc.`designation_code` as sncc_designation_code,
        sncc.`visa_permit_no` as sncc_visa_permit_no,
        sncc.`tujuanD_malaysia` as sncc_tujuanD_malaysia,
        sncc.`stay_duration_digit` as sncc_stay_duration_digit,
        sncc.`stay_duration_unit` as sncc_stay_duration_unit,
        sncc.`summary` as sncc_summary,
        sncc.`society_name` as sncc_society_name,
        sncc.`society_id` as sncc_society_id,
        sncc.`society_no` as sncc_society_no,
        sncc.`branch_id` as sncc_branch_id,
        sncc.`branch_no` as sncc_branch_no,
        sncc.`application_status_code` as sncc_application_status_code,
        sncc.`created_by` as sncc_created_by,
        sncc.`created_date` as sncc_created_date,
        sncc.`modified_by` as sncc_modified_by,
        sncc.`modified_date` as sncc_modified_date,
        sncc.`status` as sncc_status,
        sncc.`ro` as sncc_ro,
        sncc.`pembaharuan_su` as sncc_pembaharuan_su,
        sncc.`pem_caw` as sncc_pem_caw,
        sncc.`other_designation_code` as sncc_other_designation_code,
        sncc.`transfer_date` as sncc_transfer_date,
        sncc.`note_ro` as sncc_note_ro,
        sncc.`meeting_id` as sncc_meeting_id,
        sncc.`appointed_date` as sncc_appointed_date,
        sncc.`appeal_status` as sncc_appeal_status,
        sncc.`title_code` as sncc_title_code,
        sncc.`gender` as sncc_gender,
        sncc.`nationality_status` as sncc_nationality_status,
        sncc.`email` as sncc_email,
        sncc.`telephone_number` as sncc_telephone_number,
        sncc.`phone_number` as sncc_phone_number,
        sncc.`is_from_registration` as sncc_is_from_registration
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.NonCitizenCommittee" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{activeCommitteeId}, #{name}, #{citizenshipStatus}, #{identificationType}, #{identificationNo},
        #{applicantCountryCode}, #{visaExpirationDate}, #{permitExpirationDate}, #{visaNo},
        #{permitNo}, #{designationCode}, #{visaPermitNo}, #{tujuanDMalaysia},
        #{stayDurationDigit}, #{stayDurationUnit}, #{summary}, #{societyName},
        #{societyId}, #{societyNo}, #{branchId}, #{branchNo},
        #{applicationStatusCode}, #{createdBy}, NOW(), #{modifiedBy},
        NOW(), #{status}, #{ro}, #{pembaharuanSu}, #{appointedDate},
        #{pemCaw}, #{otherDesignationCode}, #{transferDate}, #{noteRo}, #{meetingId},
        #{paymentId}, #{paymentDate}, #{paymentMethod}, #{appealStatus}, #{residentialAddress},
        #{residentialCity}, #{titleCode}, #{gender}, #{nationalityStatus}, #{email}, #{telephoneNumber}, #{phoneNumber},
        #{isFromRegistration})
    </insert>

    <select id="findById" parameterType="java.lang.Long" resultMap="NonCitizenCommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <update id="updateAll" parameterType="list">
        <foreach collection="list" item="item" separator=";" open=" " close=" ">
            UPDATE <include refid="tb"/>
            <set>
                <if test="item.activeCommitteeId != null">`active_committee_id` = #{item.activeCommitteeId},</if>
                <if test="item.name != null and item.name != ''">`name` = #{item.name},</if>
                <if test="item.citizenshipStatus != null and item.citizenshipStatus != ''">`citizenship_status` = #{item.citizenshipStatus},</if>
                <if test="item.identificationType != null and item.identificationType != ''">`identification_type` = #{item.identificationType},</if>
                <if test="item.identificationNo != null and item.identificationNo != ''">`identification_no` = #{item.identificationNo},</if>
                <if test="item.applicantCountryCode != null and item.applicantCountryCode != ''">`applicant_country_code` = #{item.applicantCountryCode},</if>
                <if test="item.visaExpirationDate != null">`visa_expiration_date` = #{item.visaExpirationDate},</if>
                <if test="item.permitExpirationDate != null">`permit_expiration_date` = #{item.permitExpirationDate},</if>
                <if test="item.visaNo != null and item.visaNo != ''">`visa_no` = #{item.visaNo},</if>
                <if test="item.permitNo != null and item.permitNo != ''">`permit_no` = #{item.permitNo},</if>
                <if test="item.designationCode != null and item.designationCode != ''">`designation_code` = #{item.designationCode},</if>
                <if test="item.visaPermitNo != null and item.visaPermitNo != ''">`visa_permit_no` = #{item.visaPermitNo},</if>
                <if test="item.tujuanDMalaysia != null and item.tujuanDMalaysia != ''">`tujuanD_malaysia` = #{item.tujuanDMalaysia},</if>
                <if test="item.stayDurationDigit != null">`stay_duration_digit` = #{item.stayDurationDigit},</if>
                <if test="item.stayDurationUnit != null and item.stayDurationUnit != ''">`stay_duration_unit` = #{item.stayDurationUnit},</if>
                <if test="item.summary != null and item.summary != ''">`summary` = #{item.summary},</if>
                <if test="item.societyName != null and item.societyName != ''">`society_name` = #{item.societyName},</if>
                <if test="item.societyId != null">`society_id` = #{item.societyId},</if>
                <if test="item.societyNo != null and item.societyNo != ''">`society_no` = #{item.societyNo},</if>
                <if test="item.branchId != null">`branch_id` = #{item.branchId},</if>
                <if test="item.branchNo != null and item.branchNo != ''">`branch_no` = #{item.branchNo},</if>
                <if test="item.applicationStatusCode != null">`application_status_code` = #{item.applicationStatusCode},</if>
                <if test="item.status != null and item.status != ''">`status` = #{item.status},</if>
                <if test="item.ro != null and item.ro != ''">`ro` = #{item.ro},</if>
                <if test="item.pembaharuanSu != null and item.pembaharuanSu != ''">`pembaharuan_su` = #{item.pembaharuanSu},</if>
                <if test="item.pemCaw != null and item.pemCaw != ''">`pem_caw` = #{item.pemCaw},</if>
                <if test="item.otherDesignationCode != null and item.otherDesignationCode != ''">`other_designation_code` = #{item.otherDesignationCode},</if>
                <if test="item.transferDate != null">`transfer_date` = #{item.transferDate},</if>
                <if test="item.noteRo != null and item.noteRo != ''">`note_ro` = #{item.noteRo},</if>
                <if test="item.meetingId != null">`meeting_id`=#{item.meetingId},</if>
                <if test="item.appointedDate != null">`appointed_date` = #{appointedDate},</if>
                <if test="item.paymentId != null">`payment_id`=#{item.paymentId},</if>
                <if test="item.paymentDate != null">`payment_date`=#{item.paymentDate},</if>
                <if test="item.paymentMethod != null and item.paymentMethod != ''">`payment_method`=#{item.paymentMethod},</if>
                <if test="item.appealStatus != null">`appeal_status`=#{item.appealStatus},</if>
                <if test="item.titleCode != null and item.titleCode != ''">`title_code` = #{item.titleCode},</if>
                <if test="item.gender != null and item.gender != ''">`gender` = #{item.gender},</if>
                <if test="item.nationalityStatus != null and item.nationalityStatus != ''">`nationality_status` = #{item.nationalityStatus},</if>
                <if test="item.email != null and item.email != ''">`email` = #{item.email},</if>
                <if test="item.telephoneNumber != null and item.telephoneNumber != ''">`telephone_number` = #{item.telephoneNumber},</if>
                <if test="item.phoneNumber != null and item.phoneNumber != ''">`phone_number` = #{item.phoneNumber},</if>

                <!-- Audit Columns -->
                <if test="item.modifiedBy != null">`modified_by` = #{item.modifiedBy},</if>
            modified_date=NOW()
            </set>
            WHERE `id` = #{item.id}
        </foreach>
    </update>
    <update id="update" parameterType="com.eroses.external.society.model.NonCitizenCommittee">
        UPDATE <include refid="tb"/>
        <set>
            <if test="activeCommitteeId != null">`active_committee_id` = #{activeCommitteeId},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="citizenshipStatus != null">`citizenship_status` = #{citizenshipStatus},</if>
            <if test="identificationType != null">`identification_type` = #{identificationType},</if>
            <if test="identificationNo != null">`identification_no` = #{identificationNo},</if>
            <if test="applicantCountryCode != null">`applicant_country_code` = #{applicantCountryCode},</if>
            <if test="visaExpirationDate != null">`visa_expiration_date` = #{visaExpirationDate},</if>
            <if test="permitExpirationDate != null">`permit_expiration_date` = #{permitExpirationDate},</if>
            <if test="visaNo != null">`visa_no` = #{visaNo},</if>
            <if test="permitNo != null">`permit_no` = #{permitNo},</if>
            <if test="designationCode != null">`designation_code` = #{designationCode},</if>
            <if test="visaPermitNo != null">`visa_permit_no` = #{visaPermitNo},</if>
            <if test="tujuanDMalaysia != null">`tujuanD_malaysia` = #{tujuanDMalaysia},</if>
            <if test="stayDurationDigit != null">`stay_duration_digit` = #{stayDurationDigit},</if>
            <if test="stayDurationUnit != null">`stay_duration_unit` = #{stayDurationUnit},</if>
            <if test="summary != null">`summary` = #{summary},</if>
            <if test="societyName != null">`society_name` = #{societyName},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="ro != null">`ro` = #{ro},</if>
            <if test="pembaharuanSu != null">`pembaharuan_su` = #{pembaharuanSu},</if>
            <if test="pemCaw != null">`pem_caw` = #{pemCaw},</if>
            <if test="otherDesignationCode != null">`other_designation_code` = #{otherDesignationCode},</if>
            <if test="transferDate != null">`transfer_date` = #{transferDate},</if>
            <if test="noteRo != null">`note_ro` = #{noteRo},</if>
            <if test="meetingId != null">`meeting_id`=#{meetingId},</if>
            <if test="paymentId != null">`payment_id`=#{paymentId},</if>
            <if test="paymentDate != null">`payment_date`=#{paymentDate},</if>
            <if test="paymentMethod != null">`payment_method`=#{paymentMethod},</if>
            <if test="appealStatus != null">`appeal_status`=#{appealStatus},</if>
            <if test="appointedDate != null">`appointed_date` = #{appointedDate},</if>
            <if test="residentialAddress != null">`residential_address` = #{residentialAddress},</if>
            <if test="residentialCity != null">`residential_city` = #{residentialCity},</if>
            <if test="titleCode != null">`title_code` = #{titleCode},</if>
            <if test="gender != null">`gender` = #{gender},</if>
            <if test="nationalityStatus != null">`nationality_status` = #{nationalityStatus},</if>
            <if test="email != null">`email` = #{email},</if>
            <if test="telephoneNumber != null">`telephone_number` = #{telephoneNumber},</if>
            <if test="phoneNumber != null">`phone_number` = #{phoneNumber},</if>

            <!-- Audit Columns -->
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            modified_date=NOW()
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="findAll" parameterType="map" resultMap="NonCitizenCommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="id != null and id != ''">
            AND `id` = #{id}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <choose>
            <when test="branchId != null">
                AND `branch_id` = #{branchId}
            </when>
            <otherwise>
                AND `branch_id` IS NULL
            </otherwise>
        </choose>
        <if test="meetingId != null">
            AND `meeting_id` = #{meetingId}
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        <if test="documentType != null">
            AND `document_type` = #{documentType}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != ''">
            AND `application_status_code` != #{applicationStatusCode}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="findAllByParams" parameterType="map" resultMap="NonCitizenCommitteeSelectResultMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>,
        <include refid="Branch.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON sncc.society_id = s.id
        LEFT JOIN <include refid="Branch.selectTb"/> ON sncc.branch_id = b.id
        <where>
            <if test="searchQuery != null and searchQuery != ''">
                AND (
                s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
                OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
                OR sncc.`name` LIKE CONCAT('%', #{searchQuery}, '%')
                OR sncc.`identification_no` LIKE CONCAT('%', #{searchQuery}, '%')
                )
            </if>
            <if test="applicationStatusCode != null">
                AND sncc.`application_status_code` = #{applicationStatusCode}
            </if>
            <if test="isBranch == 0">
                AND sncc.`branch_id` IS NULL
            </if>
            <if test="isBranch == 1">
                AND sncc.`branch_id` IS NOT NULL
            </if>
        </where>
        ORDER BY sncc.`created_date` DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countAllByParams" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON sncc.society_id = s.id
        LEFT JOIN <include refid="Branch.selectTb"/> ON sncc.branch_id = b.id
        <where>
            <if test="searchQuery != null and searchQuery != ''">
                AND (
                s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
                OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
                OR sncc.`name` LIKE CONCAT('%', #{searchQuery}, '%')
                OR sncc.`identification_no` LIKE CONCAT('%', #{searchQuery}, '%')
                )
            </if>
            <if test="applicationStatusCode != null">
                AND sncc.`application_status_code` = #{applicationStatusCode}
            </if>
            <if test="isBranch == 0">
                AND sncc.`branch_id` IS NULL
            </if>
            <if test="isBranch == 1">
                AND sncc.`branch_id` IS NOT NULL
            </if>
        </where>
    </select>


    <select id="countFindAll" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="id != null and id != ''">
            AND `id` = #{id}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <choose>
            <when test="branchId != null">
                AND `branch_id` = #{branchId}
            </when>
            <otherwise>
                AND `branch_id` IS NULL
            </otherwise>
        </choose>
        <if test="meetingId != null">
            AND `meeting_id` = #{meetingId}
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        <if test="documentType != null">
            AND `document_type` = #{documentType}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != ''">
            AND `application_status_code` != #{applicationStatusCode}
        </if>
    </select>

    <select id="findBySocietyId" resultMap="NonCitizenCommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
        AND `branch_id` IS NULL
        AND `application_status_code` != -1
    </select>

    <select id="findByBranchId" resultMap="NonCitizenCommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `branch_id` = #{branchId}
    </select>

    <select id="findSocietyIdAndDesignationCodeByIdentificationNo" resultType="map">
        SELECT society_id, designation_code
        FROM
        <include refid="tb"/>
        WHERE identification_no = #{identificationNo}
    </select>

    <select id="existsByIdentityNoAndSocietyId" resultType="boolean">
        SELECT EXISTS (
        SELECT 1
        FROM
        <include refid="tb"/>
        WHERE
        identification_no = #{identificationNo}
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        )
    </select>

    <select id="findBySocietyIdAndApplicationStatusCode" resultMap="NonCitizenCommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        AND application_status_code = #{applicationStatusCode}
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countBySocietyIdAndApplicationStatusCode" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        AND application_status_code = #{applicationStatusCode}
    </select>

    <select id="findBySocietyIdAndApplicationStatusCodeAndStatus" parameterType="map" resultMap="NonCitizenCommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countBySocietyIdAndApplicationStatusCodeAndStatus" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>

    <select id="findBySocietyIdAndIdentificationNo" parameterType="map" resultMap="NonCitizenCommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="identificationNo != null and identificationNo !=''">
            AND identification_no = #{identificationNo}
        </if>
        LIMIT 1
    </select>

    <select id="findAllBySocietyIdAndIdentificationNo" parameterType="map" resultMap="NonCitizenCommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="identificationNo != null and identificationNo !=''">
            AND identification_no = #{identificationNo}
        </if>
    </select>

    <select id="findAllBySocietyIdAndApplicationStatusCodeAndStatus" parameterType="map" resultMap="NonCitizenCommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>

    <select id="getAllPendingSocietyNonCitizenByCriteria" parameterType="map" resultMap="NonCitizenCommitteeSelectResultMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON sncc.society_id = s.id
        WHERE 1=1
        <!--        To differentiate Society and Branch Non Citizen record-->
        AND sncc.branch_id IS NULL
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND sncc.application_status_code = #{applicationStatusCode}
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND s.state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND sncc.ro = #{ro}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND s.category_code_jppm = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND s.sub_category_code = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND s.society_name LIKE CONCAT('%', #{societyName}, '%')
        </if>
        ORDER BY sncc.created_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllPendingSocietyNonCitizenByCriteria" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON sncc.society_id = s.id
        WHERE 1=1
        <!--        To differentiate Society and Branch Non Citizen record-->
        AND sncc.branch_id IS NULL
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND sncc.application_status_code = #{applicationStatusCode}
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND s.state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND sncc.ro = #{ro}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND s.category_code_jppm = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND s.sub_category_code = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND s.society_name LIKE CONCAT('%', #{societyName}, '%')
        </if>
    </select>

    <select id="getAllPendingBranchNonCitizenByCriteria" parameterType="map" resultMap="NonCitizenCommitteeSelectResultMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>,
        <include refid="Branch.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON sncc.society_id = s.id
        INNER JOIN <include refid="Branch.selectTb"/> ON sncc.branch_id = b.id
        WHERE 1=1
<!--        To differentiate Society and Branch Non Citizen record-->
        AND sncc.branch_id IS NOT NULL
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND sncc.application_status_code = #{applicationStatusCode}
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND b.state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND sncc.ro = #{ro}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND s.category_code_jppm = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND s.sub_category_code = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND s.society_name LIKE CONCAT('%', #{societyName}, '%')
        </if>
        ORDER BY sncc.created_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllPendingBranchNonCitizenByCriteria" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON sncc.society_id = s.id
        INNER JOIN <include refid="Branch.selectTb"/> ON sncc.branch_id = b.id
        WHERE 1=1
        <!--        To differentiate Society and Branch Non Citizen record-->
        AND sncc.branch_id IS NOT NULL
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND sncc.application_status_code = #{applicationStatusCode}
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND b.state_id = #{stateCode}
        </if>
        <if test="ro != 0 and ro != ''">
            AND sncc.ro = #{ro}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND s.category_code_jppm = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND s.sub_category_code = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND s.society_name LIKE CONCAT('%', #{societyName}, '%')
        </if>
    </select>

    <select id="findByPaymentId" parameterType="long" resultMap="NonCitizenCommitteeMap">
        SELECT <include refid="colsWithId"/> FROM <include refid="tb"/>
        WHERE `payment_id` = #{paymentId}
    </select>

    <select id="findNonCitizenCommitteeForAppeal" parameterType="map" resultMap="Appeal.AppealByUserMap">
        SELECT
        sncc.`id` AS sncc_id,
        sncc.`appeal_status` AS sncc_appeal_status,
        sncc.`application_status_code` AS sncc_application_status_code,
        <if test="branchIdList != null and branchIdList.size() > 0">
            b.`id` AS b_id,
            b.`name` AS b_branch_name,
            b.`branch_no` AS b_branch_no,
            b.`application_status_code` AS b_application_status_code,
        </if>
        s.`id` AS s_id,
        s.`society_name` AS s_society_name,
        s.`society_no` AS s_society_no,
        s.`application_status_code` AS s_application_status_code
        FROM <include refid="selectTb"/>
        LEFT JOIN <include refid="Society.selectTb"/> ON s.`id` = sncc.`society_id`
        <if test="branchIdList != null and branchIdList.size() > 0">
            LEFT JOIN <include refid="Branch.selectTb"/> ON b.`id` = sncc.`branch_id`
        </if>
        WHERE 1=1 AND sncc.`application_status_code` IN (4)
        AND (sncc.`appeal_status` = 0 OR sncc.`appeal_status` IS NULL)
        <!-- Society filter -->
        <if test="societyIdList != null and societyIdList.size() > 0">
            AND s.`id` IN
            <foreach item="id" collection="societyIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND s.`application_status_code` != -1
            AND sncc.`branch_id` IS NULL
        </if>
        <!-- Branch filter -->
        <if test="branchIdList != null and branchIdList.size() > 0">
            AND b.`id` IN
            <foreach item="id" collection="branchIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND b.`application_status_code` != -1
        </if>
        <!-- Search for society -->
        <if test="societyIdList != null and societyIdList.size() > 0 and searchQuery != null and searchQuery != ''">
            AND (
            s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
            OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
    </select>
</mapper>