<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="Document">
    <resultMap id="DocumentMap" type="com.eroses.external.society.model.Document">
        <id property="id" column="id"/>
        <result column="type" property="type"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="society_committee_id" property="societyCommitteeId"/>
        <result column="branch_committee_id" property="branchCommitteeId"/>
        <result column="society_non_citizen_committee_id" property="societyNonCitizenCommitteeId"/>
        <result column="event_id" property="eventId"/>
        <result column="ic_no" property="icNo"/>
        <result column="ajk_appointment_letter_date" property="ajkAppointmentLetterDate"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="note" property="note"/>
        <result column="url" property="url"/>
        <result column="doc" property="doc"/>
        <result column="appeal_id" property="appealId"/>
        <result column="statement_id" property="statementId"/>
        <result column="amendment_id" property="amendmentId"/>
        <result column="liquidation_id" property="liquidationId"/>
        <result column="feedback_id" property="feedbackId"/>
        <result column="training_id" property="trainingId"/>
        <result column="training_material_id" property="trainingMaterialId"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="status" property="status"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <sql id="tb">
        `document`
    </sql>

    <sql id="cols">
        `type`,
        `society_id`,
        `society_no`,
        `branch_id`,
        `branch_no`,
        `meeting_id`,
        `society_committee_id`,
        `branch_committee_id`,
        `society_non_citizen_committee_id`,
        `event_id`,
        `icNo`,
        `name`,
        `code`,
        `note`,
        `url`,
        `doc`,
        `appeal_id`,
        `statement_id`,
        `amendment_id`,
        `liquidation_id`,
        `feedback_id`,
        `training_id`,
        `training_material_id`,
        `ajk_appointment_letter_date`,
        `created_by`,
        `created_date`,
        `status`,
        `modified_by`,
        `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <sql id="vals">
        #{type},
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{meetingId},
        #{societyCommitteeId},
        #{branchCommitteeId},
        #{societyNonCitizenCommitteeId},
        #{eventId},
        #{icNo},
        #{name},
        #{code},
        #{note},
        #{url},
        #{doc},
        #{appealId},
        #{statementId},
        #{amendmentId},
        #{liquidationId},
        #{feedbackId},
        #{trainingId},
        #{trainingMaterialId},
        #{ajkAppointmentLetterDate},
        #{createdBy},
        now(),
        #{status},
        #{modifiedBy},
        now()
    </sql>

    <select id="findAll" resultMap="DocumentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `status` = 1
    </select>

    <select id="findById" parameterType="java.lang.Long" resultMap="DocumentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
    </select>

    <select id="findByBranchId" parameterType="java.lang.Long" resultMap="DocumentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `branch_id` = #{branchId}
        AND `status` = 1
    </select>

    <select id="findByParam" parameterType="map" resultMap="DocumentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <where>
            `status` = 1
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="searchQuery != null and searchQuery != ''">
                AND `name` LIKE CONCAT('%', #{searchQuery}, '%')
            </if>
            <if test="branchId != null">
                AND `branch_id` = #{branchId}
            </if>
            <if test="societyId != null">
                AND `society_id` = #{societyId}
            </if>
            <if test="icNo != null">
                AND `icNo` = #{icNo}
            </if>
            <if test="meetingId != null">
                AND `meeting_id` = #{meetingId}
            </if>
            <if test="societyCommitteeId != null">
                AND `society_committee_id` = #{societyCommitteeId}
            </if>
            <if test="branchCommitteeId != null">
                AND `branch_committee_id` = #{branchCommitteeId}
            </if>
            <if test="societyNonCitizenCommitteeId != null">
                AND `society_non_citizen_committee_id` = #{societyNonCitizenCommitteeId}
            </if>
            <if test="appealId != null">
                AND `appeal_id` = #{appealId}
            </if>
            <if test="type != null">
                AND `type` = #{type}
            </if>
            <if test="note != null">
                AND `note` = #{note}
            </if>
            <if test="statementId != null">
                AND `statement_id` = #{statementId}
            </if>
            <if test="amendmentId != null">
                AND `amendment_id` = #{amendmentId}
            </if>
            <if test="liquidationId != null">
                AND `liquidation_id` = #{liquidationId}
            </if>
            <if test="feedbackId != null">
                AND `feedback_id` = #{feedbackId}
            </if>
            <if test="eventId != null">
                AND `event_id` = #{eventId}
            </if>
            <if test="trainingId != null">
                AND `training_id` = #{eventId}
            </if>
            <if test="trainingMaterialId != null">
                AND `training_material_id` = #{eventId}
            </if>
            <if test="code != null">
                AND `code` = #{code}
            </if>
            <if test="ajkAppointmentLetterDate != null">
                AND `ajk_appointment_letter_date` = #{ajkAppointmentLetterDate}
            </if>
        </where>
        ORDER BY `created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countFindDocumentByParam" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        <where>
            `status` = 1
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="searchQuery != null and searchQuery != ''">
                AND `name` LIKE CONCAT('%', #{searchQuery}, '%')
            </if>
            <if test="branchId != null">
                AND `branch_id` = #{branchId}
            </if>
            <if test="societyId != null">
                AND `society_id` = #{societyId}
            </if>
            <if test="icNo != null">
                AND `icNo` = #{icNo}
            </if>
            <if test="meetingId != null">
                AND `meeting_id` = #{meetingId}
            </if>
            <if test="societyCommitteeId != null">
                AND `society_committee_id` = #{societyCommitteeId}
            </if>
            <if test="branchCommitteeId != null">
                AND `branch_committee_id` = #{branchCommitteeId}
            </if>
            <if test="societyNonCitizenCommitteeId != null">
                AND `society_non_citizen_committee_id` = #{societyNonCitizenCommitteeId}
            </if>
            <if test="appealId != null">
                AND `appeal_id` = #{appealId}
            </if>
            <if test="type != null">
                AND `type` = #{type}
            </if>
            <if test="note != null">
                AND `note` = #{note}
            </if>
            <if test="statementId != null">
                AND `statement_id` = #{statementId}
            </if>
            <if test="amendmentId != null">
                AND `amendment_id` = #{amendmentId}
            </if>
            <if test="liquidationId != null">
                AND `liquidation_id` = #{liquidationId}
            </if>
            <if test="feedbackId != null">
                AND `feedback_id` = #{feedbackId}
            </if>
            <if test="eventId != null">
                AND `event_id` = #{eventId}
            </if>
            <if test="trainingId != null">
                AND `training_id` = #{eventId}
            </if>
            <if test="trainingMaterialId != null">
                AND `training_material_id` = #{eventId}
            </if>
            <if test="code != null">
                AND `code` = #{code}
            </if>
            <if test="ajkAppointmentLetterDate != null">
                AND `ajk_appointment_letter_date` = #{ajkAppointmentLetterDate}
            </if>
        </where>
    </select>

    <select id="findByParamSingle" parameterType="map" resultMap="DocumentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <where>
            `status` = 1
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="branchId != null">
                AND `branch_id` = #{branchId}
            </if>
            <if test="societyId != null">
                AND `society_id` = #{societyId}
            </if>
            <if test="icNo != null">
                AND `icNo` = #{icNo}
            </if>
            <if test="meetingId != null">
                AND `meeting_id` = #{meetingId}
            </if>
            <if test="societyCommitteeId != null">
                AND `society_committee_id` = #{societyCommitteeId}
            </if>
            <if test="branchCommitteeId != null">
                AND `branch_committee_id` = #{branchCommitteeId}
            </if>
            <if test="societyNonCitizenCommitteeId != null">
                AND `society_non_citizen_committee_id` = #{societyNonCitizenCommitteeId}
            </if>
            <if test="appealId != null">
                AND `appeal_id` = #{appealId}
            </if>
            <if test="type != null">
                AND `type` = #{type}
            </if>
            <if test="note != null">
                AND `note` = #{note}
            </if>
            <if test="statementId != null">
                AND `statement_id` = #{statementId}
            </if>
            <if test="amendmentId != null">
                AND `amendment_id` = #{amendmentId}
            </if>
            <if test="liquidationId != null">
                AND `liquidation_id` = #{liquidationId}
            </if>
            <if test="feedbackId != null">
                AND `feedback_id` = #{feedbackId}
            </if>
            <if test="eventId != null">
                AND `event_id` = #{eventId}
            </if>
            <if test="trainingId != null">
                AND `training_id` = #{trainingId}
            </if>
            <if test="trainingMaterialId != null">
                AND `training_material_id` = #{trainingMaterialId}
            </if>
            <if test="ajkAppointmentLetterDate != null">
                AND `ajk_appointment_letter_date` = #{ajkAppointmentLetterDate}
            </if>
            <if test="code != null">
                AND `code` = #{code}
            </if>
            <choose>
                <when test="status != null">
                    AND `status` = #{status}
                </when>
                <otherwise>
                    AND `status` != -1
                </otherwise>
            </choose>
        </where>
        ORDER BY `created_date` DESC
        LIMIT 1
    </select>

    <insert id="registerFileInDb" parameterType="com.eroses.external.society.model.Document" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="create" parameterType="com.eroses.external.society.model.Document" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="updateDocument" parameterType="com.eroses.external.society.model.Document">
        UPDATE
        <include refid="tb"/>
        <set>
            <!--            <if test="type != null">`type` = #{type},</if>-->
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="meetingId != null">`meeting_id` = #{meetingId},</if>
            <if test="societyCommitteeId != null">`society_committee_id` = #{societyCommitteeId},</if>
            <if test="branchCommitteeId != null">`branch_committee_id` = #{branchCommitteeId},</if>
            <if test="societyNonCitizenCommitteeId != null">`society_non_citizen_committee_id` =
                #{societyNonCitizenCommitteeId},
            </if>
            <if test="eventId != null">`event_id` = #{eventId},</if>
            <if test="trainingId != null">`training_id` = #{trainingId},</if>
            <if test="trainingMaterialId != null">`training_material_id` = #{trainingMaterialId},</if>
            <if test="icNo != null">`icNo` = #{icNo},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="code != null">`code` = #{code},</if>
            <if test="note != null">`note` = #{note},</if>
            <if test="url != null">`url` = #{url},</if>
            <if test="doc != null">`doc` = #{doc},</if>
            <if test="appealId != null">`appeal_id` = #{appealId},</if>
            <if test="statementId != null">`statement_id` = #{statementId},</if>
            <if test="amendmentId != null">`amendment_id` = #{amendmentId},</if>
            <if test="liquidationId != null">`liquidation_id` = #{liquidationId},</if>
            <if test="feedbackId != null">`feedback_id` = #{feedbackId},</if>
            <if test="ajkAppointmentLetterDate != null">`ajk_appointment_letter_date` = #{ajkAppointmentLetterDate},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            `modified_date` = now()
        </set>
        WHERE
        `id` = #{id}
    </update>

    <update id="updateDocumentUrl" parameterType="com.eroses.external.society.model.Document">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="type != null">`type` = #{type},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="meetingId != null">`meeting_id` = #{meetingId},</if>
            <if test="societyCommitteeId != null">`society_committee_id` = #{societyCommitteeId},</if>
            <if test="branchCommitteeId != null">`branch_committee_id` = #{branchCommitteeId},</if>
            <if test="societyNonCitizenCommitteeId != null">`society_non_citizen_committee_id` =
                #{societyNonCitizenCommitteeId},
            </if>
            <if test="eventId != null">`event_id` = #{eventId},</if>
            <if test="trainingId != null">`training_id` = #{trainingId},</if>
            <if test="trainingMaterialId != null">`training_material_id` = #{trainingMaterialId},</if>
            <if test="icNo != null">`icNo` = #{icNo},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="note != null">`note` = #{note},</if>
            <if test="url != null">`url` = #{url},</if>
            <if test="doc != null">`doc` = #{doc},</if>
            <if test="appealId != null">`appeal_id` = #{appealId},</if>
            <if test="statementId != null">`statement_id` = #{statementId},</if>
            <if test="amendmentId != null">`amendment_id` = #{amendmentId},</if>
            <if test="liquidationId != null">`liquidation_id` = #{liquidationId},</if>
            <if test="feedbackId != null">`feedback_id` = #{feedbackId},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="ajkAppointmentLetterDate != null">`ajk_appointment_letter_date` = #{ajkAppointmentLetterDate},</if>
            <if test="createdBy != null">`created_by` = #{createdBy},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            `modified_date` = now()
        </set>
        WHERE
        `id` = #{id}
    </update>

    <update id="submitDocument" parameterType="java.lang.Long">
        UPDATE
        <include refid="tb"/>
        SET `status` = 1
        WHERE `id` = #{id};
    </update>

    <update id="deleteDocument" parameterType="Long">
        UPDATE
        <include refid="tb"/>
        SET `status` = -1
        WHERE `id` = #{id}
    </update>

    <delete id="unregisterFromDb" parameterType="java.lang.Long">
        DELETE FROM
        <include refid="tb"/>
        WHERE `url` = #{url}
    </delete>

    <select id="findDocumentByIdListAndStatus" parameterType="map" resultMap="DocumentMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="ids != null and ids.size() > 0">
            AND `id` IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="applicationStatusCode != null">
            AND `status` = #{applicationStatusCode}
        </if>
    </select>

    <update id="updateDocumentStatusToInactive" parameterType="java.util.List">
        UPDATE
        <include refid="tb"/>
        SET `status` = 0
        WHERE `id` IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
