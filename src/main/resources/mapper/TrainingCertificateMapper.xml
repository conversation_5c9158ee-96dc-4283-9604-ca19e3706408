<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="TrainingCertificate">

    <resultMap id="TrainingCertificateMap" type="com.eroses.external.society.model.TrainingCertificate">
        <id property="id" column="id" />
        <result property="trainingEnrollmentId" column="training_enrollment_id" />
        <result property="certificatePath" column="certificate_path" />
        <result property="issueDate" column="issue_date" />
        <result property="verificationCode" column="verification_code" />
    </resultMap>

    <sql id="tb">
        training_certificate
    </sql>

    <sql id="cols">
        training_enrollment_id,
        certificate_path,
        issue_date,
        verification_code
    </sql>

    <sql id="colsWithId">
        id, <include refid="cols" />
    </sql>

    <!-- Create -->
    <insert id="create" parameterType="com.eroses.external.society.model.TrainingCertificate" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb" /> (
            training_enrollment_id,
            certificate_path,
            issue_date,
            verification_code
        ) VALUES (
            #{trainingEnrollmentId},
            #{certificatePath},
            NOW(),
            #{verificationCode}
        )
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.TrainingCertificate">
        UPDATE <include refid="tb" />
        SET
            certificate_path = #{certificatePath}
        WHERE id = #{id}
    </update>

    <!-- Find By ID -->
    <select id="findById" resultMap="TrainingCertificateMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE id = #{id}
    </select>

    <!-- Find By Enrollment ID -->
    <select id="findByEnrollmentId" resultMap="TrainingCertificateMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE training_enrollment_id = #{trainingEnrollmentId}
    </select>

    <!-- Find By Verification Code -->
    <select id="findByVerificationCode" resultMap="TrainingCertificateMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE verification_code = #{verificationCode}
    </select>

    <!-- Find All By User ID -->
    <select id="findAllByUserId" resultMap="TrainingCertificateMap">
        SELECT c.<include refid="colsWithId" />
        FROM <include refid="tb" /> c
        JOIN training_enrollment e ON c.training_enrollment_id = e.id
        WHERE e.user_id = #{userId}
        ORDER BY c.issue_date DESC
    </select>

</mapper>
