<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="BranchCommitteeArchive">

    <resultMap id="BranchCommitteeArchiveMap" type="com.eroses.external.society.model.BranchCommitteeArchive">
        <id property="id" column="id"/>
        <result property="committeeTableOldId" column="committee_table_old_id"/>
        <result property="branchId" column="branch_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="designationCode" column="designation_code"/>
        <result property="titleCode" column="title_code"/>
        <result property="committeeName" column="committee_name"/>
        <result property="gender" column="gender"/>
        <result property="citizenshipStatus" column="citizenship_status"/>
        <result property="identityType" column="identity_type"/>
        <result property="committeeIcNo" column="committee_ic_no"/>
        <result property="dateOfBirth" column="date_of_birth"/>
        <result property="placeOfBirth" column="place_of_birth"/>
        <result property="jobCode" column="job_code"/>
        <result property="committeeAddressStatus" column="committee_address_status"/>
        <result property="committeeAddress" column="committee_address"/>
        <result property="committeeCountryCode" column="committee_country_code"/>
        <result property="committeeStateCode" column="committee_state_code"/>
        <result property="committeeDistrict" column="committee_district"/>
        <result property="committeeSmallDistrict" column="committee_small_district"/>
        <result property="committeeCity" column="committee_city"/>
        <result property="postcode" column="postcode"/>
        <result property="email" column="email"/>
        <result property="homePhoneNumber" column="home_phone_number"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="officePhoneNumber" column="office_phone_number"/>
        <result property="committeeEmployerName" column="committee_employer_name"/>
        <result property="committeeEmployerAddressStatus" column="committee_employer_address_status"/>
        <result property="committeeEmployerAddress" column="committee_employer_address"/>
        <result property="committeeEmployerCountryCode" column="committee_employer_country_code"/>
        <result property="committeeEmployerStateCode" column="committee_employer_state_code"/>
        <result property="committeeEmployerDistrict" column="committee_employer_district"/>
        <result property="committeeEmployerCity" column="committee_employer_city"/>
        <result property="committeeEmployerPostcode" column="committee_employer_postcode"/>
        <result property="status" column="status"/>
        <result property="batalFlat" column="batal_flat"/>
        <result property="applicationStatusCode" column="application_status_code"/>
        <result property="pegHarta" column="peg_harta"/>
        <result property="otherPosition" column="other_position"/>
        <result property="tarikhTukarSu" column="tarikh_tukar_su"/>
        <result property="idSu" column="id_su"/>
        <result property="meetingId" column="meeting_id"/>
        <result property="documentId" column="document_id"/>
        <result property="documentType" column="document_type"/>
        <result property="meetingDate" column="meeting_date"/>
        <result property="markedDate" column="marked_date"/>
        <result property="appointedDate" column="appointed_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>

    <sql id="tb">
        `branch_committee_archive`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="selectTb">
        `branch_committee_archive` bca
    </sql>

    <sql id="cols">
        `committee_table_old_id`, `branch_id`, `branch_no`, `designation_code`, `title_code`, `committee_name`, `gender`,
        `citizenship_status`, `identity_type`, `committee_ic_no`, `date_of_birth`, `place_of_birth`,
        `job_code`, `committee_address_status`, `committee_address`, `committee_country_code`,
        `committee_state_code`, `committee_district`, `committee_small_district`, `committee_city`,
        `postcode`, `email`, `home_phone_number`, `phone_number`, `office_phone_number`,
        `committee_employer_name`, `committee_employer_address_status`, `committee_employer_address`,
        `committee_employer_country_code`, `committee_employer_state_code`, `committee_employer_district`,
        `committee_employer_city`, `committee_employer_postcode`, `status`, `batal_flat`,
        `application_status_code`, `peg_harta`, `other_position`, `tarikh_tukar_su`, `id_su`,
        `meeting_id`, `document_id`, `document_type`, `meeting_date`, `marked_date`, `appointed_date`,
        `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="vals">
        #{committeeTableOldId}, #{branchId}, #{branchNo}, #{designationCode}, #{titleCode}, #{committeeName}, #{gender},
        #{citizenshipStatus}, #{identityType}, #{committeeIcNo}, #{dateOfBirth}, #{placeOfBirth},
        #{jobCode}, #{committeeAddressStatus}, #{committeeAddress}, #{committeeCountryCode},
        #{committeeStateCode}, #{committeeDistrict}, #{committeeSmallDistrict}, #{committeeCity},
        #{postcode}, #{email}, #{homePhoneNumber}, #{phoneNumber}, #{officePhoneNumber},
        #{committeeEmployerName}, #{committeeEmployerAddressStatus}, #{committeeEmployerAddress},
        #{committeeEmployerCountryCode}, #{committeeEmployerStateCode}, #{committeeEmployerDistrict},
        #{committeeEmployerCity}, #{committeeEmployerPostcode}, #{status}, #{batalFlat},
        #{applicationStatusCode}, #{pegHarta}, #{otherPosition}, #{tarikhTukarSu}, #{idSu},
        #{meetingId}, #{documentId}, #{documentType}, #{meetingDate}, #{markedDate}, #{appointedDate},
        #{createdBy}, now(), #{modifiedBy}, now()
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.BranchCommitteeArchive" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="committeeTableOldId != null">committee_table_old_id,</if>
            <if test="branchId != null">branch_id,</if>
            <if test="branchNo != null">branch_no,</if>
            <if test="designationCode != null">designation_code,</if>
            <if test="titleCode != null">title_code,</if>
            <if test="committeeName != null">committee_name,</if>
            <if test="gender != null">gender,</if>
            <if test="citizenshipStatus != null">citizenship_status,</if>
            <if test="identityType != null">identity_type,</if>
            <if test="committeeIcNo != null">committee_ic_no,</if>
            <if test="dateOfBirth != null">date_of_birth,</if>
            <if test="placeOfBirth != null">place_of_birth,</if>
            <if test="jobCode != null">job_code,</if>
            <if test="committeeAddressStatus != null">committee_address_status,</if>
            <if test="committeeAddress != null">committee_address,</if>
            <if test="committeeCountryCode != null">committee_country_code,</if>
            <if test="committeeStateCode != null">committee_state_code,</if>
            <if test="committeeDistrict != null">committee_district,</if>
            <if test="committeeSmallDistrict != null">committee_small_district,</if>
            <if test="committeeCity != null">committee_city,</if>
            <if test="postcode != null">postcode,</if>
            <if test="email != null">email,</if>
            <if test="homePhoneNumber != null">home_phone_number,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="officePhoneNumber != null">office_phone_number,</if>
            <if test="committeeEmployerName != null">committee_employer_name,</if>
            <if test="committeeEmployerAddressStatus != null">committee_employer_address_status,</if>
            <if test="committeeEmployerAddress != null">committee_employer_address,</if>
            <if test="committeeEmployerCountryCode != null">committee_employer_country_code,</if>
            <if test="committeeEmployerStateCode != null">committee_employer_state_code,</if>
            <if test="committeeEmployerDistrict != null">committee_employer_district,</if>
            <if test="committeeEmployerCity != null">committee_employer_city,</if>
            <if test="committeeEmployerPostcode != null">committee_employer_postcode,</if>
            <if test="status != null">status,</if>
            <if test="batalFlat != null">batal_flat,</if>
            <if test="applicationStatusCode != null">application_status_code,</if>
            <if test="pegHarta != null">peg_harta,</if>
            <if test="otherPosition != null">other_position,</if>
            <if test="tarikhTukarSu != null">tarikh_tukar_su,</if>
            <if test="idSu != null">id_su,</if>
            <if test="meetingId != null">meeting_id,</if>
            <if test="documentId != null">document_id,</if>
            <if test="documentType != null">document_type,</if>
            <if test="meetingDate != null">meeting_date,</if>
            <if test="markedDate != null">marked_date,</if>
            <if test="appointedDate != null">appointed_date,</if>
            <if test="createdBy != null">created_by,</if>
            created_date,
            <if test="modifiedBy != null">modified_by,</if>
            modified_date,
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="committeeTableOldId != null">#{committeeTableOldId},</if>
            <if test="branchId != null">#{branchId},</if>
            <if test="branchNo != null">#{branchNo},</if>
            <if test="designationCode != null">#{designationCode},</if>
            <if test="titleCode != null">#{titleCode},</if>
            <if test="committeeName != null">#{committeeName},</if>
            <if test="gender != null">#{gender},</if>
            <if test="citizenshipStatus != null">#{citizenshipStatus},</if>
            <if test="identityType != null">#{identityType},</if>
            <if test="committeeIcNo != null">#{committeeIcNo},</if>
            <if test="dateOfBirth != null">#{dateOfBirth},</if>
            <if test="placeOfBirth != null">#{placeOfBirth},</if>
            <if test="jobCode != null">#{jobCode},</if>
            <if test="committeeAddressStatus != null">#{committeeAddressStatus},</if>
            <if test="committeeAddress != null">#{committeeAddress},</if>
            <if test="committeeCountryCode != null">#{committeeCountryCode},</if>
            <if test="committeeStateCode != null">#{committeeStateCode},</if>
            <if test="committeeDistrict != null">#{committeeDistrict},</if>
            <if test="committeeSmallDistrict != null">#{committeeSmallDistrict},</if>
            <if test="committeeCity != null">#{committeeCity},</if>
            <if test="postcode != null">#{postcode},</if>
            <if test="email != null">#{email},</if>
            <if test="homePhoneNumber != null">#{homePhoneNumber},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="officePhoneNumber != null">#{officePhoneNumber},</if>
            <if test="committeeEmployerName != null">#{committeeEmployerName},</if>
            <if test="committeeEmployerAddressStatus != null">#{committeeEmployerAddressStatus},</if>
            <if test="committeeEmployerAddress != null">#{committeeEmployerAddress},</if>
            <if test="committeeEmployerCountryCode != null">#{committeeEmployerCountryCode},</if>
            <if test="committeeEmployerStateCode != null">#{committeeEmployerStateCode},</if>
            <if test="committeeEmployerDistrict != null">#{committeeEmployerDistrict},</if>
            <if test="committeeEmployerCity != null">#{committeeEmployerCity},</if>
            <if test="committeeEmployerPostcode != null">#{committeeEmployerPostcode},</if>
            <if test="status != null">#{status},</if>
            <if test="batalFlat != null">#{batalFlat},</if>
            <if test="applicationStatusCode != null">#{applicationStatusCode},</if>
            <if test="pegHarta != null">#{pegHarta},</if>
            <if test="otherPosition != null">#{otherPosition},</if>
            <if test="tarikhTukarSu != null">#{tarikhTukarSu},</if>
            <if test="idSu != null">#{idSu},</if>
            <if test="meetingId != null">#{meetingId},</if>
            <if test="documentId != null">#{documentId},</if>
            <if test="documentType != null">#{documentType},</if>
            <if test="meetingDate != null">#{meetingDate},</if>
            <if test="markedDate != null">#{markedDate},</if>
            <if test="appointedDate != null">#{appointedDate},</if>
            <if test="createdBy != null">#{createdBy},</if>
            now(),
            <if test="modifiedBy != null">#{modifiedBy},</if>
            now(),
        </trim>
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.BranchCommitteeArchive">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="committeeTableOldId != null">`committee_table_old_id` = #{committeeTableOldId},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="designationCode != null">`designation_code` = #{designationCode},</if>
            <if test="titleCode != null">`title_code` = #{titleCode},</if>
            <if test="committeeName != null">`committee_name` = #{committeeName},</if>
            <if test="gender != null">`gender` = #{gender},</if>
            <if test="citizenshipStatus != null">`citizenship_status` = #{citizenshipStatus},</if>
            <if test="identityType != null">`identity_type` = #{identityType},</if>
            <if test="committeeIcNo != null">`committee_ic_no` = #{committeeIcNo},</if>
            <if test="dateOfBirth != null">`date_of_birth` = #{dateOfBirth},</if>
            <if test="placeOfBirth != null">`place_of_birth` = #{placeOfBirth},</if>
            <if test="jobCode != null">`job_code` = #{jobCode},</if>
            <if test="committeeAddressStatus != null">`committee_address_status` = #{committeeAddressStatus},</if>
            <if test="committeeAddress != null">`committee_address` = #{committeeAddress},</if>
            <if test="committeeCountryCode != null">`committee_country_code` = #{committeeCountryCode},</if>
            <if test="committeeStateCode != null">`committee_state_code` = #{committeeStateCode},</if>
            <if test="committeeDistrict != null">`committee_district` = #{committeeDistrict},</if>
            <if test="committeeSmallDistrict != null">`committee_small_district` = #{committeeSmallDistrict},</if>
            <if test="committeeCity != null">`committee_city` = #{committeeCity},</if>
            <if test="postcode != null">`postcode` = #{postcode},</if>
            <if test="email != null">`email` = #{email},</if>
            <if test="homePhoneNumber != null">`home_phone_number` = #{homePhoneNumber},</if>
            <if test="phoneNumber != null">`phone_number` = #{phoneNumber},</if>
            <if test="officePhoneNumber != null">`office_phone_number` = #{officePhoneNumber},</if>
            <if test="committeeEmployerName != null">`committee_employer_name` = #{committeeEmployerName},</if>
            <if test="committeeEmployerAddressStatus != null">`committee_employer_address_status` = #{committeeEmployerAddressStatus},</if>
            <if test="committeeEmployerAddress != null">`committee_employer_address` = #{committeeEmployerAddress},</if>
            <if test="committeeEmployerCountryCode != null">`committee_employer_country_code` = #{committeeEmployerCountryCode},</if>
            <if test="committeeEmployerStateCode != null">`committee_employer_state_code` = #{committeeEmployerStateCode},</if>
            <if test="committeeEmployerDistrict != null">`committee_employer_district` = #{committeeEmployerDistrict},</if>
            <if test="committeeEmployerCity != null">`committee_employer_city` = #{committeeEmployerCity},</if>
            <if test="committeeEmployerPostcode != null">`committee_employer_postcode` = #{committeeEmployerPostcode},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="batalFlat != null">`batal_flat` = #{batalFlat},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="pegHarta != null">`peg_harta` = #{pegHarta},</if>
            <if test="otherPosition != null">`other_position` = #{otherPosition},</if>
            <if test="tarikhTukarSu != null">`tarikh_tukar_su` = #{tarikhTukarSu},</if>
            <if test="idSu != null">`id_su` = #{idSu},</if>
            <if test="meetingId != null">`meeting_id` = #{meetingId},</if>
            <if test="documentId != null">`document_id` = #{documentId},</if>
            <if test="documentType != null">`document_type` = #{documentType},</if>
            <if test="meetingDate != null">`meeting_date` = #{meetingDate},</if>
            <if test="markedDate != null">`marked_date` = #{markedDate},</if>
            <if test="appointedDate != null">`appointed_date` = #{appointedDate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            `modified_date` = now()
        </set>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="committeeTableOldId != null">
            AND `committee_table_old_id` = #{committeeTableOldId}
        </if>
    </update>

    <select id="getBranchCommitteeArchive" parameterType="java.util.Map" resultMap="BranchCommitteeArchiveMap">
        SELECT
        bca.*
        FROM
        <include refid="selectTb"/>
        WHERE 1=1
        <if test="branchId != null">
            AND bca.branch_id = #{branchId}
        </if>
        <if test="branchNo != null and branchNo != ''">
            AND bca.branch_no = #{branchNo}
        </if>
        <if test="committeeIcNo != null and committeeIcNo != ''">
            AND bca.committee_ic_no = #{committeeIcNo}
        </if>
        <if test="committeeName != null and committeeName != ''">
            AND bca.committee_name LIKE CONCAT('%', #{committeeName}, '%')
        </if>
        <if test="designationCode != null">
            AND bca.designation_code = #{designationCode}
        </if>
        <if test="appointedDate != null">
            AND bca.appointed_date = #{appointedDate}
        </if>
        <if test="markedDate != null">
            AND bca.marked_date = #{markedDate}
        </if>
        <if test="status != null and status != ''">
            AND bca.status = #{status}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != ''">
            AND bca.application_status_code = #{applicationStatusCode}
        </if>
        <if test="meetingId != null">
            AND bca.meeting_id = #{meetingId}
        </if>
        <if test="meetingDate != null">
            AND bca.meeting_date = #{meetingDate}
        </if>
        ORDER BY bca.id DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countBranchCommitteeArchive" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT
        COUNT(*)
        FROM
        <include refid="selectTb"/>
        WHERE 1=1
        <if test="branchId != null">
            AND bca.branch_id = #{branchId}
        </if>
        <if test="branchNo != null and branchNo != ''">
            AND bca.branch_no = #{branchNo}
        </if>
        <if test="committeeIcNo != null and committeeIcNo != ''">
            AND bca.committee_ic_no = #{committeeIcNo}
        </if>
        <if test="committeeName != null and committeeName != ''">
            AND bca.committee_name LIKE CONCAT('%', #{committeeName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND bca.status = #{status}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != ''">
            AND bca.application_status_code = #{applicationStatusCode}
        </if>
        <if test="meetingId != null">
            AND bca.meeting_id = #{meetingId}
        </if>
        <if test="meetingDate != null">
            AND bca.meeting_date = #{meetingDate}
        </if>
    </select>

    <select id="findByMeetingDate" parameterType="java.time.LocalDate" resultMap="BranchCommitteeArchiveMap">
        SELECT
        *
        FROM
        <include refid="tb"/>
        WHERE
        meeting_date = #{meetingDate}
    </select>

    <select id="findByCommitteeOldId" parameterType="java.lang.Long" resultMap="BranchCommitteeArchiveMap">
        SELECT
        *
        FROM
        <include refid="tb"/>
        WHERE
        committee_table_old_id = #{committeeTableOldId}
    </select>

    <select id="findOneByParams" parameterType="map" resultMap="BranchCommitteeArchiveMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="committeeTableOldId != null">
            AND `committee_table_old_id` = #{committeeTableOldId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="position != null">
            AND `designation_code` = #{position}
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        <if test="markedDate != null">
            AND `marked_date` = #{markedDate}
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
        LIMIT 1
    </select>

    <select id="findAllAppointedDates" parameterType="java.lang.Long" resultType="java.time.LocalDate">
        SELECT
        DISTINCT `appointed_date`
        FROM
        <include refid="tb"/>
        WHERE
        `branch_id` = #{branchId}
        AND `application_status_code` = 11
    </select>

    <select id="findByParam" parameterType="map" resultMap="BranchCommitteeArchiveMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="meetingId != null">
            AND `meeting_id` = #{meetingId}
        </if>
        <if test="documentId != null">
            AND `document_id` = #{documentId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
        <if test="ids != null">
            AND `id` IN <foreach collection="ids" item="item" separator="," open="(" close=")">#{item}</foreach>
        </if>
        <if test="roleIds != null">
            AND `designation_code` IN (
            <foreach item="value" index="key" collection="roleIds" separator=",">
                #{value}
            </foreach>
            )
        </if>
    </select>

    <select id="findActiveCommitteesInBranchArchiveWithRoles" parameterType="map" resultMap="BranchCommitteeArchiveMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `branch_id` = #{branchId}
        AND `status` = #{status}
        AND `application_status_code` = #{applicationStatusCode}
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        AND `designation_code` IN
        <foreach separator="," collection="positions" item="position" open="(" close=")">
            #{position}
        </foreach>
    </select>
</mapper>
