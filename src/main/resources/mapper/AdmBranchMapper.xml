<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AdmBranch">
    <resultMap id="AdmBranchMap" type="com.eroses.external.society.model.AdmBranch">
        <!-- ID -->
        <id column="id" property="id"/>

        <!-- Columns -->
        <result property="code" column="code" />
        <result property="description" column="description"/>
        <result property="address" column="address" />
        <result property="cityCode" column="city_code" />
        <result property="districtCode" column="district_id" />
        <result property="stateCode" column="state_id" />
        <result property="postcode" column="postcode" />
        <result property="phoneNumber" column="phone_number" />
        <result property="faxNumber" column="fax_number" />
        <result property="status" column="status" />

        <!-- Audit Columns -->
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <sql id="tb">
        `adm_branch`
    </sql>

    <sql id="cols">
        `code`, `description`, `address`, `city_code`, `district_id`, `state_id`,
        `postcode`, `phone_number`, `fax_number`, `status`,
        `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <select id="findAll" resultMap="AdmBranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
    </select>

    <select id="findAdmBranchByJppmBranchId" resultMap="AdmBranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `id` = #{jppmBranchId}
    </select>

    <select id="findStateCodeByJppmBranchId" parameterType="java.lang.Long" resultType="java.lang.String">
        SELECT
        `state_id`
        FROM
        <include refid="tb"/>
        WHERE
        `id` = #{jppmBranchId}
        LIMIT 1
    </select>

    <select id="findById" parameterType="java.lang.Long" resultMap="AdmBranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `id` = #{id}
    </select>

    <select id="findByStateCode" parameterType="java.lang.String" resultMap="AdmBranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `state_id` = #{stateCode}
        LIMIT 1
    </select>

    <insert id="create" parameterType="com.eroses.external.society.model.AdmBranch" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
        (`code`, `description`, `address`, `city_code`, `district_id`, `state_id`,
        `postcode`, `phone_number`, `fax_number`, `status`,
        `created_by`, `created_date`, `modified_by`, `modified_date`)
        VALUES
        (#{code}, #{description}, #{address}, #{cityCode}, #{districtCode}, #{stateCode},
        #{postcode}, #{phoneNumber}, #{faxNumber}, #{status},
        #{createdBy}, now(), #{modifiedBy}, now())
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.AdmBranch">
        UPDATE <include refid="tb"/>
        <set>
            <if test="code != null">`code` = #{code},</if>
            <if test="description != null">`description` = #{description},</if>
            <if test="address != null">`address` = #{address},</if>
            <if test="cityCode != null">`city_code` = #{cityCode},</if>
            <if test="districtCode != null">`district_id` = #{districtCode},</if>
            <if test="stateCode != null">`state_id` = #{stateCode},</if>
            <if test="postcode != null">`postcode` = #{postcode},</if>
            <if test="phoneNumber != null">`phone_number` = #{phoneNumber},</if>
            <if test="faxNumber != null">`fax_number` = #{faxNumber},</if>
            <if test="status != null">`status` = #{status},</if>
            `modified_date` = now(),
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="existsByCode" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM <include refid="tb"/>
            WHERE `code` = #{code}
        )
    </select>

    <select id="existsByCodeExcludingId" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM <include refid="tb"/>
            WHERE `code` = #{code}
            AND `id` != #{id}
        )
    </select>

    <select id="getAllActive" resultMap="AdmBranchMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `status` = 1
    </select>

    <select id="list" resultMap="AdmBranchMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
    </select>

    <select id="getAll" resultMap="AdmBranchMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        <if test="nameQuery != null and nameQuery != ''">
            WHERE `description` LIKE CONCAT('%', #{nameQuery}, '%')
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countAll" parameterType="map" resultType="Long">
        SELECT COUNT(*)
        FROM <include refid="tb"/>
        <if test="nameQuery != null and nameQuery != ''">
            WHERE `description` LIKE CONCAT('%', #{nameQuery}, '%')
        </if>
    </select>

    <delete id="delete" parameterType="long">
        DELETE FROM <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <select id="findAllByCodes" parameterType="java.util.List" resultMap="AdmBranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `code` IN
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <select id="findByCode" parameterType="map" resultMap="AdmBranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `code` = #{code}
        LIMIT 1
    </select>

    <select id="getAllByStateCode" parameterType="java.lang.String" resultMap="AdmBranchMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `state_id` = #{stateCode}
    </select>
</mapper>
