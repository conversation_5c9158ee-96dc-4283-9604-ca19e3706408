<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="Meeting">

<!--    TO BE REMOVED, use MeetingMapV2-->
    <resultMap id="MeetingMap" type="com.eroses.external.society.model.Meeting">
        <id property="id" column="id"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="statement_id" property="statementId"/>
        <result column="amendment_id" property="amendmentId"/>
        <result column="meeting_type" property="meetingType"/>
        <result column="meeting_purpose" property="meetingPurpose"/>
        <result column="meeting_place" property="meetingPlace"/>
        <result column="meeting_method" property="meetingMethod"/>
        <result column="platform_type" property="platformType"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="meeting_time" property="meetingTime"/>
        <result column="meeting_time_to" property="meetingTimeTo"/>
        <result column="meeting_time_duration_minutes" property="meetingTimeDurationMinutes"/>
        <result column="GIS_information" property="GISInformation"/>
        <result column="meeting_address" property="meetingAddress"/>
        <result column="state" property="state"/>
        <result column="district" property="district"/>
        <result column="city" property="city"/>
        <result column="postcode" property="postcode"/>
        <result column="total_attendees" property="totalAttendees"/>
        <result column="opening_remarks" property="openingRemarks"/>
        <result column="meeting_content" property="meetingContent"/>
        <result column="matters_discussed" property="mattersDiscussed"/>
        <result column="other_matters" property="otherMatters"/>
        <result column="closing" property="closing"/>
        <result column="provided_by" property="providedBy"/>
        <result column="confirm_by" property="confirmBy"/>
        <result column="meeting_minute" property="meetingMinute"/>
        <result column="status" property="status"/>
        <result column="created_date" property="createdDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="modified_by" property="modifiedBy"/>
    </resultMap>

    <resultMap id="MeetingPagingMap" type="com.eroses.external.society.dto.response.meeting.MeetingPagingResponse">
        <id property="id" column="id"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="statement_id" property="statementId"/>
        <result column="amendment_id" property="amendmentId"/>
        <result column="meeting_type" property="meetingType"/>
        <result column="meeting_purpose" property="meetingPurpose"/>
        <result column="meeting_place" property="meetingPlace"/>
        <result column="meeting_method" property="meetingMethod"/>
        <result column="platform_type" property="platformType"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="meeting_time" property="meetingTime"/>
        <result column="meeting_time_to" property="meetingTimeTo"/>
        <result column="meeting_time_duration_minutes" property="meetingTimeDurationMinutes"/>
        <result column="GIS_information" property="GISInformation"/>
        <result column="meeting_address" property="meetingAddress"/>
        <result column="state" property="state"/>
        <result column="district" property="district"/>
        <result column="city" property="city"/>
        <result column="postcode" property="postcode"/>
        <result column="total_attendees" property="totalAttendees"/>
        <result column="opening_remarks" property="openingRemarks"/>
        <result column="meeting_content" property="meetingContent"/>
        <result column="matters_discussed" property="mattersDiscussed"/>
        <result column="other_matters" property="otherMatters"/>
        <result column="closing" property="closing"/>
        <result column="provided_by" property="providedBy"/>
        <result column="confirm_by" property="confirmBy"/>
        <result column="meeting_minute" property="meetingMinute"/>
        <result column="status" property="status"/>
    </resultMap>

    <resultMap id="BranchMeetingMap" type="com.eroses.external.society.dto.response.meeting.BranchMeetingResponse">
        <id property="id" column="id"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="meeting_type" property="meetingType"/>
        <result column="meeting_purpose" property="meetingPurpose"/>
        <result column="meeting_place" property="meetingPlace"/>
        <result column="meeting_method" property="meetingMethod"/>
        <result column="platform_type" property="platformType"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="meeting_time" property="meetingTime"/>
        <result column="GIS_information" property="GISInformation"/>
        <result column="meeting_address" property="meetingAddress"/>
        <result column="state" property="state"/>
        <result column="district" property="district"/>
        <result column="city" property="city"/>
        <result column="postcode" property="postcode"/>
        <result column="total_attendees" property="totalAttendees"/>
        <result column="opening_remarks" property="openingRemarks"/>
        <result column="meeting_content" property="meetingContent"/>
        <result column="matters_discussed" property="mattersDiscussed"/>
        <result column="other_matters" property="otherMatters"/>
        <result column="closing" property="closing"/>
        <result column="provided_by" property="providedBy"/>
        <result column="confirm_by" property="confirmBy"/>
        <result column="meeting_minute" property="meetingMinute"/>
        <result column="status" property="status"/>
        <collection property="memberAttendances" ofType="com.eroses.external.society.dto.response.meeting.MeetingMemberAttendanceResponse">
            <id column="member_id" property="id"/>
            <result column="member_name" property="name"/>
            <result column="member_position" property="position"/>
        </collection>
    </resultMap>

    <resultMap id="MeetingMapV2" type="com.eroses.external.society.dto.response.meeting.MeetingResponse">
        <id property="id" column="id"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="meeting_type" property="meetingType"/>
        <result column="meeting_purpose" property="meetingPurpose"/>
        <result column="meeting_place" property="meetingPlace"/>
        <result column="meeting_method" property="meetingMethod"/>
        <result column="platform_type" property="platformType"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="meeting_time" property="meetingTime"/>
        <result column="meeting_time_to" property="meetingTimeTo"/>
        <result column="meeting_time_duration_minutes" property="meetingTimeDurationMinutes"/>
        <result column="GIS_information" property="GISInformation"/>
        <result column="meeting_address" property="meetingAddress"/>
        <result column="state" property="state"/>
        <result column="district" property="district"/>
        <result column="city" property="city"/>
        <result column="postcode" property="postcode"/>
        <result column="total_attendees" property="totalAttendees"/>
        <result column="opening_remarks" property="openingRemarks"/>
        <result column="meeting_content" property="meetingContent"/>
        <result column="matters_discussed" property="mattersDiscussed"/>
        <result column="other_matters" property="otherMatters"/>
        <result column="closing" property="closing"/>
        <result column="provided_by" property="providedBy"/>
        <result column="confirm_by" property="confirmBy"/>
        <result column="meeting_minute" property="meetingMinute"/>
        <result column="status" property="status"/>
        <collection property="meetingMemberAttendances" ofType="com.eroses.external.society.dto.response.meeting.MeetingMemberAttendanceResponse">
            <id column="member_id" property="id"/>
            <result column="member_name" property="name"/>
            <result column="member_position" property="position"/>
        </collection>
    </resultMap>

    <resultMap id="MeetingBasicListMap" type="com.eroses.external.society.dto.response.meeting.MeetingBasicListResponse">
        <id property="id" column="id"/>
        <result column="meeting_type" property="meetingType"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="member_count" property="memberCount"/>
        <result column="ajk_count" property="ajkCount"/>
    </resultMap>

    <sql id="tb">
        meeting
    </sql>

    <sql id="cols">
        society_id,
        society_no,
        branch_id,
        branch_no,
        statement_id,
        amendment_id,
        meeting_type,
        meeting_purpose,
        meeting_place,
        meeting_method,
        platform_type,
        meeting_date,
        meeting_time,
        meeting_time_to,
        meeting_time_duration_minutes,
        GIS_information,
        meeting_address,
        state,
        district,
        city,
        postcode,
        total_attendees,
        opening_remarks,
        meeting_content,
        matters_discussed,
        other_matters,
        closing,
        provided_by,
        confirm_by,
        meeting_minute,
        status,
        created_by,
        created_date,
        modified_by,
        modified_date
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <sql id="vals">
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{statementId},
        #{amendmentId},
        #{meetingType},
        #{meetingPurpose},
        #{meetingPlace},
        #{meetingMethod},
        #{platformType},
        #{meetingDate},
        #{meetingTime},
        #{GISInformation},
        #{meetingAddress},
        #{state},
        #{district},
        #{city},
        #{postcode},
        #{totalAttendees},
        #{openingRemarks},
        #{meetingContent},
        #{mattersDiscussed},
        #{otherMatters},
        #{closing},
        #{providedBy},
        #{confirmBy},
        #{meetingMinute},
        #{status},
        #{createdBy},
        now(),
        #{modifiedBy},
        now()
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.Meeting" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
            society_id,
            society_no,
            branch_id,
            branch_no,
            statement_id,
            amendment_id,
            meeting_type,
            meeting_purpose,
            meeting_place,
            meeting_method,
            platform_type,
            meeting_date,
            meeting_time,
            meeting_time_to,
            meeting_time_duration_minutes,
            GIS_information,
            meeting_address,
            state,
            district,
            city,
            postcode,
            total_attendees,
            opening_remarks,
            meeting_content,
            matters_discussed,
            other_matters,
            closing,
            provided_by,
            confirm_by,
            meeting_minute,
            status,
            created_by,
            created_date
        )
        VALUES
        (
            #{societyId},
            #{societyNo},
            #{branchId},
            #{branchNo},
            #{statementId},
            #{amendmentId},
            #{meetingType},
            #{meetingPurpose},
            #{meetingPlace},
            #{meetingMethod},
            #{platformType},
            #{meetingDate},
            #{meetingTime},
            #{meetingTimeTo},
            #{meetingTimeDurationMinutes},
            #{GISInformation},
            #{meetingAddress},
            #{state},
            #{district},
            #{city},
            #{postcode},
            #{totalAttendees},
            #{openingRemarks},
            #{meetingContent},
            #{mattersDiscussed},
            #{otherMatters},
            #{closing},
            #{providedBy},
            #{confirmBy},
            #{meetingMinute},
            #{status},
            #{createdBy},
            now()
        )
    </insert>

    <!-- Get all -->
    <select id="getAll" resultType="list" resultMap="MeetingMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE
        status != '-1'
    </select>

    <select id="search" parameterType="map" resultMap="MeetingPagingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <where>
            society_id = #{societyId}
            <choose>
                <when test="branchId != null">
                    AND `branch_id` = #{branchId}
                </when>
                <otherwise>
                    AND `branch_id` IS NULL
                </otherwise>
            </choose>
            <if test="meetingType != null">
                AND meeting_type = #{meetingType}
            </if>
            <if test="meetingPlace != null">
                AND meeting_place = #{meetingPlace}
            </if>
            <if test="meetingYear != null">
                AND YEAR(meeting_date) = #{meetingYear}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY meeting_date DESC
        <if test="pageSize != null and pageNo != null">
            LIMIT #{pageNo}, #{pageSize}
        </if>
    </select>

    <select id="findMeetingsTotalCount" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM <include refid="tb"/>
        <where>
            society_id = #{societyId}
            <choose>
                <when test="branchId != null">
                    AND `branch_id` = #{branchId}
                </when>
                <otherwise>
                    AND `branch_id` IS NULL
                </otherwise>
            </choose>
            <if test="meetingType != null">
                AND meeting_type = #{meetingType}
            </if>
            <if test="meetingYear != null">
                AND YEAR(meeting_date) = #{meetingYear}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

    <select id="findById" parameterType="java.lang.Long" resultMap="MeetingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
    </select>

    <select id="findMeetingAndMembers" parameterType="java.lang.Long" resultMap="MeetingMapV2">
        SELECT
            m.id,
            m.society_id,
            m.society_no,
            m.branch_id,
            m.branch_no,
            m.meeting_type,
            m.meeting_purpose,
            m.meeting_place,
            m.meeting_method,
            m.platform_type,
            m.meeting_date,
            m.meeting_time,
            m.meeting_time_to,
            m.meeting_time_duration_minutes,
            m.GIS_information,
            m.meeting_address,
            m.state,
            m.district,
            m.city,
            m.postcode,
            m.total_attendees,
            m.opening_remarks,
            m.meeting_content,
            m.matters_discussed,
            m.other_matters,
            m.closing,
            m.provided_by,
            m.confirm_by,
            m.meeting_minute,
            mma.id AS member_id,
            mma.name AS member_name,
            mma.position AS member_position
        FROM <include refid="tb"/> m
        LEFT JOIN meeting_member_attendance mma ON (mma.meeting_id = m.id AND mma.status != -1)
        WHERE m.id = #{id}
        ORDER BY m.created_date DESC
    </select>

    <select id="findByBranchNo" resultMap="MeetingMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `branch_no` = #{branchNo}
        LIMIT 1
    </select>

    <!-- TO BE REMOVED, use findByBranchIdV2 -->
    <select id="findByBranchId" resultMap="MeetingMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `branch_id` = #{branchId}
    </select>

    <select id="findByBranchIdAndMeetingTypes" resultMap="BranchMeetingMap">
        SELECT
            m.id,
            m.society_id,
            m.society_no,
            m.branch_id,
            m.branch_no,
            m.meeting_type,
            m.meeting_purpose,
            m.meeting_place,
            m.meeting_method,
            m.platform_type,
            m.meeting_date,
            m.meeting_time,
            m.meeting_time_to,
            m.meeting_time_duration_minutes,
            m.GIS_information,
            m.meeting_address,
            m.state,
            m.district,
            m.city,
            m.postcode,
            m.total_attendees,
            m.opening_remarks,
            m.meeting_content,
            m.matters_discussed,
            m.other_matters,
            m.closing,
            m.provided_by,
            m.confirm_by,
            m.meeting_minute,
            m.status,
            mma.id AS member_id,
            mma.name AS member_name,
            mma.position AS member_position
        FROM <include refid="tb"/> m
        LEFT JOIN meeting_member_attendance mma ON (mma.meeting_id = m.id AND mma.status != -1)
        WHERE 1=1
        <if test="branchId != null">
            AND m.branch_id = #{branchId}
        </if>
        <if test="meetingTypes != null and meetingTypes.size() > 0">
                AND m.meeting_type IN
                <foreach item="item" index="index" collection="meetingTypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
        </if>
        ORDER BY m.created_date DESC
    </select>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.Meeting">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="branchNo != null">branch_no = #{branchNo},</if>
            <if test="branchId != null">branch_id = #{branchId},</if>
            <if test="societyId != null">society_id = #{societyId},</if>
            <if test="societyNo != null">society_no = #{societyNo},</if>
            <if test="statementId != null">statement_id = #{statementId},</if>
            <if test="amendmentId != null">amendment_id = #{amendmentId}</if>
            <if test="meetingType != null">meeting_type = #{meetingType},</if>
            <if test="meetingPurpose != null">meeting_purpose = #{meetingPurpose},</if>
            <if test="meetingPlace != null">meeting_place = #{meetingPlace},</if>
            <if test="meetingMethod != null">meeting_method = #{meetingMethod},</if>
            <if test="platformType != null">platform_type = #{platformType},</if>
            <if test="meetingDate != null">meeting_date = #{meetingDate},</if>
            <if test="meetingTime != null">meeting_time = #{meetingTime},</if>
            <if test="meetingTimeTo != null">meeting_time_to = #{meetingTimeTo},</if>
            <if test="meetingTimeDurationMinutes != null">meeting_time_duration_minutes = #{meetingTimeDurationMinutes},</if>
            <if test="GISInformation != null">GIS_information = #{GISInformation},</if>
            <if test="meetingAddress != null">meeting_address = #{meetingAddress},</if>
            <if test="state != null">state = #{state},</if>
            <if test="district != null">district = #{district},</if>
            <if test="city != null">city = #{city},</if>
            <if test="postcode != null">postcode = #{postcode},</if>
            <if test="totalAttendees != null">total_attendees = #{totalAttendees},</if>
            <if test="openingRemarks != null">opening_remarks = #{openingRemarks},</if>
            <if test="meetingContent != null">meeting_content = #{meetingContent},</if>
            <if test="mattersDiscussed != null">matters_discussed = #{mattersDiscussed},</if>
            <if test="otherMatters != null">other_matters = #{otherMatters},</if>
            <if test="closing != null">closing = #{closing},</if>
            <if test="providedBy != null">provided_by = #{providedBy},</if>
            <if test="confirmBy != null">confirm_by = #{confirmBy},</if>
            <if test="meetingMinute != null">meeting_minute = #{meetingMinute},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date = now()
        </set>
        WHERE
        id = #{id}
    </update>

    <select id="findBySocietyId" resultMap="MeetingMap">
        SELECT <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
        AND branch_id IS NULL
        ORDER BY created_date DESC
    </select>

    <select id="findBySocietyIdAndMeetingTypes" resultMap="MeetingMap">
        SELECT <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
        AND branch_id IS NULL
        <if test="meetingTypes != null and meetingTypes.size() > 0">
            AND meeting_type IN
            <foreach item="item" index="index" collection="meetingTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY created_date DESC
    </select>

    <select id="findStatementMeeting" resultMap="MeetingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        LIMIT 1
    </select>

    <select id="findByStatementId" resultMap="MeetingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `statement_id` = #{statementId}
        LIMIT 1
    </select>

    <select id="countByCriteria" resultType="int">
        SELECT COUNT(*)
        FROM <include refid="tb"/>
        WHERE 1 = 1
        <if test="societyId != null">
            AND society_id = #{societyId}
        </if>
        <if test="branchId == null ">
            AND branch_id IS NULL
        </if>
        <if test="branchId != null">
            AND branch_id = #{branchId}
        </if>
    </select>

    <select id="findBySocietyIdAndBranchId" resultMap="MeetingBasicListMap">
        SELECT
            m.id,
            m.meeting_type,
            m.meeting_date,
            COUNT(*) AS member_count,
            COUNT(
                CASE
                WHEN mma.position != "10"
                    THEN 1
                    ELSE NULL
                END
            ) AS ajk_count
        FROM
            <include refid="tb"/> m
        LEFT JOIN meeting_member_attendance mma ON mma.meeting_id = m.id
        WHERE 1=1
        <if test="branchId != null">
            AND m.branch_id = #{branchId}
        </if>
            <if test="societyId != null">
                AND m.society_id = #{societyId}
            </if>
            <if test="meetingType != null">
                AND m.meeting_type = #{meetingType}
            </if>
            <if test="meetingDate != null">
                AND m.meeting_date = #{meetingDate}
            </if>
        GROUP BY m.id
    </select>

    <select id="findBySocietyIdAndMeetingType" parameterType="map" resultMap="MeetingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="meetingType != null">
            AND `meeting_type` = #{meetingType}
        </if>
    </select>

    <select id="getMeetingInfoForStatement" parameterType="map" resultMap="MeetingMapV2">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="meetingId != null">
                AND `id` = #{meetingId}
            </if>
            <if test="statementId != null">
                AND `statement_id` = #{statementId}
            </if>
            <if test="societyId != null">
                AND `society_id` = #{societyId}
            </if>
            <if test="meetingDate != null">
                AND `meeting_date` = #{meetingDate}
            </if>
            <choose>
                <when test="branchId != null">
                    AND `branch_id` = #{branchId}
                </when>
                <otherwise>
                    AND `branch_id` IS NULL
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="findByDateAndTypeAndSocietyId" resultType="com.eroses.external.society.model.Meeting">
        SELECT *
        FROM meeting
        WHERE meeting_date = #{meetingDate}
        AND meeting_type = #{meetingType}
        AND society_id = #{societyId}
        <if test="branchId != null">
            AND branch_id = #{branchId}
        </if>
    </select>

    <select id="findByBranchIdAndMeetingType" parameterType="map" resultMap="MeetingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="id != null">
            AND `branch_id` = #{id}
        </if>
        <if test="code != null">
            AND `meeting_type` = #{code}
        </if>
    </select>

</mapper>
