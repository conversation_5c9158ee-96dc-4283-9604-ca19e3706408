<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.eroses.external.society.mappers.grant.GrantTemplateSocietyCategoryDao">
    
    <resultMap id="grantTemplateSocietyCategoryResultMap" type="com.eroses.external.society.model.grant.GrantTemplateSocietyCategory">
        <id property="id" column="id"/>
        <result property="grantTemplateId" column="grant_template_id"/>
        <result property="societyCategoryId" column="society_category_id"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
    </resultMap>
    
    <insert id="create" parameterType="com.eroses.external.society.model.grant.GrantTemplateSocietyCategory" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO grant_template_society_category (
            grant_template_id,
            society_category_id,
            created_by,
            created_date
        ) VALUES (
            #{grantTemplateId},
            #{societyCategoryId},
            #{createdBy},
            #{createdDate}
        )
    </insert>
    
    <select id="findByGrantTemplateId" parameterType="long" resultMap="grantTemplateSocietyCategoryResultMap">
        SELECT * FROM grant_template_society_category
        WHERE grant_template_id = #{grantTemplateId}
    </select>
    
    <select id="existsBySocietyCategoryId" parameterType="long" resultType="boolean">
        SELECT EXISTS (
            SELECT 1 FROM grant_template_society_category
            WHERE society_category_id = #{societyCategoryId}
        )
    </select>
    
    <delete id="deleteByGrantTemplateId" parameterType="long">
        DELETE FROM grant_template_society_category
        WHERE grant_template_id = #{grantTemplateId}
    </delete>
    
    <delete id="delete" parameterType="long">
        DELETE FROM grant_template_society_category
        WHERE id = #{id}
    </delete>
    
</mapper>
