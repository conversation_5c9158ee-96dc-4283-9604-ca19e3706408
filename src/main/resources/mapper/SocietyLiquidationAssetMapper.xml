<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="SocietyLiquidationAsset">
    <resultMap id="SocietyLiquidationAssetMap" type="com.eroses.external.society.model.societyLiquidation.SocietyLiquidationAsset">
        <id property="id" column="id" />
        <result column="liquidation_id" property="liquidationId"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="ic_no" property="icNo"/>
        <result column="asset_type" property="assetType"/>
        <result column="asset_value" property="assetValue"/>
        <result column="donation" property="donation"/>
        <result column="liability" property="liability"/>
        <result column="balance" property="balance"/>
        <result column="other" property="other"/>
        <result column="other_reason" property="otherReason"/>
        <result column="status" property="status"/>
        <result column="created_date" property="createdDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="modified_by" property="modifiedBy"/>
    </resultMap>

    <resultMap id="AssetBasicMap" type="com.eroses.external.society.dto.response.societyLiquidation.LiquidationAssetResponse">
        <id property="id" column="id" />
        <result column="asset_type" property="assetType"/>
        <result column="asset_value" property="assetValue"/>
        <result column="donation" property="donation"/>
        <result column="liability" property="liability"/>
        <result column="balance" property="balance"/>
        <result column="other" property="other"/>
        <result column="other_reason" property="otherReason"/>
    </resultMap>

    <sql id="tb">
        liquidation_asset
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="valsWithId">
        `id`, <include refid="vals"/>
    </sql>

    <sql id="cols">
        liquidation_id,
        society_id,
        society_no,
        branch_id,
        branch_no,
        ic_no,
        asset_type,
        asset_value,
        donation,
        liability,
        balance,
        other,
        other_reason,
        status,
        created_date,
        created_by,
        modified_date,
        modified_by
    </sql>

    <sql id="insertCols">
        liquidation_id,
        society_id,
        society_no,
        branch_id,
        branch_no,
        ic_no,
        asset_type,
        asset_value,
        donation,
        liability,
        balance,
        other,
        other_reason,
        status,
        created_date,
        created_by
    </sql>

    <sql id="vals">
        #{liquidationId},
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{icNo},
        #{assetType},
        #{assetValue},
        #{donation},
        #{liability},
        #{balance},
        #{other},
        #{otherReason},
        #{status},
        now(),
        #{createdBy},
        now(),
        #{modifiedBy}
    </sql>

    <sql id="insertManyVals">
        #{asset.liquidationId},
        #{asset.societyId},
        #{asset.societyNo},
        #{asset.branchId},
        #{asset.branchNo},
        #{asset.icNo},
        #{asset.assetType},
        #{asset.assetValue},
        #{asset.donation},
        #{asset.liability},
        #{asset.balance},
        #{asset.other},
        #{asset.otherReason},
        11,
        now(),
        #{asset.createdBy}
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.societyLiquidation.SocietyLiquidationAsset" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="insertMany" parameterType="com.eroses.external.society.model.societyLiquidation.SocietyLiquidationAsset" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="insertCols"/>)
        VALUES
        <foreach collection="list" separator="," item="asset">
            (<include refid="insertManyVals"/>)
        </foreach>
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.societyLiquidation.SocietyLiquidationAsset">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="assetType != null">asset_type = #{assetType},</if>
            <if test="assetValue != null">asset_value = #{assetValue},</if>
            <if test="donation != null">donation = #{donation},</if>
            <if test="liability != null">liability = #{liability},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="other != null">other = #{other},</if>
            <if test="otherReason != null">other_reason = #{otherReason},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date=NOW(),
            <if test="status != null">status = #{status},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="findByLiquidationId" parameterType="java.lang.Long" resultMap="AssetBasicMap">
        SELECT
        id,
        asset_type,
        asset_value,
        donation,
        liability,
        balance,
        other,
        other_reason
        FROM
        <include refid="tb"/>
        WHERE liquidation_id = #{id}
            AND (status != -1 OR status IS NULL);
    </select>
</mapper>
