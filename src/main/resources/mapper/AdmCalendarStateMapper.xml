<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AdmCalendarState">
    <resultMap id="AdmCalendarStateResultMap" type="com.eroses.external.society.model.lookup.AdmCalendarState">
        <id property="id" column="id"/>
        <result column="calendar_id" property="calendarId"/>
        <result column="state_id" property="stateId"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <sql id="tb">
        `adm_calendar_state`
    </sql>

    <sql id="cols">
        `calendar_id`, `state_id`, `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <select id="findByCalendarId" parameterType="java.lang.Long" resultMap="AdmCalendarStateResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `calendar_id` = #{calendarId}
    </select>

    <select id="findStateIdsByCalendarId" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT `state_id`
        FROM <include refid="tb"/>
        WHERE `calendar_id` = #{calendarId}
    </select>

    <insert id="create" parameterType="com.eroses.external.society.model.lookup.AdmCalendarState" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
        (`calendar_id`, `state_id`, `created_by`, `created_date`, `modified_by`, `modified_date`)
        VALUES
        (#{calendarId}, #{stateId}, #{createdBy}, now(), #{modifiedBy}, now())
    </insert>

    <delete id="deleteByCalendarId" parameterType="java.lang.Long">
        DELETE FROM <include refid="tb"/>
        WHERE `calendar_id` = #{calendarId}
    </delete>
</mapper>