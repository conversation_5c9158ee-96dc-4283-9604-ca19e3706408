<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="RoAppealApproval">
    <resultMap id="RoAppealResultMap" type="com.eroses.external.society.model.RoAppealApproval">
        <!-- ID -->
        <id property="id" column="id"/>
        <result property="appealId" column="appeal_id"/>
        <result property="societyNo" column="society_no"/>
        <result property="societyId" column="society_id"/>
        <result property="decision" column="decision"/>
        <result property="decisionDate" column="decision_date"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="letterNo" column="letter_no"/>
        <result property="letter" column="letter"/>
        <result property="approvedBy" column="approved_by"/>
        <result property="recommendPpp" column="recommend_ppp"/>
        <result property="recommendByPpp" column="recommend_by_ppp"/>
        <result property="recommendPppDate" column="recommend_ppp_date"/>
        <result property="notesPpp" column="notes_ppp"/>
        <result property="recommendKpp" column="recommend_kpp"/>
        <result property="recommendByKpp" column="recommend_by_kpp"/>
        <result property="recommendKppDate" column="recommend_kpp_date"/>
        <result property="notesKpp" column="notes_kpp"/>
        <result property="recommendDato" column="recommend_dato"/>
        <result property="recommendByDato" column="recommend_by_dato"/>
        <result property="recommendDatoDate" column="recommend_dato_date"/>
        <result property="notesDato" column="notes_dato"/>
        <result property="recommendFinal" column="recommend_final"/>
        <result property="recommendFinalBy" column="recommend_final_by"/>
        <result property="recommendFinalDate" column="recommend_final_date"/>
        <result property="notesFinal" column="notes_final"/>
        <result property="kdnReview" column="kdn_review"/>
        <result property="reviewDate" column="review_date"/>
        <result property="kdnDecision" column="kdn_decision"/>
        <result property="recommendByKdn" column="recommend_by_kdn"/>
        <result property="kdnSend" column="kdn_send"/>
        <result property="kdnSendDate" column="kdn_send_date"/>
        <result property="kdnRecord" column="kdn_record"/>
        <result property="notesKdn" column="notes_kdn"/>
        <result property="queryReceiver" column="query_receiver"/>
        <result property="notesQuery" column="notes_query"/>
        <result property="queryAnswer" column="query_answer"/>
        <result property="decisionNotes" column="decision_notes"/>

        <!-- Audit Columns -->
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>

    <sql id="tb">
        `ro_appeal_approval`
    </sql>

    <sql id="cols">
        `appeal_id`, `society_id`, `society_no`, `decision`, `decision_notes`, `decision_date`, `reject_reason`,
        `letter_no`, `letter`, `approved_by`, `recommend_ppp`, `recommend_by_ppp`, `recommend_ppp_date`,
        `notes_ppp`, `recommend_kpp`, `recommend_by_kpp`, `recommend_kpp_date`, `notes_kpp`,
        `recommend_dato`, `recommend_by_dato`, `recommend_dato_date`, `notes_dato`, `recommend_final`,
        `recommend_final_by`, `recommend_final_date`, `notes_final`, `kdn_review`, `review_date`,
        `kdn_decision`, `recommend_by_kdn`, `kdn_send`, `kdn_send_date`, `kdn_record`, `notes_kdn`,
        `query_receiver`, `notes_query`, `query_answer`,
        `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="vals">
        #{appealId}, #{societyId}, #{societyNo}, #{decision}, #{decisionNotes}, #{decisionDate}, #{rejectReason},
        #{letterNo}, #{letter}, #{approvedBy}, #{recommendPpp}, #{recommendByPpp}, #{recommendPppDate},
        #{notesPpp}, #{recommendKpp}, #{recommendByKpp}, #{recommendKppDate}, #{notesKpp},
        #{recommendDato}, #{recommendByDato}, #{recommendDatoDate}, #{notesDato}, #{recommendFinal},
        #{recommendFinalBy}, #{recommendFinalDate}, #{notesFinal}, #{kdnReview}, #{reviewDate},
        #{kdnDecision}, #{recommendByKdn}, #{kdnSend}, #{kdnSendDate}, #{kdnRecord}, #{notesKdn},
        #{queryReceiver}, #{notesQuery}, #{queryAnswer},
        #{createdBy}, NOW(), #{modifiedBy}, NOW()
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.RoAppealApproval" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.RoAppealApproval">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="appealId != null">appeal_id = #{appealId},</if>
            <if test="societyId != null">society_id = #{societyId},</if>
            <if test="societyNo != null">society_no = #{societyNo},</if>
            <if test="decision != null">decision = #{decision},</if>
            <if test="decisionNotes != null">decision_notes = #{decisionNotes},</if>
            <if test="decisionDate != null">decision_date = #{decisionDate},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="letterNo != null">letter_no = #{letterNo},</if>
            <if test="letter != null">letter = #{letter},</if>
            <if test="approvedBy != null">approved_by = #{approvedBy},</if>
            <if test="recommendPpp != null">recommend_ppp = #{recommendPpp},</if>
            <if test="recommendByPpp != null">recommend_by_ppp = #{recommendByPpp},</if>
            <if test="recommendPppDate != null">recommend_ppp_date = #{recommendPppDate},</if>
            <if test="notesPpp != null">notes_ppp = #{notesPpp},</if>
            <if test="recommendKpp != null">recommend_kpp = #{recommendKpp},</if>
            <if test="recommendByKpp != null">recommend_by_kpp = #{recommendByKpp},</if>
            <if test="recommendKppDate != null">recommend_kpp_date = #{recommendKppDate},</if>
            <if test="notesKpp != null">notes_kpp = #{notesKpp},</if>
            <if test="recommendDato != null">recommend_dato = #{recommendDato},</if>
            <if test="recommendByDato != null">recommend_by_dato = #{recommendByDato},</if>
            <if test="recommendDatoDate != null">recommend_dato_date = #{recommendDatoDate},</if>
            <if test="notesDato != null">notes_dato = #{notesDato},</if>
            <if test="recommendFinal != null">recommend_final = #{recommendFinal},</if>
            <if test="recommendFinalBy != null">recommend_final_by = #{recommendFinalBy},</if>
            <if test="recommendFinalDate != null">recommend_final_date = #{recommendFinalDate},</if>
            <if test="notesFinal != null">notes_final = #{notesFinal},</if>
            <if test="kdnReview != null">kdn_review = #{kdnReview},</if>
            <if test="reviewDate != null">review_date = #{reviewDate},</if>
            <if test="kdnDecision != null">kdn_decision = #{kdnDecision},</if>
            <if test="recommendByKdn != null">recommend_by_kdn = #{recommendByKdn},</if>
            <if test="kdnSend != null">kdn_send = #{kdnSend},</if>
            <if test="kdnSendDate != null">kdn_send_date = #{kdnSendDate},</if>
            <if test="kdnRecord != null">kdn_record = #{kdnRecord},</if>
            <if test="notesKdn != null">notes_kdn = #{notesKdn},</if>
            <if test="queryReceiver != null">query_receiver = #{queryReceiver},</if>
            <if test="notesQuery != null">notes_query = #{notesQuery},</if>
            <if test="queryAnswer != null">query_answer = #{queryAnswer},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date = NOW()
        </set>
        WHERE `appeal_id` = #{appealId}
    </update>

    <select id="getQueryHistory" parameterType="map" resultMap="RoAppealResultMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `appeal_id` = #{appealId}
        AND
        `decision` = #{decision}
    </select>

    <select id="getRoAppeal" parameterType="com.eroses.external.society.dto.request.GetRoAppealRequest" resultMap="RoAppealResultMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="appealId != null">
            AND `appeal_id` = #{appealId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="decision != null">
            AND `decision` = #{decision}
        </if>
        <if test="queryReceiver != null">
            AND `query_receiver` = #{queryReceiver}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

</mapper>