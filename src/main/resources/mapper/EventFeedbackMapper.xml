<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="EventFeedback">

    <resultMap id="EventFeedbackMap" type="com.eroses.external.society.model.EventFeedback">
        <id property="id" column="id"/>
        <result property="eventFeedbackScaleCode" column="event_feedback_scale_code"/>
        <result property="eventAttendeesId" column="event_attendees_id"/>
        <result property="feedbackQuestionId" column="feedback_question_id"/>
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        event_feedback
    </sql>

    <sql id="cols">
        event_feedback_scale_code,
        event_attendees_id,
        feedback_question_id,
        created_by
    </sql>

    <sql id="getCols">
        id,
        event_feedback_scale_code,
        event_attendees_id,
        feedback_question_id,
        created_date,
        modified_by,
        created_by,
        modified_date
    </sql>

    <sql id="vals">
        #{feedback.eventFeedbackScaleCode},
        #{feedback.eventAttendeesId},
        #{feedback.feedbackQuestionId},
        #{feedback.createdBy}
    </sql>




<!--        <insert id="create" parameterType="java.util.List" keyProperty="id" useGeneratedKeys="true">-->
<!--            INSERT INTO <include refid="tb"/>-->
<!--            (<include refid="cols"/>, created_date)-->
<!--            VALUES-->
<!--            <foreach collection="list" item="item" separator=",">-->
<!--                (<include refid="createVals"/>, NOW())-->
<!--            </foreach>-->
<!--        </insert>-->

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        <foreach item="feedback" collection="list" separator=",">
            (<include refid="vals"/>)
        </foreach>
    </insert>

    <select id="findByAttendeesId" resultMap="EventFeedbackMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE event_attendees_id = #{attendeesId}
    </select>

<!--    <select id="findByTemplateCode"  resultMap="EventCertTemplateConfigurationMap">-->
<!--        SELECT-->
<!--        <include refid="getCols"/>-->
<!--        FROM-->
<!--        <include refid="tb"/>-->
<!--        WHERE-->
<!--        template_code = #{templateCode}-->
<!--    </select>-->




</mapper>
