<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="Posting">
    <sql id="tb">posting</sql>

    <resultMap id="PostingMap" type="com.eroses.external.society.model.posting.Posting">
        <id property="id" column="id" />
        <result property="title" column="title" />
        <result property="description" column="description" />
        <result property="category" column="category" />
        <result property="subCategory" column="sub_category" />
        <result property="author" column="author" />
        <result property="postingDate" column="posting_date" />
        <result property="expirationDate" column="expiration_date" />
        <result property="prePublishDate" column="pre_publish_date" />
        <result property="urgencyCategory" column="urgency_category" />
        <result property="status" column="status" />
        <result property="isReviewed" column="is_reviewed" />
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date" />
        <result property="modifiedBy" column="modified_by" />
        <result property="modifiedDate" column="modified_date" />
    </resultMap>

    <sql id="cols">
        title,
        description,
        category,
        sub_category,
        author,
        posting_date,
        expiration_date,
        pre_publish_date,
        urgency_category,
        status,
        is_reviewed,
        created_by,
        created_date,
        modified_by,
        modified_date
    </sql>

    <sql id="colsWithId">
        id,
        <include refid="cols"/>
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.posting.Posting" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
            title,
            description,
            category,
            sub_category,
            author,
            posting_date,
            expiration_date,
            pre_publish_date,
            urgency_category,
            status,
            is_reviewed,
            created_by,
            created_date
        )
        VALUES (
            #{title},
            #{description},
            #{category},
            #{subCategory},
            #{author},
            #{postingDate},
            #{expirationDate},
            #{prePublishDate},
            #{urgencyCategory},
            #{status},
            #{isReviewed},
            #{createdBy},
            #{createdDate}
        )
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.posting.Posting">
        UPDATE
        <include refid="tb"/>
        SET
            title = #{title},
            description = #{description},
            category = #{category},
            sub_category = #{subCategory},
            author = #{author},
            posting_date = #{postingDate},
            expiration_date = #{expirationDate},
            pre_publish_date = #{prePublishDate},
            urgency_category = #{urgencyCategory},
            status = #{status},
            is_reviewed = #{isReviewed},
            modified_by = #{modifiedBy},
            modified_date = #{modifiedDate}
        WHERE
            id = #{id}
    </update>

    <!-- Update Status -->
    <update id="updateStatus">
        UPDATE
        <include refid="tb"/>
        SET
            status = #{status},
            modified_date = NOW()
        WHERE
            id = #{id}
    </update>

    <!-- Delete -->
    <delete id="delete">
        DELETE FROM
        <include refid="tb"/>
        WHERE
            id = #{id}
    </delete>

    <!-- Find By ID -->
    <select id="findById" resultMap="PostingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            id = #{id}
    </select>

    <!-- Find All -->
    <select id="getAll" resultMap="PostingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
    </select>

    <!-- Search -->
    <select id="search" resultType="com.eroses.external.society.dto.response.posting.PostingPagingResponse">
        SELECT
            id,
            title,
            description,
            category,
            sub_category as subCategory,
            author,
            posting_date as postingDate,
            status,
            urgency_category as urgencyCategory,
            created_date as createdDate
        FROM
        <include refid="tb"/>
        <where>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
            <if test="subCategory != null and subCategory != ''">
                AND sub_category = #{subCategory}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="dateFrom != null and dateFrom != ''">
                AND posting_date >= #{dateFrom}
            </if>
            <if test="dateTo != null and dateTo != ''">
                AND posting_date &lt;= #{dateTo}
            </if>
        </where>
        ORDER BY
            <if test="sortBy != null and sortBy != ''">
                ${sortBy} ${sortDir},
            </if>
            posting_date DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- Count -->
    <select id="findPostingsTotalCount" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
        <include refid="tb"/>
        <where>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
            <if test="subCategory != null and subCategory != ''">
                AND sub_category = #{subCategory}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="dateFrom != null and dateFrom != ''">
                AND posting_date >= #{dateFrom}
            </if>
            <if test="dateTo != null and dateTo != ''">
                AND posting_date &lt;= #{dateTo}
            </if>
        </where>
    </select>

    <!-- Find Random Postings -->
    <select id="findRandomPostings" resultMap="PostingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            status = 'PUBLISHED'
            <if test="excludeIds != null and excludeIds.size() > 0">
                AND id NOT IN
                <foreach item="item" index="index" collection="excludeIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="prioritizeRecent">
                AND posting_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            </if>
        ORDER BY
            <if test="urgentFirst">
                CASE WHEN urgency_category = 'URGENT' THEN 0 ELSE 1 END,
            </if>
            RAND()
        LIMIT #{count}
    </select>

    <!-- Find Recommended Postings -->
    <select id="findRecommendedPostings" resultMap="PostingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            status = 'PUBLISHED'
            AND id != #{postingId}
            <if test="category != null and category != ''">
                AND (category = #{category} OR sub_category = #{subCategory})
            </if>
        ORDER BY
            posting_date DESC
        LIMIT #{count}
    </select>

    <!-- Find By Category -->
    <select id="findByCategory" resultMap="PostingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            category = #{category}
            AND status = 'PUBLISHED'
        ORDER BY
            posting_date DESC
    </select>

    <!-- Find By Sub-Category -->
    <select id="findBySubCategory" resultMap="PostingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            sub_category = #{subCategory}
            AND status = 'PUBLISHED'
        ORDER BY
            posting_date DESC
    </select>

    <!-- Find By Status -->
    <select id="findByStatus" resultMap="PostingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            status = #{status}
        ORDER BY
            posting_date DESC
    </select>

    <!-- Find Expired Postings -->
    <select id="findExpiredPostings" resultMap="PostingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            status = 'PUBLISHED'
            AND expiration_date IS NOT NULL
            AND expiration_date &lt; NOW()
    </select>

    <!-- Find Postings To Publish -->
    <select id="findPostingsToPublish" resultMap="PostingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            status = 'DRAFT'
            AND pre_publish_date IS NOT NULL
            AND pre_publish_date &lt;= NOW()
    </select>
</mapper>
