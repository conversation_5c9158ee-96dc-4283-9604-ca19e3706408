<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="SocietyCancellationRevert">
    <resultMap id="SocietyCancellationRevertMap" type="com.eroses.external.society.model.societyCancellation.SocietyCancellationRevert">
        <id property="id" column="id"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="society_cancellation_id" property="societyCancellationId"/>
        <result column="revert_date" property="revertDate"/>
        <result column="revert_reason" property="revertReason"/>
        <result column="note" property="note"/>
        <result column="document_id" property="documentId"/>
        <result column="is_reverted" property="isReverted"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <!-- Result map for search results including society information -->
    <resultMap id="SocietyCancellationRevertSearchMap" type="map">
        <id property="id" column="id"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="society_cancellation_id" property="societyCancellationId"/>
        <result column="revert_date" property="revertDate"/>
        <result column="revert_reason" property="revertReason"/>
        <result column="note" property="note"/>
        <result column="document_id" property="documentId"/>
        <result column="is_reverted" property="isReverted"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>

        <!-- Society information -->
        <result column="society_name" property="societyName"/>
        <result column="short_name" property="shortName"/>
        <result column="society_level" property="societyLevel"/>
        <result column="constitution_type" property="constitutionType"/>
        <result column="has_branch" property="hasBranch"/>
        <result column="registered_date" property="registeredDate"/>
        <result column="approved_date" property="approvedDate"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="category_code_jppm" property="categoryCodeJppm"/>
        <result column="sub_category_code" property="subCategoryCode"/>
        <result column="status_code" property="statusCode"/>
    </resultMap>

    <sql id="tb">
        society_cancellation_revert
    </sql>

    <sql id="cols">
        `society_id`, `society_no`, `branch_id`, `branch_no`, `society_cancellation_id`, `revert_date`, `revert_reason`, `note`, `document_id`, `is_reverted`,
        `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.societyCancellation.SocietyCancellationRevert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (`society_id`, `society_no`, `branch_id`, `branch_no`, `society_cancellation_id`, `revert_date`, `revert_reason`, `note`, `document_id`, `is_reverted`,
        `created_by`, `created_date`, `modified_by`, `modified_date`)
        VALUES
        (#{societyId}, #{societyNo}, #{branchId}, #{branchNo}, #{societyCancellationId}, #{revertDate}, #{revertReason}, #{note}, #{documentId}, #{isReverted},
        #{createdBy}, now(), #{modifiedBy}, now())
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.societyCancellation.SocietyCancellationRevert">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="societyCancellationId != null">`society_cancellation_id` = #{societyCancellationId},</if>
            <if test="revertDate != null">`revert_date` = #{revertDate},</if>
            <if test="revertReason != null">`revert_reason` = #{revertReason},</if>
            <if test="note != null">`note` = #{note},</if>
            <if test="documentId != null">`document_id` = #{documentId},</if>
            <if test="isReverted != null">`is_reverted` = #{isReverted},</if>
            `modified_by` = #{modifiedBy},
            `modified_date` = now()
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="findById" parameterType="java.lang.Long" resultMap="SocietyCancellationRevertMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
    </select>

    <select id="findAll" resultMap="SocietyCancellationRevertMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
    </select>

    <select id="findBySocietyId" parameterType="java.lang.Long" resultMap="SocietyCancellationRevertMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
    </select>

    <select id="findBySocietyNo" parameterType="java.lang.String" resultMap="SocietyCancellationRevertMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_no` = #{societyNo}
    </select>

    <select id="findBySocietyCancellationId" parameterType="java.lang.Long" resultMap="SocietyCancellationRevertMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_cancellation_id` = #{societyCancellationId}
    </select>

    <!-- Search query that joins with Society table and returns a map -->
    <select id="findByParam" parameterType="map" resultMap="SocietyCancellationRevertSearchMap">
        SELECT
            scr.`id`,
            scr.`society_id`,
            scr.`society_no`,
            scr.`branch_id`,
            scr.`branch_no`,
            scr.`society_cancellation_id`,
            scr.`revert_date`,
            scr.`note`,
            scr.`document_id`,
            scr.`is_reverted`,
            scr.`created_by`,
            scr.`created_date`,
            scr.`modified_by`,
            scr.`modified_date`,
            s.`society_name`,
            s.`short_name`,
            s.`society_level`,
            s.`constitution_type`,
            s.`has_branch`,
            s.`registered_date`,
            s.`approved_date`,
            s.`identification_no`,
            s.`category_code_jppm`,
            s.`sub_category_code`,
            s.`status_code`
        FROM
            <include refid="tb"/> scr
        LEFT JOIN society s ON scr.society_id = s.id
        <where>
            <if test="id != null">
                AND scr.`id` = #{id}
            </if>
            <if test="societyId != null">
                AND scr.`society_id` = #{societyId}
            </if>
            <if test="societyNo != null">
                AND scr.`society_no` = #{societyNo}
            </if>
            <if test="branchId != null">
                AND scr.`branch_id` = #{branchId}
            </if>
            <if test="branchNo != null">
                AND scr.`branch_no` = #{branchNo}
            </if>
            <if test="societyCancellationId != null">
                AND scr.`society_cancellation_id` = #{societyCancellationId}
            </if>
            <if test="revertDateFrom != null">
                AND scr.`revert_date` >= #{revertDateFrom}
            </if>
            <if test="revertDateTo != null">
                AND scr.`revert_date` &lt;= #{revertDateTo}
            </if>
            <if test="documentId != null">
                AND scr.`document_id` = #{documentId}
            </if>
            <if test="societyName != null and societyName != ''">
                AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
            </if>
            <if test="statusCode != null">
                AND s.`status_code` = #{statusCode}
            </if>
        </where>
        ORDER BY scr.`revert_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- Count query that matches the conditions in findByParam -->
    <select id="countByParam" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
            <include refid="tb"/> scr
        LEFT JOIN society s ON scr.society_id = s.id
        <where>
            <if test="id != null">
                AND scr.`id` = #{id}
            </if>
            <if test="societyId != null">
                AND scr.`society_id` = #{societyId}
            </if>
            <if test="societyNo != null">
                AND scr.`society_no` = #{societyNo}
            </if>
            <if test="branchId != null">
                AND scr.`branch_id` = #{branchId}
            </if>
            <if test="branchNo != null">
                AND scr.`branch_no` = #{branchNo}
            </if>
            <if test="societyCancellationId != null">
                AND scr.`society_cancellation_id` = #{societyCancellationId}
            </if>
            <if test="revertDateFrom != null">
                AND scr.`revert_date` >= #{revertDateFrom}
            </if>
            <if test="revertDateTo != null">
                AND scr.`revert_date` &lt;= #{revertDateTo}
            </if>
            <if test="documentId != null">
                AND scr.`document_id` = #{documentId}
            </if>
            <if test="societyName != null and societyName != ''">
                AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
            </if>
            <if test="statusCode != null">
                AND s.`status_code` = #{statusCode}
            </if>
        </where>
    </select>

    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
    </delete>

    <select id="getAllPendingToProcessSocietyCancellationRevertId" parameterType="map" resultType="java.lang.Long">
        SELECT id
        FROM
        <include refid="tb" />
        WHERE `is_reverted` = #{isReverted}
        AND DATE(revert_date) &lt;= CURDATE()
    </select>
</mapper>
