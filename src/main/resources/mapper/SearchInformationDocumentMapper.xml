<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="SearchInformationDocument">

    <resultMap id="SearchInformationDocumentResultMap" type="com.eroses.external.society.model.SearchInformationDocument">
        <id column="id" property="id" />
        <result column="search_information_id" property="searchInformationId" />
        <result column="name" property="name" />
        <result column="template_code" property="templateCode" />
        <result column="s3_url" property="s3Url" />
        <result column="is_generated" property="isGenerated" />
        <result column="first_accessed_at" property="firstAccessedAt" />
        <result column="created_by" property="createdBy" />
        <result column="created_date" property="createdDate" />
        <result column="modified_by" property="modifiedBy" />
        <result column="modified_date" property="modifiedDate" />
        <result column="type" property="type" />
        <result column="download_period" property="downloadPeriod" />
        <result column="amount" property="amount" />
        <result column="status" property="status" />
        <result column="format" property="format" />
        <result column="language" property="language" />
        <result column="application_status_code" property="applicationStatusCode" />
    </resultMap>

    <resultMap id="SearchInformationDocumentSelectResultMap" type="com.eroses.external.society.model.SearchInformationDocument">
        <id column="sid_id" property="id" />
        <result column="sid_search_information_id" property="searchInformationId" />
        <result column="sid_name" property="name" />
        <result column="sid_template_code" property="templateCode" />
        <result column="sid_s3_url" property="s3Url" />
        <result column="sid_is_generated" property="isGenerated" />
        <result column="sid_first_accessed_at" property="firstAccessedAt" />
        <result column="sid_created_by" property="createdBy" />
        <result column="sid_created_date" property="createdDate" />
        <result column="sid_modified_by" property="modifiedBy" />
        <result column="sid_modified_date" property="modifiedDate" />
        <result column="sid_type" property="type" />
        <result column="sid_download_period" property="downloadPeriod" />
        <result column="sid_amount" property="amount" />
        <result column="sid_status" property="status" />
        <result column="sid_format" property="format" />
        <result column="sid_language" property="language" />
        <result column="sid_application_status_code" property="applicationStatusCode" />
        <association property="searchInformation" javaType="com.eroses.external.society.model.SearchInformation"
                     resultMap="SearchInformation.SearchInformationSelectResultMap"/>
    </resultMap>

    <!-- SQL Fragments -->
    <sql id="tb">
        `search_information_document`
    </sql>

    <sql id="selectTb">
        `search_information_document` sid
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <sql id="cols">
        `search_information_id`,
        `name`,
        `template_code`,
        `s3_url`,
        `is_generated`,
        `first_accessed_at`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`,
        `type`,
        `download_period`,
        `amount`,
        `status`,
        `format`,
        `language`,
        `application_status_code`
    </sql>

    <sql id="selectCols">
        sid.`id` AS sid_id,
        sid.`search_information_id` AS sid_search_information_id,
        sid.`name` AS sid_name,
        sid.`template_code` AS sid_template_code,
        sid.`s3_url` AS sid_s3_url,
        sid.`is_generated` AS sid_is_generated,
        sid.`first_accessed_at` AS sid_first_accessed_at,
        sid.`created_by` AS sid_created_by,
        sid.`created_date` AS sid_created_date,
        sid.`modified_by` AS sid_modified_by,
        sid.`modified_date` AS sid_modified_date,
        sid.`type` AS sid_type,
        sid.`download_period` AS sid_download_period,
        sid.`amount` AS sid_amount,
        sid.`status` AS sid_status,
        sid.`format` AS sid_format,
        sid.`language` AS sid_language,
        sid.`application_status_code` AS sid_application_status_code
    </sql>

    <!-- Select all records -->
    <select id="findAll" resultMap="SearchInformationDocumentResultMap">
        SELECT
        <include refid="colsWithId" />
        FROM
        <include refid="tb" />
    </select>

    <!-- Find by ID -->
    <select id="findById" parameterType="long" resultMap="SearchInformationDocumentSelectResultMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="SearchInformation.selectCols"/>
        FROM
        <include refid="selectTb" />
        INNER JOIN <include refid="SearchInformation.selectTb"/> ON sid.search_information_id = si.id
        WHERE sid.`id` = #{id}
    </select>

    <select id="getAllBySearchInformationId" parameterType="long" resultMap="SearchInformationDocumentSelectResultMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="SearchInformation.selectCols"/>
        FROM
        <include refid="selectTb" />
        INNER JOIN <include refid="SearchInformation.selectTb"/> ON sid.search_information_id = si.id
        WHERE `search_information_id` = #{searchInformationId}
    </select>

    <!-- Insert a new record -->
    <insert id="create" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb" />
        (<include refid="cols" />)
        VALUES (
        #{searchInformationId},
        #{name},
        #{templateCode},
        #{s3Url},
        #{isGenerated},
        #{firstAccessedAt},
        #{createdBy},
        #{createdDate},
        #{modifiedBy},
        #{modifiedDate},
        #{type},
        #{downloadPeriod},
        #{amount},
        #{status},
        #{format},
        #{language},
        #{applicationStatusCode}
        )
    </insert>

    <!-- Update an existing record -->
    <update id="update">
        UPDATE <include refid="tb" />
        <set>
            <if test="searchInformationId != null">`search_information_id` = #{searchInformationId},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="templateCode != null">`template_code` = #{templateCode},</if>
            <if test="s3Url != null">`s3_url` = #{s3Url},</if>
            <if test="isGenerated != null">`is_generated` = #{isGenerated},</if>
            <if test="firstAccessedAt != null">`first_accessed_at` = #{firstAccessedAt},</if>
            <if test="createdBy != null">`created_by` = #{createdBy},</if>
            <if test="createdDate != null">`created_date` = #{createdDate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="modifiedDate != null">`modified_date` = #{modifiedDate},</if>
            <if test="type != null">`type` = #{type},</if>
            <if test="downloadPeriod != null">`download_period` = #{downloadPeriod},</if>
            <if test="amount != null">`amount` = #{amount},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="format != null">`format` = #{format},</if>
            <if test="language != null">`language` = #{language},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- Delete a record by ID -->
    <delete id="deleteById">
        DELETE FROM <include refid="tb" />
        WHERE `id` = #{id}
    </delete>

    <select id="getAllExpiredSearchInformationDocumentId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        SELECT sid.id
        FROM
        <include refid="selectTb" />
        INNER JOIN <include refid="SearchInformation.selectTb"/> ON sid.search_information_id = si.id
        WHERE sid.`application_status_code` = #{applicationStatusCode}
        AND NOW() > DATE_ADD(si.payment_date, INTERVAL sid.download_period DAY)
    </select>

    <update id="updateExpiredSearchInformationDocument" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            application_status_code = #{applicationStatusCode}
        </set>
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
