<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.eroses.external.society.mappers.grant.GrantQueryDao">
    
    <resultMap id="grantQueryResultMap" type="com.eroses.external.society.model.grant.GrantQuery">
        <id property="id" column="id"/>
        <result property="grantApplicationId" column="grant_application_id"/>
        <result property="queryText" column="query_text"/>
        <result property="queryBy" column="query_by"/>
        <result property="queryDate" column="query_date"/>
        <result property="responseText" column="response_text"/>
        <result property="responseDate" column="response_date"/>
        <result property="status" column="status"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>
    
    <insert id="create" parameterType="com.eroses.external.society.model.grant.GrantQuery" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO grant_query (
            grant_application_id,
            query_text,
            query_by,
            query_date,
            status,
            created_by,
            created_date,
            modified_by,
            modified_date
        ) VALUES (
            #{grantApplicationId},
            #{queryText},
            #{queryBy},
            #{queryDate},
            #{status},
            #{createdBy},
            #{createdDate},
            #{modifiedBy},
            #{modifiedDate}
        )
    </insert>
    
    <update id="update" parameterType="com.eroses.external.society.model.grant.GrantQuery">
        UPDATE grant_query
        SET 
            response_text = #{responseText},
            response_date = #{responseDate},
            status = #{status},
            modified_by = #{modifiedBy},
            modified_date = #{modifiedDate}
        WHERE id = #{id}
    </update>
    
    <select id="findByGrantApplicationId" parameterType="long" resultMap="grantQueryResultMap">
        SELECT * FROM grant_query
        WHERE grant_application_id = #{grantApplicationId}
        ORDER BY query_date DESC
    </select>
    
    <select id="findById" parameterType="long" resultMap="grantQueryResultMap">
        SELECT * FROM grant_query
        WHERE id = #{id}
    </select>
    
    <select id="findPendingQueries" resultMap="grantQueryResultMap">
        SELECT * FROM grant_query
        WHERE status = 'PENDING'
        ORDER BY query_date ASC
    </select>
    
    <delete id="delete" parameterType="long">
        DELETE FROM grant_query
        WHERE id = #{id}
    </delete>
    
</mapper>
