<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AdmReligion">
    <resultMap id="AdmReligionResultMap" type="com.eroses.external.society.model.lookup.AdmReligion">
        <id property="id" column="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <sql id="tb">
        `adm_religion`
    </sql>

    <sql id="selectTb">
        `adm_religion` ar
    </sql>

    <sql id="selectCols">
        ar.`id` as ar_id,
        ar.`name` as ar_name,
        ar.`code` as ar_code,
        ar.`status` as ar_status,
        ar.`created_by` as ar_created_by,
        ar.`created_date` as ar_created_date,
        ar.`modified_by` as ar_modified_by,
        ar.`modified_date` as ar_modified_date
    </sql>

    <sql id="cols">
        `name`, `code`, `status`, `is_deleted`, `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <select id="findById" parameterType="java.lang.Long" resultMap="AdmReligionResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <select id="getAll" parameterType="map" resultMap="AdmReligionResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `is_deleted` = 0
        <if test="nameQuery != null and nameQuery != ''">
            AND `name` LIKE CONCAT('%', #{nameQuery}, '%')
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countAll" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM <include refid="tb"/>
        WHERE `is_deleted` = 0
        <if test="nameQuery != null and nameQuery != ''">
            AND `name` LIKE CONCAT('%', #{nameQuery}, '%')
        </if>
    </select>

    <select id="getAllActive" resultMap="AdmReligionResultMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `is_deleted` = 0
        AND `status` = 1
    </select>

    <insert id="create" parameterType="com.eroses.external.society.model.lookup.AdmReligion" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{name}, #{code}, #{status}, 0, #{createdBy}, now(), #{modifiedBy}, now())
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.lookup.AdmReligion">
        UPDATE <include refid="tb"/>
        <set>
            <if test="name != null">`name` = #{name},</if>
            <if test="code != null">`code` = #{code},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="isDeleted != null">`is_deleted` = #{isDeleted},</if>
            modified_date = now(),
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <update id="delete" parameterType="java.lang.Long">
        UPDATE <include refid="tb"/>
        <set>
            `is_deleted` = 1
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="existsByCode" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM <include refid="tb"/>
            WHERE `code` = #{code}
            AND `is_deleted` = 0
        )
    </select>

    <select id="existsByCodeExcludingId" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM <include refid="tb"/>
            WHERE `code` = #{code}
            AND `id` != #{id}
            AND `is_deleted` = 0
        )
    </select>
</mapper>
