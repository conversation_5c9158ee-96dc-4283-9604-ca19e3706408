<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="SecretaryCommitteeFeedback">

    <resultMap id="SecretaryCommitteeFeedbackMap" type="com.eroses.external.society.model.SecretaryCommitteeFeedback">
        <id property="id" column="id" />
        <result column="societyCommitteeId" property="societyCommitteeId"/>
        <result column="secretaryId" property="secretaryId"/>
        <result column="societyId" property="societyId"/>
        <result column="societyNo" property="societyNo"/>
        <result column="identificationNo" property="identificationNo"/>
        <result column="feedback" property="feedback"/>
        <result column="otherReason" property="otherReason"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>

    </resultMap>

    <sql id="tb">
        new_secretary_feedback
    </sql>

    <sql id="cols">
        society_committee_id,
        secretary_id,
        society_id,
        society_no,
        identification_no,
        feedback,
        other_reason,
        created_by,
        created_date,
        modified_by,
        modified_date
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="vals">
        #{societyCommitteeId},
        #{secretaryId},
        #{societyId},
        #{societyNo},
        #{identificationNo},
        #{feedback},
        #{otherReason},
        #{createdBy},
        now(),
        #{modifiedBy},
        now()
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.SecretaryCommitteeFeedback" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <!-- Get all -->
    <select id="getAll" resultType="list" resultMap="SecretaryCommitteeFeedbackMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
    </select>

</mapper>
