<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.eroses.external.society.mappers.grant.GrantApplicationFieldValueDao">
    
    <resultMap id="grantApplicationFieldValueResultMap" type="com.eroses.external.society.model.grant.GrantApplicationFieldValue">
        <id property="id" column="id"/>
        <result property="grantApplicationId" column="grant_application_id"/>
        <result property="grantTemplateFieldId" column="grant_template_field_id"/>
        <result property="fieldValue" column="field_value"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>
    
    <insert id="create" parameterType="com.eroses.external.society.model.grant.GrantApplicationFieldValue" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO grant_application_field_value (
            grant_application_id,
            grant_template_field_id,
            field_value,
            created_by,
            created_date,
            modified_by,
            modified_date
        ) VALUES (
            #{grantApplicationId},
            #{grantTemplateFieldId},
            #{fieldValue},
            #{createdBy},
            #{createdDate},
            #{modifiedBy},
            #{modifiedDate}
        )
    </insert>
    
    <update id="update" parameterType="com.eroses.external.society.model.grant.GrantApplicationFieldValue">
        UPDATE grant_application_field_value
        SET 
            field_value = #{fieldValue},
            modified_by = #{modifiedBy},
            modified_date = #{modifiedDate}
        WHERE id = #{id}
    </update>
    
    <select id="findByGrantApplicationId" parameterType="long" resultMap="grantApplicationFieldValueResultMap">
        SELECT * FROM grant_application_field_value
        WHERE grant_application_id = #{grantApplicationId}
    </select>
    
    <select id="findByGrantApplicationIdAndFieldId" parameterType="map" resultMap="grantApplicationFieldValueResultMap">
        SELECT * FROM grant_application_field_value
        WHERE grant_application_id = #{grantApplicationId}
        AND grant_template_field_id = #{grantTemplateFieldId}
    </select>
    
    <delete id="deleteByGrantApplicationId" parameterType="long">
        DELETE FROM grant_application_field_value
        WHERE grant_application_id = #{grantApplicationId}
    </delete>
    
    <delete id="delete" parameterType="long">
        DELETE FROM grant_application_field_value
        WHERE id = #{id}
    </delete>
    
</mapper>
