<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="Feedback">

    <resultMap id="FeedbackMap" type="com.eroses.external.society.model.Feedback">
        <id property="id" column="id" />
        <result column="type" property="type"/>
        <result column="title" property="title"/>
        <result column="satisfaction" property="satisfaction"/>
        <result column="details" property="details"/>
        <result column="location" property="location"/>
        <result column="parliament" property="parliament"/>
        <result column="name" property="name"/>
        <result column="identification_type" property="identificationType"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="gender" property="gender"/>
        <result column="email" property="email"/>
        <result column="contact_no" property="contactNo"/>
        <result column="home_contact_no" property="homeContactNo"/>
        <result column="state_id" property="stateCode"/>
        <result column="district_id" property="districtCode"/>
        <result column="city" property="city"/>
        <result column="postcode" property="postcode"/>
        <result column="status" property="status"/>
        <result column="PIC" property="PIC"/>
        <result column="note" property="note"/>
        <result column="tech_feedback" property="techFeedback"/>
        <result column="created_date" property="createdDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="modified_by" property="modifiedBy"/>
    </resultMap>

    <resultMap id="PagingMap" type="com.eroses.external.society.model.Feedback">
        <id property="id" column="id" />
        <result column="type" property="type"/>
        <result column="title" property="title"/>
        <result column="satisfaction" property="satisfaction"/>
        <result column="location" property="location"/>
        <result column="parliament" property="parliament"/>
        <result column="name" property="name"/>
        <result column="identification_type" property="identificationType"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="gender" property="gender"/>
        <result column="email" property="email"/>
        <result column="contact_no" property="contactNo"/>
        <result column="state_id" property="stateCode"/>
        <result column="district_id" property="districtCode"/>
        <result column="city" property="city"/>
        <result column="postcode" property="postcode"/>
        <result column="status" property="status"/>
        <result column="PIC" property="PIC"/>
        <result column="note" property="note"/>
        <result column="tech_feedback" property="techFeedback"/>
        <result column="created_date" property="createdDate"/>
    </resultMap>

    <sql id="tb">
        feedback
    </sql>

    <sql id="cols">
        type,
        title,
        satisfaction,
        details,
        location,
        parliament,
        name,
        identification_type,
        identification_no,
        gender,
        email,
        contact_no,
        home_contact_no,
        state_id,
        district_id,
        city,
        postcode,
        status,
        PIC,
        note,
        tech_feedback,
        created_date,
        created_by,
        modified_date,
        modified_by
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="vals">
        #{id},
        #{type},
        #{title},
        #{satisfaction},
        #{details},
        #{location},
        #{parliament},
        #{name},
        #{identificationType},
        #{identificationNo},
        #{gender},
        #{email},
        #{contactNo},
        #{homeContactNo},
        #{stateCode},
        #{districtCode},
        #{city},
        #{postcode},
        #{status},
        #{PIC},
        #{note},
        #{techFeedback},
        now(),
        #{createdBy},
        #{modifiedDate},
        #{modifiedBy}
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.Feedback" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="colsWithId"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <!-- Update -->
    <insert id="update" parameterType="com.eroses.external.society.model.Feedback">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="type != null">type = #{type},</if>
            <if test="title != null">title = #{title},</if>
            <if test="satisfaction != null">satisfaction = #{satisfaction},</if>
            <if test="details != null">details = #{details},</if>
            <if test="location != null">location = #{location},</if>
            <if test="parliament != null">parliament = #{parliament},</if>
            <if test="name != null">name = #{name},</if>
            <if test="identificationType != null">identification_type = #{identificationType},</if>
            <if test="identificationNo != null">identification_no = #{identificationNo},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="email != null">email = #{email},</if>
            <if test="contactNo != null">contact_no = #{contactNo},</if>
            <if test="homeContactNo != null">home_contact_no = #{homeContactNo},</if>
            <if test="stateCode != null">state_id = #{stateCode},</if>
            <if test="districtCode != null">district_id = #{districtCode},</if>
            <if test="city != null">city = #{city},</if>
            <if test="postcode != null">postcode = #{postcode},</if>
            <if test="status != null">status = #{status},</if>
            <if test="PIC != null">PIC = #{PIC},</if>
            <if test="note != null">note = #{note},</if>
            <if test="techFeedback != null">tech_feedback = #{techFeedback},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="modifiedDate != null">modified_date = #{modifiedDate},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy}</if>
        </set>
        WHERE
        id = #{id}
    </insert>


    <!-- Paging -->
    <select id="findAllByParams" parameterType="map" resultMap="PagingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="identificationNo != null">
                AND identification_no = #{identificationNo}
            </if>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="phoneNumber != null">
                AND contact_no = #{phoneNumber}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="feedbackExclude != null">
                AND type != #{feedbackExclude}
            </if>

        </where>
        ORDER BY created_date DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- Paging -->
    <select id="findAllExcludeSatisfactory" parameterType="map" resultMap="PagingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="identificationNo != null">
                AND identification_no = #{identificationNo}
            </if>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="phoneNumber != null">
                AND contact_no = #{phoneNumber}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="feedbackExclude != null">
                AND type != #{feedbackExclude}
            </if>
            <if test="feedbackType != null">
                AND type = #{feedbackType}
            </if>
            <if test="stateCode != null">
                AND state_id = #{stateCode}
            </if>
            <if test="fromDate != null">
                AND DATE(created_date) >= #{fromDate}
            </if>
            <if test="toDate != null">
                AND DATE(created_date) &lt;= #{toDate}
            </if>
            <!-- Authorization: PIC bypass OR authorized states -->
            AND (
                PIC = #{userId}
            <if test="authorizedStateCodes != null and authorizedStateCodes.size() > 0">
                OR state_id IN
                <foreach collection="authorizedStateCodes" item="stateCode" open="(" close=")" separator=",">
                    #{stateCode}
                </foreach>
            </if>
            )
        </where>
        ORDER BY created_date DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="countAllExcludeSatisfactory" resultType="Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        <where>
            <if test="identificationNo != null">
                AND identification_no = #{identificationNo}
            </if>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="phoneNumber != null">
                AND contact_no = #{phoneNumber}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="feedbackExclude != null">
                AND type != #{feedbackExclude}
            </if>
            <if test="feedbackType != null">
                AND type = #{feedbackType}
            </if>
            <if test="stateCode != null">
                AND state_id = #{stateCode}
            </if>
            <if test="fromDate != null">
                AND DATE(created_date) >= #{fromDate}
            </if>
            <if test="toDate != null">
                AND DATE(created_date) &lt;= #{toDate}
            </if>
            <!-- Authorization: PIC bypass OR authorized states -->
            AND (
            PIC = #{userId}
            <if test="authorizedStateCodes != null and authorizedStateCodes.size() > 0">
                OR state_id IN
                <foreach collection="authorizedStateCodes" item="stateCode" open="(" close=")" separator=",">
                    #{stateCode}
                </foreach>
            </if>
            )
        </where>
    </select>

    <!-- Paging -->
    <select id="findOnlySatisfactory" parameterType="map" resultMap="PagingMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="year != null and year != 0">
                AND YEAR(`created_date`) = #{year}
            </if>
            <if test="date != null">
                AND DATE(`created_date`) = #{date}
            </if>
            <if test="identificationNo != null">
                AND identification_no = #{identificationNo}
            </if>
            <if test="satisfaction != null">
                AND satisfaction = #{satisfaction}
            </if>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            AND type = #{feedbackType}

        </where>
        ORDER BY created_date DESC
        LIMIT #{offset}, #{pageSize}
    </select>


    <select id="countFindOnlySatisfactory"  resultType="Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        <where>
            <if test="year != null and year != 0">
                AND YEAR(`created_date`) = #{year}
            </if>
            <if test="date != null">
                AND DATE(`created_date`) = #{date}
            </if>
            <if test="satisfaction != null">
                AND satisfaction = #{satisfaction}
            </if>
            <if test="identificationNo != null">
                AND identification_no = #{identificationNo}
            </if>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            AND type = #{feedbackType}

        </where>

    </select>

    <!-- Count total rows where satisfaction is 'MEMUASKAN' -->
    <select id="countMemuaskan" resultType="int">
        SELECT COUNT(*) AS total_memuaskan
        FROM
        <include refid="tb"/>
        <where>
            AND satisfaction = #{satisfaction}
        </where>
    </select>

    <select id="countTidakMemuaskan" resultType="int">
        SELECT COUNT(*) AS total_tidak_memuaskan
        FROM
        <include refid="tb"/>
        <where>
            AND satisfaction = #{satisfaction}
        </where>
    </select>

    <select id="countSangatMemuaskan" resultType="int">
        SELECT COUNT(*) AS total_sangat_memuaskan
        FROM
        <include refid="tb"/>
        <where>
            AND satisfaction = #{satisfaction}
        </where>
    </select>

    <select id="findById" parameterType="java.lang.Long" resultMap="FeedbackMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
    </select>
</mapper>
