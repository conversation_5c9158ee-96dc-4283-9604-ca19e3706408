<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eroses.external.society.mappers.newSocietySecretary.NewSecretaryFeedbackDao">
    <sql id="tb">
        new_secretary_feedback
    </sql>

    <sql id="selectCommitteeId">
        SELECT id FROM society_committee
        WHERE
            society_id = #{societyId}
            AND identification_no = #{identificationNo}
    </sql>

    <sql id="selectSecretaryId">
        SELECT secretary_id FROM society_committee
        WHERE
        society_id = #{societyId}
        AND identification_no = #{identificationNo}
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.newSocietySecretary.NewSecretaryFeedback" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
            society_committee_id,
            secretary_id,
            society_id,
            society_no,
            identification_no,
            feedback,
            other_reason,
            created_by,
            created_date
        )
        VALUES
        (
            (<include refid="selectCommitteeId"/>),
            (<include refid="selectSecretaryId"/>),
            #{societyId},
            #{societyNo},
            #{identificationNo},
            #{feedback},
            #{otherReason},
            #{createdBy},
            NOW()
        )
    </insert>
</mapper>
