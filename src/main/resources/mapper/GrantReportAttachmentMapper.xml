<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.eroses.external.society.mappers.grant.GrantReportAttachmentDao">
    
    <resultMap id="grantReportAttachmentResultMap" type="com.eroses.external.society.model.grant.GrantReportAttachment">
        <id property="id" column="id"/>
        <result property="grantReportId" column="grant_report_id"/>
        <result property="filePath" column="file_path"/>
        <result property="fileType" column="file_type"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
    </resultMap>
    
    <insert id="create" parameterType="com.eroses.external.society.model.grant.GrantReportAttachment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO grant_report_attachment (
            grant_report_id,
            file_path,
            file_type,
            created_by,
            created_date
        ) VALUES (
            #{grantReportId},
            #{filePath},
            #{fileType},
            #{createdBy},
            #{createdDate}
        )
    </insert>
    
    <select id="findByGrantReportId" parameterType="long" resultMap="grantReportAttachmentResultMap">
        SELECT * FROM grant_report_attachment
        WHERE grant_report_id = #{grantReportId}
    </select>
    
    <select id="findById" parameterType="long" resultMap="grantReportAttachmentResultMap">
        SELECT * FROM grant_report_attachment
        WHERE id = #{id}
    </select>
    
    <delete id="deleteByGrantReportId" parameterType="long">
        DELETE FROM grant_report_attachment
        WHERE grant_report_id = #{grantReportId}
    </delete>
    
    <delete id="delete" parameterType="long">
        DELETE FROM grant_report_attachment
        WHERE id = #{id}
    </delete>
    
</mapper>
