<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="QuizQuestion">

    <resultMap id="QuizQuestionMap" type="com.eroses.external.society.model.QuizQuestion">
        <id property="id" column="id" />
        <result property="trainingQuizId" column="training_quiz_id" />
        <result property="questionText" column="question_text" />
        <result property="questionType" column="question_type" />
        <result property="sequenceOrder" column="sequence_order" />
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date" />
    </resultMap>

    <sql id="tb">
        quiz_question
    </sql>

    <sql id="cols">
        training_quiz_id,
        question_text,
        question_type,
        sequence_order,
        created_by,
        created_date
    </sql>

    <sql id="colsWithId">
        id, <include refid="cols" />
    </sql>

    <!-- Create -->
    <insert id="create" parameterType="com.eroses.external.society.model.QuizQuestion" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb" /> (
            training_quiz_id,
            question_text,
            question_type,
            sequence_order,
            created_by,
            created_date
        ) VALUES (
            #{trainingQuizId},
            #{questionText},
            #{questionType},
            #{sequenceOrder},
            #{createdBy},
            NOW()
        )
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.QuizQuestion">
        UPDATE <include refid="tb" />
        SET
            question_text = #{questionText},
            question_type = #{questionType},
            sequence_order = #{sequenceOrder}
        WHERE id = #{id}
    </update>

    <!-- Delete -->
    <delete id="delete">
        DELETE FROM <include refid="tb" />
        WHERE id = #{id}
    </delete>

    <!-- Find By ID -->
    <select id="findById" resultMap="QuizQuestionMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE id = #{id}
    </select>

    <!-- Find All By Quiz ID -->
    <select id="findAllByQuizId" resultMap="QuizQuestionMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE training_quiz_id = #{trainingQuizId}
        ORDER BY sequence_order ASC
    </select>

    <!-- Update Sequence Orders -->
    <update id="updateSequenceOrders">
        <foreach collection="questions" item="question" separator=";">
            UPDATE <include refid="tb" />
            SET sequence_order = #{question.sequenceOrder}
            WHERE id = #{question.id}
        </foreach>
    </update>

</mapper>
