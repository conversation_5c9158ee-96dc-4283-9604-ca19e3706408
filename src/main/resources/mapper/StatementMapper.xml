<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="Statement">

    <!-- Result Map -->
    <resultMap id="StatementMap" type="com.eroses.external.society.model.Statement">
        <id property="id" column="id"/>
        <result property="statementId" column="statement_id"/>
        <result property="societyId" column="society_id"/>
        <result property="societyNo" column="society_no"/>
        <result property="branchId" column="branch_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="statementYear" column="statement_year"/>
        <result property="financialYearStart" column="financial_year_start"/>
        <result property="financialYearEnd" column="financial_year_end"/>
        <result property="tarikhAkuan" column="tarikh_akuan"/>
        <result property="akuanPapar" column="akuan_papar"/>
        <result property="tarikhAkuanPapar" column="tarikh_akuan_papar"/>
        <result property="submissionDate" column="submission_date"/>
        <result property="mainSubmissionDate" column="main_submission_date"/>
        <result property="applicationStatusCode" column="application_status_code"/>
        <result property="ptStat" column="pt_stat"/>
        <result property="akuanSetuju" column="akuan_setuju"/>
        <result property="akuanSetujuInduk" column="akuan_setuju_induk"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
        <result property="pemCaw" column="pem_caw"/>
        <result property="meetingId" column="meeting_id"/>
        <result property="akuanLaporanAktiviti" column="akuan_laporan_aktiviti"/>
        <result property="ajkAppointedDate" column="ajk_appointed_date"/>
        <result property="juruauditAppointedDate" column="juruaudit_appointed_date"/>
    </resultMap>

    <resultMap id="StatementGetOneResponseMap" type="com.eroses.external.society.dto.response.statement.StatementGetOneResponse">
        <id property="id" column="id"/>
        <result property="statementId" column="statement_id"/>
        <result property="societyId" column="society_id"/>
        <result property="societyNo" column="society_no"/>
        <result property="branchId" column="branch_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="statementYear" column="statement_year"/>
        <result property="financialYearStart" column="financial_year_start"/>
        <result property="financialYearEnd" column="financial_year_end"/>
        <result property="tarikhAkuan" column="tarikh_akuan"/>
        <result property="akuanPapar" column="akuan_papar"/>
        <result property="tarikhAkuanPapar" column="tarikh_akuan_papar"/>
        <result property="submissionDate" column="submission_date"/>
        <result property="mainSubmissionDate" column="main_submission_date"/>
        <result property="applicationStatusCode" column="application_status_code"/>
        <result property="ptStat" column="pt_stat"/>
        <result property="akuanSetuju" column="akuan_setuju"/>
        <result property="akuanSetujuInduk" column="akuan_setuju_induk"/>
        <result property="pemCaw" column="pem_caw"/>
        <result property="meetingId" column="meeting_id"/>
        <result property="akuanLaporanAktiviti" column="akuan_laporan_aktiviti"/>
        <result property="ajkAppointedDate" column="ajk_appointed_date"/>
        <result property="juruauditAppointedDate" column="juruaudit_appointed_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>

    <resultMap id="StatementInfoForInternalResponseMap" type="com.eroses.external.society.dto.response.statement.StatementInfoForInternalResponse">
        <id property="id" column="stg_id"/>
        <result property="statementId" column="stg_statement_id"/>
        <result property="societyId" column="s_society_id"/>
        <result property="societyNo" column="s_society_no"/>
        <result property="societyName" column="s_society_name"/>
        <result property="societyStateCode" column="s_society_state_code"/>
        <result property="societyStatusCode" column="s_society_status_code"/>
        <result property="branchId" column="b_branch_id"/>
        <result property="branchNo" column="b_branch_no"/>
        <result property="branchName" column="b_branch_name"/>
        <result property="branchStateCode" column="b_branch_state_code"/>
        <result property="branchStatusCode" column="b_branch_status_code"/>
        <result property="statementYear" column="stg_statement_year"/>
        <result property="statementApplicationStatusCode" column="stg_statement_application_status_code"/>
        <result property="submissionDate" column="stg_submission_date"/>
    </resultMap>

    <sql id="selectSocietyColsForInternal">
        stg.`id` AS stg_id,
        stg.`statement_id` AS stg_statement_id,
        s.`id` AS s_society_id,
        s.`society_no` AS s_society_no,
        s.`society_name` AS s_society_name,
        s.`state_id` AS s_society_state_code,
        s.`status_code` AS s_society_status_code,
        stg.`statement_year` AS stg_statement_year,
        stg.`application_status_code` AS stg_statement_application_status_code,
        stg.`submission_date` AS stg_submission_date
    </sql>

    <sql id="selectBranchColsForInternal">
        stg.`id` AS stg_id,
        stg.`statement_id` AS stg_statement_id,
        b.`id` AS b_branch_id,
        b.`branch_no` AS b_branch_no,
        b.`name` AS b_branch_name,
        b.`state_id` AS b_branch_state_code,
        b.`status` AS b_branch_status_code,
        stg.`statement_year` AS stg_statement_year,
        stg.`application_status_code` AS stg_statement_application_status_code,
        stg.`submission_date` AS stg_submission_date
    </sql>

    <!-- Table Name -->
    <sql id="tb">
        `statement_general`
    </sql>

    <sql id="selectTb">
        `statement_general` stg
    </sql>

    <!-- Columns without ID -->
    <sql id="cols">
        `statement_id`,
        `society_id`,
        `society_no`,
        `branch_id`,
        `branch_no`,
        `statement_year`,
        `financial_year_start`,
        `financial_year_end`,
        `tarikh_akuan`,
        `akuan_papar`,
        `tarikh_akuan_papar`,
        `submission_date`,
        `main_submission_date`,
        `application_status_code`,
        `pt_stat`,
        `akuan_setuju`,
        `akuan_setuju_induk`,
        `ajk_appointed_date`,
        `juruaudit_appointed_date`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`,
        `pem_caw`,
        `meeting_id`,
        `akuan_laporan_aktiviti`
    </sql>

    <sql id="selectCols">
        stg.`statement_id` AS stg_statement_id,
        stg.`society_id` AS stg_society_id,
        stg.`society_no` AS stg_society_no,
        stg.`branch_id` AS stg_branch_id,
        stg.`branch_no` AS stg_branch_no,
        stg.`statement_year` AS stg_statement_year,
        stg.`financial_year_start` AS stg_financial_year_start,
        stg.`financial_year_end` AS stg_financial_year_end,
        stg.`tarikh_akuan` AS stg_tarikh_akuan,
        stg.`akuan_papar` AS stg_akuan_papar,
        stg.`tarikh_akuan_papar` AS stg_tarikh_akuan_papar,
        stg.`submission_date` AS stg_submission_date,
        stg.`main_submission_date` AS stg_main_submission_date,
        stg.`application_status_code` AS stg_application_status_code,
        stg.`pt_stat` AS stg_pt_stat,
        stg.`akuan_setuju` AS stg_akuan_setuju,
        stg.`akuan_setuju_induk` AS stg_akuan_setuju_induk,
        stg.`ajk_appointed_date` AS stg_ajk_appointed_date,
        stg.`juruaudit_appointed_date` AS stg_juruaudit_appointed_date,
        stg.`created_by` AS stg_created_by,
        stg.`created_date` AS stg_created_date,
        stg.`modified_by` AS stg_modified_by,
        stg.`modified_date` AS stg_modified_date,
        stg.`pem_caw` AS stg_pem_caw,
        stg.`meeting_id` AS stg_meeting_id,
        stg.`akuan_laporan_aktiviti` AS stg_akuan_laporan_aktiviti
    </sql>

    <!-- Columns with ID -->
    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <!-- Insert Statement -->
    <insert id="create" parameterType="com.eroses.external.society.model.Statement" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{statementId}, #{societyId}, #{societyNo}, #{branchId}, #{branchNo},
        #{statementYear}, #{financialYearStart}, #{financialYearEnd}, #{tarikhAkuan},
        #{akuanPapar}, #{tarikhAkuanPapar}, #{submissionDate}, #{mainSubmissionDate},
        #{applicationStatusCode}, #{ptStat}, #{akuanSetuju}, #{akuanSetujuInduk}, #{ajkAppointedDate}, #{juruauditAppointedDate},
        #{createdBy}, NOW(), #{modifiedBy}, NOW(), #{pemCaw}, #{meetingId}, #{akuanLaporanAktiviti})
    </insert>

    <!-- Update Statement -->
    <update id="update" parameterType="com.eroses.external.society.model.Statement">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="statementYear != null">`statement_year` = #{statementYear},</if>
            <if test="financialYearStart != null">`financial_year_start` = #{financialYearStart},</if>
            <if test="financialYearEnd != null">`financial_year_end` = #{financialYearEnd},</if>
            <if test="tarikhAkuan != null">`tarikh_akuan` = #{tarikhAkuan},</if>
            <if test="akuanPapar != null">`akuan_papar` = #{akuanPapar},</if>
            <if test="tarikhAkuanPapar != null">`tarikh_akuan_papar` = #{tarikhAkuanPapar},</if>
            <if test="submissionDate != null">`submission_date` = #{submissionDate},</if>
            <if test="mainSubmissionDate != null">`main_submission_date` = #{mainSubmissionDate},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="ptStat != null">`pt_stat` = #{ptStat},</if>
            <if test="akuanSetuju != null">`akuan_setuju` = #{akuanSetuju},</if>
            <if test="akuanSetujuInduk != null">`akuan_setuju_induk` = #{akuanSetujuInduk},</if>
            <if test="ajkAppointedDate != null">`ajk_appointed_date` = #{ajkAppointedDate},</if>
            <if test="juruauditAppointedDate != null">`juruaudit_appointed_date` = #{juruauditAppointedDate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="pemCaw != null">`pem_caw` = #{pemCaw},</if>
            <if test="meetingId != null">`meeting_id` = #{meetingId},</if>
            <if test="akuanLaporanAktiviti != null">`akuan_laporan_aktiviti` = #{akuanLaporanAktiviti},</if>
            `modified_date` = now()
        </set>
        WHERE `statement_id` = #{statementId}
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </update>

    <update id="updateGeneralStatement" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="statementYear != null">`statement_year` = #{statementYear},</if>
            <if test="financialYearStart != null">`financial_year_start` = #{financialYearStart},</if>
            <if test="financialYearEnd != null">`financial_year_end` = #{financialYearEnd},</if>
            <if test="tarikhAkuan != null">`tarikh_akuan` = #{tarikhAkuan},</if>
            <if test="akuanPapar != null">`akuan_papar` = #{akuanPapar},</if>
            <if test="tarikhAkuanPapar != null">`tarikh_akuan_papar` = #{tarikhAkuanPapar},</if>
            <if test="submissionDate != null">`submission_date` = #{submissionDate},</if>
            <if test="mainSubmissionDate != null">`main_submission_date` = #{mainSubmissionDate},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="ptStat != null">`pt_stat` = #{ptStat},</if>
            <if test="akuanSetuju != null">`akuan_setuju` = #{akuanSetuju},</if>
            <if test="akuanSetujuInduk != null">`akuan_setuju_induk` = #{akuanSetujuInduk},</if>
            <if test="ajkAppointedDate != null">`ajk_appointed_date` = #{ajkAppointedDate},</if>
            <if test="juruauditAppointedDate != null">`juruaudit_appointed_date` = #{juruauditAppointedDate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="pemCaw != null">`pem_caw` = #{pemCaw},</if>
            <if test="meetingId != null">`meeting_id` = #{meetingId},</if>
            <if test="akuanLaporanAktiviti != null">`akuan_laporan_aktiviti` = #{akuanLaporanAktiviti},</if>
            `modified_date` = now()
        </set>
        WHERE `statement_id` = #{statementId}
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </update>


    <!-- Select All -->
    <select id="findAll" parameterType="map" resultMap="StatementMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- Select By ID -->
    <select id="findById" parameterType="java.lang.Long" resultMap="StatementMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <!-- Count All -->
    <select id="countFindAll" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
    </select>

    <select id="listStatements" parameterType="map" resultMap="StatementGetOneResponseMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        AND `application_status_code` != -1 <!-- filter out deleted -->
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <choose>
            <when test="branchId != null and branchId != 0">
                AND `branch_id` = #{branchId}
            </when>
            <otherwise>
                AND `branch_id` IS NULL
            </otherwise>
        </choose>
        <if test="searchQuery != null and searchQuery != ''">
            AND `statement_year` LIKE CONCAT('%', #{searchQuery}, '%')
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countListStatements" resultType="long">
        SELECT
        COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        AND `application_status_code` != -1 <!-- filter out deleted -->
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <choose>
            <when test="branchId != null and branchId != 0">
                AND `branch_id` = #{branchId}
            </when>
            <otherwise>
                AND `branch_id` IS NULL
            </otherwise>
        </choose>
        <if test="searchQuery != null and searchQuery != ''">
            AND `statement_year` LIKE CONCAT('%', #{searchQuery}, '%')
        </if>
    </select>

    <!-- Check if statement exists for the given societyId and year -->
    <select id="existsBySocietyIdAndStatementYear" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="statementId != null">
            AND `statement_id` != #{statementId}
        </if>
        <if test="year != null">
            AND `statement_year` = #{year}
        </if>
        AND `application_status_code` != -1
    </select>

    <select id="findByStatementId" parameterType="map" resultMap="StatementMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="statementId != null">
            AND statement_id = #{statementId}
        </if>
        <if test="societyId != null">
            AND society_id = #{societyId}
        </if>
        <if test="branchId != null">
            AND branch_id = #{branchId}
        </if>
        LIMIT 1
    </select>

    <select id="getGeneralStatementInfo" parameterType="map" resultMap="StatementGetOneResponseMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="year != null">
            AND `statement_year` = #{statementYear}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </select>

    <select id="admSearchStatement" resultMap="StatementMap">
        SELECT
        t.id AS id,
        t.statement_id,
        t.society_id,
        t.society_no,
        t.branch_id,
        t.branch_no,
        t.statement_year,
        t.financial_year_start,
        t.financial_year_end,
        t.tarikh_akuan,
        t.akuan_papar,
        t.tarikh_akuan_papar,
        t.submission_date,
        t.main_submission_date,
        t.application_status_code,
        t.pt_stat,
        t.akuan_setuju,
        t.akuan_setuju_induk,
        t.created_by,
        t.created_date,
        t.modified_by,
        t.modified_date,
        t.pem_caw,
        s.society_name FROM
        <include refid="tb"/>
        t
        LEFT JOIN
        society s
        ON
        t.society_no = s.society_no
        WHERE 1=1
        <if test="stateCode != null">
            AND s.state_id = #{stateCode}
        </if>
        <if test="applicationStatusCode != null">
            AND t.application_status_code = #{applicationStatusCode}
        </if>
        <if test="statementYear != null">
            AND t.statement_year = #{statementYear}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            t.society_no LIKE CONCAT('%', #{searchQuery}, '%')
            OR s.society_name LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="searchStatement" resultMap="StatementMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            statement_year LIKE CONCAT('%', #{searchQuery}, '%')
            OR application_status_code LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        LIMIT 20
    </select>

    <select id="countAdmSearchStatement" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        t
        LEFT JOIN
        society s
        ON
        t.society_no = s.society_no
        WHERE 1=1
        <if test="stateCode != null">
            AND s.state_id = #{stateCode}
        </if>
        <if test="applicationStatusCode != null">
            AND t.application_status_code = #{applicationStatusCode}
        </if>
        <if test="statementYear != null">
            AND t.statement_year = #{statementYear}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            t.society_no LIKE CONCAT('%', #{searchQuery}, '%')
            OR s.society_name LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
    </select>

    <update id="deleteStatement" parameterType="map">
        UPDATE <include refid="tb"/>
        <set>
            `application_status_code` = -1
        </set>
        WHERE
        `statement_id` = #{statementId}
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </update>

    <select id="findBySocietyIdOrBranchIdAndStatementYear" parameterType="map" resultMap="StatementMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId} AND `branch_id` IS NULL
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="year != null">
            AND `statement_year` = #{year}
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        LIMIT 1
    </select>

    <select id="findByParam" parameterType="map" resultMap="StatementMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="year != null">
            AND `statement_year` = #{year}
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        LIMIT 1
    </select>

    <select id="getStatementListForInternal" parameterType="map" resultMap="StatementInfoForInternalResponseMap">
        SELECT
        <choose>
            <when test="isBranch != null and isBranch">
                <include refid="selectBranchColsForInternal"/>
            </when>
            <otherwise>
                <include refid="selectSocietyColsForInternal"/>
            </otherwise>
        </choose>
        FROM
        <include refid="selectTb"/>
        <choose>
            <when test="isBranch != null and isBranch">
                LEFT JOIN <include refid="Branch.selectTb"/> ON b.`id` = stg.`branch_id`
            </when>
            <otherwise>
                LEFT JOIN <include refid="Society.selectTb"/> ON s.`id` = stg.`society_id`
            </otherwise>
        </choose>
        WHERE 1=1
        <choose>
            <when test="isBranch != null and isBranch">
                AND stg.`branch_id` IS NOT NULL
            </when>
            <otherwise>
                AND stg.`branch_id` IS NULL
            </otherwise>
        </choose>
        <if test="statementId != null">
            AND stg.`statement_id` = #{statementId}
        </if>
        <if test="societyId != null">
            AND stg.`society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND stg.`branch_id` = #{branchId}
        </if>
        <if test="stateCode != null">
            <choose>
                <when test="isBranch != null and isBranch">
                    AND b.`state_id` = #{stateCode}
                </when>
                <otherwise>
                    AND s.`state_id` = #{stateCode}
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="applicationStatusCode != null and applicationStatusCode != ''">
                AND stg.`application_status_code` = #{applicationStatusCode}
            </when>
            <otherwise>
                AND stg.`application_status_code` != -1
            </otherwise>
        </choose>
        <if test="statementYear != null">
            AND stg.`statement_year` = #{statementYear}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            <choose>
                <when test="isBranch != null and isBranch">
                    AND (
                    b.`name` LIKE CONCAT('%', #{searchQuery}, '%')
                    OR b.`branch_no` LIKE CONCAT('%', #{searchQuery}, '%')
                    )
                </when>
                <otherwise>
                    AND (
                    s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
                    OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
                    )
                </otherwise>
            </choose>
        </if>
        ORDER BY stg.`submission_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countGetStatementListForInternal" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        <choose>
            <when test="isBranch != null and isBranch">
                LEFT JOIN <include refid="Branch.selectTb"/> ON b.`id` = stg.`branch_id`
            </when>
            <otherwise>
                LEFT JOIN <include refid="Society.selectTb"/> ON s.`id` = stg.`society_id`
            </otherwise>
        </choose>
        WHERE 1=1
        <choose>
            <when test="isBranch != null and isBranch">
                AND stg.`branch_id` IS NOT NULL
            </when>
            <otherwise>
                AND stg.`branch_id` IS NULL
            </otherwise>
        </choose>
        <if test="statementId != null">
            AND stg.`statement_id` = #{statementId}
        </if>
        <if test="societyId != null">
            AND stg.`society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND stg.`branch_id` = #{branchId}
        </if>
        <if test="stateCode != null">
            <choose>
                <when test="isBranch != null and isBranch">
                    AND b.`state_id` = #{stateCode}
                </when>
                <otherwise>
                    AND s.`state_id` = #{stateCode}
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="applicationStatusCode != null and applicationStatusCode != ''">
                AND stg.`application_status_code` = #{applicationStatusCode}
            </when>
            <otherwise>
                AND stg.`application_status_code` != -1
            </otherwise>
        </choose>
        <if test="statementYear != null">
            AND stg.`statement_year` = #{statementYear}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            <choose>
                <when test="isBranch != null and isBranch">
                    AND (
                    b.`name` LIKE CONCAT('%', #{searchQuery}, '%')
                    OR b.`branch_no` LIKE CONCAT('%', #{searchQuery}, '%')
                    )
                </when>
                <otherwise>
                    AND (
                    s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
                    OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
                    )
                </otherwise>
            </choose>
        </if>
    </select>
</mapper>
