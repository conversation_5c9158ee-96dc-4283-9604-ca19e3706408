<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Notification">
    <resultMap id="NotificationMap" type="com.eroses.external.society.model.Notification">
        <id property="id" column="id"/>
        <result column="template_code" property="templateCode"/>
        <result column="template_language" property="templateLanguage"/>
        <result column="description" property="description"/>
        <result column="subject" property="subject"/>
        <result column="content" property="content"/>
        <result column="created_date" property="createdDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="status" property="status"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="modified_by" property="modifiedBy"/>
    </resultMap>

    <sql id="tb">
        `notification`
    </sql>

    <sql id="cols">
        `template_code`, `template_language`, `description`, `subject`, `content`, `created_date`, `created_by`, `status`,
        `modified_date`, `modified_by`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <select id="findByTemplateCode" resultMap="NotificationMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE `template_code` = #{templateCode}
        LIMIT 1
    </select>

    <insert id="create" parameterType="com.eroses.external.society.model.Notification" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{templateCode}, #{templateLanguage}, #{description}, #{subject}, #{content}, now(), #{createdBy}, #{status}, now(), #{modifiedBy})
    </insert>

    <select id="findById" parameterType="java.lang.Long" resultMap="NotificationMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <update id="edit" parameterType="com.eroses.external.society.model.Notification">
        UPDATE <include refid="tb"/>
        <set>
            <if test="templateCode != null">`template_code` = #{templateCode},</if>
            <if test="templateLanguage != null">`template_language` = #{templateLanguage},</if>
            <if test="description != null">`description` = #{description},</if>
            <if test="subject != null">`subject` = #{subject},</if>
            <if test="content != null">`content` = #{content},</if>
            <if test="status != null">`status` = #{status},</if>
            modified_date = now(),
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="getAll" resultMap="NotificationMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
    </select>
</mapper>