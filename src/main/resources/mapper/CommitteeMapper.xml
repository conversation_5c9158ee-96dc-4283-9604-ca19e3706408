<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Committee">
    <resultMap id="CommitteeMap" type="com.eroses.external.society.model.Committee">
        <!-- ID -->
        <id column="id" property="id"/>
        <result column="committee_table_old_id" property="committeeTableOldId"/>

        <!-- Columns -->
        <result column="job_code" property="jobCode"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="title_code" property="titleCode"/>
        <result column="name" property="name"/>
        <result column="gender" property="gender"/>
        <result column="nationality_status" property="nationalityStatus"/>
        <result column="identification_type" property="identificationType"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="date_of_birth" property="dateOfBirth"/>
        <result column="place_of_birth" property="placeOfBirth"/>
        <result column="designation_code" property="designationCode"/>
        <result column="other_designation_code" property="otherDesignationCode"/>
        <result column="position_arrangement" property="positionArrangement"/>
        <result column="employer_address_status" property="employerAddressStatus"/>
        <result column="employer_name" property="employerName"/>
        <result column="employer_address" property="employerAddress"/>
        <result column="employer_postcode" property="employerPostcode"/>
        <result column="employer_country_code" property="employerCountryCode"/>
        <result column="employer_state_code" property="employerStateCode"/>
        <result column="employer_city" property="employerCity"/>
        <result column="employer_district" property="employerDistrict"/>
        <result column="residential_address" property="residentialAddress"/>
        <result column="residential_postcode" property="residentialPostcode"/>
        <result column="residential_address_status" property="residentialAddressStatus"/>
        <result column="residential_country_code" property="residentialCountryCode"/>
        <result column="residential_state_code" property="residentialStateCode"/>
        <result column="residential_district_code" property="residentialDistrictCode"/>
        <result column="residential_city" property="residentialCity"/>
        <result column="email" property="email"/>
        <result column="telephone_number" property="telephoneNumber"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="no_tel_p" property="noTelP"/>
        <result column="membership_no" property="membershipNo"/>
        <result column="status" property="status"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="peg_harta" property="pegHarta"/>
        <result column="tarikh_tukar_su" property="tarikhTukarSu"/>
        <result column="other_position" property="otherPosition"/>
        <result column="batal_flat" property="batalFlat"/>
        <result column="blacklist_notice" property="blacklistNotice"/>
        <result column="benar_ajk" property="benarAjk"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="document_id" property="documentId"/>
        <result column="document_type" property="documentType"/>
        <result column="appointed_date" property="appointedDate"/>
        <result column="membership_registration_date" property="membershipRegistrationDate"/>
        <!-- Audit Columns -->
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <resultMap id="CommitteeSelectMap" type="com.eroses.external.society.model.Committee">
        <!-- ID -->
        <id column="sc_id" property="id"/>

        <!-- Columns -->
        <result column="sc_job_code" property="jobCode"/>
        <result column="sc_society_id" property="societyId"/>
        <result column="sc_society_no" property="societyNo"/>
        <result column="sc_title_code" property="titleCode"/>
        <result column="sc_name" property="name"/>
        <result column="sc_gender" property="gender"/>
        <result column="sc_nationality_status" property="nationalityStatus"/>
        <result column="sc_identification_type" property="identificationType"/>
        <result column="sc_identification_no" property="identificationNo"/>
        <result column="sc_date_of_birth" property="dateOfBirth"/>
        <result column="sc_place_of_birth" property="placeOfBirth"/>
        <result column="sc_designation_code" property="designationCode"/>
        <result column="sc_other_designation_code" property="otherDesignationCode"/>
        <result column="sc_position_arrangement" property="positionArrangement"/>
        <result column="sc_employer_address_status" property="employerAddressStatus"/>
        <result column="sc_employer_name" property="employerName"/>
        <result column="sc_employer_address" property="employerAddress"/>
        <result column="sc_employer_postcode" property="employerPostcode"/>
        <result column="sc_employer_country_code" property="employerCountryCode"/>
        <result column="sc_employer_state_code" property="employerStateCode"/>
        <result column="sc_employer_city" property="employerCity"/>
        <result column="sc_employer_district" property="employerDistrict"/>
        <result column="sc_residential_address" property="residentialAddress"/>
        <result column="sc_residential_postcode" property="residentialPostcode"/>
        <result column="sc_residential_address_status" property="residentialAddressStatus"/>
        <result column="sc_residential_country_code" property="residentialCountryCode"/>
        <result column="sc_residential_state_code" property="residentialStateCode"/>
        <result column="sc_residential_district_code" property="residentialDistrictCode"/>
        <result column="sc_residential_city" property="residentialCity"/>
        <result column="sc_email" property="email"/>
        <result column="sc_telephone_number" property="telephoneNumber"/>
        <result column="sc_phone_number" property="phoneNumber"/>
        <result column="sc_no_tel_p" property="noTelP"/>
        <result column="sc_membership_no" property="membershipNo"/>
        <result column="sc_status" property="status"/>
        <result column="sc_application_status_code" property="applicationStatusCode"/>
        <result column="sc_peg_harta" property="pegHarta"/>
        <result column="sc_tarikh_tukar_su" property="tarikhTukarSu"/>
        <result column="sc_other_position" property="otherPosition"/>
        <result column="sc_batal_flat" property="batalFlat"/>
        <result column="sc_blacklist_notice" property="blacklistNotice"/>
        <result column="sc_benar_ajk" property="benarAjk"/>
        <result column="sc_meeting_id" property="meetingId"/>
        <result column="sc_document_id" property="documentId"/>
        <result column="sc_document_type" property="documentType"/>
        <result column="sc_appointed_date" property="appointedDate"/>
        <result column="sc_membership_registration_date" property="membershipRegistrationDate"/>
        <!-- Audit Columns -->
        <result column="sc_created_by" property="createdBy"/>
        <result column="sc_created_date" property="createdDate"/>
        <result column="sc_modified_by" property="modifiedBy"/>
        <result column="sc_modified_date" property="modifiedDate"/>
    </resultMap>

    <sql id="tb">
        `society_committee`
    </sql>

    <sql id="selectTb">
        `society_committee` sc
    </sql>

    <sql id="cols">
        `committee_table_old_id`, `job_code`, `society_id`, `society_no`, `title_code`,
        `name`, `gender`, `nationality_status`, `identification_type`,
        `identification_no`, `date_of_birth`, `place_of_birth`, `designation_code`,
        `other_designation_code`, `position_arrangement`, `employer_address_status`, `employer_name`, `employer_address`,
        `employer_postcode`, `employer_country_code`, `employer_state_code`, `employer_city`,
        `employer_district`, `residential_address`, `residential_postcode`, `residential_address_status`,
        `residential_country_code`, `residential_state_code`, `residential_district_code`, `residential_city`,
        `email`, `telephone_number`, `phone_number`, `no_tel_p`, `membership_no`,
        `status`, `application_status_code`, `peg_harta`, `tarikh_tukar_su`,
        `other_position`, `batal_flat`, `blacklist_notice`, `benar_ajk`,`meeting_id`, `document_id`, `document_type`,
        `appointed_date`, `membership_registration_date`, `created_by`, `created_date`, `modified_by`, `modified_date`
    </sql>

    <sql id="selectCols">
        sc.`id` as sc_id,
        sc.`committee_table_old_id` as sc_committee_table_old_id,
        sc.`job_code` as sc_job_code,
        sc.`society_id` as sc_society_id,
        sc.`society_no` as sc_society_no,
        sc.`title_code` as sc_title_code,
        sc.`name` as sc_name,
        sc.`gender` as sc_gender,
        sc.`nationality_status` as sc_nationality_status,
        sc.`identification_type` as sc_identification_type,
        sc.`identification_no` as sc_identification_no,
        sc.`date_of_birth` as sc_date_of_birth,
        sc.`place_of_birth` as sc_place_of_birth,
        sc.`designation_code` as sc_designation_code,
        sc.`other_designation_code` as sc_other_designation_code,
        sc.`position_arrangement` as sc_position_arrangement,
        sc.`employer_address_status` as sc_employer_address_status,
        sc.`employer_name` as sc_employer_name,
        sc.`employer_address` as sc_employer_address,
        sc.`employer_postcode` as sc_employer_postcode,
        sc.`employer_country_code` as sc_employer_country_code,
        sc.`employer_state_code` as sc_employer_state_code,
        sc.`employer_city` as sc_employer_city,
        sc.`employer_district` as sc_employer_district,
        sc.`residential_address` as sc_residential_address,
        sc.`residential_postcode` as sc_residential_postcode,
        sc.`residential_address_status` as sc_residential_address_status,
        sc.`residential_country_code` as sc_residential_country_code,
        sc.`residential_state_code` as sc_residential_state_code,
        sc.`residential_district_code` as sc_residential_district_code,
        sc.`residential_city` as sc_residential_city,
        sc.`email` as sc_email,
        sc.`telephone_number` as sc_telephone_number,
        sc.`phone_number` as sc_phone_number,
        sc.`no_tel_p` as sc_no_tel_p,
        sc.`membership_no` as sc_membership_no,
        sc.`status` as sc_status,
        sc.`application_status_code` as sc_application_status_code,
        sc.`peg_harta` as sc_peg_harta,
        sc.`tarikh_tukar_su` as sc_tarikh_tukar_su,
        sc.`other_position` as sc_other_position,
        sc.`batal_flat` as sc_batal_flat,
        sc.`blacklist_notice` as sc_blacklist_notice,
        sc.`benar_ajk` as sc_benar_ajk,
        sc.`meeting_id` as sc_meeting_id,
        sc.`document_id` as sc_document_id,
        sc.`document_type` as sc_document_type,
        sc.`appointed_date` as sc_appointed_date,
        sc.`membership_registration_date` as sc_membership_registration_date,
        sc.`created_by` as sc_created_by,
        sc.`created_date` as sc_created_date,
        sc.`modified_by` as sc_modified_by,
        sc.`modified_date` as sc_modified_date
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.Committee" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{committeeTableOldId}, #{jobCode}, #{societyId}, #{societyNo}, #{titleCode},
        #{name}, #{gender}, #{nationalityStatus}, #{identificationType},
        #{identificationNo}, #{dateOfBirth}, #{placeOfBirth}, #{designationCode},
        #{otherDesignationCode}, #{positionArrangement}, #{employerAddressStatus}, #{employerName}, #{employerAddress},
        #{employerPostcode}, #{employerCountryCode}, #{employerStateCode}, #{employerCity},
        #{employerDistrict}, #{residentialAddress}, #{residentialPostcode}, #{residentialAddressStatus},
        #{residentialCountryCode}, #{residentialStateCode}, #{residentialDistrictCode}, #{residentialCity},
        #{email}, #{telephoneNumber}, #{phoneNumber}, #{noTelP}, #{membershipNo},
        #{status}, #{applicationStatusCode}, #{pegHarta}, #{tarikhTukarSu},
        #{otherPosition}, #{batalFlat}, #{blacklistNotice}, #{benarAjk}, #{meetingId}, #{documentId}, #{documentType},
        #{appointedDate}, #{membershipRegistrationDate}, #{createdBy}, NOW(), #{modifiedBy}, NOW())
    </insert>

    <select id="findById" parameterType="java.lang.Long" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.Committee">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="committeeTableOldId != null">`committee_table_old_id` = #{committeeTableOldId},</if>
            <if test="jobCode != null">`job_code` = #{jobCode},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="titleCode != null">`title_code` = #{titleCode},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="gender != null">`gender` = #{gender},</if>
            <if test="nationalityStatus != null">`nationality_status` = #{nationalityStatus},</if>
            <if test="identificationType != null">`identification_type` = #{identificationType},</if>
            <if test="identificationNo != null">`identification_no` = #{identificationNo},</if>
            <if test="dateOfBirth != null">`date_of_birth` = #{dateOfBirth},</if>
            <if test="placeOfBirth != null">`place_of_birth` = #{placeOfBirth},</if>
            <if test="designationCode != null">`designation_code` = #{designationCode},</if>
            <if test="otherDesignationCode != null">`other_designation_code` = #{otherDesignationCode},</if>
            <if test="positionArrangement != null">`position_arrangement` = #{positionArrangement},</if>
            <if test="employerAddressStatus != null">`employer_address_status` = #{employerAddressStatus},</if>
            <if test="employerName != null">`employer_name` = #{employerName},</if>
            <if test="employerAddress != null">`employer_address` = #{employerAddress},</if>
            <if test="employerPostcode != null">`employer_postcode` = #{employerPostcode},</if>
            <if test="employerCountryCode != null">`employer_country_code` = #{employerCountryCode},</if>
            <if test="employerStateCode != null">`employer_state_code` = #{employerStateCode},</if>
            <if test="employerCity != null">`employer_city` = #{employerCity},</if>
            <if test="employerDistrict != null">`employer_district` = #{employerDistrict},</if>
            <if test="residentialAddress != null">`residential_address` = #{residentialAddress},</if>
            <if test="residentialPostcode != null">`residential_postcode` = #{residentialPostcode},</if>
            <if test="residentialAddressStatus != null">`residential_address_status` = #{residentialAddressStatus},</if>
            <if test="residentialCountryCode != null">`residential_country_code` = #{residentialCountryCode},</if>
            <if test="residentialStateCode != null">`residential_state_code` = #{residentialStateCode},</if>
            <if test="residentialDistrictCode != null">`residential_district_code` = #{residentialDistrictCode},</if>
            <if test="residentialCity != null">`residential_city` = #{residentialCity},</if>
            <if test="email != null">`email` = #{email},</if>
            <if test="telephoneNumber != null">`telephone_number` = #{telephoneNumber},</if>
            <if test="phoneNumber != null">`phone_number` = #{phoneNumber},</if>
            <if test="noTelP != null">`no_tel_p` = #{noTelP},</if>
            <if test="membershipNo != null">`membership_no` = #{membershipNo},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="pegHarta != null">`peg_harta` = #{pegHarta},</if>
            <if test="tarikhTukarSu != null">`tarikh_tukar_su` = #{tarikhTukarSu},</if>
            <if test="otherPosition != null">`other_position` = #{otherPosition},</if>
            <if test="batalFlat != null">`batal_flat` = #{batalFlat},</if>
            <if test="blacklistNotice != null">`blacklist_notice` = #{blacklistNotice},</if>
            <if test="benarAjk != null">`benar_ajk` = #{benarAjk},</if>
            <if test="meetingId != null">`meeting_id` = #{meetingId},</if>
            <if test="documentId != null">`document_id` = #{documentId},</if>
            <if test="documentType != null">`document_type` = #{documentType},</if>
            <if test="appointedDate != null">`appointed_date` = #{appointedDate},</if>
            <if test="membershipRegistrationDate != null">`membership_registration_date` = #{membershipRegistrationDate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            modified_date=NOW()
        </set>
        WHERE `id` = #{id}
    </update>

    <update id="updateByOldIdAndAppointedDate" parameterType="com.eroses.external.society.model.Committee">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="committeeTableOldId != null">`committee_table_old_id` = #{committeeTableOldId},</if>
            <if test="jobCode != null">`job_code` = #{jobCode},</if>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="titleCode != null">`title_code` = #{titleCode},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="gender != null">`gender` = #{gender},</if>
            <if test="nationalityStatus != null">`nationality_status` = #{nationalityStatus},</if>
            <if test="identificationType != null">`identification_type` = #{identificationType},</if>
            <if test="identificationNo != null">`identification_no` = #{identificationNo},</if>
            <if test="dateOfBirth != null">`date_of_birth` = #{dateOfBirth},</if>
            <if test="placeOfBirth != null">`place_of_birth` = #{placeOfBirth},</if>
            <if test="designationCode != null">`designation_code` = #{designationCode},</if>
            <if test="otherDesignationCode != null">`other_designation_code` = #{otherDesignationCode},</if>
            <if test="positionArrangement != null">`position_arrangement` = #{positionArrangement},</if>
            <if test="employerAddressStatus != null">`employer_address_status` = #{employerAddressStatus},</if>
            <if test="employerName != null">`employer_name` = #{employerName},</if>
            <if test="employerAddress != null">`employer_address` = #{employerAddress},</if>
            <if test="employerPostcode != null">`employer_postcode` = #{employerPostcode},</if>
            <if test="employerCountryCode != null">`employer_country_code` = #{employerCountryCode},</if>
            <if test="employerStateCode != null">`employer_state_code` = #{employerStateCode},</if>
            <if test="employerCity != null">`employer_city` = #{employerCity},</if>
            <if test="employerDistrict != null">`employer_district` = #{employerDistrict},</if>
            <if test="residentialAddress != null">`residential_address` = #{residentialAddress},</if>
            <if test="residentialPostcode != null">`residential_postcode` = #{residentialPostcode},</if>
            <if test="residentialAddressStatus != null">`residential_address_status` = #{residentialAddressStatus},</if>
            <if test="residentialCountryCode != null">`residential_country_code` = #{residentialCountryCode},</if>
            <if test="residentialStateCode != null">`residential_state_code` = #{residentialStateCode},</if>
            <if test="residentialDistrictCode != null">`residential_district_code` = #{residentialDistrictCode},</if>
            <if test="residentialCity != null">`residential_city` = #{residentialCity},</if>
            <if test="email != null">`email` = #{email},</if>
            <if test="telephoneNumber != null">`telephone_number` = #{telephoneNumber},</if>
            <if test="phoneNumber != null">`phone_number` = #{phoneNumber},</if>
            <if test="noTelP != null">`no_tel_p` = #{noTelP},</if>
            <if test="membershipNo != null">`membership_no` = #{membershipNo},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="pegHarta != null">`peg_harta` = #{pegHarta},</if>
            <if test="tarikhTukarSu != null">`tarikh_tukar_su` = #{tarikhTukarSu},</if>
            <if test="otherPosition != null">`other_position` = #{otherPosition},</if>
            <if test="batalFlat != null">`batal_flat` = #{batalFlat},</if>
            <if test="blacklistNotice != null">`blacklist_notice` = #{blacklistNotice},</if>
            <if test="benarAjk != null">`benar_ajk` = #{benarAjk},</if>
            <if test="meetingId != null">`meeting_id` = #{meetingId},</if>
            <if test="documentId != null">`document_id` = #{documentId},</if>
            <if test="documentType != null">`document_type` = #{documentType},</if>
            <if test="appointedDate != null">`appointed_date` = #{appointedDate},</if>
            <if test="membershipRegistrationDate != null">`membership_registration_date` = #{membershipRegistrationDate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            modified_date=NOW()
        </set>
        WHERE
        `committee_table_old_id` = #{committeeTableOldId}
        AND `appointed_date` = #{appointedDate}
    </update>

    <select id="findAll" parameterType="map" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId !=''">
            AND `society_id` = #{societyId}
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findOneByParams" parameterType="map" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId !=''">
            AND `society_id` = #{societyId}
        </if>
        <if test="position != null and position !=''">
            AND `designation_code` = #{position}
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        <if test="identificationNo != null and identificationNo !=''">
            AND `identification_no` = #{identificationNo}
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
        LIMIT 1
    </select>

    <select id="getAllCommitteesByCriteriaAndSort" parameterType="map" resultMap="CommitteeSelectMap">
        SELECT
        <include refid="selectCols"/>
        FROM
        <include refid="selectTb"/>
        LEFT JOIN <include refid="AdmPositionCommittee.selectTb"/> ON sc.designation_code = apc.code
        LEFT JOIN <include refid="AdmAddresses.selectTb"/> ON sc.residential_state_code = aa.id
        WHERE 1=1
        <if test="societyId != null and societyId !=''">
            AND sc.`society_id` = #{societyId}
        </if>
        <if test="sortBy == 'position' and sortDir !=''">
            ORDER BY apc.`name` ${sortDir}
        </if>
        <if test="sortBy == 'name' and sortDir !=''">
            ORDER BY sc.`name` ${sortDir}
        </if>
        <if test="sortBy == 'email' and sortDir !=''">
            ORDER BY sc.`email` ${sortDir}
        </if>
        <if test="sortBy == 'state' and sortDir !=''">
            ORDER BY aa.`name` ${sortDir}
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findBySocietyId" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
    </select>

    <!-- Improved version of the query above, findBySocietyId -->
    <select id="findByParams" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        AND `society_id` = #{societyId}
        <if test="roleIds != null">
            AND `designation_code` IN (
            <foreach item="value" index="key" collection="roleIds" separator=",">
                #{value}
            </foreach>
            )
        </if>
        <if test="meetingId != null">
            AND `meeting_id` = #{meetingId}
        </if>
        <if test="status != null and status != ''">
            AND `status`= #{status}
        </if>
        <if test="committeeName != null and committeeName != ''">
            AND `name` LIKE CONCAT('%', #{committeeName}, '%')
        </if>
        <if test="identificationNo != null and identificationNo != ''">
            AND `identification_no` LIKE CONCAT('%', #{identificationNo}, '%')
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        <if test="ids != null">
            AND `id` IN
            <foreach collection="ids" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="removeSecretary" parameterType="map">
        UPDATE <include refid="tb"/>
        <set>
            status = "008",
            application_status_code = 43,
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            modified_date=NOW()
        </set>
        <where>
            identification_no = #{identificationNo}
            AND society_id = #{societyId}
        </where>
    </update>


    <select id="findSocietyIdListByIdentificationNo" resultType="java.lang.Long">
        SELECT society_id
        FROM
        <include refid="tb"/>
        WHERE identification_no = #{identificationNo}
    </select>

    <select id="findUniqueSocietyIdListByIdentificationNo" resultType="java.lang.Long">
        SELECT DISTINCT society_id
        FROM
        <include refid="tb"/>
        WHERE identification_no = #{identificationNo}
    </select>

    <select id="countFindAll" resultType="java.lang.Long">
        SELECT COUNT(*)
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId !=''">
            AND `society_id` = #{societyId}
        </if>
    </select>

    <select id="exist" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM <include refid="tb"/>
            <where>
                identification_no = #{identificationNo}
                AND name = #{name}
                AND society_id = #{societyId}
                <if test="designationCode != null">
                    AND designation_code = #{designationCode}
                </if>
                <if test="applicationStatusCode != null">
                    AND application_status_code = #{applicationStatusCode}
                </if>
                <if test="status != null and status != ''">
                    AND status = #{status}
                </if>
            </where>
        )
    </select>

    <select id="existsByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus" resultType="boolean">
        SELECT EXISTS (
        SELECT 1
        FROM
        <include refid="tb"/>
        WHERE identification_no = #{identificationNo}
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        )
    </select>

    <select id="existsByIdentityNoAndSocietyIdAndNotEqualApplicationStatusCodesAndStatus" resultType="boolean">
        SELECT EXISTS (
        SELECT 1
        FROM
        <include refid="tb"/>
        WHERE identification_no = #{identificationNo}
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="applicationStatusCodes != null">
            AND application_status_code NOT IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="status != null and status != ''">
            AND status != #{status}
        </if>
        )
    </select>

    <select id="findBySocietyIdAndApplicationStatusCode" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        AND application_status_code = #{applicationStatusCode}
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countBySocietyIdAndApplicationStatusCode" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        AND application_status_code = #{applicationStatusCode}
    </select>

    <select id="findBySocietyIdAndApplicationStatusCodeAndStatus" parameterType="map" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="designationCodes != null">
            AND `designation_code` IN
            <foreach collection="designationCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="offset != null and limit != null">
        LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countBySocietyIdAndApplicationStatusCodeAndStatus" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="designationCodes != null">
            AND `designation_code` IN
            <foreach collection="designationCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findBySocietyIdAndIdentificationNoAndApplicationStatusCodeAndStatus" parameterType="map"
            resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="identificationNo != null and identificationNo !=''">
            AND identification_no = #{identificationNo}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        LIMIT 1
    </select>

    <select id="findBySocietyIdAndIdentificationNoAndNotEqualApplicationStatusCodeAndStatus" parameterType="map"
            resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="identificationNo != null and identificationNo !=''">
            AND identification_no = #{identificationNo}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code != #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status != #{status}
        </if>
        LIMIT 1
    </select>

    <select id="findBySocietyIdAndIdentificationNo" parameterType="map" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="identificationNo != null and identificationNo !=''">
            AND identification_no = #{identificationNo}
        </if>
        LIMIT 1
    </select>

    <select id="findNotDeletedBySocietyIdAndIdentificationNo" parameterType="map" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="identificationNo != null and identificationNo !=''">
            AND identification_no = #{identificationNo}
        </if>
        AND application_status_code != -1
        LIMIT 1
    </select>

    <select id="findAllBySocietyIdAndIdentificationNo" parameterType="map" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="identificationNo != null and identificationNo !=''">
            AND identification_no = #{identificationNo}
        </if>
    </select>

    <select id="findAllBySocietyIdAndApplicationStatusCodeAndStatus" parameterType="map" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>

    <select id="findBySocietyIdAndPositionAndApplicationStatusCodeAndStatusPaging" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId !=''">
            AND society_id = #{societyId}
        </if>
        <if test="designation_code != null and designation_code !=''">
            AND designation_code = #{position}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countBySocietyIdAndPositionAndApplicationStatusCodeAndStatus" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId !=''">
            AND society_id = #{societyId}
        </if>
        <if test="designation_code != null and designation_code !=''">
            AND designation_code = #{position}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>

    <select id="findOneByIdentificationNoAndSocietyIdAndPositionAndApplicationStatusCodeAndStatus"
            resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="identificationNo != null and identificationNo !=''">
            AND identification_no = #{identificationNo}
        </if>
        <if test="societyId != null and societyId !=''">
            AND society_id = #{societyId}
        </if>
        <if test="designation_code != null and designation_code !=''">
            AND designation_code = #{position}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        LIMIT 1
    </select>

    <select id="findOneBySocietyIdAndIdentificationNoAndApplicationStatusCodeAndStatusAndPosition"
            resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="identificationNo != null and identificationNo !=''">
            AND identification_no = #{identificationNo}
        </if>
        <if test="societyId != null and societyId !=''">
            AND society_id = #{societyId}
        </if>
        <if test="designation_code != null and designation_code !=''">
            AND designation_code = #{position}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="designation_code != null and designation_code !=''">
            AND designation_code = #{position}
        </if>
        LIMIT 1
    </select>

    <select id="findCommitteeIdListByIdentificationNo" resultType="java.lang.Long">
        SELECT id
        FROM
        <include refid="tb"/>
        WHERE identification_no = #{identificationNo}
    </select>

    <delete id="deleteByIdentityNoAndSocietyIdAndApplicationStatusCodeAndStatus">
        DELETE FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="identificationNo != null and identificationNo != ''">
            AND identification_no = #{identificationNo}
        </if>
        <if test="societyId != null and societyId != ''">
            AND society_id = #{societyId}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </delete>

    <select id="findSocietyIdAndDesignationCodeByCriteria"
            resultType="map">
        SELECT society_id, designation_code
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="identificationNo != null and identificationNo != ''">
            AND identification_no = #{identificationNo}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="designationCode != null and designationCode != ''">
            AND designation_code = #{designationCode}
        </if>
    </select>

    <select id="countRoleNoInSociety" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="status != null and status !=''">
            AND `status` = #{status}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="designationCodes != null">
            AND `designation_code` IN
            <foreach collection="designationCodes" separator="," item="designationCode" open="(" close=")">
                #{designationCode}
            </foreach>
        </if>
    </select>

    <select id="listRoleInSociety" parameterType="map" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="status != null and status !=''">
            AND `status` = #{status}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="designationCodes != null">
            AND `designation_code` IN
            <foreach collection="designationCodes" separator="," item="designationCode" open="(" close=")">
                #{designationCode}
            </foreach>
        </if>
        ORDER BY position_arrangement IS NULL, position_arrangement ASC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="listRoleInSocietyStatus" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="designationCode != null and designationCode !=''">
            AND `designation_code` = #{designationCode}
        </if>
        <if test="status != null and status !=''">
            AND `status` = #{status}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countRoleInSocietyStatus" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="designationCode != null and designationCode !=''">
            AND `designation_code` = #{designationCode}
        </if>
        <if test="status != null and status !=''">
            AND `status` = #{status}
        </if>
    </select>

    <select id="findByStatementId" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `statement_id` = #{statementId}
        LIMIT 1
    </select>

    <select id="findACommitteeInSocietyWithRole" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="position != null and position !=''">
            AND `designation_code` = #{position}
        </if>
        LIMIT 1
    </select>

    <select id="findActiveCommitteeInSocietyWithRoles" parameterType="map" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
        AND `status` = #{status}
        AND `application_status_code` = #{applicationStatusCode}
        AND `designation_code` IN
        <foreach separator="," collection="positions" item="position" open="(" close=")">
            #{position}
        </foreach>
        LIMIT 1
    </select>

    <select id="findActiveCommitteesInSocietyWithRoles" parameterType="map" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
        AND `status` = #{status}
        AND `application_status_code` = #{applicationStatusCode}
        AND `designation_code` IN
        <foreach separator="," collection="positions" item="position" open="(" close=")">
            #{position}
        </foreach>
    </select>

    <select id="findActiveByIdentificationNoAndDesignationCode" parameterType="map" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `identification_no` = #{identificationNo}
        AND `status` = #{status}
        AND `application_status_code` = #{applicationStatusCode}
        AND `designation_code` IN
        <foreach separator="," collection="positions" item="position" open="(" close=")">
            #{position}
        </foreach>
    </select>

    <delete id="deleteAllBySocietyId">
        DELETE FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
    </delete>

    <select id="findCommitteeByIdentificationNoIn" parameterType="map" resultMap="CommitteeMap" >
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        AND `society_id` = #{societyId}
        AND `identification_no` IN <foreach separator="," collection="identificationNos" item="identificationNo" open="(" close=")">
        #{identificationNo}
    </foreach>
    </select>
    <select id="findByIds" parameterType="map" resultMap="CommitteeMap" >
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE 1=1
        AND id IN <foreach collection="ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
        AND `society_id` = #{societyId}
        AND `status` = #{status}
        AND `designation_code` IN <foreach collection="designationCodes" item="designationCode" open="(" close=")" separator=",">#{designationCode}</foreach>
    </select>

    <select id="findByIdentificationNo" parameterType="java.lang.String" resultMap="CommitteeMap">
        SELECT <include refid="colsWithId"/>
        FROM <include refid="tb"/>
        WHERE 1=1
        AND `identification_no` = #{identificationNo}
    </select>

    <update id="updateBlacklistStatusByIdentificationNo" parameterType="map">
        UPDATE <include refid="tb"/>
        <set>
            `batal_flat` = #{isBlacklist},
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            modified_date=NOW()
        </set>
        <where>
            `identification_no` = #{identificationNo}
        </where>
    </update>

    <select id="existsByParam" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM <include refid="tb"/>
            WHERE 1=1
            <if test="identificationNo != null and identificationNo != ''">
                AND `identification_no` = #{identificationNo}
            </if>
            <if test="name != null and name != ''">
                AND `name` = #{name}
            </if>
            <if test="applicationStatusCode != null and applicationStatusCode != ''">
                AND `application_status_code` = #{applicationStatusCode}
            </if>
            <if test="status != null and status != ''">
                AND `status` = #{status}
            </if>
        )
    </select>

    <select id="findByParam" parameterType="map" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="identificationNo != null and identificationNo != ''">
            AND identification_no = #{identificationNo}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="meetingId != null">
            AND `meeting_id` = #{meetingId}
        </if>
        <if test="committeeName != null and committeeName != ''">
            AND `name` LIKE CONCAT('%', #{committeeName}, '%')
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
    </select>

    <select id="findByIdentificationNoAndActiveUserAndActiveSociety" parameterType="map" resultMap="CommitteeSelectMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>
        FROM <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON sc.society_id = s.id
        WHERE 1=1
        <if test="identificationNo != null and identificationNo != ''">
            AND sc.`identification_no` = #{identificationNo}
        </if>
        <if test="committeeApplicationStatusCode != null and committeeApplicationStatusCode != ''">
            AND sc.`application_status_code` = #{committeeApplicationStatusCode}
        </if>
        <if test="committeeStatusCode != null and committeeStatusCode != ''">
            AND sc.`status` = #{committeeStatusCode}
        </if>
        <if test="societyStatusCode != null and societyStatusCode != ''">
            AND s.`status_code` = #{societyStatusCode}
        </if>
        <if test="societyApplicationStatusCode != null and societyApplicationStatusCode != ''">
            AND s.`application_status_code` = #{societyApplicationStatusCode}
        </if>
    </select>

    <select id="findByPositionAndSocietyIdsAndApplicationStatusCodeAndStatus" parameterType="list" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1 AND
        `designation_code` IN
        <foreach collection="designationCode" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND `society_id` IN
        <foreach collection="societyIds" item="societyId" open="(" close=")" separator=",">
            #{societyId}
        </foreach>
        AND `status` = #{status}
        AND `application_status_code` = #{applicationStatusCode}
    </select>

    <select id="findBySocietyIdAndPositionAndStatus" parameterType="map" resultMap="CommitteeMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null and societyId !=''">
            AND society_id = #{societyId}
        </if>
        <if test="designation_code != null and designation_code !=''">
            AND designation_code = #{position}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            `status` = #{status},
            `application_status_code` = #{applicationStatusCode},
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            modified_date=NOW()
        </set>
        <where>
            `id` IN
            <foreach collection="committeeIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </update>

    <select id="findAllAppointedDates" parameterType="long" resultType="java.time.LocalDate">
        SELECT DISTINCT `appointed_date`
        FROM
        <include refid="tb"/>
        WHERE `society_id` = #{societyId}
        AND `application_status_code` = 11
    </select>

    <update id="saveAll" parameterType="java.util.List">
        UPDATE <include refid="tb"/>
        SET position_arrangement = CASE id
        <foreach collection="list" item="committee">
            WHEN #{committee.id} THEN #{committee.positionArrangement}
        </foreach>
        END
        WHERE id IN
        <foreach collection="list" item="committee" open="(" separator="," close=")">
            #{committee.id}
        </foreach>
    </update>
</mapper>
