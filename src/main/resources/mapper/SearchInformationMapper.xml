<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="SearchInformation">
    <resultMap id="SearchInformationResultMap" type="com.eroses.external.society.model.SearchInformation">
        <id column="id" property="id" />
        <result column="society_id" property="societyId" />
        <result column="society_no" property="societyNo" />
        <result column="view_certificate" property="viewCertificate" />
        <result column="view_constitution" property="viewConstitution" />
        <result column="view_committee" property="viewCommittee" />
        <result column="committee_year" property="committeeYear" />
        <result column="payment_total" property="paymentTotal" />
        <result column="acknowledgement" property="acknowledgement" />
        <result column="acknowledgement_date" property="acknowledgementDate" />
        <result column="payment_id" property="paymentId" />
        <result column="payment_method" property="paymentMethod" />
        <result column="payment_date" property="paymentDate" />
        <result column="receipt_no" property="receiptNo" />
        <result column="created_by" property="createdBy" />
        <result column="created_date" property="createdDate" />
        <result column="modified_by" property="modifiedBy" />
        <result column="modified_date" property="modifiedDate" />
        <result column="application_status_code" property="applicationStatusCode" />
        <result column="NoPPP_old" property="noPPPOld" />
        <result column="NoPPM_old" property="noPPMOld" />
        <result column="bank_name" property="bankName" />
        <result column="bank_reference_no" property="bankReferenceNo" />
        <result column="receipt_status" property="receiptStatus" />
        <result column="ro" property="ro" />
        <result column="e_payment_id" property="ePaymentId" />
        <result column="reconciliation_date" property="reconciliationDate" />
        <result column="approved_date" property="approvedDate" />
        <result column="status" property="status" />
    </resultMap>

    <resultMap id="SearchInformationSelectResultMap" type="com.eroses.external.society.model.SearchInformation">
        <id column="si_id" property="id" />
        <result column="si_society_id" property="societyId" />
        <result column="si_society_no" property="societyNo" />
        <result column="si_view_certificate" property="viewCertificate" />
        <result column="si_view_constitution" property="viewConstitution" />
        <result column="si_view_committee" property="viewCommittee" />
        <result column="si_committee_year" property="committeeYear" />
        <result column="si_payment_total" property="paymentTotal" />
        <result column="si_acknowledgement" property="acknowledgement" />
        <result column="si_acknowledgement_date" property="acknowledgementDate" />
        <result column="si_payment_id" property="paymentId" />
        <result column="si_payment_method" property="paymentMethod" />
        <result column="si_payment_date" property="paymentDate" />
        <result column="si_receipt_no" property="receiptNo" />
        <result column="si_created_by" property="createdBy" />
        <result column="si_created_date" property="createdDate" />
        <result column="si_modified_by" property="modifiedBy" />
        <result column="si_modified_date" property="modifiedDate" />
        <result column="si_application_status_code" property="applicationStatusCode" />
        <result column="si_NoPPP_old" property="noPPPOld" />
        <result column="si_NoPPM_old" property="noPPMOld" />
        <result column="si_bank_name" property="bankName" />
        <result column="si_bank_reference_no" property="bankReferenceNo" />
        <result column="si_receipt_status" property="receiptStatus" />
        <result column="si_ro" property="ro" />
        <result column="si_e_payment_id" property="ePaymentId" />
        <result column="si_reconciliation_date" property="reconciliationDate" />
        <result column="si_approved_date" property="approvedDate" />
        <result column="si_status" property="status" />

        <association property="society"
                     resultMap="Society.SocietySelectMap"/>
    </resultMap>

    <sql id="tb">
        `search_information`
    </sql>

    <sql id="selectTb">
        `search_information` si
    </sql>

    <sql id="cols">
        `id`,
        <include refid="insertCols"/>
    </sql>

    <sql id="selectCols">
        si.`id` as si_id,
        si.`society_id` as si_society_id,
        si.`society_no` as si_society_no,
        si.`view_certificate` as si_view_certificate,
        si.`view_constitution` as si_view_constitution,
        si.`view_committee` as si_view_committee,
        si.`committee_year` as si_committee_year,
        si.`payment_total` as si_payment_total,
        si.`acknowledgement` as si_acknowledgement,
        si.`acknowledgement_date` as si_acknowledgement_date,
        si.`payment_id` as si_payment_id,
        si.`payment_method` as si_payment_method,
        si.`payment_date` as si_payment_date,
        si.`receipt_no` as si_receipt_no,
        si.`created_by` as si_created_by,
        si.`created_date` as si_created_date,
        si.`modified_by` as si_modified_by,
        si.`modified_date` as si_modified_date,
        si.`application_status_code` as si_application_status_code,
        si.`NoPPP_old` as si_NoPPP_old,
        si.`NoPPM_old` as si_NoPPM_old,
        si.`bank_name` as si_bank_name,
        si.`bank_reference_no` as si_bank_reference_no,
        si.`receipt_status` as si_receipt_status,
        si.`ro` as si_ro,
        si.`e_payment_id` as si_e_payment_id,
        si.`reconciliation_date` as si_reconciliation_date,
        si.`approved_date` as si_approved_date,
        si.`status` as si_status
    </sql>

    <sql id="insertCols">
        `society_id`,
        `society_no`,
        `view_certificate`,
        `view_constitution`,
        `view_committee`,
        `committee_year`,
        `payment_total`,
        `acknowledgement`,
        `acknowledgement_date`,
        `payment_id`,
        `payment_method`,
        `payment_date`,
        `receipt_no`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`,
        `application_status_code`,
        `NoPPP_old`,
        `NoPPM_old`,
        `bank_name`,
        `bank_reference_no`,
        `receipt_status`,
        `ro`,
        `e_payment_id`,
        `reconciliation_date`,
        `approved_date`,
        `status`
    </sql>

    <sql id="val">
        #{societyId},
        #{societyNo},
        #{viewCertificate},
        #{viewConstitution},
        #{viewCommittee},
        #{committeeYear},
        #{paymentTotal},
        #{acknowledgement},
        #{acknowledgementDate},
        #{paymentId},
        #{paymentMethod},
        #{paymentDate},
        #{receiptNo},
        #{createdBy},
        #{createdDate},
        #{modifiedBy},
        #{modifiedDate},
        #{applicationStatusCode},
        #{noPPPOld},
        #{noPPMOld},
        #{bankName},
        #{bankReferenceNo},
        #{receiptStatus},
        #{ro},
        #{ePaymentId},
        #{reconciliationDate},
        #{approvedDate},
        #{status}
    </sql>

    <select id="findById" parameterType="long" resultMap="SearchInformationSelectResultMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON si.society_id = s.id
        WHERE si.`id` = #{id}
        LIMIT 1
    </select>

    <insert id="create" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="insertCols"/>)
        VALUES
        (<include refid="val"/>)
    </insert>

    <select id="getAll" parameterType="map" resultMap="SearchInformationSelectResultMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON si.society_id = s.id
        WHERE 1=1
        <if test="createdBy != null and createdBy !=''">
            AND si.`created_by` = #{createdBy}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND si.`application_status_code` != #{applicationStatusCode}
        </if>
        <if test="status != null and status !=''">
            AND si.`status` = #{status}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
            OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="applicationStatusCodeFilter != 0 and applicationStatusCodeFilter != null">
            AND si.`application_status_code` = #{applicationStatusCodeFilter}
        </if>
        <if test="paymentYear != 0 and paymentYear != null">
            AND YEAR(si.`payment_date`) = #{paymentYear}
        </if>
        ORDER BY si.`created_date` DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countGetAll" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON si.society_id = s.id
        WHERE 1=1
        <if test="createdBy != null and createdBy !=''">
            AND si.`created_by` = #{createdBy}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode !=''">
            AND si.`application_status_code` != #{applicationStatusCode}
        </if>
        <if test="status != null and status !=''">
            AND si.`status` = #{status}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            s.`society_name` LIKE CONCAT('%', #{searchQuery}, '%')
            OR s.`society_no` LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        <if test="applicationStatusCodeFilter != 0 and applicationStatusCodeFilter != null">
            AND si.`application_status_code` = #{applicationStatusCodeFilter}
        </if>
        <if test="paymentYear != 0 and paymentYear != null">
            AND YEAR(si.`payment_date`) = #{paymentYear}
        </if>
    </select>

    <select id="getByPaymentId" parameterType="long" resultMap="SearchInformationSelectResultMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON si.society_id = s.id
        WHERE si.`payment_id` = #{paymentId}
        LIMIT 1
    </select>

    <!-- Update an existing record -->
    <update id="update">
        UPDATE <include refid="tb" />
        <set>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="viewCertificate != null">`view_certificate` = #{viewCertificate},</if>
            <if test="viewConstitution != null">`view_constitution` = #{viewConstitution},</if>
            <if test="viewCommittee != null">`view_committee` = #{viewCommittee},</if>
            <if test="committeeYear != null">`committee_year` = #{committeeYear},</if>
            <if test="paymentTotal != null">`payment_total` = #{paymentTotal},</if>
            <if test="acknowledgement != null">`acknowledgement` = #{acknowledgement},</if>
            <if test="acknowledgementDate != null">`acknowledgement_date` = #{acknowledgementDate},</if>
            <if test="paymentId != null">`payment_id` = #{paymentId},</if>
            <if test="paymentMethod != null">`payment_method` = #{paymentMethod},</if>
            <if test="paymentDate != null">`payment_date` = #{paymentDate},</if>
            <if test="receiptNo != null">`receipt_no` = #{receiptNo},</if>
            <if test="createdBy != null">`created_by` = #{createdBy},</if>
            <if test="createdDate != null">`created_date` = #{createdDate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="modifiedDate != null">`modified_date` = #{modifiedDate},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="noPPPOld != null">`NoPPP_old` = #{noPPPOld},</if>
            <if test="noPPMOld != null">`NoPPM_old` = #{noPPMOld},</if>
            <if test="bankName != null">`bank_name` = #{bankName},</if>
            <if test="bankReferenceNo != null">`bank_reference_no` = #{bankReferenceNo},</if>
            <if test="receiptStatus != null">`receipt_status` = #{receiptStatus},</if>
            <if test="ro != null">`ro` = #{ro},</if>
            <if test="ePaymentId != null">`e_payment_id` = #{ePaymentId},</if>
            <if test="reconciliationDate != null">`reconciliation_date` = #{reconciliationDate},</if>
            <if test="approvedDate != null">`approved_date` = #{approvedDate},</if>
            <if test="status != null">`status` = #{status},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- Soft Delete an existing record -->
    <update id="delete">
        UPDATE <include refid="tb" />
        <set>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
        </set>
        WHERE `id` = #{id}
    </update>
</mapper>