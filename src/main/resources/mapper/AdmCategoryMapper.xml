<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AdmCategory">

    <resultMap id="AdmCategoryMap" type="com.eroses.external.society.model.AdmCategory">
        <id property="id" column="id" />
        <result column="pid" property="pid"/>
        <result column="category_code_jppm" property="categoryCodeJppm"/>
        <result column="category_name_en" property="categoryNameEn"/>
        <result column="category_name_bm" property="categoryNameBm"/>
        <result column="level" property="level"/>
        <result column="caw_stat" property="cawStat"/>
        <result column="created_date" property="createdDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="status" property="status"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        adm_category
    </sql>

    <sql id="cols">
        id,
        pid,
        category_code_jppm,
        category_name_en,
        category_name_bm,
        level,
        caw_stat,
        created_date,
        created_by,
        status,
        modified_by,
        modified_date
    </sql>

    <sql id="cols1">
        id,
        pid,
        category_code_jppm,
        category_name_en,
        category_name_bm,
        level,
        caw_stat,
        status
    </sql>

    <sql id="vals">
        #{pid},
        #{categoryCodeJppm},
        #{categoryNameEn},
        #{categoryNameBm},
        #{level},
        #{cawStat},
        now(),
        #{createdBy},
        #{status},
        #{modifiedBy},
        now()
    </sql>


    <select id="findAll" resultType="list" resultMap="AdmCategoryMap">
        SELECT
        <include refid="cols1"/>
        FROM
        <include refid="tb"/>
        WHERE `status` != '-1'
        AND `status` != '0'
    </select>

    <select id="findById" parameterType="long" resultMap="AdmCategoryMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
    </select>

</mapper>
