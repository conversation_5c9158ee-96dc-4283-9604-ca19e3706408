<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="SocietyCommitteeTaskConfig">
    <resultMap id="SocietyCommitteeTaskConfigMap" type="com.eroses.external.society.model.SocietyCommitteeTaskConfig">
        <result column="id" property="id"/>
        <result column="society_id" property="societyId"/>
        <result column="module" property="module"/>
    </resultMap>
    <sql id="tb">`society_committee_task_config`</sql>
    <sql id="col">`society_id`,`module`</sql>
    <sql id="col_with_id">`id`, <include refid="col"/></sql>
    <insert id="insertCommitteeTaskForModule">
        INSERT INTO <include refid="tb"/> (<include refid="col"/>)
        VALUES (#{societyId}, #{module});
    </insert>
    <delete id="removeCommitteeTaskForModule">
        DELETE FROM <include refid="tb"/>
        <where>
            1=1 AND `society_id`=#{societyId} AND `module`=#{module}
        </where>
    </delete>
    <select id="findCommitteeTaskForModule" resultMap="SocietyCommitteeTaskConfigMap">
        SELECT <include refid="col_with_id"/> FROM <include refid="tb"/>
        <where>
            1=1 AND `society_id`=#{societyId} AND `module`=#{module}
        </where>
    </select>
</mapper>