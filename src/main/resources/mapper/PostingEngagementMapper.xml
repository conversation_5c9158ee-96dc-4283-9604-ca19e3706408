<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="PostingEngagement">
    <sql id="tb">posting_engagement</sql>
    <resultMap id="PostingMediaMap" type="com.eroses.external.society.model.posting.Posting">
        <id property="id" column="id" />
        <result property="postingId" column="posting_id" />
        <result property="userId" column="user_id" />
        <result property="engagementType" column="engagement_type" />
        <result property="ipAddress" column="ip_address" />
        <result property="engagementDate" column="engagement_date" />
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date" />
    </resultMap>
    <sql id="cols">
        posting_id,
        user_id,
        engagement_type,
        state,
        ip_address,
        engagement_date,
        created_by,
        created_date
    </sql>

    <sql id="colsWithId">
        id,
        <include refid="cols"/>
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.posting.PostingEngagement" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
            posting_id,
            user_id,
            engagement_type,
            state,
            ip_address,
            engagement_date,
            created_by,
            created_date
        )
        VALUES (
            #{postingId},
            #{userId},
            #{engagementType},
            #{state},
            #{ipAddress},
            #{engagementDate},
            #{createdBy},
            #{createdDate}
        )
    </insert>

    <!-- Find By ID -->
    <select id="findById" resultType="com.eroses.external.society.model.posting.PostingEngagement">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            id = #{id}
    </select>

    <!-- Find By Posting ID -->
    <select id="findByPostingId" resultType="com.eroses.external.society.model.posting.PostingEngagement">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            posting_id = #{postingId}
    </select>

    <!-- Find By User ID -->
    <select id="findByUserId" resultType="com.eroses.external.society.model.posting.PostingEngagement">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            user_id = #{userId}
    </select>

    <!-- Find By User ID And Posting ID -->
    <select id="findByUserIdAndPostingId" resultType="com.eroses.external.society.model.posting.PostingEngagement">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            user_id = #{userId}
            AND posting_id = #{postingId}
    </select>

    <!-- Find By Engagement Type -->
    <select id="findByEngagementType" resultType="com.eroses.external.society.model.posting.PostingEngagement">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            engagement_type = #{engagementType}
    </select>

    <!-- Count By Posting ID And Engagement Type -->
    <select id="countByPostingIdAndEngagementType" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE
            posting_id = #{postingId}
            AND engagement_type = #{engagementType}
    </select>

    <!-- Get Views By State -->
    <select id="getViewsByState" resultType="java.util.Map">
        SELECT
            state,
            COUNT(*) as count
        FROM
        <include refid="tb"/>
        WHERE
            engagement_type = 'VIEW'
            AND state IS NOT NULL
        GROUP BY
            state
    </select>

    <!-- Get Views By Date -->
    <select id="getViewsByDate" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(engagement_date, '%Y-%m-%d') as date,
            COUNT(*) as count
        FROM
        <include refid="tb"/>
        WHERE
            engagement_type = 'VIEW'
            <if test="dateFrom != null and dateFrom != ''">
                AND engagement_date >= #{dateFrom}
            </if>
            <if test="dateTo != null and dateTo != ''">
                AND engagement_date &lt;= #{dateTo}
            </if>
        GROUP BY
            DATE_FORMAT(engagement_date, '%Y-%m-%d')
    </select>
</mapper>
