<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="StatementFinancial">

    <!-- Result Map -->
    <resultMap id="StatementFinancialMap" type="com.eroses.external.society.model.StatementFinancial">
        <id property="id" column="id"/>
        <result property="statementId" column="statement_id"/>
        <result property="societyId" column="society_id"/>
        <result property="societyNo" column="society_no"/>
        <result property="branchId" column="branch_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="financialDeclaration" column="financial_declaration"/>
        <result property="feeIncome" column="fee_income"/>
        <result property="feeIncomeMember" column="fee_income_member"/>
        <result property="fundIncome" column="fund_income"/>
        <result property="donationIncome" column="donation_income"/>
        <result property="othersIncome" column="others_income"/>
        <result property="foodRevenue" column="food_revenue"/>
        <result property="bookRevenue" column="book_revenue"/>
        <result property="serviceRevenue" column="service_revenue"/>
        <result property="otherRevenue" column="other_revenue"/>
        <result property="rentalInvestment" column="rental_investment"/>
        <result property="dividendInvestment" column="dividend_investment"/>
        <result property="interestInvestment" column="interest_investment"/>
        <result property="propertyInvestment" column="property_investment"/>
        <result property="otherInvestment" column="other_investment"/>
        <result property="govGrant" column="gov_grant"/>
        <result property="privateGrant" column="private_grant"/>
        <result property="individualGrant" column="individual_grant"/>
        <result property="otherGrant" column="other_grant"/>
        <result property="otherIncome" column="other_income"/>
        <result property="totalIncome" column="total_income"/>
        <result property="sumbExpense" column="sumb_expense"/>
        <result property="donationExpense" column="donation_expense"/>
        <result property="taxExpense" column="tax_expense"/>
        <result property="otherExpenses" column="other_expenses"/>
        <result property="generalWelfare" column="general_welfare"/>
        <result property="deathWelfare" column="death_welfare"/>
        <result property="giftWelfare" column="gift_welfare"/>
        <result property="scholarshipWelfare" column="scholarship_welfare"/>
        <result property="zakatWelfare" column="zakat_welfare"/>
        <result property="donationWelfare" column="donation_welfare"/>
        <result property="organizedActivity" column="organized_activity"/>
        <result property="promoActivity" column="promo_activity"/>
        <result property="banquetActivity" column="banquet_activity"/>
        <result property="tourActivity" column="tour_activity"/>
        <result property="rentalCost" column="rental_cost"/>
        <result property="utilityCost" column="utility_cost"/>
        <result property="supplyCost" column="supply_cost"/>
        <result property="otherCost" column="other_cost"/>
        <result property="otherExpense" column="other_expense"/>
        <result property="totalExpense" column="total_expense"/>
        <result property="buildingAsset" column="building_asset"/>
        <result property="investmentAsset" column="investment_asset"/>
        <result property="intangibleAsset" column="intangible_asset"/>
        <result property="assetOnHand" column="asset_on_hand"/>
        <result property="bankAsset" column="bank_asset"/>
        <result property="accountAsset" column="account_asset"/>
        <result property="inventoryAsset" column="inventory_asset"/>
        <result property="investAsset" column="invest_asset"/>
        <result property="depositAsset" column="deposit_asset"/>
        <result property="taxAsset" column="tax_asset"/>
        <result property="totalAsset" column="total_asset"/>
        <result property="creditorLiability" column="creditor_liability"/>
        <result property="taxLiability" column="tax_liability"/>
        <result property="loanLiability" column="loan_liability"/>
        <result property="debtLiability" column="debt_liability"/>
        <result property="deferredTaxLiability" column="deferred_tax_liability"/>
        <result property="borrowLiability" column="borrow_liability"/>
        <result property="totalLiability" column="total_liability"/>
        <result property="applicationStatusCode" column="application_status_code"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
        <result property="cardCost" column="card_cost"/>
        <result property="investmentActivity" column="investment_activity"/>
        <result property="feeActivity" column="fee_activity"/>
        <result property="otherActivity" column="other_activity"/>
        <result property="salaryCost" column="salary_cost"/>
        <result property="bonusCost" column="bonus_cost"/>
        <result property="kwspCost" column="kwsp_cost"/>
        <result property="insuranceCost" column="insurance_cost"/>
        <result property="maintenanceCost" column="maintenance_cost"/>
        <result property="renovationCost" column="renovation_cost"/>
        <result property="transportationCost" column="transportation_cost"/>
        <result property="photocopyCost" column="photocopy_cost"/>
        <result property="bankChargeCost" column="bank_charge_cost"/>
        <result property="assetNo" column="asset_no"/>
        <result property="landAsset" column="land_asset"/>
        <result property="vehicleAsset" column="vehicle_asset"/>
        <result property="machineAsset" column="machine_asset"/>
        <result property="furnitureAsset" column="furniture_asset"/>
        <result property="officeAsset" column="office_asset"/>
        <result property="uniformCost" column="uniform_cost"/>
        <result property="expenseNo" column="expense_no"/>
    </resultMap>

    <!-- Table Name -->
    <sql id="tb">
        `statement_financial`
    </sql>

    <!-- Columns without ID -->
    <sql id="cols">
        `statement_id`,
        `society_id`,
        `society_no`,
        `branch_id`,
        `branch_no`,
        `financial_declaration`,
        `fee_income`,
        `fee_income_member`,
        `fund_income`,
        `donation_income`,
        `others_income`,
        `food_revenue`,
        `book_revenue`,
        `service_revenue`,
        `other_revenue`,
        `rental_investment`,
        `dividend_investment`,
        `interest_investment`,
        `property_investment`,
        `other_investment`,
        `gov_grant`,
        `private_grant`,
        `individual_grant`,
        `other_grant`,
        `other_income`,
        `total_income`,
        `sumb_expense`,
        `donation_expense`,
        `tax_expense`,
        `other_expenses`,
        `general_welfare`,
        `death_welfare`,
        `gift_welfare`,
        `scholarship_welfare`,
        `zakat_welfare`,
        `donation_welfare`,
        `organized_activity`,
        `promo_activity`,
        `banquet_activity`,
        `tour_activity`,
        `rental_cost`,
        `utility_cost`,
        `supply_cost`,
        `other_cost`,
        `other_expense`,
        `total_expense`,
        `building_asset`,
        `investment_asset`,
        `intangible_asset`,
        `asset_on_hand`,
        `bank_asset`,
        `account_asset`,
        `inventory_asset`,
        `invest_asset`,
        `deposit_asset`,
        `tax_asset`,
        `total_asset`,
        `creditor_liability`,
        `tax_liability`,
        `loan_liability`,
        `debt_liability`,
        `deferred_tax_liability`,
        `borrow_liability`,
        `total_liability`,
        `application_status_code`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`,
        `card_cost`,
        `investment_activity`,
        `fee_activity`,
        `other_activity`,
        `salary_cost`,
        `bonus_cost`,
        `kwsp_cost`,
        `insurance_cost`,
        `maintenance_cost`,
        `renovation_cost`,
        `transportation_cost`,
        `photocopy_cost`,
        `bank_charge_cost`,
        `asset_no`,
        `land_asset`,
        `vehicle_asset`,
        `machine_asset`,
        `furniture_asset`,
        `office_asset`,
        `uniform_cost`,
        `expense_no`
    </sql>

    <!-- Columns with ID -->
    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.StatementFinancial" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (
        #{statementId},
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{financialDeclaration},
        #{feeIncome},
        #{feeIncomeMember},
        #{fundIncome},
        #{donationIncome},
        #{othersIncome},
        #{foodRevenue},
        #{bookRevenue},
        #{serviceRevenue},
        #{otherRevenue},
        #{rentalInvestment},
        #{dividendInvestment},
        #{interestInvestment},
        #{propertyInvestment},
        #{otherInvestment},
        #{govGrant},
        #{privateGrant},
        #{individualGrant},
        #{otherGrant},
        #{otherIncome},
        #{totalIncome},
        #{sumbExpense},
        #{donationExpense},
        #{taxExpense},
        #{otherExpenses},
        #{generalWelfare},
        #{deathWelfare},
        #{giftWelfare},
        #{scholarshipWelfare},
        #{zakatWelfare},
        #{donationWelfare},
        #{organizedActivity},
        #{promoActivity},
        #{banquetActivity},
        #{tourActivity},
        #{rentalCost},
        #{utilityCost},
        #{supplyCost},
        #{otherCost},
        #{otherExpense},
        #{totalExpense},
        #{buildingAsset},
        #{investmentAsset},
        #{intangibleAsset},
        #{assetOnHand},
        #{bankAsset},
        #{accountAsset},
        #{inventoryAsset},
        #{investAsset},
        #{depositAsset},
        #{taxAsset},
        #{totalAsset},
        #{creditorLiability},
        #{taxLiability},
        #{loanLiability},
        #{debtLiability},
        #{deferredTaxLiability},
        #{borrowLiability},
        #{totalLiability},
        #{applicationStatusCode},
        #{createdBy},
        NOW(),
        #{modifiedBy},
        NOW(),
        #{cardCost},
        #{investmentActivity},
        #{feeActivity},
        #{otherActivity},
        #{salaryCost},
        #{bonusCost},
        #{kwspCost},
        #{insuranceCost},
        #{maintenanceCost},
        #{renovationCost},
        #{transportationCost},
        #{photocopyCost},
        #{bankChargeCost},
        #{assetNo},
        #{landAsset},
        #{vehicleAsset},
        #{machineAsset},
        #{furnitureAsset},
        #{officeAsset},
        #{uniformCost},
        #{expenseNo}
        )
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.StatementFinancial">
        UPDATE
        <include refid="tb"/>
        <set>
<!--            <if test="statementId != null">`statement_id` = #{statementId},</if>-->
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="societyNo != null">`society_no` = #{societyNo},</if>
            <if test="branchId != null">`branch_id` = #{branchId},</if>
            <if test="branchNo != null">`branch_no` = #{branchNo},</if>
            <if test="financialDeclaration != null">`financial_declaration` = #{financialDeclaration},</if>
            <if test="feeIncome != null">`fee_income` = #{feeIncome},</if>
            <if test="feeIncomeMember != null">`fee_income_member` = #{feeIncomeMember},</if>
            <if test="fundIncome != null">`fund_income` = #{fundIncome},</if>
            <if test="donationIncome != null">`donation_income` = #{donationIncome},</if>
            <if test="othersIncome != null">`others_income` = #{othersIncome},</if>
            <if test="foodRevenue != null">`food_revenue` = #{foodRevenue},</if>
            <if test="bookRevenue != null">`book_revenue` = #{bookRevenue},</if>
            <if test="serviceRevenue != null">`service_revenue` = #{serviceRevenue},</if>
            <if test="otherRevenue != null">`other_revenue` = #{otherRevenue},</if>
            <if test="rentalInvestment != null">`rental_investment` = #{rentalInvestment},</if>
            <if test="dividendInvestment != null">`dividend_investment` = #{dividendInvestment},</if>
            <if test="interestInvestment != null">`interest_investment` = #{interestInvestment},</if>
            <if test="propertyInvestment != null">`property_investment` = #{propertyInvestment},</if>
            <if test="otherInvestment != null">`other_investment` = #{otherInvestment},</if>
            <if test="govGrant != null">`gov_grant` = #{govGrant},</if>
            <if test="privateGrant != null">`private_grant` = #{privateGrant},</if>
            <if test="individualGrant != null">`individual_grant` = #{individualGrant},</if>
            <if test="otherGrant != null">`other_grant` = #{otherGrant},</if>
            <if test="otherIncome != null">`other_income` = #{otherIncome},</if>
            <if test="totalIncome != null">`total_income` = #{totalIncome},</if>
            <if test="sumbExpense != null">`sumb_expense` = #{sumbExpense},</if>
            <if test="donationExpense != null">`donation_expense` = #{donationExpense},</if>
            <if test="taxExpense != null">`tax_expense` = #{taxExpense},</if>
            <if test="otherExpenses != null">`other_expenses` = #{otherExpenses},</if>
            <if test="generalWelfare != null">`general_welfare` = #{generalWelfare},</if>
            <if test="deathWelfare != null">`death_welfare` = #{deathWelfare},</if>
            <if test="giftWelfare != null">`gift_welfare` = #{giftWelfare},</if>
            <if test="scholarshipWelfare != null">`scholarship_welfare` = #{scholarshipWelfare},</if>
            <if test="zakatWelfare != null">`zakat_welfare` = #{zakatWelfare},</if>
            <if test="donationWelfare != null">`donation_welfare` = #{donationWelfare},</if>
            <if test="organizedActivity != null">`organized_activity` = #{organizedActivity},</if>
            <if test="promoActivity != null">`promo_activity` = #{promoActivity},</if>
            <if test="banquetActivity != null">`banquet_activity` = #{banquetActivity},</if>
            <if test="tourActivity != null">`tour_activity` = #{tourActivity},</if>
            <if test="rentalCost != null">`rental_cost` = #{rentalCost},</if>
            <if test="utilityCost != null">`utility_cost` = #{utilityCost},</if>
            <if test="supplyCost != null">`supply_cost` = #{supplyCost},</if>
            <if test="otherCost != null">`other_cost` = #{otherCost},</if>
            <if test="otherExpense != null">`other_expense` = #{otherExpense},</if>
            <if test="totalExpense != null">`total_expense` = #{totalExpense},</if>
            <if test="buildingAsset != null">`building_asset` = #{buildingAsset},</if>
            <if test="investmentAsset != null">`investment_asset` = #{investmentAsset},</if>
            <if test="intangibleAsset != null">`intangible_asset` = #{intangibleAsset},</if>
            <if test="assetOnHand != null">`asset_on_hand` = #{assetOnHand},</if>
            <if test="bankAsset != null">`bank_asset` = #{bankAsset},</if>
            <if test="accountAsset != null">`account_asset` = #{accountAsset},</if>
            <if test="inventoryAsset != null">`inventory_asset` = #{inventoryAsset},</if>
            <if test="investAsset != null">`invest_asset` = #{investAsset},</if>
            <if test="depositAsset != null">`deposit_asset` = #{depositAsset},</if>
            <if test="taxAsset != null">`tax_asset` = #{taxAsset},</if>
            <if test="totalAsset != null">`total_asset` = #{totalAsset},</if>
            <if test="creditorLiability != null">`creditor_liability` = #{creditorLiability},</if>
            <if test="taxLiability != null">`tax_liability` = #{taxLiability},</if>
            <if test="loanLiability != null">`loan_liability` = #{loanLiability},</if>
            <if test="debtLiability != null">`debt_liability` = #{debtLiability},</if>
            <if test="deferredTaxLiability != null">`deferred_tax_liability` = #{deferredTaxLiability},</if>
            <if test="borrowLiability != null">`borrow_liability` = #{borrowLiability},</if>
            <if test="totalLiability != null">`total_liability` = #{totalLiability},</if>
            <if test="applicationStatusCode != null">`application_status_code` = #{applicationStatusCode},</if>
            <if test="cardCost != null">`card_cost` = #{cardCost},</if>
            <if test="investmentActivity != null">`investment_activity` = #{investmentActivity},</if>
            <if test="feeActivity != null">`fee_activity` = #{feeActivity},</if>
            <if test="otherActivity != null">`other_activity` = #{otherActivity},</if>
            <if test="salaryCost != null">`salary_cost` = #{salaryCost},</if>
            <if test="bonusCost != null">`bonus_cost` = #{bonusCost},</if>
            <if test="kwspCost != null">`kwsp_cost` = #{kwspCost},</if>
            <if test="insuranceCost != null">`insurance_cost` = #{insuranceCost},</if>
            <if test="maintenanceCost != null">`maintenance_cost` = #{maintenanceCost},</if>
            <if test="renovationCost != null">`renovation_cost` = #{renovationCost},</if>
            <if test="transportationCost != null">`transportation_cost` = #{transportationCost},</if>
            <if test="photocopyCost != null">`photocopy_cost` = #{photocopyCost},</if>
            <if test="bankChargeCost != null">`bank_charge_cost` = #{bankChargeCost},</if>
            <if test="assetNo != null">`asset_no` = #{assetNo},</if>
            <if test="landAsset != null">`land_asset` = #{landAsset},</if>
            <if test="vehicleAsset != null">`vehicle_asset` = #{vehicleAsset},</if>
            <if test="machineAsset != null">`machine_asset` = #{machineAsset},</if>
            <if test="furnitureAsset != null">`furniture_asset` = #{furnitureAsset},</if>
            <if test="officeAsset != null">`office_asset` = #{officeAsset},</if>
            <if test="uniformCost != null">`uniform_cost` = #{uniformCost},</if>
            <if test="expenseNo != null">`expense_no` = #{expenseNo},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            `modified_date` = NOW()
        </set>
        WHERE `statement_id` = #{statementId}
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </update>

    <!-- Select All -->
    <select id="findAll" parameterType="map" resultMap="StatementFinancialMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- Select By ID -->
    <select id="findById" parameterType="java.lang.Long" resultMap="StatementFinancialMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <!-- Count All -->
    <select id="countFindAll" resultType="long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
    </select>

    <!-- Find by SocietyId and StatementId with Pagination -->
    <select id="findOneBySocietyIdAndStatementId" parameterType="map" resultMap="StatementFinancialMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND society_id = #{societyId}
        </if>
        <if test="statementId != null">
            AND statement_id = #{statementId}
        </if>
        LIMIT 1
    </select>

    <select id="findByStatementId" parameterType="map" resultMap="StatementFinancialMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `statement_id` = #{statementId}
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        LIMIT 1
    </select>

    <select id="findByParam" parameterType="map" resultMap="StatementFinancialMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `statement_id` = #{statementId}
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <choose>
            <when test="branchId == null">
                AND `branch_id` IS NULL
            </when>
            <otherwise>
                AND `branch_id` = #{branchId}
            </otherwise>
        </choose>
        LIMIT 1
    </select>

    <update id="deleteStatement" parameterType="map">
        UPDATE <include refid="tb"/>
        <set>
            `application_status_code` = -1
        </set>
        WHERE
        `statement_id` = #{statementId}
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
    </update>

    <select id="isExistsByStatementId" parameterType="java.lang.Long" resultType="boolean">
        SELECT
        EXISTS(SELECT 1 FROM <include refid="tb"/>
        WHERE `statement_id` = #{id})
    </select>
</mapper>