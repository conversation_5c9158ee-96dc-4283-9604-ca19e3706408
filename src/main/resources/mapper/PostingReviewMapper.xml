<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="PostingReview">
    <sql id="tb">posting_review</sql>

    <resultMap id="PostingReviewMap" type="com.eroses.external.society.model.posting.Posting">
        <id property="id" column="id" />
        <result property="postingId" column="posting_id" />
        <result property="committeeId" column="committee_id" />
        <result property="responseStatus" column="response_status" />
        <result property="rejectionComment" column="rejection_comment" />
        <result property="responseDate" column="response_date" />
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date" />
    </resultMap>

    <sql id="cols">
        posting_id,
        committee_id,
        response_status,
        rejection_comment,
        response_date,
        created_by,
        created_date
    </sql>

    <sql id="colsWithId">
        id,
        <include refid="cols"/>
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.posting.PostingReview" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
            posting_id,
            committee_id,
            response_status,
            rejection_comment,
            response_date,
            created_by,
            created_date
        )
        VALUES (
            #{postingId},
            #{committeeId},
            #{responseStatus},
            #{rejectionComment},
            #{responseDate},
            #{createdBy},
            #{createdDate}
        )
    </insert>

    <!-- Find By ID -->
    <select id="findById" resultType="com.eroses.external.society.model.posting.PostingReview">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            id = #{id}
    </select>

    <!-- Find By Posting ID -->
    <select id="findByPostingId" resultType="com.eroses.external.society.dto.response.posting.PostingReviewResponse">
        SELECT
            pr.id,
            pr.posting_id as postingId,
            pr.committee_id as reviewerId,
            c.name as reviewerName,
            c.designation_code as reviewerRole,
            pr.response_status as reviewStatus,
            pr.rejection_comment as rejectionComment,
            pr.response_date as reviewDate
        FROM
        <include refid="tb"/> pr
        JOIN society_committee c ON pr.committee_id = c.id
        WHERE
            pr.posting_id = #{postingId}
        ORDER BY
            pr.response_date DESC
    </select>

    <!-- Find By Reviewer ID -->
    <select id="findByReviewerId" resultType="com.eroses.external.society.model.posting.PostingReview">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            committee_id = #{reviewerId}
    </select>

    <!-- Find By Reviewer ID And Posting ID -->
    <select id="findByReviewerIdAndPostingId" resultType="com.eroses.external.society.model.posting.PostingReview">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            committee_id = #{reviewerId}
            AND posting_id = #{postingId}
    </select>

    <!-- Count By Posting ID And Review Status -->
    <select id="countByPostingIdAndReviewStatus" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE
            posting_id = #{postingId}
            AND response_status = #{reviewStatus}
    </select>
</mapper>
