<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="CommitteeTask">
    <resultMap id="CommitteeTaskMap" type="com.eroses.external.society.model.CommitteeTask">
        <!-- Basic fields -->
        <id property="id" column="id"/>
        <result property="societyId" column="society_id"/>
        <result property="societyNo" column="society_no"/>
        <result property="branchId" column="branch_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="societyCommitteeId" column="society_committee_id"/>
        <result property="identificationNo" column="identification_no"/>
        <result property="designationCode" column="designation_code"/>
        <result property="statementId" column="statement_id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="taskActivateDate" column="task_activate_date"/>
        <result property="taskDeactivateDate" column="task_deactivate_date"/>
        <result property="branchCommitteeId" column="branch_committee_id"/>
        <result property="module" column="module"/>
        <!-- Auditing fields -->
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>

    </resultMap>

    <sql id="tb">
        `committee_task`
    </sql>
    <sql id="cols">
        `society_id`,
        `society_no`,
        `branch_id`,
        `branch_no`,
        `society_committee_id`,
        `identification_no`,
        `designation_code`,
        `statement_id`,
        `name`,
        `status`,
        `task_activate_date`,
        `task_deactivate_date`,
        `branch_committee_id`,
        `module`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.CommitteeTask" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (
        <include refid="cols"/>
        )
        VALUES (
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{societyCommitteeId},
        #{identificationNo},
        #{designationCode},
        #{statementId},
        #{name},
        #{status},
        #{taskActivateDate},
        #{taskDeactivateDate},
        #{branchCommitteeId},
        #{module},
        #{createdBy},
        NOW(),
        #{modifiedBy},
        NOW()
        )
    </insert>

    <select id="listCommitteeTask" resultMap="CommitteeTaskMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <choose>
            <when test="branchId != null">
                AND `branch_id`= #{branchId}
            </when>
            <otherwise>
                AND `branch_id` IS NULL
            </otherwise>
        </choose>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="module != null">
            AND `module` = #{module}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countListCommitteeTask" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND `society_id` = #{societyId}
        </if>
        <choose>
            <when test="branchId != null">
                AND `branch_id`= #{branchId}
            </when>
            <otherwise>
                AND `branch_id` IS NULL
            </otherwise>
        </choose>
        <if test="statementId != null">
            AND `statement_id` = #{statementId}
        </if>
        <if test="module !=null">
            AND `module` = #{module}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
    </select>

    <select id="findById" resultMap="CommitteeTaskMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.CommitteeTask">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="status != null">`status` = #{status},</if>
            <if test="taskActivateDate != null">`task_activate_date` = #{taskActivateDate},</if>
            <if test="taskDeactivateDate != null">`task_deactivate_date` = #{taskDeactivateDate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="modifiedDate != null">`modified_date` = NOW(),</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <update id="updateStatus" parameterType="com.eroses.external.society.model.CommitteeTask">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="status != null">`status` = #{status},</if>
            <if test="taskActivateDate != null">`task_activate_date` = #{taskActivateDate},</if>
            `task_deactivate_date` = #{taskDeactivateDate},
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="modifiedDate != null">`modified_date` = NOW(),</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="findByStatementId" resultMap="CommitteeTaskMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `statement_id` = #{statementId}
        LIMIT 1
    </select>

    <select id="findByCriteria" resultMap="CommitteeTaskMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <where>
            1=1
            <if test="societyId != null">
                AND `society_id` = #{societyId}
            </if>
            <choose>
                <when test="branchId != null">
                    AND `branch_id`= #{branchId}
                </when>
                <otherwise>
                    AND `branch_id` IS NULL
                </otherwise>
            </choose>
            <if test="identificationNo != null">
                AND `identification_no` = #{identificationNo}
            </if>
            <if test="module!=null">
                AND `module` = #{module}
            </if>
            <if test="status != null">
                AND `status` = #{status}
            </if>
        </where>
    </select>
</mapper>
