<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="DocumentTemplate">

    <resultMap id="DocumentTemplateMap" type="com.eroses.external.society.model.DocumentTemplate">
        <id property="id" column="id"/>
        <result column="template_code" property="templateCode"/>
        <result column="template_name" property="templateName"/>
        <result column="template_language" property="templateLanguage"/>
        <result column="description" property="description"/>
        <result column="subject" property="subject"/>
        <result column="html_content" property="htmlContent"/>
        <result column="byte_content" property="byteContent"/>
        <result column="status" property="status"/>
        <result column="type" property="type"/>
        <result column="template_format" property="templateFormat"/>
        <result column="url" property="url"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <sql id="tb">
        `document_template`
    </sql>

    <sql id="cols">
        `template_code`, `template_name`, `template_language`, `description`, `subject`, `html_content`, `byte_content`, `status`,
        `type`, `template_format`, `url`, `created_date`, `created_by`, `modified_date`, `modified_by`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.DocumentTemplate" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (#{templateCode}, #{templateName}, #{templateLanguage}, #{description}, #{subject}, #{htmlContent}, #{byteContent}, #{status},
        #{type}, #{templateFormat}, #{url}, now(), #{createdBy}, now(), #{modifiedBy})
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.DocumentTemplate">
        UPDATE
        <include refid="tb"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateCode != null">`template_code` = #{templateCode},</if>
            <if test="templateName != null">`template_name` = #{templateName},</if>
            <if test="templateLanguage != null">`template_language` = #{templateLanguage},</if>
            <if test="description != null">`description` = #{description},</if>
            <if test="subject != null">`subject` = #{subject},</if>
            <if test="htmlContent != null">`html_content` = #{htmlContent},</if>
            <if test="byteContent != null">`byte_content` = #{byteContent},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="type != null">`type` = #{type},</if>
            <if test="templateFormat != null">`template_format` = #{templateFormat},</if>
            <if test="url != null">`url` = #{url},</if>
            <if test="createdBy != null">`created_by` = #{createdBy},</if>
            <if test="createdDate != null">`created_date` = #{createdDate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="modifiedDate != null">`modified_date` = now(),</if>
        </trim>
        WHERE `id` = #{id}
    </update>

    <select id="findById" resultMap="DocumentTemplateMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `id` = #{id}
    </select>

    <select id="findByCode" parameterType="map" resultMap="DocumentTemplateMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `template_code` = #{code}
    </select>

</mapper>