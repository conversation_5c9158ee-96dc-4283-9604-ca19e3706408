<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AdmPositionCommittee">

    <resultMap id="AdmPositionCommitteeMap" type="com.eroses.external.society.model.AdmPositionCommittee">
        <id property="id" column="id" />
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="rank" property="rank"/>
        <result column="is_committee" property="isCommittee"/>
        <result column="is_high_rank_committee" property="isHighRankCommittee"/>
        <result column="status" property="status"/>
        <result column="created_date" property="createdDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="modified_by" property="modifiedBy"/>
    </resultMap>

    <resultMap id="AdmPositionCommitteeSelectMap" type="com.eroses.external.society.model.AdmPositionCommittee">
        <id property="id" column="apc_id" />
        <result column="apc_code" property="code"/>
        <result column="apc_name" property="name"/>
        <result column="apc_rank" property="rank"/>
        <result column="apc_is_committee" property="isCommittee"/>
        <result column="apc_is_high_rank_committee" property="isHighRankCommittee"/>
        <result column="apc_status" property="status"/>
        <result column="apc_created_date" property="createdDate"/>
        <result column="apc_created_by" property="createdBy"/>
        <result column="apc_modified_date" property="modifiedDate"/>
        <result column="apc_modified_by" property="modifiedBy"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        adm_position_committee
    </sql>

    <sql id="selectTb">
        adm_position_committee apc
    </sql>

    <sql id="colsWithId">
        id,
        <include refid="cols"/>
    </sql>

    <sql id="cols">
        code,
        name,
        rank,
        is_committee,
        is_high_rank_committee,
        status,
        created_date,
        created_by,
        modified_date,
        modified_by
    </sql>

    <sql id="selectCols">
        apc.`id` as apc_id,
        apc.`code` as apc_code,
        apc.`name` as apc_name,
        apc.`rank` as apc_rank,
        apc.`is_committee` as apc_is_committee,
        apc.`is_high_rank_committee` as apc_is_high_rank_committee,
        apc.`status` as apc_status,
        apc.`created_by` as apc_created_by,
        apc.`created_date` as apc_created_date,
        apc.`modified_by` as apc_modified_by,
        apc.`modified_date` as apc_modified_date
    </sql>

    <sql id="vals">
        #{code},
        #{name},
        #{rank},
        #{isCommittee},
        #{isHighRankCommittee},
        #{status},
        #{createdDate},
        #{createdBy},
        #{modifiedDate},
        #{modifiedBy}
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.AdmPositionCommittee" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="com.eroses.external.society.model.AdmPositionCommittee">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="code != null">`code` = #{code},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="rank != null">`rank` = #{rank},</if>
            <if test="isCommittee != null">`is_committee` = #{isCommittee},</if>
            <if test="isHighRankCommittee != null">`is_high_rank_committee` = #{isHighRankCommittee},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            modified_date = NOW()
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="findAll" resultType="list" resultMap="AdmPositionCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE status != '-1' AND status != '0'
    </select>

    <select id="findById" parameterType="long" resultMap="AdmPositionCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE status != '-1' AND status != '0'
        AND id = #{id}
    </select>

    <select id="paging" parameterType="map" resultMap="AdmPositionCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE status != '-1' AND status != '0'
        LIMIT #{offset}, #{limit}
    </select>
</mapper>