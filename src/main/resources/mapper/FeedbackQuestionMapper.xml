<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eroses.external.society.mappers.FeedbackQuestionDao">

    <resultMap id="FeedbackQuestionMap" type="com.eroses.external.society.model.FeedbackQuestion">
        <id property="id" column="id"/>
        <result property="question" column="question"/>
        <result property="published" column="published"/>
        <result property="createdBy" column="created_by" />
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        feedback_question
    </sql>

    <sql id="cols">
        question,
        published,
        created_by,
        modified_by
    </sql>

    <sql id="getCols">
        id,
        question,
        published,
        created_date,
        modified_by,
        created_by,
        modified_date
    </sql>

    <sql id="vals">
        #{question},
        #{published},
        #{createdBy},
        #{modifiedBy}
    </sql>


    <insert id="create" parameterType="com.eroses.external.society.model.FeedbackQuestion" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>, created_date)
        VALUES (<include refid="vals"/>, NOW())
    </insert>


    <select id="findAll" resultType="list" resultMap="FeedbackQuestionMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
    </select>
    <select id="findById" resultType="list" resultMap="FeedbackQuestionMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        id = #{id}
    </select>

    <select id="findByIds" parameterType="list" resultMap="FeedbackQuestionMap">
        SELECT <include refid="getCols"/>
        FROM <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <update id="update" parameterType="com.eroses.external.society.model.FeedbackQuestion">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="question != null">question = #{question},</if>
            <if test="published != null">published = #{published},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date = now()
        </set>
        WHERE
        id = #{id}
    </update>
</mapper>
