<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="PrincipalSecretaryRoApproval">
    <resultMap id="PrincipalSecretaryRoApprovalMap" type="com.eroses.external.society.model.PrincipalSecretaryRoApproval">
        <id property="id" column="id" />
        <result column="secretaryId" property="secretaryId"/>
        <result column="result" property="result"/>
        <result column="notes" property="notes"/>
        <result column="aprrovedBy" property="aprrovedBy"/>
        <result column="approvalDate" property="approvalDate"/>
        <result column="migrateStat" property="migrateStat"/>
        <result column="createdBy" property="createdBy"/>
        <result column="createdDate" property="createdDate"/>
        <result column="modifiedBy" property="modifiedBy"/>
        <result column="modifiedDate" property="modifiedDate"/>
    </resultMap>


    <sql id="tb">
        `ro_change_secretary`
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="valsWithId">
        `id`, <include refid="vals"/>
    </sql>

    <sql id="cols">
        `secretaryId`,
        `result`,
        `notes`,
        `aprrovedBy`,
        `approvalDate`,
        `migrateStat`,
        `createdBy`,
        `createdDate`,
        `modifiedBy`,
        `modifiedDate`
    </sql>

    <sql id="vals">
        #{secretaryId},
        #{result},
        #{notes},
        #{aprrovedBy},
        #{approvalDate},
        #{migrateStat},
        #{createdBy},
        now(),
        #{modifiedBy},
        now()
    </sql>

    <update id="updatePrincipalSecretaryRoApproval" parameterType="com.eroses.external.society.model.PrincipalSecretaryRoApproval">
        UPDATE
        <include refid="tb"/>
        SET
        `aprrovedBy` = #{aprrovedBy},
        `notes` = #{notes},
        `modifiedBy` = #{modifiedBy},
        `modifiedDate` = now()
        WHERE `id` = #{id}
    </update>



    <select id="getAll" resultType="list" resultMap="PrincipalSecretaryRoApprovalMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE
        status != '-1'
    </select>

    <select id="search" parameterType="map" resultMap="PrincipalSecretaryRoApprovalMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="societyId != null">
                AND societyId = #{societyId}
            </if>
        </where>
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="existsById" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM <include refid="tb"/>
        WHERE `id` = #{id}
    </select>



</mapper>
