<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="TrainingCourse">

    <resultMap id="TrainingCourseMap" type="com.eroses.external.society.model.TrainingCourse">
        <id property="id" column="id" />
        <result property="title" column="title" />
        <result property="description" column="description" />
        <result property="difficultyLevel" column="difficulty_level" />
        <result property="status" column="status" />
        <result property="passingCriteria" column="passing_criteria" />
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date" />
        <result property="modifiedBy" column="modified_by" />
        <result property="modifiedDate" column="modified_date" />
        <result property="duration" column="duration" />
        <result property="explanation" column="explanation" />
        <result property="objective" column="objective" />
        <result property="poster" column="poster" />
        <!--<collection property="materials" >
            <id property="id" column="material_id" />
            <result property="materialType" column="material_type" />
            <result property="contentPath" column="content_path" />
            <result property="contentText" column="content_text" />
            <result property="sequenceOrder" column="material_sequence_order" />
            <result property="title" column="material_title" />
            <result property="description" column="material_description" />
            <result property="duration" column="material_duration" />
            <result property="youtubeLink" column="youtube_link" />
            <result property="media" column="media" />
        </collection>
        <result property="quizId" column="quiz_id" />
        <result property="title" column="quiz_title" />
        <result property="description" column="quiz_description" />
        <result property="timeLimitMinutes" column="time_limit_minutes" />
        <result property="isMandatory" column="is_mandatory" />
        <result property="minScore" column="min_score" />
        <result property="quizNo" column="quiz_no" />
        <collection property="questions">
            <id property="id" column="question_id" />
            <result property="questionText" column="question_text" />
            <result property="questionType" column="question_type" />
            <result property="sequenceOrder" column="question_sequence_order" />
            <collection property="options">
                <id property="id" column="option_id" />
                <result property="optionText" column="option_text" />
                <result property="isCorrect" column="is_correct" />
                <result property="sequenceOrder" column="option_sequence_order" />
            </collection>
        </collection>-->
    </resultMap>

    <sql id="tb">
        training_course
    </sql>

    <sql id="cols">
        title,
        description,
        difficulty_level,
        status,
        passing_criteria,
        created_by,
        created_date,
        modified_by,
        modified_date,
        duration,
        explanation,
        objective
    </sql>

    <sql id="colsWithId">
        id, <include refid="cols" />
    </sql>

    <sql id="colsJoin">
        a.title as title,
        a.description as description,
        a.difficulty_level as difficulty_level,
        a.status as status,
        a.passing_criteria as passing_criteria,
        a.created_by as created_by,
        a.created_date as created_date,
        a.modified_by as modified_by,
        a.modified_date as modified_date,
        a.duration as duration,
        a.explanation as explanation,
        a.objective as objective
    </sql>

    <sql id="colsWithIdJoin">
        a.id, <include refid="colsJoin" />
    </sql>
  
    <!-- Create -->
    <insert id="create" parameterType="com.eroses.external.society.model.TrainingCourse" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb" /> (
            title,
            description,
            difficulty_level,
            status,
            passing_criteria,
            duration,
            explanation,
            objective,
            created_by,
            created_date
        ) VALUES (
            #{title},
            #{description},
            #{difficultyLevel},
            #{status},
            #{passingCriteria},
            #{duration},
            #{explanation},
            #{objective},
            #{createdBy},
            NOW()
        )
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.TrainingCourse">
        UPDATE <include refid="tb" />
        SET
            title = #{title},
            description = #{description},
            difficulty_level = #{difficultyLevel},
            status = #{status},
            passing_criteria = #{passingCriteria},
            duration = #{duration},
            explanation = #{explanation},
            objective = #{objective},
            modified_by = #{modifiedBy},
            modified_date = NOW()
        WHERE id = #{id}
    </update>

    <!-- Update Status -->
    <update id="updateStatus">
        UPDATE <include refid="tb" />
        SET
            status = #{status},
            modified_by = #{modifiedBy},
            modified_date = NOW()
        WHERE id = #{id}
    </update>

    <!-- Find By ID -->
    <select id="findById" resultMap="TrainingCourseMap">
        SELECT <include refid="colsWithIdJoin" />, b.url as poster
        FROM <include refid="tb" /> as a
        LEFT JOIN document as b ON a.id = b.training_id
        WHERE a.id = #{id}
    </select>

    <!-- Find By ID -->
    <select id="findByIdWithMaterials" resultMap="TrainingCourseMap">
        SELECT <include refid="colsWithIdJoin" />, b.url as poster, d.url as c.media
        FROM <include refid="tb" /> as a
        LEFT JOIN document as b ON a.id = b.training_id
        LEFT JOIN training_material as c ON a.id = c.training_course_id
        LEFT JOIN document as d ON c.id = b.training_material_id
        WHERE a.id = #{id}
    </select>

    <!-- Find All -->
    <select id="findAll" resultMap="TrainingCourseMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        ORDER BY created_date DESC
    </select>

    <!-- Find All Published -->
    <select id="findAllPublished" resultMap="TrainingCourseMap">
        SELECT <include refid="colsWithIdJoin" />, b.url as poster
        FROM <include refid="tb" /> as a
        LEFT JOIN document as b ON a.id = b.training_id
        WHERE a.status = 1
        ORDER BY a.created_date DESC
    </select>

    <!-- Find All By Status -->
    <select id="findAllByStatus" resultMap="TrainingCourseMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE status = #{status}
        ORDER BY created_date DESC
    </select>

    <!-- Find All By Creator -->
    <select id="findAllByCreator" resultMap="TrainingCourseMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE created_by = #{createdBy}
        ORDER BY created_date DESC
    </select>
    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM <include refid="tb"/>
        WHERE id = #{id}
    </delete>
</mapper>
