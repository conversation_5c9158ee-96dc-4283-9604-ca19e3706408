<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ForbiddenLogo">
    <resultMap id="ForbiddenLogoMap" type="com.eroses.external.society.model.ForbiddenLogo">
        <id property="id" column="id"/>
        <result property="logoUrl" column="logo_url"/>
        <result property="remarks" column="remarks"/>
        <result property="active" column="active"/>
        <result property="activeRemarks" column="active_remarks"/>
        <result property="status" column="status"/>
        <result property="logoVector" column="logo_vector"/>
        <result property="createdDate" column="created_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>

    <sql id="tb">
        `forbidden_logo`
    </sql>

    <sql id="cols">
        `id`, `logo_url`, `remarks`, `active`,
        `active_remarks`, `status`, `logo_vector`, `created_date`, `created_by`,
        `modified_by`, `modified_date`
    </sql>

    <sql id="insertCols">
        `logo_url`, `remarks`, `active`,
        `active_remarks`, `status`, `logo_vector`, `created_by`, `modified_by`
    </sql>

    <select id="findAllLaranganLogo" resultMap="ForbiddenLogoMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `status` = '11'
        ORDER BY `created_date` DESC
    </select>

    <insert id="createLaranganLogo" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.eroses.external.society.model.ForbiddenLogo">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="insertCols"/>)
        VALUES (#{logoUrl}, #{remarks}, #{active},
        #{activeRemarks}, #{status}, #{logoVector}, #{createdBy}, #{modifiedBy})
    </insert>

    <select id="findLaranganLogoById" resultMap="ForbiddenLogoMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
    </select>

    <select id="findAllLaranganLogoImageOnly" resultType="java.lang.String">
        SELECT `logo_url` FROM
        <include refid="tb"/>
        WHERE `status` = '11'
        AND `active` = 1
    </select>

    <select id="findAllLaranganLogoVectorOnly" resultType="java.lang.String">
            SELECT `logo_vector` FROM
            <include refid="tb"/>
            WHERE `status` = '11'
    </select>

    <select id="searchLaranganLogo" resultMap="ForbiddenLogoMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="catatan != null and catatan != ''">
            AND `remarks` LIKE CONCAT('%', #{catatan}, '%')
        </if>
        <if test="activeStatus != null">
            AND `active` = #{activeStatus}
        </if>
        AND `status` = '11'
        ORDER BY `created_date` DESC
        <if test="offset != null and limit != null">
            LIMIT #{limit} OFFSET #{offset}
        </if>
    </select>
    <select id="countSearchLaranganLogo" resultType="Long">
        SELECT COUNT(*) FROM
        <include refid="tb"/>
        WHERE 1=1
        AND `status` = '11'
        <if test="catatan != null and catatan != ''">
            AND `remarks` LIKE CONCAT('%', #{catatan}, '%')
        </if>
        <if test="activeStatus != null">
            AND `active` = #{activeStatus}
        </if>
    </select>

    <update id="updateLaranganLogo" parameterType="com.eroses.external.society.model.ForbiddenLogo">
        UPDATE
        <include refid="tb"/>
        SET `logo_url` = #{logoUrl}, `remarks` = #{remarks},`active` = #{active},
        `active_remarks` = #{activeRemarks}, `status` = #{status},
        `modified_by` = #{modifiedBy}, `modified_date` = NOW()
        WHERE `id` = #{id}
    </update>

</mapper>