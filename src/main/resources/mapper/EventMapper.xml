<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eroses.external.society.mappers.EventDao">

    <resultMap id="EventMap" type="com.eroses.external.society.model.Event">
        <id property="id" column="id"/>
        <result property="eventName" column="event_name"/>
        <result property="eventAdminId" column="event_admin_id"/>
        <result property="hasMax" column="has_max"/>
        <result property="published" column="published"/>
        <result property="status" column="status"/>
        <result property="regStartDate" column="reg_start_date"/>
        <result property="regEndDate" column="reg_end_date"/>
        <result property="maxParticipants" column="max_participants"/>
        <result property="description" column="description"/>
        <result property="eventNo" column="event_no"/>
        <result property="address1" column="address_1"/>
        <result property="address2" column="address_2"/>
        <result property="state" column="state"/>
        <result property="postcode" column="postcode"/>
        <result property="eventStartDate" column="event_start_date"/>
        <result property="eventEndDate" column="event_end_date"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="venue" column="venue"/>
        <result property="organiserId" column="organiser_id"/>
        <result property="collaboratorName" column="collaborator_name"/>
        <result property="mapUrl" column="map_url"/>
        <result property="feedbackName" column="feedback_name"/>
        <result property="organisationLevel" column="organisation_level"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="position" column="position"/>
        <result property="visibility" column="visibility"/>
        <result property="organisationCategory" column="organisation_category"/>
        <result property="bannerUrl" column="banner_url"/>
        <result property="picContactNo" column="pic_contact_no"/>
        <result property="stateAddress" column="state_address"/>
        <result property="districtAddress" column="district_address"/>
        <result property="cityAddress" column="city_address"/>
        <result property="postcodeAddress" column="postcode_address"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        event
    </sql>

    <sql id="cols">
        visibility,
        event_name,
        event_admin_id,
        has_max,
        published,
        status,
        reg_start_date,
        reg_end_date,
        max_participants,
        description,
        address_1,
        address_2,
        state,
        postcode,
        event_start_date,
        event_end_date,
        event_no,
        start_time,
        end_time,
        venue,
        organiser_id,
        collaborator_name,
        map_url,
        feedback_name,
        organisation_level,
        city,
        district,
        position,
        organisation_category,
        banner_url,
        pic_contact_no,
        created_by,
        state_address,
        district_address,
        city_address,
        postcode_address
    </sql>

    <sql id="getCols">
        id,
        visibility,
        event_name,
        event_admin_id,
        has_max,
        published,
        status,
        reg_start_date,
        reg_end_date,
        max_participants,
        description,
        address_1,
        address_2,
        state,
        postcode,
        event_start_date,
        event_end_date,
        created_date,
        modified_by,
        created_by,
        event_no,
        start_time,
        end_time,
        venue,
        organiser_id,
        collaborator_name,
        map_url,
        feedback_name,
        organisation_level,
        city,
        district,
        position,
        organisation_category,
        banner_url,
        pic_contact_no,
        state_address,
        district_address,
        city_address,
        postcode_address
    </sql>

    <sql id="vals">
        #{visibility},
        #{eventName},
        #{eventAdminId},
        #{hasMax},
        #{published},
        #{status},
        #{regStartDate},
        #{regEndDate},
        #{maxParticipants},
        #{description},
        #{address1},
        #{address2},
        #{state},
        #{postcode},
        #{eventStartDate},
        #{eventEndDate},
        #{eventNo},
        #{startTime},
        #{endTime},
        #{venue},
        #{organiserId},
        #{collaboratorName},
        #{mapUrl},
        #{feedbackName},
        #{organisationLevel},
        #{city},
        #{district},
        #{position},
        #{organisationCategory},
        #{bannerUrl},
        #{picContactNo},
        #{createdBy},
        #{stateAddress},
        #{districtAddress},
        #{cityAddress},
        #{postcodeAddress}
    </sql>


    <insert id="create" parameterType="com.eroses.external.society.model.Event" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>, created_date)
        VALUES (<include refid="vals"/>, NOW())
    </insert>

    <select id="findAll" resultType="list" resultMap="EventMap">
        <!--        SELECT * FROM event_admin-->
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE YEAR(event_start_date) = #{year}
        AND event_start_date >= CURRENT_DATE()
        AND status = '11'
        ORDER BY event_start_date DESC
    </select>

    <select id="findById" resultType="list" resultMap="EventMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        id = #{id}
    </select>

    <select id="findByEventNo" resultType="list" resultMap="EventMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        event_no = #{eventNo}
        AND status = '11'
    </select>

    <select id="findIdByEventNo" resultType="java.lang.Long">
        SELECT
        id
        FROM
        <include refid="tb"/>
        WHERE
        event_no = #{eventNo}
    </select>

    <select id="findAllPublished" resultType="list" resultMap="EventMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE published = true
        AND event_start_date >= CURRENT_DATE()
        AND YEAR(event_start_date) = #{year}
        AND status = '11'
        ORDER BY event_start_date ASC
    </select>

    <select id="findPastEvents" resultMap="EventMap">
        SELECT *
        FROM event e
        WHERE e.published = true
        AND e.event_start_date &lt; CURRENT_DATE()
        AND YEAR(e.event_start_date) = #{year}
        AND e.status = '11'
        ORDER BY e.event_start_date DESC
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.Event">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="visibility != null">visibility = #{visibility},</if>
            <if test="eventName != null">event_name = #{eventName},</if>
            <if test="hasMax != null">has_max = #{hasMax},</if>
            <if test="published != null">published = #{published},</if>
            <if test="status != null">status = #{status},</if>
            <if test="regStartDate != null">reg_start_date = #{regStartDate},</if>
            <if test="regEndDate != null">reg_end_date = #{regEndDate},</if>
            <if test="maxParticipants != null">max_participants = #{maxParticipants},</if>
            <if test="description != null">description = #{description},</if>
            <if test="address1 != null">address_1 = #{address1},</if>
            <if test="address2 != null">address_2 = #{address2},</if>
            <if test="state != null">state = #{state},</if>
            <if test="postcode != null">postcode = #{postcode},</if>
            <if test="eventStartDate != null">event_start_date = #{eventStartDate},</if>
            <if test="eventEndDate != null">event_end_date = #{eventEndDate},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="venue != null">venue = #{venue},</if>
            <if test="organiserId != null">organiser_id = #{organiserId},</if>
            <if test="collaboratorName != null">collaborator_name = #{collaboratorName},</if>
            <if test="mapUrl != null">map_url = #{mapUrl},</if>
            <if test="feedbackName != null">feedback_name = #{feedbackName},</if>
            <if test="organisationLevel != null">organisation_level = #{organisationLevel},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="position != null">position = #{position},</if>
            <if test="organisationCategory != null">organisation_category = #{organisationCategory},</if>
            <if test="bannerUrl != null">banner_url = #{bannerUrl},</if>
            <if test="picContactNo != null">pic_contact_no = #{picContactNo},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            <if test="stateAddress != null">state_address = #{stateAddress},</if>
            <if test="districtAddress != null">district_address = #{districtAddress},</if>
            <if test="cityAddress != null">city_address = #{cityAddress},</if>
            <if test="postcodeAddress != null">postcode_address = #{postcodeAddress},</if>
            modified_date = now()
        </set>
        WHERE
        id = #{id}
    </update>

    <!-- Fetch the next sequence number for the current month -->
    <select id="getNextSequence" parameterType="string" resultType="int">
        SELECT next_number FROM event_sequence es WHERE es.year_month = #{yearMonth}
    </select>

    <!-- Insert a new sequence record if not exists -->
    <insert id="insertSequence" parameterType="string">
        INSERT INTO event_sequence (`year_month`, next_number) VALUES (#{yearMonth}, 1)
    </insert>

    <!-- Update the next sequence number -->
    <update id="updateSequence" parameterType="string">
        UPDATE event_sequence SET next_number = next_number + 1 WHERE `year_month` = #{yearMonth}
    </update>

    <delete id="delete" parameterType="java.lang.Long">
        UPDATE
        <include refid="tb"/>
        SET status = '-1',
        WHERE id = #{id}
    </delete>
</mapper>
