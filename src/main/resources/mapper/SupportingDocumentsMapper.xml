<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.eroses.external.society.mappers.SupportingDocumentsDao">
    <resultMap id="SupportingDocsMap" type="com.eroses.external.society.model.Document">
        <result property="id" column="id"/>
        <result property="documentName" column="name"/>
        <result property="remarks" column="note"/>
    </resultMap>

    <select id="findByBranchId" resultMap="SupportingDocsMap">
        SELECT id, name, note
        FROM document
        WHERE branch_id = #{branchId}
    </select>
</mapper>