<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="TrainingQuiz">

    <resultMap id="TrainingQuizMap" type="com.eroses.external.society.model.TrainingQuiz">
        <id property="id" column="id" />
        <result property="trainingCourseId" column="training_course_id" />
        <result property="title" column="title" />
        <result property="description" column="description" />
        <result property="timeLimitMinutes" column="time_limit_minutes" />
        <result property="isMandatory" column="is_mandatory" />
        <result property="minScore" column="min_score" />
        <result property="quizNo" column="quiz_no" />
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date" />
    </resultMap>

    <sql id="tb">
        training_quiz
    </sql>

    <sql id="cols">
        training_course_id,
        title,
        description,
        time_limit_minutes,
        is_mandatory,
        min_score,
        quiz_no,
        created_by,
        created_date
    </sql>

    <sql id="colsWithId">
        id, <include refid="cols" />
    </sql>

    <!-- Create -->
    <insert id="create" parameterType="com.eroses.external.society.model.TrainingQuiz" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb" /> (
            training_course_id,
            title,
            description,
            time_limit_minutes,
            is_mandatory,
            min_score,
            quiz_no,
            created_by,
            created_date
        ) VALUES (
            #{trainingCourseId},
            #{title},
            #{description},
            #{timeLimitMinutes},
            #{isMandatory},
            #{minScore},
            #{quizNo},
            #{createdBy},
            NOW()
        )
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.TrainingQuiz">
        UPDATE <include refid="tb" />
        SET
            title = #{title},
            description = #{description},
            time_limit_minutes = #{timeLimitMinutes},
            is_mandatory = #{isMandatory},
            min_score = #{minScore},
            quiz_no = #{quizNo}
        WHERE id = #{id}
    </update>

    <!-- Find By ID -->
    <select id="findById" resultMap="TrainingQuizMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE id = #{id}
    </select>

    <!-- Find By Course ID -->
    <select id="findByCourseId" resultMap="TrainingQuizMap">
        SELECT <include refid="colsWithId" />
        FROM <include refid="tb" />
        WHERE training_course_id = #{trainingCourseId}
    </select>

    <!-- Delete -->
    <delete id="delete">
        DELETE FROM <include refid="tb" />
        WHERE id = #{id}
    </delete>

</mapper>
