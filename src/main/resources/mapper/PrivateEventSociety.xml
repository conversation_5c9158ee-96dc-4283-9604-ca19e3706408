<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eroses.external.society.mappers.PrivateEventSocietyDao">

    <resultMap id="PrivateEventSocietyMap" type="com.eroses.external.society.model.PrivateEventSociety">
        <id property="id" column="id"/>
        <result property="eventId" column="event_id"/>
        <result property="societyId" column="society_id"/>
        <result property="isManualSelection" column="is_manual_selection"/>
        <result property="createdBy" column="created_by" />
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        private_event_society
    </sql>

    <sql id="cols">
        event_id,
        society_id,
        is_manual_selection,
        created_by,
        modified_by
    </sql>

    <sql id="getCols">
        id,
        event_id,
        society_id,
        is_manual_selection,
        created_date,
        modified_by,
        created_by,
        modified_date
    </sql>

    <sql id="vals">
        #{eventId},
        #{societyId},
        #{isManualSelection},
        #{createdBy},
        #{modifiedBy}
    </sql>

    <sql id="createVals">
        #{item.eventId},
        #{item.societyId},
        #{item.isManualSelection},
        #{item.createdBy},
        #{item.modifiedBy}
    </sql>


    <insert id="create" parameterType="com.eroses.external.society.model.PrivateEventSociety" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>, created_date)
        VALUES (<include refid="vals"/>, NOW())
    </insert>
    <insert id="createMultiples" parameterType="java.util.List" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb"/>
        (<include refid="cols"/>, created_date)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (<include refid="createVals"/>, NOW())
        </foreach>
    </insert>

    <delete id="deleteBySocietyIdAndEventId">
        DELETE FROM <include refid="tb"/>
        WHERE (event_id, society_id) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.eventId}, #{item.societyId})
        </foreach>
    </delete>

    <delete id="deleteByEventIdAndManualSelection" >
        DELETE FROM <include refid="tb"/>
        WHERE event_id = #{eventId}
        AND is_manual_selection = #{manualSelection}
    </delete>



<!--    <select id="findByEventIdAndIdentificationNo" resultType="list" resultMap="EventAttendeesMap">-->
<!--        SELECT-->
<!--        <include refid="getCols"/>-->
<!--        FROM-->
<!--        <include refid="tb"/>-->
<!--        WHERE-->
<!--        identification_no = #{identificationNo}-->
<!--        AND-->
<!--        event_id = #{eventId}-->
<!--    </select>-->

    <select id="findByEventId" resultType="list" resultMap="PrivateEventSocietyMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        event_id = #{eventId}
    </select>

<!--    <select id="findByIdentificationNo" resultType="list" resultMap="EventAttendeesMap">-->
<!--        SELECT-->
<!--        <include refid="getCols"/>-->
<!--        FROM-->
<!--        <include refid="tb"/>-->
<!--        WHERE-->
<!--        identification_no = #{identificationNo}-->
<!--    </select>-->

<!--    <select id="findAll" resultType="list" resultMap="EventAttendeesMap">-->
<!--        SELECT-->
<!--        <include refid="getCols"/>-->
<!--        FROM-->
<!--        <include refid="tb"/>-->
<!--    </select>-->

    <select id="findById" resultType="list" resultMap="PrivateEventSocietyMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        id = #{id}
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.EventAttendees">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="eventId != null">event_id = #{eventId},</if>
            <if test="societyId != null">society_id = #{societyId},</if>
            <if test="isManualSelection != null">is_manual_selection = #{isManualSelection},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date = now()
        </set>
        WHERE
        id = #{id}
    </update>

    <delete id="deleteByEventId" parameterType="java.lang.Long">
        DELETE FROM <include refid="tb"/>
        WHERE event_id = #{eventId}
    </delete>
</mapper>
