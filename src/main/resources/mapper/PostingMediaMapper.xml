<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="PostingMedia">
    <sql id="tb">posting_media</sql>
    <resultMap id="PostingMediaMap" type="com.eroses.external.society.model.posting.Posting">
        <id property="id" column="id" />
        <result property="postingId" column="posting_id" />
        <result property="mediaType" column="media_type" />
        <result property="mediaUrl" column="media_url" />
        <result property="sequenceOrder" column="sequence_order" />
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date" />
    </resultMap>
    <sql id="cols">
        posting_id,
        media_type,
        media_url,
        sequence_order,
        created_by,
        created_date
    </sql>

    <sql id="colsWithId">
        id,
        <include refid="cols"/>
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.posting.PostingMedia" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
            posting_id,
            media_type,
            media_url,
            sequence_order,
            created_by,
            created_date
        )
        VALUES (
            #{postingId},
            #{mediaType},
            #{mediaUrl},
            #{sequenceOrder},
            #{createdBy},
            #{createdDate}
        )
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.posting.PostingMedia">
        UPDATE
        <include refid="tb"/>
        SET
            media_type = #{mediaType},
            media_url = #{mediaUrl},
            sequence_order = #{sequenceOrder}
        WHERE
            id = #{id}
    </update>

    <!-- Delete -->
    <delete id="delete">
        DELETE FROM
        <include refid="tb"/>
        WHERE
            id = #{id}
    </delete>

    <!-- Delete By Posting ID -->
    <delete id="deleteByPostingId">
        DELETE FROM
        <include refid="tb"/>
        WHERE
            posting_id = #{postingId}
    </delete>

    <!-- Find By ID -->
    <select id="findById" resultType="com.eroses.external.society.model.posting.PostingMedia">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            id = #{id}
    </select>

    <!-- Find By Posting ID -->
    <select id="findByPostingId" resultType="com.eroses.external.society.model.posting.PostingMedia">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
            posting_id = #{postingId}
        ORDER BY
            sequence_order ASC
    </select>
</mapper>
