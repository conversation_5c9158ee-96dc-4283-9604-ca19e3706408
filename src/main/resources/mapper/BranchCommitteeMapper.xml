<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="BranchCommittee">
    <resultMap id="BranchCommitteeMap" type="com.eroses.external.society.model.BranchCommittee">
        <id property="id" column="id" />
        <result column="committee_table_old_id" property="committeeTableOldId"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="designation_code" property="designationCode"/>
        <result column="position_arrangement" property="positionArrangement"/>
        <result column="title_code" property="titleCode"/>
        <result column="committee_name" property="committeeName"/>
        <result column="gender" property="gender"/>
        <result column="citizenship_status" property="citizenshipStatus"/>
        <result column="identity_type" property="identityType"/>
        <result column="committee_ic_no" property="committeeIcNo"/>
        <result column="date_of_birth" property="dateOfBirth"/>
        <result column="place_of_birth" property="placeOfBirth"/>
        <result column="job_code" property="jobCode"/>
        <result column="committee_address_status" property="committeeAddressStatus"/>
        <result column="committee_address" property="committeeAddress"/>
        <result column="committee_country_code" property="committeeCountryCode"/>
        <result column="committee_state_code" property="committeeStateCode"/>
        <result column="committee_district" property="committeeDistrict"/>
        <result column="committee_small_district" property="committeeSmallDistrict"/>
        <result column="committee_city" property="committeeCity"/>
        <result column="postcode" property="postcode"/>
        <result column="email" property="email"/>
        <result column="home_phone_number" property="homePhoneNumber"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="office_phone_number" property="officePhoneNumber"/>
        <result column="committee_employer_name" property="committeeEmployerName"/>
        <result column="committee_employer_address_status" property="committeeEmployerAddressStatus"/>
        <result column="committee_employer_address" property="committeeEmployerAddress"/>
        <result column="committee_employer_country_code" property="committeeEmployerCountryCode"/>
        <result column="committee_employer_state_code" property="committeeEmployerStateCode"/>
        <result column="committee_employer_district" property="committeeEmployerDistrict"/>
        <result column="committee_employer_city" property="committeeEmployerCity"/>
        <result column="committee_employer_postcode" property="committeeEmployerPostcode"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="status" property="status"/>
        <result column="batal_flat" property="batalFlat"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="peg_harta" property="pegHarta"/>
        <result column="other_position" property="otherPosition"/>
        <result column="tarikh_tukar_su" property="tarikhTukarSu"/>
        <result column="id_su" property="idSu"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="document_id" property="documentId"/>
        <result column="document_type" property="documentType"/>
        <result column="appointed_date" property="appointedDate"/>
    </resultMap>

    <resultMap id="ActiveSecretaryMap" type="com.eroses.external.society.dto.response.branchCommittee.ActiveBranchSecretaryResponse">
        <id property="id" column="id" />
        <result column="branch_no" property="branchNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="committee_ic_no" property="committeeIcNo"/>
        <result column="committee_name" property="committeeName"/>
    </resultMap>

    <resultMap id="BranchCommitteeBasicMap" type="com.eroses.external.society.dto.response.branchCommittee.BranchCommitteeBasicResponse">
        <id property="id" column="id" />
        <result column="committee_name" property="name"/>
        <result column="email" property="email"/>
        <result column="committee_state_code" property="stateCode"/>
        <result column="designation_code" property="designationCode"/>
    </resultMap>

    <resultMap id="BranchCommitteeDetailMap" type="com.eroses.external.society.dto.response.branchCommittee.BranchCommitteeDetailResponse">
        <id column="branch_committee_id" property="branchCommitteeId" />
        <result column="designation_code" property="designationCode"/>
        <result column="identity_type" property="identityType"/>
        <result column="committee_ic_no" property="committeeIcNo"/>
        <result column="title_code" property="titleCode"/>
        <result column="committee_name" property="committeeName"/>
        <result column="gender" property="gender"/>
        <result column="date_of_birth" property="dateOfBirth"/>
        <result column="place_of_birth" property="placeOfBirth"/>
        <result column="job_code" property="jobCode"/>
        <result column="committee_address" property="committeeAddress"/>
        <result column="committee_state_code" property="committeeStateCode"/>
        <result column="committee_district" property="committeeDistrict"/>
        <result column="postcode" property="postcode"/>
        <result column="email" property="email"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="home_phone_number" property="homePhoneNumber"/>
        <result column="office_phone_number" property="officePhoneNumber"/>
        <result column="committee_employer_name" property="committeeEmployerName"/>
        <result column="committee_employer_address" property="committeeEmployerAddress"/>
        <result column="committee_employer_country_code" property="committeeEmployerCountryCode"/>
    </resultMap>

    <resultMap id="BranchCommitteeSelectMap" type="com.eroses.external.society.model.BranchCommittee">
        <id property="id" column="bc_id" />
        <result column="bc_branch_id" property="branchId"/>
        <result column="bc_branch_no" property="branchNo"/>
        <result column="bc_designation_code" property="designationCode"/>
        <result column="bc_title_code" property="titleCode"/>
        <result column="bc_committee_name" property="committeeName"/>
        <result column="bc_gender" property="gender"/>
        <result column="bc_citizenship_status" property="citizenshipStatus"/>
        <result column="bc_identity_type" property="identityType"/>
        <result column="bc_committee_ic_no" property="committeeIcNo"/>
        <result column="bc_date_of_birth" property="dateOfBirth"/>
        <result column="bc_place_of_birth" property="placeOfBirth"/>
        <result column="bc_job_code" property="jobCode"/>
        <result column="bc_committee_address_status" property="committeeAddressStatus"/>
        <result column="bc_committee_address" property="committeeAddress"/>
        <result column="bc_committee_country_code" property="committeeCountryCode"/>
        <result column="bc_committee_state_code" property="committeeStateCode"/>
        <result column="bc_committee_district" property="committeeDistrict"/>
        <result column="bc_committee_small_district" property="committeeSmallDistrict"/>
        <result column="bc_committee_city" property="committeeCity"/>
        <result column="bc_postcode" property="postcode"/>
        <result column="bc_email" property="email"/>
        <result column="bc_home_phone_number" property="homePhoneNumber"/>
        <result column="bc_phone_number" property="phoneNumber"/>
        <result column="bc_office_phone_number" property="officePhoneNumber"/>
        <result column="bc_committee_employer_name" property="committeeEmployerName"/>
        <result column="bc_committee_employer_address_status" property="committeeEmployerAddressStatus"/>
        <result column="bc_committee_employer_address" property="committeeEmployerAddress"/>
        <result column="bc_committee_employer_country_code" property="committeeEmployerCountryCode"/>
        <result column="bc_committee_employer_state_code" property="committeeEmployerStateCode"/>
        <result column="bc_committee_employer_district" property="committeeEmployerDistrict"/>
        <result column="bc_committee_employer_city" property="committeeEmployerCity"/>
        <result column="bc_committee_employer_postcode" property="committeeEmployerPostcode"/>
        <result column="bc_created_by" property="createdBy"/>
        <result column="bc_created_date" property="createdDate"/>
        <result column="bc_modified_by" property="modifiedBy"/>
        <result column="bc_modified_date" property="modifiedDate"/>
        <result column="bc_status" property="status"/>
        <result column="bc_batal_flat" property="batalFlat"/>
        <result column="bc_application_status_code" property="applicationStatusCode"/>
        <result column="bc_peg_harta" property="pegHarta"/>
        <result column="bc_other_position" property="otherPosition"/>
        <result column="bc_tarikh_tukar_su" property="tarikhTukarSu"/>
        <result column="bc_id_su" property="idSu"/>
    </resultMap>

    <sql id="tb">
        `branch_committee`
    </sql>

    <sql id="selectTb">
        `branch_committee` bc
    </sql>

    <sql id="colsWithoutId">
        `committee_table_old_id`,
        `branch_id`,
        `branch_no`,
        `designation_code`,
        `position_arrangement`,
        `title_code`,
        `committee_name`,
        `gender`,
        `citizenship_status`,
        `identity_type`,
        `committee_ic_no`,
        `date_of_birth`,
        `place_of_birth`,
        `job_code`,
        `committee_address_status`,
        `committee_address`,
        `committee_country_code`,
        `committee_state_code`,
        `committee_district`,
        `committee_small_district`,
        `committee_city`,
        `postcode`,
        `email`,
        `home_phone_number`,
        `phone_number`,
        `office_phone_number`,
        `committee_employer_name`,
        `committee_employer_address_status`,
        `committee_employer_address`,
        `committee_employer_country_code`,
        `committee_employer_state_code`,
        `committee_employer_district`,
        `committee_employer_city`,
        `committee_employer_postcode`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`,
        `status`,
        `batal_flat`,
        `application_status_code`,
        `peg_harta`,
        `other_position`,
        `tarikh_tukar_su`,
        `id_su`,
        `meeting_id`,
        `appointed_date`,
        `meeting_date`,
        `document_id`,
        `document_type`
    </sql>

    <sql id="cols">
        `id`,
        `committee_table_old_id`,
        `branch_id`,
        `branch_no`,
        `designation_code`,
        `position_arrangement`,
        `title_code`,
        `committee_name`,
        `gender`,
        `citizenship_status`,
        `identity_type`,
        `committee_ic_no`,
        `date_of_birth`,
        `place_of_birth`,
        `job_code`,
        `committee_address_status`,
        `committee_address`,
        `committee_country_code`,
        `committee_state_code`,
        `committee_district`,
        `committee_small_district`,
        `committee_city`,
        `postcode`,
        `email`,
        `home_phone_number`,
        `phone_number`,
        `office_phone_number`,
        `committee_employer_name`,
        `committee_employer_address_status`,
        `committee_employer_address`,
        `committee_employer_country_code`,
        `committee_employer_state_code`,
        `committee_employer_district`,
        `committee_employer_city`,
        `committee_employer_postcode`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`,
        `status`,
        `batal_flat`,
        `application_status_code`,
        `peg_harta`,
        `other_position`,
        `tarikh_tukar_su`,
        `id_su`,
        `meeting_id`,
        `appointed_date`,
        `meeting_date`,
        `document_id`,
        `document_type`
    </sql>

    <sql id="selectCols">
        bc.`id` as bc_id,
        bc.`committee_table_old_id` as bc_committee_table_old_id,
        bc.`branch_id` as bc_branch_id,
        bc.`branch_no` as bc_branch_no,
        bc.`designation_code` as bc_designation_code,
        bc.`position_arrangement` as bc_position_arrangement,
        bc.`title_code` as bc_title_code,
        bc.`committee_name` as bc_committee_name,
        bc.`gender` as bc_gender,
        bc.`citizenship_status` as bc_citizenship_status,
        bc.`identity_type` as bc_identity_type,
        bc.`committee_ic_no` as bc_committee_ic_no,
        bc.`date_of_birth` as bc_date_of_birth,
        bc.`place_of_birth` as bc_place_of_birth,
        bc.`job_code` as bc_job_code,
        bc.`committee_address_status` as bc_committee_address_status,
        bc.`committee_address` as bc_committee_address,
        bc.`committee_country_code` as bc_committee_country_code,
        bc.`committee_state_code` as bc_committee_state_code,
        bc.`committee_district` as bc_committee_district,
        bc.`committee_small_district` as bc_committee_small_district,
        bc.`committee_city` as bc_committee_city,
        bc.`postcode` as bc_postcode,
        bc.`email` as bc_email,
        bc.`home_phone_number` as bc_home_phone_number,
        bc.`phone_number` as bc_phone_number,
        bc.`office_phone_number` as bc_office_phone_number,
        bc.`committee_employer_name` as bc_committee_employer_name,
        bc.`committee_employer_address_status` as bc_committee_employer_address_status,
        bc.`committee_employer_address` as bc_committee_employer_address,
        bc.`committee_employer_country_code` as bc_committee_employer_country_code,
        bc.`committee_employer_state_code` as bc_committee_employer_state_code,
        bc.`committee_employer_district` as bc_committee_employer_district,
        bc.`committee_employer_city` as bc_committee_employer_city,
        bc.`committee_employer_postcode` as bc_committee_employer_postcode,
        bc.`created_by` as bc_created_by,
        bc.`created_date` as bc_created_date,
        bc.`modified_by` as bc_modified_by,
        bc.`modified_date` as bc_modified_date,
        bc.`status` as bc_status,
        bc.`batal_flat` as bc_batal_flat,
        bc.`application_status_code` as bc_application_status_code,
        bc.`peg_harta` as bc_peg_harta,
        bc.`other_position` as bc_other_position,
        bc.`tarikh_tukar_su` as bc_tarikh_tukar_su,
        bc.`id_su` as bc_id_su,
        bc.`meeting_id` as bc_meeting_id,
        bc.`appointed_date` as bc_appointed_date,
        bc.`meeting_date` as bc_meeting_date,
        bc.`document_id` as bc_document_id,
        bc.`document_type` as bc_document_type
    </sql>

    <sql id="colsTest">
        `committee_table_old_id`,
        `branch_id`,
        `branch_no`,
        `designation_code`,
        `position_arrangement`,
        `title_code`,
        `committee_name`,
        `gender`,
        `citizenship_status`,
        `identity_type`,
        `committee_ic_no`,
        `date_of_birth`,
        `place_of_birth`,
        `job_code`,
        `committee_address_status`,
        `committee_address`,
        `committee_country_code`,
        `committee_state_code`,
        `committee_district`,
        `committee_small_district`,
        `committee_city`,
        `postcode`,
        `email`,
        `home_phone_number`,
        `phone_number`,
        `office_phone_number`,
        `committee_employer_name`,
        `committee_employer_address_status`,
        `committee_employer_address`,
        `committee_employer_country_code`,
        `committee_employer_state_code`,
        `committee_employer_district`,
        `committee_employer_city`,
        `committee_employer_postcode`,
        `created_by`,
        `created_date`,
        `status`,
        `batal_flat`,
        `application_status_code`,
        `peg_harta`,
        `other_position`,
        `tarikh_tukar_su`,
        `id_su`,
        `meeting_id`,
        `appointed_date`,
        `meeting_date`,
        `document_id`,
        `document_type`
    </sql>

    <sql id="vals">
        #{committeeTableOldId},
        #{branchId},
        #{branchNo},
        #{designationCode},
        #{positionArrangement},
        #{titleCode},
        #{committeeName},
        #{gender},
        #{citizenshipStatus},
        #{identityType},
        #{committeeIcNo},
        #{dateOfBirth},
        #{placeOfBirth},
        #{jobCode},
        #{committeeAddressStatus},
        #{committeeAddress},
        #{committeeCountryCode},
        #{committeeStateCode},
        #{committeeDistrict},
        #{committeeSmallDistrict},
        #{committeeCity},
        #{postcode},
        #{email},
        #{homePhoneNumber},
        #{phoneNumber},
        #{officePhoneNumber},
        #{committeeEmployerName},
        #{committeeEmployerAddressStatus},
        #{committeeEmployerAddress},
        #{committeeEmployerCountryCode},
        #{committeeEmployerStateCode},
        #{committeeEmployerDistrict},
        #{committeeEmployerCity},
        #{committeeEmployerPostcode},
        #{createdBy},
        now(),
        #{modifiedBy},
        now(),
        #{status},
        #{batalFlat},
        #{applicationStatusCode},
        #{pegHarta},
        #{otherPosition},
        #{tarikhTukarSu},
        #{idSu},
        #{meetingId},
        #{appointedDate},
        #{meetingDate},
        #{documentId},
        #{documentType}
    </sql>

    <sql id="valsTest">
        #{committee.committeeTableOldId},
        #{committee.branchId},
        #{committee.branchNo},
        #{committee.designationCode},
        #{committee.positionArrangement},
        #{committee.titleCode},
        #{committee.committeeName},
        #{committee.gender},
        #{committee.citizenshipStatus},
        #{committee.identityType},
        #{committee.committeeIcNo},
        #{committee.dateOfBirth},
        #{committee.placeOfBirth},
        #{committee.jobCode},
        #{committee.committeeAddressStatus},
        #{committee.committeeAddress},
        #{committee.committeeCountryCode},
        #{committee.committeeStateCode},
        #{committee.committeeDistrict},
        #{committee.committeeSmallDistrict},
        #{committee.committeeCity},
        #{committee.postcode},
        #{committee.email},
        #{committee.homePhoneNumber},
        #{committee.phoneNumber},
        #{committee.officePhoneNumber},
        #{committee.committeeEmployerName},
        #{committee.committeeEmployerAddressStatus},
        #{committee.committeeEmployerAddress},
        #{committee.committeeEmployerCountryCode},
        #{committee.committeeEmployerStateCode},
        #{committee.committeeEmployerDistrict},
        #{committee.committeeEmployerCity},
        #{committee.committeeEmployerPostcode},
        #{committee.createdBy},
        now(),
        #{committee.status},
        #{committee.batalFlat},
        #{committee.applicationStatusCode},
        #{committee.pegHarta},
        #{committee.otherPosition},
        #{committee.tarikhTukarSu},
        #{committee.idSu},
        #{committee.meetingId},
        #{committee.appointedDate},
        #{committee.meetingDate},
        #{committee.documentId},
        #{committee.documentType}
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.BranchCommittee" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="colsWithoutId"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <!-- Insert -->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="colsTest"/>)
        VALUES
        <foreach item="committee" collection="list" separator=",">
            (<include refid="valsTest"/>)
        </foreach>
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.BranchCommittee">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="committeeTableOldId != null">committee_table_old_id = #{committeeTableOldId},</if>
            <if test="designationCode != null">designation_code = #{designationCode},</if>
            <if test="positionArrangement != null">position_arrangement = #{positionArrangement},</if>
            <if test="titleCode != null">title_code = #{titleCode},</if>
            <if test="committeeName != null">committee_name = #{committeeName},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="citizenshipStatus != null">citizenship_status = #{citizenshipStatus},</if>
            <if test="identityType != null">identity_type = #{identityType},</if>
            <if test="committeeIcNo != null">committee_ic_no = #{committeeIcNo},</if>
            <if test="dateOfBirth != null">date_of_birth = #{dateOfBirth},</if>
            <if test="placeOfBirth != null">place_of_birth = #{placeOfBirth},</if>
            <if test="jobCode != null">job_code = #{jobCode},</if>
            <if test="committeeAddressStatus != null">committee_address_status = #{committeeAddressStatus},</if>
            <if test="committeeAddress != null">committee_address = #{committeeAddress},</if>
            <if test="committeeCountryCode != null">committee_country_code = #{committeeCountryCode},</if>
            <if test="committeeStateCode != null">committee_state_code = #{committeeStateCode},</if>
            <if test="committeeDistrict != null">committee_district = #{committeeDistrict},</if>
            <if test="committeeSmallDistrict != null">committee_small_district = #{committeeSmallDistrict},</if>
            <if test="committeeCity != null">committee_city = #{committeeCity},</if>
            <if test="postcode != null">postcode = #{postcode},</if>
            <if test="email != null">email = #{email},</if>
            <if test="homePhoneNumber != null">home_phone_number = #{homePhoneNumber},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="officePhoneNumber != null">office_phone_number = #{officePhoneNumber},</if>
            <if test="committeeEmployerName != null">committee_employer_name = #{committeeEmployerName},</if>
            <if test="committeeEmployerAddressStatus != null">committee_employer_address_status = #{committeeEmployerAddressStatus},</if>
            <if test="committeeEmployerAddress != null">committee_employer_address = #{committeeEmployerAddress},</if>
            <if test="committeeEmployerCountryCode != null">committee_employer_country_code = #{committeeEmployerCountryCode},</if>
            <if test="committeeEmployerStateCode != null">committee_employer_state_code = #{committeeEmployerStateCode},</if>
            <if test="committeeEmployerDistrict != null">committee_employer_district = #{committeeEmployerDistrict},</if>
            <if test="committeeEmployerCity != null">committee_employer_city = #{committeeEmployerCity},</if>
            <if test="committeeEmployerPostcode != null">committee_employer_postcode = #{committeeEmployerPostcode},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            <if test="batalFlat != null">batal_flat = #{batalFlat},</if>
            <if test="applicationStatusCode != null">application_status_code = #{applicationStatusCode},</if>
            <if test="pegHarta != null">peg_harta = #{pegHarta},</if>
            <if test="otherPosition != null">other_position = #{otherPosition},</if>
            <if test="status != null">status = #{status},</if>
            <if test="meetingId != null">meeting_id = #{meetingId},</if>
            <if test="appointedDate != null">appointed_date = #{appointedDate},</if>
            <if test="meetingDate != null">meeting_date = #{meetingDate},</if>
            <if test="documentId != null">document_id = #{documentId},</if>
            <if test="documentType != null">document_type = #{documentType},</if>
            modified_date = now()
        </set>
        WHERE
        id = #{id}
    </update>

    <update id="updateOldIdAndAppointedDate" parameterType="com.eroses.external.society.model.BranchCommittee">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="designationCode != null">designation_code = #{designationCode},</if>
            <if test="positionArrangement != null">position_arrangement = #{positionArrangement},</if>
            <if test="titleCode != null">title_code = #{titleCode},</if>
            <if test="committeeName != null">committee_name = #{committeeName},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="citizenshipStatus != null">citizenship_status = #{citizenshipStatus},</if>
            <if test="identityType != null">identity_type = #{identityType},</if>
            <if test="committeeIcNo != null">committee_ic_no = #{committeeIcNo},</if>
            <if test="dateOfBirth != null">date_of_birth = #{dateOfBirth},</if>
            <if test="placeOfBirth != null">place_of_birth = #{placeOfBirth},</if>
            <if test="jobCode != null">job_code = #{jobCode},</if>
            <if test="committeeAddressStatus != null">committee_address_status = #{committeeAddressStatus},</if>
            <if test="committeeAddress != null">committee_address = #{committeeAddress},</if>
            <if test="committeeCountryCode != null">committee_country_code = #{committeeCountryCode},</if>
            <if test="committeeStateCode != null">committee_state_code = #{committeeStateCode},</if>
            <if test="committeeDistrict != null">committee_district = #{committeeDistrict},</if>
            <if test="committeeSmallDistrict != null">committee_small_district = #{committeeSmallDistrict},</if>
            <if test="committeeCity != null">committee_city = #{committeeCity},</if>
            <if test="postcode != null">postcode = #{postcode},</if>
            <if test="email != null">email = #{email},</if>
            <if test="homePhoneNumber != null">home_phone_number = #{homePhoneNumber},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="officePhoneNumber != null">office_phone_number = #{officePhoneNumber},</if>
            <if test="committeeEmployerName != null">committee_employer_name = #{committeeEmployerName},</if>
            <if test="committeeEmployerAddressStatus != null">committee_employer_address_status = #{committeeEmployerAddressStatus},</if>
            <if test="committeeEmployerAddress != null">committee_employer_address = #{committeeEmployerAddress},</if>
            <if test="committeeEmployerCountryCode != null">committee_employer_country_code = #{committeeEmployerCountryCode},</if>
            <if test="committeeEmployerStateCode != null">committee_employer_state_code = #{committeeEmployerStateCode},</if>
            <if test="committeeEmployerDistrict != null">committee_employer_district = #{committeeEmployerDistrict},</if>
            <if test="committeeEmployerCity != null">committee_employer_city = #{committeeEmployerCity},</if>
            <if test="committeeEmployerPostcode != null">committee_employer_postcode = #{committeeEmployerPostcode},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            <if test="batalFlat != null">batal_flat = #{batalFlat},</if>
            <if test="applicationStatusCode != null">application_status_code = #{applicationStatusCode},</if>
            <if test="pegHarta != null">peg_harta = #{pegHarta},</if>
            <if test="otherPosition != null">other_position = #{otherPosition},</if>
            <if test="status != null">status = #{status},</if>
            <if test="meetingId != null">meeting_id = #{meetingId},</if>
            <if test="meetingDate != null">meeting_date = #{meetingDate},</if>
            <if test="documentId != null">document_id = #{documentId},</if>
            <if test="documentType != null">document_type = #{documentType},</if>
            modified_date = now()
        </set>
        WHERE
        `committee_table_old_id` = #{committeeTableOldId}
        AND `appointed_date` = #{appointedDate}
    </update>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET
        `status` = #{status},
        `application_status_code` = #{applicationStatusCode},
        `modified_by` = #{modifiedBy},
        `modified_date` = now()
        WHERE
        `id` IN
        <foreach collection="committeeIds" item="item" separator="," open="(" close=")">#{item}</foreach>
    </update>

    <select id="findByBranchId" parameterType="java.lang.Long" resultMap="BranchCommitteeMap">
        SELECT `id`, `job_code`, `committee_name`, `email`, `committee_state_code`
        FROM
        <include refid="tb"/>
        WHERE
        `branch_id` = #{branchId}
        AND (status != -1 OR status IS NULL)
    </select>

    <select id="findActiveBranchCommittee" parameterType="map" resultMap="ActiveSecretaryMap">
        SELECT id, branch_id, branch_no, committee_name, committee_ic_no
        FROM <include refid="tb"/>
        WHERE
            branch_id = #{branchId}
            AND designation_code = #{secretaryPositionCode}
            AND status = #{activeStatus}
    </select>

    <select id="findByIcNo" parameterType="java.lang.String" resultMap="BranchCommitteeMap">
        SELECT `id`, `branch_id`, `job_code`, `committee_name`, `email`, `committee_state_code`
        FROM
        <include refid="tb"/>
        WHERE `committee_ic_no` = #{icNo}
    </select>

    <select id="findAllByBranchId" parameterType="java.lang.Long" resultMap="BranchCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE
        `branch_id` = #{branchId}
        AND (status != -1 OR status IS NULL)
    </select>

    <select id="countByBranchId" parameterType="long" resultType="long">
        SELECT COUNT(*)
        FROM <include refid="tb"/>
        WHERE `branch_id` = #{branchId}
    </select>

    <select id="findById" parameterType="long" resultMap="BranchCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE
        `id` = #{id}
        AND (status != -1 OR status IS NULL)
    </select>

    <select id="findByIds" parameterType="map" resultMap="BranchCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE
        1=1
        AND `id` IN <foreach collection="ids" item="item" separator="," open="(" close=")">#{item}</foreach>
        AND `branch_id` = #{branchId}
        AND (status != -1 OR status IS NULL)
    </select>

    <select id="findAllByCriteria" parameterType="map" resultMap="BranchCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="designationCodes!=null">
            AND `designation_code` IN
            <foreach collection="designationCodes" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="statusCode != null and statusCode != ''">
            AND `status` = #{statusCode}
        </if>
        <if test="deleteStatusCode != null and deleteStatusCode != ''">
            AND `status` != #{deleteStatusCode}
        </if>
        ORDER BY position_arrangement IS NULL, position_arrangement ASC
        <if test="limit != null and offset != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllByCriteria" parameterType="map" resultType="long">
        SELECT
        COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="designationCodes!=null">
            AND `designation_code` IN
            <foreach collection="designationCodes" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="statusCode != null and statusCode != ''">
            AND `status` = #{statusCode}
        </if>
        <if test="deleteStatusCode != null and deleteStatusCode != ''">
            AND `status` != #{deleteStatusCode}
        </if>
    </select>

    <select id="findBranchIdListByIdentificationNo" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT `branch_id`
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="identificationNo != null and identificationNo != ''">
            AND `committee_ic_no` = #{identificationNo}
        </if>
    </select>

    <select id="findByIdentificationNo" parameterType="java.lang.String" resultMap="BranchCommitteeMap">
        SELECT <include refid="cols"/>
        FROM <include refid="tb"/>
        WHERE 1=1
        AND `committee_ic_no` = #{identificationNo}
    </select>

    <select id="findByBranchIdAndApplicationStatusCodeAndStatus" parameterType="map" resultMap="BranchCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="branchId != null and branchId != ''">
            AND branch_id = #{branchId}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="findByBranchIdAndIdentificationNoAndApplicationStatusCodeAndStatus" parameterType="map" resultMap="BranchCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="branchId != null and branchId != ''">
            AND branch_id = #{branchId}
        </if>
        <if test="identificationNo != null and identificationNo != ''">
            AND committee_ic_no = #{identificationNo}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        LIMIT 1
    </select>

    <select id="findByBranchIdAndIdentificationNo" parameterType="map" resultMap="BranchCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="branchId != null and branchId != ''">
            AND branch_id = #{branchId}
        </if>
        <if test="identificationNo != null and identificationNo != ''">
            AND committee_ic_no = #{identificationNo}
        </if>
        LIMIT 1
    </select>

    <select id="findAll" parameterType="map" resultMap="BranchCommitteeBasicMap">
        SELECT
            bc.id,
            bc.committee_name,
            bc.email,
            bc.committee_state_code,
            bc.designation_code
        FROM <include refid="tb"/> bc
        <where>
            branch_id = #{branchId}
            <if test="applicationStatusCode != null">
                AND application_status_code = #{applicationStatusCode}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countFindAll" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM <include refid="tb"/> bc
        <where>
            branch_id = #{branchId}
            <if test="applicationStatusCode != null">
                AND application_status_code = #{applicationStatusCode}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

    <select id="findDetail" parameterType="map" resultMap="BranchCommitteeDetailMap">
        SELECT
            bc.id AS branch_committee_id,
            bc.designation_code,
            bc.identity_type,
            bc.committee_ic_no,
            bc.title_code,
            bc.committee_name,
            bc.gender,
            bc.date_of_birth,
            bc.place_of_birth,
            bc.job_code,
            bc.committee_address,
            bc.committee_state_code,
            bc.committee_district,
            bc.postcode,
            bc.email,
            bc.phone_number,
            bc.home_phone_number,
            bc.office_phone_number,
            bc.committee_employer_name,
            bc.committee_employer_address,
            bc.committee_employer_country_code
        FROM <include refid="tb"/> bc
        <where>
            bc.id = #{id}
        </where>
    </select>

    <select id="listBranchMember" parameterType="map" resultMap="BranchCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test ="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="status != null and status !=''">
            AND `status` = #{status}
        </if>
        <if test="designationCodes != null">
            AND `designation_code` IN
            <foreach collection="designationCodes" separator="," item="designationCode" open="(" close=")">
                #{designationCode}
            </foreach>
        </if>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <update id="updateBlacklistStatusByIdentificationNo" parameterType="map">
        UPDATE <include refid="tb"/>
        <set>
            `batal_flat` = #{isBlacklist},
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            modified_date=NOW()
        </set>
        <where>
            `committee_ic_no` = #{identificationNo}
        </where>
    </update>

    <select id="existsByParam" resultType="boolean">
        SELECT EXISTS (
        SELECT 1
        FROM <include refid="tb"/>
        WHERE 1=1
        <if test="committeeIcNo != null and committeeIcNo != ''">
            AND `committee_ic_no` = #{committeeIcNo}
        </if>
        <if test="committeeName != null and committeeName != ''">
            AND `committee_name` = #{committeeName}
        </if>
        <if test="applicationStatusCode != null and applicationStatusCode != ''">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
        )
    </select>

    <select id="findByIdentificationNoAndActiveUserAndActiveBranch" parameterType="map" resultMap="BranchCommitteeSelectMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Branch.selectCols"/>
        FROM <include refid="selectTb"/>
        INNER JOIN <include refid="Branch.selectTb"/> ON bc.branch_id = b.id
        WHERE 1=1
        <if test="committeeIcNo != null and committeeIcNo != ''">
            AND bc.`committee_ic_no` = #{committeeIcNo}
        </if>
        <if test="committeeApplicationStatusCode != null and committeeApplicationStatusCode != ''">
            AND bc.`application_status_code` = #{committeeApplicationStatusCode}
        </if>
        <if test="committeeStatusCode != null and committeeStatusCode != ''">
            AND bc.`status` = #{committeeStatusCode}
        </if>
        <if test="branchStatusCode != null and branchStatusCode != ''">
            AND b.`status` = #{branchStatusCode}
        </if>
        <if test="branchApplicationStatusCode != null and branchApplicationStatusCode != ''">
            AND b.`application_status_code` = #{branchApplicationStatusCode}
        </if>
    </select>

    <select id="findByParam" parameterType="map" resultMap="BranchCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="committeeIcNo != null and committeeIcNo != ''">
            AND committee_ic_no = #{committeeIcNo}
        </if>
        <if test="applicationStatusCode != null">
            AND application_status_code = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="branchId != null">
            AND branch_id = #{branchId}
        </if>
        <if test="designationCode != null">
            AND designation_code = #{designationCode}
        </if>
        <if test="appointedDate != null">
            AND appointed_date = #{appointedDate}
        </if>
        <if test="ids != null">
            AND `id` IN
            <foreach collection="ids" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="roleIds != null">
            AND `designation_code` IN (
            <foreach item="value" index="key" collection="roleIds" separator=",">
                #{value}
            </foreach>
            )
        </if>
    </select>

    <select id="findActiveCommitteeInBranchWithRoles" parameterType="map" resultMap="BranchCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `branch_id` = #{branchId}
        AND `status` = #{status}
        AND `application_status_code` = #{applicationStatusCode}
        AND `designation_code` IN
        <foreach separator="," collection="positions" item="position" open="(" close=")">
            #{position}
        </foreach>
        LIMIT 1
    </select>

    <select id="findActiveCommitteesInBranchWithRoles" parameterType="map" resultMap="BranchCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `branch_id` = #{branchId}
        AND `status` = #{status}
        AND `application_status_code` = #{applicationStatusCode}
        AND `designation_code` IN
        <foreach separator="," collection="positions" item="position" open="(" close=")">
            #{position}
        </foreach>
    </select>

    <select id="findOneByParams" parameterType="map" resultMap="BranchCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="id != null">
            AND `id` = #{id}
        </if>
        <if test="branchId != null">
            AND `branch_id` = #{branchId}
        </if>
        <if test="position != null">
            AND `designation_code` = #{position}
        </if>
        <if test="appointedDate != null">
            AND `appointed_date` = #{appointedDate}
        </if>
        <if test="applicationStatusCode != null">
            AND `application_status_code` = #{applicationStatusCode}
        </if>
        <if test="status != null and status != ''">
            AND `status` = #{status}
        </if>
        LIMIT 1
    </select>

    <select id="findAllAppointedDates" parameterType="long" resultType="java.time.LocalDate">
        SELECT DISTINCT `appointed_date`
        FROM
        <include refid="tb"/>
        WHERE `branch_id` = #{branchId}
        AND `application_status_code` = 11
    </select>

    <select id="findActiveByIdentificationNo" parameterType="map" resultMap="BranchCommitteeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `committee_ic_no` = #{identificationNo}
        AND `status` = #{status}
        AND `application_status_code` = #{applicationStatusCode}
    </select>

    <update id="saveAll" parameterType="java.util.List">
        UPDATE <include refid="tb"/>
        SET position_arrangement = CASE id
        <foreach collection="list" item="committee">
            WHEN #{committee.id} THEN #{committee.positionArrangement}
        </foreach>
        END
        WHERE id IN
        <foreach collection="list" item="committee" open="(" separator="," close=")">
            #{committee.id}
        </foreach>
    </update>
</mapper>
