<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AdmMeeting">

    <resultMap id="AdmMeetingMap" type="com.eroses.external.society.model.AdmMeeting">
        <id property="id" column="id" />
        <result column="pid" property="pid"/>
        <result column="name_en" property="nameEn"/>
        <result column="name_bm" property="nameBm"/>
        <result column="desciption" property="desciption"/>
        <result column="created_date" property="createdDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="status" property="status"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        adm_meeting
    </sql>

    <sql id="cols">
        id,
        pid,
        name_en,
        name_bm,
        desciption,
        created_date,
        created_by,
        status,
        modified_by,
        modified_date
    </sql>

    <sql id="cols1">
        id,
        pid,
        name_en,
        name_bm,
        desciption,
        status
    </sql>

    <sql id="vals">
        #{pid},
        #{nameEn},
        #{nameBm},
        #{desciption},
        now(),
        #{createdBy},
        #{status},
        #{modifiedBy},
        now()
    </sql>


    <select id="findAll" resultType="list" resultMap="AdmMeetingMap">
        SELECT
        <include refid="cols1"/>
        FROM
        <include refid="tb"/>
        WHERE `status` != '-1'
        AND `status` != '0'
    </select>

</mapper>
