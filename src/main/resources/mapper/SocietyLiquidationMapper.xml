<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="SocietyLiquidation">
    <resultMap id="map" type="com.eroses.external.society.model.societyLiquidation.SocietyLiquidation">
        <id property="id" column="id" />
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="liquidation_document_type" property="liquidationDocumentType"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="committee_vote_count" property="committeeVoteCount"/>
        <result column="committee_attend_count" property="committeeAttendCount"/>
        <result column="committee_agree_count" property="committeeAgreeCount"/>
        <result column="total_fixed_assets" property="totalFixedAssets"/>
        <result column="cash_in_hand" property="cashInHand"/>
        <result column="cash_in_bank" property="cashInBank"/>
        <result column="total_asset" property="totalAsset"/>
        <result column="short_term_liability" property="shortTermLiability"/>
        <result column="long_term_liability" property="longTermLiability"/>
        <result column="total_liability" property="totalLiability"/>
        <result column="total_fixed_assets_finance" property="totalFixedAssetsFinance"/>
        <result column="cash_in_hand_finance" property="cashInHandFinance"/>
        <result column="cash_in_bank_finance" property="cashInBankFinance"/>
        <result column="total_asset_finance" property="totalAssetFinance"/>
        <result column="short_term_liability_finance" property="shortTermLiabilityFinance"/>
        <result column="long_term_liability_finance" property="longTermLiabilityFinance"/>
        <result column="total_liability_finance" property="totalLiabilityFinance"/>
        <result column="applicant_name" property="applicantName"/>
        <result column="submission_date" property="submissionDate"/>
        <result column="decision_date" property="decisionDate"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="ro" property="ro"/>
        <result column="branch_liquidation" property="branchLiquidation"/>
        <result column="note_ppp" property="notePpp"/>
        <result column="transfer_date" property="transferDate"/>
        <result column="note_ro" property="noteRo"/>
        <result column="ro_decision_code" property="roDecisionCode"/>
        <result column="ro_decision_note" property="roDecisionNote"/>
        <result column="statement_id" property="statementId"/>
    </resultMap>

    <resultMap id="SocietyLiquidationSelectMap" type="com.eroses.external.society.model.societyLiquidation.SocietyLiquidation">
        <id property="id" column="l_id" />
        <result column="l_society_id" property="societyId"/>
        <result column="l_society_no" property="societyNo"/>
        <result column="l_branch_id" property="branchId"/>
        <result column="l_branch_no" property="branchNo"/>
        <result column="l_liquidation_document_type" property="liquidationDocumentType"/>
        <result column="l_meeting_id" property="meetingId"/>
        <result column="l_meeting_date" property="meetingDate"/>
        <result column="l_committee_vote_count" property="committeeVoteCount"/>
        <result column="l_committee_attend_count" property="committeeAttendCount"/>
        <result column="l_committee_agree_count" property="committeeAgreeCount"/>
        <result column="l_total_fixed_assets" property="totalFixedAssets"/>
        <result column="l_cash_in_hand" property="cashInHand"/>
        <result column="l_cash_in_bank" property="cashInBank"/>
        <result column="l_total_asset" property="totalAsset"/>
        <result column="l_short_term_liability" property="shortTermLiability"/>
        <result column="l_long_term_liability" property="longTermLiability"/>
        <result column="l_total_liability" property="totalLiability"/>
        <result column="l_total_fixed_assets_finance" property="totalFixedAssetsFinance"/>
        <result column="l_cash_in_hand_finance" property="cashInHandFinance"/>
        <result column="l_cash_in_bank_finance" property="cashInBankFinance"/>
        <result column="l_total_asset_finance" property="totalAssetFinance"/>
        <result column="l_short_term_liability_finance" property="shortTermLiabilityFinance"/>
        <result column="l_long_term_liability_finance" property="longTermLiabilityFinance"/>
        <result column="l_total_liability_finance" property="totalLiabilityFinance"/>
        <result column="l_applicant_name" property="applicantName"/>
        <result column="l_submission_date" property="submissionDate"/>
        <result column="l_decision_date" property="decisionDate"/>
        <result column="l_application_status_code" property="applicationStatusCode"/>
        <result column="l_created_by" property="createdBy"/>
        <result column="l_created_date" property="createdDate"/>
        <result column="l_modified_by" property="modifiedBy"/>
        <result column="l_modified_date" property="modifiedDate"/>
        <result column="l_ro" property="ro"/>
        <result column="l_branch_liquidation" property="branchLiquidation"/>
        <result column="l_note_ppp" property="notePpp"/>
        <result column="l_transfer_date" property="transferDate"/>
        <result column="l_note_ro" property="noteRo"/>
        <result column="l_ro_decision_code" property="roDecisionCode"/>
        <result column="l_ro_decision_note" property="roDecisionNote"/>
        <result column="l_statement_id" property="statementId"/>

        <association property="society" resultMap="Society.SocietySelectMap"/>
        <association property="branch" resultMap="Branch.BranchSelectMap"/>
    </resultMap>

    <resultMap id="BasicMap" type="com.eroses.external.society.dto.response.societyLiquidation.LiquidationBasicResponse">
        <id property="id" column="id" />
        <result column="liquidation_document_type" property="liquidationDocumentType"/>
        <result column="meeting_id" property="meetingId"/>
        <result column="society_name" property="societyName"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="committee_vote_count" property="committeeVoteCount"/>
        <result column="committee_attend_count" property="committeeAttendCount"/>
        <result column="committee_agree_count" property="committeeAgreeCount"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="committee_name" property="committeeName"/>
        <result column="committee_designation_code" property="committeeDesignationCode"/>
        <result column="committee_identification_no" property="committeeIdentificationNo"/>
        <result column="committee_nationality_status" property="committeeNationalityStatus"/>
        <result column="is_secretary" property="isSecretary"/>
        <result column="statement_id" property="statementId"/>
    </resultMap>

    <resultMap id="PagingMap" type="com.eroses.external.society.model.societyLiquidation.SocietyLiquidation">
        <id property="id" column="id" />
        <result column="society_no" property="societyNo"/>
        <result column="branch_no" property="branchNo"/>
        <result column="liquidation_document_type" property="liquidationDocumentType"/>
        <result column="committee_vote_count" property="committeeVoteCount"/>
        <result column="committee_attend_count" property="committeeAttendCount"/>
        <result column="committee_agree_count" property="committeeAgreeCount"/>
        <result column="application_status_code" property="applicationStatusCode"/>
        <result column="branch_liquidation" property="branchLiquidation"/>
    </resultMap>

    <resultMap id="LiquidationMap" type="com.eroses.external.society.dto.response.societyLiquidation.LiquidationMeetingResponse">
        <id property="id" column="id" />
    </resultMap>

    <sql id="tb">
        liquidation
    </sql>

    <sql id="selectTb">
        liquidation l
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="valsWithId">
        `id`, <include refid="vals"/>
    </sql>

    <sql id="cols">
        society_id,
        society_no,
        branch_id,
        branch_no,
        liquidation_document_type,
        meeting_id,
        meeting_date,
        committee_vote_count,
        committee_attend_count,
        committee_agree_count,
        total_fixed_assets,
        cash_in_hand,
        cash_in_bank,
        total_asset,
        short_term_liability,
        long_term_liability,
        total_liability,
        total_fixed_assets_finance,
        cash_in_hand_finance,
        cash_in_bank_finance,
        total_asset_finance,
        short_term_liability_finance,
        long_term_liability_finance,
        total_liability_finance,
        applicant_name,
        submission_date,
        decision_date,
        application_status_code,
        created_by,
        created_date,
        modified_by,
        modified_date,
        ro,
        branch_liquidation,
        note_ppp,
        transfer_date,
        note_ro,
        ro_decision_code,
        ro_decision_note,
        statement_id
    </sql>

    <sql id="selectCols">
        l.`id` AS l_id,
        l.`society_id` AS l_society_id,
        l.`society_no` AS l_society_no,
        l.`branch_id` AS l_branch_id,
        l.`branch_no` AS l_branch_no,
        l.`liquidation_document_type` AS l_liquidation_document_type,
        l.`meeting_id` AS l_meeting_id,
        l.`meeting_date` AS l_meeting_date,
        l.`committee_vote_count` AS l_committee_vote_count,
        l.`committee_attend_count` AS l_committee_attend_count,
        l.`committee_agree_count` AS l_committee_agree_count,
        l.`total_fixed_assets` AS l_total_fixed_assets,
        l.`cash_in_hand` AS l_cash_in_hand,
        l.`cash_in_bank` AS l_cash_in_bank,
        l.`total_asset` AS l_total_asset,
        l.`short_term_liability` AS l_short_term_liability,
        l.`long_term_liability` AS l_long_term_liability,
        l.`total_liability` AS l_total_liability,
        l.`total_fixed_assets_finance` AS l_total_fixed_assets_finance,
        l.`cash_in_hand_finance` AS l_cash_in_hand_finance,
        l.`cash_in_bank_finance` AS l_cash_in_bank_finance,
        l.`total_asset_finance` AS l_total_asset_finance,
        l.`short_term_liability_finance` AS l_short_term_liability_finance,
        l.`long_term_liability_finance` AS l_long_term_liability_finance,
        l.`total_liability_finance` AS l_total_liability_finance,
        l.`applicant_name` AS l_applicant_name,
        l.`submission_date` AS l_submission_date,
        l.`decision_date` AS l_decision_date,
        l.`application_status_code` AS l_application_status_code,
        l.`created_by` AS l_created_by,
        l.`created_date` AS l_created_date,
        l.`modified_by` AS l_modified_by,
        l.`modified_date` AS l_modified_date,
        l.`ro` AS l_ro,
        l.`branch_liquidation` AS l_branch_liquidation,
        l.`note_ppp` AS l_note_ppp,
        l.`transfer_date` AS l_transfer_date,
        l.`note_ro` AS l_note_ro,
        l.`ro_decision_code` AS l_ro_decision_code,
        l.`ro_decision_note` AS l_ro_decision_note,
        l.`statement_id` AS l_statement_id
    </sql>

    <sql id="vals">
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{liquidationDocumentType},
        #{meetingId},
        #{meetingDate},
        0,
        #{committeeAttendCount},
        0,
        #{totalFixedAssets},
        #{cashInHand},
        #{cashInBank},
        #{totalAsset},
        #{shortTermLiability},
        #{longTermLiability},
        #{totalLiability},
        #{totalFixedAssetsFinance},
        #{cashInHandFinance},
        #{cashInBankFinance},
        #{totalAssetFinance},
        #{shortTermLiabilityFinance},
        #{longTermLiabilityFinance},
        #{totalLiabilityFinance},
        #{applicantName},
        #{submissionDate},
        #{decisionDate},
        #{applicationStatusCode},
        #{createdBy},
        now(),
        #{modifiedBy},
        now(),
        #{ro},
        #{branchLiquidation},
        #{notePpp},
        #{transferDate},
        #{noteRo},
        #{roDecisionCode},
        #{roDecisionNote},
        #{statementId}
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.societyLiquidation.SocietyLiquidation" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="getById" parameterType="java.lang.Long" resultMap="map">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <update id="updateVote" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="feedback == 'Setuju'">committee_agree_count = committee_agree_count + 1,</if>
            committee_vote_count = committee_vote_count + 1
        </set>
        <where>
            id = #{liquidationId}
        </where>
    </update>

    <update id="update" parameterType="com.eroses.external.society.model.societyLiquidation.SocietyLiquidation">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="liquidationDocumentType != null">liquidation_document_type = #{liquidationDocumentType},</if>
            <if test="meetingId != null">meeting_id = #{meetingId},</if>
            <if test="meetingDate != null">meeting_date = #{meetingDate},</if>
            <if test="committeeAttendCount != null">committee_attend_count = #{committeeAttendCount},</if>
            <if test="applicationStatusCode != null">application_status_code = #{applicationStatusCode},</if>
            <if test="submissionDate != null">submission_date = #{submissionDate},</if>
            <if test="decisionDate != null">decision_date = #{decisionDate},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date=NOW(),
            <if test="ro != null">ro = #{ro},</if>
            <if test="branchLiquidation != null">branch_liquidation = #{branchLiquidation},</if>
            <if test="notePpp != null">note_ppp = #{notePpp},</if>
            <if test="transferDate != null">transfer_date = #{transferDate},</if>
            <if test="noteRo != null">note_ro = #{noteRo},</if>
            <if test="roDecisionCode != null">ro_decision_code = #{roDecisionCode},</if>
            <if test="roDecisionNote != null">ro_decision_note = #{roDecisionNote},</if>
            <if test="statementId != null">statement_id = #{statementId},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <update id="submit" parameterType="com.eroses.external.society.model.societyLiquidation.SocietyLiquidation">
        UPDATE
        <include refid="tb"/>
        <set>
            meeting_date = #{meetingDate},
            meeting_id = #{meetingId},
            application_status_code = #{applicationStatusCode},
            submission_date = #{submissionDate},
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date=NOW(),
        </set>
        WHERE `id` = #{id}
    </update>

    <update id="assignRO" parameterType="com.eroses.external.society.model.societyLiquidation.SocietyLiquidation">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date = NOW(),
            <if test="ro != null">ro = #{ro},</if>
            <if test="notePpp != null">note_ppp = #{notePpp},</if>
            transfer_date = NOW(),
        </set>
        WHERE `id` = #{id}
    </update>

    <select id="findAllByCriteria" parameterType="map" resultMap="map">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE (status IS NULL OR status != 0)
            <if test="societyId != null">
                AND society_id = #{societyId}
            </if>
            <choose>
                <when test="branchId != null">
                    AND `branch_id` = #{branchId}
                </when>
                <otherwise>
                    AND `branch_id` IS NULL
                </otherwise>
            </choose>
            <if test="createdYear != null">
                AND YEAR(created_date) = #{createdYear}
            </if>
            <if test="submissionYear != null">
                AND YEAR(submission_date) = #{submissionYear}
            </if>
            <if test="decisionYear != null">
                AND YEAR(decision_date) = #{decisionYear}
            </if>
            <if test="applicationStatusCode != null">
                AND application_status_code = #{applicationStatusCode}
            </if>
            <if test="applicantName != null and applicantName != ''">
                AND applicant_name LIKE CONCAT('%', #{applicantName}, '%')
            </if>
            AND application_status_code != -1
        ORDER BY created_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllByCriteria" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE (status IS NULL OR status != 0)
            <if test="societyId != null">
                AND society_id = #{societyId}
            </if>
            <choose>
                <when test="branchId != null">
                    AND `branch_id` = #{branchId}
                </when>
                <otherwise>
                    AND `branch_id` IS NULL
                </otherwise>
            </choose>
            <if test="createdYear != null">
                AND YEAR(created_date) = #{createdYear}
            </if>
            <if test="submissionYear != null">
                AND YEAR(submission_date) = #{submissionYear}
            </if>
            <if test="decisionYear != null">
                AND YEAR(decision_date) = #{decisionYear}
            </if>
            <if test="applicationStatusCode != null">
                AND application_status_code = #{applicationStatusCode}
            </if>
            <if test="applicantName != null and applicantName != ''">
                AND applicant_name LIKE CONCAT('%', #{applicantName}, '%')
            </if>
            AND application_status_code != -1
    </select>

    <select id="findById" parameterType="map" resultMap="BasicMap">
        SELECT
        l.id,
        l.liquidation_document_type,
        s.society_name,
        l.society_id,
        l.society_no,
        l.meeting_id,
        l.committee_vote_count,
        l.committee_attend_count,
        l.committee_agree_count,
        l.application_status_code,
        sc.name AS committee_name,
        sc.nationality_status AS committee_nationality_status,
        sc.identification_no AS committee_identification_no,
        sc.designation_code AS committee_designation_code,
        (#{secretaryDegsinationCode} = sc.designation_code) AS is_secretary,
        l.statement_id
        FROM <include refid="tb"/> l
        JOIN society_committee sc ON sc.society_id = l.society_id
        JOIN society s ON s.id = l.society_id
        WHERE
            l.id = #{liquidationId}
            AND sc.identification_no = #{identificationNo}
    </select>

    <select id="findBasic" parameterType="java.lang.Long" resultMap="map">
        SELECT
        l.id,
        l.modified_date
        FROM <include refid="tb"/> l
        WHERE l.id = #{liquidationId}
    </select>

    <select id="getAllPendingSocietyLiquidationByCriteria" parameterType="map" resultMap="SocietyLiquidationSelectMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON l.society_id = s.id
        WHERE l.`branch_id` IS NULL <!-- To differentiate Society and Branch Liquidation record -->
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND l.`application_status_code` IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND s.`state_id` = #{stateCode}
        </if>
        <if test="ro != null and ro != 0">
            AND l.`ro` = #{ro}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND s.`category_code_jppm` = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND s.`sub_category_code` = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
        </if>
        ORDER BY l.created_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllPendingSocietyLiquidationByCriteria" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON l.society_id = s.id
        WHERE l.`branch_id` IS NULL <!-- To differentiate Society and Branch Liquidation record -->
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND l.`application_status_code` IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND s.`state_id` = #{stateCode}
        </if>
        <if test="ro != null and ro != 0">
            AND l.`ro` = #{ro}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND s.`category_code_jppm` = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND s.`sub_category_code` = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
        </if>
    </select>

    <select id="getAllPendingBranchLiquidationByCriteria" parameterType="map" resultMap="SocietyLiquidationSelectMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>,
        <include refid="Branch.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON l.society_id = s.id
        INNER JOIN <include refid="Branch.selectTb"/> ON l.branch_id = b.id
        WHERE l.`branch_id` IS NOT NULL <!-- To differentiate Society and Branch Liquidation record -->
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND l.`application_status_code` IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND b.`state_id` = #{stateCode}
        </if>
        <if test="branchSearchQuery != null and branchSearchQuery != ''">
            AND (
            b.`branch_no` LIKE CONCAT('%', #{branchSearchQuery}, '%')
            OR b.`name` LIKE CONCAT('%', #{branchSearchQuery}, '%' )
            )
        </if>
        <if test="ro != null and ro != 0">
            AND l.`ro` = #{ro}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND s.`category_code_jppm` = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND s.`sub_category_code` = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
        </if>
        ORDER BY l.created_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countAllPendingBranchLiquidationByCriteria" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON l.society_id = s.id
        INNER JOIN <include refid="Branch.selectTb"/> ON l.branch_id = b.id
        WHERE l.`branch_id` IS NOT NULL <!-- To differentiate Society and Branch Liquidation record -->
        <if test="applicationStatusCodes != null and applicationStatusCodes !=''">
            AND l.`application_status_code` IN
            <foreach separator="," collection="applicationStatusCodes" item="applicationStatusCode" open="(" close=")">
                #{applicationStatusCode}
            </foreach>
        </if>
        <if test="stateCode != null and stateCode !=''">
            AND b.`state_id` = #{stateCode}
        </if>
        <if test="branchSearchQuery != null and branchSearchQuery != ''">
            AND (
            b.`branch_no` LIKE CONCAT('%', #{branchSearchQuery}, '%')
            OR b.`name` LIKE CONCAT('%', #{branchSearchQuery}, '%' )
            )
        </if>
        <if test="ro != null and ro != 0">
            AND l.`ro` = #{ro}
        </if>
        <if test="categoryCode != null and categoryCode !=''">
            AND s.`category_code_jppm` = #{categoryCode}
        </if>
        <if test="subCategoryCode != null and subCategoryCode !=''">
            AND s.`sub_category_code` = #{subCategoryCode}
        </if>
        <if test="societyName != null and societyName != ''">
            AND s.`society_name` LIKE CONCAT('%', #{societyName}, '%')
        </if>
    </select>

    <select id="findDistinctCreatedYears" parameterType="map" resultType="java.lang.String">
        SELECT DISTINCT YEAR(created_date) AS created_year
        FROM
        <include refid="tb"/>
        WHERE application_status_code != -1
        AND society_id = #{societyId}
        AND created_date IS NOT NULL
        <if test="branchId != null">
            AND branch_id = #{branchId}
        </if>
        ORDER BY created_year DESC
    </select>

    <select id="findDistinctSubmissionYears" resultType="java.lang.String">
        SELECT DISTINCT YEAR(submission_date) AS submission_year
        FROM
        <include refid="tb"/>
        WHERE application_status_code != -1
        AND submission_date IS NOT NULL
        AND society_id = #{societyId}
        <if test="branchId != null">
            AND branch_id = #{branchId}
        </if>
        ORDER BY submission_year DESC
    </select>

    <select id="findDistinctDecisionYears" resultType="java.lang.String">
        SELECT DISTINCT YEAR(decision_date) AS decision_year
        FROM
        <include refid="tb"/>
        WHERE application_status_code != -1
        AND decision_date IS NOT NULL
        AND society_id = #{societyId}
        <if test="branchId != null">
            AND branch_id = #{branchId}
        </if>
        ORDER BY decision_year DESC
    </select>

</mapper>
