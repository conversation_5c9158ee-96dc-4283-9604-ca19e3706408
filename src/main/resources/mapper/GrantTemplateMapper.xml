<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.eroses.external.society.mappers.grant.GrantTemplateDao">
    
    <resultMap id="grantTemplateResultMap" type="com.eroses.external.society.model.grant.GrantTemplate">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <result property="publishDate" column="publish_date"/>
        <result property="prePublishDate" column="pre_publish_date"/>
        <result property="endDate" column="end_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>
    
    <insert id="create" parameterType="com.eroses.external.society.model.grant.GrantTemplate" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO grant_template (
            title, 
            description, 
            status, 
            publish_date, 
            pre_publish_date, 
            end_date, 
            created_by, 
            created_date, 
            modified_by, 
            modified_date
        ) VALUES (
            #{title}, 
            #{description}, 
            #{status}, 
            #{publishDate}, 
            #{prePublishDate}, 
            #{endDate}, 
            #{createdBy}, 
            #{createdDate}, 
            #{modifiedBy}, 
            #{modifiedDate}
        )
    </insert>
    
    <update id="update" parameterType="com.eroses.external.society.model.grant.GrantTemplate">
        UPDATE grant_template
        SET 
            title = #{title},
            description = #{description},
            status = #{status},
            publish_date = #{publishDate},
            pre_publish_date = #{prePublishDate},
            end_date = #{endDate},
            modified_by = #{modifiedBy},
            modified_date = #{modifiedDate}
        WHERE id = #{id}
    </update>
    
    <select id="findAll" parameterType="map" resultMap="grantTemplateResultMap">
        SELECT * FROM grant_template
        <where>
            <if test="title != null">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY created_date DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>
    
    <select id="countAll" parameterType="map" resultType="long">
        SELECT COUNT(*) FROM grant_template
        <where>
            <if test="title != null">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>
    
    <select id="findAllDrafts" resultMap="grantTemplateResultMap">
        SELECT * FROM grant_template
        WHERE status = 'DRAFT'
        ORDER BY created_date DESC
    </select>
    
    <select id="findAllPublished" resultMap="grantTemplateResultMap">
        SELECT * FROM grant_template
        WHERE status = 'PUBLISHED'
        ORDER BY created_date DESC
    </select>
    
    <select id="findById" parameterType="long" resultMap="grantTemplateResultMap">
        SELECT * FROM grant_template
        WHERE id = #{id}
    </select>
    
    <select id="findBySocietyCategory" parameterType="long" resultMap="grantTemplateResultMap">
        SELECT gt.* FROM grant_template gt
        JOIN grant_template_society_category gtsc ON gt.id = gtsc.grant_template_id
        WHERE gtsc.society_category_id = #{societyCategoryId}
        AND gt.status = 'PUBLISHED'
        ORDER BY gt.created_date DESC
    </select>
    
    <select id="findAvailableForSociety" parameterType="long" resultMap="grantTemplateResultMap">
        SELECT gt.* FROM grant_template gt
        JOIN grant_template_society_category gtsc ON gt.id = gtsc.grant_template_id
        JOIN society s ON s.category_id = gtsc.society_category_id
        WHERE s.id = #{societyId}
        AND gt.status = 'PUBLISHED'
        AND (gt.end_date IS NULL OR gt.end_date > NOW())
        ORDER BY gt.created_date DESC
    </select>
    
    <delete id="delete" parameterType="long">
        DELETE FROM grant_template
        WHERE id = #{id}
    </delete>
    
</mapper>
