<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="AuditTrail">

    <resultMap id="AuditTrailMap" type="com.eroses.external.society.model.AuditTrail">
        <id property="id" column="id" />
        <result column="module" property="module"/>
        <result column="action_type" property="actionType"/>
        <result column="user_group" property="userGroup"/>
        <result column="user_name" property="userName"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="internal_user_id" property="internalUserId"/>
        <result column="old_data" property="oldData"/>
        <result column="new_data" property="newData"/>
        <result column="created_date" property="createdDate"/>
        <result column="created_by" property="createdBy"/>
    </resultMap>

    <resultMap id="PagingMap" type="com.eroses.external.society.dto.response.auditTrail.AuditTrailPagingResponse">
        <id property="id" column="id" />
        <result column="module" property="module"/>
        <result column="action_type" property="actionType"/>
        <result column="user_group" property="userGroup"/>
        <result column="user_name" property="userName"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="created_date" property="createdDate"/>
    </resultMap>

    <sql id="tb">
        audit_trail
    </sql>

    <sql id="cols">
        module,
        action_type,
        user_group,
        user_name,
        identification_no,
        society_id,
        society_no,
        branch_id,
        branch_no,
        internal_user_id,
        old_data,
        new_data,
        created_by,
        created_date
    </sql>

    <sql id="colsWithId">
        `id`, <include refid="cols"/>
    </sql>

    <sql id="vals">
        #{id},
        #{module},
        #{actionType},
        #{userGroup},
        #{userName},
        #{identificationNo},
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{internalUserId},
        #{oldData},
        #{newData},
        #{createdBy},
        now()
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.AuditTrail" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="colsWithId"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <!-- Update -->
    <insert id="update" parameterType="com.eroses.external.society.model.AuditTrail">
        UPDATE
            <include refid="tb"/>
        <set>
            <if test="module != null">module = #{module},</if>
            <if test="actionType != null">action_type = #{actionType},</if>
            <if test="userGroup != null">user_group = #{userGroup},</if>
            <if test="identificationNo != null">identification_no = #{identificationNo},</if>
            <if test="oldData != null">old_data = #{oldData},</if>
            <if test="newData != null">new_data = #{newData},</if>
            <if test="branchId != null">branch_id = #{branchId},</if>
            <if test="branchNo != null">branch_no = #{branchNo},</if>
            <if test="internalUserId != null">internal_user_id = #{internalUserId},</if>
            <if test="societyId != null">society_id = #{societyId},</if>
            <if test="societyNo != null">society_no = #{societyNo}</if>
        </set>
        WHERE
            id = #{id}
    </insert>

    <select id="findAll" resultMap="AuditTrailMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        ORDER BY created_date DESC
    </select>

<!-- Paging -->
    <select id="findAllByParams" parameterType="map" resultMap="PagingMap">
        SELECT
            id,
            module,
            action_type,
            user_group,
            user_name,
            identification_no,
            created_date
        FROM
        <include refid="tb"/>
        <where>
            <if test="userGroup != null">
                AND `user_group` = #{userGroup}
            </if>
            <if test="identificationNo != null">
                AND `identification_no` = #{identificationNo}
            </if>
            <if test="module != null and module != ''">
                AND `module` = #{module}
            </if>
        </where>
        ORDER BY created_date DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="countByIdNo" parameterType="map" resultType="long">
        SELECT COUNT(*) FROM <include refid="tb" />
        WHERE
        identification_no = #{identificationNo}
        AND user_group = #{userGroup}
    </select>

    <select id="findAuditTrailForUser" parameterType="map" resultMap="AuditTrailMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="userId != null">
                AND `internal_user_id` = #{userId}
            </if>
            <if test="module != null and module != ''">
                AND `module` = #{module}
            </if>
            <if test="actionType != null and actionType != ''">
                AND `action_type` = #{actionType}
            </if>
            <if test="userGroup != null">
                AND `user_group` = #{userGroup}
            </if>
            <if test="identificationNo != null and identificationNo != ''">
                AND `identification_no` = #{identificationNo}
            </if>
        </where>
        ORDER BY created_date DESC
        LIMIT 1
    </select>
</mapper>