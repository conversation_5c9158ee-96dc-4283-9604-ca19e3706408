<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eroses.external.society.mappers.OrganiserDao">

    <resultMap id="OrganiserMap" type="com.eroses.external.society.model.Organiser">
        <id property="id" column="id"/>
        <result column="name" property="name"/>
        <result column="identification_no" property="identificationNo"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        organiser
    </sql>

    <sql id="cols">
        id,
        name,
        identification_no,
        phone_number,
        created_date,
        modified_by,
        created_by
    </sql>

    <sql id="insertCols">
        name,
        identification_no,
        phone_number,
        created_by
    </sql>

    <sql id="vals">
        #{name},
        #{identificationNo},
        #{phoneNumber},
        #{createdBy}
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.Organiser" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="insertCols"/>)
        VALUES (<include refid="vals"/>)
    </insert>

    <select id="findAll" resultType="list" resultMap="OrganiserMap">
        <!--        SELECT * FROM event_admin-->
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
    </select>

    <select id="findByIdentificationNo" resultType="list" resultMap="OrganiserMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE
        identification_no = #{identificationNo}
    </select>

    <select id="findById" resultType="list" resultMap="OrganiserMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE
        id = #{id}
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.Organiser">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="identificationNo != null">identification_no = #{identificationNo},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date = now()
        </set>
        WHERE
        id = #{id}
    </update>
</mapper>
