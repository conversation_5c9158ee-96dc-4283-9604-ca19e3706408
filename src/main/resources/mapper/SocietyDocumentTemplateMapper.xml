<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="SocietyDocumentTemplate">

    <!-- Result Map for SocietyDocumentTemplate -->
    <resultMap id="SocietyDocumentTemplateResultMap" type="com.eroses.external.society.model.SocietyDocumentTemplate">
        <id column="id" property="id" />
        <result column="society_id" property="societyId" />
        <result column="document_template_id" property="documentTemplateId" />
        <result column="assigned_at" property="assignedAt" />
        <result column="created_by" property="createdBy" />
        <result column="created_date" property="createdDate" />
        <result column="modified_by" property="modifiedBy" />
        <result column="modified_date" property="modifiedDate" />
        <result column="status" property="status" />
    </resultMap>

    <resultMap id="SocietyDocumentTemplateSelectResultMap" type="com.eroses.external.society.model.SocietyDocumentTemplate">
        <id column="sdt_id" property="id" />
        <result column="sdt_society_id" property="societyId" />
        <result column="sdt_document_template_id" property="documentTemplateId" />
        <result column="sdt_assigned_at" property="assignedAt" />
        <result column="sdt_created_by" property="createdBy" />
        <result column="sdt_created_date" property="createdDate" />
        <result column="sdt_modified_by" property="modifiedBy" />
        <result column="sdt_modified_date" property="modifiedDate" />
        <result column="sdt_status" property="status" />

        <association property="society"
                     resultMap="Society.SocietySelectMap"/>
        <association property="searchInformationDocumentTemplate"
                     resultMap="SearchInformationDocumentTemplate.SearchInformationDocumentTemplateSelectResultMap"/>
    </resultMap>

    <!-- SQL Fragments -->
    <sql id="tb">
        `society_document_template`
    </sql>

    <sql id="selectTb">
        `society_document_template` sdt
    </sql>

    <sql id="colsWithId">
        `id`,
        <include refid="cols" />
    </sql>

    <sql id="cols">
        `society_id`,
        `document_template_id`,
        `assigned_at`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`,
        `status`
    </sql>

    <sql id="selectCols">
        sdt.`id` AS sdt_id,
        sdt.`society_id` AS sdt_society_id,
        sdt.`document_template_id` AS sdt_document_template_id,
        sdt.`assigned_at` AS sdt_assigned_at,
        sdt.`created_by` AS sdt_created_by,
        sdt.`created_date` AS sdt_created_date,
        sdt.`modified_by` AS sdt_modified_by,
        sdt.`modified_date` AS sdt_modified_date,
        sdt.`status` AS sdt_status
    </sql>

    <!-- Select all records -->
    <select id="findAll" resultMap="SocietyDocumentTemplateResultMap">
        SELECT
        <include refid="colsWithId" />
        FROM
        <include refid="tb" />
    </select>

    <!-- Find by Society ID And Template Code-->
    <select id="getBySocietyIdAndTemplateCode" parameterType="long" resultMap="SocietyDocumentTemplateSelectResultMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>,
        <include refid="SearchInformationDocumentTemplate.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON sdt.society_id = s.id
        INNER JOIN <include refid="SearchInformationDocumentTemplate.selectTb"/> ON sdt.document_template_id = sidt.id
        WHERE 1=1
        <if test="societyId != null and societyId !=''">
            AND sdt.`society_id` = #{societyId}
        </if>
        <if test="templateCode != null and templateCode !=''">
            AND sidt.`code` = #{templateCode}
        </if>
        <if test="status != null and status !=''">
            AND sdt.`status` = #{status}
        </if>
    </select>

    <!-- Find by Society ID -->
    <select id="getAllBySocietyId" parameterType="java.lang.Long" resultMap="SocietyDocumentTemplateSelectResultMap">
        SELECT
        <include refid="selectCols"/>,
        <include refid="Society.selectCols"/>,
        <include refid="SearchInformationDocumentTemplate.selectCols"/>
        FROM
        <include refid="selectTb"/>
        INNER JOIN <include refid="Society.selectTb"/> ON sdt.society_id = s.id
        INNER JOIN <include refid="SearchInformationDocumentTemplate.selectTb"/> ON sdt.document_template_id = sidt.id
        WHERE sdt.`society_id` = #{societyId}
        AND sdt.`status` = 1
    </select>

    <!-- Insert a new record -->
    <insert id="create" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tb" />
        (<include refid="cols" />)
        VALUES (
        #{societyId},
        #{documentTemplateId},
        #{assignedAt},
        #{createdBy},
        #{createdDate},
        #{modifiedBy},
        #{modifiedDate},
        #{status}
        )
    </insert>

    <!-- Update an existing record -->
    <update id="update">
        UPDATE <include refid="tb" />
        <set>
            <if test="societyId != null">`society_id` = #{societyId},</if>
            <if test="documentTemplateId != null">`document_template_id` = #{documentTemplateId},</if>
            <if test="assignedAt != null">`assigned_at` = #{assignedAt},</if>
            <if test="createdBy != null">`created_by` = #{createdBy},</if>
            <if test="createdDate != null">`created_date` = #{createdDate},</if>
            <if test="modifiedBy != null">`modified_by` = #{modifiedBy},</if>
            <if test="modifiedDate != null">`modified_date` = #{modifiedDate},</if>
            <if test="status != null">`status` = #{status},</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- Delete a record by ID -->
    <delete id="deleteById">
        DELETE FROM <include refid="tb" />
        WHERE `id` = #{id}
    </delete>

    <!-- Delete records by Society ID -->
    <delete id="deleteBySocietyId">
        DELETE FROM <include refid="tb" />
        WHERE `society_id` = #{societyId}
    </delete>

</mapper>