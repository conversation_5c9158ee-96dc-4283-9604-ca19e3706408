<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eroses.external.society.mappers.EventAdminDao">

    <resultMap id="EventAdminMap" type="com.eroses.external.society.model.EventAdmin">
        <id property="id" column="id" />
        <result property="identificationNo" column="identification_no" />
        <result property="username" column="username" />
        <result column="created_by" property="createdBy"/>
        <result column="created_date" property="createdDate"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        event_admin
    </sql>

    <sql id="cols">
        identification_no, username, created_by
    </sql>

    <sql id="colsUpdate">
        identification_no = #{eventadmin.identificationNo},
        username = #{eventadmin.username},
        modified_by = #{eventadmin.modifiedBy}
    </sql>

    <sql id="vals">
        #{identificationNo}, #{username}, #{createdBy}
    </sql>

    <sql id="valsUpdate">
        #{identificationNo}, #{username}, #(modifiedBy)
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.EventAdmin" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb"/> (<include refid="cols"/>)
        VALUES (<include refid="vals"/>)
    </insert>

    <select id="findAll" resultType="list" resultMap="EventAdminMap">
        SELECT * FROM event_admin
    </select>

    <select id="findByIdentificationNo" parameterType="string" resultMap="EventAdminMap">
        SELECT *
<!--        <include refid="cols"/>-->
        FROM event_admin
<!--        <include refid="tb"/>-->
        WHERE  `identification_no` = #{identificationNo}
    </select>

    <select id="findById" parameterType="Long" resultMap="EventAdminMap">
        SELECT *
        FROM event_admin
        <include refid="tb"/>
        WHERE  `id` = #{id}
    </select>

    <update id="update" parameterType="map">
        UPDATE event_admin
        SET
        identification_no = #{eventadmin.identificationNo},
        username = #{eventadmin.username}
        WHERE identification_no = #{identificationNo}
    </update>

<!--    <select id="paging" parameterType="map" resultMap="AdmAddressesMap">-->
<!--        select-->
<!--        <include refid="cols"/>-->
<!--        from-->
<!--        <include refid="tb"/>-->
<!--        where status != '-1'-->
<!--        AND `status` != '0'-->
<!--        LIMIT #{offset}, #{limit}-->
<!--    </select>-->
</mapper>
