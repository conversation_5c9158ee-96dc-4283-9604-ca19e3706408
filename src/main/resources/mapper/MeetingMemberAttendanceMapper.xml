<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC
        "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="MeetingMemberAttendance">

    <resultMap id="MeetingMemberAttendanceMap" type="com.eroses.external.society.model.MeetingMemberAttendance">
        <id property="id" column="id" />
        <result column="meeting_id" property="meetingId"/>
        <result column="society_id" property="societyId"/>
        <result column="society_no" property="societyNo"/>
        <result column="branch_id" property="branchId"/>
        <result column="branch_no" property="branchNo"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="ic_no" property="icNo"/>
        <result column="present" property="present"/>
        <result column="name" property="name"/>
        <result column="position" property="position"/>
        <result column="status" property="status"/>
        <result column="created_date" property="createdDate"/>
        <result column="created_by" property="createdBy"/>
        <result column="modified_date" property="modifiedDate"/>
        <result column="modified_by" property="modifiedBy"/>
    </resultMap>

    <resultMap id="MeetingMemberAttendanceBasicMap" type="com.eroses.external.society.dto.response.meeting.MeetingMemberAttendanceResponse">
        <id property="id" column="id" />
        <result column="name" property="name"/>
        <result column="position" property="position"/>
    </resultMap>

    <sql id="tb">
        meeting_member_attendance
    </sql>

    <sql id="cols">
        id,
        meeting_id,
        society_id,
        society_no,
        branch_id,
        branch_no,
        meeting_date,
        ic_no,
        present,
        name,
        position,
        status,
        created_date,
        created_by,
        modified_date,
        modified_by
    </sql>

    <sql id="vals">
        #{id},
        #{meetingId},
        #{societyId},
        #{societyNo},
        #{branchId},
        #{branchNo},
        #{meetingDate},
        #{icNo},
        #{present},
        #{name},
        #{position},
        #{status},
        now(),
        #{createdBy},
        now(),
        #{modifiedBy}
    </sql>

    <!-- Insert -->
    <insert id="create" parameterType="com.eroses.external.society.model.MeetingMemberAttendance" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
            meeting_id,
            society_id,
            society_no,
            branch_id,
            branch_no,
            meeting_date,
            ic_no,
            present,
            name,
            position,
            status,
            created_date,
            created_by
        )
        VALUES
        (
            #{meetingId},
            #{societyId},
            #{societyNo},
            #{branchId},
            #{branchNo},
            #{meetingDate},
            #{icNo},
            0,
            #{name},
            #{position},
            11,
            now(),
            #{createdBy}
        )
    </insert>

    <insert id="createMany" parameterType="com.eroses.external.society.model.MeetingMemberAttendance" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
            meeting_id,
            society_id,
            society_no,
            branch_id,
            branch_no,
            meeting_date,
            ic_no,
            present,
            name,
            position,
            status,
            created_date,
            created_by
        )
        VALUES
        <foreach item="member" collection="list" separator=",">
            (
                #{member.meetingId},
                #{member.societyId},
                #{member.societyNo},
                #{member.branchId},
                #{member.branchNo},
                #{member.meetingDate},
                #{member.icNo},
                0,
                #{member.name},
                #{member.position},
                11,
                now(),
                #{member.createdBy}
            )
        </foreach>
    </insert>

    <!-- Get all by meeting id -->
    <select id="getAllByMeetingId" resultType="list" resultMap="MeetingMemberAttendanceMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE meeting_id = #{meetingId} AND (status != -1 OR status IS NULL)
    </select>

    <select id="findByMeetingId" resultType="list" resultMap="MeetingMemberAttendanceBasicMap">
        SELECT
        id,
        meeting_id,
        name,
        position
        FROM
        <include refid="tb"/>
        WHERE meeting_id = #{meetingId} AND (status != -1 OR status IS NULL)
    </select>

    <!-- Update -->
    <update id="update" parameterType="com.eroses.external.society.model.MeetingMemberAttendance">
        UPDATE
            <include refid="tb"/>
        <set>
            <if test="meetingDate != null">meeting_date = #{meetingDate},</if>
            <if test="icNo != null">ic_no = #{icNo},</if>
            <if test="present != null">present = #{present},</if>
            <if test="name != null">name = #{name},</if>
            <if test="position != null">position = #{position},</if>
            <if test="status != null">status = #{status},</if>
            modified_date = now(),
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
        </set>
        WHERE
            id = #{id}
    </update>

    <update id="delete" parameterType="com.eroses.external.society.model.MeetingMemberAttendance">
        UPDATE
        <include refid="tb"/>
        <set>
            status = -1,
            modified_date = now(),
            modified_by = #{modifiedBy}
        </set>
        WHERE id IN (
            <foreach item="value" index="key" collection="memberIds" separator=",">
                #{value}
            </foreach>
        )
    </update>
</mapper>
