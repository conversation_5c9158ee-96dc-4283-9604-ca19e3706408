<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eroses.external.society.mappers.EventAttendeesDao">

    <resultMap id="EventAttendeesMap" type="com.eroses.external.society.model.EventAttendees">
        <id property="id" column="id"/>
        <result property="eventId" column="event_id"/>
        <result property="identificationNo" column="identification_no"/>
        <result property="email" column="email"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="societyId" column="society_id"/>
        <result property="applicationStatusCode" column="application_status_code"/>
        <result property="state" column="state"/>
        <result property="present" column="present"/>
        <result property="fullName" column="full_name"/>
        <result property="eventCertificateName" column="event_certificate_name"/>
        <result property="isFeedbackCompleted" column="is_feedback_completed"/>
        <result property="attendanceNo" column="attendance_no"/>
        <result property="createdBy" column="created_by" />
        <result property="cancelledDateTime" column="cancelled_date_time"/>
        <result column="created_date" property="createdDate"/>
        <result property="registeredDateTime" column="registered_date_time"/>
        <result property="checkedInDateTime" column="checked_in_date_time"/>
        <result column="modified_by" property="modifiedBy"/>
        <result column="modified_date" property="modifiedDate"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        event_attendees
    </sql>

    <sql id="cols">
        event_id,
        identification_no,
        email,
        phone_number,
        society_id,
        application_status_code,
        state,
        present,
        full_name,
        event_certificate_name,
        attendance_no,
        created_by,
        modified_by,
        cancelled_date_time,
        registered_date_time,
        checked_in_date_time
    </sql>

    <sql id="getCols">
        id,
        event_id,
        identification_no,
        email,
        phone_number,
        society_id,
        application_status_code,
        state,
        present,
        full_name,
        event_certificate_name,
        is_feedback_completed,
        attendance_no,
        created_date,
        modified_by,
        created_by,
        modified_date,
        cancelled_date_time,
        registered_date_time,
        checked_in_date_time
    </sql>

    <sql id="vals">
        #{eventId},
        #{identificationNo},
        #{email},
        #{phoneNumber},
        #{societyId},
        #{applicationStatusCode},
        #{state},
        #{present},
        #{fullName},
        #{eventCertificateName},
        #{attendanceNo},
        #{createdBy},
        #{modifiedBy},
        #{cancelledDateTime},
        #{registeredDateTime},
        #{checkedInDateTime}
    </sql>


    <insert id="create" parameterType="com.eroses.external.society.model.EventAttendees" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>, created_date)
        VALUES (<include refid="vals"/>, NOW())
    </insert>

    <select id="findByEventIdAndIdentificationNo" resultType="list" resultMap="EventAttendeesMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        identification_no = #{identificationNo}
        AND
        event_id = #{eventId}
        AND
        application_status_code = '11'
    </select>

    <select id="findByEventIdAndIdentificationNoIncludeCancelled" resultType="list" resultMap="EventAttendeesMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        identification_no = #{identificationNo}
        AND
        event_id = #{eventId}
    </select>

    <select id="findByEventId" resultType="list" resultMap="EventAttendeesMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        event_id = #{eventId}
        AND
        application_status_code = '11'
    </select>

    <select id="findAttendeesSubmittedFeedbackByEventId" resultType="list" resultMap="EventAttendeesMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        event_id = #{eventId}
        AND
        is_feedback_completed = true
        AND
        application_status_code = '11'
    </select>

    <select id="findCanceledByEventId" resultType="list" resultMap="EventAttendeesMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        event_id = #{eventId}
        AND
        application_status_code = '-1'
    </select>



    <select id="findByIdentificationNo" resultType="list" resultMap="EventAttendeesMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        identification_no = #{identificationNo}
        AND
        application_status_code = '11'
    </select>

    <select id="findAll" resultType="list" resultMap="EventAttendeesMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        application_status_code = '11'
    </select>

    <select id="findById" resultType="list" resultMap="EventAttendeesMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        id = #{id}
        AND
        application_status_code = '11'
    </select>

    <select id="findByAttendanceNo" resultType="list" resultMap="EventAttendeesMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE
        attendance_no = #{attendanceNo}
<!--        AND-->
<!--        application_status_code = '11'-->
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.EventAttendees">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="eventId != null">event_id = #{eventId},</if>
            <if test="identificationNo != null">identification_no = #{identificationNo},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="societyId != null">society_id = #{societyId},</if>
            <if test="state != null">state = #{state},</if>
            <if test="fullName != null">full_name = #{fullName},</if>
            <if test="eventCertificateName != null">event_certificate_name = #{eventCertificateName},</if>
            <if test="applicationStatusCode != null">application_status_code = #{applicationStatusCode},</if>
            <if test="isFeedbackCompleted != null">is_feedback_completed = #{isFeedbackCompleted},</if>
            <if test="present != null">present = #{present},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            <if test="cancelledDateTime != null">cancelled_date_time = #{cancelledDateTime},</if>
            <if test="registeredDateTime != null">registered_date_time = #{registeredDateTime},</if>
            <if test="checkedInDateTime != null">checked_in_date_time = #{checkedInDateTime},</if>
            modified_date = now()
        </set>

        WHERE
        id = #{id}
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM <include refid="tb"/>
        WHERE id = #{id}
    </delete>
    <delete id="deleteByEventId" parameterType="long">
        DELETE FROM <include refid="tb"/>
        WHERE event_id = #{eventId}
    </delete>

    <!-- Optional: Soft delete if you prefer not to hard delete records -->
    <!--
    <update id="delete" parameterType="long">
        UPDATE event_attendees
        SET
            is_deleted = true,
            modified_date = NOW(),
            modified_by = #{modifiedBy}
        WHERE id = #{id}
    </update>
    -->
</mapper>
