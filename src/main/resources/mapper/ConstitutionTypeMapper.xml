<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ConstitutionType">

    <resultMap id="ConstitutionTypeMap" type="com.eroses.external.society.model.ConstitutionType">
        <id property="id" column="id" />
        <result column="name" property="name"/>
        <result column="version" property="version"/>
        <result column="status" property="status"/>
    </resultMap>


    <sql id="tb">
        constitution_type
    </sql>

    <sql id="selectTb">
        constitution_type ct
    </sql>

    <sql id="cols">
        id,
        name,
        version,
        status
    </sql>

    <sql id="cols1">
        id,
        name,
        version,
        status
    </sql>

    <sql id="vals">
        #{name},
        #{version},
        #{status}
    </sql>



    <select id="findAll" resultType="list" resultMap="ConstitutionTypeMap">
        SELECT
        <include refid="cols1"/>
        FROM
        <include refid="tb"/>
        WHERE `status` != '-1'
    </select>

    <select id="findByName" parameterType="java.lang.String" resultMap="ConstitutionTypeMap">
        SELECT
        <include refid="cols"/>
        FROM
        <include refid="tb"/>
        WHERE `name` = #{name}
        AND
        `status` != '-1'
    </select>

</mapper>
