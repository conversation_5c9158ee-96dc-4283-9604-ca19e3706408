<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="RoLiquidationQuery">
    <resultMap id="FullDetailsMap" type="com.eroses.external.society.model.societyLiquidation.RoLiquidationQuery">
        <id property="id" column="id"/>
        <result property="liquidationId" column="liquidation_id"/>
        <result property="type" column="type"/>
        <result property="societyNo" column="society_no"/>
        <result property="societyId" column="society_id"/>
        <result property="branchId" column="branch_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="note" column="note"/>
        <result property="createdDate" column="created_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="finished" column="finished"/>
        <result property="modifiedDate" column="modified_date"/>
        <result property="modifiedBy" column="modified_by"/>
    </resultMap>

    <sql id="tb">
        `ro_liquidation_query`
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.societyLiquidation.RoLiquidationQuery" useGeneratedKeys="true"
            keyProperty="id"
    >
        INSERT INTO
        <include refid="tb"/>
        (
            `liquidation_id`,
            `type`,
            `society_no`,
            `society_id`,
            `branch_id`,
            `branch_no`,
            `note`,
            `created_by`
        )
        VALUES
        (
            #{liquidationId},
            #{type},
            #{societyNo},
            #{societyId},
            #{branchId},
            #{branchNo},
            #{note},
            #{createdBy}
        )
    </insert>
</mapper>
