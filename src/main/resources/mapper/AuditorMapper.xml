<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="Auditor">
    <!-- Result Map -->
    <resultMap id="AuditorMap" type="com.eroses.external.society.model.Auditor">
        <id property="id" column="id"/>
        <result property="societyId" column="society_id"/>
        <result property="societyNo" column="society_no"/>
        <result property="branchId" column="branch_id"/>
        <result property="branchNo" column="branch_no"/>
        <result property="statementId" column="statement_id"/>
        <result property="auditorType" column="auditor_type"/>
        <result property="titleCode" column="title_code"/>
        <result property="name" column="name"/>
        <result property="licenseNo" column="license_no"/>
        <result property="companyName" column="company_name"/>
        <result property="companyNo" column="company_no"/>
        <result property="gender" column="gender"/>
        <result property="nationalityStatus" column="nationality_status"/>
        <result property="identificationType" column="identification_type"/>
        <result property="identificationNo" column="identification_no"/>
        <result property="dateOfBirth" column="date_of_birth"/>
        <result property="placeOfBirth" column="place_of_birth"/>
        <result property="employmentCode" column="employment_code"/>
        <result property="address" column="address"/>
        <result property="countryCode" column="country_code"/>
        <result property="stateCode" column="state_id"/>
        <result property="districtCode" column="district_id"/>
        <result property="smallDistrictCode" column="small_district_code"/>
        <result property="city" column="city"/>
        <result property="postcode" column="postcode"/>
        <result property="email" column="email"/>
        <result property="telephoneNo" column="telephone_no"/>
        <result property="phoneNo" column="phone_no"/>
        <result property="appointmentDate" column="appointment_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
        <result property="status" column="status"/>
        <result property="deleteStatus" column="delete_status"/>
        <result property="pemCaw" column="pem_caw"/>
    </resultMap>

    <!-- Table Name -->
    <sql id="tb">
        `auditor`
    </sql>

    <!-- Columns without ID -->
    <sql id="cols">
        `society_id`,
        `society_no`,
        `branch_id`,
        `branch_no`,
        `statement_id`,
        `auditor_type`,
        `title_code`,
        `name`,
        `license_no`,
        `company_name`,
        `company_no`,
        `gender`,
        `nationality_status`,
        `identification_type`,
        `identification_no`,
        `date_of_birth`,
        `place_of_birth`,
        `employment_code`,
        `address`,
        `country_code`,
        `state_id`,
        `district_id`,
        `small_district_code`,
        `city`,
        `postcode`,
        `email`,
        `telephone_no`,
        `phone_no`,
        `appointment_date`,
        `created_by`,
        `created_date`,
        `modified_by`,
        `modified_date`,
        `status`,
        `delete_status`,
        `pem_caw`
    </sql>

    <!-- Columns with ID -->
    <sql id="colsWithId">
        `id`,
        <include refid="cols"/>
    </sql>

    <!-- Create auditor -->
    <insert id="create" parameterType="com.eroses.external.society.model.Auditor" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES (
        #{societyId}, #{societyNo}, #{branchId}, #{branchNo}, #{statementId}, #{auditorType},
        #{titleCode}, #{name}, #{licenseNo}, #{companyName}, #{companyNo}, #{gender}, #{nationalityStatus},
        #{identificationType}, #{identificationNo}, #{dateOfBirth}, #{placeOfBirth}, #{employmentCode},
        #{address}, #{countryCode}, #{stateCode}, #{districtCode}, #{smallDistrictCode},
        #{city}, #{postcode}, #{email}, #{telephoneNo}, #{phoneNo}, #{appointmentDate},
        #{createdBy}, NOW(), #{modifiedBy}, NOW(), #{status}, #{deleteStatus}, #{pemCaw}
        )
    </insert>

    <select id="listAuditor" parameterType="map" resultMap="AuditorMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND society_id = #{societyId}
        </if>
        <choose>
            <when test="branchId != null">
                AND `branch_id` = #{branchId}
            </when>
            <otherwise>
                AND `branch_id` IS NULL
            </otherwise>
        </choose>
        <if test="statementId != null">
            AND statement_id = #{statementId}
        </if>
        <if test="appointmentDate != null">
            AND appointment_date = #{appointmentDate}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        AND (delete_status != 1 OR delete_status IS NULL)
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countListAuditor" parameterType="map" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM
        <include refid="tb"/>
        WHERE 1=1
        <if test="societyId != null">
            AND society_id = #{societyId}
        </if>
        <choose>
            <when test="branchId != null">
                AND `branch_id` = #{branchId}
            </when>
            <otherwise>
                AND `branch_id` IS NULL
            </otherwise>
        </choose>
        <if test="statementId != null">
            AND statement_id = #{statementId}
        </if>
        <if test="appointmentDate != null">
            AND appointment_date = #{appointmentDate}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        AND (delete_status != 1 OR delete_status IS NULL)
    </select>

    <select id="findById" resultMap="AuditorMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE `id` = #{id}
        LIMIT 1
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.Auditor">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="societyId != null">society_id = #{societyId},</if>
            <if test="societyNo != null">society_no = #{societyNo},</if>
            <if test="branchId != null">branch_id = #{branchId},</if>
            <if test="branchNo != null">branch_no = #{branchNo},</if>
            <if test="statementId != null">statement_id = #{statementId},</if>
            <if test="auditorType != null">auditor_type = #{auditorType},</if>
            <if test="titleCode != null">title_code = #{titleCode},</if>
            <if test="name != null">name = #{name},</if>
            <if test="licenseNo != null">license_no = #{licenseNo},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="companyNo != null">company_no = #{companyNo},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="nationalityStatus != null">nationality_status = #{nationalityStatus},</if>
            <if test="identificationType != null">identification_type = #{identificationType},</if>
            <if test="identificationNo != null">identification_no = #{identificationNo},</if>
            <if test="dateOfBirth != null">date_of_birth = #{dateOfBirth},</if>
            <if test="placeOfBirth != null">place_of_birth = #{placeOfBirth},</if>
            <if test="employmentCode != null">employment_code = #{employmentCode},</if>
            <if test="address != null">address = #{address},</if>
            <if test="countryCode != null">country_code = #{countryCode},</if>
            <if test="stateCode != null">state_id = #{stateCode},</if>
            <if test="districtCode != null">district_id = #{districtCode},</if>
            <if test="smallDistrictCode != null">small_district_code = #{smallDistrictCode},</if>
            <if test="city != null">city = #{city},</if>
            <if test="postcode != null">postcode = #{postcode},</if>
            <if test="email != null">email = #{email},</if>
            <if test="telephoneNo != null">telephone_no = #{telephoneNo},</if>
            <if test="phoneNo != null">phone_no = #{phoneNo},</if>
            <if test="appointmentDate != null">appointment_date = #{appointmentDate},</if>
            <if test="modifiedBy != null">modified_by = #{modifiedBy},</if>
            modified_date = NOW(),
            <if test="status != null">status = #{status},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
            <if test="pemCaw != null">pem_caw = #{pemCaw}</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="findByStatementId" resultMap="AuditorMap">
        SELECT
        <include refid="colsWithId"/>
        FROM
        <include refid="tb"/>
        WHERE
        `statement_id` = #{statementId}
        LIMIT 1
    </select>
</mapper>
