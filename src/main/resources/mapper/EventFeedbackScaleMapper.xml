<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eroses.external.society.mappers.EventFeedbackScaleDao">

    <resultMap id="EventFeedbackScaleMap" type="com.eroses.external.society.model.EventFeedbackScale">
        <id property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="label" column="label"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedDate" column="modified_date"/>
    </resultMap>

    <!-- Define reusable SQL snippets -->
    <sql id="tb">
        event_feedback_scale
    </sql>

    <sql id="cols">
        code,
        label,
        sort_order,
        created_by
    </sql>

    <sql id="getCols">
        id,
        code,
        label,
        sort_order,
        created_date,
        modified_by,
        created_by,
        modified_date
    </sql>

    <sql id="vals">
        #{code},
        #{label},
        #{sortOrder},
        #{createdBy}
    </sql>

    <insert id="create" parameterType="com.eroses.external.society.model.EventFeedbackScale" 
            keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols"/>)
        VALUES (<include refid="vals"/>)
    </insert>

    <select id="findAll" resultMap="EventFeedbackScaleMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
    </select>

    <select id="findById" resultMap="EventFeedbackScaleMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByCode" resultMap="EventFeedbackScaleMap">
        SELECT
        <include refid="getCols"/>
        FROM
        <include refid="tb"/>
        WHERE code = #{code}
    </select>

    <update id="update" parameterType="com.eroses.external.society.model.EventFeedbackScale">
        UPDATE
        <include refid="tb"/>
        SET
            code = #{code},
            label = #{label},
            sort_order = #{sortOrder},
            modified_by = #{modifiedBy},
            modified_date = NOW()
        WHERE id = #{id}
    </update>

    <delete id="delete">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

</mapper>