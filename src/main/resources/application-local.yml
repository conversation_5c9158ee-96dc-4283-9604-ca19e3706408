spring:
  cache:
    redis:
      host: your-elasticache-endpoint.amazonaws.com
      port: 6379
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    #    hibernate:
    #      ddl-auto: update
    show-sql: true

mybatis:
  mapper-locations: classpath:/mapper/*.xml

jwt:
  secret: I8xKJ4TLOORtjtXotP3tW4RJmcqgn5+ag9VGzvS5x2zeWhgw8qs0RI4uXHfhNtnLsJjDJizYIWUUPyYzzbScGQ==
  expiration-time: 3600

awsS3:
  bucket-name: eroses-backend-docs
  region: ap-southeast-5
  secret-name: aws_credentials_dev

userClient:
  #  url: http://localhost:8085
  #  loginUrl: http://localhost:8085/user/auth/login
  url: https://a-dev.ros.gov.my
  loginUrl: https://a-dev.ros.gov.my/user/auth/login
  #  username: SOCIETY_SERVICE
  #  password: society4883#68567@8302948
  #  url: https://a-dev.ros.gov.my
  #  loginUrl: https://a-dev.ros.gov.my/user/auth/login
#  notification_url: https://a-dev.ros.gov.my/notification
  notification_url: http://localhost:8095/notification
  #  document_url: https://a-dev.ros.gov.my/document
  document_url: http://localhost:8100/document
  username: SOCIETY_SERVICE
  password: society4883#68567@8302948

logging:
  level:
    root: warn

api:
  jpn:
    base-url: https://staging.ros.gov.my/api_eroses/chxMI.PHP
    key: ?k=O*)3B@nM6vrtaI^9qD*1hkZm~kN
  payment:
    url: https://epayment.eroses.gov.my/eps/process
    urlHost: https://epayment.eroses.gov.my/eps
    exchangeId: EX00002561
    environment: production
    urlRequest: https://epayment.eroses.gov.my/
    osolCode : 72499
    department : "JABATAN PENDAFTARAN PERTUBUHAN"
    ptj : "IBU PEJABAT JAB. PENDAFTARAN PERTUBUHAN"
    paymentMode : fpx
    requestStatus : 3

aws:
  region: ap-southeast-5
  secrets-manager:
    database: db_credentials_dev
    cache: cache_credentials_dev
    map: map_credentials_dev
  s3:
    bucket-name: eroses-backend-docs-dev
    domain-name: doc-dev.ros.gov.my
    old-domain-name: eroses-backend-docs-dev.s3.ap-southeast-5.amazonaws.com
    region: ap-southeast-5
    secret-name: aws_credentials_dev
    template:
      meeting-minutes: https://eroses-backend-docs-dev.s3.ap-southeast-5.amazonaws.com/asset/MeetingMinutes.docx
      payment-counter: https://eroses-backend-docs-dev.s3.ap-southeast-5.amazonaws.com/asset/Slip+pengesahan+pembayaran+kaunter.docx
      payment-online : https://eroses-backend-docs-dev.s3.ap-southeast-5.amazonaws.com/asset/Slip+pengesahan+pembayaran+kaunter.docx
  rekognition:
    region: ap-southeast-1
  quicksight:
    region: ap-southeast-1
    secret-name: aws_credentials_dev
    account-id: "************"
    user-arn: arn:aws:quicksight:ap-southeast-1:************:user/default/<EMAIL>
    session-timeout-minutes: 60
    undo-redo-disabled: false
    reset-disabled: false
    access-key: ********************
    secret-key: 9vyuqt8vTVwQqQVqYaM9FPAFHTTPhoNyQxXJU8qi
