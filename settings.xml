<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <!-- localRepository
     | The path to the local repository maven will use to store artifacts.
     |
     | Default: ${user.home}/.m2/repository
    <localRepository>/path/to/local/repo</localRepository>
    -->
    <localRepository>/Users/<USER>/.m2</localRepository>

    <!-- Optional: Repositories to override defaults -->
    <profiles>
        <profile>
            <id>central-repo</id>
            <repositories>
                <repository>
                    <id>central</id>
                    <url>https://repo.maven.apache.org/maven2</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
            </repositories>
        </profile>
    </profiles>

    <servers>
        <server>
            <id>github</id>
            <username>E-Roses</username>
            <password>****************************************</password>
        </server>
        <server>
            <id>github-innoxafjar</id>
            <username>E-Roses</username>
            <password>****************************************</password>
        </server>
    </servers>

    <activeProfiles>
        <activeProfile>central-repo</activeProfile>
    </activeProfiles>
</settings>